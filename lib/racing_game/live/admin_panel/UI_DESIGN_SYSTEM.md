# UI设计系统规范

## 📋 概述

本文档定义了Racing Game Admin Panel的完整UI设计系统，包括视觉规范、组件标准、交互模式等，确保整个管理后台界面的一致性和专业性。

## 🎨 视觉设计基础

### 色彩系统
```css
/* 主色调 - 专业蓝色系 */
:root {
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;  /* 主色 */
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;
}

/* 功能色彩 */
:root {
  --success-500: #10b981;    /* 成功 */
  --warning-500: #f59e0b;    /* 警告 */
  --error-500: #ef4444;      /* 错误 */
  --info-500: #06b6d4;       /* 信息 */
}

/* 中性色彩 */
:root {
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
}
```

### 字体系统
```css
/* 字体族 */
:root {
  --font-sans: 'Inter', 'Noto Sans SC', system-ui, sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
}

/* 字体大小 */
:root {
  --text-xs: 0.75rem;      /* 12px */
  --text-sm: 0.875rem;     /* 14px */
  --text-base: 1rem;       /* 16px */
  --text-lg: 1.125rem;     /* 18px */
  --text-xl: 1.25rem;      /* 20px */
  --text-2xl: 1.5rem;      /* 24px */
  --text-3xl: 1.875rem;    /* 30px */
  --text-4xl: 2.25rem;     /* 36px */
}

/* 字重 */
:root {
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
}

/* 行高 */
:root {
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
}
```

### 间距系统
```css
/* 基于4px的间距系统 */
:root {
  --space-1: 0.25rem;      /* 4px */
  --space-2: 0.5rem;       /* 8px */
  --space-3: 0.75rem;      /* 12px */
  --space-4: 1rem;         /* 16px */
  --space-5: 1.25rem;      /* 20px */
  --space-6: 1.5rem;       /* 24px */
  --space-8: 2rem;         /* 32px */
  --space-10: 2.5rem;      /* 40px */
  --space-12: 3rem;        /* 48px */
  --space-16: 4rem;        /* 64px */
  --space-20: 5rem;        /* 80px */
}
```

### 圆角系统
```css
:root {
  --radius-none: 0;
  --radius-sm: 0.125rem;    /* 2px */
  --radius-base: 0.25rem;   /* 4px */
  --radius-md: 0.375rem;    /* 6px */
  --radius-lg: 0.5rem;      /* 8px */
  --radius-xl: 0.75rem;     /* 12px */
  --radius-2xl: 1rem;       /* 16px */
  --radius-full: 9999px;
}
```

### 阴影系统
```css
:root {
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-base: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
}
```

## 🧩 基础组件规范

### 按钮组件 (Button)
```html
<!-- 主要按钮 -->
<button class="btn btn-primary">
  <svg class="w-4 h-4 mr-2">...</svg>
  主要操作
</button>

<!-- 次要按钮 -->
<button class="btn btn-secondary">
  次要操作
</button>

<!-- 危险按钮 -->
<button class="btn btn-error">
  删除
</button>

<!-- 文本按钮 -->
<button class="btn btn-ghost">
  取消
</button>

<!-- 图标按钮 -->
<button class="btn btn-square btn-ghost">
  <svg class="w-5 h-5">...</svg>
</button>
```

### 输入组件 (Input)
```html
<!-- 基础输入框 -->
<div class="form-control">
  <label class="label">
    <span class="label-text">用户名</span>
  </label>
  <input type="text" class="input input-bordered" placeholder="请输入用户名" />
  <label class="label">
    <span class="label-text-alt text-error">错误提示信息</span>
  </label>
</div>

<!-- 搜索框 -->
<div class="form-control">
  <div class="input-group">
    <input type="text" class="input input-bordered flex-1" placeholder="搜索用户..." />
    <button class="btn btn-square">
      <svg class="w-5 h-5">...</svg>
    </button>
  </div>
</div>

<!-- 选择器 -->
<select class="select select-bordered">
  <option disabled selected>请选择状态</option>
  <option value="active">活跃</option>
  <option value="inactive">非活跃</option>
</select>
```

### 卡片组件 (Card)
```html
<!-- 基础卡片 -->
<div class="card bg-base-100 shadow-md">
  <div class="card-body">
    <h2 class="card-title">卡片标题</h2>
    <p>卡片内容描述</p>
    <div class="card-actions justify-end">
      <button class="btn btn-primary">操作</button>
    </div>
  </div>
</div>

<!-- 统计卡片 -->
<div class="stats shadow">
  <div class="stat">
    <div class="stat-figure text-primary">
      <svg class="w-8 h-8">...</svg>
    </div>
    <div class="stat-title">在线用户</div>
    <div class="stat-value text-primary">1,234</div>
    <div class="stat-desc">较昨日增长 12%</div>
  </div>
</div>
```

### 表格组件 (Table)
```html
<div class="overflow-x-auto">
  <table class="table table-zebra">
    <thead>
      <tr>
        <th>
          <label>
            <input type="checkbox" class="checkbox" />
          </label>
        </th>
        <th>用户名</th>
        <th>邮箱</th>
        <th>状态</th>
        <th>操作</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <th>
          <label>
            <input type="checkbox" class="checkbox" />
          </label>
        </th>
        <td>
          <div class="flex items-center space-x-3">
            <div class="avatar">
              <div class="mask mask-squircle w-12 h-12">
                <img src="avatar.jpg" alt="Avatar" />
              </div>
            </div>
            <div>
              <div class="font-bold">张三</div>
              <div class="text-sm opacity-50">ID: 12345</div>
            </div>
          </div>
        </td>
        <td><EMAIL></td>
        <td>
          <span class="badge badge-success">活跃</span>
        </td>
        <td>
          <div class="dropdown dropdown-end">
            <label tabindex="0" class="btn btn-ghost btn-xs">操作</label>
            <ul class="dropdown-content menu p-2 shadow bg-base-100 rounded-box w-52">
              <li><a>编辑</a></li>
              <li><a>删除</a></li>
            </ul>
          </div>
        </td>
      </tr>
    </tbody>
  </table>
</div>
```

### 模态框组件 (Modal)
```html
<!-- 基础模态框 -->
<div class="modal" id="my-modal">
  <div class="modal-box">
    <h3 class="font-bold text-lg">模态框标题</h3>
    <p class="py-4">模态框内容</p>
    <div class="modal-action">
      <button class="btn btn-primary">确认</button>
      <button class="btn" onclick="document.getElementById('my-modal').close()">取消</button>
    </div>
  </div>
  <form method="dialog" class="modal-backdrop">
    <button>close</button>
  </form>
</div>

<!-- 确认对话框 -->
<div class="modal" id="confirm-modal">
  <div class="modal-box">
    <h3 class="font-bold text-lg">确认操作</h3>
    <p class="py-4">您确定要执行此操作吗？此操作不可撤销。</p>
    <div class="modal-action">
      <button class="btn btn-error">确认删除</button>
      <button class="btn">取消</button>
    </div>
  </div>
</div>
```

## 🎛️ 业务组件规范

### 用户信息卡片
```html
<div class="card bg-base-100 shadow-md">
  <div class="card-body">
    <div class="flex items-center space-x-4">
      <div class="avatar">
        <div class="w-16 h-16 rounded-full">
          <img src="user-avatar.jpg" alt="用户头像" />
        </div>
      </div>
      <div class="flex-1">
        <h3 class="text-lg font-semibold">张三</h3>
        <p class="text-sm text-gray-500">ID: 12345</p>
        <div class="flex items-center space-x-2 mt-2">
          <span class="badge badge-success">VIP用户</span>
          <span class="badge badge-outline">活跃</span>
        </div>
      </div>
      <div class="text-right">
        <p class="text-sm text-gray-500">余额</p>
        <p class="text-xl font-bold text-primary">¥1,234.56</p>
      </div>
    </div>
  </div>
</div>
```

### 支付状态组件
```html
<div class="flex items-center space-x-2">
  <div class="w-3 h-3 rounded-full bg-success animate-pulse"></div>
  <span class="text-sm font-medium text-success">支付成功</span>
  <span class="text-xs text-gray-500">2分钟前</span>
</div>

<!-- 不同状态的样式 -->
<div class="payment-status">
  <!-- 待支付 -->
  <div class="flex items-center space-x-2">
    <div class="w-3 h-3 rounded-full bg-warning"></div>
    <span class="text-sm font-medium text-warning">待支付</span>
  </div>
  
  <!-- 支付失败 -->
  <div class="flex items-center space-x-2">
    <div class="w-3 h-3 rounded-full bg-error"></div>
    <span class="text-sm font-medium text-error">支付失败</span>
  </div>
  
  <!-- 退款中 -->
  <div class="flex items-center space-x-2">
    <div class="w-3 h-3 rounded-full bg-info animate-spin"></div>
    <span class="text-sm font-medium text-info">退款中</span>
  </div>
</div>
```

### 筛选面板组件
```html
<div class="card bg-base-100 shadow-sm">
  <div class="card-body">
    <h3 class="card-title text-base">筛选条件</h3>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <!-- 状态筛选 -->
      <div class="form-control">
        <label class="label">
          <span class="label-text">用户状态</span>
        </label>
        <select class="select select-bordered select-sm">
          <option value="">全部状态</option>
          <option value="active">活跃</option>
          <option value="inactive">非活跃</option>
        </select>
      </div>
      
      <!-- 时间范围 -->
      <div class="form-control">
        <label class="label">
          <span class="label-text">注册时间</span>
        </label>
        <input type="date" class="input input-bordered input-sm" />
      </div>
      
      <!-- 搜索框 -->
      <div class="form-control">
        <label class="label">
          <span class="label-text">搜索</span>
        </label>
        <div class="input-group">
          <input type="text" class="input input-bordered input-sm flex-1" placeholder="用户名或邮箱" />
          <button class="btn btn-square btn-sm">
            <svg class="w-4 h-4">...</svg>
          </button>
        </div>
      </div>
    </div>
    
    <div class="card-actions justify-between mt-4">
      <button class="btn btn-ghost btn-sm">重置</button>
      <button class="btn btn-primary btn-sm">应用筛选</button>
    </div>
  </div>
</div>
```

## 📱 响应式设计规范

### 断点系统
```css
/* 移动端优先的响应式设计 */
.responsive-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 640px) {
  .responsive-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .responsive-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .responsive-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}
```

### 移动端适配
```css
/* 移动端导航 */
.mobile-nav {
  @apply fixed bottom-0 left-0 right-0 bg-base-100 border-t border-base-300;
  @apply flex justify-around items-center h-16 z-50;
}

.mobile-nav-item {
  @apply flex flex-col items-center justify-center space-y-1;
  @apply text-xs text-gray-500 hover:text-primary;
}

/* 移动端表格 */
.mobile-table {
  @apply block md:table;
}

.mobile-table tbody {
  @apply block md:table-row-group;
}

.mobile-table tr {
  @apply block md:table-row border border-base-300 mb-2 md:mb-0;
}

.mobile-table td {
  @apply block md:table-cell px-4 py-2;
}

.mobile-table td:before {
  content: attr(data-label) ": ";
  @apply font-semibold md:hidden;
}
```

## 🎭 动画和过渡效果

### 基础动画
```css
/* 淡入动画 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* 滑入动画 */
@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(20px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

/* 弹跳动画 */
@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 应用动画的工具类 */
.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

.animate-bounce-in {
  animation: bounceIn 0.5s ease-out;
}
```

### 过渡效果
```css
/* 通用过渡 */
.transition-all {
  transition: all 0.2s ease-in-out;
}

.transition-colors {
  transition: color 0.2s, background-color 0.2s, border-color 0.2s;
}

.transition-transform {
  transition: transform 0.2s ease-in-out;
}

/* 悬停效果 */
.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.hover-scale:hover {
  transform: scale(1.05);
}

/* 点击效果 */
.click-scale:active {
  transform: scale(0.98);
}
```

## 🔧 实用工具类

### 布局工具
```css
/* Flexbox 工具 */
.flex-center {
  @apply flex items-center justify-center;
}

.flex-between {
  @apply flex items-center justify-between;
}

.flex-start {
  @apply flex items-center justify-start;
}

.flex-end {
  @apply flex items-center justify-end;
}

/* Grid 工具 */
.grid-auto-fit {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.grid-auto-fill {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}
```

### 文本工具
```css
/* 文本截断 */
.text-truncate {
  @apply overflow-hidden whitespace-nowrap;
  text-overflow: ellipsis;
}

.text-truncate-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-truncate-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
```

### 状态工具
```css
/* 加载状态 */
.loading-overlay {
  @apply absolute inset-0 bg-base-100 bg-opacity-75 flex-center;
}

.loading-spinner {
  @apply w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin;
}

/* 空状态 */
.empty-state {
  @apply flex flex-col items-center justify-center py-12 text-center;
}

.empty-state-icon {
  @apply w-16 h-16 text-gray-400 mb-4;
}

.empty-state-title {
  @apply text-lg font-semibold text-gray-900 mb-2;
}

.empty-state-description {
  @apply text-gray-500 mb-4;
}
```

---

**文档版本**: v1.0  
**创建日期**: 2024-12-19  
**最后更新**: 2024-12-19  
**维护团队**: Racing Game Frontend Team
