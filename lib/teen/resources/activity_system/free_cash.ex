defmodule Teen.ActivitySystem.FreeCash do
  @moduledoc """
  Free Cash资源

  管理Free Cash活动配置和奖励
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  postgres do
    table "free_cashes"
    repo Cyp<PERSON><PERSON>.Repo
  end

  admin do
    table_columns [:id, :cash_name, :total_reward_amount, :min_amount, :max_amount, :status]
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_cashes
  end

  actions do
    defaults [:create, :read, :update, :destroy]

    read :list_active_cashes do
      filter expr(status == 1)
    end

    update :enable do
      change set_attribute(:status, 1)
    end

    update :disable do
      change set_attribute(:status, 0)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :cash_name, :string do
      allow_nil? false
      public? true
      description "Free Cash名称"
      constraints max_length: 100
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "Free Cash描述"
      constraints max_length: 1000
    end

    attribute :total_reward_amount, :decimal do
      allow_nil? false
      public? true
      description "奖励总金额"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :min_amount, :decimal do
      allow_nil? false
      public? true
      description "初始最小值"
      default Decimal.new("1")
      constraints min: Decimal.new("0")
    end

    attribute :max_amount, :decimal do
      allow_nil? false
      public? true
      description "初始最大值"
      default Decimal.new("100")
      constraints min: Decimal.new("0")
    end

    attribute :distribution_type, :integer do
      allow_nil? false
      public? true
      description "分配类型：1-随机分配，2-固定分配，3-递增分配"
      default 1
      constraints min: 1, max: 3
    end

    attribute :daily_limit, :integer do
      allow_nil? false
      public? true
      description "每日限制次数（0为无限制）"
      default 1
      constraints min: 0
    end

    attribute :per_user_daily_limit, :integer do
      allow_nil? false
      public? true
      description "每用户每日限制次数"
      default 1
      constraints min: 0
    end

    attribute :total_limit, :integer do
      allow_nil? false
      public? true
      description "总限制次数（0为无限制）"
      default 0
      constraints min: 0
    end

    attribute :used_count, :integer do
      allow_nil? false
      public? true
      description "已使用次数"
      default 0
      constraints min: 0
    end

    attribute :distributed_amount, :decimal do
      allow_nil? false
      public? true
      description "已分配金额"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :min_level, :integer do
      allow_nil? false
      public? true
      description "最低等级要求"
      default 1
      constraints min: 1
    end

    attribute :vip_level, :integer do
      allow_nil? true
      public? true
      description "VIP等级要求"
      constraints min: 0
    end

    attribute :cooldown_hours, :integer do
      allow_nil? false
      public? true
      description "冷却时间（小时）"
      default 24
      constraints min: 0
    end

    attribute :weight_config, :map do
      allow_nil? true
      public? true
      description "权重配置（用于随机分配）"
    end

    attribute :bonus_multiplier, :decimal do
      allow_nil? false
      public? true
      description "奖励倍数"
      default Decimal.new("1.0")
      constraints min: Decimal.new("0.1")
    end

    attribute :status, :integer do
      allow_nil? false
      public? true
      description "状态：0-禁用，1-启用"
      default 1
      constraints min: 0, max: 1
    end

    attribute :priority, :integer do
      allow_nil? false
      public? true
      description "优先级（数字越大优先级越高）"
      default 0
      constraints min: 0
    end

    attribute :conditions, :map do
      allow_nil? true
      public? true
      description "额外条件配置"
    end

    timestamps()
  end

  identities do
    identity :unique_cash_name, [:cash_name]
  end
end
