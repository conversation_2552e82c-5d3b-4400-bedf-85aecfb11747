defmodule RacingGame.Live.AdminPanel.SystemCommunicationsComponent do
  @moduledoc """
  系统通信管理组件 - 统一管理系统消息、公告和通知

  整合了原来的三个独立组件：
  - SystemMessagesComponent
  - SystemAnnouncementsComponent
  - SystemNotificationsComponent

  通过类型字段区分不同的通信类型，提供统一的管理界面。

  ## 架构说明

  该组件采用模块化架构，将功能拆分到不同的模块中：

  ### 事件处理器 (Handlers)
  - `FilterEventHandler` - 过滤和搜索事件处理
  - `ModalEventHandler` - 模态框事件处理
  - `DataEventHandler` - 数据操作事件处理
  - `UiEventHandler` - UI交互事件处理
  - `DialogEventHandler` - 对话框事件处理

  ### 操作模块 (Operations)
  - `SearchOperations` - 搜索和数据加载操作
  - `CrudOperations` - 创建、更新、删除操作

  ### 验证器 (Validators)
  - `CommunicationValidator` - 通信数据验证
  - `DeleteConstraintValidator` - 删除约束验证

  ### 映射配置 (Mappings)
  - `TypeMappings` - 类型、优先级、状态映射
  """
  use CypridinaWeb, :live_component

  # 导入事件处理器
  alias RacingGame.Live.AdminPanel.Handlers.{
    FilterEventHandler,
    ModalEventHandler,
    DataEventHandler,
    UiEventHandler,
    DialogEventHandler
  }

  # 导入操作模块
  alias RacingGame.Live.AdminPanel.Operations.SearchOperations

  # 导入映射配置
  alias RacingGame.Live.AdminPanel.Mappings.TypeMappings

  # 导入其他依赖
  alias RacingGame.Utils.DynamicPagination
  alias CypridinaWeb.Components.AdminButtonGroup
  alias RacingGame.Live.AdminPanel.SystemCommunicationsDialog
  import RacingGame.Utils.DynamicDialog
  import Phoenix.Naming, only: [humanize: 1]
  require Logger

  # 通信类型定义
  @communication_types [
    {:all, "全部类型"},
    {:message, "系统消息"},
    {:announcement, "系统公告"},
    {:notification, "系统通知"}
  ]

  # 状态类型定义
  @status_types [
    {:all, "全部状态"},
    {true, "启用"},
    {false, "禁用"}
  ]

  @priority_types [
    {nil, "全部优先级"},
    {"low", "低"},
    {"medium", "中"},
    {"high", "高"},
    {"urgent", "紧急"}
  ]

  @spec update(maybe_improper_list() | map(), any()) :: {:ok, map()}
  def update(assigns, socket) do
    socket = assign(socket, assigns)

    # 简化权限检查 - 暂时允许所有用户访问以便测试
    is_admin = case socket.assigns.current_user do
      %{role: :admin} -> true
      %{email: email} when is_binary(email) -> String.contains?(email, "admin")
      _ -> true  # 临时允许所有用户访问
    end

    if is_admin do
      socket =
        socket
        |> assign_defaults()
        |> SearchOperations.load_communications()

      {:ok, socket}
    else
      # 非管理员用户显示无权限信息
      socket =
        socket
        |> assign_defaults()
        |> assign(:unauthorized, true)
      {:ok, socket}
    end
  end

  # ============================================================================
  # 事件处理器 - 委托给专门的处理器模块
  # ============================================================================

  @spec handle_event(binary(), map(), Phoenix.LiveView.Socket.t()) :: {:noreply, Phoenix.LiveView.Socket.t()}

  # 过滤和搜索事件 - 委托给 FilterEventHandler
  def handle_event("filter:" <> action, params, socket) do
    Logger.info("🔍 过滤事件: #{action}, 参数: #{inspect(params)}")

    case action do
      "change" -> FilterEventHandler.handle_filter_change(params, socket)
      "search" ->
        # 处理搜索事件，支持多种参数格式
        query = params["query"] || params["value"] || ""
        FilterEventHandler.handle_search(query, socket)
      "clear" -> FilterEventHandler.handle_clear_filters(socket)
      _ ->
        Logger.warning("⚠️ [事件路由] 未知的过滤器事件: #{action}")
        {:noreply, socket}
    end
  end

  # 模态框管理事件 - 委托给 ModalEventHandler
  def handle_event("modal:" <> action, params, socket) do
    case action do
      "show_create" -> ModalEventHandler.handle_show_create(params["id"], socket)
      "show_edit" -> ModalEventHandler.handle_show_edit(params["id"], socket)
      "close" -> ModalEventHandler.handle_close_modal(socket)
      "validate" -> ModalEventHandler.handle_validate_form(params["communication"], socket)
      _ ->
        Logger.warning("⚠️ [事件路由] 未知的模态框事件: #{action}")
        {:noreply, socket}
    end
  end

  # 数据操作事件 - 委托给 DataEventHandler
  def handle_event("data:" <> action, params, socket) do
    case action do
      "save" -> DataEventHandler.handle_save_communication(params["communication"], socket)
      "show_delete" -> DataEventHandler.handle_show_delete_dialog(params["id"], socket)
      "confirm_delete" -> DataEventHandler.handle_confirm_delete(params["id"], params, socket)
      "show_status" -> DataEventHandler.handle_show_status_dialog(params["id"], socket)
      "confirm_status" -> DataEventHandler.handle_confirm_status_toggle(params["id"], socket)
      _ ->
        Logger.warning("⚠️ [事件路由] 未知的数据操作事件: #{action}")
        {:noreply, socket}
    end
  end

  # 界面交互事件 - 委托给 UiEventHandler
  def handle_event("ui:" <> action, params, socket) do
    case action do
      "page_change" -> UiEventHandler.handle_page_change(params, socket)
      "per_page_change" -> UiEventHandler.handle_per_page_change(params, socket)
      "page_jump" -> UiEventHandler.handle_page_jump(params, socket)
      "refresh" -> handle_refresh(socket)
      _ ->
        Logger.warning("⚠️ [事件路由] 未知的界面交互事件: #{action}")
        {:noreply, socket}
    end
  end

  # 对话框管理事件 - 委托给 DialogEventHandler
  def handle_event("dialog:" <> action, params, socket) do
    case action do
      "hide" when is_map_key(params, "dialog") ->
        DialogEventHandler.handle_hide_dialog(params["dialog"], socket)
      "hide" ->
        DialogEventHandler.handle_hide_all_dialogs(socket)
      _ ->
        Logger.warning("⚠️ [事件路由] 未知的对话框事件: #{action}")
        {:noreply, socket}
    end
  end

  # 向后兼容的事件处理（保持原有事件名称）
  def handle_event("filter_change", params, socket), do: FilterEventHandler.handle_filter_change(params, socket)
  def handle_event("perform_search", %{"query" => query}, socket), do: FilterEventHandler.handle_search(query, socket)
  def handle_event("clear_search", _params, socket), do: FilterEventHandler.handle_clear_filters(socket)

  # 模态框事件处理 - 统一处理
  def handle_event("show_create_dialog", %{"id" => type}, socket) do
    Logger.info("🎯 显示创建对话框，类型: #{type}")

    socket = assign_modal_state(socket, :create, type, nil)
    {:noreply, socket}
  end

  def handle_event("show_edit_dialog", %{"id" => id}, socket) do
    Logger.info("🎯 显示编辑对话框，ID: #{id}")

    case find_communication_by_id(socket.assigns.communications, id) do
      {:ok, communication} ->
        socket = assign_modal_state(socket, :edit, communication.type, communication)
        {:noreply, socket}
      {:error, :not_found} ->
        Logger.warning("⚠️ 未找到要编辑的通信记录，ID: #{id}")
        {:noreply, put_flash(socket, :error, "未找到要编辑的通信记录")}
    end
  end

  def handle_event("close_modal", _params, socket) do
    Logger.info("🎯 关闭模态框")
    socket = close_modal_state(socket)
    {:noreply, socket}
  end

  # 刷新数据处理
  defp handle_refresh(socket) do
    Logger.info("🔄 刷新系统通信数据")

    socket =
      socket
      |> SearchOperations.load_communications()
      |> put_flash(:info, "数据已刷新")

    {:noreply, socket}
  end

  # 测试连接事件
  def handle_event("test_connection", _params, socket) do
    Logger.info("✅ LiveView连接测试成功！")
    socket = put_flash(socket, :info, "LiveView连接正常！")
    {:noreply, socket}
  end

  # 捕获所有未处理的事件
  def handle_event(event_name, params, socket) do
    Logger.warning("⚠️ [事件调试] 未处理的事件: #{event_name}, 参数: #{inspect(params)}")
    {:noreply, socket}
  end

  # 处理表单模态框事件 - 优化后的统一处理
  def handle_info({:modal_event, event_type, data}, socket) do
    socket = handle_modal_event(socket, event_type, data)
    {:noreply, socket}
  end

  # 私有函数 - 处理模态框事件
  defp handle_modal_event(socket, :save_success, _communication) do
    socket
    |> close_modal_state()
    |> SearchOperations.load_communications()
    |> put_flash(:info, "保存成功！")
  end

  defp handle_modal_event(socket, :save_success_no_close, _communication) do
    socket
    |> SearchOperations.load_communications()
    |> put_flash(:info, "保存成功！")
  end

  defp handle_modal_event(socket, :save_error, error) do
    put_flash(socket, :error, "保存失败：#{inspect(error)}")
  end

  defp handle_modal_event(socket, :close, _data) do
    close_modal_state(socket)
  end

  defp handle_modal_event(socket, _unknown_event, _data) do
    Logger.warning("⚠️ 未知的模态框事件")
    socket
  end

  # ============================================================================
  # 辅助函数 - 初始化和配置
  # ============================================================================

  # 私有函数 - 分配默认值
  defp assign_defaults(socket) do
    socket
    |> assign_if_not_exists(:unauthorized, false)
    |> assign_if_not_exists(:search_query, "")
    |> assign_if_not_exists(:selected_type, nil)
    |> assign_if_not_exists(:selected_status, nil)
    |> assign_if_not_exists(:selected_priority, nil)
    |> assign_if_not_exists(:page, 1)
    |> assign_if_not_exists(:per_page, 10)
    |> assign_if_not_exists(:communications, [])
    |> assign_if_not_exists(:total_count, 0)
    |> assign_if_not_exists(:loading, false)
    |> assign_if_not_exists(:show_create_modal, false)
    |> assign_if_not_exists(:show_edit_modal, false)
    |> assign_if_not_exists(:current_communication, nil)
    |> assign_if_not_exists(:modal_data, nil)
    |> assign_if_not_exists(:modal_mode, nil)
    |> assign_if_not_exists(:modal_type, nil)
    |> assign_if_not_exists(:buttons_hidden, true)
    |> assign_if_not_exists(:communication_types, @communication_types)
    |> assign_if_not_exists(:status_types, @status_types)
    |> assign_if_not_exists(:priority_types, @priority_types)
    |> assign_if_not_exists(:show_delete_dialog, false)
    |> assign_if_not_exists(:show_status_dialog, false)
    |> assign_if_not_exists(:delete_dialog_message, nil)
    |> assign_if_not_exists(:status_dialog_title, nil)
    |> assign_if_not_exists(:status_dialog_message, nil)
    |> assign_if_not_exists(:current_operation_id, nil)
  end

  # 私有函数 - 条件分配（如果不存在则分配）
  defp assign_if_not_exists(socket, key, default_value) do
    if Map.has_key?(socket.assigns, key) do
      socket
    else
      assign(socket, key, default_value)
    end
  end

  # 私有函数 - 统一的模态框状态设置
  defp assign_modal_state(socket, mode, type, communication) do
    socket
    |> assign(:show_create_modal, mode == :create)
    |> assign(:show_edit_modal, mode == :edit)
    |> assign(:modal_mode, mode)
    |> assign(:modal_type, type)
    |> assign(:current_communication, communication)
  end

  # 私有函数 - 关闭模态框状态
  defp close_modal_state(socket) do
    socket
    |> assign(:show_create_modal, false)
    |> assign(:show_edit_modal, false)
    |> assign(:modal_mode, nil)
    |> assign(:modal_type, nil)
    |> assign(:current_communication, nil)
    |> assign(:modal_data, nil)
  end

  # 私有函数 - 查找通信记录
  defp find_communication_by_id(communications, id) do
    case Enum.find(communications, &(&1.id == id)) do
      nil -> {:error, :not_found}
      communication -> {:ok, communication}
    end
  end

  # 私有函数 - 获取创建按钮配置
  defp get_create_button_configs do
    [
      {"message", %{
        text: "消息",
        icon: "fas fa-envelope",
        class: "px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors duration-200 font-medium",
        style: "background-color: #4f46e5 !important;"
      }},
      {"announcement", %{
        text: "公告",
        icon: "fas fa-bullhorn",
        class: "px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors duration-200 font-medium",
        style: "background-color: #22c55e !important;"
      }},
      {"notification", %{
        text: "通知",
        icon: "fas fa-bell",
        class: "px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 transition-colors duration-200 font-medium",
        style: "background-color: #eab308 !important;"
      }}
    ]
  end

  # 私有函数 - 渲染接收者类型
  defp render_recipient_type("all") do
    ~H"""
    <span class="text-green-600">
      <i class="fas fa-users mr-1"></i>全体用户
    </span>
    """
  end

  defp render_recipient_type("user") do
    ~H"""
    <span class="text-blue-600">
      <i class="fas fa-user mr-1"></i>指定用户
    </span>
    """
  end

  defp render_recipient_type("admin") do
    ~H"""
    <span class="text-purple-600">
      <i class="fas fa-user-shield mr-1"></i>管理员
    </span>
    """
  end

  defp render_recipient_type(_) do
    ~H"""
    <span class="text-gray-500">未知</span>
    """
  end



  # ============================================================================
  # 便捷函数 - 委托给映射模块
  # ============================================================================

  # 获取类型相关信息
  def get_type_display_name(type), do: TypeMappings.get_type_display_name(type)
  def get_type_icon(type), do: TypeMappings.get_type_icon(type)
  def get_type_color_class(type), do: TypeMappings.get_type_color_class(type)

  # 获取优先级相关信息
  def get_priority_display_name(priority), do: TypeMappings.get_priority_display_name(priority)
  def get_priority_badge_class(priority), do: TypeMappings.get_priority_badge_class(priority)
  def get_priority_icon(priority), do: TypeMappings.get_priority_icon(priority)

  # 获取状态相关信息
  def get_status_display_name(status), do: TypeMappings.get_status_display_name(status)
  def get_status_badge_class(status), do: TypeMappings.get_status_badge_class(status)
  def get_status_icon(status), do: TypeMappings.get_status_icon(status)

  # 获取支持的类型和优先级
  def get_supported_types, do: TypeMappings.get_supported_types()
  def get_supported_priorities, do: TypeMappings.get_supported_priorities()

  # 验证函数
  def valid_type?(type), do: TypeMappings.valid_type?(type)
  def valid_priority?(priority), do: TypeMappings.valid_priority?(priority)

  # 准备对话框数据
  defp prepare_dialog_data(nil), do: %{}
  defp prepare_dialog_data(communication) when is_map(communication) do
    %{
      id: communication.id,
      title: communication.title || "",
      content: communication.content || "",
      type: communication.type || "message",
      priority: communication.priority || "medium",
      recipient_type: communication.recipient_type || "all",
      recipient_id: communication.recipient_id || "",
      active: communication.active || true,
      expires_at: communication.expires_at
    }
  end

  # ============================================================================
  # 渲染函数
  # ============================================================================

  def render(assigns) do
    ~H"""
    <div>
      <!-- 对话框组件 - 移到主容器外面，确保fixed定位相对于整个屏幕 -->
      <%= if @show_create_modal or @show_edit_modal do %>
        <.live_component
          module={SystemCommunicationsDialog}
          id="system-communications-dialog"
          show={true}
          mode={@modal_mode}
          type={@modal_type}
          data={prepare_dialog_data(@current_communication)}
          current_user={@current_user}
        />
      <% end %>
      <div class="p-6 bg-white rounded-lg shadow-md">
        <%= if assigns[:unauthorized] do %>
          <h3 class="text-xl font-bold text-red-600 mb-4">权限不足</h3>
          <p class="text-gray-700">您没有访问系统通信管理的权限。只有管理员可以查看此页面。</p>
        <% else %>
          <!-- 页面标题和操作按钮 -->
          <div class="flex justify-between items-center mb-6">
            <h3 class="text-xl font-bold text-gray-800">系统通信管理</h3>


            <div class="flex gap-3">
              <!-- 创建按钮组 -->
              <div class="flex gap-2">
                <%= for {type, config} <- get_create_button_configs() do %>
                  <button
                    type="button"
                    phx-click="show_create_dialog"
                    phx-target={@myself}
                    phx-value-id={type}
                    class={config.class}
                    style={config.style}
                  >
                    <i class={config.icon <> " mr-2"}></i><%= config.text %>
                  </button>
                <% end %>
              </div>
              <!-- 刷新按钮 -->
              <AdminButtonGroup.refresh_button
                action="ui:refresh"
                target={@myself}
              />
            </div>
          </div>

          <!-- 搜索和过滤栏 -->
          <div class="mb-6 bg-gray-50 p-4 rounded-lg">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
              <!-- 搜索框 -->
              <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-700 mb-1">搜索</label>
                <div class="flex">
                  <input
                    type="text"
                    value={@search_query}
                    placeholder="搜索标题或内容..."
                    phx-keyup="filter:search"
                    phx-debounce="300"
                    phx-target={@myself}
                    class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <AdminButtonGroup.search_button
                    text=""
                    action="filter:search"
                    target={@myself}
                    query={@search_query}
                    icon="fas fa-search"
                  />
                </div>
              </div>

              <!-- 类型过滤 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">类型</label>
                <select
                  phx-change="filter:change"
                  phx-target={@myself}
                  name="type"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <%= for {value, label} <- @communication_types do %>
                    <option value={value} selected={@selected_type == value}>
                      <%= label %>
                    </option>
                  <% end %>
                </select>
              </div>

              <!-- 状态过滤 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                <select
                  phx-change="filter:change"
                  phx-target={@myself}
                  name="status"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <%= for {value, label} <- @status_types do %>
                    <option value={value} selected={@selected_status == value}>
                      <%= label %>
                    </option>
                  <% end %>
                </select>
              </div>

              <!-- 优先级过滤 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">优先级</label>
                <select
                  phx-change="filter:change"
                  phx-target={@myself}
                  name="priority"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <%= for {value, label} <- @priority_types do %>
                    <option value={value} selected={@selected_priority == value}>
                      <%= label %>
                    </option>
                  <% end %>
                </select>
              </div>
            </div>

            <!-- 清除过滤器按钮 -->
            <%= if @search_query != "" or @selected_type != nil or @selected_status != nil do %>
              <div class="mt-3 flex justify-end">
                <AdminButtonGroup.secondary_button
                  text="清除过滤器"
                  action="filter:clear"
                  target={@myself}
                  icon="fas fa-times"
                />
              </div>
            <% end %>
          </div>

          <!-- 通信列表 -->
          <div class="overflow-x-auto">
            <table class="min-w-full bg-white border border-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  类型
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  标题
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  内容
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  优先级
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  接收者
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  状态
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  创建时间
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  操作
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <%= if Enum.empty?(@communications) do %>
                <tr>
                  <td colspan="8" class="px-6 py-8 text-center text-gray-500">
                    <div class="flex flex-col items-center">
                      <i class="fas fa-inbox text-4xl text-gray-300 mb-2"></i>
                      <p class="text-lg">暂无系统通信记录</p>
                      <p class="text-sm">点击上方按钮创建新的消息、公告或通知</p>
                    </div>
                  </td>
                </tr>
              <% else %>
                <%= for communication <- @communications do %>
                  <tr class="hover:bg-gray-50">
                    <!-- 类型 -->
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="flex items-center">
                        <i class={get_type_icon(communication.type) <> " mr-2 " <> get_type_color_class(communication.type)}></i>
                        <span class="text-sm font-medium">
                          <%= get_type_display_name(communication.type) %>
                        </span>
                      </div>
                    </td>

                    <!-- 标题 -->
                    <td class="px-6 py-4 text-sm font-medium text-gray-900 max-w-xs">
                      <div class="truncate" title={communication.title}>
                        <%= communication.title %>
                      </div>
                    </td>

                    <!-- 内容 -->
                    <td class="px-6 py-4 text-sm text-gray-500 max-w-xs">
                      <div class="truncate" title={communication.content}>
                        <%= communication.content %>
                      </div>
                    </td>

                    <!-- 优先级 -->
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class={get_priority_badge_class(communication.priority)}>
                        <i class={get_priority_icon(communication.priority) <> " mr-1"}></i>
                        <%= get_priority_display_name(communication.priority) %>
                      </span>
                    </td>

                    <!-- 接收者 -->
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <%= render_recipient_type(communication.recipient_type) %>
                    </td>

                    <!-- 状态 -->
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class={get_status_badge_class(communication.active)}>
                        <i class={get_status_icon(communication.active) <> " mr-1"}></i>
                        <%= get_status_display_name(communication.active) %>
                      </span>
                    </td>

                    <!-- 创建时间 -->
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <%= if communication.inserted_at do %>
                        <%= Calendar.strftime(communication.inserted_at, "%Y-%m-%d %H:%M") %>
                      <% else %>
                        -
                      <% end %>
                    </td>

                    <!-- 操作 -->
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div class="flex space-x-2">
                        <!-- 编辑按钮 -->
                        <AdminButtonGroup.edit_button
                          text="编辑"
                          action="show_edit_dialog"
                          target={@myself}
                          id={communication.id}
                          class="text-indigo-600 hover:text-indigo-900 px-2 py-1"
                        />

                        <!-- 状态切换按钮 -->
                        <%= if communication.active do %>
                          <AdminButtonGroup.warning_button
                            text=""
                            action="show_status_dialog"
                            target={@myself}
                            id={communication.id}
                            icon="fas fa-pause"
                          />
                        <% else %>
                          <AdminButtonGroup.success_button
                            text=""
                            action="show_status_dialog"
                            target={@myself}
                            id={communication.id}
                            icon="fas fa-play"
                          />
                        <% end %>

                        <!-- 删除按钮 -->
                        <AdminButtonGroup.delete_button
                          text="删除"
                          action="show_delete_dialog"
                          target={@myself}
                          id={communication.id}
                          use_native_confirm={false}
                          class="text-red-600 hover:text-red-900 px-2 py-1"
                        />
                      </div>
                    </td>
                  </tr>
                <% end %>
              <% end %>
            </tbody>
            </table>
          </div>

          <!-- 分页控制 -->
          <%= if @total_count > @per_page do %>
            <DynamicPagination.pagination
              current_page={@page}
              total_count={@total_count}
              per_page={@per_page}
              target={@myself}
              page_change_event="ui:page_change"
              per_page_change_event="ui:per_page_change"
              show_per_page_selector={true}
              show_page_info={true}
              show_quick_jump={true}
              per_page_options={[10, 20, 50, 100]}
              max_visible_pages={7}
            />
          <% end %>

          <!-- 对话框组件 -->
          <!-- 删除确认对话框 -->
          <.dynamic_dialog
            id="delete-confirm-dialog"
            type="confirm"
            title="确认删除"
            message={@delete_dialog_message || "您确定要删除这条通信记录吗？此操作不可撤销。"}
            show={@show_delete_dialog || false}
            confirm_text="删除"
            cancel_text="取消"
            confirm_action="data:confirm_delete"
            cancel_action="dialog:hide"
            target={@myself}
            danger={true}
            icon="fas fa-exclamation-triangle"
          />

          <!-- 状态切换确认对话框 -->
          <.dynamic_dialog
            id="status-confirm-dialog"
            type="confirm"
            title={@status_dialog_title || "确认状态切换"}
            message={@status_dialog_message || "您确定要切换此通信记录的状态吗？"}
            show={@show_status_dialog || false}
            confirm_text="确认"
            cancel_text="取消"
            confirm_action="data:confirm_status"
            cancel_action="dialog:hide"
            target={@myself}
            icon="fas fa-toggle-on"
          />



        <% end %>
      </div>
    </div>
    """
  end


end
