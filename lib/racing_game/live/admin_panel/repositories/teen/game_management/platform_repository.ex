defmodule RacingGame.Live.AdminPanel.Repositories.Teen.GameManagement.PlatformRepository do
  @moduledoc """
  🏢 平台配置仓储层

  负责平台配置的数据访问操作，包括：
  - 平台配置的CRUD操作
  - 平台状态管理
  - 平台查询和过滤
  - 平台配置缓存
  - 平台统计信息
  """

  require Logger
  import Ash.Query

  alias Teen.GameManagement.Platform

  # 缓存配置
  @cache_ttl 300_000  # 5分钟缓存
  @cache_prefix "platform_repo"

  # ==================== 基础 CRUD 操作 ====================

  @doc """
  创建平台配置

  ## 参数
  - `platform_data` - 平台配置数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, platform}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_platform(platform_data, options \\ []) do
    Logger.info("🏢 [平台仓储] 创建平台配置: #{inspect(platform_data)}")

    try do
      case Platform.create(platform_data) do
        {:ok, platform} ->
          Logger.info("✅ [平台仓储] 平台配置创建成功: #{platform.id}")
          clear_platform_cache()
          {:ok, platform}

        {:error, error} ->
          Logger.error("❌ [平台仓储] 平台配置创建失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [平台仓储] 创建平台配置异常: #{inspect(exception)}")
        {:error, :create_platform_exception}
    end
  end

  @doc """
  根据ID获取平台配置

  ## 参数
  - `platform_id` - 平台ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, platform}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_platform_by_id(platform_id, options \\ []) do
    Logger.info("🔍 [平台仓储] 获取平台配置: #{platform_id}")

    cache_key = "#{@cache_prefix}:platform:#{platform_id}"
    use_cache = Keyword.get(options, :use_cache, true)

    if use_cache do
      case get_from_cache(cache_key) do
        {:ok, platform} ->
          Logger.info("📦 [平台仓储] 从缓存获取平台: #{platform_id}")
          {:ok, platform}

        {:error, :not_found} ->
          fetch_and_cache_platform(platform_id, cache_key)
      end
    else
      fetch_platform_by_id(platform_id)
    end
  end

  @doc """
  根据平台编号获取平台配置

  ## 参数
  - `platform_number` - 平台编号
  - `options` - 选项参数

  ## 返回
  - `{:ok, platform}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_platform_by_number(platform_number, options \\ []) do
    Logger.info("🔍 [平台仓储] 根据编号获取平台: #{platform_number}")

    try do
      case Platform.get_by_platform_number(platform_number) do
        {:ok, [platform]} ->
          Logger.info("✅ [平台仓储] 找到平台: #{platform.id}")
          {:ok, platform}

        {:ok, []} ->
          Logger.info("📭 [平台仓储] 未找到平台: #{platform_number}")
          {:error, :platform_not_found}

        {:error, error} ->
          Logger.error("❌ [平台仓储] 查询平台失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [平台仓储] 查询平台异常: #{inspect(exception)}")
        {:error, :query_platform_exception}
    end
  end

  @doc """
  更新平台配置

  ## 参数
  - `platform_id` - 平台ID
  - `update_data` - 更新数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, platform}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_platform(platform_id, update_data, options \\ []) do
    Logger.info("📝 [平台仓储] 更新平台配置: #{platform_id}")

    with {:ok, platform} <- get_platform_by_id(platform_id, use_cache: false),
         {:ok, updated_platform} <- Platform.update(platform, update_data) do
      Logger.info("✅ [平台仓储] 平台配置更新成功: #{platform_id}")
      clear_platform_cache()
      {:ok, updated_platform}
    else
      {:error, error} ->
        Logger.error("❌ [平台仓储] 平台配置更新失败: #{inspect(error)}")
        {:error, error}
    end
  end

  @doc """
  删除平台配置

  ## 参数
  - `platform_id` - 平台ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, platform}` - 成功
  - `{:error, reason}` - 失败
  """
  def delete_platform(platform_id, options \\ []) do
    Logger.info("🗑️ [平台仓储] 删除平台配置: #{platform_id}")

    with {:ok, platform} <- get_platform_by_id(platform_id, use_cache: false),
         {:ok, deleted_platform} <- Platform.destroy(platform) do
      Logger.info("✅ [平台仓储] 平台配置删除成功: #{platform_id}")
      clear_platform_cache()
      {:ok, deleted_platform}
    else
      {:error, error} ->
        Logger.error("❌ [平台仓储] 平台配置删除失败: #{inspect(error)}")
        {:error, error}
    end
  end

  # ==================== 查询操作 ====================

  @doc """
  获取所有活跃平台

  ## 参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, platforms}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_active_platforms(options \\ []) do
    Logger.info("📋 [平台仓储] 获取活跃平台列表")

    try do
      case Platform.list_active_platforms() do
        {:ok, platforms} ->
          Logger.info("✅ [平台仓储] 获取到 #{length(platforms)} 个活跃平台")
          {:ok, platforms}

        {:error, error} ->
          Logger.error("❌ [平台仓储] 获取活跃平台失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [平台仓储] 获取活跃平台异常: #{inspect(exception)}")
        {:error, :list_active_platforms_exception}
    end
  end

  @doc """
  获取平台列表（带分页和过滤）

  ## 参数
  - `params` - 查询参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, %{platforms: platforms, pagination: pagination}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_platforms(params \\ %{}, options \\ []) do
    Logger.info("📋 [平台仓储] 获取平台列表: #{inspect(params)}")

    try do
      query = Platform
      |> apply_platform_filters(params)
      |> apply_platform_sorting(params)
      |> apply_platform_pagination(params)

      case Ash.read(query) do
        {:ok, platforms} ->
          pagination = build_pagination_info(platforms, params)
          Logger.info("✅ [平台仓储] 获取到 #{length(platforms)} 个平台")
          {:ok, %{platforms: platforms, pagination: pagination}}

        {:error, error} ->
          Logger.error("❌ [平台仓储] 获取平台列表失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [平台仓储] 获取平台列表异常: #{inspect(exception)}")
        {:error, :list_platforms_exception}
    end
  end

  # ==================== 状态管理操作 ====================

  @doc """
  启用平台

  ## 参数
  - `platform_id` - 平台ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, platform}` - 成功
  - `{:error, reason}` - 失败
  """
  def enable_platform(platform_id, options \\ []) do
    Logger.info("🟢 [平台仓储] 启用平台: #{platform_id}")
    update_platform_status(platform_id, 1, options)
  end

  @doc """
  禁用平台

  ## 参数
  - `platform_id` - 平台ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, platform}` - 成功
  - `{:error, reason}` - 失败
  """
  def disable_platform(platform_id, options \\ []) do
    Logger.info("🔴 [平台仓储] 禁用平台: #{platform_id}")
    update_platform_status(platform_id, 0, options)
  end

  @doc """
  切换代理充值开关

  ## 参数
  - `platform_id` - 平台ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, platform}` - 成功
  - `{:error, reason}` - 失败
  """
  def toggle_agent_recharge(platform_id, options \\ []) do
    Logger.info("🔄 [平台仓储] 切换代理充值开关: #{platform_id}")

    with {:ok, platform} <- get_platform_by_id(platform_id, use_cache: false) do
      new_switch = if platform.agent_recharge_switch == 1, do: 0, else: 1
      update_platform(platform_id, %{agent_recharge_switch: new_switch}, options)
    end
  end

  # ==================== 私有辅助函数 ====================

  # 从数据库获取平台并缓存
  defp fetch_and_cache_platform(platform_id, cache_key) do
    case fetch_platform_by_id(platform_id) do
      {:ok, platform} ->
        cache_platform(cache_key, platform)
        {:ok, platform}

      error ->
        error
    end
  end

  # 从数据库获取平台
  defp fetch_platform_by_id(platform_id) do
    try do
      case Platform.read(platform_id) do
        {:ok, platform} ->
          Logger.info("✅ [平台仓储] 从数据库获取平台: #{platform_id}")
          {:ok, platform}

        {:error, error} ->
          Logger.error("❌ [平台仓储] 获取平台失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [平台仓储] 获取平台异常: #{inspect(exception)}")
        {:error, :fetch_platform_exception}
    end
  end

  # 更新平台状态
  defp update_platform_status(platform_id, status, options) do
    update_platform(platform_id, %{status: status}, options)
  end

  # 应用平台过滤条件
  defp apply_platform_filters(query, params) do
    Enum.reduce(params, query, fn
      {"status", status}, acc when not is_nil(status) ->
        filter(acc, status == ^status)

      {"platform_number", number}, acc when not is_nil(number) and number != "" ->
        filter(acc, platform_number == ^number)

      {"platform_name", name}, acc when not is_nil(name) and name != "" ->
        filter(acc, contains(platform_name, ^name))

      {"agent_recharge_switch", switch}, acc when not is_nil(switch) ->
        filter(acc, agent_recharge_switch == ^switch)

      _other, acc ->
        acc
    end)
  end

  # 应用排序
  defp apply_platform_sorting(query, params) do
    sort_by = Map.get(params, "sort_by", "inserted_at")
    sort_order = Map.get(params, "sort_order", "desc")

    case {sort_by, sort_order} do
      {"inserted_at", "desc"} -> sort(query, desc: :inserted_at)
      {"inserted_at", "asc"} -> sort(query, asc: :inserted_at)
      {"platform_name", "asc"} -> sort(query, asc: :platform_name)
      {"platform_name", "desc"} -> sort(query, desc: :platform_name)
      {"platform_number", "asc"} -> sort(query, asc: :platform_number)
      {"platform_number", "desc"} -> sort(query, desc: :platform_number)
      _ -> sort(query, desc: :inserted_at)
    end
  end

  # 应用分页
  defp apply_platform_pagination(query, params) do
    page = Map.get(params, "page", 1) |> ensure_integer()
    page_size = Map.get(params, "page_size", 20) |> ensure_integer()

    query
    |> limit(^page_size)
    |> offset(^((page - 1) * page_size))
  end

  # 构建分页信息
  defp build_pagination_info(platforms, params) do
    page = Map.get(params, "page", 1) |> ensure_integer()
    page_size = Map.get(params, "page_size", 20) |> ensure_integer()

    %{
      current_page: page,
      page_size: page_size,
      total_count: length(platforms),
      has_next: length(platforms) == page_size,
      has_prev: page > 1
    }
  end

  # 确保整数类型
  defp ensure_integer(value) when is_integer(value), do: value
  defp ensure_integer(value) when is_binary(value) do
    case Integer.parse(value) do
      {int, _} -> int
      :error -> 1
    end
  end
  defp ensure_integer(_), do: 1

  # 缓存操作
  defp get_from_cache(cache_key) do
    # 这里可以集成实际的缓存系统，如 Cachex 或 ETS
    {:error, :not_found}
  end

  defp cache_platform(cache_key, platform) do
    # 这里可以集成实际的缓存系统
    :ok
  end

  defp clear_platform_cache do
    # 这里可以清理相关缓存
    :ok
  end
end
