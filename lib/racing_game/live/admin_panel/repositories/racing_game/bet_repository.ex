defmodule RacingGame.Live.AdminPanel.Repositories.RacingGame.BetRepository do
  @moduledoc """
  投注数据访问仓储

  提供投注相关的数据访问抽象：
  - 投注基础管理
  - 投注状态查询
  - 投注统计分析
  - 用户投注历史
  """

  require Logger
  alias Ash.Query
  import Ash.Query
  alias RacingGame.{Bet, Race}
  alias RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder
  require Ash.Query

  # 常量定义
  @cache_ttl 300  # 5分钟缓存
  @default_limit 20
  @max_limit 100

  # ============================================================================
  # 投注基础查询
  # ============================================================================

  @doc """
  根据ID获取投注

  ## 参数
  - `bet_id` - 投注ID
  - `options` - 选项

  ## 返回
  - `{:ok, bet}` - 成功
  - `{:error, :not_found}` - 投注不存在
  - `{:error, reason}` - 其他错误
  """
  def get_bet_by_id(bet_id, options \\ []) do
    Logger.debug("🔍 [投注仓储] 根据ID获取投注: #{bet_id}")

    preload = Keyword.get(options, :preload, [])
    use_cache = Keyword.get(options, :use_cache, true)

    cache_key = build_cache_key("bet", bet_id, preload)

    if use_cache do
      case get_from_cache(cache_key) do
        {:ok, bet} -> {:ok, bet}
        :miss -> fetch_and_cache_bet_by_id(bet_id, preload, cache_key)
      end
    else
      fetch_bet_by_id(bet_id, preload)
    end
  end

  @doc """
  分页查询投注列表

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, %{bets: bets, page_info: page_info}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_bets_paginated(params \\ %{}) do
    Logger.debug("📋 [投注仓储] 分页查询投注列表")

    try do
      normalized_params = normalize_list_params(params)

      with {:ok, query} <- RacingGameQueryBuilder.build_bet_list_query(normalized_params),
           {:ok, results} <- execute_paginated_query(query, normalized_params) do
        {:ok, %{bets: results.items, page_info: results.page_info}}
      else
        {:error, reason} -> {:error, reason}
      end
    rescue
      error ->
        Logger.error("❌ [投注仓储] 分页查询异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  获取用户投注历史

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - `{:ok, bets}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_bets(user_id, options \\ []) do
    Logger.debug("👤 [投注仓储] 获取用户投注历史: #{user_id}")

    try do
      query = Bet
      |> Ash.Query.filter(user_id == user_id)
      |> maybe_preload(options[:preload])
      |> apply_sort(options[:sort] || [inserted_at: :desc])
      |> maybe_limit(options[:limit])
      |> maybe_date_range(options[:date_range])

      case Ash.read(query) do
        {:ok, bets} -> {:ok, bets}
        {:error, error} ->
          Logger.error("❌ [投注仓储] 获取用户投注失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [投注仓储] 获取用户投注异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  获取比赛投注

  ## 参数
  - `race_id` - 比赛ID
  - `options` - 选项

  ## 返回
  - `{:ok, bets}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_race_bets(race_id, options \\ []) do
    Logger.debug("🏁 [投注仓储] 获取比赛投注: #{race_id}")

    try do
      query = Bet
      |> Ash.Query.filter(race_id == race_id)
      |> maybe_preload(options[:preload])
      |> apply_sort(options[:sort] || [inserted_at: :desc])
      |> maybe_limit(options[:limit])

      case Ash.read(query) do
        {:ok, bets} -> {:ok, bets}
        {:error, error} ->
          Logger.error("❌ [投注仓储] 获取比赛投注失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [投注仓储] 获取比赛投注异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  获取待结算投注

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, bets}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_pending_settlement_bets(options \\ []) do
    Logger.debug("⏳ [投注仓储] 获取待结算投注")

    try do
      query = Bet
      |> Ash.Query.filter(status == :pending)
      |> maybe_preload(options[:preload])
      |> apply_sort([inserted_at: :asc])
      |> maybe_limit(options[:limit])

      case Ash.read(query) do
        {:ok, bets} -> {:ok, bets}
        {:error, error} ->
          Logger.error("❌ [投注仓储] 获取待结算投注失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [投注仓储] 获取待结算投注异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # ============================================================================
  # 投注创建和管理
  # ============================================================================

  @doc """
  创建投注

  ## 参数
  - `bet_data` - 投注数据

  ## 返回
  - `{:ok, bet}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_bet(bet_data) do
    Logger.info("➕ [投注仓储] 创建投注: 用户#{bet_data[:user_id]} 投注#{bet_data[:amount]}")

    try do
      case Bet.create(bet_data) do
        {:ok, bet} ->
          clear_cache(:bet)
          Logger.info("✅ [投注仓储] 投注创建成功: #{bet.id}")
          {:ok, bet}
        {:error, error} ->
          Logger.error("❌ [投注仓储] 创建投注失败: #{inspect(error)}")
          {:error, :create_failed}
      end
    rescue
      error ->
        Logger.error("❌ [投注仓储] 创建投注异常: #{inspect(error)}")
        {:error, :create_error}
    end
  end

  @doc """
  批量创建投注

  ## 参数
  - `bets_data` - 投注数据列表

  ## 返回
  - `{:ok, bets}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_bets_batch(bets_data) do
    Logger.info("📦 [投注仓储] 批量创建投注: #{length(bets_data)} 笔")

    try do
      results = Enum.map(bets_data, &create_bet/1)

      case Enum.split_with(results, &match?({:ok, _}, &1)) do
        {successes, []} ->
          bets = Enum.map(successes, fn {:ok, bet} -> bet end)
          Logger.info("✅ [投注仓储] 批量投注全部成功: #{length(bets)} 笔")
          {:ok, bets}
        {successes, failures} ->
          Logger.warn("⚠️ [投注仓储] 批量投注部分失败: 成功 #{length(successes)}, 失败 #{length(failures)}")
          {:error, {:partial_failure, successes, failures}}
      end
    rescue
      error ->
        Logger.error("❌ [投注仓储] 批量投注异常: #{inspect(error)}")
        {:error, :batch_error}
    end
  end

  @doc """
  更新投注状态

  ## 参数
  - `bet_id` - 投注ID
  - `status` - 新状态
  - `options` - 选项

  ## 返回
  - `{:ok, bet}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_bet_status(bet_id, status, options \\ []) do
    Logger.info("🔄 [投注仓储] 更新投注状态: #{bet_id} -> #{status}")

    with {:ok, bet} <- get_bet_by_id(bet_id, use_cache: false),
         :ok <- validate_status_transition(bet.status, status),
         {:ok, updated_bet} <- do_update_bet_status(bet, status, options) do

      clear_cache(:bet, bet_id)
      Logger.info("✅ [投注仓储] 投注状态更新成功: #{bet_id}")
      {:ok, updated_bet}
    else
      error -> error
    end
  end

  @doc """
  结算投注

  ## 参数
  - `bet_id` - 投注ID
  - `settlement_data` - 结算数据

  ## 返回
  - `{:ok, bet}` - 成功
  - `{:error, reason}` - 失败
  """
  def settle_bet(bet_id, settlement_data) do
    Logger.info("💰 [投注仓储] 结算投注: #{bet_id}")

    with {:ok, bet} <- get_bet_by_id(bet_id, use_cache: false),
         :ok <- validate_bet_settlement(bet),
         {:ok, settled_bet} <- do_settle_bet(bet, settlement_data) do

      clear_cache(:bet, bet_id)
      Logger.info("✅ [投注仓储] 投注结算成功: #{bet_id}")
      {:ok, settled_bet}
    else
      error -> error
    end
  end

  @doc """
  取消投注

  ## 参数
  - `bet_id` - 投注ID
  - `reason` - 取消原因

  ## 返回
  - `{:ok, bet}` - 成功
  - `{:error, reason}` - 失败
  """
  def cancel_bet(bet_id, reason \\ "用户取消") do
    Logger.info("❌ [投注仓储] 取消投注: #{bet_id} - #{reason}")

    with {:ok, bet} <- get_bet_by_id(bet_id),
         :ok <- validate_bet_cancellation(bet),
         {:ok, cancelled_bet} <- do_cancel_bet(bet, reason) do

      clear_cache(:bet, bet_id)
      Logger.info("✅ [投注仓储] 投注取消成功: #{bet_id}")
      {:ok, cancelled_bet}
    else
      error -> error
    end
  end

  # ============================================================================
  # 投注统计
  # ============================================================================

  @doc """
  获取投注统计信息

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, statistics}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_bet_statistics(options \\ []) do
    Logger.debug("📊 [投注仓储] 获取投注统计")

    try do
      stats = %{
        total_bets: get_total_bets(),
        bets_by_status: get_bets_by_status(),
        bets_by_type: get_bets_by_type(),
        total_bet_amount: get_total_bet_amount(),
        total_payout: get_total_payout(),
        recent_bets: get_recent_bets_count(options[:days] || 7),
        active_bets: get_active_bets_count()
      }

      {:ok, stats}
    rescue
      error ->
        Logger.error("❌ [投注仓储] 获取统计异常: #{inspect(error)}")
        {:error, :statistics_error}
    end
  end

  @doc """
  获取用户投注统计

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - `{:ok, statistics}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_bet_statistics(user_id, options \\ []) do
    Logger.debug("👤 [投注仓储] 获取用户投注统计: #{user_id}")

    try do
      stats = %{
        total_bets: get_user_total_bets(user_id),
        total_bet_amount: get_user_total_bet_amount(user_id),
        total_winnings: get_user_total_winnings(user_id),
        win_rate: get_user_win_rate(user_id),
        recent_activity: get_user_recent_activity(user_id, options[:days] || 30)
      }

      {:ok, stats}
    rescue
      error ->
        Logger.error("❌ [投注仓储] 获取用户统计异常: #{inspect(error)}")
        {:error, :statistics_error}
    end
  end

  # ============================================================================
  # 缓存管理
  # ============================================================================

  @doc """
  清除投注相关缓存

  ## 参数
  - `cache_type` - 缓存类型
  - `id` - ID（可选）
  """
  def clear_cache(cache_type, id \\ nil) do
    cache_patterns = case {cache_type, id} do
      {:bet, nil} -> ["bet:*"]
      {:bet, bet_id} -> ["bet:#{bet_id}:*"]
      {:user_bets, user_id} -> ["user_bets:#{user_id}:*"]
      {:race_bets, race_id} -> ["race_bets:#{race_id}:*"]
      {:all, _} -> ["bet:*", "user_bets:*", "race_bets:*"]
    end

    Logger.debug("🧹 [投注仓储] 清除缓存: #{inspect(cache_patterns)}")
    Enum.each(cache_patterns, &clear_cache_pattern/1)
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 标准化列表参数
  defp normalize_list_params(params) do
    %{
      page: max(1, params[:page] || 1),
      limit: min(params[:limit] || @default_limit, @max_limit),
      search: String.trim(params[:search] || ""),
      filters: params[:filters] || %{},
      sort: params[:sort] || [inserted_at: :desc],
      preload: params[:preload] || []
    }
  end

  # 获取并缓存投注
  defp fetch_and_cache_bet_by_id(bet_id, preload, cache_key) do
    case fetch_bet_by_id(bet_id, preload) do
      {:ok, bet} ->
        cache_put(cache_key, bet, @cache_ttl)
        {:ok, bet}
      error -> error
    end
  end

  # 获取投注
  defp fetch_bet_by_id(bet_id, preload) do
    try do
      case Bet
           |> maybe_preload(preload)
           |> Ash.get(bet_id) do
        {:ok, bet} -> {:ok, bet}
        {:error, %Ash.Error.Query.NotFound{}} -> {:error, :not_found}
        {:error, error} ->
          Logger.error("❌ [投注仓储] 获取投注失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [投注仓储] 获取投注异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # 验证状态转换
  defp validate_status_transition(current_status, new_status) do
    valid_transitions = %{
      :pending => [:won, :lost, :cancelled, :refunded],
      :won => [:paid],
      :lost => [],
      :cancelled => [],
      :refunded => [],
      :paid => []
    }

    allowed_statuses = Map.get(valid_transitions, current_status, [])

    if new_status in allowed_statuses do
      :ok
    else
      {:error, :invalid_status_transition}
    end
  end

  # 执行更新投注状态
  defp do_update_bet_status(bet, status, options) do
    update_data = %{status: status}
    update_data = if options[:reason], do: Map.put(update_data, :status_reason, options[:reason]), else: update_data
    update_data = if options[:payout_amount], do: Map.put(update_data, :payout_amount, options[:payout_amount]), else: update_data

    try do
      case bet
           |> Ash.Changeset.for_update(:update, update_data)
           |> Ash.update() do
        {:ok, updated_bet} -> {:ok, updated_bet}
        {:error, error} ->
          Logger.error("❌ [投注仓储] 更新投注状态失败: #{inspect(error)}")
          {:error, :update_failed}
      end
    rescue
      error ->
        Logger.error("❌ [投注仓储] 更新投注状态异常: #{inspect(error)}")
        {:error, :update_error}
    end
  end

  # 验证投注结算
  defp validate_bet_settlement(bet) do
    case bet.status do
      :pending -> :ok
      :won -> {:error, :already_settled}
      :lost -> {:error, :already_settled}
      :cancelled -> {:error, :bet_cancelled}
      :refunded -> {:error, :bet_refunded}
      _ -> {:error, :invalid_status}
    end
  end

  # 执行投注结算
  defp do_settle_bet(bet, settlement_data) do
    update_data = Map.merge(%{
      status: settlement_data[:status],
      payout_amount: settlement_data[:payout_amount] || Money.new(0, :XAA),
      settled_at: DateTime.utc_now()
    }, settlement_data[:metadata] || %{})

    try do
      case bet
           |> Ash.Changeset.for_update(:settle, update_data)
           |> Ash.update() do
        {:ok, settled_bet} -> {:ok, settled_bet}
        {:error, error} ->
          Logger.error("❌ [投注仓储] 结算投注失败: #{inspect(error)}")
          {:error, :settlement_failed}
      end
    rescue
      error ->
        Logger.error("❌ [投注仓储] 结算投注异常: #{inspect(error)}")
        {:error, :settlement_error}
    end
  end

  # 验证投注取消
  defp validate_bet_cancellation(bet) do
    case bet.status do
      :pending -> :ok
      :won -> {:error, :bet_won}
      :lost -> {:error, :bet_lost}
      :cancelled -> {:error, :already_cancelled}
      :paid -> {:error, :bet_paid}
      _ -> {:error, :invalid_status}
    end
  end

  # 执行取消投注
  defp do_cancel_bet(bet, reason) do
    try do
      case bet
           |> Ash.Changeset.for_update(:cancel, %{cancellation_reason: reason})
           |> Ash.update() do
        {:ok, cancelled_bet} -> {:ok, cancelled_bet}
        {:error, error} ->
          Logger.error("❌ [投注仓储] 取消投注失败: #{inspect(error)}")
          {:error, :cancel_failed}
      end
    rescue
      error ->
        Logger.error("❌ [投注仓储] 取消投注异常: #{inspect(error)}")
        {:error, :cancel_error}
    end
  end

  # 执行分页查询
  defp execute_paginated_query(query, params) do
    offset = (params.page - 1) * params.limit

    case query
         |> maybe_preload(params.preload)
         |> Ash.Query.page(count: true, limit: params.limit, offset: offset)
         |> Ash.read() do
      {:ok, %Ash.Page.Offset{results: items, count: total_count}} ->
        page_info = %{
          total_count: total_count,
          page: params.page,
          limit: params.limit,
          total_pages: ceil(total_count / params.limit)
        }
        {:ok, %{items: items, page_info: page_info}}
      {:error, error} ->
        Logger.error("❌ [投注仓储] 分页查询失败: #{inspect(error)}")
        {:error, :query_failed}
    end
  end

  # 可能预加载关联数据
  defp maybe_preload(query, []), do: query
  defp maybe_preload(query, preload) when is_list(preload) do
    Ash.Query.load(query, preload)
  end

  # 可能限制数量
  defp maybe_limit(query, nil), do: query
  defp maybe_limit(query, limit) when is_integer(limit) and limit > 0 do
    Ash.Query.limit(query, limit)
  end
  defp maybe_limit(query, _), do: query

  # 应用排序
  defp apply_sort(query, sort) when is_list(sort) do
    Ash.Query.sort(query, sort)
  end
  defp apply_sort(query, _), do: query

  # 可能应用日期范围
  defp maybe_date_range(query, nil), do: query
  defp maybe_date_range(query, {start_date, end_date}) do
    query
    |> Ash.Query.filter(inserted_at >= start_date and inserted_at  <= end_date)
  end
  defp maybe_date_range(query, _), do: query

  # 统计函数（简化实现）
  defp get_total_bets do
    case Bet |> Ash.Query.aggregate(:count, :id) |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_bets_by_status do
    case Bet
         |> Ash.Query.aggregate(:count, :id, group: [:status])
         |> Ash.read() do
      {:ok, results} ->
        Enum.into(results, %{}, fn %{status: status, count: count} ->
          {status, count}
        end)
      _ -> %{}
    end
  end

  defp get_bets_by_type do
    case Bet
         |> Ash.Query.aggregate(:count, :id, group: [:bet_type])
         |> Ash.read() do
      {:ok, results} ->
        Enum.into(results, %{}, fn %{bet_type: type, count: count} ->
          {type, count}
        end)
      _ -> %{}
    end
  end

  defp get_total_bet_amount do
    # 这里需要根据实际的金额聚合逻辑实现
    Money.new(0, :XAA)
  end

  defp get_total_payout do
    # 这里需要根据实际的支付聚合逻辑实现
    Money.new(0, :XAA)
  end

  defp get_recent_bets_count(days) do
    date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)

    case Bet
         |> Ash.Query.filter(inserted_at >= date_threshold)
         |> Ash.Query.aggregate(:count, :id)
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_active_bets_count do
    case Bet
         |> Ash.Query.filter(status == :pending)
         |> Ash.Query.aggregate(:count, :id)
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  # 用户统计函数
  defp get_user_total_bets(user_id) do
    case Bet
         |> Ash.Query.filter(user_id  == user_id)
         |> Ash.Query.aggregate(:count, :id)
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_user_total_bet_amount(user_id) do
    # 这里需要根据实际的用户投注金额聚合逻辑实现
    Money.new(0, :XAA)
  end

  defp get_user_total_winnings(user_id) do
    # 这里需要根据实际的用户赢利聚合逻辑实现
    Money.new(0, :XAA)
  end

  defp get_user_win_rate(user_id) do
    # 这里需要根据实际的胜率计算逻辑实现
    0.0
  end

  defp get_user_recent_activity(user_id, days) do
    date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)

    case Bet
         |> Ash.Query.filter(user_id == user_id and inserted_at >= date_threshold)
         |> Ash.Query.aggregate(:count, :id)
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  # 构建缓存键
  defp build_cache_key(prefix, id, extra \\ nil) do
    case extra do
      nil -> "#{prefix}:#{id}"
      extra -> "#{prefix}:#{id}:#{:erlang.phash2(extra)}"
    end
  end

  # 缓存操作（需要实现具体的缓存后端）
  defp get_from_cache(_key), do: :miss
  defp cache_put(_key, _value, _ttl), do: :ok
  defp clear_cache_pattern(_pattern), do: :ok
end
