# 游戏配置文件
# 在这里配置要启用的游戏类型

import Config

# 配置要注册的游戏模块
config :cypridina, :games,
  # 内置游戏（自动注册）
  builtin: [
    Cypridina.Teen.GameSystem.Games.TeenPatti.Slot<PERSON><PERSON>Game,
    Cypridina.Teen.GameSystem.Games.Slot777.Slot777Game,
    Cypridina.Teen.GameSystem.Games.Explorer.ExplorerGame,
    Cypridina.Teen.GameSystem.Games.JhandiMunda.JhandiMundaGame,
    Cypridina.Teen.GameSystem.Games.LongHu.LongHuGame,
    Cypridina.Teen.GameSystem.Games.TeenPatti.TeenPattiGame,
    Cypridina.Teen.GameSystem.Games.PotBlind.PotBlindGame,
    Cypridina.Teen.GameSystem.Games.SafariOfWealth.SafariOfWealthGame
  ],

  # 可选游戏（需要手动启用）
  optional: [
    # Cy<PERSON><PERSON><PERSON>.Teen.GameSystem.Games.Baccarat.Baccarat<PERSON><PERSON>,
    # <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.AndarBahar.AndarBaharGame,
    # Cy<PERSON>ridina.Teen.GameSystem.Games.Rummy.RummyGame
  ],

  # 第三方游戏插件
  plugins: [
    # MyCompany.Games.CustomGame
  ]

# 游戏特定配置
config :cypridina, :game_configs,
  # 龙虎斗配置覆盖
  longhu: %{
    # 可以在这里覆盖默认配置
    # bet_time: 20,
    # max_bet: 50000
  },

  # Teen Patti配置覆盖
  teen_patti: %{
    # max_players: 8,
    # robot_count: 4
  },

  # Explorer配置覆盖
  explorer: %{
    # max_players: 8,
    # min_bet: 50,
    # explore_time: 15
  },

  # Jhandi Munda配置覆盖
  jhandi_munda: %{
    # max_players: 8,
    # dice_count: 6,
    # bet_time: 25
  },

  # AK47 Teen Patti配置覆盖
  ak47_teen_patti: %{
    # max_players: 6,
    # min_bet: 100,
    # ante: 50
  },

  # Pot Blind配置覆盖
  pot_blind: %{
    # max_players: 6,
    # small_blind: 25,
    # big_blind: 50
  },

  # Safari of Wealth配置覆盖
  safari_of_wealth: %{
    # min_bet: 100,
    # max_bet: 10000,
    # paylines: 25
  }

# 房间管理器配置
config :cypridina, :room_manager,
  # 房间超时时间（毫秒）
  room_timeout: 300_000,
  # 最大房间数量
  max_rooms: 1000,
  # 房间清理间隔（毫秒）
  cleanup_interval: 60_000
