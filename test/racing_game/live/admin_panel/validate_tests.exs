#!/usr/bin/env elixir

# 🧪 Teen系统测试文件验证脚本
# 验证所有测试文件的语法正确性，不实际运行测试

defmodule TestValidator do
  @moduledoc """
  验证测试文件的语法和基本结构
  """

  @test_files [
    "test_helper.ex",
    "repositories/repository_integration_test.exs",
    "query_builders/query_builder_integration_test.exs", 
    "services/service_integration_test.exs",
    "cross_system_integration_test.exs",
    "performance_load_test.exs",
    "test_runner.exs",
    "test_config.exs"
  ]

  def run_validation do
    IO.puts("🧪 开始验证Teen系统测试文件...")
    IO.puts("=" |> String.duplicate(50))
    
    results = Enum.map(@test_files, &validate_file/1)
    
    # 统计结果
    total_files = length(results)
    passed_files = results |> Enum.count(fn {_, status} -> status == :ok end)
    failed_files = total_files - passed_files
    
    IO.puts("\n" <> "=" |> String.duplicate(50))
    IO.puts("📊 验证结果统计:")
    IO.puts("   总文件数: #{total_files}")
    IO.puts("   通过: #{passed_files} ✅")
    IO.puts("   失败: #{failed_files} ❌")
    IO.puts("   成功率: #{Float.round(passed_files / total_files * 100, 2)}%")
    
    if failed_files == 0 do
      IO.puts("\n🎉 所有测试文件验证通过！")
      System.halt(0)
    else
      IO.puts("\n❌ 部分测试文件验证失败，请检查上述错误信息")
      System.halt(1)
    end
  end

  defp validate_file(file_path) do
    full_path = Path.join([File.cwd!(), file_path])
    
    IO.write("验证 #{file_path}... ")
    
    cond do
      not File.exists?(full_path) ->
        IO.puts("❓ 文件不存在")
        {file_path, :not_found}
      
      Path.extname(file_path) in [".ex", ".exs"] ->
        validate_elixir_file(full_path, file_path)
      
      true ->
        IO.puts("⚠️  跳过非Elixir文件")
        {file_path, :skipped}
    end
  end

  defp validate_elixir_file(full_path, file_path) do
    try do
      # 读取文件内容
      content = File.read!(full_path)
      
      # 基本语法检查
      case Code.string_to_quoted(content) do
        {:ok, _ast} ->
          # 进一步验证文件结构
          case validate_file_structure(content, file_path) do
            :ok ->
              IO.puts("✅ 通过")
              {file_path, :ok}
            {:error, reason} ->
              IO.puts("❌ 结构错误: #{reason}")
              {file_path, :structure_error}
          end
        
        {:error, {line, error_info, token}} ->
          error_msg = "第#{line}行语法错误: #{format_syntax_error(error_info, token)}"
          IO.puts("❌ #{error_msg}")
          {file_path, :syntax_error}
      end
      
    rescue
      error ->
        IO.puts("❌ 读取错误: #{inspect(error)}")
        {file_path, :read_error}
    end
  end

  defp validate_file_structure(content, file_path) do
    cond do
      String.contains?(file_path, "_test.exs") ->
        validate_test_file_structure(content)
      
      String.contains?(file_path, "test_helper.ex") ->
        validate_helper_file_structure(content)
      
      String.contains?(file_path, "test_config.exs") ->
        validate_config_file_structure(content)
      
      String.contains?(file_path, "test_runner.exs") ->
        validate_runner_file_structure(content)
      
      true ->
        :ok
    end
  end

  defp validate_test_file_structure(content) do
    required_patterns = [
      ~r/defmodule\s+\w+.*Test\s+do/,  # 测试模块定义
      ~r/use\s+ExUnit\.Case/,          # ExUnit使用
      ~r/describe\s+/,                 # 至少一个describe块
      ~r/test\s+/                      # 至少一个test
    ]
    
    missing_patterns = required_patterns
    |> Enum.reject(fn pattern -> Regex.match?(pattern, content) end)
    
    if length(missing_patterns) == 0 do
      :ok
    else
      {:error, "缺少必要的测试结构"}
    end
  end

  defp validate_helper_file_structure(content) do
    required_patterns = [
      ~r/defmodule\s+.*TestHelper\s+do/,  # TestHelper模块
      ~r/def\s+generate_test_/,           # 测试数据生成函数
      ~r/def\s+setup_test_environment/,   # 环境设置函数
    ]
    
    missing_patterns = required_patterns
    |> Enum.reject(fn pattern -> Regex.match?(pattern, content) end)
    
    if length(missing_patterns) == 0 do
      :ok
    else
      {:error, "缺少必要的Helper结构"}
    end
  end

  defp validate_config_file_structure(content) do
    if Regex.match?(~r/import\s+Config/, content) or Regex.match?(~r/use\s+Mix\.Config/, content) do
      :ok
    else
      {:error, "缺少Config导入"}
    end
  end

  defp validate_runner_file_structure(content) do
    required_patterns = [
      ~r/defmodule\s+.*TestRunner\s+do/,  # TestRunner模块
      ~r/def\s+run_all_tests/,            # 运行所有测试函数
      ~r/def\s+run_test_suite/,           # 运行测试套件函数
    ]
    
    missing_patterns = required_patterns
    |> Enum.reject(fn pattern -> Regex.match?(pattern, content) end)
    
    if length(missing_patterns) == 0 do
      :ok
    else
      {:error, "缺少必要的Runner结构"}
    end
  end

  defp format_syntax_error(error_info, token) do
    case error_info do
      {message, _} when is_binary(message) -> message
      message when is_binary(message) -> message
      _ -> "语法错误，token: #{inspect(token)}"
    end
  end

  def check_dependencies do
    IO.puts("\n🔍 检查测试依赖...")
    
    # 检查必要的依赖模块
    dependencies = [
      {"ExUnit", "Elixir测试框架"},
      {"Jason", "JSON处理库"},
      {"Decimal", "精确数值计算库"}
    ]
    
    Enum.each(dependencies, fn {module_name, description} ->
      IO.write("检查 #{module_name} (#{description})... ")
      
      try do
        module = String.to_existing_atom("Elixir.#{module_name}")
        Code.ensure_loaded(module)
        IO.puts("✅ 可用")
      rescue
        _ ->
          IO.puts("❌ 不可用")
      end
    end)
  end

  def show_file_stats do
    IO.puts("\n📈 测试文件统计:")
    
    @test_files
    |> Enum.each(fn file_path ->
      full_path = Path.join([File.cwd!(), file_path])
      
      if File.exists?(full_path) do
        stat = File.stat!(full_path)
        content = File.read!(full_path)
        lines = content |> String.split("\n") |> length()
        size_kb = Float.round(stat.size / 1024, 2)
        
        IO.puts("   #{file_path}:")
        IO.puts("     大小: #{size_kb} KB")
        IO.puts("     行数: #{lines}")
        IO.puts("     修改时间: #{format_datetime(stat.mtime)}")
      else
        IO.puts("   #{file_path}: 文件不存在")
      end
    end)
  end

  defp format_datetime({{year, month, day}, {hour, minute, second}}) do
    "#{year}-#{String.pad_leading(to_string(month), 2, "0")}-#{String.pad_leading(to_string(day), 2, "0")} " <>
    "#{String.pad_leading(to_string(hour), 2, "0")}:#{String.pad_leading(to_string(minute), 2, "0")}:#{String.pad_leading(to_string(second), 2, "0")}"
  end

  def show_test_coverage do
    IO.puts("\n📋 测试覆盖范围:")
    
    coverage_areas = [
      {"Repository层", "CRUD操作、缓存机制、数据一致性"},
      {"QueryBuilder层", "复杂查询、并行处理、统计分析"},
      {"Service层", "业务流程、跨系统协调、事务管理"},
      {"跨系统集成", "用户生命周期、数据流转、业务协调"},
      {"性能测试", "高并发、响应时间、资源使用"},
      {"负载测试", "极限负载、吞吐量、稳定性"}
    ]
    
    Enum.each(coverage_areas, fn {area, description} ->
      IO.puts("   ✅ #{area}: #{description}")
    end)
  end
end

# 主程序入口
case System.argv() do
  ["--stats"] ->
    TestValidator.show_file_stats()
  
  ["--coverage"] ->
    TestValidator.show_test_coverage()
  
  ["--deps"] ->
    TestValidator.check_dependencies()
  
  ["--all"] ->
    TestValidator.run_validation()
    TestValidator.check_dependencies()
    TestValidator.show_file_stats()
    TestValidator.show_test_coverage()
  
  [] ->
    TestValidator.run_validation()
  
  _ ->
    IO.puts("用法:")
    IO.puts("  elixir validate_tests.exs           # 验证测试文件")
    IO.puts("  elixir validate_tests.exs --stats   # 显示文件统计")
    IO.puts("  elixir validate_tests.exs --coverage # 显示测试覆盖")
    IO.puts("  elixir validate_tests.exs --deps    # 检查依赖")
    IO.puts("  elixir validate_tests.exs --all     # 运行所有检查")
end
