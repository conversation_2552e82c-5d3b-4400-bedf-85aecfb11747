defmodule Teen.CustomerService.UserQuestion do
  @moduledoc """
  用户问题资源
  
  管理用户提交的问题，包括问题分类、处理状态等
  """
  
  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.CustomerService,
    extensions: [AshAdmin.Resource]

  postgres do
    table "user_questions"
    repo Cypridina.Repo
  end

  admin do
    table_columns [:id, :title, :user_id, :question_type, :status, :inserted_at]
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_by_status
    define :list_by_type
    define :list_by_user
  end

  actions do
    defaults [:create, :read, :update, :destroy]

    read :list_by_status do
      argument :status, :integer, allow_nil?: false
      filter expr(status == ^arg(:status))
    end

    read :list_by_type do
      argument :question_type, :integer, allow_nil?: false
      filter expr(question_type == ^arg(:question_type))
    end

    read :list_by_user do
      argument :user_id, :uuid, allow_nil?: false
      filter expr(user_id == ^arg(:user_id))
    end

    update :assign_to_staff do
      argument :staff_id, :uuid, allow_nil?: false
      change set_attribute(:assigned_staff_id, arg(:staff_id))
      change set_attribute(:status, 1)
    end

    update :mark_completed do
      change set_attribute(:status, 2)
      change set_attribute(:completed_at, &DateTime.utc_now/0)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :user_id, :uuid do
      allow_nil? false
      public? true
      description "用户ID"
    end

    attribute :title, :string do
      allow_nil? false
      public? true
      description "问题标题"
      constraints max_length: 200
    end

    attribute :content, :string do
      allow_nil? false
      public? true
      description "问题内容"
      constraints max_length: 2000
    end

    attribute :question_type, :integer do
      allow_nil? false
      public? true
      description "问题类型：1-技术问题，2-账户问题，3-支付问题，4-游戏问题，5-其他"
      constraints min: 1, max: 5
    end

    attribute :priority, :integer do
      allow_nil? false
      public? true
      description "优先级：1-低，2-中，3-高，4-紧急"
      default 2
      constraints min: 1, max: 4
    end

    attribute :status, :integer do
      allow_nil? false
      public? true
      description "状态：0-新创建，1-处理中，2-已完成"
      default 0
      constraints min: 0, max: 2
    end

    attribute :contact_phone, :string do
      allow_nil? true
      public? true
      description "联系电话"
      constraints max_length: 20
    end

    attribute :contact_email, :string do
      allow_nil? true
      public? true
      description "联系邮箱"
      constraints max_length: 100
    end

    attribute :assigned_staff_id, :uuid do
      allow_nil? true
      public? true
      description "分配的客服人员ID"
    end

    attribute :completed_at, :utc_datetime do
      allow_nil? true
      public? true
      description "完成时间"
    end

    attribute :images, {:array, :string} do
      allow_nil? true
      public? true
      description "问题相关图片"
      default []
    end

    timestamps()
  end

  relationships do
    belongs_to :user, Cypridina.Accounts.User do
      public? true
      source_attribute :user_id
      destination_attribute :id
    end

    belongs_to :assigned_staff, Cypridina.Accounts.User do
      public? true
      source_attribute :assigned_staff_id
      destination_attribute :id
    end
  end
end
