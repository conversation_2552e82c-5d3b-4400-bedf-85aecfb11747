defmodule <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.TeenPatti.TeenPattiGame do
  @moduledoc """
  Teen Patti游戏定义模块

  实现游戏工厂行为，定义Teen Patti游戏的基本信息和配置
  """

  @behaviour Cypridina.RoomSystem.GameFactory

  @impl true
  def game_type, do: :teen_patti

  @impl true
  def game_name, do: "<PERSON> Patti"

  @impl true
  def room_module, do: <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.TeenPatti.TeenPattiRoom

  @impl true
  def default_config do
    %{
      max_players: 6,
      min_players: 2,
      # 5秒后自动开始
      auto_start_delay: 5000,
      enable_robots: true,
      robot_count: 3,
      # 最小下注
      min_bet: 10,
      # 最大下注
      max_bet: 1000,
      # 底注
      ante: 5,
      # 最大轮数
      max_rounds: 50,
      # 摊牌费用倍数
      show_cost_multiplier: 2
    }
  end

  @impl true
  def is_lobby_game?, do: false

  @impl true
  def supported_game_ids do
    [
      # <PERSON> Patti
      1
    ]
  end
end
