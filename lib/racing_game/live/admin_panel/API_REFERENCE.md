# API 接口参考文档

## 📋 概述

本文档提供了 Racing Game Admin Panel 系统的完整 API 接口参考，包括 Repository 层、Service 层和 QueryBuilder 层的所有公共接口。

## 🏗️ 架构层次

```
┌─────────────────────────────────────────┐
│           Service Layer                 │
│  (业务逻辑层 - 高级业务操作)              │
├─────────────────────────────────────────┤
│         QueryBuilder Layer              │
│  (查询构建器层 - 复杂查询和分析)          │
├─────────────────────────────────────────┤
│         Repository Layer                │
│  (数据访问层 - 基础CRUD操作)             │
└─────────────────────────────────────────┘
```

## 📚 Repository 层 API

### 🎯 标准接口规范

所有 Repository 模块都遵循统一的接口规范：

#### 基础 CRUD 操作
```elixir
# 列表查询
list_items(filters \\ %{}, options \\ []) :: {:ok, [item]} | {:error, reason}

# 获取单个项目
get_item(id, options \\ []) :: {:ok, item} | {:error, reason}

# 创建项目
create_item(data, options \\ []) :: {:ok, item} | {:error, reason}

# 更新项目
update_item(id, data, options \\ []) :: {:ok, item} | {:error, reason}

# 删除项目
delete_item(id, options \\ []) :: {:ok, item} | {:error, reason}
```

#### 通用参数说明

**filters 参数** (Map):
- `status` - 状态过滤 (integer | atom)
- `type` - 类型过滤 (string | atom)
- `date_range` - 日期范围 `{start_date, end_date}`
- `search` - 搜索关键词 (string)
- `user_id` - 用户ID过滤 (integer)

**options 参数** (Keyword List):
- `limit` - 限制数量 (integer, 默认: 20)
- `offset` - 偏移量 (integer, 默认: 0)
- `sort` - 排序字段 (atom | string)
- `order` - 排序方向 (`:asc` | `:desc`)
- `load` - 关联加载 (list of atoms)
- `use_cache` - 是否使用缓存 (boolean, 默认: true)

**返回值规范**:
- 成功: `{:ok, result}` 或 `{:ok, {items, total_count}}`
- 失败: `{:error, reason}` 其中 reason 为 atom 或 string

### 🏢 Teen 系统 Repository 模块

#### 1. CustomerService 客服管理

**模块**: `RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.CustomerServiceRepository`

```elixir
# 客服管理
list_customers(filters \\ %{}, options \\ [])
get_customer(customer_id, options \\ [])
create_customer(customer_data, options \\ [])
update_customer(customer_id, data, options \\ [])
delete_customer(customer_id, options \\ [])

# 统计信息
get_customer_statistics(options \\ [])
```

**特殊过滤器**:
- `status` - 客服状态 (`:active`, `:inactive`, `:busy`)
- `handled_count_min` - 最小处理数量
- `response_time_max` - 最大响应时间

#### 2. PaymentSystem 支付系统

**模块**: `RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.PaymentOrderRepository`

```elixir
# 支付订单管理
list_payment_orders(filters \\ %{}, options \\ [])
get_payment_order(order_id, options \\ [])
create_payment_order(order_data, options \\ [])
update_payment_order(order_id, update_data, options \\ [])
delete_payment_order(order_id, options \\ [])

# 统计分析
get_payment_statistics(options \\ [])
```

**模块**: `RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.PaymentGatewayRepository`

```elixir
# 支付网关管理
list_payment_gateways(params \\ %{}, options \\ [])
get_payment_gateway(gateway_id, options \\ [])
create_payment_gateway(gateway_data, options \\ [])
update_payment_gateway(gateway_id, update_data, options \\ [])
delete_payment_gateway(gateway_id, options \\ [])

# 网关状态管理
list_active_payment_gateways(options \\ [])
activate_payment_gateway(gateway_id, options \\ [])
deactivate_payment_gateway(gateway_id, options \\ [])
```

#### 3. GameManagement 游戏管理

**模块**: `RacingGame.Live.AdminPanel.Repositories.Teen.GameManagement.PlatformRepository`

```elixir
# 平台配置管理
create_platform(platform_data, options \\ [])
get_platform_by_id(platform_id, options \\ [])
update_platform(platform_id, update_data, options \\ [])
delete_platform(platform_id, options \\ [])
list_platforms(filters \\ %{}, options \\ [])

# 平台状态管理
enable_platform(platform_id, options \\ [])
disable_platform(platform_id, options \\ [])
toggle_agent_recharge(platform_id, options \\ [])
```

#### 4. SystemSettings 系统设置

**模块**: `RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.SystemConfigRepository`

```elixir
# 系统配置管理
list_system_configs(filters \\ %{}, options \\ [])
get_system_config(config_id, options \\ [])
get_config_by_key(config_key, options \\ [])
create_system_config(config_data, options \\ [])
update_system_config(config_id, update_data, options \\ [])
delete_system_config(config_id, options \\ [])

# 配置统计
get_config_statistics(options \\ [])
```

#### 5. ActivitySystem 活动系统

**模块**: `RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.SignInActivityRepository`

```elixir
# 签到活动管理
list_sign_in_activities(filters \\ %{}, options \\ [])
list_sign_in_activities(filters, options) # 重载版本，支持过滤器
get_sign_in_activity(activity_id, options \\ [])
create_sign_in_activity(activity_data, options \\ [])
update_sign_in_activity(activity_id, update_data, options \\ [])
delete_sign_in_activity(activity_id, options \\ [])

# 活动状态查询
list_active_sign_in_activities(options \\ [])
list_current_sign_in_activities(options \\ [])
```

#### 6. DataStatistics 数据统计

**模块**: `RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.ChannelStatsRepository`

```elixir
# 渠道统计管理
create_channel_stats(stats_data, options \\ [])
get_channel_stats(stats_id, options \\ [])
update_channel_stats(channel_stats, update_data, options \\ [])
delete_channel_stats(channel_stats, options \\ [])
```

#### 7. PromotionSystem 推广系统

**模块**: `RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionChannelRepository`

```elixir
# 推广渠道管理
get_channel(channel_id)
list_channels_by_type(channel_type, options \\ [])
list_channels_by_promoter(promoter_id, options \\ [])
list_active_channels(options \\ [])
```

## 🔧 使用示例

### 基础查询示例

```elixir
# 获取活跃客服列表
{:ok, customers} = CustomerServiceRepository.list_customers(
  %{status: :active}, 
  [limit: 10, sort: :handled_count, order: :desc]
)

# 创建支付订单
{:ok, order} = PaymentOrderRepository.create_payment_order(%{
  user_id: 123,
  amount: Decimal.new("100.00"),
  payment_method: "alipay"
})

# 获取系统配置
{:ok, config} = SystemConfigRepository.get_config_by_key("max_bet_amount")
```

### 复杂查询示例

```elixir
# 按日期范围查询支付订单
{:ok, orders} = PaymentOrderRepository.list_payment_orders(
  %{
    status: :completed,
    date_range: {~D[2024-01-01], ~D[2024-01-31]},
    amount_min: Decimal.new("50.00")
  },
  [limit: 50, load: [:user, :gateway]]
)

# 获取当前进行中的活动
{:ok, activities} = SignInActivityRepository.list_current_sign_in_activities([
  load: [:rewards, :participants]
])
```

## ⚠️ 错误处理

### 常见错误类型

```elixir
# 资源不存在
{:error, :not_found}

# 参数验证失败
{:error, :invalid_params}

# 数据库操作失败
{:error, :database_error}

# 权限不足
{:error, :permission_denied}

# 业务逻辑错误
{:error, :business_logic_error}

# 系统异常
{:error, :system_exception}
```

### 错误处理最佳实践

```elixir
case CustomerServiceRepository.get_customer(customer_id) do
  {:ok, customer} ->
    # 处理成功情况
    process_customer(customer)
    
  {:error, :not_found} ->
    # 处理资源不存在
    {:error, "客服不存在"}
    
  {:error, reason} ->
    # 处理其他错误
    Logger.error("获取客服失败: #{inspect(reason)}")
    {:error, "系统错误"}
end
```

## 📊 性能考虑

### 缓存使用

```elixir
# 使用缓存（默认）
{:ok, config} = SystemConfigRepository.get_config_by_key("key")

# 跳过缓存
{:ok, config} = SystemConfigRepository.get_config_by_key("key", use_cache: false)
```

### 分页查询

```elixir
# 分页查询
{:ok, {orders, total}} = PaymentOrderRepository.list_payment_orders(
  %{},
  [limit: 20, offset: 40]  # 第3页，每页20条
)
```

### 关联加载

```elixir
# 预加载关联数据
{:ok, orders} = PaymentOrderRepository.list_payment_orders(
  %{},
  [load: [:user, :gateway, :transactions]]
)
```

## 🔧 Service 层 API

### 🎯 Service 层职责

Service 层负责业务逻辑处理，协调多个 Repository 操作，提供高级业务功能。

#### 标准接口规范

```elixir
# 业务操作
perform_business_operation(params, options \\ []) :: {:ok, result} | {:error, reason}

# 批量操作
batch_operation(items, operation, options \\ []) :: {:ok, results} | {:error, reason}

# 统计分析
get_analytics(filters, options \\ []) :: {:ok, analytics} | {:error, reason}

# 业务验证
validate_business_rules(data, options \\ []) :: {:ok, validated_data} | {:error, reason}
```

### 🏢 Teen 系统 Service 模块

#### 1. CustomerService 客服管理服务

**模块**: `RacingGame.Live.AdminPanel.Services.Teen.CustomerServiceService`

```elixir
# 客服工作流程
assign_customer_to_user(customer_id, user_id, options \\ [])
handle_user_question(question_id, customer_id, response, options \\ [])
escalate_complaint(complaint_id, reason, options \\ [])

# 客服绩效分析
get_customer_performance(customer_id, date_range, options \\ [])
get_team_performance(date_range, options \\ [])
generate_performance_report(filters, options \\ [])

# 工作量管理
balance_workload(options \\ [])
get_workload_statistics(options \\ [])
```

#### 2. PaymentSystem 支付系统服务

**模块**: `RacingGame.Live.AdminPanel.Services.Teen.PaymentSystemService`

```elixir
# 支付流程管理
process_payment(payment_data, options \\ [])
refund_payment(order_id, refund_amount, reason, options \\ [])
verify_payment_status(order_id, options \\ [])

# 支付分析
get_payment_analytics(date_range, options \\ [])
get_gateway_performance(gateway_id, date_range, options \\ [])
detect_fraud_patterns(options \\ [])

# 批量操作
batch_process_orders(order_ids, action, options \\ [])
reconcile_payments(date_range, options \\ [])
```

#### 3. GameManagement 游戏管理服务

**模块**: `RacingGame.Live.AdminPanel.Services.Teen.GameManagementService`

```elixir
# 游戏配置管理
deploy_game_config(config_data, options \\ [])
rollback_game_config(version, options \\ [])
validate_game_config(config_data, options \\ [])

# 机器人管理
optimize_robot_distribution(room_id, options \\ [])
adjust_robot_difficulty(difficulty_level, options \\ [])
monitor_robot_performance(options \\ [])

# 游戏监控
get_game_health_status(options \\ [])
detect_game_anomalies(options \\ [])
generate_game_report(date_range, options \\ [])
```

#### 4. ActivitySystem 活动系统服务

**模块**: `RacingGame.Live.AdminPanel.Services.Teen.ActivitySystemService`

```elixir
# 活动生命周期管理
create_sign_in_activity(activity_data, options \\ [])
start_activity(activity_id, options \\ [])
pause_activity(activity_id, options \\ [])
end_activity(activity_id, options \\ [])

# 奖励管理
distribute_rewards(activity_id, options \\ [])
calculate_reward_cost(activity_id, options \\ [])
validate_reward_rules(reward_data, options \\ [])

# 活动分析
get_activity_participation(activity_id, options \\ [])
get_activity_effectiveness(activity_id, options \\ [])
compare_activity_performance(activity_ids, options \\ [])
```

## 🔍 QueryBuilder 层 API

### 🎯 QueryBuilder 层职责

QueryBuilder 层负责构建复杂查询，提供高级数据分析和跨表查询功能。

#### 标准接口规范

```elixir
# 复杂查询构建
build_complex_query(conditions, options \\ []) :: Ash.Query.t()

# 统计查询
build_statistics_query(metrics, filters, options \\ []) :: Ash.Query.t()

# 聚合查询
build_aggregation_query(aggregations, group_by, options \\ []) :: Ash.Query.t()

# 跨表查询
build_join_query(tables, conditions, options \\ []) :: Ash.Query.t()
```

### 🏢 Teen 系统 QueryBuilder 模块

#### 1. CustomerService 客服查询构建器

**模块**: `RacingGame.Live.AdminPanel.QueryBuilders.Teen.CustomerServiceQueryBuilder`

```elixir
# 客服绩效查询
build_performance_query(customer_id, date_range, options \\ [])
build_team_comparison_query(team_ids, metrics, options \\ [])
build_satisfaction_trend_query(date_range, options \\ [])

# 工作量分析查询
build_workload_distribution_query(date_range, options \\ [])
build_response_time_analysis_query(filters, options \\ [])
build_case_resolution_query(filters, options \\ [])
```

#### 2. PaymentSystem 支付查询构建器

**模块**: `RacingGame.Live.AdminPanel.QueryBuilders.Teen.PaymentSystemQueryBuilder`

```elixir
# 支付统计查询
build_payment_volume_query(date_range, group_by, options \\ [])
build_gateway_performance_query(gateway_ids, metrics, options \\ [])
build_fraud_detection_query(risk_factors, options \\ [])

# 财务分析查询
build_revenue_analysis_query(date_range, dimensions, options \\ [])
build_refund_analysis_query(filters, options \\ [])
build_settlement_query(date_range, options \\ [])
```

#### 3. ActivitySystem 活动查询构建器

**模块**: `RacingGame.Live.AdminPanel.QueryBuilders.Teen.ActivitySystemQueryBuilder`

```elixir
# 活动参与度查询
build_participation_analysis_query(activity_id, options \\ [])
build_user_engagement_query(user_segments, options \\ [])
build_retention_impact_query(activity_ids, options \\ [])

# 奖励效果查询
build_reward_distribution_query(activity_id, options \\ [])
build_cost_benefit_analysis_query(activity_ids, options \\ [])
build_reward_optimization_query(constraints, options \\ [])
```

## 🔧 高级使用示例

### Service 层业务操作

```elixir
# 处理客服工作流程
{:ok, assignment} = CustomerServiceService.assign_customer_to_user(
  customer_id: 1,
  user_id: 123,
  priority: :high,
  estimated_duration: 30
)

# 支付流程处理
{:ok, payment_result} = PaymentSystemService.process_payment(%{
  user_id: 456,
  amount: Decimal.new("100.00"),
  payment_method: "alipay",
  gateway_id: 1
}, [timeout: 30_000])

# 活动效果分析
{:ok, analytics} = ActivitySystemService.get_activity_effectiveness(
  activity_id: 1,
  metrics: [:participation_rate, :conversion_rate, :roi]
)
```

### QueryBuilder 层复杂查询

```elixir
# 构建客服绩效分析查询
query = CustomerServiceQueryBuilder.build_performance_query(
  customer_id: 1,
  date_range: {~D[2024-01-01], ~D[2024-01-31]},
  metrics: [:response_time, :resolution_rate, :satisfaction_score]
)

# 执行查询
{:ok, performance_data} = Ash.read(query)

# 构建支付趋势分析查询
query = PaymentSystemQueryBuilder.build_payment_volume_query(
  date_range: {~D[2024-01-01], ~D[2024-12-31]},
  group_by: [:month, :gateway],
  metrics: [:total_amount, :transaction_count, :success_rate]
)

{:ok, payment_trends} = Ash.read(query)
```

### 批量操作示例

```elixir
# 批量处理支付订单
order_ids = [1, 2, 3, 4, 5]
{:ok, results} = PaymentSystemService.batch_process_orders(
  order_ids,
  :approve,
  [parallel: true, timeout: 60_000]
)

# 批量分发活动奖励
{:ok, distribution_result} = ActivitySystemService.distribute_rewards(
  activity_id: 1,
  [batch_size: 100, async: true]
)
```

## 📊 性能优化建议

### 查询优化

```elixir
# 使用索引友好的查询条件
query = PaymentSystemQueryBuilder.build_payment_volume_query(
  date_range: {start_date, end_date},  # 使用日期范围而非具体日期列表
  group_by: [:gateway_id],             # 按索引字段分组
  options: [use_index: :payment_date_gateway_idx]
)

# 限制查询结果集大小
query = CustomerServiceQueryBuilder.build_performance_query(
  customer_id: 1,
  date_range: {recent_date, today},    # 限制时间范围
  options: [limit: 1000]               # 限制结果数量
)
```

### 缓存策略

```elixir
# Service 层缓存业务结果
{:ok, analytics} = ActivitySystemService.get_activity_effectiveness(
  activity_id: 1,
  [cache_ttl: 3600, cache_key: "activity_1_effectiveness"]
)

# Repository 层缓存数据查询
{:ok, configs} = SystemConfigRepository.list_system_configs(
  %{category: "payment"},
  [use_cache: true, cache_ttl: 1800]
)
```

---

**文档版本**: v1.0
**创建日期**: 2024-12-19
**最后更新**: 2024-12-19
**维护团队**: Racing Game Development Team
