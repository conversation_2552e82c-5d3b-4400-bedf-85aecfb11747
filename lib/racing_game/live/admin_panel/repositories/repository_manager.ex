defmodule RacingGame.Live.AdminPanel.Repositories.RepositoryManager do
  @moduledoc """
  仓储管理器
  
  统一管理所有数据访问仓储，提供：
  - 仓储实例管理
  - 事务协调
  - 缓存策略统一
  - 性能监控
  """

  require Logger
  alias RacingGame.Live.AdminPanel.Repositories.{
    CommunicationRepository,
    BetRepository,
    StockRepository,
    UserRepository
  }

  # 常量定义
  @repositories %{
    communication: CommunicationRepository,
    bet: BetRepository,
    stock: StockRepository,
    user: UserRepository
  }

  # ============================================================================
  # 仓储访问接口
  # ============================================================================

  @doc """
  获取通信仓储实例

  ## 返回
  - 通信仓储模块
  """
  def communication_repo, do: @repositories.communication

  @doc """
  获取下注仓储实例

  ## 返回
  - 下注仓储模块
  """
  def bet_repo, do: @repositories.bet

  @doc """
  获取股票仓储实例

  ## 返回
  - 股票仓储模块
  """
  def stock_repo, do: @repositories.stock

  @doc """
  获取用户仓储实例

  ## 返回
  - 用户仓储模块
  """
  def user_repo, do: @repositories.user

  @doc """
  获取指定类型的仓储

  ## 参数
  - `repo_type` - 仓储类型

  ## 返回
  - `{:ok, repository_module}` - 成功
  - `{:error, :unknown_repository}` - 未知仓储类型
  """
  def get_repository(repo_type) when repo_type in [:communication, :bet, :stock, :user] do
    {:ok, Map.get(@repositories, repo_type)}
  end
  def get_repository(_repo_type) do
    {:error, :unknown_repository}
  end

  # ============================================================================
  # 事务管理
  # ============================================================================

  @doc """
  在事务中执行多个仓储操作

  ## 参数
  - `operations` - 操作列表，每个操作为 {repo_type, function, args}
  - `options` - 事务选项

  ## 返回
  - `{:ok, results}` - 成功，返回所有操作结果
  - `{:error, reason}` - 失败
  """
  def transaction(operations, options \\ []) do
    Logger.debug("🔄 [仓储管理器] 开始事务执行: #{length(operations)}个操作")
    
    try do
      Cypridina.Repo.transaction(fn ->
        results = Enum.map(operations, fn {repo_type, function, args} ->
          case get_repository(repo_type) do
            {:ok, repo_module} ->
              apply(repo_module, function, args)
            {:error, reason} ->
              Cypridina.Repo.rollback({:repository_error, reason})
          end
        end)
        
        # 检查是否有失败的操作
        case Enum.find(results, fn result -> match?({:error, _}, result) end) do
          nil -> results
          {:error, reason} -> Cypridina.Repo.rollback(reason)
        end
      end, options)
    rescue
      error ->
        Logger.error("❌ [仓储管理器] 事务执行异常: #{inspect(error)}")
        {:error, :transaction_failed}
    end
  end

  @doc """
  批量执行同类型仓储操作

  ## 参数
  - `repo_type` - 仓储类型
  - `operations` - 操作列表，每个操作为 {function, args}
  - `options` - 选项

  ## 返回
  - `{:ok, results}` - 成功
  - `{:error, reason}` - 失败
  """
  def batch_execute(repo_type, operations, options \\ []) do
    Logger.debug("📦 [仓储管理器] 批量执行#{repo_type}操作: #{length(operations)}个")
    
    case get_repository(repo_type) do
      {:ok, repo_module} ->
        use_transaction = Keyword.get(options, :use_transaction, true)
        
        if use_transaction do
          transaction(Enum.map(operations, fn {function, args} ->
            {repo_type, function, args}
          end), options)
        else
          results = Enum.map(operations, fn {function, args} ->
            apply(repo_module, function, args)
          end)
          {:ok, results}
        end
      
      {:error, reason} -> {:error, reason}
    end
  end

  # ============================================================================
  # 缓存管理
  # ============================================================================

  @doc """
  清除所有仓储缓存

  ## 参数
  - `options` - 选项
    - `:repositories` - 指定要清除的仓储列表
  """
  def clear_all_caches(options \\ []) do
    repositories = Keyword.get(options, :repositories, Map.keys(@repositories))
    
    Logger.debug("🧹 [仓储管理器] 清除缓存: #{inspect(repositories)}")
    
    Enum.each(repositories, fn repo_type ->
      case get_repository(repo_type) do
        {:ok, repo_module} ->
          if function_exported?(repo_module, :clear_cache, 0) do
            apply(repo_module, :clear_cache, [])
          end
        {:error, _} -> :ok
      end
    end)
  end

  @doc """
  预热缓存

  ## 参数
  - `options` - 选项
    - `:repositories` - 指定要预热的仓储列表
  """
  def warm_up_caches(options \\ []) do
    repositories = Keyword.get(options, :repositories, Map.keys(@repositories))
    
    Logger.debug("🔥 [仓储管理器] 预热缓存: #{inspect(repositories)}")
    
    Enum.each(repositories, fn repo_type ->
      case get_repository(repo_type) do
        {:ok, repo_module} ->
          if function_exported?(repo_module, :warm_up_cache, 0) do
            apply(repo_module, :warm_up_cache, [])
          end
        {:error, _} -> :ok
      end
    end)
  end

  # ============================================================================
  # 性能监控
  # ============================================================================

  @doc """
  获取所有仓储的性能统计

  ## 返回
  - `{:ok, statistics}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_performance_statistics do
    Logger.debug("📊 [仓储管理器] 获取性能统计")
    
    try do
      stats = Enum.reduce(@repositories, %{}, fn {repo_type, repo_module}, acc ->
        repo_stats = if function_exported?(repo_module, :get_performance_stats, 0) do
          case apply(repo_module, :get_performance_stats, []) do
            {:ok, stats} -> stats
            _ -> %{}
          end
        else
          %{}
        end
        
        Map.put(acc, repo_type, repo_stats)
      end)
      
      {:ok, stats}
    rescue
      error ->
        Logger.error("❌ [仓储管理器] 获取性能统计异常: #{inspect(error)}")
        {:error, :stats_error}
    end
  end

  @doc """
  监控仓储操作性能

  ## 参数
  - `repo_type` - 仓储类型
  - `function` - 函数名
  - `args` - 参数
  - `options` - 选项

  ## 返回
  - 操作结果和性能信息
  """
  def monitor_operation(repo_type, function, args, options \\ []) do
    start_time = System.monotonic_time(:millisecond)
    
    Logger.debug("⏱️ [仓储管理器] 监控操作: #{repo_type}.#{function}")
    
    result = case get_repository(repo_type) do
      {:ok, repo_module} ->
        apply(repo_module, function, args)
      {:error, reason} ->
        {:error, reason}
    end
    
    end_time = System.monotonic_time(:millisecond)
    duration = end_time - start_time
    
    # 记录性能信息
    performance_info = %{
      repository: repo_type,
      function: function,
      duration_ms: duration,
      success: match?({:ok, _}, result),
      timestamp: DateTime.utc_now()
    }
    
    if Keyword.get(options, :log_performance, true) do
      log_performance(performance_info)
    end
    
    {result, performance_info}
  end

  # ============================================================================
  # 健康检查
  # ============================================================================

  @doc """
  检查所有仓储的健康状态

  ## 返回
  - `{:ok, health_status}` - 成功
  - `{:error, reason}` - 失败
  """
  def health_check do
    Logger.debug("🏥 [仓储管理器] 执行健康检查")
    
    try do
      health_status = Enum.reduce(@repositories, %{}, fn {repo_type, repo_module}, acc ->
        status = if function_exported?(repo_module, :health_check, 0) do
          case apply(repo_module, :health_check, []) do
            {:ok, status} -> status
            {:error, reason} -> %{status: :unhealthy, reason: reason}
          end
        else
          %{status: :unknown, message: "健康检查未实现"}
        end
        
        Map.put(acc, repo_type, status)
      end)
      
      overall_status = if Enum.all?(health_status, fn {_, status} -> 
        Map.get(status, :status) == :healthy 
      end) do
        :healthy
      else
        :unhealthy
      end
      
      {:ok, %{overall: overall_status, repositories: health_status}}
    rescue
      error ->
        Logger.error("❌ [仓储管理器] 健康检查异常: #{inspect(error)}")
        {:error, :health_check_failed}
    end
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 记录性能信息
  defp log_performance(performance_info) do
    %{
      repository: repo_type,
      function: function,
      duration_ms: duration,
      success: success
    } = performance_info
    
    level = cond do
      duration > 1000 -> :warning
      duration > 500 -> :info
      true -> :debug
    end
    
    status = if success, do: "✅", else: "❌"
    
    Logger.log(level, "#{status} [仓储性能] #{repo_type}.#{function} - #{duration}ms")
  end
end
