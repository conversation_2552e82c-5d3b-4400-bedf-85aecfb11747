defmodule Teen.PaymentSystem do
  @moduledoc """
  支付系统域

  包含支付配置、兑换配置、银行信息、支付渠道、支付网关等功能
  """

  use Ash.Domain,
    otp_app: :cypridina,
    extensions: [AshAdmin.Domain]

  alias Teen.PaymentSystem.{PaymentConfig, ExchangeConfig, PaymentGateway}

  admin do
    show? true
  end

  resources do
    resource Teen.PaymentSystem.PaymentConfig
    resource Teen.PaymentSystem.ExchangeConfig
    resource Teen.PaymentSystem.PaymentGateway
  end

  # ==================== 业务逻辑函数 ====================

  @doc """
  获取用户可用的支付方式
  """
  def get_available_payment_methods(user_id, amount) do
    with {:ok, configs} <- PaymentConfig.list_active_configs(),
         filtered_configs <- filter_configs_by_amount(configs, amount),
         available_configs <- filter_configs_by_user_eligibility(filtered_configs, user_id) do

      # 按排序和费率排序
      sorted_configs = Enum.sort_by(available_configs, &{&1.sort_order, &1.fee_rate})

      {:ok, sorted_configs}
    end
  end

  @doc """
  选择最优支付网关
  """
  def select_optimal_gateway(payment_type, amount) do
    with {:ok, configs} <- PaymentConfig.list_by_payment_type(payment_type),
         available_configs <- filter_available_configs(configs, amount) do

      # 选择费率最低且状态正常的网关
      optimal_config = Enum.min_by(available_configs, fn config ->
        {config.fee_rate, config.sort_order}
      end)

      case optimal_config do
        nil -> {:error, "No available gateway"}
        config -> {:ok, config}
      end
    end
  end

  @doc """
  计算支付费用
  """
  def calculate_payment_fees(amount, payment_config) do
    fee_amount = Decimal.mult(amount, Decimal.div(payment_config.fee_rate, 100))
    deduction_amount = Decimal.mult(amount, Decimal.div(payment_config.deduction_rate, 100))

    %{
      original_amount: amount,
      fee_rate: payment_config.fee_rate,
      fee_amount: fee_amount,
      deduction_rate: payment_config.deduction_rate,
      deduction_amount: deduction_amount,
      total_cost: Decimal.add(amount, fee_amount),
      net_amount: Decimal.sub(amount, deduction_amount)
    }
  end

  @doc """
  获取兑换配置
  """
  def get_exchange_config(exchange_type, user_vip_level \\ 0) do
    config_name = case exchange_type do
      1 -> "game_exchange"
      2 -> "promotion_exchange"
      _ -> "default_exchange"
    end

    case ExchangeConfig.get_by_name(config_name) do
      {:ok, [config]} ->
        # 根据VIP等级调整配置
        adjusted_config = adjust_config_for_vip(config, user_vip_level)
        {:ok, adjusted_config}

      {:ok, []} ->
        {:error, "Exchange config not found"}

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  验证兑换资格
  """
  def validate_exchange_eligibility(user_id, amount, exchange_type) do
    with {:ok, config} <- get_exchange_config(exchange_type),
         :ok <- validate_amount_limits(amount, config),
         :ok <- validate_daily_limits(user_id, amount, config),
         :ok <- validate_user_requirements(user_id, config) do
      {:ok, config}
    end
  end

  @doc """
  计算兑换金额
  """
  def calculate_exchange_amount(coins, exchange_config) do
    # 基础兑换比例
    base_amount = Decimal.div(coins, exchange_config.exchange_rate)

    # 扣除手续费
    fee_amount = Decimal.mult(base_amount, Decimal.div(exchange_config.fee_rate, 100))

    # 扣除税费
    tax_amount = Decimal.mult(base_amount, Decimal.div(exchange_config.tax_rate, 100))

    # 最终金额
    final_amount = Decimal.sub(Decimal.sub(base_amount, fee_amount), tax_amount)

    %{
      coins: coins,
      exchange_rate: exchange_config.exchange_rate,
      base_amount: base_amount,
      fee_rate: exchange_config.fee_rate,
      fee_amount: fee_amount,
      tax_rate: exchange_config.tax_rate,
      tax_amount: tax_amount,
      final_amount: final_amount
    }
  end

  @doc """
  更新支付网关状态
  """
  def update_gateway_status(gateway_id, status) do
    case PaymentGateway.read(gateway_id) do
      {:ok, gateway} ->
        case status do
          :enable -> PaymentGateway.enable(gateway)
          :disable -> PaymentGateway.disable(gateway)
          _ -> {:error, "Invalid status"}
        end

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  测试支付网关连接
  """
  def test_gateway_connection(gateway_id) do
    case PaymentGateway.read(gateway_id) do
      {:ok, gateway} ->
        # 这里应该实现实际的网关连接测试
        test_result = simulate_gateway_test(gateway)

        # 更新最后测试时间
        PaymentGateway.test_connection(gateway)

        {:ok, test_result}

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  批量更新支付配置状态
  """
  def batch_update_config_status(config_ids, status) when is_list(config_ids) do
    results = Enum.map(config_ids, fn config_id ->
      case PaymentConfig.read(config_id) do
        {:ok, config} ->
          case status do
            :enable -> PaymentConfig.enable(config)
            :disable -> PaymentConfig.disable(config)
            _ -> {:error, "Invalid status"}
          end

        {:error, reason} ->
          {:error, {config_id, reason}}
      end
    end)

    {successes, failures} = Enum.split_with(results, fn
      {:ok, _} -> true
      _ -> false
    end)

    %{
      success_count: length(successes),
      failure_count: length(failures),
      failures: failures
    }
  end

  # ==================== 私有函数 ====================

  defp filter_configs_by_amount(configs, amount) do
    Enum.filter(configs, fn config ->
      Decimal.compare(amount, config.min_amount) != :lt and
      Decimal.compare(amount, config.max_amount) != :gt
    end)
  end

  defp filter_configs_by_user_eligibility(configs, _user_id) do
    # 这里可以根据用户的VIP等级、黑名单状态等进行过滤
    configs
  end

  defp filter_available_configs(configs, amount) do
    configs
    |> Enum.filter(& &1.status == 1)
    |> filter_configs_by_amount(amount)
  end

  defp adjust_config_for_vip(config, vip_level) do
    # 根据VIP等级调整配置
    vip_bonus = case vip_level do
      level when level >= 5 -> Decimal.new("0.2") # 20%优惠
      level when level >= 3 -> Decimal.new("0.1") # 10%优惠
      level when level >= 1 -> Decimal.new("0.05") # 5%优惠
      _ -> Decimal.new("0")
    end

    adjusted_fee_rate = Decimal.mult(config.fee_rate, Decimal.sub(Decimal.new("1"), vip_bonus))

    %{config | fee_rate: adjusted_fee_rate}
  end

  defp validate_amount_limits(amount, config) do
    cond do
      Decimal.compare(amount, config.min_amount) == :lt ->
        {:error, "Amount below minimum limit"}

      Decimal.compare(amount, config.max_amount) == :gt ->
        {:error, "Amount exceeds maximum limit"}

      true ->
        :ok
    end
  end

  defp validate_daily_limits(_user_id, _amount, _config), do: :ok
  defp validate_user_requirements(_user_id, _config), do: :ok

  defp simulate_gateway_test(_gateway) do
    case :rand.uniform(10) do
      n when n <= 8 ->
        %{
          status: :success,
          response_time: :rand.uniform(1000),
          message: "Gateway connection successful"
        }

      _ ->
        %{
          status: :failed,
          response_time: nil,
          message: "Gateway connection failed"
        }
    end
  end
end
