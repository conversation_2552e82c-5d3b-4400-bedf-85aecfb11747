defmodule RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder do
  @moduledoc """
  账户系统查询构建器
  
  提供账户系统相关的动态查询构建：
  - 用户查询构建
  - 身份认证查询构建
  - 代理关系查询构建
  - 复杂过滤条件组合
  - 搜索和排序功能
  """

  require Logger
  alias Cypridina.Accounts.{User, UserIdentity, AgentRelationship}
  require Ash.Query

  # ============================================================================
  # 用户查询构建
  # ============================================================================

  @doc """
  构建用户列表查询

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, query}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_user_list_query(params) do
    Logger.debug("🔨 [查询构建器] 构建用户列表查询")
    
    try do
      query = User
      |> apply_user_filters(params.filters)
      |> apply_user_search(params.search)
      |> apply_sort(params.sort)
      
      {:ok, query}
    rescue
      error ->
        Logger.error("❌ [查询构建器] 构建用户查询失败: #{inspect(error)}")
        {:error, :query_build_failed}
    end
  end

  @doc """
  构建用户搜索查询

  ## 参数
  - `search_term` - 搜索词
  - `fields` - 搜索字段

  ## 返回
  - `query` - 查询对象
  """
  def build_user_search_query(search_term, fields \\ [:username, :email]) do
    Logger.debug("🔍 [查询构建器] 构建用户搜索查询: #{search_term}")
    
    search_conditions = build_search_conditions(search_term, fields)
    
    User
    |> Ash.Query.filter(^search_conditions)
  end

  # ============================================================================
  # 身份认证查询构建
  # ============================================================================

  @doc """
  构建身份列表查询

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, query}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_identity_list_query(params) do
    Logger.debug("🔨 [查询构建器] 构建身份列表查询")
    
    try do
      query = UserIdentity
      |> apply_identity_filters(params.filters)
      |> apply_identity_search(params.search)
      |> apply_sort(params.sort)
      
      {:ok, query}
    rescue
      error ->
        Logger.error("❌ [查询构建器] 构建身份查询失败: #{inspect(error)}")
        {:error, :query_build_failed}
    end
  end

  # ============================================================================
  # 代理关系查询构建
  # ============================================================================

  @doc """
  构建代理关系列表查询

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, query}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_agent_relationship_list_query(params) do
    Logger.debug("🔨 [查询构建器] 构建代理关系列表查询")
    
    try do
      query = AgentRelationship
      |> apply_agent_relationship_filters(params.filters)
      |> apply_agent_relationship_search(params.search)
      |> apply_sort(params.sort)
      
      {:ok, query}
    rescue
      error ->
        Logger.error("❌ [查询构建器] 构建代理关系查询失败: #{inspect(error)}")
        {:error, :query_build_failed}
    end
  end

  # ============================================================================
  # 用户过滤器
  # ============================================================================

  # 应用用户过滤条件
  defp apply_user_filters(query, filters) when map_size(filters) == 0, do: query
  defp apply_user_filters(query, filters) do
    Enum.reduce(filters, query, fn {key, value}, acc_query ->
      apply_user_filter(acc_query, key, value)
    end)
  end

  # 应用单个用户过滤条件
  defp apply_user_filter(query, :agent_level, value) when is_integer(value) do
    Ash.Query.filter(query, agent_level == ^value)
  end

  defp apply_user_filter(query, :agent_level_range, {min_level, max_level}) do
    Ash.Query.filter(query, agent_level >= ^min_level and agent_level <= ^max_level)
  end

  defp apply_user_filter(query, :is_guest, true) do
    Ash.Query.filter(query, username == nil)
  end

  defp apply_user_filter(query, :is_guest, false) do
    Ash.Query.filter(query, username != nil)
  end

  defp apply_user_filter(query, :is_confirmed, true) do
    Ash.Query.filter(query, confirmed_at != nil)
  end

  defp apply_user_filter(query, :is_confirmed, false) do
    Ash.Query.filter(query, confirmed_at == nil)
  end

  defp apply_user_filter(query, :created_after, date) do
    Ash.Query.filter(query, inserted_at >= ^date)
  end

  defp apply_user_filter(query, :created_before, date) do
    Ash.Query.filter(query, inserted_at <= ^date)
  end

  defp apply_user_filter(query, :created_date_range, {start_date, end_date}) do
    Ash.Query.filter(query, inserted_at >= ^start_date and inserted_at <= ^end_date)
  end

  defp apply_user_filter(query, :numeric_id_range, {min_id, max_id}) do
    Ash.Query.filter(query, numeric_id >= ^min_id and numeric_id <= ^max_id)
  end

  defp apply_user_filter(query, :has_email, true) do
    Ash.Query.filter(query, email != nil)
  end

  defp apply_user_filter(query, :has_email, false) do
    Ash.Query.filter(query, email == nil)
  end

  defp apply_user_filter(query, _key, _value) do
    # 忽略未知的过滤条件
    query
  end

  # 应用用户搜索
  defp apply_user_search(query, search) when search == "" or is_nil(search), do: query
  defp apply_user_search(query, search) do
    search_term = "%#{String.trim(search)}%"
    
    Ash.Query.filter(query, 
      ilike(username, ^search_term) or 
      ilike(email, ^search_term) or
      ilike(client_uniq_id, ^search_term)
    )
  end

  # ============================================================================
  # 身份认证过滤器
  # ============================================================================

  # 应用身份过滤条件
  defp apply_identity_filters(query, filters) when map_size(filters) == 0, do: query
  defp apply_identity_filters(query, filters) do
    Enum.reduce(filters, query, fn {key, value}, acc_query ->
      apply_identity_filter(acc_query, key, value)
    end)
  end

  # 应用单个身份过滤条件
  defp apply_identity_filter(query, :strategy, value) when is_binary(value) do
    Ash.Query.filter(query, strategy == ^value)
  end

  defp apply_identity_filter(query, :user_id, value) when is_binary(value) do
    Ash.Query.filter(query, user_id == ^value)
  end

  defp apply_identity_filter(query, :created_after, date) do
    Ash.Query.filter(query, inserted_at >= ^date)
  end

  defp apply_identity_filter(query, :created_before, date) do
    Ash.Query.filter(query, inserted_at <= ^date)
  end

  defp apply_identity_filter(query, _key, _value) do
    # 忽略未知的过滤条件
    query
  end

  # 应用身份搜索
  defp apply_identity_search(query, search) when search == "" or is_nil(search), do: query
  defp apply_identity_search(query, search) do
    search_term = "%#{String.trim(search)}%"
    
    Ash.Query.filter(query, 
      ilike(strategy, ^search_term) or 
      ilike(uid, ^search_term)
    )
  end

  # ============================================================================
  # 代理关系过滤器
  # ============================================================================

  # 应用代理关系过滤条件
  defp apply_agent_relationship_filters(query, filters) when map_size(filters) == 0, do: query
  defp apply_agent_relationship_filters(query, filters) do
    Enum.reduce(filters, query, fn {key, value}, acc_query ->
      apply_agent_relationship_filter(acc_query, key, value)
    end)
  end

  # 应用单个代理关系过滤条件
  defp apply_agent_relationship_filter(query, :agent_id, value) when is_binary(value) do
    Ash.Query.filter(query, agent_id == ^value)
  end

  defp apply_agent_relationship_filter(query, :user_id, value) when is_binary(value) do
    Ash.Query.filter(query, user_id == ^value)
  end

  defp apply_agent_relationship_filter(query, :level, value) when is_integer(value) do
    Ash.Query.filter(query, level == ^value)
  end

  defp apply_agent_relationship_filter(query, :level_range, {min_level, max_level}) do
    Ash.Query.filter(query, level >= ^min_level and level <= ^max_level)
  end

  defp apply_agent_relationship_filter(query, :is_active, value) when is_boolean(value) do
    Ash.Query.filter(query, is_active == ^value)
  end

  defp apply_agent_relationship_filter(query, :created_after, date) do
    Ash.Query.filter(query, inserted_at >= ^date)
  end

  defp apply_agent_relationship_filter(query, :created_before, date) do
    Ash.Query.filter(query, inserted_at <= ^date)
  end

  defp apply_agent_relationship_filter(query, :created_date_range, {start_date, end_date}) do
    Ash.Query.filter(query, inserted_at >= ^start_date and inserted_at <= ^end_date)
  end

  defp apply_agent_relationship_filter(query, _key, _value) do
    # 忽略未知的过滤条件
    query
  end

  # 应用代理关系搜索
  defp apply_agent_relationship_search(query, search) when search == "" or is_nil(search), do: query
  defp apply_agent_relationship_search(query, search) do
    # 代理关系搜索需要关联用户和代理信息
    query
    |> Ash.Query.load([:user, :agent])
    |> apply_relationship_search_filter(search)
  end

  # 应用关系搜索过滤
  defp apply_relationship_search_filter(query, search) do
    search_term = "%#{String.trim(search)}%"
    
    # 这里需要根据实际的关联字段调整
    Ash.Query.filter(query, 
      ilike(user.username, ^search_term) or 
      ilike(agent.username, ^search_term) or
      ilike(user.email, ^search_term) or
      ilike(agent.email, ^search_term)
    )
  end

  # ============================================================================
  # 通用工具函数
  # ============================================================================

  # 应用排序
  defp apply_sort(query, []), do: query
  defp apply_sort(query, sort) when is_list(sort) do
    Ash.Query.sort(query, sort)
  end
  defp apply_sort(query, _), do: query

  # 构建搜索条件
  defp build_search_conditions(search_term, fields) do
    search_pattern = "%#{String.trim(search_term)}%"
    
    conditions = Enum.map(fields, fn field ->
      case field do
        :username -> {:ilike, :username, search_pattern}
        :email -> {:ilike, :email, search_pattern}
        :client_uniq_id -> {:ilike, :client_uniq_id, search_pattern}
        :numeric_id -> 
          case Integer.parse(search_term) do
            {numeric_value, ""} -> {:==, :numeric_id, numeric_value}
            _ -> nil
          end
        _ -> nil
      end
    end)
    |> Enum.filter(& &1)
    
    case conditions do
      [] -> true  # 没有有效条件时返回 true（不过滤）
      [condition] -> condition
      conditions -> {:or, conditions}
    end
  end

  # ============================================================================
  # 高级查询构建器
  # ============================================================================

  @doc """
  构建复杂用户查询

  ## 参数
  - `conditions` - 查询条件列表

  ## 返回
  - `{:ok, query}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_complex_user_query(conditions) do
    Logger.debug("🔨 [查询构建器] 构建复杂用户查询")
    
    try do
      query = Enum.reduce(conditions, User, fn condition, acc_query ->
        apply_complex_condition(acc_query, condition)
      end)
      
      {:ok, query}
    rescue
      error ->
        Logger.error("❌ [查询构建器] 构建复杂查询失败: #{inspect(error)}")
        {:error, :complex_query_build_failed}
    end
  end

  # 应用复杂条件
  defp apply_complex_condition(query, {:and, conditions}) do
    Enum.reduce(conditions, query, &apply_complex_condition(&2, &1))
  end

  defp apply_complex_condition(query, {:or, conditions}) do
    or_conditions = Enum.map(conditions, &build_condition_expr/1)
    Ash.Query.filter(query, ^{:or, or_conditions})
  end

  defp apply_complex_condition(query, condition) do
    condition_expr = build_condition_expr(condition)
    Ash.Query.filter(query, ^condition_expr)
  end

  # 构建条件表达式
  defp build_condition_expr({:eq, field, value}) do
    {field, value}
  end

  defp build_condition_expr({:ne, field, value}) do
    {:!=, field, value}
  end

  defp build_condition_expr({:gt, field, value}) do
    {:>, field, value}
  end

  defp build_condition_expr({:gte, field, value}) do
    {:>=, field, value}
  end

  defp build_condition_expr({:lt, field, value}) do
    {:<, field, value}
  end

  defp build_condition_expr({:lte, field, value}) do
    {:<=, field, value}
  end

  defp build_condition_expr({:like, field, pattern}) do
    {:ilike, field, pattern}
  end

  defp build_condition_expr({:in, field, values}) when is_list(values) do
    {:in, field, values}
  end

  defp build_condition_expr({:not_in, field, values}) when is_list(values) do
    {:not, {:in, field, values}}
  end

  defp build_condition_expr({:is_null, field}) do
    {:is_nil, field}
  end

  defp build_condition_expr({:is_not_null, field}) do
    {:not, {:is_nil, field}}
  end

  defp build_condition_expr({:between, field, {min_value, max_value}}) do
    {:and, [{:>=, field, min_value}, {:<=, field, max_value}]}
  end

  defp build_condition_expr(condition) do
    Logger.warn("⚠️ [查询构建器] 未知条件类型: #{inspect(condition)}")
    true  # 默认返回 true（不过滤）
  end

  @doc """
  构建聚合查询

  ## 参数
  - `resource` - 资源模块
  - `aggregate_type` - 聚合类型 (:count, :sum, :avg, :max, :min)
  - `field` - 聚合字段
  - `filters` - 过滤条件
  - `group_by` - 分组字段

  ## 返回
  - `{:ok, query}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_aggregate_query(resource, aggregate_type, field, filters \\ %{}, group_by \\ []) do
    Logger.debug("🔨 [查询构建器] 构建聚合查询: #{aggregate_type}")
    
    try do
      query = resource
      |> apply_generic_filters(filters)
      |> apply_aggregate(aggregate_type, field, group_by)
      
      {:ok, query}
    rescue
      error ->
        Logger.error("❌ [查询构建器] 构建聚合查询失败: #{inspect(error)}")
        {:error, :aggregate_query_build_failed}
    end
  end

  # 应用通用过滤条件
  defp apply_generic_filters(query, filters) when map_size(filters) == 0, do: query
  defp apply_generic_filters(query, filters) do
    Enum.reduce(filters, query, fn {key, value}, acc_query ->
      apply_generic_filter(acc_query, key, value)
    end)
  end

  # 应用通用过滤条件
  defp apply_generic_filter(query, key, value) do
    Ash.Query.filter(query, ^{key, value})
  end

  # 应用聚合
  defp apply_aggregate(query, aggregate_type, field, []) do
    Ash.Query.aggregate(query, aggregate_type, field)
  end

  defp apply_aggregate(query, aggregate_type, field, group_by) when is_list(group_by) do
    Ash.Query.aggregate(query, aggregate_type, field, group: group_by)
  end
end
