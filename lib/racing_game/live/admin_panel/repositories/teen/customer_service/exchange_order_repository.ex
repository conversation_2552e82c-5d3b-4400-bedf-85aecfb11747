defmodule RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.ExchangeOrderRepository do
  @moduledoc """
  兑换订单数据访问仓储

  提供兑换订单相关的数据访问抽象：
  - 订单管理
  - 审核流程
  - 状态跟踪
  - 订单统计
  """

  require Logger
  alias Ash.Query
  import Ash.Query
  alias Teen.CustomerService.ExchangeOrder
  require Ash.Query

  # 常量定义
  @default_limit 20
  @max_limit 100
  @cache_ttl 300  # 5分钟缓存

  # ============================================================================
  # 订单管理
  # ============================================================================

  @doc """
  根据ID获取订单

  ## 参数
  - `order_id` - 订单ID
  - `options` - 选项

  ## 返回
  - `{:ok, order}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_order_by_id(order_id, options \\ []) do
    Logger.debug("🔄 [兑换订单仓储] 获取订单: #{order_id}")

    cache_key = build_cache_key("order", order_id, options[:preload])

    case get_from_cache(cache_key) do
      :miss ->
        fetch_and_cache_order_by_id(order_id, options[:preload] || [], cache_key)
      {:hit, order} ->
        Logger.debug("💾 [兑换订单仓储] 缓存命中: #{order_id}")
        {:ok, order}
    end
  end

  @doc """
  根据订单号获取订单

  ## 参数
  - `order_number` - 订单号
  - `options` - 选项

  ## 返回
  - `{:ok, order}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_order_by_number(order_number, options \\ []) do
    Logger.debug("🔄 [兑换订单仓储] 根据订单号获取订单: #{order_number}")

    try do
      case ExchangeOrder
           |> maybe_preload(options[:preload] || [])
           |> Ash.Query.filter(order_id == order_number)
           |> Ash.read_one() do
        {:ok, order} when not is_nil(order) -> {:ok, order}
        {:ok, nil} -> {:error, :not_found}
        {:error, error} ->
          Logger.error("❌ [兑换订单仓储] 获取订单失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [兑换订单仓储] 获取订单异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  创建订单

  ## 参数
  - `order_data` - 订单数据

  ## 返回
  - `{:ok, order}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_order(order_data) do
    Logger.info("📝 [兑换订单仓储] 创建订单: 用户#{order_data[:user_id]}")

    try do
      case ExchangeOrder.create(order_data) do
        {:ok, order} ->
          clear_cache(:order, nil)
          Logger.info("✅ [兑换订单仓储] 订单创建成功: #{order.id}")
          {:ok, order}
        {:error, error} ->
          Logger.error("❌ [兑换订单仓储] 创建订单失败: #{inspect(error)}")
          {:error, :create_failed}
      end
    rescue
      error ->
        Logger.error("❌ [兑换订单仓储] 创建订单异常: #{inspect(error)}")
        {:error, :create_error}
    end
  end

  @doc """
  更新订单

  ## 参数
  - `order_id` - 订单ID
  - `update_data` - 更新数据

  ## 返回
  - `{:ok, order}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_order(order_id, update_data) do
    Logger.info("📝 [兑换订单仓储] 更新订单: #{order_id}")

    with {:ok, order} <- get_order_by_id(order_id),
         {:ok, updated_order} <- do_update_order(order, update_data) do

      clear_cache(:order, order_id)
      Logger.info("✅ [兑换订单仓储] 订单更新成功: #{order_id}")
      {:ok, updated_order}
    else
      error -> error
    end
  end

  @doc """
  审核订单

  ## 参数
  - `order_id` - 订单ID
  - `audit_status` - 审核状态 (0-待审核, 1-通过, 2-拒绝)
  - `auditor_id` - 审核人ID
  - `audit_notes` - 审核备注

  ## 返回
  - `{:ok, order}` - 成功
  - `{:error, reason}` - 失败
  """
  def audit_order(order_id, audit_status, auditor_id, audit_notes \\ nil) do
    Logger.info("🔍 [兑换订单仓储] 审核订单: #{order_id} -> #{audit_status}")

    with {:ok, order} <- get_order_by_id(order_id),
         {:ok, audited_order} <- do_audit_order(order, audit_status, auditor_id, audit_notes) do

      clear_cache(:order, order_id)
      Logger.info("✅ [兑换订单仓储] 订单审核成功: #{order_id}")
      {:ok, audited_order}
    else
      error -> error
    end
  end

  @doc """
  更新订单进度状态

  ## 参数
  - `order_id` - 订单ID
  - `progress_status` - 进度状态 (0-待处理, 1-处理中, 2-已完成, 3-已取消)

  ## 返回
  - `{:ok, order}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_progress_status(order_id, progress_status) do
    Logger.info("📊 [兑换订单仓储] 更新订单进度: #{order_id} -> #{progress_status}")

    update_data = %{
      progress_status: progress_status,
      process_time: if(progress_status in [2, 3], do: DateTime.utc_now(), else: nil)
    }

    update_order(order_id, update_data)
  end

  # ============================================================================
  # 订单查询
  # ============================================================================

  @doc """
  分页获取订单列表

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, %{items: orders, page_info: page_info}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_orders_paginated(params \\ %{}) do
    Logger.debug("📋 [兑换订单仓储] 分页获取订单列表")

    normalized_params = normalize_list_params(params)
    cache_key = build_cache_key("orders_list", :paginated, normalized_params)

    case get_from_cache(cache_key) do
      :miss ->
        fetch_and_cache_orders_list(normalized_params, cache_key)
      {:hit, result} ->
        Logger.debug("💾 [兑换订单仓储] 缓存命中: 订单列表")
        {:ok, result}
    end
  end

  @doc """
  获取用户订单

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - `{:ok, orders}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_orders(user_id, options \\ []) do
    Logger.debug("👤 [兑换订单仓储] 获取用户订单: #{user_id}")

    try do
      query = ExchangeOrder
      |> Ash.Query.filter(user_id == user_id)
      |> maybe_apply_audit_status_filter(options[:audit_status])
      |> maybe_apply_progress_status_filter(options[:progress_status])
      |> maybe_apply_exchange_type_filter(options[:exchange_type])
      |> maybe_apply_date_range(options[:date_range])
      |> apply_sort(options[:sort] || [inserted_at: :desc])
      |> maybe_limit(options[:limit])

      case Ash.read(query) do
        {:ok, orders} -> {:ok, orders}
        {:error, error} ->
          Logger.error("❌ [兑换订单仓储] 获取用户订单失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [兑换订单仓储] 获取用户订单异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  获取待审核订单

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, orders}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_pending_audit_orders(options \\ []) do
    Logger.debug("⏳ [兑换订单仓储] 获取待审核订单")

    cache_key = build_cache_key("pending_audit_orders", :list, options)

    case get_from_cache(cache_key) do
      :miss ->
        fetch_and_cache_pending_audit_orders(options, cache_key)
      {:hit, orders} ->
        Logger.debug("💾 [兑换订单仓储] 缓存命中: 待审核订单")
        {:ok, orders}
    end
  end

  @doc """
  获取待处理订单

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, orders}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_pending_process_orders(options \\ []) do
    Logger.debug("⏳ [兑换订单仓储] 获取待处理订单")

    cache_key = build_cache_key("pending_process_orders", :list, options)

    case get_from_cache(cache_key) do
      :miss ->
        fetch_and_cache_pending_process_orders(options, cache_key)
      {:hit, orders} ->
        Logger.debug("💾 [兑换订单仓储] 缓存命中: 待处理订单")
        {:ok, orders}
    end
  end

  @doc """
  批量审核订单

  ## 参数
  - `order_ids` - 订单ID列表
  - `audit_status` - 审核状态
  - `auditor_id` - 审核人ID
  - `audit_notes` - 审核备注

  ## 返回
  - `{:ok, results}` - 成功
  - `{:error, reason}` - 失败
  """
  def batch_audit_orders(order_ids, audit_status, auditor_id, audit_notes \\ nil) do
    Logger.info("🔍 [兑换订单仓储] 批量审核订单: #{length(order_ids)}个")

    results = Enum.map(order_ids, fn order_id ->
      case audit_order(order_id, audit_status, auditor_id, audit_notes) do
        {:ok, order} -> {:ok, order}
        {:error, reason} -> {:error, {order_id, reason}}
      end
    end)

    success_count = Enum.count(results, fn {status, _} -> status == :ok end)
    Logger.info("✅ [兑换订单仓储] 批量审核完成: #{success_count}/#{length(order_ids)}")

    {:ok, results}
  end

  # ============================================================================
  # 统计查询
  # ============================================================================

  @doc """
  获取订单统计

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, statistics}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_order_statistics(options \\ []) do
    Logger.debug("📊 [兑换订单仓储] 获取订单统计")

    try do
      stats = %{
        total_orders: get_total_orders(),
        pending_audit_orders: get_pending_audit_orders_count(),
        approved_orders: get_approved_orders_count(),
        rejected_orders: get_rejected_orders_count(),
        pending_process_orders: get_pending_process_orders_count(),
        completed_orders: get_completed_orders_count(),
        recent_orders: get_recent_orders_count(options[:days] || 7),
        orders_by_type: get_orders_by_type(),
        total_exchange_amount: get_total_exchange_amount()
      }

      {:ok, stats}
    rescue
      error ->
        Logger.error("❌ [兑换订单仓储] 获取统计异常: #{inspect(error)}")
        {:error, :statistics_error}
    end
  end

  # ============================================================================
  # 缓存管理
  # ============================================================================

  @doc """
  清除订单相关缓存

  ## 参数
  - `cache_type` - 缓存类型
  - `id` - ID（可选）
  """
  def clear_cache(cache_type, id \\ nil) do
    cache_patterns = case {cache_type, id} do
      {:order, nil} -> ["order:*"]
      {:order, order_id} -> ["order:#{order_id}:*"]
      {:user_orders, user_id} -> ["user_orders:#{user_id}:*"]
      {:pending_orders, _} -> ["pending_*_orders:*"]
      {:all, _} -> ["order:*", "user_orders:*", "pending_*_orders:*", "orders_list:*"]
    end

    Logger.debug("🧹 [兑换订单仓储] 清除缓存: #{inspect(cache_patterns)}")
    Enum.each(cache_patterns, &clear_cache_pattern/1)
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 标准化列表参数
  defp normalize_list_params(params) do
    %{
      page: max(1, params[:page] || 1),
      limit: min(params[:limit] || @default_limit, @max_limit),
      search: String.trim(params[:search] || ""),
      filters: params[:filters] || %{},
      sort: params[:sort] || [inserted_at: :desc],
      preload: params[:preload] || []
    }
  end

  # 获取并缓存订单
  defp fetch_and_cache_order_by_id(order_id, preload, cache_key) do
    case fetch_order_by_id(order_id, preload) do
      {:ok, order} ->
        cache_put(cache_key, order, @cache_ttl)
        {:ok, order}
      error -> error
    end
  end

  # 获取订单
  defp fetch_order_by_id(order_id, preload) do
    try do
      case ExchangeOrder
           |> maybe_preload(preload)
           |> Ash.get(order_id) do
        {:ok, order} -> {:ok, order}
        {:error, %Ash.Error.Query.NotFound{}} -> {:error, :not_found}
        {:error, error} ->
          Logger.error("❌ [兑换订单仓储] 获取订单失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [兑换订单仓储] 获取订单异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # 执行更新订单
  defp do_update_order(order, update_data) do
    try do
      case order
           |> Ash.Changeset.for_update(:update, update_data)
           |> Ash.update() do
        {:ok, updated_order} -> {:ok, updated_order}
        {:error, error} ->
          Logger.error("❌ [兑换订单仓储] 更新订单失败: #{inspect(error)}")
          {:error, :update_failed}
      end
    rescue
      error ->
        Logger.error("❌ [兑换订单仓储] 更新订单异常: #{inspect(error)}")
        {:error, :update_error}
    end
  end

  # 执行审核订单
  defp do_audit_order(order, audit_status, auditor_id, audit_notes) do
    update_data = %{
      audit_status: audit_status,
      auditor_id: auditor_id,
      audit_time: DateTime.utc_now(),
      audit_notes: audit_notes
    }

    try do
      case order
           |> Ash.Changeset.for_update(:audit_order, update_data)
           |> Ash.update() do
        {:ok, audited_order} -> {:ok, audited_order}
        {:error, error} ->
          Logger.error("❌ [兑换订单仓储] 审核订单失败: #{inspect(error)}")
          {:error, :audit_failed}
      end
    rescue
      error ->
        Logger.error("❌ [兑换订单仓储] 审核订单异常: #{inspect(error)}")
        {:error, :audit_error}
    end
  end

  # 获取并缓存订单列表
  defp fetch_and_cache_orders_list(params, cache_key) do
    case fetch_orders_list(params) do
      {:ok, result} ->
        cache_put(cache_key, result, @cache_ttl)
        {:ok, result}
      error -> error
    end
  end

  # 获取订单列表
  defp fetch_orders_list(params) do
    try do
      query = ExchangeOrder
      |> apply_order_filters(params.filters)
      |> apply_order_search(params.search)
      |> apply_sort(params.sort)

      execute_paginated_query(query, params)
    rescue
      error ->
        Logger.error("❌ [兑换订单仓储] 获取订单列表异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # 获取并缓存待审核订单
  defp fetch_and_cache_pending_audit_orders(options, cache_key) do
    case fetch_pending_audit_orders(options) do
      {:ok, orders} ->
        cache_put(cache_key, orders, @cache_ttl)
        {:ok, orders}
      error -> error
    end
  end

  # 获取待审核订单
  defp fetch_pending_audit_orders(options) do
    try do
      query = ExchangeOrder
      |> Ash.Query.filter(audit_status == 0)
      |> maybe_apply_exchange_type_filter(options[:exchange_type])
      |> apply_sort(options[:sort] || [inserted_at: :asc])
      |> maybe_limit(options[:limit])

      case Ash.read(query) do
        {:ok, orders} -> {:ok, orders}
        {:error, error} ->
          Logger.error("❌ [兑换订单仓储] 获取待审核订单失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [兑换订单仓储] 获取待审核订单异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # 获取并缓存待处理订单
  defp fetch_and_cache_pending_process_orders(options, cache_key) do
    case fetch_pending_process_orders(options) do
      {:ok, orders} ->
        cache_put(cache_key, orders, @cache_ttl)
        {:ok, orders}
      error -> error
    end
  end

  # 获取待处理订单
  defp fetch_pending_process_orders(options) do
    try do
      query = ExchangeOrder
      |> Ash.Query.filter(audit_status == 1 and progress_status == 0)
      |> maybe_apply_exchange_type_filter(options[:exchange_type])
      |> apply_sort(options[:sort] || [audit_time: :asc])
      |> maybe_limit(options[:limit])

      case Ash.read(query) do
        {:ok, orders} -> {:ok, orders}
        {:error, error} ->
          Logger.error("❌ [兑换订单仓储] 获取待处理订单失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [兑换订单仓储] 获取待处理订单异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # 应用订单过滤器
  defp apply_order_filters(query, filters) when map_size(filters) == 0, do: query
  defp apply_order_filters(query, filters) do
    Enum.reduce(filters, query, fn {key, value}, acc_query ->
      apply_order_filter(acc_query, key, value)
    end)
  end

  # 应用单个订单过滤器
  defp apply_order_filter(query, :audit_status, audit_status) when not is_nil(audit_status) do
    Ash.Query.filter(query, audit_status  == audit_status)
  end
  defp apply_order_filter(query, :progress_status, progress_status) when not is_nil(progress_status) do
    Ash.Query.filter(query, progress_status  == progress_status)
  end
  defp apply_order_filter(query, :exchange_type, exchange_type) when not is_nil(exchange_type) do
    Ash.Query.filter(query, exchange_type  == exchange_type)
  end
  defp apply_order_filter(query, :user_id, user_id) when not is_nil(user_id) do
    Ash.Query.filter(query, user_id  == user_id)
  end
  defp apply_order_filter(query, :platform, platform) when not is_nil(platform) do
    Ash.Query.filter(query, platform  == platform)
  end
  defp apply_order_filter(query, _key, _value), do: query

  # 应用订单搜索
  defp apply_order_search(query, search) when search == "" or is_nil(search), do: query
  defp apply_order_search(query, search) do
    search_term = "%#{search}%"
    Ash.Query.filter(query, ilike(order_id, search_term) or ilike(platform, search_term))
  end

  # 执行分页查询
  defp execute_paginated_query(query, params) do
    offset = (params.page - 1) * params.limit

    case query
         |> maybe_preload(params.preload)
         |> Ash.Query.page(count: true, limit: params.limit, offset: offset)
         |> Ash.read() do
      {:ok, %Ash.Page.Offset{results: items, count: total_count}} ->
        page_info = %{
          total_count: total_count,
          page: params.page,
          limit: params.limit,
          total_pages: ceil(total_count / params.limit)
        }
        {:ok, %{items: items, page_info: page_info}}
      {:error, error} ->
        Logger.error("❌ [兑换订单仓储] 分页查询失败: #{inspect(error)}")
        {:error, :query_failed}
    end
  end

  # 统计函数
  defp get_total_orders do
    case ExchangeOrder |> Ash.Query.aggregate(:count, :id) |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_pending_audit_orders_count do
    case ExchangeOrder
         |> Ash.Query.filter(audit_status == 0)
         |> Ash.Query.aggregate(:count, :id)
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_approved_orders_count do
    case ExchangeOrder
         |> Ash.Query.filter(audit_status == 1)
         |> Ash.Query.aggregate(:count, :id)
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_rejected_orders_count do
    case ExchangeOrder
         |> Ash.Query.filter(audit_status == 2)
         |> Ash.Query.aggregate(:count, :id)
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_pending_process_orders_count do
    case ExchangeOrder
         |> Ash.Query.filter(audit_status == 1 and progress_status == 0)
         |> Ash.Query.aggregate(:count, :id)
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_completed_orders_count do
    case ExchangeOrder
         |> Ash.Query.filter(progress_status == 2)
         |> Ash.Query.aggregate(:count, :id)
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_recent_orders_count(days) do
    date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)

    case ExchangeOrder
         |> Ash.Query.filter(inserted_at >= date_threshold)
         |> Ash.Query.aggregate(:count, :id)
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_orders_by_type do
    # 这里需要根据实际需求实现类型分组统计
    %{}
  end

  defp get_total_exchange_amount do
    # 这里需要根据实际需求计算总兑换金额
    Decimal.new("0")
  end

  # 可能预加载关联数据
  defp maybe_preload(query, []), do: query
  defp maybe_preload(query, preload) when is_list(preload) do
    Ash.Query.load(query, preload)
  end

  # 可能限制数量
  defp maybe_limit(query, nil), do: query
  defp maybe_limit(query, limit) when is_integer(limit) and limit > 0 do
    Ash.Query.limit(query, limit)
  end
  defp maybe_limit(query, _), do: query

  # 应用排序
  defp apply_sort(query, sort) when is_list(sort) do
    Ash.Query.sort(query, sort)
  end
  defp apply_sort(query, _), do: query

  # 可能应用审核状态过滤
  defp maybe_apply_audit_status_filter(query, nil), do: query
  defp maybe_apply_audit_status_filter(query, audit_status) do
    Ash.Query.filter(query, audit_status  == audit_status)
  end

  # 可能应用进度状态过滤
  defp maybe_apply_progress_status_filter(query, nil), do: query
  defp maybe_apply_progress_status_filter(query, progress_status) do
    Ash.Query.filter(query, progress_status  == progress_status)
  end

  # 可能应用兑换类型过滤
  defp maybe_apply_exchange_type_filter(query, nil), do: query
  defp maybe_apply_exchange_type_filter(query, exchange_type) do
    Ash.Query.filter(query, exchange_type  == exchange_type)
  end

  # 可能应用日期范围
  defp maybe_apply_date_range(query, nil), do: query
  defp maybe_apply_date_range(query, {start_date, end_date}) do
    Ash.Query.filter(query, inserted_at  >= start_date and inserted_at <= end_date)
  end

  # 构建缓存键
  defp build_cache_key(prefix, id, extra \\ nil) do
    case extra do
      nil -> "#{prefix}:#{id}"
      extra -> "#{prefix}:#{id}:#{:erlang.phash2(extra)}"
    end
  end

  # 缓存操作（需要实现具体的缓存后端）
  defp get_from_cache(_key), do: :miss
  defp cache_put(_key, _value, _ttl), do: :ok
  defp clear_cache_pattern(_pattern), do: :ok
end
