defmodule RacingGame.Live.AdminPanel.Utils.CalculationHelper do
  @moduledoc """
  计算助手工具
  
  提供通用的计算功能：
  - 数学计算
  - 统计计算
  - 金融计算
  - 百分比计算
  """

  require Logger

  # ============================================================================
  # 基础数学计算
  # ============================================================================

  @doc """
  安全除法，避免除零错误

  ## 参数
  - `dividend` - 被除数
  - `divisor` - 除数
  - `default` - 除零时的默认值

  ## 返回
  - 计算结果或默认值
  """
  def safe_divide(dividend, divisor, default \\ 0) do
    if divisor == 0 or divisor == 0.0 do
      default
    else
      dividend / divisor
    end
  end

  @doc """
  计算百分比

  ## 参数
  - `part` - 部分值
  - `total` - 总值
  - `precision` - 精度（小数位数）

  ## 返回
  - 百分比值
  """
  def calculate_percentage(part, total, precision \\ 2) do
    percentage = safe_divide(part, total, 0) * 100
    Float.round(percentage, precision)
  end

  @doc """
  计算增长率

  ## 参数
  - `current` - 当前值
  - `previous` - 之前值
  - `precision` - 精度（小数位数）

  ## 返回
  - 增长率百分比
  """
  def calculate_growth_rate(current, previous, precision \\ 2) do
    if previous == 0 do
      if current > 0, do: 100.0, else: 0.0
    else
      growth_rate = (current - previous) / previous * 100
      Float.round(growth_rate, precision)
    end
  end

  @doc """
  计算平均值

  ## 参数
  - `values` - 数值列表
  - `precision` - 精度（小数位数）

  ## 返回
  - 平均值
  """
  def calculate_average(values, precision \\ 2) when is_list(values) do
    case values do
      [] -> 0.0
      _ ->
        sum = Enum.sum(values)
        average = sum / length(values)
        Float.round(average, precision)
    end
  end

  @doc """
  计算中位数

  ## 参数
  - `values` - 数值列表

  ## 返回
  - 中位数
  """
  def calculate_median(values) when is_list(values) do
    case values do
      [] -> 0.0
      _ ->
        sorted = Enum.sort(values)
        length = length(sorted)
        
        if rem(length, 2) == 0 do
          # 偶数个元素，取中间两个的平均值
          mid1 = Enum.at(sorted, div(length, 2) - 1)
          mid2 = Enum.at(sorted, div(length, 2))
          (mid1 + mid2) / 2
        else
          # 奇数个元素，取中间元素
          Enum.at(sorted, div(length, 2))
        end
    end
  end

  # ============================================================================
  # 统计计算
  # ============================================================================

  @doc """
  计算标准差

  ## 参数
  - `values` - 数值列表
  - `precision` - 精度（小数位数）

  ## 返回
  - 标准差
  """
  def calculate_standard_deviation(values, precision \\ 2) when is_list(values) do
    case values do
      [] -> 0.0
      [_] -> 0.0
      _ ->
        mean = calculate_average(values, 10)  # 使用更高精度计算均值
        variance = Enum.reduce(values, 0, fn value, acc ->
          acc + :math.pow(value - mean, 2)
        end) / length(values)
        
        std_dev = :math.sqrt(variance)
        Float.round(std_dev, precision)
    end
  end

  @doc """
  计算分位数

  ## 参数
  - `values` - 数值列表
  - `percentile` - 分位数（0-100）

  ## 返回
  - 分位数值
  """
  def calculate_percentile(values, percentile) when is_list(values) and percentile >= 0 and percentile <= 100 do
    case values do
      [] -> 0.0
      _ ->
        sorted = Enum.sort(values)
        length = length(sorted)
        index = percentile / 100 * (length - 1)
        
        if index == trunc(index) do
          # 整数索引
          Enum.at(sorted, trunc(index))
        else
          # 需要插值
          lower_index = trunc(index)
          upper_index = lower_index + 1
          
          if upper_index >= length do
            Enum.at(sorted, lower_index)
          else
            lower_value = Enum.at(sorted, lower_index)
            upper_value = Enum.at(sorted, upper_index)
            fraction = index - lower_index
            
            lower_value + fraction * (upper_value - lower_value)
          end
        end
    end
  end

  @doc """
  计算分布统计

  ## 参数
  - `values` - 数值列表

  ## 返回
  - 包含各种统计指标的映射
  """
  def calculate_distribution_stats(values) when is_list(values) do
    case values do
      [] ->
        %{
          count: 0,
          sum: 0,
          min: 0,
          max: 0,
          mean: 0.0,
          median: 0.0,
          std_dev: 0.0,
          q25: 0.0,
          q75: 0.0
        }
      
      _ ->
        %{
          count: length(values),
          sum: Enum.sum(values),
          min: Enum.min(values),
          max: Enum.max(values),
          mean: calculate_average(values),
          median: calculate_median(values),
          std_dev: calculate_standard_deviation(values),
          q25: calculate_percentile(values, 25),
          q75: calculate_percentile(values, 75)
        }
    end
  end

  # ============================================================================
  # 金融计算
  # ============================================================================

  @doc """
  计算赔付金额

  ## 参数
  - `bet_amount` - 下注金额
  - `odds` - 赔率

  ## 返回
  - 赔付金额
  """
  def calculate_payout(bet_amount, odds) when is_number(bet_amount) and is_number(odds) do
    trunc(bet_amount * odds)
  end

  @doc """
  计算盈亏

  ## 参数
  - `payout` - 赔付金额
  - `bet_amount` - 下注金额

  ## 返回
  - 盈亏金额（正数为盈利，负数为亏损）
  """
  def calculate_profit_loss(payout, bet_amount) when is_number(payout) and is_number(bet_amount) do
    payout - bet_amount
  end

  @doc """
  计算胜率

  ## 参数
  - `wins` - 胜利次数
  - `total` - 总次数
  - `precision` - 精度（小数位数）

  ## 返回
  - 胜率百分比
  """
  def calculate_win_rate(wins, total, precision \\ 2) do
    calculate_percentage(wins, total, precision)
  end

  @doc """
  计算投资回报率 (ROI)

  ## 参数
  - `gain` - 收益
  - `cost` - 成本
  - `precision` - 精度（小数位数）

  ## 返回
  - ROI百分比
  """
  def calculate_roi(gain, cost, precision \\ 2) do
    if cost == 0 do
      0.0
    else
      roi = (gain - cost) / cost * 100
      Float.round(roi, precision)
    end
  end

  @doc """
  计算复合增长率 (CAGR)

  ## 参数
  - `ending_value` - 期末值
  - `beginning_value` - 期初值
  - `periods` - 期数
  - `precision` - 精度（小数位数）

  ## 返回
  - CAGR百分比
  """
  def calculate_cagr(ending_value, beginning_value, periods, precision \\ 2) do
    if beginning_value == 0 or periods == 0 do
      0.0
    else
      cagr = (:math.pow(ending_value / beginning_value, 1 / periods) - 1) * 100
      Float.round(cagr, precision)
    end
  end

  # ============================================================================
  # 时间相关计算
  # ============================================================================

  @doc """
  计算时间差（秒）

  ## 参数
  - `start_time` - 开始时间
  - `end_time` - 结束时间

  ## 返回
  - 时间差（秒）
  """
  def calculate_time_diff_seconds(start_time, end_time) do
    DateTime.diff(end_time, start_time, :second)
  end

  @doc """
  计算时间差（分钟）

  ## 参数
  - `start_time` - 开始时间
  - `end_time` - 结束时间

  ## 返回
  - 时间差（分钟）
  """
  def calculate_time_diff_minutes(start_time, end_time) do
    DateTime.diff(end_time, start_time, :second) / 60
  end

  @doc """
  计算时间差（小时）

  ## 参数
  - `start_time` - 开始时间
  - `end_time` - 结束时间

  ## 返回
  - 时间差（小时）
  """
  def calculate_time_diff_hours(start_time, end_time) do
    DateTime.diff(end_time, start_time, :second) / 3600
  end

  @doc """
  计算时间差（天）

  ## 参数
  - `start_time` - 开始时间
  - `end_time` - 结束时间

  ## 返回
  - 时间差（天）
  """
  def calculate_time_diff_days(start_time, end_time) do
    DateTime.diff(end_time, start_time, :second) / 86400
  end

  # ============================================================================
  # 数据聚合计算
  # ============================================================================

  @doc """
  按时间段聚合数据

  ## 参数
  - `data` - 数据列表，每个元素包含 :timestamp 和 :value
  - `interval` - 时间间隔（:hour, :day, :week, :month）

  ## 返回
  - 聚合后的数据列表
  """
  def aggregate_by_time_interval(data, interval) when is_list(data) do
    data
    |> Enum.group_by(fn item ->
      truncate_datetime(item.timestamp, interval)
    end)
    |> Enum.map(fn {period, items} ->
      values = Enum.map(items, & &1.value)
      %{
        period: period,
        count: length(items),
        sum: Enum.sum(values),
        average: calculate_average(values),
        min: if(length(values) > 0, do: Enum.min(values), else: 0),
        max: if(length(values) > 0, do: Enum.max(values), else: 0)
      }
    end)
    |> Enum.sort_by(& &1.period, DateTime)
  end

  @doc """
  计算移动平均

  ## 参数
  - `values` - 数值列表
  - `window_size` - 窗口大小

  ## 返回
  - 移动平均列表
  """
  def calculate_moving_average(values, window_size) when is_list(values) and window_size > 0 do
    if length(values) < window_size do
      []
    else
      values
      |> Enum.chunk_every(window_size, 1, :discard)
      |> Enum.map(&calculate_average/1)
    end
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 截断日期时间到指定间隔
  defp truncate_datetime(datetime, :hour) do
    %{datetime | minute: 0, second: 0, microsecond: {0, 0}}
  end
  defp truncate_datetime(datetime, :day) do
    %{datetime | hour: 0, minute: 0, second: 0, microsecond: {0, 0}}
  end
  defp truncate_datetime(datetime, :week) do
    # 截断到周一
    days_to_subtract = Date.day_of_week(datetime) - 1
    datetime
    |> DateTime.add(-days_to_subtract * 86400, :second)
    |> truncate_datetime(:day)
  end
  defp truncate_datetime(datetime, :month) do
    %{datetime | day: 1, hour: 0, minute: 0, second: 0, microsecond: {0, 0}}
  end
end
