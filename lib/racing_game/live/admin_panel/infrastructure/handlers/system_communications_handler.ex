defmodule RacingGame.Live.AdminPanel.Handlers.SystemCommunicationsHandler do
  @moduledoc """
  系统通信专用事件处理器

  处理系统通信对话框中的所有事件，包括创建、编辑、保存、验证等操作。
  """

  require Logger
  alias RacingGame.SystemCommunication
  alias RacingGame.Live.AdminPanel.Validators.CommunicationValidator
  alias RacingGame.Live.AdminPanel.Operations.SystemCommunicationsOperations
  alias RacingGame.Live.AdminPanel.Mappings.SystemCommunicationsMappings
  import RacingGame.Utils.DialogHelper
  import Phoenix.Component, only: [assign: 3]
  import Phoenix.LiveView, only: [put_flash: 3]

  @doc """
  处理表单验证事件

  ## 参数
  - `params` - 表单参数
  - `socket` - Phoenix LiveView socket

  ## 返回
  - `{:noreply, socket}` - 更新后的socket
  """
  def handle_validate_form(params, socket) do
    Logger.debug("🔍 [系统通信处理器] 表单验证事件")

    # 更新表单数据
    updated_form_data = merge_form_data(socket.assigns.form_data, params)
    socket = assign(socket, :form_data, updated_form_data)

    {:noreply, socket}
  end

  @doc """
  处理保存通信事件

  ## 参数
  - `params` - 表单参数
  - `socket` - Phoenix LiveView socket

  ## 返回
  - `{:noreply, socket}` - 更新后的socket
  """
  def handle_save_communication(params, socket) do
    Logger.info("💾 [系统通信处理器] 开始保存通信")

    communication_params = params["communication"] || socket.assigns.form_data
    close_after_save = params["close_after_save"] != "true"

    case CommunicationValidator.validate_communication_params(communication_params) do
      {:ok, validated_params} ->
        show_save_confirmation(socket, validated_params, close_after_save)
      {:error, errors} ->
        show_validation_error(socket, errors)
    end
  end

  @doc """
  处理确认保存事件

  ## 参数
  - `params` - 确认参数
  - `socket` - Phoenix LiveView socket

  ## 返回
  - `{:noreply, socket}` - 更新后的socket
  """
  def handle_confirm_save(params, socket) do
    Logger.info("✅ [系统通信处理器] 确认保存操作")

    validated_params = params["params"]
    close_after_save = params["close_after_save"]

    case socket.assigns.mode do
      :create ->
        execute_create_operation(socket, validated_params, close_after_save)
      :edit ->
        execute_update_operation(socket, validated_params, close_after_save)
      _ ->
        handle_save_error(socket, "未知的操作模式")
    end
  end

  @doc """
  处理对话框关闭事件

  ## 参数
  - `socket` - Phoenix LiveView socket

  ## 返回
  - `{:noreply, socket}` - 更新后的socket
  """
  def handle_close_dialog(socket) do
    Logger.debug("🚪 [系统通信处理器] 关闭对话框")

    socket =
      socket
      |> assign(:show_main_dialog, false)
      |> assign(:form_dirty, false)

    send(self(), {:modal_event, :close})
    {:noreply, socket}
  end

  # 私有函数 - 合并表单数据
  defp merge_form_data(existing_data, new_params) when is_map(existing_data) and is_map(new_params) do
    Map.merge(existing_data, new_params)
  end
  defp merge_form_data(_existing_data, new_params) when is_map(new_params), do: new_params
  defp merge_form_data(existing_data, _new_params) when is_map(existing_data), do: existing_data
  defp merge_form_data(_existing_data, _new_params), do: %{}

  # 私有函数 - 显示保存确认对话框
  defp show_save_confirmation(socket, validated_params, close_after_save) do
    type_config = socket.assigns.type_config
    operation = get_operation_name(socket.assigns.mode)

    socket = show_confirm_dialog(socket, :save_confirm,
      title: "确认#{operation}",
      message: "您确定要#{operation}这个#{type_config.title}吗？",
      confirm_action: "confirm_save",
      confirm_data: %{
        "params" => validated_params,
        "close_after_save" => close_after_save
      }
    )
    {:noreply, socket}
  end

  # 私有函数 - 显示验证错误对话框
  defp show_validation_error(socket, errors) do
    error_message = format_validation_errors(errors)
    socket = show_error_dialog(socket, :validation_error,
      title: "表单验证失败",
      message: "❌ #{error_message}",
      confirm_action: "hide_dialog",
      confirm_data: %{"dialog" => "validation_error"}
    )
    {:noreply, socket}
  end

  # 私有函数 - 执行创建操作
  defp execute_create_operation(socket, validated_params, close_after_save) do
    case create_communication(validated_params, socket) do
      {:ok, communication} ->
        handle_save_success(socket, communication, close_after_save)
      {:error, error} ->
        handle_save_error(socket, error)
    end
  end

  # 私有函数 - 执行更新操作
  defp execute_update_operation(socket, validated_params, close_after_save) do
    case update_communication(validated_params, socket) do
      {:ok, communication} ->
        handle_save_success(socket, communication, close_after_save)
      {:error, error} ->
        handle_save_error(socket, error)
    end
  end

  # 私有函数 - 创建通信
  defp create_communication(validated_params, socket) do
    current_user_id = get_current_user_id(socket)
    create_params = Map.put(validated_params, :creator_id, current_user_id)

    case SystemCommunication.create(create_params) do
      {:ok, communication} ->
        Logger.info("✅ [系统通信处理器] 创建成功: #{communication.title}")
        {:ok, communication}
      {:error, error} ->
        Logger.error("❌ [系统通信处理器] 创建失败: #{inspect(error)}")
        {:error, format_create_error(error)}
    end
  end

  # 私有函数 - 更新通信
  defp update_communication(validated_params, socket) do
    case get_communication_for_update(socket) do
      {:ok, communication} ->
        case SystemCommunication.update(communication, validated_params) do
          {:ok, updated_communication} ->
            Logger.info("✅ [系统通信处理器] 更新成功: #{updated_communication.title}")
            {:ok, updated_communication}
          {:error, error} ->
            Logger.error("❌ [系统通信处理器] 更新失败: #{inspect(error)}")
            {:error, format_update_error(error)}
        end
      {:error, error} ->
        {:error, error}
    end
  end

  # 私有函数 - 获取要更新的通信记录
  defp get_communication_for_update(socket) do
    case socket.assigns.data do
      %{id: id} when not is_nil(id) ->
        case SystemCommunication.read(id) do
          {:ok, communication} -> {:ok, communication}
          {:error, _} -> {:error, "记录不存在，可能已被删除"}
        end
      _ ->
        {:error, "缺少记录ID"}
    end
  end

  # 私有函数 - 处理保存成功
  defp handle_save_success(socket, communication, close_after_save) do
    socket = assign(socket, :form_dirty, false)

    event = if close_after_save, do: :save_success, else: :save_success_no_close
    send(self(), {:modal_event, event, communication})
    {:noreply, socket}
  end

  # 私有函数 - 处理保存错误
  defp handle_save_error(socket, error) do
    send(self(), {:modal_event, :save_error, error})
    {:noreply, socket}
  end

  # 私有函数 - 获取当前用户ID
  defp get_current_user_id(socket) do
    case Map.get(socket.assigns, :current_user) do
      %{id: id} when is_integer(id) ->
        Logger.info("✅ [系统通信处理器] 找到当前用户ID: #{id}")
        id
      %{id: id} when is_binary(id) ->
        case Integer.parse(id) do
          {parsed_id, ""} ->
            Logger.info("✅ [系统通信处理器] 解析用户ID: #{parsed_id}")
            parsed_id
          _ ->
            Logger.warning("⚠️ [系统通信处理器] 无法解析用户ID: #{id}，使用默认值")
            1
        end
      _ ->
        # 如果没有当前用户信息，使用默认值
        Logger.warning("⚠️ [系统通信处理器] 未找到当前用户信息，使用默认用户ID")
        1  # 使用默认的管理员用户ID
    end
  end

  # 私有函数 - 获取操作名称
  defp get_operation_name(:create), do: "创建"
  defp get_operation_name(:edit), do: "更新"
  defp get_operation_name(_), do: "保存"

  # 私有函数 - 格式化验证错误
  defp format_validation_errors(errors) when is_list(errors) do
    Enum.join(errors, "；")
  end
  defp format_validation_errors(error) when is_binary(error), do: error
  defp format_validation_errors(_), do: "验证失败"

  # 私有函数 - 格式化创建错误
  defp format_create_error(error) when is_binary(error), do: error
  defp format_create_error(%{message: message}) when is_binary(message), do: message
  defp format_create_error(%Ash.Error.Invalid{} = error), do: "创建失败: #{inspect(error)}"
  defp format_create_error(%{__struct__: struct_name} = error) when is_atom(struct_name) do
    case Atom.to_string(struct_name) do
      "Elixir.Ash.Error" <> _ -> "创建失败: #{inspect(error)}"
      _ -> "创建失败，请重试"
    end
  end

  # 私有函数 - 格式化更新错误
  defp format_update_error(error) when is_binary(error), do: error
  defp format_update_error(%{message: message}) when is_binary(message), do: message
  defp format_update_error(%Ash.Error.Invalid{} = error), do: "更新失败: #{inspect(error)}"
  defp format_update_error(%{__struct__: struct_name} = error) when is_atom(struct_name) do
    case Atom.to_string(struct_name) do
      "Elixir.Ash.Error" <> _ -> "更新失败: #{inspect(error)}"
      _ -> "更新失败，请重试"
    end
  end

  @doc """
  处理模态对话框表单提交事件
  """
  def handle_modal_submit(form_data, mode, socket) do
    Logger.info("💾 [系统通信处理器] 模态对话框提交表单")
    Logger.info("📝 [系统通信处理器] 提交的表单数据: #{inspect(form_data)}")

    # 处理 checkbox 字段 - 确保 active 字段存在
    processed_form_data = Map.put_new(form_data, "active", false)

    # 清理和转换数据类型
    cleaned_form_data = clean_form_params(processed_form_data)
    Logger.info("📝 [系统通信处理器] 处理后的表单数据: #{inspect(cleaned_form_data)}")
    Logger.info("📊 [系统通信处理器] 开始验证表单数据")
    Logger.info("📊 [系统通信处理器] 验证模式: #{mode}")
    Logger.info("📊 [系统通信处理器] Socket类型: #{socket.assigns.type}")
    Logger.info("📊 [系统通信处理器] Socket模式: #{socket.assigns.mode}")
    Logger.info("📊 [系统通信处理器] Socket表单数据: #{inspect(socket.assigns.form_data)}")
    Logger.info("📊 [系统通信处理器] Socket当前用户: #{inspect(socket.assigns.current_user)}")
    Logger.info("📊 [系统通信处理器] Socket数据: #{inspect(socket.assigns.data)}")
    Logger.info("📊 [系统通信处理器] Socket数据校验: #{inspect(validate_modal_form_data(socket.assigns.form_data))}")

    case validate_modal_form_data(cleaned_form_data) do
      {:ok, validated_data} ->
        Logger.info("✅ [系统通信处理器] 表单验证通过")
        show_modal_confirmation(socket, validated_data, mode)
      {:error, errors} ->
        Logger.error("❌ [系统通信处理器] 表单验证失败: #{inspect(errors)}")
        show_modal_validation_error(socket, errors)
    end
  end

  @doc """
  处理模态对话框确认保存事件
  """
  def handle_modal_confirm_save(validated_data, mode, socket) do
    Logger.info("🔄 [系统通信处理器] 确认模态对话框保存")
    Logger.info("📊 [系统通信处理器] 验证数据: #{inspect(validated_data)}")
    Logger.info("📊 [系统通信处理器] 模式: #{mode}")
    Logger.info("📊 [系统通信处理器] Socket类型: #{socket.assigns.type}")

    # 简化处理：直接执行保存操作
    try do
      Logger.info("🚀 [系统通信处理器] 开始执行保存操作")

      case execute_modal_save_operation(validated_data, mode, socket) do
        {:ok, saved_record} ->
          Logger.info("✅ [系统通信处理器] 保存操作成功: #{inspect(saved_record)}")
          handle_modal_save_success(socket, saved_record, mode)
        {:error, reason} ->
          Logger.error("❌ [系统通信处理器] 保存操作失败: #{inspect(reason)}")
          handle_modal_save_error(socket, reason)
      end
    rescue
      error ->
        Logger.error("❌ [系统通信处理器] 执行保存时发生异常: #{inspect(error)}")
        Logger.error("❌ [系统通信处理器] 异常堆栈: #{inspect(__STACKTRACE__)}")
        handle_modal_save_error(socket, "保存时发生异常: #{inspect(error)}")
    end
  end

  @doc """
  处理模态对话框表单验证事件
  """
  def handle_modal_validate_form(params, socket) do
    Logger.info("✅ [系统通信处理器] 模态对话框验证表单")
    Logger.info("📝 [系统通信处理器] 接收到的参数: #{inspect(params)}")
    Logger.info("📝 [系统通信处理器] 当前form_data: #{inspect(socket.assigns.form_data)}")

    # 处理 checkbox 字段 - Phoenix 表单中未选中的 checkbox 不会发送参数
    processed_params = Map.put_new(params, "active", false)

    # 数据类型转换和清理
    cleaned_params = clean_form_params(processed_params)

    # 更新表单数据
    updated_form_data = Map.merge(socket.assigns.form_data, cleaned_params)

    Logger.info("📝 [系统通信处理器] 更新后的form_data: #{inspect(updated_form_data)}")

    # 重新创建正确的表单 changeset
    changeset =
      {%{}, %{title: :string, content: :string, priority: :string,
              recipient_type: :string, recipient_id: :string,
              active: :boolean, expires_at: :string}}
      |> Ecto.Changeset.cast(updated_form_data, [:title, :content, :priority,
                                                 :recipient_type, :recipient_id,
                                                 :active, :expires_at])

    form = Phoenix.Component.to_form(changeset, as: "communication")

    socket =
      socket
      |> assign(:form_data, updated_form_data)
      |> assign(:form, form)

    {:noreply, socket}
  end

  # 私有函数 - 验证模态表单数据（主入口）
  defp validate_modal_form_data(form_data) do
    Logger.info("🔍 [系统通信处理器] 开始验证表单数据: #{inspect(form_data)}")

    validation_results = [
      validate_title(form_data["title"]),
      validate_content(form_data["content"]),
      validate_priority(form_data["priority"]),
      validate_recipient_type(form_data["recipient_type"])
    ]

    errors = validation_results
             |> Enum.filter(&match?({:error, _}, &1))
             |> Enum.map(fn {:error, error} -> error end)

    Logger.info("🔍 [系统通信处理器] 验证结果 - 错误数量: #{length(errors)}")

    if Enum.empty?(errors) do
      Logger.info("✅ [系统通信处理器] 表单验证通过")
      {:ok, form_data}
    else
      Logger.error("❌ [系统通信处理器] 表单验证失败: #{inspect(errors)}")
      {:error, errors}
    end
  end

  # 验证标题
  defp validate_title(title) when title in [nil, ""], do: {:error, "标题不能为空"}
  defp validate_title(title) do
    title = String.trim(to_string(title))
    cond do
      String.length(title) < 2 -> {:error, "标题至少需要2个字符"}
      String.length(title) > 100 -> {:error, "标题不能超过100个字符"}
      true -> :ok
    end
  end

  # 验证内容
  defp validate_content(content) when content in [nil, ""], do: {:error, "内容不能为空"}
  defp validate_content(content) do
    content = String.trim(to_string(content))
    cond do
      String.length(content) < 5 -> {:error, "内容至少需要5个字符"}
      String.length(content) > 2000 -> {:error, "内容不能超过2000个字符"}
      true -> :ok
    end
  end

  # 验证优先级
  defp validate_priority(priority) do
    valid_priorities = ~w[low medium high urgent]
    priority = to_string(priority || "medium")

    if priority in valid_priorities do
      :ok
    else
      {:error, "优先级必须是：低、中、高、紧急之一"}
    end
  end

  # 验证接收者类型
  defp validate_recipient_type(recipient_type) do
    valid_types = ~w[all user admin]
    recipient_type = to_string(recipient_type || "all")

    if recipient_type in valid_types do
      :ok
    else
      {:error, "接收者类型必须是：所有用户、特定用户、管理员之一"}
    end
  end

  # 私有函数 - 显示模态确认对话框
  defp show_modal_confirmation(socket, validated_data, mode) do
    Logger.info("📝 [系统通信处理器] 模态确认消息: #{validated_data}")
    confirm_message = build_modal_confirm_message(validated_data, mode)

    socket =
      socket
      |> assign(:show_confirm_dialog, true)
      |> assign(:pending_save_data, validated_data)
      |> assign(:confirm_message, confirm_message)

    {:noreply, socket}
  end

  # 私有函数 - 显示模态验证错误对话框
  defp show_modal_validation_error(socket, errors) do
    Logger.warning("⚠️ [系统通信处理器] 显示验证错误对话框")
    Logger.warning("⚠️ [系统通信处理器] 验证错误: #{inspect(errors)}")

    # 构建错误消息
    error_message = build_validation_error_message(errors)

    socket =
      socket
      |> assign(:show_validation_error_dialog, true)
      |> assign(:validation_error_message, error_message)
      |> put_flash(:error, "表单验证失败，请检查输入内容")

    {:noreply, socket}
  end

  # 私有函数 - 构建验证错误消息
  defp build_validation_error_message(errors) when is_list(errors) do
    error_list = Enum.map_join(errors, "\n", fn error -> "• #{error}" end)

    """
    表单验证失败，请检查以下问题：

    #{error_list}

    请修正上述问题后重新提交。
    """
  end

  defp build_validation_error_message(error) when is_binary(error) do
    """
    表单验证失败：

    • #{error}

    请修正问题后重新提交。
    """
  end

  defp build_validation_error_message(_), do: "表单验证失败，请检查输入内容。"

  # 私有函数 - 执行模态保存操作
  defp execute_modal_save_operation(validated_data, mode, socket) do
    alias RacingGame.Live.AdminPanel.Operations.SystemCommunicationsOperations
    alias RacingGame.Live.AdminPanel.Mappings.SystemCommunicationsMappings

    Logger.info("🔄 [系统通信处理器] 执行模态保存操作，模式: #{mode}")

    case mode do
      :create ->
        # 准备创建数据（包含创建者信息）
        current_user_id = get_current_user_id(socket)
        Logger.info("📝 [系统通信处理器] 准备创建数据的人员ID: #{current_user_id}")

        save_data = SystemCommunicationsMappings.prepare_create_data(
          validated_data,
          to_string(socket.assigns.type),
          current_user_id
        )

        Logger.info("📝 [系统通信处理器] 准备创建数据: #{inspect(save_data)}")

        # 调用operations的直接创建函数，跳过重复验证
        SystemCommunicationsOperations.create_communication_direct(save_data, current_user_id)

      :edit ->
        # 准备更新数据
        save_data = SystemCommunicationsMappings.prepare_save_data(
          validated_data,
          to_string(socket.assigns.type)
        )

        communication_id = socket.assigns.data["id"]
        Logger.info("📝 [系统通信处理器] 准备更新数据: #{inspect(save_data)}, ID: #{communication_id}")

        case RacingGame.SystemCommunication.read(communication_id) do
          {:ok, communication} ->
            # 调用operations的直接更新函数，跳过重复验证
            SystemCommunicationsOperations.update_communication_direct(communication, save_data)
          {:error, _} ->
            Logger.error("❌ [系统通信处理器] 记录不存在: #{communication_id}")
            {:error, "记录不存在，可能已被删除"}
        end
    end
  end

  # 私有函数 - 处理模态保存成功
  defp handle_modal_save_success(socket, saved_record, mode) do
    type_name = get_type_display_name(socket.assigns.type)
    action = if mode == :create, do: "创建", else: "更新"

    # 发送成功消息给父组件
    send(self(), {:modal_event, :save_success, saved_record})

    socket =
      socket
      |> assign(:show_main_dialog, false)
      |> assign(:show_confirm_dialog, false)
      |> put_flash(:info, "#{type_name}#{action}成功！")

    {:noreply, socket}
  end

  # 私有函数 - 处理模态保存错误
  defp handle_modal_save_error(socket, reason) do
    socket =
      socket
      |> assign(:show_confirm_dialog, false)
      |> put_flash(:error, "保存失败: #{reason}")

    {:noreply, socket}
  end



  # 私有函数 - 构建模态确认消息
  defp build_modal_confirm_message(data, mode) do
    alias RacingGame.Live.AdminPanel.Mappings.SystemCommunicationsMappings

    SystemCommunicationsMappings.build_confirm_message(data, mode)
  end

  # 私有函数 - 获取类型显示名称
  defp get_type_display_name(type) do
    alias RacingGame.Live.AdminPanel.Mappings.SystemCommunicationsMappings

    SystemCommunicationsMappings.get_type_display_name(type)
  end

  # ============================================================================
  # 数据清理和验证辅助函数
  # ============================================================================

  # 私有函数 - 检查是否为空
  defp is_blank?(value) when value in [nil, ""], do: true
  defp is_blank?(str) when is_binary(str), do: String.trim(str) == ""
  defp is_blank?(_), do: false

  # 私有函数 - 清理表单参数（管道操作）
  defp clean_form_params(params) do
    params
    |> convert_active_field()
    |> trim_string_fields()
    |> handle_empty_datetime()
    |> normalize_recipient_type()
  end

  # 转换 active 字段为正确的 boolean 值
  defp convert_active_field(params) do
    active_value = case Map.get(params, "active") do
      value when value in ["true", true] -> true
      _ -> false
    end
    Map.put(params, "active", active_value)
  end

  # 清理字符串字段的空白
  defp trim_string_fields(params) do
    string_fields = ~w[title content recipient_id expires_at]

    Enum.reduce(string_fields, params, fn field, acc ->
      case Map.get(acc, field) do
        value when is_binary(value) -> Map.put(acc, field, String.trim(value))
        _ -> acc
      end
    end)
  end

  # 处理空的 datetime 字段
  defp handle_empty_datetime(params) do
    expires_at = case Map.get(params, "expires_at") do
      value when value in ["", nil] -> nil
      value -> value
    end
    Map.put(params, "expires_at", expires_at)
  end

  # 规范化接收者类型
  defp normalize_recipient_type(params) do
    recipient_type = case Map.get(params, "recipient_type") do
      type when type in ["all", "user", "admin"] -> type
      _ -> "all"
    end
    Map.put(params, "recipient_type", recipient_type)
  end

end
