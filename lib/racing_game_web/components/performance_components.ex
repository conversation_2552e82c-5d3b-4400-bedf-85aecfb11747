defmodule RacingGameWeb.PerformanceComponents do
  @moduledoc """
  Racing Game Admin Panel - 性能优化组件

  提供懒加载、虚拟滚动、性能监控等高性能组件
  """

  use Phoenix.Component
  import RacingGameWeb.UIComponents

  @doc """
  懒加载容器组件

  ## 属性
  - `component_type` - 组件类型 (必需)
  - `component_id` - 组件ID (可选)
  - `placeholder_height` - 占位符高度 (可选，默认: "200px")
  - `loading_text` - 加载文本 (可选)
  - `class` - 额外CSS类 (可选)

  ## 示例
      <.lazy_container 
        component_type="racing-chart" 
        component_id="user-stats-chart"
        placeholder_height="300px"
        loading_text="加载图表中..."
        class="mb-6"
      />
  """
  attr :component_type, :string, required: true
  attr :component_id, :string, default: nil
  attr :placeholder_height, :string, default: "200px"
  attr :loading_text, :string, default: "加载中..."
  attr :class, :string, default: ""
  attr :rest, :global

  def lazy_container(assigns) do
    assigns = assign(assigns, :component_id, assigns.component_id || assigns.component_type)

    ~H"""
    <div
      phx-hook="LazyLoad"
      data-lazy-component={@component_type}
      data-component-id={@component_id}
      class={["lazy-container", @class]}
      style={"min-height: #{@placeholder_height}"}
      {@rest}
    >
      <div class="lazy-placeholder flex items-center justify-center h-full">
        <div class="text-center">
          <div class="loading loading-spinner loading-lg text-primary mb-2"></div>
          <p class="text-sm text-gray-500"><%= @loading_text %></p>
        </div>
      </div>
    </div>
    """
  end

  @doc """
  虚拟滚动列表组件

  ## 属性
  - `items` - 数据项列表 (必需)
  - `item_height` - 每项高度 (可选，默认: 60)
  - `container_height` - 容器高度 (可选，默认: "400px")
  - `render_item` - 渲染函数插槽 (必需)
  - `class` - 额外CSS类 (可选)

  ## 示例
      <.virtual_scroll_list 
        items={@users} 
        item_height={80}
        container_height="500px"
        class="border rounded-lg"
      >
        <:render_item :let={user}>
          <.user_card user={user} compact={true} />
        </:render_item>
      </.virtual_scroll_list>
  """
  attr :items, :list, required: true
  attr :item_height, :integer, default: 60
  attr :container_height, :string, default: "400px"
  attr :class, :string, default: ""
  attr :rest, :global

  slot :render_item, required: true

  def virtual_scroll_list(assigns) do
    ~H"""
    <div
      phx-hook="VirtualScroll"
      data-item-height={@item_height}
      data-total-items={length(@items)}
      class={["virtual-scroll-container", @class]}
      style={"height: #{@container_height}"}
      {@rest}
    >
      <!-- 虚拟滚动内容将由JavaScript动态生成 -->
      <div class="virtual-scroll-loading text-center py-8">
        <div class="loading loading-spinner loading-lg text-primary mb-2"></div>
        <p class="text-sm text-gray-500">初始化虚拟滚动...</p>
      </div>
    </div>
    """
  end

  @doc """
  性能监控面板组件

  显示实时性能指标和优化建议

  ## 属性
  - `show_details` - 是否显示详细信息 (可选，默认: false)
  - `class` - 额外CSS类 (可选)

  ## 示例
      <.performance_monitor show_details={true} class="mb-6" />
  """
  attr :show_details, :boolean, default: false
  attr :class, :string, default: ""
  attr :rest, :global

  def performance_monitor(assigns) do
    ~H"""
    <div
      phx-hook="PerformanceMonitor"
      class={["performance-monitor card bg-base-100 shadow", @class]}
      {@rest}
    >
      <div class="card-body">
        <div class="flex items-center justify-between mb-4">
          <h3 class="card-title text-lg">
            <span class="text-primary">📊</span>
            性能监控
          </h3>
          <div class="badge badge-success">实时监控</div>
        </div>

        <!-- Core Web Vitals -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div class="stat bg-base-200 rounded-lg">
            <div class="stat-figure text-primary">
              <span class="text-2xl">⚡</span>
            </div>
            <div class="stat-title">LCP</div>
            <div class="stat-value text-sm" id="lcp-value">--</div>
            <div class="stat-desc">最大内容绘制</div>
          </div>

          <div class="stat bg-base-200 rounded-lg">
            <div class="stat-figure text-secondary">
              <span class="text-2xl">🎯</span>
            </div>
            <div class="stat-title">FID</div>
            <div class="stat-value text-sm" id="fid-value">--</div>
            <div class="stat-desc">首次输入延迟</div>
          </div>

          <div class="stat bg-base-200 rounded-lg">
            <div class="stat-figure text-accent">
              <span class="text-2xl">📐</span>
            </div>
            <div class="stat-title">CLS</div>
            <div class="stat-value text-sm" id="cls-value">--</div>
            <div class="stat-desc">累积布局偏移</div>
          </div>
        </div>

        <!-- 详细信息 -->
        <div :if={@show_details} class="space-y-4">
          <!-- 内存使用 -->
          <div class="alert alert-info">
            <span class="text-lg">🧠</span>
            <div>
              <h4 class="font-bold">内存使用情况</h4>
              <div class="text-sm">
                <span id="memory-usage">正在检测...</span>
              </div>
            </div>
          </div>

          <!-- 资源加载统计 -->
          <div class="alert alert-success">
            <span class="text-lg">📦</span>
            <div>
              <h4 class="font-bold">资源加载统计</h4>
              <div class="text-sm">
                <span id="resource-stats">正在统计...</span>
              </div>
            </div>
          </div>

          <!-- 优化建议 -->
          <div class="alert alert-warning">
            <span class="text-lg">💡</span>
            <div>
              <h4 class="font-bold">优化建议</h4>
              <div class="text-sm" id="optimization-suggestions">
                正在分析...
              </div>
            </div>
          </div>
        </div>

        <!-- 控制按钮 -->
        <div class="card-actions justify-end mt-4">
          <button class="btn btn-sm btn-ghost" onclick="window.performanceMonitor?.generateReport()">
            生成报告
          </button>
          <button class="btn btn-sm btn-primary" onclick="window.performanceMonitor?.clearMetrics()">
            清除数据
          </button>
        </div>
      </div>
    </div>
    """
  end

  @doc """
  资源优化状态组件

  显示资源优化状态和缓存信息

  ## 属性
  - `class` - 额外CSS类 (可选)

  ## 示例
      <.resource_optimizer_status class="mb-4" />
  """
  attr :class, :string, default: ""
  attr :rest, :global

  def resource_optimizer_status(assigns) do
    ~H"""
    <div
      phx-hook="ResourceOptimizer"
      class={["resource-optimizer-status card bg-base-100 shadow", @class]}
      {@rest}
    >
      <div class="card-body">
        <div class="flex items-center justify-between mb-4">
          <h3 class="card-title text-lg">
            <span class="text-success">⚡</span>
            资源优化状态
          </h3>
          <div class="badge badge-primary">已启用</div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- 预加载状态 -->
          <div class="stat bg-base-200 rounded-lg">
            <div class="stat-figure text-primary">
              <span class="text-xl">🚀</span>
            </div>
            <div class="stat-title">预加载资源</div>
            <div class="stat-value text-sm" id="preloaded-count">--</div>
            <div class="stat-desc">已预加载</div>
          </div>

          <!-- 缓存状态 -->
          <div class="stat bg-base-200 rounded-lg">
            <div class="stat-figure text-secondary">
              <span class="text-xl">💾</span>
            </div>
            <div class="stat-title">缓存大小</div>
            <div class="stat-value text-sm" id="cache-size">--</div>
            <div class="stat-desc">已缓存项目</div>
          </div>
        </div>

        <!-- Service Worker状态 -->
        <div class="mt-4">
          <div class="flex items-center justify-between">
            <span class="text-sm font-medium">Service Worker</span>
            <div class="badge badge-success" id="sw-status">检测中...</div>
          </div>
          <progress class="progress progress-primary w-full mt-2" id="sw-progress" value="0" max="100"></progress>
        </div>

        <!-- 控制按钮 -->
        <div class="card-actions justify-end mt-4">
          <button class="btn btn-sm btn-ghost" onclick="window.resourceOptimizer?.getResourceStats()">
            查看统计
          </button>
          <button class="btn btn-sm btn-warning" onclick="navigator.serviceWorker?.getRegistration().then(reg => reg?.unregister())">
            清除缓存
          </button>
        </div>
      </div>
    </div>
    """
  end

  @doc """
  性能优化图表组件

  显示性能指标的可视化图表

  ## 属性
  - `chart_type` - 图表类型 (可选，默认: "line")
  - `height` - 图表高度 (可选，默认: "300px")
  - `class` - 额外CSS类 (可选)

  ## 示例
      <.performance_chart chart_type="line" height="400px" class="mb-6" />
  """
  attr :chart_type, :string, default: "line"
  attr :height, :string, default: "300px"
  attr :class, :string, default: ""
  attr :rest, :global

  def performance_chart(assigns) do
    ~H"""
    <.lazy_container
      component_type="racing-chart"
      component_id="performance-chart"
      placeholder_height={@height}
      loading_text="加载性能图表..."
      class={["performance-chart", @class]}
      data-chart-type={@chart_type}
      {@rest}
    />
    """
  end

  @doc """
  大数据表格组件 (使用虚拟滚动)

  ## 属性
  - `data` - 表格数据 (必需)
  - `columns` - 列定义 (必需)
  - `row_height` - 行高 (可选，默认: 50)
  - `table_height` - 表格高度 (可选，默认: "500px")
  - `class` - 额外CSS类 (可选)

  ## 示例
      <.virtual_table 
        data={@large_dataset}
        columns={@table_columns}
        row_height={60}
        table_height="600px"
        class="border rounded-lg"
      />
  """
  attr :data, :list, required: true
  attr :columns, :list, required: true
  attr :row_height, :integer, default: 50
  attr :table_height, :string, default: "500px"
  attr :class, :string, default: ""
  attr :rest, :global

  def virtual_table(assigns) do
    ~H"""
    <div class={["virtual-table-wrapper", @class]} {@rest}>
      <!-- 表格头部 -->
      <div class="virtual-table-header bg-base-200 border-b">
        <div class="flex" style={"height: #{@row_height}px"}>
          <div
            :for={column <- @columns}
            class="flex-1 px-4 py-2 font-semibold flex items-center"
            style={if column[:width], do: "width: #{column.width}", else: ""}
          >
            <%= column.title || column.key %>
          </div>
        </div>
      </div>

      <!-- 虚拟滚动表格体 -->
      <.virtual_scroll_list
        items={@data}
        item_height={@row_height}
        container_height={@table_height}
        class="virtual-table-body"
      >
        <:render_item :let={row}>
          <div class="flex border-b border-base-200" style={"height: #{@row_height}px"}>
            <div
              :for={column <- @columns}
              class="flex-1 px-4 py-2 flex items-center"
              style={if column[:width], do: "width: #{column.width}", else: ""}
            >
              <%= if column[:render] do %>
                <%= column.render.(get_cell_value(row, column)) %>
              <% else %>
                <%= get_cell_value(row, column) %>
              <% end %>
            </div>
          </div>
        </:render_item>
      </.virtual_scroll_list>
    </div>
    """
  end

  # 私有函数：获取单元格值
  defp get_cell_value(row, column) do
    case column do
      %{data_index: data_index} when is_binary(data_index) ->
        get_nested_value(row, String.split(data_index, "."))
      %{key: key} ->
        Map.get(row, key, "")
      _ ->
        ""
    end
  end

  # 私有函数：获取嵌套值
  defp get_nested_value(data, [key]) do
    Map.get(data, String.to_atom(key), Map.get(data, key, ""))
  end

  defp get_nested_value(data, [key | rest]) do
    case Map.get(data, String.to_atom(key), Map.get(data, key)) do
      nil -> ""
      value when is_map(value) -> get_nested_value(value, rest)
      _ -> ""
    end
  end
end
