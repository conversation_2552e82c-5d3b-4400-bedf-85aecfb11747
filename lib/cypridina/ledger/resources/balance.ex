defmodule Cypridina.Ledger.Balance do
  @moduledoc """
  余额快照资源

  记录每次转账后的账户余额，支持：
  - 历史时点余额查询
  - 余额变化追踪
  - 审计和对账
  """

  use Ash.Resource,
    domain: Cypridina.Ledger,
    data_layer: AshPostgres.DataLayer,
    extensions: [AshDoubleEntry.Balance, AshAdmin.Resource],
    notifiers: [Ash.Notifier.PubSub]

  postgres do
    table "ledger_balances"
    repo Cypridina.Repo

    references do
      reference :transfer, on_delete: :delete
    end
  end

  admin do
    table_columns [:id, :account, :transfer, :balance]
  end

  # 添加 pub_sub 通知，当余额更新时通知相关组件
  pub_sub do
    module CypridinaWeb.Endpoint
    prefix "balance"

    # 发布余额更新事件，使用 publish 来发布自定义动作的事件
    # 这将发布到 "balance" 主题
    publish :upsert_balance, ["balance"]

    # 也发布标准的 create/update 事件
    publish_all :create, [[:account_id, nil]]
    publish_all :update, [[:account_id, nil]]
  end

  balance do
    # 配置相关资源
    transfer_resource Cypridina.Ledger.Transfer
    account_resource Cypridina.Ledger.Account
  end

  attributes do
    # ash_double_entry会自动添加：id, account_id, transfer_id
    # 我们需要手动添加其他字段
    attribute :balance, AshMoney.Types.Money do
      allow_nil? false
      description "账户余额"
    end

    attribute :inserted_at, :utc_datetime_usec do
      allow_nil? false
      default &DateTime.utc_now/0
      writable? false
      description "创建时间"
    end

    attribute :updated_at, :utc_datetime_usec do
      allow_nil? false
      default &DateTime.utc_now/0
      update_default &DateTime.utc_now/0
      description "更新时间"
    end
  end

  code_interface do
    define :read
    define :upsert_balance, action: :upsert_balance
  end

  actions do
    # ash_double_entry会自动添加read和upsert_balance actions

    # 自定义读取操作
    read :read do
      primary? true
      # 配置keyset分页用于流式处理
      pagination keyset?: true, required?: false
    end

    # 按账户查询余额历史
    read :by_account do
      argument :account_id, :uuid, allow_nil?: false
      filter account_id: arg(:account_id)
      pagination offset?: true, keyset?: true, countable: true
    end

    # 按用户查询余额历史
    read :by_user do
      argument :user_id, :string, allow_nil?: false

      prepare build(load: [:account])
    end

    # 获取指定时间点的余额
    read :balance_at_time do
      argument :account_id, :uuid, allow_nil?: false
      argument :timestamp, :utc_datetime_usec, allow_nil?: false

      filter account_id: arg(:account_id)

      get? true
    end

    # 获取最新余额
    read :latest_balance do
      argument :account_id, :uuid, allow_nil?: false
      filter account_id: arg(:account_id)
      get? true
    end
  end

  # 简化calculations，避免复杂的expr语法
  # 这些可以在应用层实现
end
