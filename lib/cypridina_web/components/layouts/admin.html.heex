<Backpex.HTML.Layout.app_shell fluid={@fluid?}>
  <:topbar>
    <Backpex.HTML.Layout.topbar_branding />
    <Backpex.HTML.Layout.theme_selector
      socket={@socket}
      class="mr-2"
      themes={[
        {"Light", "light"},
        {"Dark", "dark"},
        {"Cupcake", "cupcake"},
        {"Bumblebee", "bumblebee"},
        {"Emerald", "emerald"},
        {"Corporate", "corporate"},
        {"Synthwave", "synthwave"},
        {"Retro", "retro"},
        {"Cyberpunk", "cyberpunk"},
        {"Valentine", "valentine"},
        {"Halloween", "halloween"},
        {"Garden", "garden"},
        {"Forest", "forest"},
        {"Aqua", "aqua"},
        {"Lofi", "lofi"},
        {"Pastel", "pastel"},
        {"Fantasy", "fantasy"},
        {"Wireframe", "wireframe"},
        {"Black", "black"},
        {"Luxury", "luxury"},
        {"Dracula", "dracula"},
        {"CMYK", "cmyk"},
        {"Autumn", "autumn"},
        {"Business", "business"},
        {"Acid", "acid"},
        {"Lemonade", "lemonade"},
        {"Night", "night"},
        {"Coffee", "coffee"},
        {"Winter", "winter"},
        {"Dim", "dim"},
        {"Nord", "nord"},
        {"Sunset", "sunset"}
      ]}
    />
    <Backpex.HTML.Layout.topbar_dropdown class="mr-2 md:mr-0">
      <:label>
        <label tabindex="0" class="btn btn-square btn-ghost">
          <.icon name="hero-user" class="size-6" />
        </label>
      </:label>

      <li>
        <.link
          navigate={~p"/app/racing_game"}
          class="text-error flex justify-between hover:bg-base-200"
        >
          <p>返回</p>
          <.icon name="hero-arrow-right-on-rectangle" class="size-5" />
        </.link>
      </li>
    </Backpex.HTML.Layout.topbar_dropdown>
  </:topbar>

  <:sidebar>
    <!-- 个人信息 - 所有用户都可以访问 -->
    <Backpex.HTML.Layout.sidebar_item
      current_url={@current_url}
      navigate={~p"/admin_panel"}
    >
      <.icon name="hero-user" class="size-5" /> 个人信息
    </Backpex.HTML.Layout.sidebar_item>

    <!-- 管理员和代理功能 -->
    <%= if @current_user && (@current_user.permission_level >= 1 || CypridinaWeb.AuthHelper.has_permission?(@current_user, :agent)) do %>
      <!-- 用户管理 -->
      <Backpex.HTML.Layout.sidebar_item
        current_url={@current_url}
        navigate={~p"/backpex_admin/users"}
      >
        <.icon name="hero-users" class="size-5" /> 用户管理
      </Backpex.HTML.Layout.sidebar_item>

      <!-- 用户资产 - 仅管理员可见 -->
      <%= if @current_user.permission_level >= 1 do %>
        <Backpex.HTML.Layout.sidebar_item
          current_url={@current_url}
          navigate={~p"/backpex_admin/user_assets"}
        >
          <.icon name="hero-banknotes" class="size-5" /> 用户资产
        </Backpex.HTML.Layout.sidebar_item>
      <% end %>

      <!-- 代理系统 -->
      <Backpex.HTML.Layout.sidebar_item
        current_url={@current_url}
        navigate={~p"/backpex_admin/agent_relationships"}
      >
        <.icon name="hero-user-group" class="size-5" /> 抽水比例
      </Backpex.HTML.Layout.sidebar_item>



      <!-- 业务控制 - 仅管理员可见 -->
      <%= if @current_user.permission_level >= 1 do %>
        <Backpex.HTML.Layout.sidebar_item
          current_url={@current_url}
          navigate={~p"/race_control"}
        >
          <.icon name="hero-cog-6-tooth" class="size-5" /> 比赛控制
        </Backpex.HTML.Layout.sidebar_item>
      <% end %>
    <% end %>
  </:sidebar>
  <Backpex.HTML.Layout.flash_messages flash={@flash} /> {@inner_content}
</Backpex.HTML.Layout.app_shell>
