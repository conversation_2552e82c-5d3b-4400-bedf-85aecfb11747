defmodule RacingGame.Live.AdminPanel.Repositories.StockRepository do
  @moduledoc """
  股票持仓数据访问仓储
  
  提供股票持仓相关的数据访问抽象：
  - 股票持仓记录的查询和过滤
  - 用户持仓历史查询
  - 持仓统计和分析
  - 数据缓存管理
  """

  require Logger
  alias RacingGame.Stock
  alias RacingGame.Live.AdminPanel.Repositories.QueryBuilders.StockQueryBuilder
  alias Cypridina.Accounts.User
  require Ash.Query

  # 常量定义
  @cache_ttl 300  # 5分钟缓存
  @default_limit 20
  @max_limit 100

  # ============================================================================
  # 基础查询方法
  # ============================================================================

  @doc """
  根据ID获取股票持仓记录

  ## 参数
  - `stock_id` - 股票持仓ID
  - `options` - 选项

  ## 返回
  - `{:ok, stock}` - 成功
  - `{:error, :not_found}` - 持仓记录不存在
  - `{:error, reason}` - 其他错误
  """
  def get_by_id(stock_id, options \\ []) do
    Logger.debug("🔍 [股票仓储] 根据ID获取持仓记录: #{stock_id}")
    
    preload = Keyword.get(options, :preload, [:user])
    use_cache = Keyword.get(options, :use_cache, true)
    
    cache_key = build_cache_key("stock", stock_id, preload)
    
    if use_cache do
      case get_from_cache(cache_key) do
        {:ok, stock} -> {:ok, stock}
        :miss -> fetch_and_cache_stock_by_id(stock_id, preload, cache_key)
      end
    else
      fetch_stock_by_id(stock_id, preload)
    end
  end

  @doc """
  分页查询股票持仓记录

  ## 参数
  - `params` - 查询参数
    - `:page` - 页码
    - `:limit` - 每页数量
    - `:user_id` - 用户ID过滤
    - `:racer_id` - 赛车手ID过滤
    - `:min_quantity` - 最小持仓数量
    - `:date_from` - 开始日期
    - `:date_to` - 结束日期
    - `:sort` - 排序条件

  ## 返回
  - `{:ok, %{stocks: stocks, page_info: page_info}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_paginated(params \\ %{}) do
    Logger.debug("📋 [股票仓储] 分页查询持仓记录")
    
    try do
      normalized_params = normalize_list_params(params)
      
      with {:ok, query} <- StockQueryBuilder.build_list_query(normalized_params),
           {:ok, results} <- execute_paginated_query(query, normalized_params) do
        {:ok, results}
      else
        {:error, reason} -> {:error, reason}
      end
    rescue
      error ->
        Logger.error("❌ [股票仓储] 分页查询异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  获取用户股票持仓

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项
    - `:racer_id` - 赛车手ID过滤
    - `:min_quantity` - 最小持仓数量

  ## 返回
  - `{:ok, stocks}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_stocks(user_id, options \\ []) do
    Logger.debug("👤 [股票仓储] 获取用户持仓: #{user_id}")
    
    try do
      query = Stock
      |> Ash.Query.filter(user_id == ^user_id)
      |> apply_racer_filter(options[:racer_id])
      |> apply_quantity_filter(options[:min_quantity])
      |> Ash.Query.sort(updated_at: :desc)
      |> maybe_limit(options[:limit])
      
      case Ash.read(query) do
        {:ok, stocks} -> {:ok, stocks}
        {:error, error} ->
          Logger.error("❌ [股票仓储] 获取用户持仓失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [股票仓储] 获取用户持仓异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  获取赛车手的持仓分布

  ## 参数
  - `racer_id` - 赛车手ID
  - `options` - 选项

  ## 返回
  - `{:ok, holdings}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_racer_holdings(racer_id, options \\ []) do
    Logger.debug("🏎️ [股票仓储] 获取赛车手持仓分布: #{racer_id}")
    
    try do
      query = Stock
      |> Ash.Query.filter(racer_id == ^racer_id)
      |> Ash.Query.filter(quantity > 0)
      |> Ash.Query.load([:user])
      |> Ash.Query.sort(quantity: :desc)
      |> maybe_limit(options[:limit])
      
      case Ash.read(query) do
        {:ok, holdings} -> {:ok, holdings}
        {:error, error} ->
          Logger.error("❌ [股票仓储] 获取赛车手持仓失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [股票仓储] 获取赛车手持仓异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  获取用户特定赛车手的持仓

  ## 参数
  - `user_id` - 用户ID
  - `racer_id` - 赛车手ID

  ## 返回
  - `{:ok, stock}` - 成功
  - `{:error, :not_found}` - 持仓不存在
  - `{:error, reason}` - 其他错误
  """
  def get_user_racer_stock(user_id, racer_id) do
    Logger.debug("🎯 [股票仓储] 获取用户特定持仓: #{user_id} - #{racer_id}")
    
    try do
      case Stock
           |> Ash.Query.filter(user_id == ^user_id and racer_id == ^racer_id)
           |> Ash.read() do
        {:ok, [stock]} -> {:ok, stock}
        {:ok, []} -> {:error, :not_found}
        {:error, error} ->
          Logger.error("❌ [股票仓储] 获取特定持仓失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [股票仓储] 获取特定持仓异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # ============================================================================
  # 统计查询
  # ============================================================================

  @doc """
  获取股票持仓统计信息

  ## 参数
  - `params` - 查询参数
    - `:user_id` - 用户ID（可选）
    - `:racer_id` - 赛车手ID（可选）
    - `:date_from` - 开始日期
    - `:date_to` - 结束日期

  ## 返回
  - `{:ok, statistics}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_statistics(params \\ %{}) do
    Logger.debug("📊 [股票仓储] 获取持仓统计")
    
    try do
      base_query = build_statistics_base_query(params)
      
      stats = %{
        total_holdings: get_total_holdings(base_query),
        total_quantity: get_total_quantity(base_query),
        total_cost: get_total_cost(base_query),
        average_cost: get_average_cost(base_query),
        unique_users: get_unique_users(base_query),
        unique_racers: get_unique_racers(base_query),
        top_holdings: get_top_holdings(base_query),
        racer_distribution: get_racer_distribution(base_query),
        cost_distribution: get_cost_distribution(base_query)
      }
      
      {:ok, stats}
    rescue
      error ->
        Logger.error("❌ [股票仓储] 获取统计异常: #{inspect(error)}")
        {:error, :statistics_error}
    end
  end

  @doc """
  获取用户持仓汇总

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - `{:ok, summary}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_summary(user_id, options \\ []) do
    Logger.debug("📊 [股票仓储] 获取用户持仓汇总: #{user_id}")
    
    try do
      query = Stock |> Ash.Query.filter(user_id == ^user_id)
      
      summary = %{
        total_holdings: get_total_holdings(query),
        total_quantity: get_total_quantity(query),
        total_cost: get_total_cost(query),
        average_cost: get_average_cost(query),
        unique_racers: get_unique_racers(query),
        largest_holding: get_largest_holding(user_id),
        recent_activity: get_user_recent_activity(user_id, options[:days] || 7),
        portfolio_value: calculate_portfolio_value(user_id),
        profit_loss: calculate_profit_loss(user_id)
      }
      
      {:ok, summary}
    rescue
      error ->
        Logger.error("❌ [股票仓储] 获取用户汇总异常: #{inspect(error)}")
        {:error, :summary_error}
    end
  end

  # ============================================================================
  # 缓存管理
  # ============================================================================

  @doc """
  清除股票相关缓存

  ## 参数
  - `stock_id` - 股票持仓ID（可选）
  - `user_id` - 用户ID（可选）
  - `racer_id` - 赛车手ID（可选）
  """
  def clear_cache(stock_id \\ nil, user_id \\ nil, racer_id \\ nil) do
    cache_patterns = []
    
    if stock_id do
      cache_patterns = ["stock:#{stock_id}:*" | cache_patterns]
    end
    
    if user_id do
      cache_patterns = ["user_stocks:#{user_id}:*" | cache_patterns]
    end
    
    if racer_id do
      cache_patterns = ["racer_holdings:#{racer_id}:*" | cache_patterns]
    end
    
    if Enum.empty?(cache_patterns) do
      cache_patterns = ["stock:*", "user_stocks:*", "racer_holdings:*"]
    end
    
    Logger.debug("🧹 [股票仓储] 清除持仓缓存: #{inspect(cache_patterns)}")
    Enum.each(cache_patterns, &clear_cache_pattern/1)
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 标准化列表参数
  defp normalize_list_params(params) do
    %{
      page: max(1, params[:page] || 1),
      limit: min(params[:limit] || @default_limit, @max_limit),
      user_id: params[:user_id],
      racer_id: params[:racer_id],
      min_quantity: params[:min_quantity],
      date_from: params[:date_from],
      date_to: params[:date_to],
      sort: params[:sort] || [updated_at: :desc],
      preload: params[:preload] || [:user]
    }
  end

  # 获取并缓存股票记录
  defp fetch_and_cache_stock_by_id(stock_id, preload, cache_key) do
    case fetch_stock_by_id(stock_id, preload) do
      {:ok, stock} ->
        cache_put(cache_key, stock, @cache_ttl)
        {:ok, stock}
      error -> error
    end
  end

  # 获取股票记录
  defp fetch_stock_by_id(stock_id, preload) do
    try do
      case Stock
           |> maybe_preload(preload)
           |> Ash.read(stock_id) do
        {:ok, stock} -> {:ok, stock}
        {:error, %Ash.Error.Query.NotFound{}} -> {:error, :not_found}
        {:error, error} ->
          Logger.error("❌ [股票仓储] 获取持仓失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [股票仓储] 获取持仓异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # 执行分页查询
  defp execute_paginated_query(query, params) do
    offset = (params.page - 1) * params.limit
    
    case query
         |> maybe_preload(params.preload)
         |> Ash.Query.page(count: true, limit: params.limit, offset: offset)
         |> Ash.read() do
      {:ok, %Ash.Page.Offset{results: stocks, count: total_count}} ->
        page_info = %{
          total_count: total_count,
          page: params.page,
          limit: params.limit,
          total_pages: ceil(total_count / params.limit)
        }
        {:ok, %{stocks: stocks, page_info: page_info}}
      {:error, error} ->
        Logger.error("❌ [股票仓储] 分页查询失败: #{inspect(error)}")
        {:error, :query_failed}
    end
  end

  # 应用赛车手过滤
  defp apply_racer_filter(query, nil), do: query
  defp apply_racer_filter(query, racer_id) do
    Ash.Query.filter(query, racer_id == ^racer_id)
  end

  # 应用数量过滤
  defp apply_quantity_filter(query, nil), do: query
  defp apply_quantity_filter(query, min_quantity) when is_integer(min_quantity) do
    Ash.Query.filter(query, quantity >= ^min_quantity)
  end

  # 可能限制数量
  defp maybe_limit(query, nil), do: query
  defp maybe_limit(query, limit) when is_integer(limit) and limit > 0 do
    Ash.Query.limit(query, limit)
  end
  defp maybe_limit(query, _), do: query

  # 可能预加载关联数据
  defp maybe_preload(query, []), do: query
  defp maybe_preload(query, preload) when is_list(preload) do
    Ash.Query.load(query, preload)
  end

  # 构建统计基础查询
  defp build_statistics_base_query(params) do
    query = Stock
    
    query = if params[:user_id] do
      Ash.Query.filter(query, user_id == ^params[:user_id])
    else
      query
    end
    
    query = if params[:racer_id] do
      Ash.Query.filter(query, racer_id == ^params[:racer_id])
    else
      query
    end
    
    query = if params[:date_from] do
      Ash.Query.filter(query, updated_at >= ^params[:date_from])
    else
      query
    end
    
    if params[:date_to] do
      Ash.Query.filter(query, updated_at <= ^params[:date_to])
    else
      query
    end
  end

  # 统计函数（简化实现，实际需要使用聚合查询）
  defp get_total_holdings(query) do
    case query |> Ash.Query.aggregate(:count, :id) |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_total_quantity(query) do
    case query |> Ash.Query.aggregate(:sum, :quantity) |> Ash.read() do
      {:ok, [%{sum: sum}]} when is_number(sum) -> sum
      _ -> 0
    end
  end

  defp get_total_cost(query) do
    case query |> Ash.Query.aggregate(:sum, :total_cost) |> Ash.read() do
      {:ok, [%{sum: sum}]} when is_number(sum) -> sum
      _ -> Decimal.new(0)
    end
  end

  defp get_average_cost(query) do
    case query |> Ash.Query.aggregate(:avg, :total_cost) |> Ash.read() do
      {:ok, [%{avg: avg}]} when is_number(avg) -> avg
      _ -> Decimal.new(0)
    end
  end

  # 其他统计函数的简化实现
  defp get_unique_users(_query), do: 0
  defp get_unique_racers(_query), do: 0
  defp get_top_holdings(_query), do: []
  defp get_racer_distribution(_query), do: %{}
  defp get_cost_distribution(_query), do: %{}
  defp get_largest_holding(_user_id), do: nil
  defp get_user_recent_activity(_user_id, _days), do: []
  defp calculate_portfolio_value(_user_id), do: Decimal.new(0)
  defp calculate_profit_loss(_user_id), do: Decimal.new(0)

  # 构建缓存键
  defp build_cache_key(prefix, id, extra \\ nil) do
    case extra do
      nil -> "#{prefix}:#{id}"
      extra -> "#{prefix}:#{id}:#{:erlang.phash2(extra)}"
    end
  end

  # 缓存操作（需要实现具体的缓存后端）
  defp get_from_cache(_key), do: :miss
  defp cache_put(_key, _value, _ttl), do: :ok
  defp clear_cache_pattern(_pattern), do: :ok
end
