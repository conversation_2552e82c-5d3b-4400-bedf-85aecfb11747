/**
 * Racing Game Admin Panel - 动画引擎
 * 
 * 高性能动画系统，支持CSS和JavaScript动画的统一管理
 */

/**
 * 动画引擎核心类
 */
class AnimationEngine {
  constructor(options = {}) {
    this.options = {
      enableGPUAcceleration: true,
      defaultDuration: 300,
      defaultEasing: 'cubic-bezier(0.4, 0, 0.2, 1)',
      performanceMonitoring: true,
      maxConcurrentAnimations: 50,
      ...options
    };

    this.activeAnimations = new Map();
    this.animationQueue = [];
    this.performanceMetrics = {
      totalAnimations: 0,
      droppedFrames: 0,
      averageFPS: 60
    };

    this.init();
  }

  init() {
    this.setupPerformanceMonitoring();
    this.registerAnimationPresets();
    this.setupIntersectionObserver();
  }

  /**
   * 设置性能监控
   */
  setupPerformanceMonitoring() {
    if (!this.options.performanceMonitoring) return;

    let lastTime = performance.now();
    let frameCount = 0;

    const measurePerformance = (currentTime) => {
      frameCount++;
      
      if (currentTime - lastTime >= 1000) {
        this.performanceMetrics.averageFPS = Math.round((frameCount * 1000) / (currentTime - lastTime));
        
        if (this.performanceMetrics.averageFPS < 55) {
          console.warn(`⚠️ Animation performance warning: ${this.performanceMetrics.averageFPS}fps`);
        }
        
        frameCount = 0;
        lastTime = currentTime;
      }
      
      if (this.activeAnimations.size > 0) {
        requestAnimationFrame(measurePerformance);
      }
    };

    // 开始监控
    requestAnimationFrame(measurePerformance);
  }

  /**
   * 注册动画预设
   */
  registerAnimationPresets() {
    this.presets = {
      // 淡入淡出
      fadeIn: {
        keyframes: [
          { opacity: 0, transform: 'translateY(10px)' },
          { opacity: 1, transform: 'translateY(0)' }
        ],
        options: { duration: 300, easing: 'ease-out' }
      },
      fadeOut: {
        keyframes: [
          { opacity: 1, transform: 'translateY(0)' },
          { opacity: 0, transform: 'translateY(-10px)' }
        ],
        options: { duration: 300, easing: 'ease-in' }
      },

      // 滑动效果
      slideInLeft: {
        keyframes: [
          { transform: 'translateX(-100%)', opacity: 0 },
          { transform: 'translateX(0)', opacity: 1 }
        ],
        options: { duration: 400, easing: 'cubic-bezier(0.4, 0, 0.2, 1)' }
      },
      slideInRight: {
        keyframes: [
          { transform: 'translateX(100%)', opacity: 0 },
          { transform: 'translateX(0)', opacity: 1 }
        ],
        options: { duration: 400, easing: 'cubic-bezier(0.4, 0, 0.2, 1)' }
      },
      slideInUp: {
        keyframes: [
          { transform: 'translateY(100%)', opacity: 0 },
          { transform: 'translateY(0)', opacity: 1 }
        ],
        options: { duration: 400, easing: 'cubic-bezier(0.4, 0, 0.2, 1)' }
      },
      slideInDown: {
        keyframes: [
          { transform: 'translateY(-100%)', opacity: 0 },
          { transform: 'translateY(0)', opacity: 1 }
        ],
        options: { duration: 400, easing: 'cubic-bezier(0.4, 0, 0.2, 1)' }
      },

      // 缩放效果
      scaleIn: {
        keyframes: [
          { transform: 'scale(0.8)', opacity: 0 },
          { transform: 'scale(1)', opacity: 1 }
        ],
        options: { duration: 300, easing: 'cubic-bezier(0.34, 1.56, 0.64, 1)' }
      },
      scaleOut: {
        keyframes: [
          { transform: 'scale(1)', opacity: 1 },
          { transform: 'scale(0.8)', opacity: 0 }
        ],
        options: { duration: 300, easing: 'ease-in' }
      },

      // 弹跳效果
      bounce: {
        keyframes: [
          { transform: 'translateY(0)' },
          { transform: 'translateY(-10px)' },
          { transform: 'translateY(0)' },
          { transform: 'translateY(-5px)' },
          { transform: 'translateY(0)' }
        ],
        options: { duration: 600, easing: 'ease-out' }
      },

      // 摇摆效果
      shake: {
        keyframes: [
          { transform: 'translateX(0)' },
          { transform: 'translateX(-10px)' },
          { transform: 'translateX(10px)' },
          { transform: 'translateX(-10px)' },
          { transform: 'translateX(10px)' },
          { transform: 'translateX(0)' }
        ],
        options: { duration: 500, easing: 'ease-in-out' }
      },

      // 脉冲效果
      pulse: {
        keyframes: [
          { transform: 'scale(1)', opacity: 1 },
          { transform: 'scale(1.05)', opacity: 0.8 },
          { transform: 'scale(1)', opacity: 1 }
        ],
        options: { duration: 800, easing: 'ease-in-out', iterations: Infinity }
      },

      // 旋转效果
      rotateIn: {
        keyframes: [
          { transform: 'rotate(-180deg) scale(0.8)', opacity: 0 },
          { transform: 'rotate(0deg) scale(1)', opacity: 1 }
        ],
        options: { duration: 500, easing: 'cubic-bezier(0.4, 0, 0.2, 1)' }
      }
    };
  }

  /**
   * 设置交叉观察器用于滚动动画
   */
  setupIntersectionObserver() {
    if (!('IntersectionObserver' in window)) return;

    this.scrollObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const element = entry.target;
          const animationType = element.dataset.scrollAnimation;
          
          if (animationType && this.presets[animationType]) {
            this.animate(element, animationType);
            this.scrollObserver.unobserve(element);
          }
        }
      });
    }, {
      threshold: 0.1,
      rootMargin: '50px'
    });
  }

  /**
   * 执行动画
   */
  async animate(element, animationType, customOptions = {}) {
    if (!element || !animationType) {
      throw new Error('Element and animation type are required');
    }

    // 检查并发动画限制
    if (this.activeAnimations.size >= this.options.maxConcurrentAnimations) {
      console.warn('Maximum concurrent animations reached, queuing animation');
      return this.queueAnimation(element, animationType, customOptions);
    }

    const preset = this.presets[animationType];
    if (!preset) {
      throw new Error(`Unknown animation type: ${animationType}`);
    }

    // 准备元素
    this.prepareElement(element);

    // 合并选项
    const options = { ...preset.options, ...customOptions };
    
    // 创建动画
    const animation = element.animate(preset.keyframes, options);
    const animationId = this.generateAnimationId();
    
    // 记录活动动画
    this.activeAnimations.set(animationId, {
      element,
      animation,
      type: animationType,
      startTime: performance.now()
    });

    // 更新性能指标
    this.performanceMetrics.totalAnimations++;

    // 处理动画完成
    return new Promise((resolve, reject) => {
      animation.addEventListener('finish', () => {
        this.cleanupAnimation(animationId);
        resolve(animation);
      });

      animation.addEventListener('cancel', () => {
        this.cleanupAnimation(animationId);
        reject(new Error('Animation was cancelled'));
      });
    });
  }

  /**
   * 准备元素进行动画
   */
  prepareElement(element) {
    if (this.options.enableGPUAcceleration) {
      element.style.willChange = 'transform, opacity';
      element.style.transform = element.style.transform || 'translateZ(0)';
    }
  }

  /**
   * 清理动画
   */
  cleanupAnimation(animationId) {
    const animationData = this.activeAnimations.get(animationId);
    
    if (animationData) {
      const { element } = animationData;
      
      // 清理GPU加速属性
      if (this.options.enableGPUAcceleration) {
        element.style.willChange = 'auto';
      }
      
      this.activeAnimations.delete(animationId);
      
      // 处理队列中的动画
      this.processAnimationQueue();
    }
  }

  /**
   * 队列动画
   */
  queueAnimation(element, animationType, customOptions) {
    return new Promise((resolve, reject) => {
      this.animationQueue.push({
        element,
        animationType,
        customOptions,
        resolve,
        reject
      });
    });
  }

  /**
   * 处理动画队列
   */
  processAnimationQueue() {
    if (this.animationQueue.length === 0) return;
    if (this.activeAnimations.size >= this.options.maxConcurrentAnimations) return;

    const queuedAnimation = this.animationQueue.shift();
    
    this.animate(
      queuedAnimation.element,
      queuedAnimation.animationType,
      queuedAnimation.customOptions
    ).then(queuedAnimation.resolve).catch(queuedAnimation.reject);
  }

  /**
   * 生成动画ID
   */
  generateAnimationId() {
    return `anim_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 停止元素的所有动画
   */
  stopAnimations(element) {
    for (const [id, animationData] of this.activeAnimations) {
      if (animationData.element === element) {
        animationData.animation.cancel();
        this.cleanupAnimation(id);
      }
    }
  }

  /**
   * 暂停所有动画
   */
  pauseAllAnimations() {
    for (const [, animationData] of this.activeAnimations) {
      animationData.animation.pause();
    }
  }

  /**
   * 恢复所有动画
   */
  resumeAllAnimations() {
    for (const [, animationData] of this.activeAnimations) {
      animationData.animation.play();
    }
  }

  /**
   * 观察滚动动画
   */
  observeScrollAnimation(element) {
    if (this.scrollObserver) {
      this.scrollObserver.observe(element);
    }
  }

  /**
   * 创建自定义动画
   */
  createCustomAnimation(name, keyframes, options) {
    this.presets[name] = { keyframes, options };
  }

  /**
   * 获取性能指标
   */
  getPerformanceMetrics() {
    return {
      ...this.performanceMetrics,
      activeAnimations: this.activeAnimations.size,
      queuedAnimations: this.animationQueue.length
    };
  }

  /**
   * 销毁动画引擎
   */
  destroy() {
    // 停止所有动画
    for (const [id] of this.activeAnimations) {
      const animationData = this.activeAnimations.get(id);
      animationData.animation.cancel();
    }
    
    this.activeAnimations.clear();
    this.animationQueue = [];
    
    if (this.scrollObserver) {
      this.scrollObserver.disconnect();
    }
  }
}

/**
 * 页面过渡管理器
 */
class PageTransitionManager {
  constructor(animationEngine) {
    this.animationEngine = animationEngine;
    this.transitionInProgress = false;
  }

  /**
   * 执行页面过渡
   */
  async transition(fromElement, toElement, transitionType = 'fade') {
    if (this.transitionInProgress) return;
    
    this.transitionInProgress = true;
    
    try {
      // 准备新页面
      toElement.style.display = 'block';
      
      // 执行过渡动画
      await Promise.all([
        this.animationEngine.animate(fromElement, `${transitionType}Out`),
        this.animationEngine.animate(toElement, `${transitionType}In`)
      ]);
      
      // 隐藏旧页面
      fromElement.style.display = 'none';
      
    } finally {
      this.transitionInProgress = false;
    }
  }

  /**
   * 模态框过渡
   */
  async modalTransition(modalElement, action = 'show') {
    const backdrop = modalElement.querySelector('.modal-backdrop');
    const content = modalElement.querySelector('.modal-content');
    
    if (action === 'show') {
      modalElement.style.display = 'flex';
      
      await Promise.all([
        this.animationEngine.animate(backdrop, 'fadeIn'),
        this.animationEngine.animate(content, 'scaleIn')
      ]);
    } else {
      await Promise.all([
        this.animationEngine.animate(backdrop, 'fadeOut'),
        this.animationEngine.animate(content, 'scaleOut')
      ]);
      
      modalElement.style.display = 'none';
    }
  }
}

// 全局实例
window.animationEngine = new AnimationEngine();
window.pageTransitionManager = new PageTransitionManager(window.animationEngine);

// Phoenix LiveView Hook
const AnimationEngineHook = {
  mounted() {
    // 观察滚动动画元素
    const scrollAnimElements = this.el.querySelectorAll('[data-scroll-animation]');
    scrollAnimElements.forEach(element => {
      window.animationEngine.observeScrollAnimation(element);
    });
    
    // 自动执行入场动画
    const autoAnimElements = this.el.querySelectorAll('[data-auto-animation]');
    autoAnimElements.forEach(element => {
      const animationType = element.dataset.autoAnimation;
      const delay = parseInt(element.dataset.animationDelay) || 0;
      
      setTimeout(() => {
        window.animationEngine.animate(element, animationType);
      }, delay);
    });
  },

  updated() {
    // 处理新添加的动画元素
    const newScrollAnimElements = this.el.querySelectorAll('[data-scroll-animation]:not([data-observed])');
    newScrollAnimElements.forEach(element => {
      element.dataset.observed = 'true';
      window.animationEngine.observeScrollAnimation(element);
    });
  },

  destroyed() {
    // 停止元素相关的动画
    window.animationEngine.stopAnimations(this.el);
  }
};

// 导出
export { AnimationEngine, PageTransitionManager, AnimationEngineHook };
