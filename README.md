# Cypridina

To start your Phoenix server:

* Run `mix setup` to install and setup dependencies
* Start Phoenix endpoint with `mix phx.server` or inside IEx with `iex -S mix phx.server`

Now you can visit [`localhost:4000`](http://localhost:4000) from your browser.

Ready to run in production? Please [check our deployment guides](https://hexdocs.pm/phoenix/deployment.html).

## Learn more

* Official website: https://www.phoenixframework.org/
* Guides: https://hexdocs.pm/phoenix/overview.html
* Docs: https://hexdocs.pm/phoenix
* Forum: https://elixirforum.com/c/phoenix-forum
* Source: https://github.com/phoenixframework/phoenix


<!-- 参考https://hexdocs.pm/phoenix/up_and_running.html -->

<!-- 在本地安装hex包管理器 -->
###=# 一些常用的mix命令
HEX_MIRROR=http://hexpm.upyun.com mix local.hex
<!-- 在本地安装erlang用的工具rebar -->
HEX_MIRROR=http://hexpm.upyun.com mix local.rebar
 <!--安装依赖  -->
HEX_MIRROR=http://hexpm.upyun.com mix deps.get


#### 初始化项目
ash mix任务见https://hexdocs.pm/ash/Mix.Tasks.Ash.Codegen.html

```bash
<!-- 强制清库 -->
mix ecto.drop  --force-drop
<!-- 根据ash的资源声明，生成迁移文件 -->
mix ash.codegen <xxxx-随便起个名>
<!-- 初始化项目 -->
mix setup
```

#### 启动服务
```bash
<!-- 赋名式启动 -->
MIX_ENV=dev elixir --sname dev_server -S mix phx.server
<!-- 连接到上述节点 -->
iex --remsh dev_server --sname dev

<!-- 正式服 -->
MIX_ENV=prod PORT=4001 elixir --sname prod_server -S mix phx.server
iex --remsh prod_server --sname dev
```