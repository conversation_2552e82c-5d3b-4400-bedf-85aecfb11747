# 公共表单组件使用指南

## 概述

`Cypridina.Utils.FormModalComponent` 是一个通用的表单模态框组件，提供统一的表单界面，支持动态字段配置、多种表单类型、自定义样式主题等功能。

`Cypridina.Utils.FormBuilder` 是配套的表单构建器工具，提供便捷的方法来构建表单配置。

## 主要特性

- 🎨 **多主题支持**: 支持 blue、green、purple、red、gray 等主题
- 📝 **动态字段**: 支持文本、文本域、下拉选择、复选框、隐藏字段等
- 🔧 **灵活配置**: 可自定义标题、图标、样式等
- ⚡ **事件处理**: 统一的事件处理机制
- 📱 **响应式设计**: 适配不同屏幕尺寸

## 快速开始

### 1. 基本使用

```elixir
# 在你的 LiveView 组件中
defmodule MyApp.MyComponent do
  use CypridinaWeb, :live_component
  
  alias Cypridina.Utils.{FormModalComponent, FormBuilder}

  def handle_event("show_form", _params, socket) do
    # 构建表单配置
    form_config = FormBuilder.new_form(%{
      title: "创建用户",
      subtitle: "请填写用户信息",
      icon: "fas fa-user-plus",
      theme: "blue"
    })
    |> FormBuilder.add_field_group("基本信息", icon: "fas fa-info-circle")
    |> FormBuilder.add_text_field("user[name]", "用户名", 
         required: true, placeholder: "请输入用户名")
    |> FormBuilder.add_text_field("user[email]", "邮箱",
         required: true, input_type: "email")

    socket = assign(socket, :form_config, form_config)
    {:noreply, socket}
  end

  # 处理表单模态框事件
  def handle_info({:form_modal_event, :submit_form, params}, socket) do
    # 处理表单提交
    IO.inspect(params, label: "Form Data")
    {:noreply, socket}
  end

  def handle_info({:form_modal_event, :hide_modal, _params}, socket) do
    # 处理模态框关闭
    socket = assign(socket, :form_config, nil)
    {:noreply, socket}
  end

  def render(assigns) do
    ~H"""
    <div>
      <button phx-click="show_form" phx-target={@myself}>
        显示表单
      </button>
      
      <%= if @form_config do %>
        <.live_component
          module={FormModalComponent}
          id="my_form_modal"
          config={@form_config.config}
          form_fields={@form_config.form_fields}
        />
      <% end %>
    </div>
    """
  end
end
```

### 2. 使用预定义的通信表单

```elixir
# 创建系统消息表单
form_config = FormBuilder.build_communication_form("message", %{}, false)

# 编辑现有通信记录
form_data = %{
  title: "现有标题",
  content: "现有内容",
  priority: "high",
  active: true,
  recipient: "user123"
}
form_config = FormBuilder.build_communication_form("message", form_data, true)
```

## 字段类型

### 文本输入框
```elixir
FormBuilder.add_text_field(form, "field_name", "字段标签",
  icon: "fas fa-user",
  placeholder: "请输入...",
  required: true,
  maxlength: 100,
  help_text: "帮助文本"
)
```

### 文本域
```elixir
FormBuilder.add_textarea_field(form, "field_name", "字段标签",
  rows: 6,
  maxlength: 2000,
  placeholder: "请输入详细内容..."
)
```

### 下拉选择
```elixir
options = [
  %{value: "option1", label: "选项1"},
  %{value: "option2", label: "选项2"}
]
FormBuilder.add_select_field(form, "field_name", "字段标签", options,
  value: "option1"
)
```

### 复选框
```elixir
FormBuilder.add_checkbox_field(form, "field_name", "字段标签",
  checked: true,
  help_text: "勾选以启用此选项"
)
```

### 隐藏字段
```elixir
FormBuilder.add_hidden_field(form, "field_name", "field_value")
```

## 主题配置

支持的主题：
- `blue` - 蓝色主题（默认）
- `green` - 绿色主题
- `purple` - 紫色主题
- `red` - 红色主题
- `gray` - 灰色主题

## 字段组配置

```elixir
# 添加字段组
FormBuilder.add_field_group(form, "字段组标题",
  icon: "fas fa-info-circle",
  grid: "grid-cols-1 md:grid-cols-2"  # 网格布局
)
```

## 事件处理

组件会发送以下事件到父组件：

- `{:form_modal_event, :hide_modal, params}` - 模态框关闭
- `{:form_modal_event, :submit_form, params}` - 表单提交

在父组件中使用 `handle_info/2` 来处理这些事件。

## 完整配置选项

```elixir
config = %{
  title: "表单标题",
  subtitle: "表单副标题", 
  icon: "fas fa-plus",
  theme: "blue",
  max_width: "2xl",  # Tailwind 宽度类
  close_after_save: true,
  show_close_after_save_option: true,
  submit_text: "保存"
}
```

## 注意事项

1. 确保在父组件中正确处理 `handle_info` 事件
2. 表单字段的 `name` 属性应该遵循 Phoenix 的参数命名约定
3. 使用 `FormBuilder` 可以简化表单配置的创建
4. 组件会自动处理表单验证和提交逻辑
