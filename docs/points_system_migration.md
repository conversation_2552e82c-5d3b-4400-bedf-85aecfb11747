# 积分系统迁移指南

## 概述

本文档详细说明如何将现有的积分系统迁移到新的统一Ledger复式记账系统。新系统提供了更好的数据一致性、审计追踪和扩展性。

## 系统架构

### 新架构组件

1. **Cypridina.PointsLedgerBridge** - 统一的积分操作入口
2. **Cypridina.Ledger** - 复式记账系统核心
3. **Cypridina.PointsMigrationHelper** - 迁移助手工具
4. **Mix.Tasks.PointsMigration** - 命令行迁移工具

### 数据流

```
应用代码 → PointsLedgerBridge → Ledger系统 → 数据库
                ↓
        (可选) PointsTransaction (兼容性)
```

## 迁移步骤

### 第一阶段：准备工作

1. **检查当前状态**
   ```bash
   mix points_migration.status
   ```

2. **验证数据一致性**
   ```bash
   mix points_migration.verify
   ```

3. **生成迁移建议**
   ```bash
   mix points_migration.suggestions
   ```

### 第二阶段：代码迁移

#### 2.1 更新积分操作调用

**旧代码：**
```elixir
# 增加积分
Cypridina.Accounts.add_points(user_id, amount, opts)

# 扣除积分
Cypridina.Accounts.subtract_points(user_id, amount, opts)

# 转账
Cypridina.Accounts.transfer_points(from_id, to_id, amount, reason)
```

**新代码：**
```elixir
# 增加积分
Cypridina.PointsLedgerBridge.add_points(user_id, amount, [
  transaction_type: :game_win,
  description: "游戏获奖",
  metadata: %{game_type: "racing", race_id: race_id}
])

# 扣除积分
Cypridina.PointsLedgerBridge.subtract_points(user_id, amount, [
  transaction_type: :game_bet,
  description: "游戏下注",
  metadata: %{game_type: "racing", bet_id: bet_id}
])

# 转账
Cypridina.PointsLedgerBridge.transfer_points(from_id, to_id, amount, reason, [
  metadata: %{transfer_type: "user_to_user"}
])
```

#### 2.2 更新元数据结构

新系统使用结构化的元数据：

```elixir
# 推荐的元数据结构
metadata: %{
  # 业务类型标识
  business_type: "racing_game",
  
  # 具体操作信息
  operation_details: %{
    bet_id: bet_id,
    race_id: race_id,
    animal_id: animal_id
  },
  
  # 抽水信息（如果适用）
  commission_info: %{
    rate: commission_rate,
    amount: commission_amount,
    agent_id: agent_id
  }
}
```

### 第三阶段：数据迁移

1. **预览迁移**
   ```bash
   mix points_migration.migrate --dry-run
   ```

2. **分批迁移**
   ```bash
   # 迁移100条记录
   mix points_migration.migrate --limit=100
   
   # 迁移所有记录
   mix points_migration.migrate --all
   ```

3. **验证迁移结果**
   ```bash
   mix points_migration.verify
   mix points_migration.status
   ```

### 第四阶段：清理和优化

1. **移除旧代码**
   - 逐步移除对旧系统的直接调用
   - 保留必要的兼容性接口

2. **性能优化**
   - 监控新系统的性能表现
   - 根据需要调整配置参数

## 配置说明

### 主要配置项

```elixir
# config/points_system.exs
config :cypridina, :points_system,
  # 是否启用新的Ledger系统
  enable_ledger_system: true,
  
  # 是否保持与旧系统的兼容性
  legacy_compatibility: true,
  
  # 是否自动创建旧系统的兼容记录
  create_legacy_records: false
```

### 环境特定配置

- **开发环境**: 启用详细日志和兼容性记录
- **测试环境**: 禁用日志以提高性能
- **生产环境**: 保守设置，启用监控和验证

## API 参考

### PointsLedgerBridge 主要方法

#### add_points/3
```elixir
@spec add_points(user_id :: String.t(), amount :: integer(), opts :: keyword()) ::
  {:ok, result} | {:error, reason}
```

#### subtract_points/3
```elixir
@spec subtract_points(user_id :: String.t(), amount :: integer(), opts :: keyword()) ::
  {:ok, result} | {:error, reason}
```

#### transfer_points/5
```elixir
@spec transfer_points(from_id :: String.t(), to_id :: String.t(), 
                     amount :: integer(), reason :: String.t(), opts :: keyword()) ::
  {:ok, result} | {:error, reason}
```

#### batch_operations/1
```elixir
@spec batch_operations(operations :: list()) :: {:ok, results} | {:error, reason}
```

### 选项参数

- `transaction_type`: 交易类型（原子）
- `description`: 交易描述（字符串）
- `metadata`: 元数据（映射）
- `race_issue`: 期号（字符串，可选）
- `business_id`: 业务ID（字符串，可选）
- `create_legacy_record`: 是否创建旧系统记录（布尔值，默认false）

## 监控和维护

### 日常监控

1. **检查系统状态**
   ```bash
   mix points_migration.status
   ```

2. **验证数据一致性**
   ```bash
   # 每日验证
   mix points_migration.verify
   ```

3. **查看日志**
   ```bash
   tail -f logs/system.log | grep "积分"
   ```

### 故障排除

#### 常见问题

1. **积分不一致**
   - 运行一致性验证
   - 检查是否有未迁移的记录
   - 查看错误日志

2. **性能问题**
   - 检查数据库索引
   - 调整批处理大小
   - 监控内存使用

3. **迁移失败**
   - 检查数据完整性
   - 验证用户账户存在性
   - 查看详细错误信息

## 最佳实践

### 代码编写

1. **使用统一接口**
   - 所有积分操作都通过PointsLedgerBridge
   - 避免直接调用底层系统

2. **提供详细元数据**
   - 记录足够的业务上下文
   - 便于后续审计和分析

3. **错误处理**
   - 妥善处理积分操作失败
   - 提供用户友好的错误信息

### 部署建议

1. **分阶段部署**
   - 先在开发环境完整测试
   - 生产环境分批迁移

2. **备份策略**
   - 迁移前完整备份数据库
   - 保留回滚方案

3. **监控告警**
   - 设置积分不一致告警
   - 监控系统性能指标

## 总结

通过本迁移指南，您可以安全地将现有积分系统迁移到新的统一Ledger系统。新系统提供了更好的数据一致性、审计能力和扩展性，为未来的业务发展奠定了坚实基础。

如有问题，请参考故障排除部分或联系开发团队。
