defmodule Cypridina.Utils.StringUtils do
  @moduledoc """
  提供字符串处理相关的工具函数
  """

  @doc """
  将字符串转换为下划线格式

  ## 示例

      iex> Cypridina.Utils.StringUtils.to_underscore("HelloWorld")
      "hello_world"
  """
  def to_underscore(str) when is_binary(str) do
    str
    |> String.replace(~r/([A-Z])/, "_\\1")
    |> String.downcase()
    |> String.trim_leading("_")
  end

  @doc """
  将字符串转换为驼峰格式

  ## 示例

      iex> Cypridina.Utils.StringUtils.to_camel_case("hello_world")
      "helloWorld"
  """
  def to_camel_case(str) when is_binary(str) do
    parts = String.split(str, "_")
    first = List.first(parts)
    rest = Enum.map(tl(parts), &String.capitalize/1)

    Enum.join([first | rest], "")
  end

  @doc """
  将字符串截断到指定长度，并添加省略号

  ## 示例

      iex> Cypridina.Utils.StringUtils.truncate("这是一个很长的字符串", 5)
      "这是一个..."
  """
  def truncate(str, max_length, ellipsis \\ "...") when is_binary(str) do
    if String.length(str) > max_length do
      "#{String.slice(str, 0, max_length)}#{ellipsis}"
    else
      str
    end
  end
end
