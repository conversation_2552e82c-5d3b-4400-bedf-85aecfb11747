defmodule Teen.ActivitySystem.LevelReward do
  @moduledoc """
  等级奖励资源

  管理等级奖励配置
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  postgres do
    table "level_rewards"
    repo Cy<PERSON><PERSON><PERSON>.Repo
  end

  admin do
    table_columns [:id, :reward_name, :target_level, :reward_type, :reward_amount, :status]
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_rewards
    define :list_by_level
    define :list_by_type
  end

  actions do
    defaults [:create, :read, :update, :destroy]

    read :list_active_rewards do
      filter expr(status == 1)
    end

    read :list_by_level do
      argument :target_level, :integer, allow_nil?: false
      filter expr(target_level == ^arg(:target_level) and status == 1)
    end

    read :list_by_type do
      argument :reward_type, :integer, allow_nil?: false
      filter expr(reward_type == ^arg(:reward_type) and status == 1)
    end

    update :enable do
      change set_attribute(:status, 1)
    end

    update :disable do
      change set_attribute(:status, 0)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :reward_name, :string do
      allow_nil? false
      public? true
      description "奖励名称"
      constraints max_length: 100
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "奖励描述"
      constraints max_length: 1000
    end

    attribute :target_level, :integer do
      allow_nil? false
      public? true
      description "目标等级"
      constraints min: 1
    end

    attribute :reward_type, :integer do
      allow_nil? false
      public? true
      description "奖励类型：1-金币，2-积分，3-道具，4-特权，5-经验"
      default 1
      constraints min: 1, max: 5
    end

    attribute :reward_amount, :decimal do
      allow_nil? false
      public? true
      description "奖励数量"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :reward_items, {:array, :map} do
      allow_nil? true
      public? true
      description "奖励道具配置"
      default []
    end

    attribute :is_one_time, :boolean do
      allow_nil? false
      public? true
      description "是否一次性奖励"
      default true
    end

    attribute :is_cumulative, :boolean do
      allow_nil? false
      public? true
      description "是否累积奖励"
      default false
    end

    attribute :vip_bonus_rate, :decimal do
      allow_nil? false
      public? true
      description "VIP奖励加成比例（%）"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :conditions, :map do
      allow_nil? true
      public? true
      description "领取条件配置"
    end

    attribute :auto_claim, :boolean do
      allow_nil? false
      public? true
      description "是否自动领取"
      default false
    end

    attribute :claim_limit, :integer do
      allow_nil? false
      public? true
      description "领取次数限制（0为无限制）"
      default 1
      constraints min: 0
    end

    attribute :claimed_count, :integer do
      allow_nil? false
      public? true
      description "已领取次数"
      default 0
      constraints min: 0
    end

    attribute :valid_days, :integer do
      allow_nil? true
      public? true
      description "有效天数（从达到等级开始计算）"
      constraints min: 1
    end

    attribute :status, :integer do
      allow_nil? false
      public? true
      description "状态：0-禁用，1-启用"
      default 1
      constraints min: 0, max: 1
    end

    attribute :priority, :integer do
      allow_nil? false
      public? true
      description "优先级（数字越大优先级越高）"
      default 0
      constraints min: 0
    end

    timestamps()
  end

  relationships do
    belongs_to :task_level, Teen.ActivitySystem.TaskLevel do
      public? true
      source_attribute :target_level
      destination_attribute :level
    end
  end

  identities do
    identity :unique_reward_name, [:reward_name]
  end
end
