defmodule RacingGame.Live.AdminPanel.SystemMaintenanceComponent do
  @moduledoc """
  系统维护管理组件 - 仅限管理员使用
  """
  use CypridinaWeb, :live_component

  alias CypridinaWeb.AuthHelper
  alias Cypridina.Accounts.User
  alias CypridinaWeb.Components.AdminButtonGroup
  require Ash.Query
  require Logger

  def update(assigns, socket) do
    socket = assign(socket, assigns)

    if AuthHelper.has_permission?(socket.assigns.current_user, :admin) do
      socket =
        socket
        |> assign_defaults()
        |> load_maintenance_status()

      {:ok, socket}
    else
      # 非管理员用户显示无权限信息
      socket =
        socket
        |> assign_defaults()
        |> assign(:unauthorized, true)
      {:ok, socket}
    end
  end

  defp assign_defaults(socket) do
    socket
    |> assign(:maintenance_mode, false)
    |> assign(:maintenance_start, nil)
    |> assign(:maintenance_end, nil)
    |> assign(:maintenance_message, "系统正在进行维护，预计30分钟后恢复正常，给您带来的不便敬请谅解。")
    |> assign(:show_confirm_modal, false)
    |> assign(:backup_status, "idle")
    |> assign(:restore_status, "idle")
    |> assign(:backup_progress, 0)
    |> assign(:backup_log, "")
    |> assign(:selected_backup, "latest")
    |> assign(:restore_confirm_modal, false)
    |> assign(:unauthorized, false)
  end

  defp load_maintenance_status(socket) do
    # 模拟维护状态（实际项目中应该从数据库或配置文件获取）
    socket
    |> assign(:maintenance_mode, false)
    |> assign(:maintenance_start, nil)
    |> assign(:maintenance_end, nil)
  end

  def handle_event("toggle_maintenance", %{"maintenance_mode" => mode}, socket) do
    new_mode = mode == "true"
    user = socket.assigns.current_user
    Logger.info("用户 #{user.id} #{if new_mode, do: "启用", else: "禁用"} 了系统维护模式")

    # 模拟设置维护模式（实际项目中应该更新数据库或配置文件）
    socket =
      socket
      |> assign(:maintenance_mode, new_mode)
      |> put_flash(:info, if(new_mode, do: "维护模式已启用", else: "维护模式已禁用"))
    {:noreply, socket}
  end

  def handle_event("set_maintenance_schedule", %{"start" => start, "end" => end_date, "message" => message}, socket) do
    # 解析日期时间字符串
    with {:ok, start_time} <- parse_datetime_local(start),
         {:ok, end_time} <- parse_datetime_local(end_date),
         true <- end_time > start_time,
         true <- start_time > DateTime.utc_now() do

      user = socket.assigns.current_user
      Logger.info("用户 #{user.id} 设置系统维护计划: #{Calendar.strftime(start_time, "%Y-%m-%d %H:%M")} 至 #{Calendar.strftime(end_time, "%Y-%m-%d %H:%M")}")

      socket =
        socket
        |> assign(:maintenance_start, start_time)
        |> assign(:maintenance_end, end_time)
        |> assign(:maintenance_message, message)
        |> put_flash(:info, "维护计划设置成功")
      {:noreply, socket}
    else
      _ ->
        socket = put_flash(socket, :error, "无效的时间设置，请确保结束时间晚于开始时间且开始时间在当前时间之后")
        {:noreply, socket}
    end
  end

  # 解析本地时间字符串为UTC DateTime
  defp parse_datetime_local(datetime_string) do
    case NaiveDateTime.from_iso8601(datetime_string <> ":00") do
      {:ok, naive_datetime} ->
        # 假设本地时间为UTC（实际项目中应该考虑时区）
        {:ok, DateTime.from_naive!(naive_datetime, "Etc/UTC")}
      {:error, _} ->
        {:error, :invalid_datetime}
    end
  end

  # 格式化DateTime为datetime-local输入框格式
  defp format_datetime_local(nil), do: ""
  defp format_datetime_local(datetime) do
    datetime
    |> DateTime.to_iso8601()
    |> String.replace("Z", "")
    |> String.slice(0, 16)  # 只取到分钟，去掉秒和毫秒
  end

  def handle_event("confirm_maintenance", _params, socket) do
    # 模拟执行维护
    socket =
      socket
      |> assign(:show_confirm_modal, false)
      |> assign(:maintenance_mode, true)
      |> load_maintenance_status()
      |> put_flash(:info, "维护已开始执行")
    {:noreply, socket}
  end

  def handle_event("cancel_maintenance", _params, socket) do
    # 模拟取消维护
    socket =
      socket
      |> assign(:show_confirm_modal, false)
      |> assign(:maintenance_start, nil)
      |> assign(:maintenance_end, nil)
      |> load_maintenance_status()
      |> put_flash(:info, "维护计划已取消")
    {:noreply, socket}
  end

  def handle_event("update_message", %{"value" => message}, socket) do
    socket = assign(socket, :maintenance_message, message)
    {:noreply, socket}
  end

  def handle_event("perform_backup", _params, socket) do
    user = socket.assigns.current_user
    Logger.info("用户 #{user.id} 开始创建系统备份")

    Task.start(fn ->
      # 模拟备份过程
      steps = [
        "正在准备备份环境...",
        "正在备份数据库...",
        "正在备份文件系统...",
        "正在压缩备份文件...",
        "正在验证备份完整性..."
      ]

      Enum.with_index(steps, 1)
      |> Enum.each(fn {step, index} ->
        progress = div(index * 100, length(steps))

        send_update(__MODULE__,
          id: socket.assigns.id,
          backup_progress: progress,
          backup_log: socket.assigns.backup_log <> "#{step}\n"
        )

        # 模拟处理时间
        Process.sleep(1000)
      end)

      # 备份完成
      send_update(__MODULE__,
        id: socket.assigns.id,
        backup_status: "completed",
        backup_progress: 100,
        backup_log: socket.assigns.backup_log <> "备份完成！\n"
      )
    end)

    socket =
      socket
      |> assign(:backup_status, "in_progress")
      |> assign(:backup_progress, 0)
      |> assign(:backup_log, "开始系统备份...\n")
    {:noreply, socket}
  end

  def handle_event("select_backup", %{"backup_id" => backup_id}, socket) do
    {:noreply, assign(socket, :selected_backup, backup_id)}
  end

  def handle_event("show_restore_confirm", _params, socket) do
    {:noreply, assign(socket, :restore_confirm_modal, true)}
  end

  def handle_event("cancel_restore", _params, socket) do
    {:noreply, assign(socket, :restore_confirm_modal, false)}
  end

  def handle_event("confirm_restore", _params, socket) do
    backup_id = socket.assigns.selected_backup
    user = socket.assigns.current_user
    Logger.info("用户 #{user.id} 开始恢复系统备份: #{backup_id}")

    Task.start(fn ->
      # 模拟恢复过程
      Process.sleep(3000)
      send_update(__MODULE__, id: socket.assigns.id, restore_status: "completed")
      Logger.info("用户 #{user.id} 系统备份恢复完成: #{backup_id}")
    end)

    socket =
      socket
      |> assign(:restore_confirm_modal, false)
      |> assign(:restore_status, "in_progress")
    {:noreply, socket}
  end
  def render(assigns) do
    ~H"""
    <div class="p-6 bg-white rounded-lg shadow-md">
      <%= if @unauthorized do %>
        <h3 class="text-xl font-bold text-red-600 mb-4">权限不足</h3>
        <p class="text-gray-700">您没有访问系统维护管理的权限。只有管理员可以查看此页面。</p>
      <% else %>
        <h3 class="text-xl font-bold text-gray-800 mb-6">系统维护管理</h3>

        <!-- 维护模式切换 -->
        <div class="mb-8 p-4 border border-gray-200 rounded-lg bg-gray-50">
          <div class="flex justify-between items-center mb-4">
            <h4 class="text-lg font-semibold text-gray-800">维护模式</h4>
            <label class="inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                class="sr-only peer"
                checked={@maintenance_mode}
                phx-click="toggle_maintenance"
                phx-target={@myself}
                phx-value-maintenance_mode={!@maintenance_mode}
              />
              <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              <span class="ml-3 text-sm font-medium text-gray-700"><%= if @maintenance_mode, do: "开启", else: "关闭" %></span>
            </label>
          </div>

          <%= if @maintenance_mode do %>
            <div class="p-4 bg-red-50 border border-red-200 rounded-md">
              <p class="text-red-800 font-medium">系统当前处于维护模式！</p>
              <p class="text-red-700 text-sm mt-1">普通用户将无法访问系统，请尽快完成维护工作。</p>
            </div>
          <% end %>

          <div class="mt-4">
            <label class="block text-sm font-medium text-gray-700 mb-1">维护通知消息</label>
            <textarea
              class="w-full p-2 border border-gray-300 rounded-md"
              rows="3"
              phx-debounce="blur"
              phx-target={@myself}
              phx-change="update_message"
              value={@maintenance_message}
            ></textarea>
          </div>
        </div>

        <!-- 维护计划 -->
        <div class="mb-8 p-4 border border-gray-200 rounded-lg bg-gray-50">
          <h4 class="text-lg font-semibold text-gray-800 mb-4">维护计划</h4>
          <form phx-submit="set_maintenance_schedule" phx-target={@myself}>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">开始时间</label>
                <input
                  type="datetime-local"
                  class="w-full p-2 border border-gray-300 rounded-md"
                  value={format_datetime_local(@maintenance_start)}
                  name="start"
                  required
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">结束时间</label>
                <input
                  type="datetime-local"
                  class="w-full p-2 border border-gray-300 rounded-md"
                  value={format_datetime_local(@maintenance_end)}
                  name="end"
                  required
                />
              </div>
              <div class="flex items-end">
                <AdminButtonGroup.admin_button
                  text="设置维护计划"
                  type="submit"
                  class="w-full bg-blue-600 text-white hover:bg-blue-700"
                  bg_color="#2563eb"
                />
              </div>
            </div>
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-1">维护说明</label>
              <input
                type="text"
                class="w-full p-2 border border-gray-300 rounded-md"
                value={@maintenance_message}
                name="message"
                placeholder="请输入维护说明..."
              />
            </div>
          </form>

          <%= if @maintenance_start do %>
            <div class="p-3 bg-blue-50 border border-blue-200 rounded-md">
              <p class="text-blue-800 font-medium">已计划维护</p>
              <p class="text-blue-700 text-sm mt-1">开始时间: <%= Calendar.strftime(@maintenance_start, "%Y-%m-%d %H:%M") %></p>
              <p class="text-blue-700 text-sm">结束时间: <%= Calendar.strftime(@maintenance_end, "%Y-%m-%d %H:%M") %></p>
              <div class="flex space-x-2 mt-3">
                <AdminButtonGroup.admin_button
                  text="取消计划"
                  action="cancel_maintenance"
                  target={@myself}
                  class="px-3 py-1 text-sm bg-red-600 text-white hover:bg-red-700"
                  bg_color="#dc2626"
                />
                <AdminButtonGroup.admin_button
                  text="立即执行"
                  action="confirm_maintenance"
                  target={@myself}
                  class="px-3 py-1 text-sm bg-green-600 text-white hover:bg-green-700"
                  bg_color="#16a34a"
                />
              </div>
            </div>
          <% end %>
        </div>

        <!-- 系统备份与恢复 -->
        <div class="p-4 border border-gray-200 rounded-lg bg-gray-50">
          <h4 class="text-lg font-semibold text-gray-800 mb-4">系统备份与恢复</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h5 class="text-md font-medium text-gray-800 mb-3">创建备份</h5>
              <AdminButtonGroup.admin_button
                text={
                  if @backup_status == "in_progress" do
                    "备份中..."
                  else
                    if @backup_status == "completed" do
                      "备份完成"
                    else
                      "创建系统备份"
                    end
                  end
                }
                action="perform_backup"
                target={@myself}
                class="bg-green-600 text-white hover:bg-green-700"
                bg_color="#16a34a"
                disabled={@backup_status == "in_progress"}
              />

              <%= if @backup_status == "in_progress" do %>
                <div class="mt-3">
                  <div class="w-full bg-gray-200 rounded-full h-2.5 mb-2">
                    <div class="bg-green-600 h-2.5 rounded-full" style={"width: #{@backup_progress}%"}></div>
                  </div>
                  <p class="text-sm text-gray-700">进度: #{@backup_progress}%</p>
                  <div class="bg-gray-50 p-2 rounded-md h-24 overflow-y-auto text-xs text-gray-700 font-mono mt-2">
                    <%= @backup_log %>
                  </div>
                </div>
              <% end %>

              <%= if @backup_status == "completed" do %>
                <p class="text-green-600 text-sm mt-2">最后备份时间: <%= Calendar.strftime(DateTime.utc_now(), "%Y-%m-%d %H:%M:%S") %></p>
              <% end %>
            </div>
            <div>
              <h5 class="text-md font-medium text-gray-800 mb-3">恢复备份</h5>
              <div class="mb-3">
                <label class="block text-sm font-medium text-gray-700 mb-1">选择备份</label>
                <select class="w-full p-2 border border-gray-300 rounded-md" name="backup_id" phx-change="select_backup" phx-target={@myself} value={@selected_backup}>
                  <option value="latest">最新备份 (推荐)</option>
                  <option value="20231115">2023-11-15 备份</option>
                  <option value="20231110">2023-11-10 备份</option>
                </select>
              </div>
              <AdminButtonGroup.admin_button
                text={
                  if @restore_status == "in_progress" do
                    "恢复中..."
                  else
                    if @restore_status == "completed" do
                      "恢复完成"
                    else
                      "恢复系统备份"
                    end
                  end
                }
                action="show_restore_confirm"
                target={@myself}
                class="bg-orange-600 text-white hover:bg-orange-700"
                bg_color="#ea580c"
                disabled={@restore_status == "in_progress"}
              />
            </div>
          </div>

          <!-- 恢复确认模态框 -->
          <%= if @restore_confirm_modal do %>
            <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div class="bg-white rounded-lg p-6 w-full max-w-md">
                <h3 class="text-lg font-bold text-gray-800 mb-4">确认恢复备份</h3>
                <p class="text-red-700 mb-4">
                  警告：恢复操作将覆盖当前系统数据，此操作不可撤销！
                </p>
                <p class="text-gray-700 mb-6">
                  您确定要恢复备份: <%= @selected_backup %> 吗？
                  系统将重启并应用此备份，所有当前数据将被替换。
                </p>
                <div class="flex justify-end space-x-4 pt-4 border-t border-gray-200">
                  <AdminButtonGroup.cancel_button
                    text="取消"
                    action="cancel_restore"
                    target={@myself}
                  />
                  <AdminButtonGroup.danger_button
                    text="确认恢复"
                    action="confirm_restore"
                    target={@myself}
                  />
                </div>
              </div>
            </div>
          <% end %>
        </div>
      <% end %>
    </div>
    """
  end
end
