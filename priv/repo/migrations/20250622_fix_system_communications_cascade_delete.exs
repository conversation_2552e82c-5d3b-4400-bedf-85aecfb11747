defmodule Cypridina.Repo.Migrations.FixSystemCommunicationsCascadeDelete do
  @moduledoc """
  修复系统通信表的级联删除约束
  
  添加级联删除功能，确保删除系统通信记录时，相关的阅读记录也会自动删除
  """

  use Ecto.Migration

  def up do
    # 删除现有的外键约束
    drop constraint(:system_communication_reads, "system_communication_reads_communication_id_fkey")
    
    # 重新添加带有级联删除的外键约束
    alter table(:system_communication_reads) do
      modify :communication_id,
             references(:system_communications,
               column: :id,
               name: "system_communication_reads_communication_id_fkey",
               type: :uuid,
               prefix: "public",
               on_delete: :delete_all  # 添加级联删除
             )
    end
  end

  def down do
    # 删除带级联删除的外键约束
    drop constraint(:system_communication_reads, "system_communication_reads_communication_id_fkey")
    
    # 恢复原来的外键约束（不带级联删除）
    alter table(:system_communication_reads) do
      modify :communication_id,
             references(:system_communications,
               column: :id,
               name: "system_communication_reads_communication_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end
  end
end
