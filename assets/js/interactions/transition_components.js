/**
 * Racing Game Admin Panel - 过渡效果组件
 * 
 * 专门处理页面、模态框、标签页等过渡效果的组件系统
 */

/**
 * 过渡效果管理器
 */
class TransitionManager {
  constructor(options = {}) {
    this.options = {
      defaultDuration: 300,
      defaultEasing: 'cubic-bezier(0.4, 0, 0.2, 1)',
      enablePreload: true,
      enableHistory: true,
      ...options
    };

    this.activeTransitions = new Map();
    this.transitionHistory = [];
    this.preloadedPages = new Map();
    
    this.init();
  }

  init() {
    this.setupPageTransitions();
    this.setupModalTransitions();
    this.setupTabTransitions();
    this.setupAccordionTransitions();
  }

  /**
   * 设置页面过渡
   */
  setupPageTransitions() {
    // 监听LiveView页面变化
    document.addEventListener('phx:page-loading-start', (e) => {
      this.startPageTransition(e.detail);
    });

    document.addEventListener('phx:page-loading-stop', (e) => {
      this.completePageTransition(e.detail);
    });
  }

  /**
   * 开始页面过渡
   */
  async startPageTransition(detail) {
    const currentPage = document.querySelector('[data-page-container]');
    if (!currentPage) return;

    // 添加过渡类
    currentPage.classList.add('page-transitioning-out');
    
    // 显示加载指示器
    this.showPageLoader();
    
    // 执行退出动画
    await this.animatePageOut(currentPage);
  }

  /**
   * 完成页面过渡
   */
  async completePageTransition(detail) {
    const newPage = document.querySelector('[data-page-container]');
    if (!newPage) return;

    // 隐藏加载指示器
    this.hidePageLoader();
    
    // 执行进入动画
    await this.animatePageIn(newPage);
    
    // 清理过渡类
    newPage.classList.remove('page-transitioning-in');
  }

  /**
   * 页面退出动画
   */
  async animatePageOut(element) {
    return new Promise((resolve) => {
      element.style.transition = `opacity ${this.options.defaultDuration}ms ${this.options.defaultEasing}`;
      element.style.opacity = '0';
      
      setTimeout(resolve, this.options.defaultDuration);
    });
  }

  /**
   * 页面进入动画
   */
  async animatePageIn(element) {
    element.classList.add('page-transitioning-in');
    element.style.opacity = '0';
    element.style.transform = 'translateY(20px)';
    
    // 强制重排
    element.offsetHeight;
    
    return new Promise((resolve) => {
      element.style.transition = `opacity ${this.options.defaultDuration}ms ${this.options.defaultEasing}, transform ${this.options.defaultDuration}ms ${this.options.defaultEasing}`;
      element.style.opacity = '1';
      element.style.transform = 'translateY(0)';
      
      setTimeout(() => {
        element.style.transition = '';
        element.style.transform = '';
        resolve();
      }, this.options.defaultDuration);
    });
  }

  /**
   * 显示页面加载器
   */
  showPageLoader() {
    let loader = document.getElementById('page-transition-loader');
    
    if (!loader) {
      loader = document.createElement('div');
      loader.id = 'page-transition-loader';
      loader.className = 'page-transition-loader';
      loader.innerHTML = `
        <div class="loader-backdrop"></div>
        <div class="loader-content">
          <div class="loading-spinner"></div>
          <p>页面加载中...</p>
        </div>
      `;
      document.body.appendChild(loader);
    }
    
    loader.classList.add('active');
  }

  /**
   * 隐藏页面加载器
   */
  hidePageLoader() {
    const loader = document.getElementById('page-transition-loader');
    if (loader) {
      loader.classList.remove('active');
    }
  }

  /**
   * 设置模态框过渡
   */
  setupModalTransitions() {
    // 监听模态框事件
    document.addEventListener('modal:show', (e) => {
      this.showModal(e.detail.modal, e.detail.options);
    });

    document.addEventListener('modal:hide', (e) => {
      this.hideModal(e.detail.modal, e.detail.options);
    });
  }

  /**
   * 显示模态框
   */
  async showModal(modal, options = {}) {
    const backdrop = modal.querySelector('.modal-backdrop');
    const content = modal.querySelector('.modal-content');
    
    // 设置初始状态
    modal.style.display = 'flex';
    backdrop.style.opacity = '0';
    content.style.opacity = '0';
    content.style.transform = 'scale(0.9) translateY(-20px)';
    
    // 强制重排
    modal.offsetHeight;
    
    // 执行动画
    const duration = options.duration || this.options.defaultDuration;
    
    backdrop.style.transition = `opacity ${duration}ms ${this.options.defaultEasing}`;
    content.style.transition = `opacity ${duration}ms ${this.options.defaultEasing}, transform ${duration}ms ${this.options.defaultEasing}`;
    
    backdrop.style.opacity = '1';
    content.style.opacity = '1';
    content.style.transform = 'scale(1) translateY(0)';
    
    // 添加活动类
    modal.classList.add('modal-active');
    
    return new Promise((resolve) => {
      setTimeout(() => {
        backdrop.style.transition = '';
        content.style.transition = '';
        resolve();
      }, duration);
    });
  }

  /**
   * 隐藏模态框
   */
  async hideModal(modal, options = {}) {
    const backdrop = modal.querySelector('.modal-backdrop');
    const content = modal.querySelector('.modal-content');
    
    const duration = options.duration || this.options.defaultDuration;
    
    backdrop.style.transition = `opacity ${duration}ms ${this.options.defaultEasing}`;
    content.style.transition = `opacity ${duration}ms ${this.options.defaultEasing}, transform ${duration}ms ${this.options.defaultEasing}`;
    
    backdrop.style.opacity = '0';
    content.style.opacity = '0';
    content.style.transform = 'scale(0.9) translateY(-20px)';
    
    return new Promise((resolve) => {
      setTimeout(() => {
        modal.style.display = 'none';
        modal.classList.remove('modal-active');
        backdrop.style.transition = '';
        content.style.transition = '';
        resolve();
      }, duration);
    });
  }

  /**
   * 设置标签页过渡
   */
  setupTabTransitions() {
    document.addEventListener('tab:change', (e) => {
      this.changeTab(e.detail.fromTab, e.detail.toTab, e.detail.options);
    });
  }

  /**
   * 标签页切换
   */
  async changeTab(fromTab, toTab, options = {}) {
    const direction = options.direction || 'horizontal';
    const duration = options.duration || 200;
    
    // 设置过渡样式
    const transitionStyle = `opacity ${duration}ms ${this.options.defaultEasing}, transform ${duration}ms ${this.options.defaultEasing}`;
    
    if (fromTab) {
      fromTab.style.transition = transitionStyle;
      fromTab.style.opacity = '0';
      
      if (direction === 'horizontal') {
        fromTab.style.transform = 'translateX(-20px)';
      } else {
        fromTab.style.transform = 'translateY(-10px)';
      }
    }
    
    // 等待退出动画完成
    await new Promise(resolve => setTimeout(resolve, duration / 2));
    
    // 隐藏旧标签页
    if (fromTab) {
      fromTab.style.display = 'none';
    }
    
    // 显示新标签页
    toTab.style.display = 'block';
    toTab.style.opacity = '0';
    
    if (direction === 'horizontal') {
      toTab.style.transform = 'translateX(20px)';
    } else {
      toTab.style.transform = 'translateY(10px)';
    }
    
    // 强制重排
    toTab.offsetHeight;
    
    // 执行进入动画
    toTab.style.transition = transitionStyle;
    toTab.style.opacity = '1';
    toTab.style.transform = 'translate(0)';
    
    // 清理样式
    setTimeout(() => {
      if (fromTab) {
        fromTab.style.transition = '';
        fromTab.style.transform = '';
      }
      toTab.style.transition = '';
      toTab.style.transform = '';
    }, duration);
  }

  /**
   * 设置手风琴过渡
   */
  setupAccordionTransitions() {
    document.addEventListener('accordion:toggle', (e) => {
      this.toggleAccordion(e.detail.element, e.detail.options);
    });
  }

  /**
   * 切换手风琴
   */
  async toggleAccordion(element, options = {}) {
    const content = element.querySelector('.accordion-content');
    const isExpanded = element.classList.contains('expanded');
    const duration = options.duration || this.options.defaultDuration;
    
    if (isExpanded) {
      // 收起
      const height = content.scrollHeight;
      content.style.height = height + 'px';
      
      // 强制重排
      content.offsetHeight;
      
      content.style.transition = `height ${duration}ms ${this.options.defaultEasing}, opacity ${duration}ms ${this.options.defaultEasing}`;
      content.style.height = '0';
      content.style.opacity = '0.7';
      
      setTimeout(() => {
        element.classList.remove('expanded');
        content.style.transition = '';
        content.style.height = '';
        content.style.opacity = '';
      }, duration);
      
    } else {
      // 展开
      element.classList.add('expanded');
      
      const height = content.scrollHeight;
      content.style.height = '0';
      content.style.opacity = '0.7';
      
      // 强制重排
      content.offsetHeight;
      
      content.style.transition = `height ${duration}ms ${this.options.defaultEasing}, opacity ${duration}ms ${this.options.defaultEasing}`;
      content.style.height = height + 'px';
      content.style.opacity = '1';
      
      setTimeout(() => {
        content.style.transition = '';
        content.style.height = 'auto';
        content.style.opacity = '';
      }, duration);
    }
  }

  /**
   * 创建自定义过渡
   */
  createCustomTransition(name, config) {
    this.customTransitions = this.customTransitions || {};
    this.customTransitions[name] = config;
  }

  /**
   * 执行自定义过渡
   */
  async executeCustomTransition(name, element, options = {}) {
    const config = this.customTransitions?.[name];
    if (!config) {
      throw new Error(`Custom transition '${name}' not found`);
    }
    
    const duration = options.duration || config.duration || this.options.defaultDuration;
    
    // 应用开始样式
    if (config.from) {
      Object.assign(element.style, config.from);
    }
    
    // 强制重排
    element.offsetHeight;
    
    // 设置过渡
    element.style.transition = config.transition || `all ${duration}ms ${this.options.defaultEasing}`;
    
    // 应用结束样式
    if (config.to) {
      Object.assign(element.style, config.to);
    }
    
    return new Promise((resolve) => {
      setTimeout(() => {
        element.style.transition = '';
        if (config.cleanup) {
          config.cleanup(element);
        }
        resolve();
      }, duration);
    });
  }

  /**
   * 获取过渡状态
   */
  getTransitionState() {
    return {
      activeTransitions: this.activeTransitions.size,
      history: this.transitionHistory.slice(-10)
    };
  }

  /**
   * 销毁过渡管理器
   */
  destroy() {
    // 清理所有活动过渡
    for (const [id, transition] of this.activeTransitions) {
      if (transition.cleanup) {
        transition.cleanup();
      }
    }
    
    this.activeTransitions.clear();
    this.transitionHistory = [];
    this.preloadedPages.clear();
  }
}

// 全局实例
window.transitionManager = new TransitionManager();

// Phoenix LiveView Hook
const TransitionHook = {
  mounted() {
    // 标记为页面容器
    if (this.el.dataset.pageContainer !== undefined) {
      this.el.setAttribute('data-page-container', 'true');
    }
    
    // 设置自动过渡
    if (this.el.dataset.autoTransition) {
      const transitionType = this.el.dataset.autoTransition;
      const delay = parseInt(this.el.dataset.transitionDelay) || 0;
      
      setTimeout(() => {
        this.el.classList.add(`animate-${transitionType}`);
      }, delay);
    }
  },

  updated() {
    // 处理内容更新动画
    if (this.el.dataset.updateAnimation) {
      const animationType = this.el.dataset.updateAnimation;
      this.el.classList.add(`animate-${animationType}`);
      
      // 清理动画类
      setTimeout(() => {
        this.el.classList.remove(`animate-${animationType}`);
      }, 500);
    }
  },

  destroyed() {
    // 清理过渡相关的资源
    this.el.style.transition = '';
    this.el.style.transform = '';
    this.el.style.opacity = '';
  }
};

// 导出
export { TransitionManager, TransitionHook };
