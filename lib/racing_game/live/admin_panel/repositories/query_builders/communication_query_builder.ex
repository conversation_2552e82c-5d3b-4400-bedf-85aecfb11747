defmodule RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CommunicationQueryBuilder do
  @moduledoc """
  系统通信查询构建器

  提供复杂通信查询的构建功能：
  - 动态查询条件构建
  - 搜索查询优化
  - 过滤条件组合
  - 排序和分页支持
  """

  require Logger
  alias RacingGame.SystemCommunication
  require Ash.Query

  # 常量定义
  @searchable_fields [:title, :content]
  @filterable_fields [:type, :priority, :active, :recipient_type, :inserted_at, :updated_at, :expires_at]
  @sortable_fields [:title, :type, :priority, :active, :inserted_at, :updated_at, :expires_at]
  @valid_types [:message, :announcement, :notification]
  @valid_priorities [:low, :medium, :high, :urgent]
  @valid_recipient_types [:all, :user, :admin]

  # ============================================================================
  # 公共API
  # ============================================================================

  @doc """
  构建列表查询

  ## 参数
  - `params` - 查询参数
    - `:search` - 搜索关键词
    - `:type` - 通信类型过滤
    - `:status` - 状态过滤
    - `:priority` - 优先级过滤
    - `:recipient_type` - 接收者类型过滤
    - `:filters` - 其他过滤条件
    - `:sort` - 排序条件

  ## 返回
  - `{:ok, query}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_list_query(params) do
    Logger.debug("🔨 [通信查询构建器] 构建通信列表查询")

    try do
      query = SystemCommunication
      |> apply_search_conditions(params[:search])
      |> apply_type_filter(params[:type])
      |> apply_status_filter(params[:status])
      |> apply_priority_filter(params[:priority])
      |> apply_recipient_type_filter(params[:recipient_type])
      |> apply_filter_conditions(params[:filters])
      |> apply_sort_conditions(params[:sort])

      {:ok, query}
    rescue
      error ->
        Logger.error("❌ [通信查询构建器] 构建查询失败: #{inspect(error)}")
        {:error, "查询构建失败"}
    end
  end

  @doc """
  构建搜索查询

  ## 参数
  - `search_term` - 搜索词
  - `fields` - 搜索字段列表

  ## 返回
  - 查询对象
  """
  def build_search_query(search_term, fields \\ @searchable_fields) do
    Logger.debug("🔍 [通信查询构建器] 构建搜索查询: #{search_term}")

    SystemCommunication
    |> apply_search_across_fields(search_term, fields)
    |> Ash.Query.sort(inserted_at: :desc)
  end

  @doc """
  构建统计查询

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, query}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_statistics_query(params \\ %{}) do
    Logger.debug("📊 [通信查询构建器] 构建统计查询")

    try do
      query = SystemCommunication
      |> apply_date_range_filter(params[:date_from], params[:date_to])
      |> apply_type_filter(params[:type])
      |> apply_status_filter(params[:status])
      |> apply_filter_conditions(params[:filters])

      {:ok, query}
    rescue
      error ->
        Logger.error("❌ [通信查询构建器] 构建统计查询失败: #{inspect(error)}")
        {:error, "统计查询构建失败"}
    end
  end

  @doc """
  构建用户通信查询

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - 查询对象
  """
  def build_user_communications_query(user_id, options \\ []) do
    Logger.debug("👤 [通信查询构建器] 构建用户通信查询: #{user_id}")

    SystemCommunication
    |> apply_user_recipient_filter(user_id)
    |> apply_active_filter()
    |> apply_expiry_filter()
    |> apply_type_filter(options[:type])
    |> Ash.Query.sort(inserted_at: :desc)
  end

  @doc """
  构建活跃通信查询

  ## 参数
  - `options` - 选项

  ## 返回
  - 查询对象
  """
  def build_active_communications_query(options \\ []) do
    Logger.debug("📢 [通信查询构建器] 构建活跃通信查询")

    SystemCommunication
    |> apply_active_filter()
    |> apply_expiry_filter()
    |> apply_type_filter(options[:type])
    |> apply_priority_filter(options[:priority])
    |> Ash.Query.sort(priority: :desc, inserted_at: :desc)
  end

  # ============================================================================
  # 搜索条件应用
  # ============================================================================

  # 应用搜索条件
  defp apply_search_conditions(query, nil), do: query
  defp apply_search_conditions(query, ""), do: query
  defp apply_search_conditions(query, search_term) do
    trimmed_search = String.trim(search_term)

    if String.length(trimmed_search) >= 2 do
      apply_search_across_fields(query, trimmed_search, @searchable_fields)
    else
      query
    end
  end

  # 跨字段搜索
  defp apply_search_across_fields(query, search_term, fields) do
    search_conditions = build_search_conditions(search_term, fields)

    case search_conditions do
      [] -> query
      [condition] -> Ash.Query.filter(query, ^condition)
      conditions -> Ash.Query.filter(query, ^{:or, conditions})
    end
  end

  # 构建搜索条件
  defp build_search_conditions(search_term, fields) do
    conditions = Enum.reduce(fields, [], fn field, acc ->
      case field do
        :title -> [:title | acc]
        :content -> [:content | acc]
        _ -> acc
      end
    end)

    # 返回搜索条件，将在查询中使用
    {search_term, conditions}
  end

  # ============================================================================
  # 过滤条件应用
  # ============================================================================

  # 应用类型过滤
  defp apply_type_filter(query, nil), do: query
  defp apply_type_filter(query, type) when type in @valid_types do
    Ash.Query.filter(query, type == ^type)
  end
  defp apply_type_filter(query, type) when is_binary(type) do
    case String.to_existing_atom(type) do
      atom_type when atom_type in @valid_types ->
        Ash.Query.filter(query, type == ^atom_type)
      _ ->
        Logger.warning("⚠️ [通信查询构建器] 无效的类型过滤: #{type}")
        query
    end
  rescue
    ArgumentError ->
      Logger.warning("⚠️ [通信查询构建器] 无效的类型过滤: #{type}")
      query
  end
  defp apply_type_filter(query, _), do: query

  # 应用状态过滤
  defp apply_status_filter(query, nil), do: query
  defp apply_status_filter(query, status) when is_boolean(status) do
    Ash.Query.filter(query, active == ^status)
  end
  defp apply_status_filter(query, "true"), do: apply_status_filter(query, true)
  defp apply_status_filter(query, "false"), do: apply_status_filter(query, false)
  defp apply_status_filter(query, "active"), do: apply_status_filter(query, true)
  defp apply_status_filter(query, "inactive"), do: apply_status_filter(query, false)
  defp apply_status_filter(query, _), do: query

  # 应用优先级过滤
  defp apply_priority_filter(query, nil), do: query
  defp apply_priority_filter(query, priority) when priority in @valid_priorities do
    Ash.Query.filter(query, priority == ^priority)
  end
  defp apply_priority_filter(query, priority) when is_binary(priority) do
    case String.to_existing_atom(priority) do
      atom_priority when atom_priority in @valid_priorities ->
        Ash.Query.filter(query, priority == ^atom_priority)
      _ ->
        Logger.warning("⚠️ [通信查询构建器] 无效的优先级过滤: #{priority}")
        query
    end
  rescue
    ArgumentError ->
      Logger.warning("⚠️ [通信查询构建器] 无效的优先级过滤: #{priority}")
      query
  end
  defp apply_priority_filter(query, _), do: query

  # 应用接收者类型过滤
  defp apply_recipient_type_filter(query, nil), do: query
  defp apply_recipient_type_filter(query, recipient_type) when recipient_type in @valid_recipient_types do
    Ash.Query.filter(query, recipient_type == ^recipient_type)
  end
  defp apply_recipient_type_filter(query, recipient_type) when is_binary(recipient_type) do
    case String.to_existing_atom(recipient_type) do
      atom_type when atom_type in @valid_recipient_types ->
        Ash.Query.filter(query, recipient_type == ^atom_type)
      _ ->
        Logger.warning("⚠️ [通信查询构建器] 无效的接收者类型过滤: #{recipient_type}")
        query
    end
  rescue
    ArgumentError ->
      Logger.warning("⚠️ [通信查询构建器] 无效的接收者类型过滤: #{recipient_type}")
      query
  end
  defp apply_recipient_type_filter(query, _), do: query

  # 应用用户接收者过滤
  defp apply_user_recipient_filter(query, user_id) do
    Ash.Query.filter(query,
      recipient_type == :all or
      (recipient_type == :user and recipient_id == ^user_id)
    )
  end

  # 应用活跃状态过滤
  defp apply_active_filter(query) do
    Ash.Query.filter(query, active == true)
  end

  # 应用过期时间过滤
  defp apply_expiry_filter(query) do
    now = DateTime.utc_now()
    Ash.Query.filter(query, is_nil(expires_at) or expires_at > ^now)
  end

  # 应用其他过滤条件
  defp apply_filter_conditions(query, nil), do: query
  defp apply_filter_conditions(query, filters) when map_size(filters) == 0, do: query
  defp apply_filter_conditions(query, filters) do
    Enum.reduce(filters, query, fn {field, value}, acc_query ->
      apply_single_filter(acc_query, field, value)
    end)
  end

  # 应用单个过滤条件
  defp apply_single_filter(query, field, value) when field in @filterable_fields do
    case {field, value} do
      {:inserted_at, %{from: from_date, to: to_date}} ->
        apply_date_range_filter(query, from_date, to_date)

      {:updated_at, %{from: from_date, to: to_date}} ->
        query
        |> Ash.Query.filter(updated_at >= ^from_date)
        |> Ash.Query.filter(updated_at <= ^to_date)

      {:expires_at, %{from: from_date, to: to_date}} ->
        query
        |> Ash.Query.filter(expires_at >= ^from_date)
        |> Ash.Query.filter(expires_at <= ^to_date)

      _ ->
        Logger.warning("⚠️ [通信查询构建器] 忽略无效过滤条件: #{field} = #{inspect(value)}")
        query
    end
  end
  defp apply_single_filter(query, field, _value) do
    Logger.warning("⚠️ [通信查询构建器] 忽略不支持的过滤字段: #{field}")
    query
  end

  # 应用日期范围过滤
  defp apply_date_range_filter(query, nil, nil), do: query
  defp apply_date_range_filter(query, from_date, nil) do
    Ash.Query.filter(query, inserted_at >= ^from_date)
  end
  defp apply_date_range_filter(query, nil, to_date) do
    Ash.Query.filter(query, inserted_at <= ^to_date)
  end
  defp apply_date_range_filter(query, from_date, to_date) do
    query
    |> Ash.Query.filter(inserted_at >= ^from_date)
    |> Ash.Query.filter(inserted_at <= ^to_date)
  end

  # ============================================================================
  # 排序条件应用
  # ============================================================================

  # 应用排序条件
  defp apply_sort_conditions(query, nil), do: Ash.Query.sort(query, inserted_at: :desc)
  defp apply_sort_conditions(query, []), do: Ash.Query.sort(query, inserted_at: :desc)
  defp apply_sort_conditions(query, sort_conditions) when is_list(sort_conditions) do
    validated_sorts = validate_sort_conditions(sort_conditions)

    case validated_sorts do
      [] -> Ash.Query.sort(query, inserted_at: :desc)
      sorts -> Ash.Query.sort(query, sorts)
    end
  end
  defp apply_sort_conditions(query, sort_condition) do
    apply_sort_conditions(query, [sort_condition])
  end

  # 验证排序条件
  defp validate_sort_conditions(sort_conditions) do
    Enum.reduce(sort_conditions, [], fn sort_condition, acc ->
      case validate_single_sort_condition(sort_condition) do
        {:ok, validated_sort} -> [validated_sort | acc]
        :error -> acc
      end
    end)
    |> Enum.reverse()
  end

  # 验证单个排序条件
  defp validate_single_sort_condition({field, direction})
       when field in @sortable_fields and direction in [:asc, :desc] do
    {:ok, {field, direction}}
  end
  defp validate_single_sort_condition(field) when field in @sortable_fields do
    {:ok, {field, :asc}}
  end
  defp validate_single_sort_condition(sort_condition) do
    Logger.warning("⚠️ [通信查询构建器] 忽略无效排序条件: #{inspect(sort_condition)}")
    :error
  end

  # ============================================================================
  # 查询优化
  # ============================================================================

  @doc """
  优化查询性能

  ## 参数
  - `query` - 查询对象
  - `options` - 优化选项

  ## 返回
  - 优化后的查询对象
  """
  def optimize_query(query, options \\ []) do
    Logger.debug("⚡ [通信查询构建器] 优化查询性能")

    query
    |> maybe_add_index_hints(options)
    |> maybe_limit_results(options)
    |> maybe_select_fields(options)
  end

  # 可能添加索引提示
  defp maybe_add_index_hints(query, _options) do
    # TODO: 根据查询条件添加索引提示
    query
  end

  # 可能限制结果数量
  defp maybe_limit_results(query, options) do
    case Keyword.get(options, :max_results) do
      nil -> query
      max_results when is_integer(max_results) and max_results > 0 ->
        Ash.Query.limit(query, max_results)
      _ -> query
    end
  end

  # 可能选择特定字段
  defp maybe_select_fields(query, options) do
    case Keyword.get(options, :select_fields) do
      nil -> query
      fields when is_list(fields) ->
        Ash.Query.select(query, fields)
      _ -> query
    end
  end
end
