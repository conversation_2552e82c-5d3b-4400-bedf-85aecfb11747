defmodule RacingGame.Utils.CommonUtils do
  @moduledoc """
  通用工具函数模块

  提供各种常用的工具函数，包括ID生成、映射合并、过滤器判断等。

  ## 功能分类

  ### ID和标识符生成
  - `generate_unique_id/1` - 生成唯一标识符
  - `generate_uuid/0` - 生成UUID
  - `generate_short_id/1` - 生成短ID

  ### 映射操作
  - `deep_merge/2` - 深度合并映射
  - `safe_get_in/3` - 安全获取嵌套值
  - `safe_put_in/3` - 安全更新嵌套值
  - `rename_keys/2` - 批量重命名键
  - `filter_keys/2` - 过滤指定键
  - `drop_keys/2` - 移除指定键

  ### 数据转换
  - `keyword_to_map/2` - 关键字列表转映射
  - `map_to_keyword/2` - 映射转关键字列表
  - `zip_to_map/2` - 压缩列表为映射
  - `normalize_params/1` - 标准化参数

  ### 过滤和判断
  - `should_show_clear_button?/3` - 判断是否显示清除按钮
  - `is_empty?/1` - 判断是否为空
  - `present?/1` - 判断是否存在值

  ### 字符串处理
  - `truncate/2` - 截断字符串
  - `slugify/1` - 生成URL友好的字符串
  - `sanitize_filename/1` - 清理文件名

  ### 时间处理
  - `format_duration/1` - 格式化时间间隔
  - `time_ago/1` - 相对时间描述
  """

  require Logger

  # 常量定义
  @default_truncate_length 50
  @uuid_regex ~r/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  @slug_regex ~r/[^a-z0-9\-_]/
  @filename_invalid_chars ~r/[<>:"\/\\|?*]/

  @doc """
  生成唯一标识符

  ## 参数
  - `prefix` - 前缀字符串（可选，默认为空）

  ## 示例
      iex> CommonUtils.generate_unique_id()
      "1701234567890_1234"

      iex> CommonUtils.generate_unique_id("user_")
      "user_1701234567890_5678"
  """
  def generate_unique_id(prefix \\ "") do
    timestamp = System.system_time(:millisecond)
    random = :rand.uniform(9999)
    "#{prefix}#{timestamp}_#{random}"
  end

  @doc """
  生成UUID

  ## 示例
      iex> CommonUtils.generate_uuid()
      "550e8400-e29b-41d4-a716-************"
  """
  def generate_uuid do
    Ecto.UUID.generate()
  end

  @doc """
  生成短ID

  ## 参数
  - `length` - ID长度（默认8位）

  ## 示例
      iex> CommonUtils.generate_short_id(6)
      "a1b2c3"
  """
  def generate_short_id(length \\ 8) do
    length
    |> :crypto.strong_rand_bytes()
    |> Base.encode16(case: :lower)
    |> String.slice(0, length)
  end

  @doc """
  验证UUID格式

  ## 参数
  - `uuid` - 要验证的UUID字符串

  ## 示例
      iex> CommonUtils.valid_uuid?("550e8400-e29b-41d4-a716-************")
      true
  """
  def valid_uuid?(uuid) when is_binary(uuid) do
    String.match?(uuid, @uuid_regex)
  end
  def valid_uuid?(_), do: false

  @doc """
  深度合并两个映射

  递归合并嵌套的映射结构，右侧映射的值会覆盖左侧映射的值。

  ## 参数
  - `left` - 左侧映射
  - `right` - 右侧映射

  ## 示例
      iex> left = %{a: 1, b: %{c: 2, d: 3}}
      iex> right = %{b: %{c: 4, e: 5}, f: 6}
      iex> CommonUtils.deep_merge(left, right)
      %{a: 1, b: %{c: 4, d: 3, e: 5}, f: 6}
  """
  def deep_merge(left, right) when is_map(left) and is_map(right) do
    Map.merge(left, right, fn _key, left_val, right_val ->
      if is_map(left_val) and is_map(right_val) do
        deep_merge(left_val, right_val)
      else
        right_val
      end
    end)
  end
  def deep_merge(_left, right), do: right

  @doc """
  判断是否应该显示清除按钮

  根据搜索查询、类型过滤和状态过滤的情况判断是否需要显示清除按钮。

  ## 参数
  - `search_query` - 搜索查询字符串
  - `selected_type` - 选中的类型过滤
  - `selected_status` - 选中的状态过滤

  ## 示例
      iex> CommonUtils.should_show_clear_button?("test", nil, nil)
      true

      iex> CommonUtils.should_show_clear_button?("", nil, nil)
      false

      iex> CommonUtils.should_show_clear_button?("", :message, nil)
      true
  """
  def should_show_clear_button?(search_query, selected_type, selected_status) do
    has_search_query = present?(search_query)
    has_type_filter = selected_type != nil and selected_type != :all
    has_status_filter = selected_status != nil and selected_status != :all

    has_search_query or has_type_filter or has_status_filter
  end

  @doc """
  判断值是否为空

  ## 参数
  - `value` - 要检查的值

  ## 示例
      iex> CommonUtils.is_empty?("")
      true

      iex> CommonUtils.is_empty?([])
      true

      iex> CommonUtils.is_empty?(%{})
      true

      iex> CommonUtils.is_empty?(nil)
      true

      iex> CommonUtils.is_empty?("hello")
      false
  """
  def is_empty?(nil), do: true
  def is_empty?(""), do: true
  def is_empty?(value) when is_binary(value), do: String.trim(value) == ""
  def is_empty?(value) when is_list(value), do: Enum.empty?(value)
  def is_empty?(value) when is_map(value), do: map_size(value) == 0
  def is_empty?(_), do: false

  @doc """
  判断值是否存在（非空）

  ## 参数
  - `value` - 要检查的值

  ## 示例
      iex> CommonUtils.present?("hello")
      true

      iex> CommonUtils.present?("")
      false
  """
  def present?(value), do: not is_empty?(value)

  @doc """
  标准化参数映射

  移除空值、修剪字符串、转换类型等

  ## 参数
  - `params` - 参数映射
  - `opts` - 选项
    - `:remove_empty` - 是否移除空值（默认true）
    - `:trim_strings` - 是否修剪字符串（默认true）

  ## 示例
      iex> params = %{"name" => "  John  ", "age" => "", "city" => "NYC"}
      iex> CommonUtils.normalize_params(params)
      %{"name" => "John", "city" => "NYC"}
  """
  def normalize_params(params, opts \\ []) when is_map(params) do
    remove_empty = Keyword.get(opts, :remove_empty, true)
    trim_strings = Keyword.get(opts, :trim_strings, true)

    params
    |> maybe_trim_strings(trim_strings)
    |> maybe_remove_empty(remove_empty)
  end
  def normalize_params(params, _opts), do: params

  @doc """
  安全地获取映射中的嵌套值

  使用路径列表安全地获取嵌套映射中的值，如果路径不存在则返回默认值。

  ## 参数
  - `map` - 源映射
  - `path` - 路径列表
  - `default` - 默认值（可选，默认为 nil）

  ## 示例
      iex> data = %{user: %{profile: %{name: "John"}}}
      iex> CommonUtils.safe_get_in(data, [:user, :profile, :name])
      "John"

      iex> CommonUtils.safe_get_in(data, [:user, :settings, :theme], "default")
      "default"
  """
  def safe_get_in(map, path, default \\ nil) when is_map(map) and is_list(path) do
    try do
      get_in(map, path) || default
    rescue
      _ -> default
    end
  end
  def safe_get_in(_map, _path, default), do: default

  @doc """
  安全地更新映射中的嵌套值

  使用路径列表安全地更新嵌套映射中的值，如果路径不存在则创建。

  ## 参数
  - `map` - 源映射
  - `path` - 路径列表
  - `value` - 新值

  ## 示例
      iex> data = %{user: %{profile: %{name: "John"}}}
      iex> CommonUtils.safe_put_in(data, [:user, :profile, :age], 25)
      %{user: %{profile: %{name: "John", age: 25}}}
  """
  def safe_put_in(map, path, value) when is_map(map) and is_list(path) do
    try do
      put_in(map, path, value)
    rescue
      _ ->
        # 如果路径不存在，创建嵌套结构
        create_nested_path(map, path, value)
    end
  end
  def safe_put_in(map, _path, _value), do: map

  @doc """
  将关键字列表转换为映射，支持嵌套结构

  ## 参数
  - `keyword_list` - 关键字列表
  - `opts` - 选项
    - `:deep` - 是否深度转换（默认 false）

  ## 示例
      iex> CommonUtils.keyword_to_map([a: 1, b: 2])
      %{a: 1, b: 2}

      iex> CommonUtils.keyword_to_map([a: [b: 1, c: 2]], deep: true)
      %{a: %{b: 1, c: 2}}
  """
  def keyword_to_map(keyword_list, opts \\ []) when is_list(keyword_list) do
    deep = Keyword.get(opts, :deep, false)

    if deep do
      keyword_list
      |> Enum.into(%{})
      |> deep_convert_keywords_to_maps()
    else
      Enum.into(keyword_list, %{})
    end
  end
  def keyword_to_map(value, _opts), do: value

  @doc """
  将映射转换为关键字列表

  ## 参数
  - `map` - 源映射
  - `opts` - 选项
    - `:atom_keys` - 是否将字符串键转换为原子（默认 false）

  ## 示例
      iex> CommonUtils.map_to_keyword(%{a: 1, b: 2})
      [a: 1, b: 2]
  """
  def map_to_keyword(map, opts \\ []) when is_map(map) do
    atom_keys = Keyword.get(opts, :atom_keys, false)

    map
    |> Enum.map(fn {key, value} ->
      final_key = if atom_keys and is_binary(key) do
        String.to_atom(key)
      else
        key
      end
      {final_key, value}
    end)
  end
  def map_to_keyword(value, _opts), do: value

  @doc """
  批量重命名映射的键

  ## 参数
  - `map` - 源映射
  - `key_mapping` - 键映射关系

  ## 示例
      iex> data = %{"old_name" => "John", "old_age" => 25}
      iex> mapping = %{"old_name" => "name", "old_age" => "age"}
      iex> CommonUtils.rename_keys(data, mapping)
      %{"name" => "John", "age" => 25}
  """
  def rename_keys(map, key_mapping) when is_map(map) and is_map(key_mapping) do
    Enum.reduce(map, %{}, fn {key, value}, acc ->
      new_key = Map.get(key_mapping, key, key)
      Map.put(acc, new_key, value)
    end)
  end
  def rename_keys(map, _key_mapping), do: map

  @doc """
  过滤映射，只保留指定的键

  ## 参数
  - `map` - 源映射
  - `keys` - 要保留的键列表

  ## 示例
      iex> data = %{a: 1, b: 2, c: 3}
      iex> CommonUtils.filter_keys(data, [:a, :c])
      %{a: 1, c: 3}
  """
  def filter_keys(map, keys) when is_map(map) and is_list(keys) do
    Map.take(map, keys)
  end
  def filter_keys(map, _keys), do: map

  @doc """
  移除映射中的指定键

  ## 参数
  - `map` - 源映射
  - `keys` - 要移除的键列表

  ## 示例
      iex> data = %{a: 1, b: 2, c: 3}
      iex> CommonUtils.drop_keys(data, [:b])
      %{a: 1, c: 3}
  """
  def drop_keys(map, keys) when is_map(map) and is_list(keys) do
    Map.drop(map, keys)
  end
  def drop_keys(map, _keys), do: map

  @doc """
  压缩两个列表为键值对映射

  ## 参数
  - `keys` - 键列表
  - `values` - 值列表

  ## 示例
      iex> CommonUtils.zip_to_map([:a, :b, :c], [1, 2, 3])
      %{a: 1, b: 2, c: 3}
  """
  def zip_to_map(keys, values) when is_list(keys) and is_list(values) do
    keys
    |> Enum.zip(values)
    |> Enum.into(%{})
  end
  def zip_to_map(_keys, _values), do: %{}

  @doc """
  截断字符串

  ## 参数
  - `string` - 要截断的字符串
  - `length` - 最大长度（默认50）
  - `opts` - 选项
    - `:suffix` - 后缀（默认"..."）

  ## 示例
      iex> CommonUtils.truncate("Hello World", 5)
      "Hello..."

      iex> CommonUtils.truncate("Hello", 10)
      "Hello"
  """
  def truncate(string, length \\ @default_truncate_length, opts \\ [])
  def truncate(string, length, opts) when is_binary(string) do
    suffix = Keyword.get(opts, :suffix, "...")

    if String.length(string) <= length do
      string
    else
      truncated_length = max(0, length - String.length(suffix))
      String.slice(string, 0, truncated_length) <> suffix
    end
  end
  def truncate(value, _length, _opts), do: to_string(value)

  @doc """
  生成URL友好的字符串（slug）

  ## 参数
  - `string` - 要转换的字符串

  ## 示例
      iex> CommonUtils.slugify("Hello World!")
      "hello-world"

      iex> CommonUtils.slugify("用户管理")
      "yong-hu-guan-li"
  """
  def slugify(string) when is_binary(string) do
    string
    |> String.downcase()
    |> String.replace(~r/\s+/, "-")
    |> String.replace(@slug_regex, "")
    |> String.replace(~r/-+/, "-")
    |> String.trim("-")
  end
  def slugify(value), do: value |> to_string() |> slugify()

  @doc """
  清理文件名，移除无效字符

  ## 参数
  - `filename` - 文件名

  ## 示例
      iex> CommonUtils.sanitize_filename("file<name>.txt")
      "filename.txt"
  """
  def sanitize_filename(filename) when is_binary(filename) do
    filename
    |> String.replace(@filename_invalid_chars, "")
    |> String.trim()
    |> case do
      "" -> "unnamed_file"
      clean_name -> clean_name
    end
  end
  def sanitize_filename(value), do: value |> to_string() |> sanitize_filename()

  @doc """
  格式化时间间隔

  ## 参数
  - `seconds` - 秒数

  ## 示例
      iex> CommonUtils.format_duration(3661)
      "1h 1m 1s"

      iex> CommonUtils.format_duration(90)
      "1m 30s"
  """
  def format_duration(seconds) when is_integer(seconds) and seconds >= 0 do
    hours = div(seconds, 3600)
    minutes = div(rem(seconds, 3600), 60)
    secs = rem(seconds, 60)

    parts = []
    parts = if hours > 0, do: ["#{hours}h" | parts], else: parts
    parts = if minutes > 0, do: ["#{minutes}m" | parts], else: parts
    parts = if secs > 0 or Enum.empty?(parts), do: ["#{secs}s" | parts], else: parts

    parts |> Enum.reverse() |> Enum.join(" ")
  end
  def format_duration(_), do: "0s"

  @doc """
  相对时间描述

  ## 参数
  - `datetime` - 时间

  ## 示例
      iex> past_time = DateTime.add(DateTime.utc_now(), -3600, :second)
      iex> CommonUtils.time_ago(past_time)
      "1小时前"
  """
  def time_ago(%DateTime{} = datetime) do
    now = DateTime.utc_now()
    diff = DateTime.diff(now, datetime, :second)

    cond do
      diff < 60 -> "刚刚"
      diff < 3600 -> "#{div(diff, 60)}分钟前"
      diff < 86400 -> "#{div(diff, 3600)}小时前"
      diff < 2592000 -> "#{div(diff, 86400)}天前"
      diff < 31536000 -> "#{div(diff, 2592000)}个月前"
      true -> "#{div(diff, 31536000)}年前"
    end
  end
  def time_ago(_), do: "未知时间"

  # ============================================================================
  # 私有辅助函数
  # ============================================================================

  # 私有函数 - 创建嵌套路径
  defp create_nested_path(map, [key], value) do
    Map.put(map, key, value)
  end
  defp create_nested_path(map, [key | rest], value) do
    nested_map = Map.get(map, key, %{})
    Map.put(map, key, create_nested_path(nested_map, rest, value))
  end

  # 私有函数 - 可能修剪字符串
  defp maybe_trim_strings(params, false), do: params
  defp maybe_trim_strings(params, true) do
    Map.new(params, fn {key, value} ->
      trimmed_value = if is_binary(value), do: String.trim(value), else: value
      {key, trimmed_value}
    end)
  end

  # 私有函数 - 可能移除空值
  defp maybe_remove_empty(params, false), do: params
  defp maybe_remove_empty(params, true) do
    Enum.reject(params, fn {_key, value} -> is_empty?(value) end)
    |> Enum.into(%{})
  end

  # 私有函数 - 深度转换关键字列表为映射
  defp deep_convert_keywords_to_maps(map) when is_map(map) do
    Map.new(map, fn {key, value} ->
      {key, deep_convert_keywords_to_maps(value)}
    end)
  end
  defp deep_convert_keywords_to_maps(keyword_list) when is_list(keyword_list) do
    if Keyword.keyword?(keyword_list) do
      keyword_list
      |> Enum.into(%{})
      |> deep_convert_keywords_to_maps()
    else
      Enum.map(keyword_list, &deep_convert_keywords_to_maps/1)
    end
  end
  defp deep_convert_keywords_to_maps(value), do: value
end
