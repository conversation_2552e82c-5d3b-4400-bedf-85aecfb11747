defmodule RacingGame.Live.AdminPanel.Services.Domain.DataService do
  @moduledoc """
  数据管理领域服务
  
  提供数据相关的业务逻辑处理，包括：
  - 下注记录查询和分析
  - 股票持仓数据管理
  - 数据统计和报表
  - 数据导出功能
  """

  require Logger
  alias RacingGame.{Bet, Stock}
  alias RacingGame.Live.AdminPanel.Services.Application.PermissionFilter
  alias Cypridina.Accounts.User
  require Ash.Query

  # 常量定义
  @default_per_page 20
  @max_per_page 100
  @bet_statuses %{0 => "待开奖", 1 => "中奖", 2 => "未中奖"}
  @decimal_precision 2

  # ============================================================================
  # 下注记录相关
  # ============================================================================

  @doc """
  获取下注记录列表

  ## 参数
  - `current_user` - 当前用户
  - `params` - 查询参数

  ## 返回
  - `{:ok, %{bet_records: records, page_info: page_info}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_bet_records(current_user, params \\ %{}) do
    Logger.debug("📋 [数据服务] 获取下注记录列表")
    
    try do
      query_params = normalize_list_params(params)
      
      with {:ok, query} <- build_bet_records_query(current_user, query_params),
           {:ok, results} <- execute_bet_records_query(query, query_params) do
        Logger.info("✅ [数据服务] 成功获取 #{length(results.bet_records)} 条下注记录")
        {:ok, results}
      else
        {:error, reason} -> {:error, reason}
      end
    rescue
      error ->
        Logger.error("❌ [数据服务] 获取下注记录异常: #{inspect(error)}")
        {:error, "获取下注记录失败"}
    end
  end

  @doc """
  获取下注统计数据

  ## 参数
  - `current_user` - 当前用户
  - `params` - 查询参数

  ## 返回
  - `{:ok, statistics}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_bet_statistics(current_user, params \\ %{}) do
    Logger.debug("📊 [数据服务] 获取下注统计数据")
    
    try do
      with {:ok, query} <- build_bet_statistics_query(current_user, params),
           {:ok, stats} <- calculate_bet_statistics(query) do
        Logger.info("✅ [数据服务] 成功获取下注统计数据")
        {:ok, stats}
      else
        {:error, reason} -> {:error, reason}
      end
    rescue
      error ->
        Logger.error("❌ [数据服务] 获取下注统计异常: #{inspect(error)}")
        {:error, "获取下注统计失败"}
    end
  end

  # ============================================================================
  # 股票持仓相关
  # ============================================================================

  @doc """
  获取股票持仓列表

  ## 参数
  - `current_user` - 当前用户
  - `params` - 查询参数

  ## 返回
  - `{:ok, %{stock_holdings: holdings, page_info: page_info}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_stock_holdings(current_user, params \\ %{}) do
    Logger.debug("📋 [数据服务] 获取股票持仓列表")
    
    try do
      query_params = normalize_list_params(params)
      
      with {:ok, query} <- build_stock_holdings_query(current_user, query_params),
           {:ok, results} <- execute_stock_holdings_query(query, query_params) do
        Logger.info("✅ [数据服务] 成功获取 #{length(results.stock_holdings)} 条持仓记录")
        {:ok, results}
      else
        {:error, reason} -> {:error, reason}
      end
    rescue
      error ->
        Logger.error("❌ [数据服务] 获取股票持仓异常: #{inspect(error)}")
        {:error, "获取股票持仓失败"}
    end
  end

  @doc """
  获取股票持仓统计数据

  ## 参数
  - `current_user` - 当前用户
  - `params` - 查询参数

  ## 返回
  - `{:ok, statistics}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_stock_statistics(current_user, params \\ %{}) do
    Logger.debug("📊 [数据服务] 获取股票统计数据")
    
    try do
      with {:ok, query} <- build_stock_statistics_query(current_user, params),
           {:ok, stats} <- calculate_stock_statistics(query) do
        Logger.info("✅ [数据服务] 成功获取股票统计数据")
        {:ok, stats}
      else
        {:error, reason} -> {:error, reason}
      end
    rescue
      error ->
        Logger.error("❌ [数据服务] 获取股票统计异常: #{inspect(error)}")
        {:error, "获取股票统计失败"}
    end
  end

  # ============================================================================
  # 数据导出相关
  # ============================================================================

  @doc """
  导出下注记录

  ## 参数
  - `current_user` - 当前用户
  - `params` - 导出参数

  ## 返回
  - `{:ok, export_data}` - 成功
  - `{:error, reason}` - 失败
  """
  def export_bet_records(current_user, params \\ %{}) do
    Logger.info("📤 [数据服务] 导出下注记录")
    
    try do
      with {:ok, records} <- get_export_bet_records(current_user, params),
           {:ok, export_data} <- format_bet_records_export(records, params) do
        Logger.info("✅ [数据服务] 成功导出 #{length(records)} 条下注记录")
        {:ok, export_data}
      else
        {:error, reason} -> {:error, reason}
      end
    rescue
      error ->
        Logger.error("❌ [数据服务] 导出下注记录异常: #{inspect(error)}")
        {:error, "导出下注记录失败"}
    end
  end

  @doc """
  导出股票持仓

  ## 参数
  - `current_user` - 当前用户
  - `params` - 导出参数

  ## 返回
  - `{:ok, export_data}` - 成功
  - `{:error, reason}` - 失败
  """
  def export_stock_holdings(current_user, params \\ %{}) do
    Logger.info("📤 [数据服务] 导出股票持仓")
    
    try do
      with {:ok, holdings} <- get_export_stock_holdings(current_user, params),
           {:ok, export_data} <- format_stock_holdings_export(holdings, params) do
        Logger.info("✅ [数据服务] 成功导出 #{length(holdings)} 条持仓记录")
        {:ok, export_data}
      else
        {:error, reason} -> {:error, reason}
      end
    rescue
      error ->
        Logger.error("❌ [数据服务] 导出股票持仓异常: #{inspect(error)}")
        {:error, "导出股票持仓失败"}
    end
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 标准化列表参数
  defp normalize_list_params(params) do
    %{
      search_query: String.trim(params[:search_query] || ""),
      page: max(1, params[:page] || 1),
      per_page: min(params[:per_page] || @default_per_page, @max_per_page),
      date_from: params[:date_from],
      date_to: params[:date_to]
    }
  end

  # 构建下注记录查询
  defp build_bet_records_query(current_user, params) do
    try do
      query = Bet
      |> Ash.Query.load([:user])
      |> PermissionFilter.apply_user_filter(current_user, :user_id)
      |> apply_search_filter_for_bets(params.search_query)
      |> apply_date_filter(params.date_from, params.date_to)
      |> Ash.Query.sort(inserted_at: :desc)
      
      {:ok, query}
    rescue
      error ->
        Logger.error("❌ [数据服务] 构建下注查询失败: #{inspect(error)}")
        {:error, "构建查询失败"}
    end
  end

  # 构建股票持仓查询
  defp build_stock_holdings_query(current_user, params) do
    try do
      query = Stock
      |> Ash.Query.load([:user])
      |> PermissionFilter.apply_user_filter(current_user, :user_id)
      |> apply_search_filter_for_stocks(params.search_query)
      |> apply_date_filter(params.date_from, params.date_to)
      |> Ash.Query.sort(updated_at: :desc)
      
      {:ok, query}
    rescue
      error ->
        Logger.error("❌ [数据服务] 构建股票查询失败: #{inspect(error)}")
        {:error, "构建查询失败"}
    end
  end

  # 应用下注记录搜索过滤
  defp apply_search_filter_for_bets(query, ""), do: query
  defp apply_search_filter_for_bets(query, search_query) do
    # 通过关联的用户名进行搜索
    matching_user_ids = get_matching_user_ids(search_query)
    
    if length(matching_user_ids) > 0 do
      Ash.Query.filter(query, user_id in ^matching_user_ids)
    else
      Ash.Query.filter(query, user_id == "no-match-found")
    end
  end

  # 应用股票持仓搜索过滤
  defp apply_search_filter_for_stocks(query, ""), do: query
  defp apply_search_filter_for_stocks(query, search_query) do
    # 通过关联的用户名进行搜索
    matching_user_ids = get_matching_user_ids(search_query)
    
    if length(matching_user_ids) > 0 do
      Ash.Query.filter(query, user_id in ^matching_user_ids)
    else
      Ash.Query.filter(query, user_id == "no-match-found")
    end
  end

  # 获取匹配的用户ID
  defp get_matching_user_ids(search_query) do
    import Ash.Query
    
    case User
         |> filter(contains(username, ^search_query))
         |> select([:id])
         |> Ash.read() do
      {:ok, users} -> Enum.map(users, & &1.id)
      {:error, error} ->
        Logger.warning("⚠️ [数据服务] 搜索用户失败: #{inspect(error)}")
        []
    end
  end

  # 应用日期过滤
  defp apply_date_filter(query, nil, nil), do: query
  defp apply_date_filter(query, date_from, nil) do
    Ash.Query.filter(query, inserted_at >= ^date_from)
  end
  defp apply_date_filter(query, nil, date_to) do
    Ash.Query.filter(query, inserted_at <= ^date_to)
  end
  defp apply_date_filter(query, date_from, date_to) do
    Ash.Query.filter(query, inserted_at >= ^date_from and inserted_at <= ^date_to)
  end

  # 执行下注记录查询
  defp execute_bet_records_query(query, params) do
    offset = (params.page - 1) * params.per_page
    
    case query
         |> Ash.Query.page(count: true, limit: params.per_page, offset: offset)
         |> Ash.read() do
      {:ok, %Ash.Page.Offset{results: bet_records, count: total_count}} ->
        page_info = %{
          total_count: total_count,
          page: params.page,
          per_page: params.per_page
        }
        
        {:ok, %{bet_records: bet_records, page_info: page_info}}
      {:error, error} ->
        Logger.error("❌ [数据服务] 执行下注查询失败: #{inspect(error)}")
        {:error, "查询下注记录失败"}
    end
  end

  # 执行股票持仓查询
  defp execute_stock_holdings_query(query, params) do
    offset = (params.page - 1) * params.per_page
    
    case query
         |> Ash.Query.page(count: true, limit: params.per_page, offset: offset)
         |> Ash.read() do
      {:ok, %Ash.Page.Offset{results: stock_holdings, count: total_count}} ->
        page_info = %{
          total_count: total_count,
          page: params.page,
          per_page: params.per_page
        }
        
        {:ok, %{stock_holdings: stock_holdings, page_info: page_info}}
      {:error, error} ->
        Logger.error("❌ [数据服务] 执行股票查询失败: #{inspect(error)}")
        {:error, "查询股票持仓失败"}
    end
  end

  # 构建下注统计查询
  defp build_bet_statistics_query(current_user, params) do
    # TODO: 实现下注统计查询构建
    {:ok, nil}
  end

  # 构建股票统计查询
  defp build_stock_statistics_query(current_user, params) do
    # TODO: 实现股票统计查询构建
    {:ok, nil}
  end

  # 计算下注统计
  defp calculate_bet_statistics(query) do
    # TODO: 实现下注统计计算
    {:ok, %{}}
  end

  # 计算股票统计
  defp calculate_stock_statistics(query) do
    # TODO: 实现股票统计计算
    {:ok, %{}}
  end

  # 获取导出用的下注记录
  defp get_export_bet_records(current_user, params) do
    # TODO: 实现导出数据获取
    {:ok, []}
  end

  # 获取导出用的股票持仓
  defp get_export_stock_holdings(current_user, params) do
    # TODO: 实现导出数据获取
    {:ok, []}
  end

  # 格式化下注记录导出数据
  defp format_bet_records_export(records, params) do
    # TODO: 实现导出数据格式化
    {:ok, %{}}
  end

  # 格式化股票持仓导出数据
  defp format_stock_holdings_export(holdings, params) do
    # TODO: 实现导出数据格式化
    {:ok, %{}}
  end
end
