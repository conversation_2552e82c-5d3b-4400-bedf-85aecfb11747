defmodule RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.PaymentGatewayRepository do
  @moduledoc """
  💳 支付网关数据访问层

  负责支付网关相关的数据库操作，包括：
  - 支付网关的CRUD操作
  - 网关状态管理
  - 网关类型查询
  - 网关连接测试
  - 网关配置管理
  - 网关统计分析
  """

  require Logger
  alias Ash.Query
  import Ash.Query
  alias Teen.PaymentSystem.PaymentGateway

  # 缓存配置
  @cache_ttl 300  # 5分钟缓存

  # ==================== 基础CRUD操作 ====================

  @doc """
  创建支付网关

  ## 参数
  - `gateway_data` - 网关数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, gateway}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_payment_gateway(gateway_data, options \\ []) do
    Logger.info("💳 [支付网关仓储] 创建支付网关: #{inspect(gateway_data[:name])}")

    try do
      result = PaymentGateway
      |> Ash.Changeset.for_create(:create, gateway_data)
      |> Ash.create()

      case result do
        {:ok, gateway} ->
          Logger.info("✅ [支付网关仓储] 支付网关创建成功: #{gateway.name}")
          clear_cache()
          {:ok, gateway}
        
        {:error, error} ->
          Logger.error("❌ [支付网关仓储] 支付网关创建失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [支付网关仓储] 创建支付网关异常: #{inspect(exception)}")
        {:error, :create_gateway_exception}
    end
  end

  @doc """
  获取支付网关

  ## 参数
  - `gateway_id` - 网关ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, gateway}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_payment_gateway(gateway_id, options \\ []) do
    Logger.info("💳 [支付网关仓储] 获取支付网关: #{gateway_id}")

    try do
      result = PaymentGateway
      |> Ash.Query.for_read(:read)
      |> Ash.Query.filter(id == gateway_id)
      |> maybe_load_associations(options)
      |> Ash.read_one()

      case result do
        {:ok, gateway} when not is_nil(gateway) ->
          Logger.info("✅ [支付网关仓储] 支付网关获取成功: #{gateway.name}")
          {:ok, gateway}
        
        {:ok, nil} ->
          Logger.warn("⚠️ [支付网关仓储] 支付网关不存在: #{gateway_id}")
          {:ok, nil}
        
        {:error, error} ->
          Logger.error("❌ [支付网关仓储] 支付网关获取失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [支付网关仓储] 获取支付网关异常: #{inspect(exception)}")
        {:error, :get_gateway_exception}
    end
  end

  @doc """
  更新支付网关

  ## 参数
  - `gateway_id` - 网关ID
  - `update_data` - 更新数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, gateway}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_payment_gateway(gateway_id, update_data, options \\ []) do
    Logger.info("💳 [支付网关仓储] 更新支付网关: #{gateway_id}")

    try do
      result = PaymentGateway
      |> Ash.get!(gateway_id)
      |> Ash.Changeset.for_update(:update, update_data)
      |> Ash.update()

      case result do
        {:ok, gateway} ->
          Logger.info("✅ [支付网关仓储] 支付网关更新成功: #{gateway.name}")
          clear_cache()
          {:ok, gateway}
        
        {:error, error} ->
          Logger.error("❌ [支付网关仓储] 支付网关更新失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [支付网关仓储] 更新支付网关异常: #{inspect(exception)}")
        {:error, :update_gateway_exception}
    end
  end

  @doc """
  删除支付网关

  ## 参数
  - `gateway_id` - 网关ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, gateway}` - 成功
  - `{:error, reason}` - 失败
  """
  def delete_payment_gateway(gateway_id, options \\ []) do
    Logger.info("💳 [支付网关仓储] 删除支付网关: #{gateway_id}")

    try do
      result = PaymentGateway
      |> Ash.get!(gateway_id)
      |> Ash.destroy()

      case result do
        :ok ->
          Logger.info("✅ [支付网关仓储] 支付网关删除成功: #{gateway_id}")
          clear_cache()
          {:ok, :deleted}
        
        {:error, error} ->
          Logger.error("❌ [支付网关仓储] 支付网关删除失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [支付网关仓储] 删除支付网关异常: #{inspect(exception)}")
        {:error, :delete_gateway_exception}
    end
  end

  # ==================== 查询操作 ====================

  @doc """
  列出支付网关

  ## 参数
  - `params` - 查询参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, {gateways, total_count}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_payment_gateways(params \\ %{}, options \\ []) do
    Logger.info("💳 [支付网关仓储] 列出支付网关: #{inspect(params)}")

    try do
      query = PaymentGateway
      |> Ash.Query.for_read(:read)
      |> apply_filters(params)
      |> apply_sorting(params)
      |> maybe_load_associations(options)

      # 获取总数
      total_count_result = query |> Ash.count()
      
      # 应用分页
      paginated_query = apply_pagination(query, params)
      
      # 执行查询
      gateways_result = paginated_query |> Ash.read()

      case {gateways_result, total_count_result} do
        {{:ok, gateways}, {:ok, total_count}} ->
          Logger.info("✅ [支付网关仓储] 支付网关列表获取成功: #{length(gateways)}/#{total_count}")
          {:ok, {gateways, total_count}}
        
        {{:error, error}, _} ->
          Logger.error("❌ [支付网关仓储] 支付网关列表获取失败: #{inspect(error)}")
          {:error, error}
        
        {_, {:error, error}} ->
          Logger.error("❌ [支付网关仓储] 支付网关计数失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [支付网关仓储] 列出支付网关异常: #{inspect(exception)}")
        {:error, :list_gateways_exception}
    end
  end

  @doc """
  获取活跃支付网关

  ## 参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, gateways}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_active_payment_gateways(options \\ []) do
    Logger.info("💳 [支付网关仓储] 获取活跃支付网关")

    try do
      result = PaymentGateway
      |> Ash.Query.for_read(:list_active_gateways)
      |> maybe_load_associations(options)
      |> Ash.read()

      case result do
        {:ok, gateways} ->
          Logger.info("✅ [支付网关仓储] 活跃支付网关获取成功: #{length(gateways)}个")
          {:ok, gateways}
        
        {:error, error} ->
          Logger.error("❌ [支付网关仓储] 活跃支付网关获取失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [支付网关仓储] 获取活跃支付网关异常: #{inspect(exception)}")
        {:error, :list_active_gateways_exception}
    end
  end

  @doc """
  根据类型获取支付网关

  ## 参数
  - `gateway_type` - 网关类型
  - `options` - 选项参数

  ## 返回
  - `{:ok, gateways}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_payment_gateways_by_type(gateway_type, options \\ []) do
    Logger.info("💳 [支付网关仓储] 根据类型获取支付网关: #{gateway_type}")

    try do
      result = PaymentGateway
      |> Ash.Query.for_read(:list_by_type, %{gateway_type: gateway_type})
      |> maybe_load_associations(options)
      |> Ash.read()

      case result do
        {:ok, gateways} ->
          Logger.info("✅ [支付网关仓储] 类型网关获取成功: #{length(gateways)}个")
          {:ok, gateways}
        
        {:error, error} ->
          Logger.error("❌ [支付网关仓储] 类型网关获取失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [支付网关仓储] 根据类型获取支付网关异常: #{inspect(exception)}")
        {:error, :list_gateways_by_type_exception}
    end
  end

  # ==================== 状态管理操作 ====================

  @doc """
  激活支付网关

  ## 参数
  - `gateway_id` - 网关ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, gateway}` - 成功
  - `{:error, reason}` - 失败
  """
  def activate_payment_gateway(gateway_id, options \\ []) do
    Logger.info("💳 [支付网关仓储] 激活支付网关: #{gateway_id}")

    try do
      result = PaymentGateway
      |> Ash.get!(gateway_id)
      |> Ash.Changeset.for_update(:enable)
      |> Ash.update()

      case result do
        {:ok, gateway} ->
          Logger.info("✅ [支付网关仓储] 支付网关激活成功: #{gateway.name}")
          clear_cache()
          {:ok, gateway}
        
        {:error, error} ->
          Logger.error("❌ [支付网关仓储] 支付网关激活失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [支付网关仓储] 激活支付网关异常: #{inspect(exception)}")
        {:error, :activate_gateway_exception}
    end
  end

  @doc """
  禁用支付网关

  ## 参数
  - `gateway_id` - 网关ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, gateway}` - 成功
  - `{:error, reason}` - 失败
  """
  def deactivate_payment_gateway(gateway_id, options \\ []) do
    Logger.info("💳 [支付网关仓储] 禁用支付网关: #{gateway_id}")

    try do
      result = PaymentGateway
      |> Ash.get!(gateway_id)
      |> Ash.Changeset.for_update(:disable)
      |> Ash.update()

      case result do
        {:ok, gateway} ->
          Logger.info("✅ [支付网关仓储] 支付网关禁用成功: #{gateway.name}")
          clear_cache()
          {:ok, gateway}
        
        {:error, error} ->
          Logger.error("❌ [支付网关仓储] 支付网关禁用失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [支付网关仓储] 禁用支付网关异常: #{inspect(exception)}")
        {:error, :deactivate_gateway_exception}
    end
  end

  # ==================== 私有辅助函数 ====================

  # 应用过滤条件
  defp apply_filters(query, params) do
    Enum.reduce(params, query, fn
      {"name", name}, query when is_binary(name) and name != "" ->
        Ash.Query.filter(query, contains(name, name))
      
      {"gateway_type", gateway_type}, query when is_binary(gateway_type) and gateway_type != "" ->
        Ash.Query.filter(query, gateway_type  == gateway_type)
      
      {"status", status}, query when is_integer(status) ->
        Ash.Query.filter(query, status  == status)
      
      {"merchant_id", merchant_id}, query when is_binary(merchant_id) and merchant_id != "" ->
        Ash.Query.filter(query, merchant_id  == merchant_id)
      
      _other, query ->
        query
    end)
  end

  # 应用排序
  defp apply_sorting(query, params) do
    case params do
      %{"sort_by" => sort_by, "sort_order" => sort_order} ->
        sort_field = String.to_existing_atom(sort_by)
        case sort_order do
          "desc" -> Ash.Query.sort(query, desc: sort_field)
          _ -> Ash.Query.sort(query, asc: sort_field)
        end
      
      %{"sort_by" => sort_by} ->
        sort_field = String.to_existing_atom(sort_by)
        Ash.Query.sort(query, asc: sort_field)
      
      _ ->
        Ash.Query.sort(query, desc: :inserted_at)
    end
  end

  # 应用分页
  defp apply_pagination(query, params) do
    page = Map.get(params, "page", 1) |> ensure_integer()
    page_size = Map.get(params, "page_size", 20) |> ensure_integer()
    offset = (page - 1) * page_size

    query
    |> Ash.Query.limit(page_size)
    |> Ash.Query.offset(offset)
  end

  # 加载关联数据
  defp maybe_load_associations(query, options) do
    case Keyword.get(options, :load, []) do
      [] -> query
      associations -> Ash.Query.load(query, associations)
    end
  end

  # 确保整数类型
  defp ensure_integer(value) when is_integer(value), do: value
  defp ensure_integer(value) when is_binary(value) do
    case Integer.parse(value) do
      {int, ""} -> int
      _ -> 1
    end
  end
  defp ensure_integer(_), do: 1

  # 清除缓存
  defp clear_cache do
    # 这里可以实现缓存清除逻辑
    :ok
  end
end
