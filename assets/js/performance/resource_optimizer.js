/**
 * Racing Game Admin Panel - 资源优化系统
 * 
 * 实现代码分割、资源预加载、缓存管理等优化功能
 */

/**
 * 资源优化管理器
 */
class ResourceOptimizer {
  constructor(options = {}) {
    this.options = {
      enableCodeSplitting: true,
      enablePreloading: true,
      enableCaching: true,
      cacheVersion: '1.0.0',
      maxCacheSize: 50 * 1024 * 1024, // 50MB
      preloadThreshold: 0.5, // 50%概率预加载
      ...options
    };

    this.cache = new Map();
    this.preloadedResources = new Set();
    this.loadingPromises = new Map();
    this.resourceStats = new Map();
    
    this.init();
  }

  init() {
    this.setupServiceWorker();
    this.setupResourceHints();
    this.setupIntersectionObserver();
    this.startResourceMonitoring();
  }

  /**
   * 设置Service Worker
   */
  async setupServiceWorker() {
    if ('serviceWorker' in navigator && this.options.enableCaching) {
      try {
        const registration = await navigator.serviceWorker.register('/sw.js');
        console.log('🔧 Service Worker registered:', registration);
        
        // 监听Service Worker更新
        registration.addEventListener('updatefound', () => {
          console.log('🔄 Service Worker update found');
        });
      } catch (error) {
        console.warn('Service Worker registration failed:', error);
      }
    }
  }

  /**
   * 设置资源提示
   */
  setupResourceHints() {
    // DNS预解析
    this.addDNSPrefetch([
      '//fonts.googleapis.com',
      '//cdn.jsdelivr.net',
      '//unpkg.com'
    ]);

    // 预连接关键域名
    this.addPreconnect([
      'https://fonts.googleapis.com',
      'https://fonts.gstatic.com'
    ]);

    // 预加载关键资源
    this.preloadCriticalResources();
  }

  /**
   * 添加DNS预解析
   */
  addDNSPrefetch(domains) {
    domains.forEach(domain => {
      const link = document.createElement('link');
      link.rel = 'dns-prefetch';
      link.href = domain;
      document.head.appendChild(link);
    });
  }

  /**
   * 添加预连接
   */
  addPreconnect(urls) {
    urls.forEach(url => {
      const link = document.createElement('link');
      link.rel = 'preconnect';
      link.href = url;
      link.crossOrigin = 'anonymous';
      document.head.appendChild(link);
    });
  }

  /**
   * 预加载关键资源
   */
  preloadCriticalResources() {
    const criticalResources = [
      { href: '/css/app.css', as: 'style' },
      { href: '/js/app.js', as: 'script' },
      { href: '/fonts/inter-var.woff2', as: 'font', type: 'font/woff2', crossorigin: 'anonymous' }
    ];

    criticalResources.forEach(resource => {
      this.preloadResource(resource);
    });
  }

  /**
   * 预加载资源
   */
  preloadResource(resource) {
    if (this.preloadedResources.has(resource.href)) {
      return;
    }

    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = resource.href;
    link.as = resource.as;
    
    if (resource.type) {
      link.type = resource.type;
    }
    
    if (resource.crossorigin) {
      link.crossOrigin = resource.crossorigin;
    }

    link.onload = () => {
      this.preloadedResources.add(resource.href);
      console.log(`✅ Preloaded: ${resource.href}`);
    };

    link.onerror = () => {
      console.warn(`❌ Failed to preload: ${resource.href}`);
    };

    document.head.appendChild(link);
  }

  /**
   * 设置交叉观察器用于智能预加载
   */
  setupIntersectionObserver() {
    if (!('IntersectionObserver' in window)) return;

    this.preloadObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this.handleIntersection(entry.target);
        }
      });
    }, {
      rootMargin: '100px'
    });
  }

  /**
   * 处理元素进入视口
   */
  handleIntersection(element) {
    const preloadHref = element.dataset.preload;
    if (preloadHref && Math.random() < this.options.preloadThreshold) {
      this.preloadResource({ href: preloadHref, as: 'script' });
    }
  }

  /**
   * 动态导入模块
   */
  async importModule(modulePath) {
    if (this.loadingPromises.has(modulePath)) {
      return this.loadingPromises.get(modulePath);
    }

    const loadingPromise = this.loadModuleWithFallback(modulePath);
    this.loadingPromises.set(modulePath, loadingPromise);

    try {
      const module = await loadingPromise;
      this.loadingPromises.delete(modulePath);
      this.recordResourceLoad(modulePath, 'success');
      return module;
    } catch (error) {
      this.loadingPromises.delete(modulePath);
      this.recordResourceLoad(modulePath, 'error', error);
      throw error;
    }
  }

  /**
   * 带回退的模块加载
   */
  async loadModuleWithFallback(modulePath) {
    try {
      return await import(modulePath);
    } catch (error) {
      console.warn(`Failed to load module ${modulePath}, trying fallback...`);
      
      // 尝试从CDN加载
      const cdnPath = this.getCDNPath(modulePath);
      if (cdnPath) {
        return await import(cdnPath);
      }
      
      throw error;
    }
  }

  /**
   * 获取CDN路径
   */
  getCDNPath(modulePath) {
    const cdnMappings = {
      'chart.js': 'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js',
      'lodash': 'https://cdn.jsdelivr.net/npm/lodash@4.17.21/lodash.min.js'
    };

    const moduleName = modulePath.split('/').pop().replace('.js', '');
    return cdnMappings[moduleName];
  }

  /**
   * 记录资源加载统计
   */
  recordResourceLoad(resource, status, error = null) {
    if (!this.resourceStats.has(resource)) {
      this.resourceStats.set(resource, {
        attempts: 0,
        successes: 0,
        errors: 0,
        lastError: null
      });
    }

    const stats = this.resourceStats.get(resource);
    stats.attempts++;
    
    if (status === 'success') {
      stats.successes++;
    } else {
      stats.errors++;
      stats.lastError = error;
    }
  }

  /**
   * 缓存管理
   */
  async cacheResource(url, data) {
    if (!this.options.enableCaching) return;

    try {
      const cache = await caches.open(`racing-admin-${this.options.cacheVersion}`);
      const response = new Response(data);
      await cache.put(url, response);
      
      this.cache.set(url, {
        data,
        timestamp: Date.now(),
        size: new Blob([data]).size
      });

      this.cleanupCache();
    } catch (error) {
      console.warn('Failed to cache resource:', error);
    }
  }

  /**
   * 从缓存获取资源
   */
  async getCachedResource(url) {
    if (!this.options.enableCaching) return null;

    try {
      const cache = await caches.open(`racing-admin-${this.options.cacheVersion}`);
      const response = await cache.match(url);
      
      if (response) {
        return await response.text();
      }
    } catch (error) {
      console.warn('Failed to get cached resource:', error);
    }

    return null;
  }

  /**
   * 清理缓存
   */
  async cleanupCache() {
    const totalSize = Array.from(this.cache.values())
      .reduce((sum, item) => sum + item.size, 0);

    if (totalSize > this.options.maxCacheSize) {
      // 按时间戳排序，删除最旧的项目
      const sortedEntries = Array.from(this.cache.entries())
        .sort((a, b) => a[1].timestamp - b[1].timestamp);

      let removedSize = 0;
      const targetSize = this.options.maxCacheSize * 0.8; // 清理到80%

      for (const [url, data] of sortedEntries) {
        this.cache.delete(url);
        removedSize += data.size;
        
        // 也从Cache API中删除
        try {
          const cache = await caches.open(`racing-admin-${this.options.cacheVersion}`);
          await cache.delete(url);
        } catch (error) {
          console.warn('Failed to delete from cache:', error);
        }

        if (totalSize - removedSize <= targetSize) {
          break;
        }
      }

      console.log(`🧹 Cache cleanup: removed ${removedSize} bytes`);
    }
  }

  /**
   * 开始资源监控
   */
  startResourceMonitoring() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach(entry => {
          this.analyzeResourcePerformance(entry);
        });
      });

      observer.observe({ entryTypes: ['resource'] });
    }
  }

  /**
   * 分析资源性能
   */
  analyzeResourcePerformance(entry) {
    const analysis = {
      url: entry.name,
      duration: entry.duration,
      size: entry.transferSize,
      cached: entry.transferSize === 0,
      type: entry.initiatorType,
      timing: {
        dns: entry.domainLookupEnd - entry.domainLookupStart,
        connect: entry.connectEnd - entry.connectStart,
        request: entry.responseStart - entry.requestStart,
        response: entry.responseEnd - entry.responseStart
      }
    };

    // 检查性能问题
    if (analysis.duration > 1000) {
      console.warn(`🐌 Slow resource: ${analysis.url} (${analysis.duration.toFixed(2)}ms)`);
    }

    if (analysis.size > 1024 * 1024 && !analysis.cached) {
      console.warn(`📦 Large uncached resource: ${analysis.url} (${(analysis.size / 1024 / 1024).toFixed(2)}MB)`);
    }

    // 建议优化
    this.suggestOptimizations(analysis);
  }

  /**
   * 建议优化
   */
  suggestOptimizations(analysis) {
    const suggestions = [];

    if (analysis.timing.dns > 100) {
      suggestions.push('Consider DNS prefetch for this domain');
    }

    if (analysis.timing.connect > 200) {
      suggestions.push('Consider preconnect for this domain');
    }

    if (analysis.size > 500 * 1024 && analysis.type === 'script') {
      suggestions.push('Consider code splitting for this JavaScript file');
    }

    if (analysis.size > 100 * 1024 && analysis.type === 'img') {
      suggestions.push('Consider image optimization (WebP, compression)');
    }

    if (suggestions.length > 0) {
      console.log(`💡 Optimization suggestions for ${analysis.url}:`, suggestions);
    }
  }

  /**
   * 预取下一页资源
   */
  prefetchNextPage(nextPageUrl) {
    if (this.preloadedResources.has(nextPageUrl)) return;

    const link = document.createElement('link');
    link.rel = 'prefetch';
    link.href = nextPageUrl;
    
    link.onload = () => {
      this.preloadedResources.add(nextPageUrl);
      console.log(`🔮 Prefetched next page: ${nextPageUrl}`);
    };

    document.head.appendChild(link);
  }

  /**
   * 获取资源统计
   */
  getResourceStats() {
    return {
      preloadedCount: this.preloadedResources.size,
      cacheSize: this.cache.size,
      loadingCount: this.loadingPromises.size,
      resourceStats: Object.fromEntries(this.resourceStats)
    };
  }

  /**
   * 优化图片加载
   */
  optimizeImageLoading() {
    const images = document.querySelectorAll('img[data-src]');
    
    images.forEach(img => {
      // 添加到懒加载观察器
      if (this.preloadObserver) {
        this.preloadObserver.observe(img);
      }

      // 检查是否支持WebP
      if (this.supportsWebP()) {
        const webpSrc = img.dataset.src.replace(/\.(jpg|jpeg|png)$/i, '.webp');
        img.dataset.src = webpSrc;
      }
    });
  }

  /**
   * 检查WebP支持
   */
  supportsWebP() {
    if (this._webpSupport !== undefined) {
      return this._webpSupport;
    }

    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    
    this._webpSupport = canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
    return this._webpSupport;
  }

  /**
   * 批量预加载资源
   */
  async batchPreload(resources, concurrency = 3) {
    const chunks = [];
    for (let i = 0; i < resources.length; i += concurrency) {
      chunks.push(resources.slice(i, i + concurrency));
    }

    for (const chunk of chunks) {
      await Promise.allSettled(
        chunk.map(resource => this.preloadResource(resource))
      );
    }
  }

  /**
   * 销毁优化器
   */
  destroy() {
    if (this.preloadObserver) {
      this.preloadObserver.disconnect();
    }
    
    this.cache.clear();
    this.preloadedResources.clear();
    this.loadingPromises.clear();
    this.resourceStats.clear();
  }
}

/**
 * 代码分割助手
 */
class CodeSplitter {
  static async loadComponent(componentName) {
    const componentMap = {
      'UserManagement': () => import('../components/user_management'),
      'PaymentManagement': () => import('../components/payment_management'),
      'DataStatistics': () => import('../components/data_statistics'),
      'SystemSettings': () => import('../components/system_settings'),
      'ChartComponents': () => import('../components/charts'),
      'TableComponents': () => import('../components/tables')
    };

    const loader = componentMap[componentName];
    if (!loader) {
      throw new Error(`Unknown component: ${componentName}`);
    }

    return await loader();
  }

  static async loadUtility(utilityName) {
    const utilityMap = {
      'lodash': () => import('lodash'),
      'moment': () => import('moment'),
      'chartjs': () => import('chart.js')
    };

    const loader = utilityMap[utilityName];
    if (!loader) {
      throw new Error(`Unknown utility: ${utilityName}`);
    }

    return await loader();
  }
}

// 全局实例
window.resourceOptimizer = new ResourceOptimizer();

// Phoenix LiveView Hook
const ResourceOptimizerHook = {
  mounted() {
    // 优化当前页面的图片
    window.resourceOptimizer.optimizeImageLoading();
    
    // 预取可能的下一页
    const nextPageLink = this.el.querySelector('[data-prefetch]');
    if (nextPageLink) {
      window.resourceOptimizer.prefetchNextPage(nextPageLink.href);
    }
  },

  updated() {
    // 页面更新后重新优化图片
    window.resourceOptimizer.optimizeImageLoading();
  }
};

// 导出
export { ResourceOptimizer, CodeSplitter, ResourceOptimizerHook };
