# 组件关系图文档

## 📋 概述

本文档通过详细的组件关系图和交互流程图，展示 Racing Game Admin Panel 系统各组件之间的关系、依赖和数据流向，帮助开发人员理解系统架构和组件交互。

## 🏗️ 系统组件总览图

### 高层架构组件图
```mermaid
graph TB
    subgraph "Presentation Layer 表现层"
        UI[Web UI<br/>Phoenix LiveView]
        API[REST API<br/>Phoenix Router]
        WS[WebSocket<br/>Phoenix Channels]
    end
    
    subgraph "Business Logic Layer 业务逻辑层"
        CS[CustomerService<br/>用户管理]
        PS[PaymentSystem<br/>支付管理]
        GM[GameManagement<br/>游戏管理]
        SS[SystemSettings<br/>系统设置]
        AS[ActivitySystem<br/>活动管理]
        DS[DataStatistics<br/>数据统计]
        PRS[PromotionSystem<br/>推广管理]
    end
    
    subgraph "Data Access Layer 数据访问层"
        REPO[Repository Layer<br/>数据仓库层]
        QB[QueryBuilder Layer<br/>查询构建层]
        CACHE[Cache Layer<br/>缓存层]
    end
    
    subgraph "Infrastructure Layer 基础设施层"
        PG[(PostgreSQL<br/>主数据库)]
        REDIS[(Redis<br/>缓存数据库)]
        S3[(AWS S3<br/>文件存储)]
        MQ[Message Queue<br/>消息队列]
    end
    
    UI --> CS
    UI --> PS
    UI --> GM
    API --> CS
    API --> PS
    API --> GM
    WS --> CS
    
    CS --> REPO
    PS --> REPO
    GM --> REPO
    SS --> REPO
    AS --> REPO
    DS --> QB
    PRS --> REPO
    
    REPO --> PG
    QB --> PG
    CACHE --> REDIS
    REPO --> CACHE
    
    CS --> MQ
    PS --> MQ
    GM --> S3
```

## 🔧 核心业务组件详细图

### 用户管理组件 (CustomerService)
```mermaid
graph LR
    subgraph "CustomerService 用户管理"
        UR[UserRepository<br/>用户数据仓库]
        US[UserService<br/>用户业务服务]
        UQ[UserQueryBuilder<br/>用户查询构建]
        CR[ComplaintRepository<br/>投诉数据仓库]
        CS[ComplaintService<br/>投诉业务服务]
        CQ[ComplaintQueryBuilder<br/>投诉查询构建]
    end
    
    subgraph "External Dependencies"
        DB[(PostgreSQL)]
        CACHE[(Redis)]
        SMS[SMS Service<br/>短信服务]
        EMAIL[Email Service<br/>邮件服务]
    end
    
    US --> UR
    US --> SMS
    US --> EMAIL
    CS --> CR
    CS --> US
    UQ --> DB
    CQ --> DB
    UR --> DB
    UR --> CACHE
    CR --> DB
```

### 支付管理组件 (PaymentSystem)
```mermaid
graph LR
    subgraph "PaymentSystem 支付管理"
        PR[PaymentRepository<br/>支付数据仓库]
        PS[PaymentService<br/>支付业务服务]
        PQ[PaymentQueryBuilder<br/>支付查询构建]
        RR[RefundRepository<br/>退款数据仓库]
        RS[RefundService<br/>退款业务服务]
        RQ[RefundQueryBuilder<br/>退款查询构建]
    end
    
    subgraph "External Dependencies"
        DB[(PostgreSQL)]
        CACHE[(Redis)]
        PAY[Payment Gateway<br/>支付网关]
        BANK[Bank API<br/>银行接口]
    end
    
    PS --> PR
    PS --> PAY
    PS --> BANK
    RS --> RR
    RS --> PS
    PQ --> DB
    RQ --> DB
    PR --> DB
    PR --> CACHE
    RR --> DB
```

### 游戏管理组件 (GameManagement)
```mermaid
graph LR
    subgraph "GameManagement 游戏管理"
        GR[GameRepository<br/>游戏数据仓库]
        GS[GameService<br/>游戏业务服务]
        GQ[GameQueryBuilder<br/>游戏查询构建]
        GCR[GameConfigRepository<br/>游戏配置仓库]
        GCS[GameConfigService<br/>游戏配置服务]
        GCQ[GameConfigQueryBuilder<br/>配置查询构建]
    end
    
    subgraph "External Dependencies"
        DB[(PostgreSQL)]
        CACHE[(Redis)]
        S3[(AWS S3)]
        GAME[Game Engine<br/>游戏引擎]
    end
    
    GS --> GR
    GS --> GAME
    GCS --> GCR
    GCS --> S3
    GQ --> DB
    GCQ --> DB
    GR --> DB
    GR --> CACHE
    GCR --> DB
```

## 📊 数据流程图

### 用户注册流程
```mermaid
sequenceDiagram
    participant U as User
    participant UI as Web UI
    participant US as UserService
    participant UR as UserRepository
    participant SMS as SMS Service
    participant DB as PostgreSQL
    participant CACHE as Redis
    
    U->>UI: 提交注册信息
    UI->>US: 调用用户注册服务
    US->>UR: 检查用户是否存在
    UR->>DB: 查询用户数据
    DB-->>UR: 返回查询结果
    UR-->>US: 返回检查结果
    
    alt 用户不存在
        US->>SMS: 发送验证码
        SMS-->>US: 发送成功
        US->>UR: 创建临时用户记录
        UR->>DB: 插入临时数据
        DB-->>UR: 插入成功
        UR-->>US: 创建成功
        US-->>UI: 返回验证码发送成功
        UI-->>U: 显示验证码输入界面
        
        U->>UI: 输入验证码
        UI->>US: 验证验证码
        US->>UR: 激活用户账号
        UR->>DB: 更新用户状态
        UR->>CACHE: 缓存用户信息
        DB-->>UR: 更新成功
        UR-->>US: 激活成功
        US-->>UI: 注册完成
        UI-->>U: 显示注册成功
    else 用户已存在
        US-->>UI: 返回用户已存在错误
        UI-->>U: 显示错误信息
    end
```

### 支付处理流程
```mermaid
sequenceDiagram
    participant U as User
    participant UI as Web UI
    participant PS as PaymentService
    participant PR as PaymentRepository
    participant PAY as Payment Gateway
    participant DB as PostgreSQL
    participant MQ as Message Queue
    
    U->>UI: 发起支付请求
    UI->>PS: 调用支付服务
    PS->>PR: 创建支付订单
    PR->>DB: 插入订单数据
    DB-->>PR: 插入成功
    PR-->>PS: 订单创建成功
    
    PS->>PAY: 调用支付网关
    PAY-->>PS: 返回支付链接
    PS-->>UI: 返回支付信息
    UI-->>U: 跳转到支付页面
    
    U->>PAY: 完成支付
    PAY->>PS: 支付结果回调
    PS->>PR: 更新订单状态
    PR->>DB: 更新订单数据
    DB-->>PR: 更新成功
    PR-->>PS: 状态更新成功
    
    PS->>MQ: 发送支付成功消息
    MQ-->>PS: 消息发送成功
    PS-->>PAY: 返回处理成功
    
    Note over MQ: 异步处理业务逻辑
    MQ->>PS: 处理支付成功事件
    PS->>PR: 执行后续业务逻辑
```

### 数据查询流程
```mermaid
sequenceDiagram
    participant UI as Web UI
    participant QB as QueryBuilder
    participant CACHE as Redis
    participant DB as PostgreSQL
    participant REPO as Repository
    
    UI->>QB: 请求复杂查询
    QB->>QB: 构建查询条件
    QB->>CACHE: 检查缓存
    
    alt 缓存命中
        CACHE-->>QB: 返回缓存数据
        QB-->>UI: 返回查询结果
    else 缓存未命中
        QB->>DB: 执行数据库查询
        DB-->>QB: 返回查询结果
        QB->>CACHE: 缓存查询结果
        QB-->>UI: 返回查询结果
    end
    
    Note over UI,DB: 对于实时性要求高的查询，跳过缓存直接查询数据库
```

## 🔄 组件交互模式

### 分层交互模式
```mermaid
graph TD
    subgraph "Layer Interaction Pattern"
        L1[Presentation Layer<br/>表现层]
        L2[Business Logic Layer<br/>业务逻辑层]
        L3[Data Access Layer<br/>数据访问层]
        L4[Infrastructure Layer<br/>基础设施层]
    end
    
    L1 -->|HTTP/WebSocket| L2
    L2 -->|Function Call| L3
    L3 -->|SQL/Cache Query| L4
    
    L1 -.->|Direct Access<br/>静态资源| L4
    L2 -.->|Event Publishing<br/>事件发布| L4
```

### 服务间通信模式
```mermaid
graph LR
    subgraph "Synchronous Communication 同步通信"
        A[Service A] -->|Function Call| B[Service B]
        B -->|Return Result| A
    end
    
    subgraph "Asynchronous Communication 异步通信"
        C[Service C] -->|Publish Event| MQ[Message Queue]
        MQ -->|Subscribe Event| D[Service D]
    end
    
    subgraph "Cache-Aside Pattern 缓存模式"
        E[Service E] -->|Check Cache| CACHE[Redis]
        E -->|Query DB| DB[(PostgreSQL)]
        E -->|Update Cache| CACHE
    end
```

## 🔍 组件依赖关系

### 依赖层次图
```mermaid
graph TB
    subgraph "Level 1 - Infrastructure 基础设施"
        PG[(PostgreSQL)]
        REDIS[(Redis)]
        S3[(AWS S3)]
        MQ[Message Queue]
    end
    
    subgraph "Level 2 - Data Access 数据访问"
        REPO[Repository Layer]
        QB[QueryBuilder Layer]
        CACHE[Cache Layer]
    end
    
    subgraph "Level 3 - Business Logic 业务逻辑"
        CS[CustomerService]
        PS[PaymentSystem]
        GM[GameManagement]
        SS[SystemSettings]
    end
    
    subgraph "Level 4 - Presentation 表现层"
        UI[Web UI]
        API[REST API]
        WS[WebSocket]
    end
    
    REPO --> PG
    REPO --> REDIS
    QB --> PG
    CACHE --> REDIS
    
    CS --> REPO
    CS --> QB
    CS --> CACHE
    PS --> REPO
    PS --> QB
    GM --> REPO
    GM --> S3
    SS --> REPO
    
    UI --> CS
    UI --> PS
    UI --> GM
    API --> CS
    API --> PS
    WS --> CS
```

### 循环依赖检测
```mermaid
graph LR
    subgraph "Good Design 良好设计"
        A[Service A] --> C[Common Service]
        B[Service B] --> C
        C --> D[Data Layer]
    end
    
    subgraph "Bad Design 不良设计"
        E[Service E] --> F[Service F]
        F --> E
        style E fill:#ffcccc
        style F fill:#ffcccc
    end
```

## 📈 性能关键路径

### 关键性能路径图
```mermaid
graph LR
    subgraph "Critical Path 关键路径"
        START([用户请求]) --> AUTH[身份认证]
        AUTH --> CACHE{缓存检查}
        CACHE -->|命中| RETURN[返回结果]
        CACHE -->|未命中| DB[数据库查询]
        DB --> CACHE_UPDATE[更新缓存]
        CACHE_UPDATE --> RETURN
        RETURN --> END([响应用户])
    end
    
    subgraph "Performance Metrics 性能指标"
        AUTH -.->|< 50ms| P1[认证时间]
        CACHE -.->|< 10ms| P2[缓存查询时间]
        DB -.->|< 100ms| P3[数据库查询时间]
        CACHE_UPDATE -.->|< 20ms| P4[缓存更新时间]
    end
```

### 并发处理模式
```mermaid
graph TB
    subgraph "Concurrent Processing 并发处理"
        REQ[用户请求] --> LB[负载均衡器]
        LB --> P1[进程1]
        LB --> P2[进程2]
        LB --> P3[进程3]
        
        P1 --> POOL[连接池]
        P2 --> POOL
        P3 --> POOL
        
        POOL --> DB[(数据库)]
    end
    
    subgraph "Resource Management 资源管理"
        POOL -.->|最大连接数: 20| LIMIT1[连接限制]
        P1 -.->|最大并发: 1000| LIMIT2[进程限制]
        LB -.->|请求分发策略| STRATEGY[负载策略]
    end
```

## 🔒 安全组件交互

### 安全边界图
```mermaid
graph TB
    subgraph "External Zone 外部区域"
        USER[用户]
        ATTACKER[攻击者]
    end
    
    subgraph "DMZ 非军事区"
        LB[负载均衡器]
        WAF[Web应用防火墙]
    end
    
    subgraph "Application Zone 应用区域"
        AUTH[认证服务]
        APP[应用服务器]
    end
    
    subgraph "Data Zone 数据区域"
        DB[(数据库)]
        CACHE[(缓存)]
    end
    
    USER --> WAF
    ATTACKER -.->|阻止| WAF
    WAF --> LB
    LB --> AUTH
    AUTH --> APP
    APP --> DB
    APP --> CACHE
    
    style WAF fill:#90EE90
    style AUTH fill:#87CEEB
    style DB fill:#FFB6C1
```

### 权限控制流程
```mermaid
sequenceDiagram
    participant U as User
    participant AUTH as Auth Service
    participant RBAC as RBAC Service
    participant APP as Application
    participant DB as Database
    
    U->>AUTH: 登录请求
    AUTH->>DB: 验证用户凭证
    DB-->>AUTH: 返回用户信息
    AUTH->>RBAC: 获取用户权限
    RBAC->>DB: 查询角色权限
    DB-->>RBAC: 返回权限列表
    RBAC-->>AUTH: 返回用户权限
    AUTH-->>U: 返回访问令牌
    
    U->>APP: 业务请求 + 令牌
    APP->>AUTH: 验证令牌
    AUTH-->>APP: 令牌有效
    APP->>RBAC: 检查操作权限
    RBAC-->>APP: 权限检查结果
    
    alt 有权限
        APP->>DB: 执行业务操作
        DB-->>APP: 返回操作结果
        APP-->>U: 返回业务结果
    else 无权限
        APP-->>U: 返回权限不足错误
    end
```

## 📊 监控和可观测性组件

### 监控架构图
```mermaid
graph TB
    subgraph "Application Layer 应用层"
        APP1[应用实例1]
        APP2[应用实例2]
        APP3[应用实例3]
    end
    
    subgraph "Metrics Collection 指标收集"
        PROM[Prometheus]
        TELE[Telemetry]
    end
    
    subgraph "Log Collection 日志收集"
        FLUENTD[Fluentd]
        ELK[ELK Stack]
    end
    
    subgraph "Visualization 可视化"
        GRAFANA[Grafana]
        KIBANA[Kibana]
    end
    
    subgraph "Alerting 告警"
        ALERT[AlertManager]
        NOTIFY[通知服务]
    end
    
    APP1 --> TELE
    APP2 --> TELE
    APP3 --> TELE
    TELE --> PROM
    
    APP1 --> FLUENTD
    APP2 --> FLUENTD
    APP3 --> FLUENTD
    FLUENTD --> ELK
    
    PROM --> GRAFANA
    PROM --> ALERT
    ELK --> KIBANA
    
    ALERT --> NOTIFY
```

---

**文档版本**: v1.0  
**创建日期**: 2024-12-19  
**最后更新**: 2024-12-19  
**维护团队**: Racing Game Development Team
