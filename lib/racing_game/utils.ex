defmodule RacingGame.Utils do
  @moduledoc """
  RacingGame 工具模块集合

  这个模块提供了一个统一的接口来访问所有工具模块的功能。
  您可以通过这个模块访问文本处理、类型转换、验证和通用工具等功能。

  ## 子模块
  - `TextUtils` - 文本处理工具（截断、清理等）
  - `TypeConverter` - 类型转换工具（安全转换）
  - `Validator` - 验证工具（数据验证）
  - `CommonUtils` - 通用工具（ID生成、映射操作等）

  ## 使用方式

  ### 直接使用子模块
      alias RacingGame.Utils.TextUtils
      alias RacingGame.Utils.TypeConverter
      alias RacingGame.Utils.Validator
      alias RacingGame.Utils.CommonUtils

  ### 使用统一接口
      alias RacingGame.Utils

      Utils.truncate_text("长文本", :title)
      Utils.safe_convert("123", :integer)
      Utils.validate("test", :present)
      Utils.generate_unique_id("prefix_")

  ## 快速参考

  ### 文本处理
  - `truncate_text/2` - 截断文本
  - `smart_truncate/2` - 智能截断
  - `sanitize_string/1` - 清理字符串

  ### 类型转换
  - `safe_convert/3` - 安全类型转换
  - `safe_to_string/2` - 安全字符串转换
  - `safe_to_integer/2` - 安全整数转换
  - `safe_to_boolean/2` - 安全布尔值转换
  - `format_time/2` - 时间格式化

  ### 验证
  - `validate/3` - 统一验证
  - `blank?/1` - 空值检查
  - `present?/1` - 内容检查
  - `valid_type?/2` - 类型检查

  ### 通用工具
  - `generate_unique_id/1` - 生成唯一ID
  - `deep_merge/2` - 深度合并映射
  - `should_show_clear_button?/3` - 清除按钮显示判断
  """

  # 导入所有工具模块
  alias RacingGame.Utils.TextUtils
  alias RacingGame.Utils.TypeConverter
  alias RacingGame.Utils.Validator
  alias RacingGame.Utils.CommonUtils

  # ============================================================================
  # 文本处理工具 - 统一接口
  # ============================================================================

  @doc "截断文本到指定长度"
  defdelegate truncate_text(text, options \\ []), to: TextUtils

  @doc "智能截断文本，根据上下文自动选择配置"
  defdelegate smart_truncate(text, context \\ :general), to: TextUtils

  @doc "清理和标准化字符串"
  defdelegate sanitize_string(value), to: TextUtils

  @doc "获取可用的截断预设列表"
  defdelegate get_available_presets(), to: TextUtils

  @doc "获取预设配置详情"
  defdelegate get_preset_info(preset), to: TextUtils

  # ============================================================================
  # 类型转换工具 - 统一接口
  # ============================================================================

  @doc "安全类型转换 - 统一入口"
  defdelegate safe_convert(value, target_type, opts \\ []), to: TypeConverter

  @doc "安全字符串转换"
  defdelegate safe_to_string(value, opts \\ []), to: TypeConverter

  @doc "安全整数转换"
  defdelegate safe_to_integer(value, opts \\ []), to: TypeConverter

  @doc "安全布尔值转换"
  defdelegate safe_to_boolean(value, opts \\ []), to: TypeConverter

  @doc "格式化时间"
  defdelegate format_time(value, opts \\ []), to: TypeConverter

  # ============================================================================
  # 验证工具 - 统一接口
  # ============================================================================

  @doc "统一验证函数 - 主入口"
  defdelegate validate(value, validation_type, opts \\ []), to: Validator

  @doc "检查值是否为空或仅包含空白字符"
  defdelegate blank?(value), to: Validator

  @doc "检查值是否有内容"
  defdelegate present?(value), to: Validator

  @doc "检查值是否为空（更严格的检查）"
  defdelegate empty?(value), to: Validator

  @doc "检查值是否为指定类型"
  defdelegate valid_type?(value, type), to: Validator

  # ============================================================================
  # 通用工具 - 统一接口
  # ============================================================================

  @doc "生成唯一标识符"
  defdelegate generate_unique_id(prefix \\ ""), to: CommonUtils

  @doc "深度合并两个映射"
  defdelegate deep_merge(left, right), to: CommonUtils

  @doc "判断是否应该显示清除按钮"
  defdelegate should_show_clear_button?(search_query, selected_type, selected_status), to: CommonUtils

  @doc "安全地获取映射中的嵌套值"
  defdelegate safe_get_in(map, path, default \\ nil), to: CommonUtils

  @doc "安全地更新映射中的嵌套值"
  defdelegate safe_put_in(map, path, value), to: CommonUtils

  @doc "将关键字列表转换为映射"
  defdelegate keyword_to_map(keyword_list, opts \\ []), to: CommonUtils

  @doc "将映射转换为关键字列表"
  defdelegate map_to_keyword(map, opts \\ []), to: CommonUtils

  @doc "批量重命名映射的键"
  defdelegate rename_keys(map, key_mapping), to: CommonUtils

  @doc "过滤映射，只保留指定的键"
  defdelegate filter_keys(map, keys), to: CommonUtils

  @doc "移除映射中的指定键"
  defdelegate drop_keys(map, keys), to: CommonUtils

  @doc "压缩两个列表为键值对映射"
  defdelegate zip_to_map(keys, values), to: CommonUtils

  # ============================================================================
  # 便捷函数 - 常用组合操作
  # ============================================================================

  @doc """
  快速文本处理 - 清理并截断文本

  ## 示例
      iex> Utils.clean_and_truncate("  很长的文本内容  ", :title)
      "很长的文本内容"
  """
  def clean_and_truncate(text, preset_or_options \\ :medium) do
    text
    |> sanitize_string()
    |> truncate_text(preset_or_options)
  end

  @doc """
  安全获取并转换值

  ## 示例
      iex> data = %{user: %{age: "25"}}
      iex> Utils.safe_get_and_convert(data, [:user, :age], :integer)
      {:ok, 25}
  """
  def safe_get_and_convert(map, path, target_type, opts \\ []) do
    case safe_get_in(map, path) do
      nil -> {:error, "路径不存在"}
      value -> safe_convert(value, target_type, opts)
    end
  end

  @doc """
  验证并转换值

  ## 示例
      iex> Utils.validate_and_convert("123", :integer, [
      ...>   {:length, [min: 1, max: 5]},
      ...>   {:format, [pattern: ~r/^\d+$/]}
      ...> ])
      {:ok, 123}
  """
  def validate_and_convert(value, target_type, validations \\ [], convert_opts \\ []) do
    case validate(value, :all, validations: validations) do
      {:ok, validated_value} ->
        safe_convert(validated_value, target_type, convert_opts)
      {:error, _} = error ->
        error
      true ->
        safe_convert(value, target_type, convert_opts)
      false ->
        {:error, "验证失败"}
    end
  end

  @doc """
  批量处理文本列表

  ## 示例
      iex> texts = ["长文本1", "长文本2", "长文本3"]
      iex> Utils.batch_process_texts(texts, :title)
      ["长文本1", "长文本2", "长文本3"]
  """
  def batch_process_texts(texts, preset_or_options \\ :medium) when is_list(texts) do
    Enum.map(texts, &clean_and_truncate(&1, preset_or_options))
  end

  @doc """
  创建配置映射

  将关键字列表转换为映射，并进行深度转换和验证

  ## 示例
      iex> config = [database: [host: "localhost", port: "5432"]]
      iex> Utils.create_config_map(config, [:database, :host], :string)
      {:ok, %{database: %{host: "localhost", port: "5432"}}}
  """
  def create_config_map(keyword_list, required_paths \\ [], validations \\ []) do
    config_map = keyword_to_map(keyword_list, deep: true)

    # 验证必需的路径
    missing_paths = Enum.filter(required_paths, fn path ->
      safe_get_in(config_map, path) |> blank?()
    end)

    if Enum.empty?(missing_paths) do
      # 执行验证
      case validate_config_paths(config_map, validations) do
        :ok -> {:ok, config_map}
        {:error, _} = error -> error
      end
    else
      {:error, "缺少必需的配置路径: #{inspect(missing_paths)}"}
    end
  end

  # 私有函数 - 验证配置路径
  defp validate_config_paths(config_map, validations) do
    Enum.reduce_while(validations, :ok, fn {path, validation_opts}, :ok ->
      value = safe_get_in(config_map, path)
      
      case validate(value, :all, validations: validation_opts) do
        {:ok, _} -> {:cont, :ok}
        {:error, message} -> {:halt, {:error, "路径 #{inspect(path)} 验证失败: #{message}"}}
        true -> {:cont, :ok}
        false -> {:halt, {:error, "路径 #{inspect(path)} 验证失败"}}
      end
    end)
  end
end
