defmodule RacingGame.Live.AdminPanel.SystemCommunicationsDialog do
  @moduledoc """
  系统通信管理自定义对话框组件

  使用handlers、mappings、operations、validators目录下的模块
  提供创建和编辑系统消息、公告、通知的专用界面
  """
  use CypridinaWeb, :live_component

  use Phoenix.Component

  alias CypridinaWeb.Components.AdminButtonGroup
  alias RacingGame.Live.AdminPanel.Handlers.SystemCommunicationsHandler

  require Logger

  def update(assigns, socket) do
    Logger.info("🔄 SystemCommunicationsDialog.update - assigns: #{inspect(Map.keys(assigns))}")
    Logger.info("🔄 show参数: #{inspect(assigns[:show])}")

    # 简化初始化，避免复杂的依赖
    type = assigns[:type] || "message"

    # 使用简单的类型配置
    type_config = get_simple_type_config(type)

    # 简化状态分配
    socket =
      socket
      |> assign(assigns)
      |> assign(:type, type)
      |> assign(:mode, assigns[:mode] || :create)
      |> assign(:type_config, type_config)
      |> assign(:show_main_dialog, assigns[:show] || false)
      |> assign(:current_user, assigns[:current_user])  # 确保传递当前用户信息
      |> assign_simple_form_data(assigns[:data] || %{})

    Logger.info("✅ 对话框组件状态: show_main_dialog=#{Map.get(socket.assigns, :show_main_dialog, false)}")

    {:ok, socket}
  end

  # 简化的类型配置
  defp get_simple_type_config("message") do
    %{title: "系统消息", description: "向用户发送重要的系统消息", icon: "fas fa-envelope", color: "blue"}
  end

  defp get_simple_type_config("announcement") do
    %{title: "系统公告", description: "发布重要的系统公告", icon: "fas fa-bullhorn", color: "green"}
  end

  defp get_simple_type_config("notification") do
    %{title: "系统通知", description: "发送系统状态通知", icon: "fas fa-bell", color: "yellow"}
  end

  defp get_simple_type_config(_) do
    %{title: "系统通信", description: "系统通信记录", icon: "fas fa-comments", color: "gray"}
  end

  # 简化的表单数据分配
  defp assign_simple_form_data(socket, data) do
    form_data = %{
      "title" => to_string(data[:title] || ""),
      "content" => to_string(data[:content] || ""),
      "priority" => to_string(data[:priority] || "medium"),
      "recipient_type" => to_string(data[:recipient_type] || "all"),
      "recipient_id" => to_string(data[:recipient_id] || ""),
      "active" => normalize_boolean(data[:active]),
      "expires_at" => normalize_datetime(data[:expires_at])
    }

    # 创建正确的表单 changeset
    # 使用 Ecto.Changeset.cast/3 来创建正确的 changeset
    changeset =
      {%{}, %{title: :string, content: :string, priority: :string,
              recipient_type: :string, recipient_id: :string,
              active: :boolean, expires_at: :string}}
      |> Ecto.Changeset.cast(form_data, [:title, :content, :priority,
                                         :recipient_type, :recipient_id,
                                         :active, :expires_at])

    form = Phoenix.Component.to_form(changeset, as: "communication")

    socket
    |> assign(:form_data, form_data)
    |> assign(:form, form)
    |> assign(:priority_select_options, [
      {"低", "low"},
      {"中", "medium"},
      {"高", "high"},
      {"紧急", "urgent"}
    ])
    |> assign(:show_confirm_dialog, false)
    |> assign(:show_validation_error_dialog, false)
    |> assign(:pending_save_data, %{})
    |> assign(:confirm_message, "")
    |> assign(:validation_error_message, "")
  end

  # 简化的事件处理
  def handle_event("close_dialog", _params, socket) do
    Logger.info("🚪 关闭对话框")
    # send(self(), {:modal_event, :close})
    {:noreply, assign(socket, :show_main_dialog, false)}
  end

  # 重命名的提交事件处理器，避免冲突
  def handle_event("modal_submit_form", params, socket) do
    Logger.info("💾 [模态对话框] 模态提交表单事件被触发！")
    Logger.info("💾 [模态对话框] 接收到的参数: #{inspect(params)}")
    Logger.info("💾 [模态对话框] 参数类型: #{inspect(if is_map(params) and Map.has_key?(params, :__struct__), do: params.__struct__, else: "map")}")
    Logger.info("💾 [模态对话框] Socket assigns keys: #{inspect(Map.keys(socket.assigns))}")
    Logger.info("💾 [模态对话框] 当前模式: #{socket.assigns.mode}")
    Logger.info("💾 [模态对话框] 当前类型: #{socket.assigns.type}")
    Logger.info("💾 [模态对话框] 当前参数{ #{params}")

    # 尝试从不同的参数格式中提取数据
    form_params = case params do
      %{"communication" => comm_params} ->
        Logger.info("💾 [模态对话框] 使用 communication 参数")
        comm_params
      %{} when map_size(params) > 0 ->
        Logger.info("💾 [模态对话框] 使用直接参数")
        params
      _ ->
        Logger.info("💾 [模态对话框] 使用当前表单数据")
        socket.assigns.form_data
    end

    Logger.info("💾 [模态对话框] 最终使用的参数: #{inspect(form_params)}")

    try do
      # 使用处理后的参数
      result = SystemCommunicationsHandler.handle_modal_submit(
        form_params,
        socket.assigns.mode,
        socket
      )
      Logger.info("💾 [模态对话框] SystemCommunicationsHandler 返回结果: #{inspect(result)}")
      result
    rescue
      error ->
        Logger.error("💾 [模态对话框] SystemCommunicationsHandler 发生异常: #{inspect(error)}")
        Logger.error("💾 [模态对话框] 异常堆栈: #{inspect(__STACKTRACE__)}")
        socket = put_flash(socket, :error, "提交表单时发生错误: #{inspect(error)}")
        {:noreply, socket}
    end
  end

  def handle_event("confirm_save", params, socket) do
    Logger.info("🔄 [模态对话框] 确认保存数据")
    Logger.info("📝 [模态对话框] 确认保存参数: #{inspect(params)}")
    Logger.info("📝 [模态对话框] pending_save_data: #{inspect(socket.assigns.pending_save_data)}")
    Logger.info("📝 [模态对话框] mode: #{inspect(socket.assigns.mode)}")
    Logger.info("📝 [模态对话框] type: #{inspect(socket.assigns.type)}")

    # 获取要保存的数据
    save_data = case socket.assigns.pending_save_data do
      data when data == %{} or is_nil(data) ->
        Logger.warning("⚠️ [模态对话框] 没有待保存的数据，使用当前表单数据")
        socket.assigns.form_data
      data ->
        Logger.info("✅ [模态对话框] 使用待保存数据")
        data
    end

    Logger.info("📝 [模态对话框] 最终保存数据: #{inspect(save_data)}")

    # 直接调用保存逻辑，不通过 SystemCommunicationsHandler
    try do
      result = SystemCommunicationsHandler.handle_modal_confirm_save(
        save_data,
        socket.assigns.mode,
        socket
      )
      Logger.info("✅ [模态对话框] SystemCommunicationsHandler 返回: #{inspect(result)}")
      result
    rescue
      error ->
        Logger.error("❌ [模态对话框] SystemCommunicationsHandler 异常: #{inspect(error)}")
        socket = put_flash(socket, :error, "保存时发生异常: #{inspect(error)}")
        {:noreply, socket}
    end
  end

  def handle_event("cancel_save", _params, socket) do
    Logger.info("❌ [模态对话框] 取消保存")

    socket = assign(socket, :show_confirm_dialog, false)
    {:noreply, socket}
  end

  def handle_event("close_validation_error", _params, socket) do
    Logger.info("❌ [模态对话框] 关闭验证错误对话框")

    socket = assign(socket, :show_validation_error_dialog, false)
    {:noreply, socket}
  end

  def handle_event("validate_form", %{"communication" => params}, socket) do
    Logger.info("✅ [模态对话框] 验证表单: #{inspect(params)}")
    Logger.info("🔍 [模态对话框] 当前form_data: #{inspect(socket.assigns.form_data)}")

    # 使用handlers处理表单验证
    SystemCommunicationsHandler.handle_modal_validate_form(params, socket)
  end

  # 捕获其他事件
  def handle_event(event_name, params, socket) do
    Logger.info("🔄 对话框事件: #{event_name}, 参数: #{inspect(params)}")
    {:noreply, socket}
  end

  def render(assigns) do
    ~H"""
    <div>
      <%= if @show_main_dialog do %>
        <div class="fixed inset-0 z-50" aria-labelledby="modal-title" role="dialog" aria-modal="true">
          <!-- 背景遮罩 - 透明 -->
          <div class="fixed inset-0 bg-transparent transition-opacity" aria-hidden="true"></div>

          <!-- 对话框容器 - 可拖动，自由定位 -->
          <div
            id="draggable-dialog"
            class="absolute bg-white rounded-lg shadow-xl border-4 border-blue-400 overflow-hidden"
            style="
              width: 50vw;
              height: 50vh;
              top: 100px;
              left: 100px;
              cursor: move;
              max-width: calc(100vw - 40px);
              max-height: calc(100vh - 40px);
              min-width: 600px;
              min-height: 500px;
            "
            phx-hook="DraggableDialog"
          >
              <!-- 对话框头部 - 作为拖动手柄 -->
              <div class="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4 drag-handle flex items-center justify-between" style="cursor: move;">
                <h3 class="text-lg font-semibold text-white" id="modal-title">
                  <%= if @mode == :create, do: "创建", else: "编辑" %><%= @type_config.title %>
                </h3>
                <AdminButtonGroup.secondary_button
                  text=""
                  action="close_dialog"
                  target={@myself}
                  icon="fas fa-times"
                  id="close-main-dialog"
                />
              </div>

              <!-- 对话框主体内容 -->
              <div class="flex-1 overflow-y-auto p-6" style="height: calc(100% - 140px);">
                <%= render_form(assigns) %>
              </div>

              <!-- 对话框底部按钮 -->
              <div class="bg-gray-50 px-6 py-4 flex justify-end space-x-3 border-t">
                <AdminButtonGroup.secondary_button
                  text="取消"
                  action="close_dialog"
                  target={@myself}
                  icon="fas fa-times"
                />
                <AdminButtonGroup.save_button
                  text={if @mode == :create, do: "创建", else: "保存"}
                  type="button"
                  action="modal_submit_form"
                  target={@myself}
                  icon={if @mode == :create, do: "fas fa-plus", else: "fas fa-save"}
                />
              </div>
            </div>
        </div>
      <% end %>

      <!-- 确认保存对话框 - 嵌套在父对话框上面，透明背景，可移动 -->
      <%= if @show_confirm_dialog do %>
        <div class="fixed inset-0 z-[9999]" aria-labelledby="confirm-title" role="dialog" aria-modal="true">
          <!-- 透明背景遮罩 -->
          <div class="fixed inset-0 bg-transparent transition-opacity" aria-hidden="true"></div>

          <!-- 确认对话框容器 - 可移动，自由定位 -->
          <div
            id="draggable-confirm-dialog"
            class="absolute bg-white rounded-lg shadow-xl border-4 border-green-400 overflow-hidden"
            style="
              width: 25vw;
              height: auto;
              top: 200px;
              left: 200px;
              cursor: move;
              max-width: calc(100vw - 40px);
              max-height: calc(100vh - 40px);
              min-width: 400px;
            "
            phx-hook="DraggableDialog"
          >
            <!-- 对话框头部 - 作为拖动手柄 -->
            <div class="bg-gradient-to-r from-green-600 to-green-700 px-6 py-4 drag-handle flex items-center justify-between" style="cursor: move;">
              <h3 class="text-lg font-semibold text-white" id="confirm-title">
                <i class="fas fa-check-circle mr-2"></i>
                确认保存
              </h3>
              <div style="cursor: pointer;">
                <AdminButtonGroup.secondary_button
                  text=""
                  action="cancel_save"
                  target={@myself}
                  icon="fas fa-times"
                  id="close-confirm-dialog"
                />
              </div>
            </div>

            <!-- 对话框内容 -->
            <div class="p-6">
              <div class="text-gray-700 whitespace-pre-line text-sm leading-relaxed">
                <%= @confirm_message %>
              </div>
            </div>

            <!-- 对话框按钮 -->
            <div class="bg-gray-50 px-6 py-4 flex justify-end space-x-3 border-t">
              <AdminButtonGroup.secondary_button
                text="取消"
                action="cancel_save"
                target={@myself}
                icon="fas fa-times"
              />
              <AdminButtonGroup.save_button
                text="确认保存"
                type="button"
                action="confirm_save"
                target={@myself}
                icon="fas fa-check"
              />
            </div>
          </div>
        </div>
      <% end %>

      <!-- 验证错误对话框 - 显示表单验证失败信息 -->
      <%= if @show_validation_error_dialog do %>
        <div class="fixed inset-0 z-[9999]" aria-labelledby="confirm-title" role="dialog" aria-modal="true">
          <!-- 透明背景遮罩 -->
          <div class="fixed inset-0 bg-transparent transition-opacity" aria-hidden="true"></div>

          <!-- 验证错误对话框容器 - 居中显示 -->
          <div class="fixed inset-0 flex items-center justify-center p-4">
            <div
              class="bg-white rounded-lg shadow-xl border-4 border-red-400 overflow-hidden max-w-md w-full"
              style="max-height: 80vh;"
            >
              <!-- 对话框头部 -->
              <div class="bg-gradient-to-r from-red-600 to-red-700 px-6 py-4 flex items-center justify-between">
                <h3 class="text-lg font-semibold text-white" id="validation-error-title">
                  <i class="fas fa-exclamation-triangle mr-2"></i>
                  表单验证失败
                </h3>
                <AdminButtonGroup.secondary_button
                  text=""
                  action="close_validation_error"
                  target={@myself}
                  icon="fas fa-times"
                  id="close-validation-error-dialog"
                />
              </div>

              <!-- 对话框内容 -->
              <div class="p-6 max-h-96 overflow-y-auto">
                <div class="text-gray-700 whitespace-pre-line text-sm leading-relaxed">
                  <%= @validation_error_message %>
                </div>
              </div>

              <!-- 对话框按钮 -->
              <div class="bg-gray-50 px-6 py-4 flex justify-end border-t">
                <AdminButtonGroup.secondary_button
                  text="确定"
                  action="close_validation_error"
                  target={@myself}
                  icon="fas fa-check"
                />
              </div>
            </div>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  # 渲染美化的表单内容
  defp render_form(assigns) do
    ~H"""
    <div class="w-full mx-auto">
      <.form for={@form} id="communication-form" phx-change="validate_form" phx-submit="submit_form" phx-target={@myself} class="space-y-6">
        <!-- 表单标题区域 -->
        <div class="text-center border-b border-gray-200 pb-4 mb-6">
          <div class="flex items-center justify-center mb-2">
            <i class={[@type_config.icon, "text-2xl mr-3", "text-#{@type_config.color}-500"]}></i>
            <h2 class="text-xl font-semibold text-gray-800">
              <%= if @mode == :create, do: "创建", else: "编辑" %><%= @type_config.title %>
            </h2>
          </div>
          <p class="text-sm text-gray-600"><%= @type_config.description %></p>
        </div>

        <!-- 基本信息区域 -->
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
          <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
            <i class="fas fa-info-circle mr-2 text-blue-500"></i>
            基本信息
          </h3>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 标题字段 -->
            <div class="md:col-span-2">
              <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-heading mr-1 text-blue-500"></i>
                标题 <span class="text-red-500">*</span>
              </label>
              <.input
                field={@form[:title]}
                type="text"
                placeholder="请输入标题..."
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
              />
            </div>

            <!-- 优先级字段 -->
            <div>
              <label for="priority" class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-flag mr-1 text-orange-500"></i>
                优先级
              </label>
              <.input
                field={@form[:priority]}
                type="select"
                options={@priority_select_options}
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
              />
            </div>

            <!-- 状态字段 -->
            <div class="flex items-center">
              <div class="flex items-center space-x-4 pt-6">
                <label class="flex items-center cursor-pointer">
                  <.input
                    field={@form[:active]}
                    type="checkbox"
                    checked={@form[:active].value == true || @form[:active].value == "true"}
                    class="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span class="text-sm font-medium text-gray-700">
                    <i class="fas fa-toggle-on mr-1 text-green-500"></i>
                    立即激活
                  </span>
                </label>
              </div>
            </div>
          </div>
        </div>

        <!-- 内容区域 -->
        <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-6 border border-green-200">
          <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
            <i class="fas fa-edit mr-2 text-green-500"></i>
            内容详情
          </h3>

          <div>
            <label for="content" class="block text-sm font-medium text-gray-700 mb-2">
              <i class="fas fa-align-left mr-1 text-green-500"></i>
              内容 <span class="text-red-500">*</span>
            </label>
            <.input
              field={@form[:content]}
              type="textarea"
              rows="4"
              placeholder="请输入详细内容..."
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 resize-none"
            />
            <p class="text-xs text-gray-500 mt-1">最多2000个字符</p>
          </div>
        </div>

        <!-- 接收者设置区域 -->
        <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-6 border border-purple-200">
          <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
            <i class="fas fa-users mr-2 text-purple-500"></i>
            接收者设置
          </h3>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="recipient_type" class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-user-tag mr-1 text-purple-500"></i>
                接收者类型
              </label>
              <.input
                field={@form[:recipient_type]}
                type="select"
                options={[
                  {"所有用户", "all"},
                  {"特定用户", "user"},
                  {"管理员", "admin"}
                ]}
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
              />
            </div>

            <div>
              <label for="recipient_id" class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-id-card mr-1 text-purple-500"></i>
                接收者ID
              </label>
              <.input
                field={@form[:recipient_id]}
                type="text"
                placeholder="留空表示所有用户"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
              />
            </div>
          </div>
        </div>

        <!-- 高级设置区域 -->
        <div class="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg p-6 border border-yellow-200">
          <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
            <i class="fas fa-cogs mr-2 text-yellow-500"></i>
            高级设置
          </h3>

          <div>
            <label for="expires_at" class="block text-sm font-medium text-gray-700 mb-2">
              <i class="fas fa-clock mr-1 text-yellow-500"></i>
              过期时间
            </label>
            <.input
              field={@form[:expires_at]}
              type="datetime-local"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-200"
            />
            <p class="text-xs text-gray-500 mt-1">留空表示永不过期</p>
          </div>
        </div>
      </.form>
    </div>
    """
  end

  # 私有函数 - 规范化 boolean 值
  defp normalize_boolean(nil), do: false
  defp normalize_boolean(false), do: false
  defp normalize_boolean(true), do: true
  defp normalize_boolean("true"), do: true
  defp normalize_boolean("false"), do: false
  defp normalize_boolean(_), do: false

  # 私有函数 - 规范化 datetime 值
  defp normalize_datetime(nil), do: ""
  defp normalize_datetime(""), do: ""
  defp normalize_datetime(value) when is_binary(value), do: value
  defp normalize_datetime(%DateTime{} = dt), do: DateTime.to_iso8601(dt)
  defp normalize_datetime(%NaiveDateTime{} = ndt), do: NaiveDateTime.to_iso8601(ndt)
  defp normalize_datetime(_), do: ""

end
