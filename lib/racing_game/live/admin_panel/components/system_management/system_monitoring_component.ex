defmodule RacingGame.Live.AdminPanel.SystemMonitoringComponent do
  @moduledoc """
  系统监控管理组件 - 仅限管理员使用
  """
  use Cy<PERSON>ridinaWeb, :live_component

  alias CypridinaWeb.AuthHelper
  alias Cypridina.Accounts.User
  alias CypridinaWeb.Components.AdminButtonGroup
  require Ash.Query
  require Logger

  def update(assigns, socket) do
    socket = assign(socket, assigns)

    if AuthHelper.has_permission?(socket.assigns.current_user, :admin) do
      socket =
        socket
        |> assign_defaults()
        |> load_monitoring_data()

      # 启动自动刷新定时器
      Process.send_after(self(), :auto_refresh, socket.assigns.refresh_interval)

      {:ok, socket}
    else
      # 非管理员用户显示无权限信息
      socket =
        socket
        |> assign_defaults()
        |> assign(:unauthorized, true)
      {:ok, socket}
    end
  end

  defp assign_defaults(socket) do
    socket
    |> assign(:server_status, %{cpu_usage: 0, memory_usage: 0, disk_usage: 0})
    |> assign(:active_users, 0)
    |> assign(:recent_errors, [])
    |> assign(:system_alerts, [])
    |> assign(:refresh_interval, 30_000) # 30秒刷新一次
    |> assign(:loading, false)
    |> assign(:unauthorized, false)
  end

  defp load_monitoring_data(socket) do
    # 使用模拟数据（实际项目中应该从系统监控服务获取）
    server_status = %{
      cpu_usage: :rand.uniform(100),
      memory_usage: :rand.uniform(100),
      disk_usage: :rand.uniform(100)
    }

    active_users = :rand.uniform(500)

    recent_errors = [
      %{
        id: 1,
        timestamp: "2024-01-15 10:30:15",
        type: "Database Error",
        message: "Connection timeout to database server"
      },
      %{
        id: 2,
        timestamp: "2024-01-15 10:25:42",
        type: "HTTP Error",
        message: "404 Not Found: /api/invalid-endpoint"
      },
      %{
        id: 3,
        timestamp: "2024-01-15 10:20:18",
        type: "Memory Warning",
        message: "Memory usage exceeded 90% threshold"
      }
    ]

    system_alerts = [
      %{
        id: 1,
        title: "高CPU使用率警告",
        message: "CPU使用率持续超过85%，请检查系统负载",
        timestamp: "2024-01-15 10:30:00"
      },
      %{
        id: 2,
        title: "磁盘空间不足",
        message: "系统磁盘剩余空间不足10%，请及时清理",
        timestamp: "2024-01-15 10:25:00"
      }
    ]

    socket
    |> assign(:server_status, server_status)
    |> assign(:active_users, active_users)
    |> assign(:recent_errors, recent_errors)
    |> assign(:system_alerts, system_alerts)
  end

  def handle_event("refresh_now", _params, socket) do
    socket =
      socket
      |> load_monitoring_data()
      |> put_flash(:info, "监控数据已刷新")
    {:noreply, socket}
  end

  def handle_info(:auto_refresh, socket) do
    # 自动刷新监控数据
    socket = load_monitoring_data(socket)

    # 设置下次自动刷新
    Process.send_after(self(), :auto_refresh, socket.assigns.refresh_interval)

    {:noreply, socket}
  end

  def handle_event("acknowledge_alert", %{"id" => id}, socket) do
    # 模拟确认警报（实际项目中应该更新数据库）
    Logger.info("Alert #{id} acknowledged by admin")

    # 从当前警报列表中移除已确认的警报
    updated_alerts = Enum.reject(socket.assigns.system_alerts, &(&1.id == String.to_integer(id)))

    socket =
      socket
      |> assign(:system_alerts, updated_alerts)
      |> put_flash(:info, "警报已确认")
    {:noreply, socket}
  end

  def handle_event("change_refresh_interval", %{"interval" => interval}, socket) do
    # 转换为整数并更新刷新间隔
    interval_ms = String.to_integer(interval) * 1000
    socket = assign(socket, :refresh_interval, interval_ms)
    {:noreply, socket}
  end

  def render(assigns) do
    ~H"""
    <div class="p-6 bg-white rounded-lg shadow-md">
      <%= if @unauthorized do %>
        <h3 class="text-xl font-bold text-red-600 mb-4">权限不足</h3>
        <p class="text-gray-700">您没有访问系统监控的权限。只有管理员可以查看此页面。</p>
      <% else %>
        <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-bold text-gray-800">系统监控管理</h3>
        <div class="flex items-center space-x-4">
          <div class="flex items-center">
            <span class="text-sm text-gray-600 mr-2">刷新间隔:</span>
            <select
              class="p-2 border border-gray-300 rounded-md"
              phx-change="change_refresh_interval"
              phx-target={@myself}
              value={div(@refresh_interval, 1000)}
            >
              <option value="10">10秒</option>
              <option value="30">30秒</option>
              <option value="60">1分钟</option>
              <option value="300">5分钟</option>
            </select>
          </div>
          <AdminButtonGroup.refresh_button
            text="立即刷新"
            action="refresh_now"
            target={@myself}
          />
        </div>
      </div>

      <!-- 服务器状态卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div class="p-4 bg-gray-50 rounded-lg border border-gray-200">
          <h4 class="text-sm text-gray-500 mb-1">CPU 使用率</h4>
          <p class="text-2xl font-bold text-gray-800"><%= @server_status.cpu_usage %>%</p>
          <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
            <div class="bg-blue-600 h-2 rounded-full" style={"width: #{@server_status.cpu_usage}%"}></div>
          </div>
        </div>
        <div class="p-4 bg-gray-50 rounded-lg border border-gray-200">
          <h4 class="text-sm text-gray-500 mb-1">内存使用率</h4>
          <p class="text-2xl font-bold text-gray-800"><%= @server_status.memory_usage %>%</p>
          <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
            <div class="bg-green-600 h-2 rounded-full" style={"width: #{@server_status.memory_usage}%"}></div>
          </div>
        </div>
        <div class="p-4 bg-gray-50 rounded-lg border border-gray-200">
          <h4 class="text-sm text-gray-500 mb-1">磁盘使用率</h4>
          <p class="text-2xl font-bold text-gray-800"><%= @server_status.disk_usage %>%</p>
          <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
            <div class="bg-yellow-600 h-2 rounded-full" style={"width: #{@server_status.disk_usage}%"}></div>
          </div>
        </div>
      </div>

      <!-- 活动用户和系统警报 -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div class="p-4 bg-gray-50 rounded-lg border border-gray-200">
          <h4 class="text-lg font-semibold text-gray-800 mb-4">当前活动用户</h4>
          <div class="flex items-center justify-center h-32">
            <p class="text-5xl font-bold text-gray-800"><%= @active_users %></p>
          </div>
        </div>
        <div class="p-4 bg-gray-50 rounded-lg border border-gray-200">
          <h4 class="text-lg font-semibold text-gray-800 mb-4">系统警报</h4>
          <div class="space-y-3 max-h-60 overflow-y-auto pr-2">
            <%= if Enum.empty?(@system_alerts) do %>
              <p class="text-gray-500 text-center italic">暂无系统警报</p>
            <% else %>
              <%= for alert <- @system_alerts do %>
                <div class="p-3 bg-red-50 border border-red-200 rounded-md flex justify-between items-start">
                  <div>
                    <p class="font-medium text-red-800"><%= alert.title %></p>
                    <p class="text-sm text-red-700 mt-1"><%= alert.message %></p>
                    <p class="text-xs text-gray-500 mt-1"><%= alert.timestamp %></p>
                  </div>
                  <AdminButtonGroup.admin_button
                    text="确认"
                    action="acknowledge_alert"
                    target={@myself}
                    id={alert.id}
                    class="text-xs bg-red-600 text-white px-2 py-1 hover:bg-red-700"
                    bg_color="#dc2626"
                  />
                </div>
              <% end %>
            <% end %>
          </div>
        </div>
      </div>

      <!-- 最近错误日志 -->
      <div class="p-4 bg-gray-50 rounded-lg border border-gray-200">
        <h4 class="text-lg font-semibold text-gray-800 mb-4">最近错误日志</h4>
        <div class="overflow-x-auto">
          <table class="min-w-full bg-white">
            <thead>
              <tr class="bg-gray-100">
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">时间</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">错误类型</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">消息</th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
              <%= if Enum.empty?(@recent_errors) do %>
                <tr>
                  <td colspan="3" class="py-4 px-4 text-center text-gray-500 italic">暂无错误日志</td>
                </tr>
              <% else %>
                <%= for error <- @recent_errors do %>
                  <tr>
                    <td class="py-2 px-4 text-sm text-gray-900"><%= error.timestamp %></td>
                    <td class="py-2 px-4 text-sm text-red-600"><%= error.type %></td>
                    <td class="py-2 px-4 text-sm text-gray-700"><%= error.message %></td>
                  </tr>
                <% end %>
              <% end %>
            </tbody>
          </table>
        </div>
        </div>
      <% end %>
    </div>
    """
  end
end
