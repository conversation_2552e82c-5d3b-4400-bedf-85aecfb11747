# 监控配置指南

## 📋 概述

本文档提供 Racing Game Admin Panel 系统的完整监控配置指南，包括 Prometheus、<PERSON><PERSON>、日志收集和告警系统的搭建与配置。

## 🏗️ 监控架构

```
┌─────────────────────────────────────────────────────────────┐
│                    监控数据流                                │
│                                                             │
│  应用服务器 ──→ Prometheus ──→ Grafana ──→ 告警管理器        │
│      │              │              │           │            │
│      │              │              │           ▼            │
│      │              │              │      邮件/短信/Slack    │
│      │              │              │                        │
│      ▼              ▼              ▼                        │
│  日志文件 ──→ Filebeat ──→ Elasticsearch ──→ Kibana         │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## 📊 Prometheus 配置

### 1. Prometheus 安装和配置

#### Docker Compose 配置
```yaml
# docker-compose.monitoring.yml
version: '3.8'

services:
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./prometheus/rules:/etc/prometheus/rules
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
      - ./grafana/dashboards:/var/lib/grafana/dashboards
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    restart: unless-stopped

  alertmanager:
    image: prom/alertmanager:latest
    container_name: alertmanager
    ports:
      - "9093:9093"
    volumes:
      - ./alertmanager/alertmanager.yml:/etc/alertmanager/alertmanager.yml
    restart: unless-stopped

volumes:
  prometheus_data:
  grafana_data:
```

#### Prometheus 主配置
```yaml
# prometheus/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Racing Game 应用监控
  - job_name: 'racing-game'
    static_configs:
      - targets: ['racing-game:4000']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # 系统监控
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  # PostgreSQL 监控
  - job_name: 'postgres-exporter'
    static_configs:
      - targets: ['postgres-exporter:9187']

  # Redis 监控
  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['redis-exporter:9121']

  # Nginx 监控
  - job_name: 'nginx-exporter'
    static_configs:
      - targets: ['nginx-exporter:9113']
```

### 2. 应用指标暴露

#### Phoenix 应用 Telemetry 配置
```elixir
# lib/racing_game_web/telemetry.ex
defmodule RacingGameWeb.Telemetry do
  use Supervisor
  import Telemetry.Metrics

  def start_link(arg) do
    Supervisor.start_link(__MODULE__, arg, name: __MODULE__)
  end

  def init(_arg) do
    children = [
      # Telemetry poller 定期收集指标
      {:telemetry_poller, measurements: periodic_measurements(), period: 10_000},
      
      # Prometheus 指标导出器
      {TelemetryMetricsPrometheus, metrics: metrics()}
    ]

    Supervisor.init(children, strategy: :one_for_one)
  end

  def metrics do
    [
      # Phoenix 指标
      counter("phoenix.endpoint.start.system_time",
        unit: {:native, :millisecond}
      ),
      summary("phoenix.endpoint.stop.duration",
        unit: {:native, :millisecond}
      ),
      summary("phoenix.router_dispatch.stop.duration",
        tags: [:route],
        unit: {:native, :millisecond}
      ),

      # 数据库指标
      summary("racing_game.repo.query.total_time",
        unit: {:native, :millisecond}
      ),
      summary("racing_game.repo.query.decode_time",
        unit: {:native, :millisecond}
      ),
      summary("racing_game.repo.query.query_time",
        unit: {:native, :millisecond}
      ),
      summary("racing_game.repo.query.queue_time",
        unit: {:native, :millisecond}
      ),

      # 业务指标
      counter("racing_game.users.login.count"),
      counter("racing_game.users.register.count"),
      counter("racing_game.payments.success.count"),
      counter("racing_game.payments.failed.count"),
      counter("racing_game.games.bet.count"),
      counter("racing_game.games.win.count"),
      
      # 系统指标
      last_value("vm.memory.total", unit: :byte),
      last_value("vm.memory.processes", unit: :byte),
      last_value("vm.memory.processes_used", unit: :byte),
      last_value("vm.memory.system", unit: :byte),
      last_value("vm.memory.atom", unit: :byte),
      last_value("vm.memory.atom_used", unit: :byte),
      last_value("vm.memory.binary", unit: :byte),
      last_value("vm.memory.code", unit: :byte),
      last_value("vm.memory.ets", unit: :byte),

      # 进程指标
      last_value("vm.total_run_queue_lengths.total"),
      last_value("vm.total_run_queue_lengths.cpu"),
      last_value("vm.total_run_queue_lengths.io"),
      last_value("vm.system_counts.process_count"),
      last_value("vm.system_counts.atom_count"),
      last_value("vm.system_counts.port_count")
    ]
  end

  defp periodic_measurements do
    [
      # VM 指标收集
      {__MODULE__, :dispatch_vm_metrics, []},
      
      # 业务指标收集
      {__MODULE__, :dispatch_business_metrics, []},
      
      # 数据库连接池指标
      {__MODULE__, :dispatch_db_pool_metrics, []}
    ]
  end

  def dispatch_vm_metrics do
    :telemetry.execute([:vm, :memory], :erlang.memory())
    :telemetry.execute([:vm, :total_run_queue_lengths], %{
      total: :erlang.statistics(:total_run_queue_lengths),
      cpu: :erlang.statistics(:run_queue_lengths),
      io: :erlang.statistics(:io_run_queue_lengths)
    })
    :telemetry.execute([:vm, :system_counts], %{
      process_count: :erlang.system_info(:process_count),
      atom_count: :erlang.system_info(:atom_count),
      port_count: :erlang.system_info(:port_count)
    })
  end

  def dispatch_business_metrics do
    # 收集业务相关指标
    active_users = get_active_users_count()
    pending_payments = get_pending_payments_count()
    
    :telemetry.execute([:racing_game, :business], %{
      active_users: active_users,
      pending_payments: pending_payments
    })
  end

  def dispatch_db_pool_metrics do
    pool_status = DBConnection.status(RacingGame.Repo)
    
    :telemetry.execute([:racing_game, :db_pool], %{
      pool_size: pool_status.pool_size,
      checked_out: pool_status.checked_out,
      checked_in: pool_status.checked_in
    })
  end

  # 辅助函数
  defp get_active_users_count do
    # 实现获取活跃用户数的逻辑
    0
  end

  defp get_pending_payments_count do
    # 实现获取待处理支付数的逻辑
    0
  end
end
```

### 3. 告警规则配置

```yaml
# prometheus/rules/racing_game_alerts.yml
groups:
  - name: racing_game_alerts
    rules:
      # 应用可用性告警
      - alert: RacingGameDown
        expr: up{job="racing-game"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Racing Game 应用不可用"
          description: "Racing Game 应用已停止响应超过 1 分钟"

      # 响应时间告警
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, phoenix_endpoint_stop_duration_seconds) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "应用响应时间过高"
          description: "95% 的请求响应时间超过 1 秒"

      # 错误率告警
      - alert: HighErrorRate
        expr: rate(phoenix_endpoint_start_total{status=~"5.."}[5m]) / rate(phoenix_endpoint_start_total[5m]) > 0.05
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "应用错误率过高"
          description: "5xx 错误率超过 5%"

      # 数据库连接告警
      - alert: DatabaseConnectionHigh
        expr: racing_game_db_pool_checked_out / racing_game_db_pool_pool_size > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "数据库连接池使用率过高"
          description: "数据库连接池使用率超过 80%"

      # 内存使用告警
      - alert: HighMemoryUsage
        expr: vm_memory_total / (1024*1024*1024) > 2
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "应用内存使用过高"
          description: "应用内存使用超过 2GB"

      # 进程数告警
      - alert: HighProcessCount
        expr: vm_system_counts_process_count > 1000000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Erlang 进程数过多"
          description: "Erlang 进程数超过 100 万"
```

## 📈 Grafana 仪表板配置

### 1. 数据源配置

```yaml
# grafana/provisioning/datasources/prometheus.yml
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
```

### 2. 仪表板配置

```json
# grafana/dashboards/racing_game_dashboard.json
{
  "dashboard": {
    "id": null,
    "title": "Racing Game Monitoring",
    "tags": ["racing-game"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(phoenix_endpoint_start_total[5m])",
            "legendFormat": "Requests/sec"
          }
        ],
        "yAxes": [
          {
            "label": "requests/sec"
          }
        ]
      },
      {
        "id": 2,
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.50, phoenix_endpoint_stop_duration_seconds)",
            "legendFormat": "50th percentile"
          },
          {
            "expr": "histogram_quantile(0.95, phoenix_endpoint_stop_duration_seconds)",
            "legendFormat": "95th percentile"
          },
          {
            "expr": "histogram_quantile(0.99, phoenix_endpoint_stop_duration_seconds)",
            "legendFormat": "99th percentile"
          }
        ]
      },
      {
        "id": 3,
        "title": "Database Query Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, racing_game_repo_query_total_time_seconds)",
            "legendFormat": "95th percentile"
          }
        ]
      },
      {
        "id": 4,
        "title": "Memory Usage",
        "type": "graph",
        "targets": [
          {
            "expr": "vm_memory_total / (1024*1024)",
            "legendFormat": "Total Memory (MB)"
          },
          {
            "expr": "vm_memory_processes / (1024*1024)",
            "legendFormat": "Process Memory (MB)"
          }
        ]
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "30s"
  }
}
```

## 🚨 告警管理器配置

### 1. Alertmanager 配置

```yaml
# alertmanager/alertmanager.yml
global:
  smtp_smarthost: 'smtp.gmail.com:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'your-app-password'

route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'
  routes:
    - match:
        severity: critical
      receiver: 'critical-alerts'
    - match:
        severity: warning
      receiver: 'warning-alerts'

receivers:
  - name: 'web.hook'
    webhook_configs:
      - url: 'http://127.0.0.1:5001/'

  - name: 'critical-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[CRITICAL] Racing Game Alert'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          {{ end }}
    slack_configs:
      - api_url: 'YOUR_SLACK_WEBHOOK_URL'
        channel: '#alerts'
        title: 'Critical Alert'
        text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'

  - name: 'warning-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[WARNING] Racing Game Alert'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          {{ end }}

inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'dev', 'instance']
```

## 📋 日志收集配置

### 1. 结构化日志配置

```elixir
# config/prod.exs
config :logger,
  backends: [:console, {LoggerFileBackend, :info_log}]

config :logger, :console,
  format: {Jason, :encode!},
  metadata: [:request_id, :user_id, :session_id]

config :logger, :info_log,
  path: "/var/log/racing_game/info.log",
  level: :info,
  format: {Jason, :encode!},
  metadata: [:request_id, :user_id, :session_id]
```

### 2. ELK Stack 配置

```yaml
# docker-compose.elk.yml
version: '3.8'

services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.15.0
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data

  kibana:
    image: docker.elastic.co/kibana/kibana:7.15.0
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch

  filebeat:
    image: docker.elastic.co/beats/filebeat:7.15.0
    volumes:
      - ./filebeat/filebeat.yml:/usr/share/filebeat/filebeat.yml
      - /var/log/racing_game:/var/log/racing_game:ro
    depends_on:
      - elasticsearch

volumes:
  elasticsearch_data:
```

```yaml
# filebeat/filebeat.yml
filebeat.inputs:
  - type: log
    enabled: true
    paths:
      - /var/log/racing_game/*.log
    json.keys_under_root: true
    json.add_error_key: true

output.elasticsearch:
  hosts: ["elasticsearch:9200"]
  index: "racing-game-logs-%{+yyyy.MM.dd}"

setup.template.name: "racing-game"
setup.template.pattern: "racing-game-*"
```

## 🔧 监控部署脚本

### 1. 一键部署脚本

```bash
#!/bin/bash
# deploy_monitoring.sh

set -e

echo "开始部署监控系统..."

# 创建监控目录
mkdir -p monitoring/{prometheus,grafana,alertmanager,filebeat}
cd monitoring

# 下载配置文件
curl -o docker-compose.monitoring.yml https://raw.githubusercontent.com/your-org/racing-game/main/monitoring/docker-compose.monitoring.yml

# 启动监控服务
docker-compose -f docker-compose.monitoring.yml up -d

# 等待服务启动
echo "等待服务启动..."
sleep 30

# 验证服务状态
echo "验证 Prometheus..."
curl -f http://localhost:9090/-/healthy

echo "验证 Grafana..."
curl -f http://localhost:3000/api/health

echo "验证 Alertmanager..."
curl -f http://localhost:9093/-/healthy

echo "监控系统部署完成!"
echo "Prometheus: http://localhost:9090"
echo "Grafana: http://localhost:3000 (admin/admin123)"
echo "Alertmanager: http://localhost:9093"
```

### 2. 健康检查脚本

```bash
#!/bin/bash
# monitoring_health_check.sh

echo "检查监控系统健康状态..."

# 检查 Prometheus
if curl -s http://localhost:9090/-/healthy > /dev/null; then
    echo "✅ Prometheus 运行正常"
else
    echo "❌ Prometheus 异常"
fi

# 检查 Grafana
if curl -s http://localhost:3000/api/health > /dev/null; then
    echo "✅ Grafana 运行正常"
else
    echo "❌ Grafana 异常"
fi

# 检查 Alertmanager
if curl -s http://localhost:9093/-/healthy > /dev/null; then
    echo "✅ Alertmanager 运行正常"
else
    echo "❌ Alertmanager 异常"
fi

# 检查应用指标
if curl -s http://localhost:4000/metrics > /dev/null; then
    echo "✅ 应用指标暴露正常"
else
    echo "❌ 应用指标异常"
fi
```

## 📊 监控最佳实践

### 1. 指标命名规范
```
# 好的指标命名
racing_game_users_login_total
racing_game_payments_processing_duration_seconds
racing_game_database_connections_active

# 避免的命名
user_login_count
payment_time
db_conn
```

### 2. 告警规则设计原则
- **可操作性**: 每个告警都应该有明确的处理步骤
- **相关性**: 告警应该与业务影响直接相关
- **时效性**: 设置合适的告警阈值和持续时间
- **分级管理**: 区分不同严重级别的告警

### 3. 仪表板设计建议
- **分层展示**: 从概览到详细的层次结构
- **关键指标**: 突出显示最重要的业务指标
- **时间范围**: 提供多个时间范围选择
- **交互性**: 支持钻取和过滤功能

---

**文档版本**: v1.0  
**创建日期**: 2024-12-19  
**最后更新**: 2024-12-19  
**维护团队**: Racing Game Development Team
