defmodule RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService do
  @moduledoc """
  📊 数据统计服务层

  负责数据统计系统的业务逻辑协调，包括：
  - 统计数据的业务逻辑处理
  - 跨仓储的数据协调和整合
  - 数据分析和报表生成
  - 统计任务的调度和管理
  - 数据质量监控和验证
  """

  require Logger

  alias RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.{
    SystemReportRepository,
    OnlineStatsRepository,
    ChannelStatsRepository,
    UserStatsRepository,
    CoinStatsRepository,
    RetentionStatsRepository,
    PaymentStatsRepository,
    LtvStatsRepository,
    RobotStatsRepository
  }

  alias RacingGame.Live.AdminPanel.QueryBuilders.Teen.DataStatisticsQueryBuilder

  # ==================== 统计数据管理 ====================

  @doc """
  创建系统报表

  ## 参数
  - `report_data` - 报表数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, system_report}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_system_report(report_data, options \\ []) do
    Logger.info("📊 [数据统计服务] 创建系统报表: #{inspect(report_data[:report_type])}")

    with {:ok, validated_data} <- validate_report_data(report_data),
         {:ok, system_report} <- SystemReportRepository.create_system_report(validated_data, options),
         :ok <- trigger_report_notifications(system_report, options) do
      Logger.info("✅ [数据统计服务] 系统报表创建成功: #{system_report.id}")
      {:ok, system_report}
    else
      {:error, reason} ->
        Logger.error("❌ [数据统计服务] 系统报表创建失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  批量创建统计数据

  ## 参数
  - `stats_batch` - 统计数据批次
  - `options` - 选项参数

  ## 返回
  - `{:ok, batch_results}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_stats_batch(stats_batch, options \\ []) do
    Logger.info("📊 [数据统计服务] 批量创建统计数据: #{length(stats_batch)} 条")

    try do
      # 按类型分组处理
      grouped_stats = group_stats_by_type(stats_batch)

      # 并行处理各类型统计数据
      tasks = Enum.map(grouped_stats, fn {type, stats_list} ->
        Task.async(fn -> process_stats_by_type(type, stats_list, options) end)
      end)

      results = Task.await_many(tasks, 60_000)

      batch_results = %{
        total_processed: length(stats_batch),
        results_by_type: Enum.zip(Map.keys(grouped_stats), results),
        processing_time: DateTime.utc_now(),
        success_count: count_successful_results(results),
        error_count: count_error_results(results)
      }

      Logger.info("✅ [数据统计服务] 批量统计数据创建完成: 成功 #{batch_results.success_count}, 失败 #{batch_results.error_count}")
      {:ok, batch_results}
    rescue
      exception ->
        Logger.error("💥 [数据统计服务] 批量创建统计数据异常: #{inspect(exception)}")
        {:error, :batch_create_failed}
    end
  end

  @doc """
  生成综合统计报告

  ## 参数
  - `report_config` - 报告配置
  - `options` - 选项参数

  ## 返回
  - `{:ok, comprehensive_report}` - 成功
  - `{:error, reason}` - 失败
  """
  def generate_comprehensive_report(report_config, options \\ []) do
    Logger.info("📊 [数据统计服务] 生成综合统计报告: #{inspect(report_config[:report_type])}")

    try do
      with {:ok, validated_config} <- validate_report_config(report_config),
           {:ok, raw_data} <- collect_report_data(validated_config, options),
           {:ok, processed_data} <- process_report_data(raw_data, validated_config, options),
           {:ok, formatted_report} <- format_comprehensive_report(processed_data, validated_config, options) do

        # 保存报告记录
        report_record = %{
          report_type: validated_config.report_type,
          report_date: Date.utc_today(),
          data_summary: extract_report_summary(formatted_report),
          status: "completed",
          generated_at: DateTime.utc_now()
        }

        case SystemReportRepository.create_system_report(report_record, options) do
          {:ok, _saved_report} ->
            Logger.info("✅ [数据统计服务] 综合统计报告生成成功")
            {:ok, formatted_report}
          {:error, save_error} ->
            Logger.warn("⚠️ [数据统计服务] 报告保存失败，但报告生成成功: #{inspect(save_error)}")
            {:ok, formatted_report}
        end
      else
        {:error, reason} ->
          Logger.error("❌ [数据统计服务] 综合统计报告生成失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [数据统计服务] 生成综合统计报告异常: #{inspect(exception)}")
        {:error, :generate_report_failed}
    end
  end

  @doc """
  执行数据分析任务

  ## 参数
  - `analysis_config` - 分析配置
  - `options` - 选项参数

  ## 返回
  - `{:ok, analysis_results}` - 成功
  - `{:error, reason}` - 失败
  """
  def execute_data_analysis(analysis_config, options \\ []) do
    Logger.info("📊 [数据统计服务] 执行数据分析任务: #{inspect(analysis_config[:analysis_type])}")

    try do
      with {:ok, validated_config} <- validate_analysis_config(analysis_config),
           {:ok, analysis_results} <- perform_data_analysis(validated_config, options),
           :ok <- store_analysis_results(analysis_results, validated_config, options) do

        Logger.info("✅ [数据统计服务] 数据分析任务执行成功")
        {:ok, analysis_results}
      else
        {:error, reason} ->
          Logger.error("❌ [数据统计服务] 数据分析任务执行失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [数据统计服务] 执行数据分析任务异常: #{inspect(exception)}")
        {:error, :execute_analysis_failed}
    end
  end

  @doc """
  获取实时统计数据

  ## 参数
  - `metrics` - 指标列表
  - `options` - 选项参数

  ## 返回
  - `{:ok, realtime_data}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_realtime_statistics(metrics \\ [:all], options \\ []) do
    Logger.info("📊 [数据统计服务] 获取实时统计数据: #{inspect(metrics)}")

    try do
      case DataStatisticsQueryBuilder.get_realtime_dashboard(options) do
        {:ok, dashboard_data} ->
          filtered_data = filter_metrics_data(dashboard_data, metrics)

          realtime_data = %{
            metrics: filtered_data,
            last_updated: DateTime.utc_now(),
            data_freshness: calculate_data_freshness(filtered_data),
            system_status: assess_system_status(filtered_data)
          }

          Logger.info("✅ [数据统计服务] 实时统计数据获取成功")
          {:ok, realtime_data}

        {:error, reason} ->
          Logger.error("❌ [数据统计服务] 实时统计数据获取失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [数据统计服务] 获取实时统计数据异常: #{inspect(exception)}")
        {:error, :get_realtime_failed}
    end
  end

  @doc """
  执行统计数据同步

  ## 参数
  - `sync_config` - 同步配置
  - `options` - 选项参数

  ## 返回
  - `{:ok, sync_results}` - 成功
  - `{:error, reason}` - 失败
  """
  def synchronize_statistics(sync_config \\ %{}, options \\ []) do
    Logger.info("📊 [数据统计服务] 执行统计数据同步")

    try do
      sync_tasks = [
        {:online_stats, fn -> sync_online_statistics(sync_config, options) end},
        {:user_stats, fn -> sync_user_statistics(sync_config, options) end},
        {:payment_stats, fn -> sync_payment_statistics(sync_config, options) end},
        {:channel_stats, fn -> sync_channel_statistics(sync_config, options) end},
        {:retention_stats, fn -> sync_retention_statistics(sync_config, options) end}
      ]

      # 并行执行同步任务
      tasks = Enum.map(sync_tasks, fn {type, task_fn} ->
        Task.async(fn -> {type, task_fn.()} end)
      end)

      results = Task.await_many(tasks, 120_000)

      sync_results = %{
        sync_timestamp: DateTime.utc_now(),
        results: results,
        success_count: count_sync_successes(results),
        error_count: count_sync_errors(results),
        total_synced: length(results)
      }

      Logger.info("✅ [数据统计服务] 统计数据同步完成: 成功 #{sync_results.success_count}, 失败 #{sync_results.error_count}")
      {:ok, sync_results}
    rescue
      exception ->
        Logger.error("💥 [数据统计服务] 统计数据同步异常: #{inspect(exception)}")
        {:error, :sync_failed}
    end
  end

  # ==================== 数据质量管理 ====================

  @doc """
  验证统计数据质量

  ## 参数
  - `validation_config` - 验证配置
  - `options` - 选项参数

  ## 返回
  - `{:ok, validation_results}` - 成功
  - `{:error, reason}` - 失败
  """
  def validate_data_quality(validation_config \\ %{}, options \\ []) do
    Logger.info("📊 [数据统计服务] 验证统计数据质量")

    try do
      validation_tasks = [
        {:completeness, fn -> check_data_completeness(validation_config, options) end},
        {:consistency, fn -> check_data_consistency(validation_config, options) end},
        {:accuracy, fn -> check_data_accuracy(validation_config, options) end},
        {:timeliness, fn -> check_data_timeliness(validation_config, options) end}
      ]

      # 并行执行验证任务
      tasks = Enum.map(validation_tasks, fn {type, task_fn} ->
        Task.async(fn -> {type, task_fn.()} end)
      end)

      results = Task.await_many(tasks, 60_000)

      validation_results = %{
        validation_timestamp: DateTime.utc_now(),
        quality_checks: results,
        overall_score: calculate_quality_score(results),
        issues_found: extract_quality_issues(results),
        recommendations: generate_quality_recommendations(results)
      }

      Logger.info("✅ [数据统计服务] 数据质量验证完成，质量评分: #{validation_results.overall_score}")
      {:ok, validation_results}
    rescue
      exception ->
        Logger.error("💥 [数据统计服务] 验证统计数据质量异常: #{inspect(exception)}")
        {:error, :validate_quality_failed}
    end
  end

  @doc """
  修复数据质量问题

  ## 参数
  - `repair_config` - 修复配置
  - `options` - 选项参数

  ## 返回
  - `{:ok, repair_results}` - 成功
  - `{:error, reason}` - 失败
  """
  def repair_data_issues(repair_config, options \\ []) do
    Logger.info("📊 [数据统计服务] 修复数据质量问题")

    try do
      with {:ok, validated_config} <- validate_repair_config(repair_config),
           {:ok, issue_analysis} <- analyze_data_issues(validated_config, options),
           {:ok, repair_plan} <- create_repair_plan(issue_analysis, validated_config, options),
           {:ok, repair_results} <- execute_repair_plan(repair_plan, options) do

        Logger.info("✅ [数据统计服务] 数据质量问题修复完成")
        {:ok, repair_results}
      else
        {:error, reason} ->
          Logger.error("❌ [数据统计服务] 数据质量问题修复失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [数据统计服务] 修复数据质量问题异常: #{inspect(exception)}")
        {:error, :repair_issues_failed}
    end
  end

  # ==================== 私有辅助函数 ====================

  # 验证报表数据
  defp validate_report_data(report_data) do
    required_fields = [:report_type, :report_date]

    case check_required_fields(report_data, required_fields) do
      :ok ->
        validated_data = report_data
        |> Map.put_new(:status, "pending")
        |> Map.put_new(:created_at, DateTime.utc_now())

        {:ok, validated_data}
      {:error, missing_fields} ->
        {:error, {:missing_fields, missing_fields}}
    end
  end

  # 触发报表通知
  defp trigger_report_notifications(system_report, options) do
    if Keyword.get(options, :send_notifications, false) do
      # 这里可以集成通知系统
      Logger.info("📧 [数据统计服务] 触发报表通知: #{system_report.id}")
    end
    :ok
  end

  # 按类型分组统计数据
  defp group_stats_by_type(stats_batch) do
    Enum.group_by(stats_batch, fn stat ->
      Map.get(stat, :type, :unknown)
    end)
  end

  # 按类型处理统计数据
  defp process_stats_by_type(type, stats_list, options) do
    case type do
      :online_stats ->
        Enum.map(stats_list, &OnlineStatsRepository.create_online_stats(&1, options))
      :user_stats ->
        Enum.map(stats_list, &UserStatsRepository.create_user_stats(&1, options))
      :payment_stats ->
        Enum.map(stats_list, &PaymentStatsRepository.create_payment_stats(&1, options))
      :channel_stats ->
        Enum.map(stats_list, &ChannelStatsRepository.create_channel_stats(&1, options))
      :retention_stats ->
        Enum.map(stats_list, &RetentionStatsRepository.create_retention_stats(&1, options))
      :ltv_stats ->
        Enum.map(stats_list, &LtvStatsRepository.create_ltv_stats(&1, options))
      :coin_stats ->
        Enum.map(stats_list, &CoinStatsRepository.create_coin_stats(&1, options))
      :robot_stats ->
        Enum.map(stats_list, &RobotStatsRepository.create_robot_stats(&1, options))
      _ ->
        [{:error, :unknown_stats_type}]
    end
  end

  # 统计成功结果数量
  defp count_successful_results(results) do
    results
    |> List.flatten()
    |> Enum.count(fn result -> match?({:ok, _}, result) end)
  end

  # 统计错误结果数量
  defp count_error_results(results) do
    results
    |> List.flatten()
    |> Enum.count(fn result -> match?({:error, _}, result) end)
  end

  # 验证报告配置
  defp validate_report_config(report_config) do
    required_fields = [:report_type, :date_range]

    case check_required_fields(report_config, required_fields) do
      :ok -> {:ok, report_config}
      error -> error
    end
  end

  # 收集报告数据
  defp collect_report_data(config, options) do
    case DataStatisticsQueryBuilder.get_comprehensive_stats(config.date_range, options) do
      {:ok, data} -> {:ok, data}
      error -> error
    end
  end

  # 处理报告数据
  defp process_report_data(raw_data, config, _options) do
    processed_data = raw_data
    |> add_calculated_metrics()
    |> add_trend_analysis()
    |> add_comparative_analysis()

    {:ok, processed_data}
  end

  # 格式化综合报告
  defp format_comprehensive_report(processed_data, config, _options) do
    formatted_report = %{
      report_type: config.report_type,
      generated_at: DateTime.utc_now(),
      data: processed_data,
      summary: generate_report_summary(processed_data),
      insights: generate_report_insights(processed_data),
      recommendations: generate_report_recommendations(processed_data)
    }

    {:ok, formatted_report}
  end

  # 提取报告摘要
  defp extract_report_summary(formatted_report) do
    %{
      total_metrics: count_report_metrics(formatted_report),
      key_insights: length(formatted_report.insights),
      recommendations: length(formatted_report.recommendations)
    }
  end

  # 检查必需字段
  defp check_required_fields(data, required_fields) do
    missing_fields = Enum.filter(required_fields, fn field ->
      not Map.has_key?(data, field) or is_nil(Map.get(data, field))
    end)

    if Enum.empty?(missing_fields) do
      :ok
    else
      {:error, {:missing_fields, missing_fields}}
    end
  end

  # 添加计算指标
  defp add_calculated_metrics(data) do
    # 添加计算得出的指标
    Map.put(data, :calculated_metrics, %{
      growth_rates: calculate_growth_rates(data),
      conversion_rates: calculate_conversion_rates(data),
      efficiency_metrics: calculate_efficiency_metrics(data)
    })
  end

  # 添加趋势分析
  defp add_trend_analysis(data) do
    Map.put(data, :trend_analysis, %{
      user_trends: analyze_user_trends(data),
      revenue_trends: analyze_revenue_trends(data),
      engagement_trends: analyze_engagement_trends(data)
    })
  end

  # 添加对比分析
  defp add_comparative_analysis(data) do
    Map.put(data, :comparative_analysis, %{
      period_comparison: compare_periods(data),
      channel_comparison: compare_channels(data),
      segment_comparison: compare_segments(data)
    })
  end

  # 生成报告摘要
  defp generate_report_summary(processed_data) do
    %{
      key_metrics: extract_key_metrics(processed_data),
      performance_highlights: extract_performance_highlights(processed_data),
      areas_of_concern: extract_areas_of_concern(processed_data)
    }
  end

  # 生成报告洞察
  defp generate_report_insights(processed_data) do
    [
      analyze_user_behavior_insights(processed_data),
      analyze_revenue_insights(processed_data),
      analyze_retention_insights(processed_data),
      analyze_channel_insights(processed_data)
    ]
    |> Enum.filter(&(&1 != nil))
  end

  # 生成报告建议
  defp generate_report_recommendations(processed_data) do
    [
      recommend_user_acquisition_strategies(processed_data),
      recommend_retention_improvements(processed_data),
      recommend_monetization_optimizations(processed_data),
      recommend_operational_improvements(processed_data)
    ]
    |> Enum.filter(&(&1 != nil))
  end

  # 计算报告指标数量
  defp count_report_metrics(formatted_report) do
    # 计算报告中包含的指标总数
    base_metrics = map_size(formatted_report.data)
    calculated_metrics = map_size(Map.get(formatted_report.data, :calculated_metrics, %{}))
    base_metrics + calculated_metrics
  end

  # 辅助函数的占位符实现
  defp calculate_growth_rates(_data), do: %{}
  defp calculate_conversion_rates(_data), do: %{}
  defp calculate_efficiency_metrics(_data), do: %{}
  defp analyze_user_trends(_data), do: %{}
  defp analyze_revenue_trends(_data), do: %{}
  defp analyze_engagement_trends(_data), do: %{}
  defp compare_periods(_data), do: %{}
  defp compare_channels(_data), do: %{}
  defp compare_segments(_data), do: %{}
  defp extract_key_metrics(_data), do: %{}
  defp extract_performance_highlights(_data), do: %{}
  defp extract_areas_of_concern(_data), do: %{}
  defp analyze_user_behavior_insights(_data), do: nil
  defp analyze_revenue_insights(_data), do: nil
  defp analyze_retention_insights(_data), do: nil
  defp analyze_channel_insights(_data), do: nil
  defp recommend_user_acquisition_strategies(_data), do: nil
  defp recommend_retention_improvements(_data), do: nil
  defp recommend_monetization_optimizations(_data), do: nil
  defp recommend_operational_improvements(_data), do: nil

  # ==================== 分析配置验证 ====================

  # 验证分析配置
  defp validate_analysis_config(analysis_config) do
    required_fields = [:analysis_type, :date_range]

    case check_required_fields(analysis_config, required_fields) do
      :ok -> {:ok, analysis_config}
      error -> error
    end
  end

  # 执行数据分析
  defp perform_data_analysis(config, options) do
    case config.analysis_type do
      :trend_analysis ->
        DataStatisticsQueryBuilder.get_trend_analysis(config.metric_type, config.days, options)
      :user_behavior ->
        DataStatisticsQueryBuilder.get_user_behavior_analysis(config.behavior_type, config.date_range, options)
      :revenue_analysis ->
        DataStatisticsQueryBuilder.get_revenue_analysis(config.analysis_period, options)
      :cohort_analysis ->
        DataStatisticsQueryBuilder.get_cohort_analysis(config.cohort_date, config.analysis_days, options)
      :channel_comparison ->
        DataStatisticsQueryBuilder.get_channel_comparison(config.date_range, config.channel_list, options)
      :user_value_analysis ->
        DataStatisticsQueryBuilder.get_user_value_analysis(config.date_range, config.segment_type, options)
      :system_health ->
        DataStatisticsQueryBuilder.get_system_health_monitor(config.monitor_type, options)
      _ ->
        {:error, :unsupported_analysis_type}
    end
  end

  # 存储分析结果
  defp store_analysis_results(analysis_results, config, options) do
    if Keyword.get(options, :store_results, true) do
      report_data = %{
        report_type: "analysis_#{config.analysis_type}",
        report_date: Date.utc_today(),
        data_summary: %{
          analysis_type: config.analysis_type,
          results_count: count_analysis_results(analysis_results),
          generated_at: DateTime.utc_now()
        },
        status: "completed"
      }

      case SystemReportRepository.create_system_report(report_data, options) do
        {:ok, _report} -> :ok
        {:error, _error} -> :ok  # 不因存储失败而影响分析结果
      end
    else
      :ok
    end
  end

  # 统计分析结果数量
  defp count_analysis_results(analysis_results) do
    case analysis_results do
      %{trend_data: data} when is_list(data) -> length(data)
      %{behavior_data: data} when is_list(data) -> length(data)
      %{channels: data} when is_list(data) -> length(data)
      _ -> 1
    end
  end

  # ==================== 实时数据处理 ====================

  # 过滤指标数据
  defp filter_metrics_data(dashboard_data, metrics) do
    case metrics do
      [:all] -> dashboard_data
      specific_metrics ->
        Map.take(dashboard_data, specific_metrics)
    end
  end

  # 计算数据新鲜度
  defp calculate_data_freshness(filtered_data) do
    now = DateTime.utc_now()

    freshness_scores = Enum.map(filtered_data, fn {_key, data} ->
      case Map.get(data, :last_updated) do
        nil -> 0
        last_updated ->
          diff_minutes = DateTime.diff(now, last_updated, :minute)
          max(0, 100 - diff_minutes)  # 100分满分，每分钟扣1分
      end
    end)

    if Enum.empty?(freshness_scores) do
      0
    else
      Enum.sum(freshness_scores) / length(freshness_scores)
    end
  end

  # 评估系统状态
  defp assess_system_status(filtered_data) do
    status_indicators = [
      assess_online_status(Map.get(filtered_data, :online_realtime)),
      assess_user_status(Map.get(filtered_data, :user_realtime)),
      assess_payment_status(Map.get(filtered_data, :payment_realtime)),
      assess_system_health_status(Map.get(filtered_data, :system_health))
    ]

    error_count = Enum.count(status_indicators, &(&1 == :error))
    warning_count = Enum.count(status_indicators, &(&1 == :warning))

    cond do
      error_count > 0 -> :critical
      warning_count > 1 -> :warning
      warning_count == 1 -> :caution
      true -> :healthy
    end
  end

  # 评估各项状态的辅助函数
  defp assess_online_status(%{average_online_count: count}) when count < 50, do: :error
  defp assess_online_status(%{average_online_count: count}) when count < 100, do: :warning
  defp assess_online_status(_), do: :ok

  defp assess_user_status(%{new_user_count: count}) when count < 10, do: :warning
  defp assess_user_status(_), do: :ok

  defp assess_payment_status(%{total_revenue: revenue}) when revenue < 100, do: :warning
  defp assess_payment_status(_), do: :ok

  defp assess_system_health_status(%{error_count: count}) when count > 50, do: :error
  defp assess_system_health_status(%{error_count: count}) when count > 20, do: :warning
  defp assess_system_health_status(_), do: :ok

  # ==================== 数据同步处理 ====================

  # 同步在线统计
  defp sync_online_statistics(sync_config, options) do
    Logger.info("📈 [数据统计服务] 同步在线统计数据")

    try do
      # 这里可以集成外部数据源
      # 目前返回模拟结果
      {:ok, %{synced_records: 0, last_sync: DateTime.utc_now()}}
    rescue
      exception ->
        Logger.error("💥 [数据统计服务] 同步在线统计异常: #{inspect(exception)}")
        {:error, :sync_online_failed}
    end
  end

  # 同步用户统计
  defp sync_user_statistics(sync_config, options) do
    Logger.info("👥 [数据统计服务] 同步用户统计数据")

    try do
      {:ok, %{synced_records: 0, last_sync: DateTime.utc_now()}}
    rescue
      exception ->
        Logger.error("💥 [数据统计服务] 同步用户统计异常: #{inspect(exception)}")
        {:error, :sync_user_failed}
    end
  end

  # 同步支付统计
  defp sync_payment_statistics(sync_config, options) do
    Logger.info("💰 [数据统计服务] 同步支付统计数据")

    try do
      {:ok, %{synced_records: 0, last_sync: DateTime.utc_now()}}
    rescue
      exception ->
        Logger.error("💥 [数据统计服务] 同步支付统计异常: #{inspect(exception)}")
        {:error, :sync_payment_failed}
    end
  end

  # 同步渠道统计
  defp sync_channel_statistics(sync_config, options) do
    Logger.info("📊 [数据统计服务] 同步渠道统计数据")

    try do
      {:ok, %{synced_records: 0, last_sync: DateTime.utc_now()}}
    rescue
      exception ->
        Logger.error("💥 [数据统计服务] 同步渠道统计异常: #{inspect(exception)}")
        {:error, :sync_channel_failed}
    end
  end

  # 同步留存统计
  defp sync_retention_statistics(sync_config, options) do
    Logger.info("📈 [数据统计服务] 同步留存统计数据")

    try do
      {:ok, %{synced_records: 0, last_sync: DateTime.utc_now()}}
    rescue
      exception ->
        Logger.error("💥 [数据统计服务] 同步留存统计异常: #{inspect(exception)}")
        {:error, :sync_retention_failed}
    end
  end

  # 统计同步成功数量
  defp count_sync_successes(results) do
    Enum.count(results, fn {_type, result} -> match?({:ok, _}, result) end)
  end

  # 统计同步错误数量
  defp count_sync_errors(results) do
    Enum.count(results, fn {_type, result} -> match?({:error, _}, result) end)
  end

  # ==================== 数据质量检查 ====================

  # 检查数据完整性
  defp check_data_completeness(validation_config, options) do
    Logger.info("🔍 [数据统计服务] 检查数据完整性")

    try do
      today = Date.utc_today()
      yesterday = Date.add(today, -1)

      # 检查各类统计数据是否完整
      completeness_checks = [
        check_online_stats_completeness(yesterday, today, options),
        check_user_stats_completeness(yesterday, today, options),
        check_payment_stats_completeness(yesterday, today, options),
        check_channel_stats_completeness(yesterday, today, options),
        check_retention_stats_completeness(yesterday, today, options)
      ]

      total_checks = length(completeness_checks)
      passed_checks = Enum.count(completeness_checks, &(&1 == :ok))

      completeness_score = if total_checks > 0, do: passed_checks / total_checks * 100, else: 0

      {:ok, %{
        score: completeness_score,
        total_checks: total_checks,
        passed_checks: passed_checks,
        failed_checks: total_checks - passed_checks,
        details: completeness_checks
      }}
    rescue
      exception ->
        Logger.error("💥 [数据统计服务] 检查数据完整性异常: #{inspect(exception)}")
        {:error, :check_completeness_failed}
    end
  end

  # 检查数据一致性
  defp check_data_consistency(validation_config, options) do
    Logger.info("🔍 [数据统计服务] 检查数据一致性")

    try do
      # 检查跨表数据一致性
      consistency_checks = [
        check_user_payment_consistency(options),
        check_channel_conversion_consistency(options),
        check_retention_user_consistency(options)
      ]

      total_checks = length(consistency_checks)
      passed_checks = Enum.count(consistency_checks, &(&1 == :ok))

      consistency_score = if total_checks > 0, do: passed_checks / total_checks * 100, else: 0

      {:ok, %{
        score: consistency_score,
        total_checks: total_checks,
        passed_checks: passed_checks,
        failed_checks: total_checks - passed_checks,
        details: consistency_checks
      }}
    rescue
      exception ->
        Logger.error("💥 [数据统计服务] 检查数据一致性异常: #{inspect(exception)}")
        {:error, :check_consistency_failed}
    end
  end

  # 检查数据准确性
  defp check_data_accuracy(validation_config, options) do
    Logger.info("🔍 [数据统计服务] 检查数据准确性")

    try do
      # 检查数据准确性
      accuracy_checks = [
        check_calculation_accuracy(options),
        check_aggregation_accuracy(options),
        check_rate_calculation_accuracy(options)
      ]

      total_checks = length(accuracy_checks)
      passed_checks = Enum.count(accuracy_checks, &(&1 == :ok))

      accuracy_score = if total_checks > 0, do: passed_checks / total_checks * 100, else: 0

      {:ok, %{
        score: accuracy_score,
        total_checks: total_checks,
        passed_checks: passed_checks,
        failed_checks: total_checks - passed_checks,
        details: accuracy_checks
      }}
    rescue
      exception ->
        Logger.error("💥 [数据统计服务] 检查数据准确性异常: #{inspect(exception)}")
        {:error, :check_accuracy_failed}
    end
  end

  # 检查数据时效性
  defp check_data_timeliness(validation_config, options) do
    Logger.info("🔍 [数据统计服务] 检查数据时效性")

    try do
      # 检查数据更新时效性
      timeliness_checks = [
        check_realtime_data_timeliness(options),
        check_daily_stats_timeliness(options),
        check_report_generation_timeliness(options)
      ]

      total_checks = length(timeliness_checks)
      passed_checks = Enum.count(timeliness_checks, &(&1 == :ok))

      timeliness_score = if total_checks > 0, do: passed_checks / total_checks * 100, else: 0

      {:ok, %{
        score: timeliness_score,
        total_checks: total_checks,
        passed_checks: passed_checks,
        failed_checks: total_checks - passed_checks,
        details: timeliness_checks
      }}
    rescue
      exception ->
        Logger.error("💥 [数据统计服务] 检查数据时效性异常: #{inspect(exception)}")
        {:error, :check_timeliness_failed}
    end
  end

  # 计算质量评分
  defp calculate_quality_score(results) do
    scores = Enum.map(results, fn {_type, result} ->
      case result do
        {:ok, %{score: score}} -> score
        _ -> 0
      end
    end)

    if Enum.empty?(scores) do
      0
    else
      Enum.sum(scores) / length(scores)
    end
  end

  # 提取质量问题
  defp extract_quality_issues(results) do
    Enum.flat_map(results, fn {type, result} ->
      case result do
        {:ok, %{failed_checks: failed_count}} when failed_count > 0 ->
          [%{type: type, failed_checks: failed_count}]
        {:error, reason} ->
          [%{type: type, error: reason}]
        _ ->
          []
      end
    end)
  end

  # 生成质量建议
  defp generate_quality_recommendations(results) do
    issues = extract_quality_issues(results)

    Enum.map(issues, fn issue ->
      case issue.type do
        :completeness -> "建议检查数据采集流程，确保所有统计数据按时生成"
        :consistency -> "建议检查跨表数据关联，修复数据不一致问题"
        :accuracy -> "建议检查计算逻辑，确保统计指标计算准确"
        :timeliness -> "建议优化数据处理流程，提高数据更新时效性"
        _ -> "建议进行详细的数据质量分析"
      end
    end)
  end

  # 数据完整性检查辅助函数
  defp check_online_stats_completeness(start_date, end_date, options) do
    case OnlineStatsRepository.get_stats_by_date_range(start_date, end_date, options) do
      {:ok, stats} when length(stats) > 0 -> :ok
      _ -> :failed
    end
  end

  defp check_user_stats_completeness(start_date, end_date, options) do
    case UserStatsRepository.get_stats_by_date_range(start_date, end_date, options) do
      {:ok, stats} when length(stats) > 0 -> :ok
      _ -> :failed
    end
  end

  defp check_payment_stats_completeness(start_date, end_date, options) do
    case PaymentStatsRepository.get_stats_by_date_range(start_date, end_date, options) do
      {:ok, stats} when length(stats) > 0 -> :ok
      _ -> :failed
    end
  end

  defp check_channel_stats_completeness(start_date, end_date, options) do
    case ChannelStatsRepository.get_stats_by_date_range(start_date, end_date, options) do
      {:ok, stats} when length(stats) > 0 -> :ok
      _ -> :failed
    end
  end

  defp check_retention_stats_completeness(start_date, end_date, options) do
    case RetentionStatsRepository.get_stats_by_date_range(start_date, end_date, options) do
      {:ok, stats} when length(stats) > 0 -> :ok
      _ -> :failed
    end
  end

  # 数据一致性检查辅助函数
  defp check_user_payment_consistency(_options) do
    # 检查用户统计和支付统计的一致性
    :ok  # 占位符实现
  end

  defp check_channel_conversion_consistency(_options) do
    # 检查渠道统计和转化数据的一致性
    :ok  # 占位符实现
  end

  defp check_retention_user_consistency(_options) do
    # 检查留存统计和用户统计的一致性
    :ok  # 占位符实现
  end

  # 数据准确性检查辅助函数
  defp check_calculation_accuracy(_options) do
    # 检查计算准确性
    :ok  # 占位符实现
  end

  defp check_aggregation_accuracy(_options) do
    # 检查聚合准确性
    :ok  # 占位符实现
  end

  defp check_rate_calculation_accuracy(_options) do
    # 检查比率计算准确性
    :ok  # 占位符实现
  end

  # 数据时效性检查辅助函数
  defp check_realtime_data_timeliness(_options) do
    # 检查实时数据时效性
    :ok  # 占位符实现
  end

  defp check_daily_stats_timeliness(_options) do
    # 检查日统计数据时效性
    :ok  # 占位符实现
  end

  defp check_report_generation_timeliness(_options) do
    # 检查报告生成时效性
    :ok  # 占位符实现
  end

  # ==================== 数据修复处理 ====================

  # 验证修复配置
  defp validate_repair_config(repair_config) do
    required_fields = [:repair_type, :target_date_range]

    case check_required_fields(repair_config, required_fields) do
      :ok -> {:ok, repair_config}
      error -> error
    end
  end

  # 分析数据问题
  defp analyze_data_issues(config, options) do
    Logger.info("🔍 [数据统计服务] 分析数据问题: #{config.repair_type}")

    try do
      {start_date, end_date} = extract_date_range(config.target_date_range)

      issue_analysis = case config.repair_type do
        :missing_data -> analyze_missing_data_issues(start_date, end_date, options)
        :inconsistent_data -> analyze_inconsistent_data_issues(start_date, end_date, options)
        :duplicate_data -> analyze_duplicate_data_issues(start_date, end_date, options)
        :invalid_data -> analyze_invalid_data_issues(start_date, end_date, options)
        _ -> {:error, :unsupported_repair_type}
      end

      case issue_analysis do
        {:ok, analysis} ->
          Logger.info("✅ [数据统计服务] 数据问题分析完成")
          {:ok, analysis}
        error ->
          Logger.error("❌ [数据统计服务] 数据问题分析失败: #{inspect(error)}")
          error
      end
    rescue
      exception ->
        Logger.error("💥 [数据统计服务] 分析数据问题异常: #{inspect(exception)}")
        {:error, :analyze_issues_failed}
    end
  end

  # 创建修复计划
  defp create_repair_plan(issue_analysis, config, options) do
    Logger.info("📋 [数据统计服务] 创建修复计划")

    try do
      repair_plan = %{
        repair_type: config.repair_type,
        issues_found: issue_analysis.issues_count,
        repair_actions: generate_repair_actions(issue_analysis, config),
        estimated_time: estimate_repair_time(issue_analysis),
        priority: determine_repair_priority(issue_analysis),
        backup_required: should_backup_before_repair(config),
        rollback_plan: create_rollback_plan(issue_analysis, config)
      }

      Logger.info("✅ [数据统计服务] 修复计划创建完成")
      {:ok, repair_plan}
    rescue
      exception ->
        Logger.error("💥 [数据统计服务] 创建修复计划异常: #{inspect(exception)}")
        {:error, :create_repair_plan_failed}
    end
  end

  # 执行修复计划
  defp execute_repair_plan(repair_plan, options) do
    Logger.info("🔧 [数据统计服务] 执行修复计划: #{repair_plan.repair_type}")

    try do
      # 备份数据（如果需要）
      backup_result = if repair_plan.backup_required do
        create_data_backup(repair_plan, options)
      else
        {:ok, :no_backup_needed}
      end

      case backup_result do
        {:ok, _} ->
          # 执行修复操作
          repair_results = execute_repair_actions(repair_plan.repair_actions, options)

          # 验证修复结果
          validation_result = validate_repair_results(repair_results, repair_plan, options)

          final_results = %{
            repair_type: repair_plan.repair_type,
            backup_result: backup_result,
            repair_results: repair_results,
            validation_result: validation_result,
            completed_at: DateTime.utc_now(),
            success_rate: calculate_repair_success_rate(repair_results)
          }

          Logger.info("✅ [数据统计服务] 修复计划执行完成，成功率: #{final_results.success_rate}%")
          {:ok, final_results}

        {:error, backup_error} ->
          Logger.error("❌ [数据统计服务] 数据备份失败: #{inspect(backup_error)}")
          {:error, {:backup_failed, backup_error}}
      end
    rescue
      exception ->
        Logger.error("💥 [数据统计服务] 执行修复计划异常: #{inspect(exception)}")
        {:error, :execute_repair_plan_failed}
    end
  end

  # 分析缺失数据问题
  defp analyze_missing_data_issues(start_date, end_date, options) do
    Logger.info("🔍 [数据统计服务] 分析缺失数据问题: #{start_date} - #{end_date}")

    # 检查各类统计数据的缺失情况
    missing_issues = [
      check_missing_online_stats(start_date, end_date, options),
      check_missing_user_stats(start_date, end_date, options),
      check_missing_payment_stats(start_date, end_date, options),
      check_missing_channel_stats(start_date, end_date, options),
      check_missing_retention_stats(start_date, end_date, options)
    ]

    total_missing = Enum.sum(Enum.map(missing_issues, &elem(&1, 1)))

    {:ok, %{
      analysis_type: :missing_data,
      date_range: {start_date, end_date},
      issues_count: total_missing,
      missing_by_type: missing_issues,
      severity: determine_missing_data_severity(total_missing)
    }}
  end

  # 分析不一致数据问题
  defp analyze_inconsistent_data_issues(start_date, end_date, options) do
    Logger.info("🔍 [数据统计服务] 分析不一致数据问题: #{start_date} - #{end_date}")

    # 检查数据一致性问题
    inconsistency_issues = [
      check_user_payment_inconsistencies(start_date, end_date, options),
      check_channel_conversion_inconsistencies(start_date, end_date, options),
      check_retention_user_inconsistencies(start_date, end_date, options)
    ]

    total_inconsistencies = Enum.sum(Enum.map(inconsistency_issues, &elem(&1, 1)))

    {:ok, %{
      analysis_type: :inconsistent_data,
      date_range: {start_date, end_date},
      issues_count: total_inconsistencies,
      inconsistencies_by_type: inconsistency_issues,
      severity: determine_inconsistency_severity(total_inconsistencies)
    }}
  end

  # 分析重复数据问题
  defp analyze_duplicate_data_issues(start_date, end_date, options) do
    Logger.info("🔍 [数据统计服务] 分析重复数据问题: #{start_date} - #{end_date}")

    # 检查重复数据
    duplicate_issues = [
      check_duplicate_online_stats(start_date, end_date, options),
      check_duplicate_user_stats(start_date, end_date, options),
      check_duplicate_payment_stats(start_date, end_date, options)
    ]

    total_duplicates = Enum.sum(Enum.map(duplicate_issues, &elem(&1, 1)))

    {:ok, %{
      analysis_type: :duplicate_data,
      date_range: {start_date, end_date},
      issues_count: total_duplicates,
      duplicates_by_type: duplicate_issues,
      severity: determine_duplicate_severity(total_duplicates)
    }}
  end

  # 分析无效数据问题
  defp analyze_invalid_data_issues(start_date, end_date, options) do
    Logger.info("🔍 [数据统计服务] 分析无效数据问题: #{start_date} - #{end_date}")

    # 检查无效数据
    invalid_issues = [
      check_invalid_online_stats(start_date, end_date, options),
      check_invalid_user_stats(start_date, end_date, options),
      check_invalid_payment_stats(start_date, end_date, options)
    ]

    total_invalid = Enum.sum(Enum.map(invalid_issues, &elem(&1, 1)))

    {:ok, %{
      analysis_type: :invalid_data,
      date_range: {start_date, end_date},
      issues_count: total_invalid,
      invalid_by_type: invalid_issues,
      severity: determine_invalid_data_severity(total_invalid)
    }}
  end

  # 生成修复操作
  defp generate_repair_actions(issue_analysis, config) do
    case issue_analysis.analysis_type do
      :missing_data -> generate_missing_data_repair_actions(issue_analysis, config)
      :inconsistent_data -> generate_inconsistency_repair_actions(issue_analysis, config)
      :duplicate_data -> generate_duplicate_repair_actions(issue_analysis, config)
      :invalid_data -> generate_invalid_data_repair_actions(issue_analysis, config)
      _ -> []
    end
  end

  # 生成缺失数据修复操作
  defp generate_missing_data_repair_actions(issue_analysis, _config) do
    Enum.flat_map(issue_analysis.missing_by_type, fn {type, count} ->
      if count > 0 do
        [%{
          action: :regenerate_missing_data,
          target_type: type,
          count: count,
          priority: :high
        }]
      else
        []
      end
    end)
  end

  # 生成不一致数据修复操作
  defp generate_inconsistency_repair_actions(issue_analysis, _config) do
    Enum.flat_map(issue_analysis.inconsistencies_by_type, fn {type, count} ->
      if count > 0 do
        [%{
          action: :fix_data_inconsistency,
          target_type: type,
          count: count,
          priority: :medium
        }]
      else
        []
      end
    end)
  end

  # 生成重复数据修复操作
  defp generate_duplicate_repair_actions(issue_analysis, _config) do
    Enum.flat_map(issue_analysis.duplicates_by_type, fn {type, count} ->
      if count > 0 do
        [%{
          action: :remove_duplicate_data,
          target_type: type,
          count: count,
          priority: :low
        }]
      else
        []
      end
    end)
  end

  # 生成无效数据修复操作
  defp generate_invalid_data_repair_actions(issue_analysis, _config) do
    Enum.flat_map(issue_analysis.invalid_by_type, fn {type, count} ->
      if count > 0 do
        [%{
          action: :fix_invalid_data,
          target_type: type,
          count: count,
          priority: :high
        }]
      else
        []
      end
    end)
  end

  # 估算修复时间
  defp estimate_repair_time(issue_analysis) do
    base_time = case issue_analysis.analysis_type do
      :missing_data -> issue_analysis.issues_count * 2  # 每个缺失记录2分钟
      :inconsistent_data -> issue_analysis.issues_count * 3  # 每个不一致3分钟
      :duplicate_data -> issue_analysis.issues_count * 1  # 每个重复1分钟
      :invalid_data -> issue_analysis.issues_count * 2  # 每个无效2分钟
      _ -> 10
    end

    max(5, min(120, base_time))  # 最少5分钟，最多120分钟
  end

  # 确定修复优先级
  defp determine_repair_priority(issue_analysis) do
    case {issue_analysis.analysis_type, issue_analysis.severity} do
      {:missing_data, :high} -> :critical
      {:invalid_data, :high} -> :critical
      {:inconsistent_data, :high} -> :high
      {:missing_data, :medium} -> :high
      {:inconsistent_data, :medium} -> :medium
      {:duplicate_data, _} -> :low
      _ -> :medium
    end
  end

  # 判断是否需要备份
  defp should_backup_before_repair(config) do
    case config.repair_type do
      :duplicate_data -> true  # 删除操作需要备份
      :invalid_data -> true   # 修改操作需要备份
      _ -> false
    end
  end

  # 创建回滚计划
  defp create_rollback_plan(issue_analysis, config) do
    %{
      rollback_type: config.repair_type,
      backup_location: "backup_#{DateTime.utc_now() |> DateTime.to_unix()}",
      rollback_steps: generate_rollback_steps(issue_analysis, config),
      estimated_rollback_time: estimate_rollback_time(issue_analysis)
    }
  end

  # 生成回滚步骤
  defp generate_rollback_steps(_issue_analysis, config) do
    case config.repair_type do
      :duplicate_data -> ["恢复已删除的重复数据"]
      :invalid_data -> ["恢复原始无效数据"]
      _ -> ["无需回滚操作"]
    end
  end

  # 估算回滚时间
  defp estimate_rollback_time(issue_analysis) do
    estimate_repair_time(issue_analysis) / 2  # 回滚通常比修复快
  end

  # 创建数据备份
  defp create_data_backup(repair_plan, options) do
    Logger.info("💾 [数据统计服务] 创建数据备份")

    try do
      # 这里应该实现实际的备份逻辑
      # 目前返回模拟结果
      backup_info = %{
        backup_id: "backup_#{DateTime.utc_now() |> DateTime.to_unix()}",
        backup_time: DateTime.utc_now(),
        backup_size: "估算大小",
        backup_location: repair_plan.rollback_plan.backup_location
      }

      Logger.info("✅ [数据统计服务] 数据备份创建成功: #{backup_info.backup_id}")
      {:ok, backup_info}
    rescue
      exception ->
        Logger.error("💥 [数据统计服务] 创建数据备份异常: #{inspect(exception)}")
        {:error, :backup_failed}
    end
  end

  # 执行修复操作
  defp execute_repair_actions(repair_actions, options) do
    Logger.info("🔧 [数据统计服务] 执行修复操作: #{length(repair_actions)} 个操作")

    # 按优先级排序
    sorted_actions = Enum.sort_by(repair_actions, fn action ->
      case action.priority do
        :critical -> 0
        :high -> 1
        :medium -> 2
        :low -> 3
        _ -> 4
      end
    end)

    # 执行修复操作
    results = Enum.map(sorted_actions, fn action ->
      execute_single_repair_action(action, options)
    end)

    %{
      total_actions: length(repair_actions),
      results: results,
      success_count: Enum.count(results, &match?({:ok, _}, &1)),
      error_count: Enum.count(results, &match?({:error, _}, &1))
    }
  end

  # 执行单个修复操作
  defp execute_single_repair_action(action, options) do
    Logger.info("🔧 [数据统计服务] 执行修复操作: #{action.action} - #{action.target_type}")

    try do
      case action.action do
        :regenerate_missing_data -> regenerate_missing_data(action, options)
        :fix_data_inconsistency -> fix_data_inconsistency(action, options)
        :remove_duplicate_data -> remove_duplicate_data(action, options)
        :fix_invalid_data -> fix_invalid_data(action, options)
        _ -> {:error, :unsupported_action}
      end
    rescue
      exception ->
        Logger.error("💥 [数据统计服务] 执行修复操作异常: #{inspect(exception)}")
        {:error, :action_execution_failed}
    end
  end

  # 验证修复结果
  defp validate_repair_results(repair_results, repair_plan, options) do
    Logger.info("✅ [数据统计服务] 验证修复结果")

    validation_score = if repair_results.total_actions > 0 do
      repair_results.success_count / repair_results.total_actions * 100
    else
      100
    end

    %{
      validation_score: validation_score,
      total_validated: repair_results.total_actions,
      successful_repairs: repair_results.success_count,
      failed_repairs: repair_results.error_count,
      validation_passed: validation_score >= 80  # 80%以上成功率视为通过
    }
  end

  # 计算修复成功率
  defp calculate_repair_success_rate(repair_results) do
    if repair_results.total_actions > 0 do
      repair_results.success_count / repair_results.total_actions * 100
    else
      100
    end
  end

  # 修复操作的具体实现（占位符）
  defp regenerate_missing_data(_action, _options), do: {:ok, :regenerated}
  defp fix_data_inconsistency(_action, _options), do: {:ok, :fixed}
  defp remove_duplicate_data(_action, _options), do: {:ok, :removed}
  defp fix_invalid_data(_action, _options), do: {:ok, :fixed}

  # 数据问题检查的辅助函数（占位符实现）
  defp check_missing_online_stats(_start_date, _end_date, _options), do: {:online_stats, 0}
  defp check_missing_user_stats(_start_date, _end_date, _options), do: {:user_stats, 0}
  defp check_missing_payment_stats(_start_date, _end_date, _options), do: {:payment_stats, 0}
  defp check_missing_channel_stats(_start_date, _end_date, _options), do: {:channel_stats, 0}
  defp check_missing_retention_stats(_start_date, _end_date, _options), do: {:retention_stats, 0}

  defp check_user_payment_inconsistencies(_start_date, _end_date, _options), do: {:user_payment, 0}
  defp check_channel_conversion_inconsistencies(_start_date, _end_date, _options), do: {:channel_conversion, 0}
  defp check_retention_user_inconsistencies(_start_date, _end_date, _options), do: {:retention_user, 0}

  defp check_duplicate_online_stats(_start_date, _end_date, _options), do: {:online_stats, 0}
  defp check_duplicate_user_stats(_start_date, _end_date, _options), do: {:user_stats, 0}
  defp check_duplicate_payment_stats(_start_date, _end_date, _options), do: {:payment_stats, 0}

  defp check_invalid_online_stats(_start_date, _end_date, _options), do: {:online_stats, 0}
  defp check_invalid_user_stats(_start_date, _end_date, _options), do: {:user_stats, 0}
  defp check_invalid_payment_stats(_start_date, _end_date, _options), do: {:payment_stats, 0}

  # 严重程度判断函数
  defp determine_missing_data_severity(count) when count > 50, do: :high
  defp determine_missing_data_severity(count) when count > 10, do: :medium
  defp determine_missing_data_severity(_), do: :low

  defp determine_inconsistency_severity(count) when count > 20, do: :high
  defp determine_inconsistency_severity(count) when count > 5, do: :medium
  defp determine_inconsistency_severity(_), do: :low

  defp determine_duplicate_severity(count) when count > 100, do: :high
  defp determine_duplicate_severity(count) when count > 20, do: :medium
  defp determine_duplicate_severity(_), do: :low

  defp determine_invalid_data_severity(count) when count > 30, do: :high
  defp determine_invalid_data_severity(count) when count > 10, do: :medium
  defp determine_invalid_data_severity(_), do: :low

  # 提取日期范围
  defp extract_date_range({start_date, end_date}), do: {start_date, end_date}
  defp extract_date_range([start_date, end_date]), do: {start_date, end_date}
  defp extract_date_range(%{start_date: start_date, end_date: end_date}), do: {start_date, end_date}
  defp extract_date_range(date_range) when is_binary(date_range) do
    # 解析字符串格式的日期范围，如 "2024-01-01,2024-01-31"
    case String.split(date_range, ",") do
      [start_str, end_str] ->
        with {:ok, start_date} <- Date.from_iso8601(String.trim(start_str)),
             {:ok, end_date} <- Date.from_iso8601(String.trim(end_str)) do
          {start_date, end_date}
        else
          _ -> {Date.utc_today(), Date.utc_today()}
        end
      _ -> {Date.utc_today(), Date.utc_today()}
    end
  end
  defp extract_date_range(_), do: {Date.utc_today(), Date.utc_today()}
end
