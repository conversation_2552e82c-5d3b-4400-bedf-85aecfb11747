# 数据库表结构分析与分层架构实现计划

## 📊 数据库表结构总览

### 1. 用户账户系统 (Cypridina.Accounts)
```
users                    - 用户基础信息
user_identities         - 用户身份认证
tokens                   - 认证令牌
agent_relationships      - 代理关系
api_keys                 - API密钥
```

### 2. 账本系统 (Cypridina.Ledger)
```
ledger_accounts         - 账户表
ledger_transfers        - 转账记录
ledger_balances         - 余额记录
```

### 3. 赛车游戏系统 (RacingGame)
```
races                   - 比赛记录
racing_game_bets        - 投注记录
racing_game_stocks      - 股票持仓
system_communications   - 系统通信
system_communication_reads - 通信阅读记录
```

### 4. Teen后台系统

#### 4.1 客服管理 (Teen.CustomerService)
```
customer_chats          - 客服聊天
user_questions          - 用户问题
exchange_orders         - 兑换订单
sensitive_words         - 敏感词
user_tags              - 用户标签
verification_codes      - 验证码
```

#### 4.2 游戏管理 (Teen.GameManagement)
```
platforms              - 平台配置
vip_levels            - VIP等级
robot_configs         - 机器人配置
```

#### 4.3 系统设置 (Teen.SystemSettings)
```
admin_users           - 管理员用户
roles                 - 角色管理
permissions           - 权限管理
operation_logs        - 操作日志
ip_whitelists        - IP白名单
```

#### 4.4 支付系统 (Teen.PaymentSystem)
```
payment_gateways      - 支付网关
payment_configs       - 支付配置
exchange_configs      - 兑换配置
```

#### 4.5 活动系统 (Teen.ActivitySystem)
```
sign_in_activities    - 签到活动
cdkey_activities      - 兑换码活动
activity_configs      - 活动配置
```

#### 4.6 数据统计 (Teen.DataStatistics)
```
system_reports        - 系统报表
online_stats          - 在线统计
channel_stats         - 渠道统计
user_stats           - 用户统计
coin_stats           - 金币统计
retention_stats      - 留存率统计
payment_stats        - 支付统计
ltv_stats           - LTV统计
robot_stats         - 机器人统计
```

#### 4.7 推广系统 (Teen.PromotionSystem)
```
promoters            - 推广员
promotion_channels   - 推广渠道
promotion_settlements - 推广结算
share_managements    - 分享管理
share_settlements    - 分享结算
share_configs        - 分成配置
```

## 🏗️ 分层架构实现计划

### 阶段1: 核心系统分层架构
1. **Cypridina.Accounts** - 用户账户系统
2. **Cypridina.Ledger** - 账本系统
3. **RacingGame** - 赛车游戏系统

### 阶段2: Teen后台系统分层架构
1. **Teen.CustomerService** - 客服管理系统
2. **Teen.GameManagement** - 游戏管理系统
3. **Teen.SystemSettings** - 系统设置

### 阶段3: 扩展系统分层架构
1. **Teen.PaymentSystem** - 支付系统
2. **Teen.ActivitySystem** - 活动系统
3. **Teen.DataStatistics** - 数据统计系统
4. **Teen.PromotionSystem** - 推广系统

## 📁 目标目录结构

```
lib/racing_game/live/admin_panel/
├── repositories/                  # 数据访问层
│   ├── accounts/                 # 用户账户仓储
│   │   ├── user_repository.ex
│   │   ├── user_identity_repository.ex
│   │   └── agent_relationship_repository.ex
│   ├── ledger/                   # 账本仓储
│   │   ├── account_repository.ex
│   │   ├── transfer_repository.ex
│   │   └── balance_repository.ex
│   ├── racing_game/              # 赛车游戏仓储
│   │   ├── race_repository.ex
│   │   ├── bet_repository.ex
│   │   └── stock_repository.ex
│   ├── teen/                     # Teen系统仓储
│   │   ├── customer_service/
│   │   ├── game_management/
│   │   ├── system_settings/
│   │   ├── payment_system/
│   │   ├── activity_system/
│   │   ├── data_statistics/
│   │   └── promotion_system/
│   └── query_builders/           # 查询构建器
│       ├── accounts_query_builder.ex
│       ├── ledger_query_builder.ex
│       ├── racing_game_query_builder.ex
│       └── teen/
├── services/                     # 业务逻辑层
│   ├── accounts/                 # 用户账户服务
│   ├── ledger/                   # 账本服务
│   ├── racing_game/              # 赛车游戏服务
│   └── teen/                     # Teen系统服务
├── infrastructure/               # 基础设施层
│   ├── cache/                    # 缓存管理
│   ├── events/                   # 事件处理
│   └── notifications/            # 通知系统
└── utils/                        # 工具层
    ├── database_utils.ex         # 数据库工具
    ├── query_utils.ex           # 查询工具
    └── cache_utils.ex           # 缓存工具
```

## 🎯 实现优先级

### 高优先级 (立即实现)
1. **Cypridina.Accounts** - 用户系统是核心基础
2. **Teen.CustomerService** - 客服系统业务重要
3. **Teen.GameManagement** - 游戏管理核心功能

### 中优先级 (第二阶段)
1. **Cypridina.Ledger** - 账本系统
2. **RacingGame** - 赛车游戏系统
3. **Teen.SystemSettings** - 系统设置

### 低优先级 (第三阶段)
1. **Teen.PaymentSystem** - 支付系统
2. **Teen.ActivitySystem** - 活动系统
3. **Teen.DataStatistics** - 数据统计
4. **Teen.PromotionSystem** - 推广系统

## 📋 实现标准

### Repository层标准
- 统一的错误处理模式
- 缓存策略实现
- 分页查询支持
- 复杂查询构建
- 统计查询功能

### Service层标准
- 业务逻辑封装
- 参数验证
- 事务管理
- 通知集成
- 权限检查

### QueryBuilder层标准
- 动态查询构建
- 过滤条件组合
- 排序和分页
- 搜索功能
- 聚合查询

## 🔧 技术规范

### 命名规范
- Repository: `{Domain}{Entity}Repository`
- Service: `{Domain}{Entity}Service`
- QueryBuilder: `{Domain}QueryBuilder`

### 错误处理规范
- 统一错误返回格式: `{:ok, result}` | `{:error, reason}`
- 详细的错误日志记录
- 用户友好的错误消息

### 缓存策略规范
- TTL缓存机制
- 缓存键命名规范
- 缓存失效策略
- 缓存预热机制

### 日志规范
- 结构化日志记录
- 操作追踪
- 性能监控
- 错误告警

## 📈 预期收益

### 1. 代码质量提升
- 清晰的职责分离
- 统一的代码风格
- 更好的可测试性

### 2. 开发效率提升
- 标准化的开发模式
- 可复用的组件
- 减少重复代码

### 3. 系统性能优化
- 统一的缓存策略
- 优化的查询性能
- 更好的资源利用

### 4. 维护成本降低
- 模块化的架构
- 清晰的依赖关系
- 便于扩展和修改

## 🚀 实现进度

### ✅ 已完成系统

#### 1. Cypridina.Accounts 用户账户系统
- ✅ UserRepository - 用户数据访问仓储
- ✅ UserIdentityRepository - 用户身份认证仓储
- ✅ AgentRelationshipRepository - 代理关系仓储
- ✅ AccountsQueryBuilder - 账户系统查询构建器
- ✅ UserService - 用户业务逻辑服务

#### 2. Cypridina.Ledger 账本系统
- ✅ AccountRepository - 账本账户数据访问仓储
- ✅ TransferRepository - 转账交易数据访问仓储
- ✅ BalanceRepository - 余额数据访问仓储
- ✅ LedgerQueryBuilder - 账本系统查询构建器
- ✅ LedgerService - 账本业务逻辑服务

### 🔄 正在实现

### 📋 待实现系统

#### 3. Teen.CustomerService 客服管理系统
#### 4. Teen.GameManagement 游戏管理系统
#### 5. RacingGame 赛车游戏系统
#### 6. 其他扩展系统

每个系统将包含完整的 Repository、Service、QueryBuilder 层实现。
