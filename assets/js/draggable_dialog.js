// 拖动对话框的 JavaScript Hook
export const DraggableDialog = {
  mounted() {
    this.initDraggable();
  },

  updated() {
    this.initDraggable();
  },

  initDraggable() {
    const dialog = this.el;
    const dragHandle = dialog.querySelector('.drag-handle');
    
    if (!dragHandle) return;

    let isDragging = false;
    let currentX;
    let currentY;
    let initialX;
    let initialY;
    let xOffset = 0;
    let yOffset = 0;

    // 设置初始位置为居中
    this.centerDialog();

    dragHandle.addEventListener('mousedown', (e) => {
      if (e.target.closest('button')) return; // 不拖动按钮
      
      initialX = e.clientX - xOffset;
      initialY = e.clientY - yOffset;

      if (e.target === dragHandle || dragHandle.contains(e.target)) {
        isDragging = true;
        dialog.style.cursor = 'grabbing';
        dragHandle.style.cursor = 'grabbing';
      }
    });

    document.addEventListener('mousemove', (e) => {
      if (isDragging) {
        e.preventDefault();
        currentX = e.clientX - initialX;
        currentY = e.clientY - initialY;

        xOffset = currentX;
        yOffset = currentY;

        // 限制拖动范围，确保对话框不会完全移出视窗
        const rect = dialog.getBoundingClientRect();
        const maxX = window.innerWidth - rect.width;
        const maxY = window.innerHeight - rect.height;
        
        xOffset = Math.max(-rect.width + 100, Math.min(maxX, xOffset));
        yOffset = Math.max(0, Math.min(maxY, yOffset));

        this.setTransform(xOffset, yOffset);
      }
    });

    document.addEventListener('mouseup', () => {
      initialX = currentX;
      initialY = currentY;
      isDragging = false;
      dialog.style.cursor = 'move';
      dragHandle.style.cursor = 'move';
    });
  },

  centerDialog() {
    const dialog = this.el;
    const rect = dialog.getBoundingClientRect();
    const centerX = (window.innerWidth - rect.width) / 2;
    const centerY = (window.innerHeight - rect.height) / 2;
    
    this.setTransform(centerX, centerY);
  },

  setTransform(x, y) {
    this.el.style.transform = `translate(${x}px, ${y}px)`;
  }
};
