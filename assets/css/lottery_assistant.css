/* 赛马游戏样式 */

/* 排名走势图区域样式 */
.ranking-chart-container {
  width: 100%;
  margin: 20px 0;
  padding: 15px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
}

.ranking-chart-container .chart-title {
  margin-bottom: 15px;
  text-align: center;
}

.ranking-chart-container .chart-title h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
  font-weight: bold;
}

.chart-wrapper {
  width: 100%;
  height: 350px;
  position: relative;
}

#ranking-chart {
  width: 100%;
  height: 100%;
}

/* 动物卡片样式 */
.animal-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.animal-card {
  background-color: white;
  border-radius: 8px;
  padding: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.animal-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.animal-card.selected {
  border: 2px solid #3182ce;
}

.animal-avatar {
  width: 100%;
  height: auto;
  border-radius: 4px;
  margin-bottom: 8px;
}

.animal-name {
  font-weight: bold;
  text-align: center;
  margin-bottom: 5px;
}

.animal-bet-amount, .my-bet-amount {
  display: flex;
  justify-content: space-between;
  font-size: 0.9em;
  margin-top: 5px;
}

.bet-label, .my-bet-label {
  color: #666;
}