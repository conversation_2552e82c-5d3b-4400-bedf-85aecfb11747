defmodule RacingGame.Live.AdminPanel.Repositories.Ledger.AccountRepository do
  @moduledoc """
  账本账户数据访问仓储
  
  提供账本账户相关的数据访问抽象：
  - 账户基础管理
  - 账户类型查询
  - 余额查询
  - 账户统计
  """

  require Logger
  alias Cypridina.Ledger.{Account, Transfer, Balance}
  alias RacingGame.Live.AdminPanel.Repositories.QueryBuilders.LedgerQueryBuilder
  require Ash.Query

  # 常量定义
  @cache_ttl 300  # 5分钟缓存
  @default_limit 20
  @max_limit 100

  # ============================================================================
  # 账户基础查询
  # ============================================================================

  @doc """
  根据ID获取账户

  ## 参数
  - `account_id` - 账户ID
  - `options` - 选项

  ## 返回
  - `{:ok, account}` - 成功
  - `{:error, :not_found}` - 账户不存在
  - `{:error, reason}` - 其他错误
  """
  def get_account_by_id(account_id, options \\ []) do
    Logger.debug("🔍 [账户仓储] 根据ID获取账户: #{account_id}")
    
    preload = Keyword.get(options, :preload, [])
    use_cache = Keyword.get(options, :use_cache, true)
    
    cache_key = build_cache_key("account", account_id, preload)
    
    if use_cache do
      case get_from_cache(cache_key) do
        {:ok, account} -> {:ok, account}
        :miss -> fetch_and_cache_account_by_id(account_id, preload, cache_key)
      end
    else
      fetch_account_by_id(account_id, preload)
    end
  end

  @doc """
  根据标识符获取账户

  ## 参数
  - `identifier` - 账户标识符
  - `options` - 选项

  ## 返回
  - `{:ok, account}` - 成功
  - `{:error, :not_found}` - 账户不存在
  - `{:error, reason}` - 其他错误
  """
  def get_account_by_identifier(identifier, options \\ []) do
    Logger.debug("🔍 [账户仓储] 根据标识符获取账户: #{identifier}")
    
    try do
      query = Account
      |> Ash.Query.filter(identifier == ^identifier)
      |> maybe_preload(options[:preload])
      
      case Ash.read_one(query) do
        {:ok, account} when not is_nil(account) -> {:ok, account}
        {:ok, nil} -> {:error, :not_found}
        {:error, error} ->
          Logger.error("❌ [账户仓储] 获取账户失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [账户仓储] 获取账户异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  获取用户账户

  ## 参数
  - `user_id` - 用户ID
  - `currency` - 货币类型
  - `options` - 选项

  ## 返回
  - `{:ok, account}` - 成功
  - `{:error, :not_found}` - 账户不存在
  - `{:error, reason}` - 其他错误
  """
  def get_user_account(user_id, currency \\ :XAA, options \\ []) do
    Logger.debug("👤 [账户仓储] 获取用户账户: #{user_id} - #{currency}")
    
    identifier = "user:#{currency}:#{user_id}"
    get_account_by_identifier(identifier, options)
  end

  @doc """
  分页查询账户列表

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, %{accounts: accounts, page_info: page_info}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_accounts_paginated(params \\ %{}) do
    Logger.debug("📋 [账户仓储] 分页查询账户列表")
    
    try do
      normalized_params = normalize_list_params(params)
      
      with {:ok, query} <- LedgerQueryBuilder.build_account_list_query(normalized_params),
           {:ok, results} <- execute_paginated_query(query, normalized_params) do
        {:ok, %{accounts: results.items, page_info: results.page_info}}
      else
        {:error, reason} -> {:error, reason}
      end
    rescue
      error ->
        Logger.error("❌ [账户仓储] 分页查询异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  搜索账户

  ## 参数
  - `search_term` - 搜索词
  - `options` - 选项

  ## 返回
  - `{:ok, accounts}` - 成功
  - `{:error, reason}` - 失败
  """
  def search_accounts(search_term, options \\ []) do
    Logger.debug("🔍 [账户仓储] 搜索账户: #{search_term}")
    
    limit = Keyword.get(options, :limit, @default_limit)
    fields = Keyword.get(options, :fields, [:identifier, :description])
    
    try do
      query = LedgerQueryBuilder.build_account_search_query(search_term, fields)
      
      case query
           |> Ash.Query.limit(limit)
           |> Ash.read() do
        {:ok, accounts} -> {:ok, accounts}
        {:error, error} ->
          Logger.error("❌ [账户仓储] 搜索失败: #{inspect(error)}")
          {:error, :search_failed}
      end
    rescue
      error ->
        Logger.error("❌ [账户仓储] 搜索异常: #{inspect(error)}")
        {:error, :search_error}
    end
  end

  # ============================================================================
  # 账户创建和管理
  # ============================================================================

  @doc """
  创建账户

  ## 参数
  - `account_data` - 账户数据

  ## 返回
  - `{:ok, account}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_account(account_data) do
    Logger.info("➕ [账户仓储] 创建账户: #{account_data[:identifier]}")
    
    try do
      case Account.open(account_data) do
        {:ok, account} ->
          clear_cache(:account)
          Logger.info("✅ [账户仓储] 账户创建成功: #{account.id}")
          {:ok, account}
        {:error, error} ->
          Logger.error("❌ [账户仓储] 创建账户失败: #{inspect(error)}")
          {:error, :create_failed}
      end
    rescue
      error ->
        Logger.error("❌ [账户仓储] 创建账户异常: #{inspect(error)}")
        {:error, :create_error}
    end
  end

  @doc """
  更新账户

  ## 参数
  - `account_id` - 账户ID
  - `update_data` - 更新数据

  ## 返回
  - `{:ok, account}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_account(account_id, update_data) do
    Logger.info("✏️ [账户仓储] 更新账户: #{account_id}")
    
    with {:ok, account} <- get_account_by_id(account_id, use_cache: false),
         {:ok, updated_account} <- do_update_account(account, update_data) do
      
      clear_cache(:account, account_id)
      Logger.info("✅ [账户仓储] 账户更新成功: #{account_id}")
      {:ok, updated_account}
    else
      error -> error
    end
  end

  @doc """
  删除账户

  ## 参数
  - `account_id` - 账户ID

  ## 返回
  - `:ok` - 成功
  - `{:error, reason}` - 失败
  """
  def delete_account(account_id) do
    Logger.info("🗑️ [账户仓储] 删除账户: #{account_id}")
    
    with {:ok, account} <- get_account_by_id(account_id),
         :ok <- validate_account_deletion(account),
         :ok <- do_delete_account(account) do
      
      clear_cache(:account, account_id)
      Logger.info("✅ [账户仓储] 账户删除成功: #{account_id}")
      :ok
    else
      error -> error
    end
  end

  # ============================================================================
  # 余额查询
  # ============================================================================

  @doc """
  获取账户当前余额

  ## 参数
  - `account_id` - 账户ID
  - `options` - 选项

  ## 返回
  - `{:ok, balance}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_account_balance(account_id, options \\ []) do
    Logger.debug("💰 [账户仓储] 获取账户余额: #{account_id}")
    
    try do
      case get_account_by_id(account_id, preload: [:balance_as_of]) do
        {:ok, account} ->
          balance = account.balance_as_of || Money.new(0, :XAA)
          {:ok, balance}
        error -> error
      end
    rescue
      error ->
        Logger.error("❌ [账户仓储] 获取余额异常: #{inspect(error)}")
        {:error, :balance_error}
    end
  end

  @doc """
  获取用户余额

  ## 参数
  - `user_id` - 用户ID
  - `currency` - 货币类型
  - `options` - 选项

  ## 返回
  - `{:ok, balance}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_balance(user_id, currency \\ :XAA, options \\ []) do
    Logger.debug("👤 [账户仓储] 获取用户余额: #{user_id} - #{currency}")
    
    case get_user_account(user_id, currency, preload: [:balance_as_of]) do
      {:ok, account} ->
        balance = account.balance_as_of || Money.new(0, currency)
        {:ok, balance}
      error -> error
    end
  end

  @doc """
  获取账户余额历史

  ## 参数
  - `account_id` - 账户ID
  - `options` - 选项

  ## 返回
  - `{:ok, balances}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_account_balance_history(account_id, options \\ []) do
    Logger.debug("📊 [账户仓储] 获取账户余额历史: #{account_id}")
    
    try do
      query = Balance
      |> Ash.Query.filter(account_id == ^account_id)
      |> maybe_preload(options[:preload])
      |> apply_sort([inserted_at: :desc])
      |> maybe_limit(options[:limit])
      
      case Ash.read(query) do
        {:ok, balances} -> {:ok, balances}
        {:error, error} ->
          Logger.error("❌ [账户仓储] 获取余额历史失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [账户仓储] 获取余额历史异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # ============================================================================
  # 账户统计
  # ============================================================================

  @doc """
  获取账户统计信息

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, statistics}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_account_statistics(options \\ []) do
    Logger.debug("📊 [账户仓储] 获取账户统计")
    
    try do
      stats = %{
        total_accounts: get_total_accounts(),
        accounts_by_type: get_accounts_by_type(),
        accounts_by_currency: get_accounts_by_currency(),
        active_accounts: get_active_accounts_count(),
        total_balance: get_total_balance(),
        recent_accounts: get_recent_accounts_count(options[:days] || 7)
      }
      
      {:ok, stats}
    rescue
      error ->
        Logger.error("❌ [账户仓储] 获取统计异常: #{inspect(error)}")
        {:error, :statistics_error}
    end
  end

  # ============================================================================
  # 缓存管理
  # ============================================================================

  @doc """
  清除账户相关缓存

  ## 参数
  - `cache_type` - 缓存类型
  - `id` - ID（可选）
  """
  def clear_cache(cache_type, id \\ nil) do
    cache_patterns = case {cache_type, id} do
      {:account, nil} -> ["account:*"]
      {:account, account_id} -> ["account:#{account_id}:*"]
      {:balance, account_id} -> ["balance:#{account_id}:*"]
      {:all, _} -> ["account:*", "balance:*"]
    end
    
    Logger.debug("🧹 [账户仓储] 清除缓存: #{inspect(cache_patterns)}")
    Enum.each(cache_patterns, &clear_cache_pattern/1)
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 标准化列表参数
  defp normalize_list_params(params) do
    %{
      page: max(1, params[:page] || 1),
      limit: min(params[:limit] || @default_limit, @max_limit),
      search: String.trim(params[:search] || ""),
      filters: params[:filters] || %{},
      sort: params[:sort] || [inserted_at: :desc],
      preload: params[:preload] || []
    }
  end

  # 获取并缓存账户
  defp fetch_and_cache_account_by_id(account_id, preload, cache_key) do
    case fetch_account_by_id(account_id, preload) do
      {:ok, account} ->
        cache_put(cache_key, account, @cache_ttl)
        {:ok, account}
      error -> error
    end
  end

  # 获取账户
  defp fetch_account_by_id(account_id, preload) do
    try do
      case Account
           |> maybe_preload(preload)
           |> Ash.get(account_id) do
        {:ok, account} -> {:ok, account}
        {:error, %Ash.Error.Query.NotFound{}} -> {:error, :not_found}
        {:error, error} ->
          Logger.error("❌ [账户仓储] 获取账户失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [账户仓储] 获取账户异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # 执行更新账户
  defp do_update_account(account, update_data) do
    try do
      case account
           |> Ash.Changeset.for_update(:update, update_data)
           |> Ash.update() do
        {:ok, updated_account} -> {:ok, updated_account}
        {:error, error} ->
          Logger.error("❌ [账户仓储] 更新账户失败: #{inspect(error)}")
          {:error, :update_failed}
      end
    rescue
      error ->
        Logger.error("❌ [账户仓储] 更新账户异常: #{inspect(error)}")
        {:error, :update_error}
    end
  end

  # 验证账户删除
  defp validate_account_deletion(account) do
    # 检查账户是否有余额
    case get_account_balance(account.id) do
      {:ok, balance} ->
        if Money.zero?(balance) do
          :ok
        else
          {:error, :account_has_balance}
        end
      {:error, _} -> :ok  # 如果无法获取余额，允许删除
    end
  end

  # 执行删除账户
  defp do_delete_account(account) do
    try do
      case Ash.destroy(account) do
        :ok -> :ok
        {:error, error} ->
          Logger.error("❌ [账户仓储] 删除账户失败: #{inspect(error)}")
          {:error, :delete_failed}
      end
    rescue
      error ->
        Logger.error("❌ [账户仓储] 删除账户异常: #{inspect(error)}")
        {:error, :delete_error}
    end
  end

  # 执行分页查询
  defp execute_paginated_query(query, params) do
    offset = (params.page - 1) * params.limit
    
    case query
         |> maybe_preload(params.preload)
         |> Ash.Query.page(count: true, limit: params.limit, offset: offset)
         |> Ash.read() do
      {:ok, %Ash.Page.Offset{results: items, count: total_count}} ->
        page_info = %{
          total_count: total_count,
          page: params.page,
          limit: params.limit,
          total_pages: ceil(total_count / params.limit)
        }
        {:ok, %{items: items, page_info: page_info}}
      {:error, error} ->
        Logger.error("❌ [账户仓储] 分页查询失败: #{inspect(error)}")
        {:error, :query_failed}
    end
  end

  # 可能预加载关联数据
  defp maybe_preload(query, []), do: query
  defp maybe_preload(query, preload) when is_list(preload) do
    Ash.Query.load(query, preload)
  end

  # 可能限制数量
  defp maybe_limit(query, nil), do: query
  defp maybe_limit(query, limit) when is_integer(limit) and limit > 0 do
    Ash.Query.limit(query, limit)
  end
  defp maybe_limit(query, _), do: query

  # 应用排序
  defp apply_sort(query, sort) when is_list(sort) do
    Ash.Query.sort(query, sort)
  end
  defp apply_sort(query, _), do: query

  # 统计函数（简化实现）
  defp get_total_accounts do
    case Account |> Ash.Query.aggregate(:count, :id) |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_accounts_by_type do
    case Account 
         |> Ash.Query.aggregate(:count, :id, group: [:account_type]) 
         |> Ash.read() do
      {:ok, results} -> 
        Enum.into(results, %{}, fn %{account_type: type, count: count} -> 
          {type, count} 
        end)
      _ -> %{}
    end
  end

  defp get_accounts_by_currency do
    case Account 
         |> Ash.Query.aggregate(:count, :id, group: [:currency]) 
         |> Ash.read() do
      {:ok, results} -> 
        Enum.into(results, %{}, fn %{currency: currency, count: count} -> 
          {currency, count} 
        end)
      _ -> %{}
    end
  end

  defp get_active_accounts_count do
    case Account 
         |> Ash.Query.filter(is_active == true)
         |> Ash.Query.aggregate(:count, :id) 
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_total_balance do
    # 这里需要根据实际的余额聚合逻辑实现
    Money.new(0, :XAA)
  end

  defp get_recent_accounts_count(days) do
    date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
    
    case Account 
         |> Ash.Query.filter(inserted_at >= ^date_threshold)
         |> Ash.Query.aggregate(:count, :id) 
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  # 构建缓存键
  defp build_cache_key(prefix, id, extra \\ nil) do
    case extra do
      nil -> "#{prefix}:#{id}"
      extra -> "#{prefix}:#{id}:#{:erlang.phash2(extra)}"
    end
  end

  # 缓存操作（需要实现具体的缓存后端）
  defp get_from_cache(_key), do: :miss
  defp cache_put(_key, _value, _ttl), do: :ok
  defp clear_cache_pattern(_pattern), do: :ok
end
