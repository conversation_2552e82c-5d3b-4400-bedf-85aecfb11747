# 部署指南

## 📋 概述

本文档提供 Racing Game Admin Panel 系统的完整部署指南，包括开发、测试和生产环境的标准化部署流程。

## 🏗️ 系统架构概览

```
┌─────────────────────────────────────────────────────────────┐
│                    负载均衡器 (Nginx)                        │
├─────────────────────────────────────────────────────────────┤
│  应用服务器集群                                              │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │ App Server 1│  │ App Server 2│  │ App Server 3│          │
│  │ (Phoenix)   │  │ (Phoenix)   │  │ (Phoenix)   │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│  数据层                                                      │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │ PostgreSQL  │  │    Redis    │  │   文件存储   │          │
│  │   主从集群   │  │    缓存     │  │    (S3)     │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 环境要求

### 系统要求
```bash
# 操作系统
Ubuntu 20.04 LTS 或 CentOS 8+

# 硬件要求 (生产环境)
CPU: 4核心以上
内存: 8GB以上
磁盘: 100GB SSD以上
网络: 1Gbps以上
```

### 软件依赖
```bash
# 必需软件版本
Elixir: >= 1.14
Erlang/OTP: >= 25
Node.js: >= 18.0
PostgreSQL: >= 14
Redis: >= 6.0
Nginx: >= 1.20
Docker: >= 20.10 (可选)
```

## 🐳 Docker 容器化部署

### 1. Dockerfile 配置

```dockerfile
# Dockerfile
FROM elixir:1.14-alpine AS builder

# 安装构建依赖
RUN apk add --no-cache build-base npm git python3

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY mix.exs mix.lock ./
COPY config config
COPY priv priv

# 安装依赖
RUN mix local.hex --force && \
    mix local.rebar --force && \
    mix deps.get --only prod && \
    mix deps.compile

# 复制源代码
COPY assets assets
COPY lib lib

# 构建前端资源
RUN cd assets && npm install && npm run deploy
RUN mix phx.digest

# 构建发布版本
RUN mix release

# 运行时镜像
FROM alpine:3.16 AS runner

# 安装运行时依赖
RUN apk add --no-cache openssl ncurses-libs libstdc++

# 创建应用用户
RUN addgroup -g 1000 -S racing_game && \
    adduser -u 1000 -S racing_game -G racing_game

# 设置工作目录
WORKDIR /app
USER racing_game

# 复制发布文件
COPY --from=builder --chown=racing_game:racing_game /app/_build/prod/rel/racing_game ./

# 暴露端口
EXPOSE 4000

# 启动命令
CMD ["./bin/racing_game", "start"]
```

### 2. Docker Compose 配置

```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "4000:4000"
    environment:
      - DATABASE_URL=**************************************/racing_game_prod
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY_BASE=${SECRET_KEY_BASE}
      - PHX_HOST=${PHX_HOST}
    depends_on:
      - db
      - redis
    restart: unless-stopped
    volumes:
      - ./uploads:/app/uploads

  db:
    image: postgres:14-alpine
    environment:
      - POSTGRES_DB=racing_game_prod
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped
    ports:
      - "5432:5432"

  redis:
    image: redis:6-alpine
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

### 3. 环境变量配置

```bash
# .env.prod
# 数据库配置
DATABASE_URL=postgresql://username:password@localhost:5432/racing_game_prod
POOL_SIZE=10

# Redis配置
REDIS_URL=redis://localhost:6379/0

# 应用配置
SECRET_KEY_BASE=your_secret_key_base_here
PHX_HOST=your-domain.com
PORT=4000

# 外部服务配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password

# 文件存储配置
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_S3_BUCKET=your-bucket-name
AWS_REGION=us-east-1

# 监控配置
PROMETHEUS_ENABLED=true
GRAFANA_ENABLED=true
```

## 🚀 生产环境部署

### 1. 服务器准备

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装必需软件
sudo apt install -y curl wget git build-essential

# 安装 Elixir 和 Erlang
wget https://packages.erlang-solutions.com/erlang-solutions_2.0_all.deb
sudo dpkg -i erlang-solutions_2.0_all.deb
sudo apt update
sudo apt install -y esl-erlang elixir

# 安装 Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs

# 安装 PostgreSQL
sudo apt install -y postgresql postgresql-contrib

# 安装 Redis
sudo apt install -y redis-server

# 安装 Nginx
sudo apt install -y nginx
```

### 2. 数据库设置

```bash
# 创建数据库用户
sudo -u postgres createuser --interactive racing_game
sudo -u postgres createdb racing_game_prod -O racing_game

# 设置密码
sudo -u postgres psql
ALTER USER racing_game PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE racing_game_prod TO racing_game;
\q

# 优化 PostgreSQL 配置
sudo nano /etc/postgresql/14/main/postgresql.conf
```

```sql
-- PostgreSQL 优化配置
shared_buffers = 256MB
effective_cache_size = 1GB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200
work_mem = 4MB
min_wal_size = 1GB
max_wal_size = 4GB
```

### 3. 应用部署

```bash
# 创建应用目录
sudo mkdir -p /opt/racing_game
sudo chown $USER:$USER /opt/racing_game
cd /opt/racing_game

# 克隆代码
git clone https://github.com/your-org/racing-game.git .

# 安装依赖
mix local.hex --force
mix local.rebar --force
mix deps.get --only prod

# 编译应用
MIX_ENV=prod mix compile

# 构建前端资源
cd assets
npm install
npm run deploy
cd ..
MIX_ENV=prod mix phx.digest

# 运行数据库迁移
MIX_ENV=prod mix ecto.migrate

# 构建发布版本
MIX_ENV=prod mix release
```

### 4. Systemd 服务配置

```ini
# /etc/systemd/system/racing-game.service
[Unit]
Description=Racing Game Phoenix App
After=network.target postgresql.service redis.service

[Service]
Type=exec
User=racing_game
Group=racing_game
WorkingDirectory=/opt/racing_game
Environment=MIX_ENV=prod
Environment=PORT=4000
EnvironmentFile=/opt/racing_game/.env.prod
ExecStart=/opt/racing_game/_build/prod/rel/racing_game/bin/racing_game start
ExecStop=/opt/racing_game/_build/prod/rel/racing_game/bin/racing_game stop
Restart=on-failure
RestartSec=5
RemainAfterExit=no
KillMode=process
TimeoutSec=30

[Install]
WantedBy=multi-user.target
```

```bash
# 启用和启动服务
sudo systemctl daemon-reload
sudo systemctl enable racing-game
sudo systemctl start racing-game
sudo systemctl status racing-game
```

## 🔒 Nginx 反向代理配置

### 1. SSL 证书配置

```bash
# 安装 Certbot
sudo apt install -y certbot python3-certbot-nginx

# 获取 SSL 证书
sudo certbot --nginx -d your-domain.com
```

### 2. Nginx 配置

```nginx
# /etc/nginx/sites-available/racing-game
upstream racing_game {
    server 127.0.0.1:4000;
    # 如果有多个应用服务器
    # server 127.0.0.1:4001;
    # server 127.0.0.1:4002;
}

# HTTP 重定向到 HTTPS
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

# HTTPS 配置
server {
    listen 443 ssl http2;
    server_name your-domain.com;

    # SSL 配置
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";

    # 文件上传大小限制
    client_max_body_size 50M;

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri @proxy;
    }

    # WebSocket 支持
    location /live {
        proxy_pass http://racing_game;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 86400;
    }

    # 主要代理配置
    location @proxy {
        proxy_pass http://racing_game;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    location / {
        try_files $uri @proxy;
    }
}
```

```bash
# 启用站点配置
sudo ln -s /etc/nginx/sites-available/racing-game /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 🔄 蓝绿部署策略

### 1. 部署脚本

```bash
#!/bin/bash
# deploy.sh

set -e

DEPLOY_DIR="/opt/racing_game"
BACKUP_DIR="/opt/racing_game_backup"
NEW_VERSION=$1

if [ -z "$NEW_VERSION" ]; then
    echo "Usage: $0 <version>"
    exit 1
fi

echo "开始部署版本: $NEW_VERSION"

# 1. 备份当前版本
echo "备份当前版本..."
sudo systemctl stop racing-game
cp -r $DEPLOY_DIR $BACKUP_DIR

# 2. 更新代码
echo "更新代码..."
cd $DEPLOY_DIR
git fetch origin
git checkout $NEW_VERSION

# 3. 安装依赖和构建
echo "构建新版本..."
mix deps.get --only prod
MIX_ENV=prod mix compile
cd assets && npm install && npm run deploy && cd ..
MIX_ENV=prod mix phx.digest

# 4. 运行数据库迁移
echo "运行数据库迁移..."
MIX_ENV=prod mix ecto.migrate

# 5. 构建发布版本
echo "构建发布版本..."
MIX_ENV=prod mix release --overwrite

# 6. 启动服务
echo "启动服务..."
sudo systemctl start racing-game

# 7. 健康检查
echo "进行健康检查..."
sleep 10
if curl -f http://localhost:4000/health; then
    echo "部署成功!"
    rm -rf $BACKUP_DIR
else
    echo "健康检查失败，回滚..."
    sudo systemctl stop racing-game
    rm -rf $DEPLOY_DIR
    mv $BACKUP_DIR $DEPLOY_DIR
    sudo systemctl start racing-game
    exit 1
fi
```

### 2. 零停机部署

```bash
#!/bin/bash
# zero_downtime_deploy.sh

# 使用多个应用实例实现零停机部署
INSTANCES=("4000" "4001" "4002")
DEPLOY_DIR="/opt/racing_game"

for port in "${INSTANCES[@]}"; do
    echo "更新实例 $port..."
    
    # 从负载均衡器移除实例
    # 这里需要根据具体的负载均衡器配置
    
    # 停止实例
    sudo systemctl stop racing-game@$port
    
    # 更新和重启
    # ... 部署逻辑 ...
    
    # 启动实例
    sudo systemctl start racing-game@$port
    
    # 健康检查
    sleep 5
    curl -f http://localhost:$port/health
    
    # 重新加入负载均衡器
    echo "实例 $port 更新完成"
done
```

## 📊 部署验证

### 1. 健康检查端点

```elixir
# lib/racing_game_web/controllers/health_controller.ex
defmodule RacingGameWeb.HealthController do
  use RacingGameWeb, :controller

  def check(conn, _params) do
    # 检查数据库连接
    case Ecto.Adapters.SQL.query(RacingGame.Repo, "SELECT 1") do
      {:ok, _} ->
        # 检查 Redis 连接
        case Redix.command(:redix, ["PING"]) do
          {:ok, "PONG"} ->
            json(conn, %{status: "ok", timestamp: DateTime.utc_now()})
          _ ->
            conn
            |> put_status(503)
            |> json(%{status: "error", message: "Redis connection failed"})
        end
      _ ->
        conn
        |> put_status(503)
        |> json(%{status: "error", message: "Database connection failed"})
    end
  end
end
```

### 2. 部署后验证清单

```bash
# 部署验证脚本
#!/bin/bash
# verify_deployment.sh

echo "开始部署验证..."

# 1. 服务状态检查
echo "检查服务状态..."
sudo systemctl is-active racing-game

# 2. 端口监听检查
echo "检查端口监听..."
netstat -tlnp | grep :4000

# 3. 健康检查
echo "执行健康检查..."
curl -f http://localhost:4000/health

# 4. 数据库连接检查
echo "检查数据库连接..."
MIX_ENV=prod mix run -e "RacingGame.Repo.query!(\"SELECT 1\")"

# 5. 日志检查
echo "检查应用日志..."
sudo journalctl -u racing-game --since "5 minutes ago" --no-pager

# 6. 性能检查
echo "执行性能检查..."
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:4000/

echo "部署验证完成!"
```

## 🔧 故障排查

### 常见问题和解决方案

1. **服务启动失败**
```bash
# 查看服务日志
sudo journalctl -u racing-game -f

# 检查配置文件
sudo -u racing_game /opt/racing_game/_build/prod/rel/racing_game/bin/racing_game eval "Application.get_all_env(:racing_game)"
```

2. **数据库连接问题**
```bash
# 测试数据库连接
psql -h localhost -U racing_game -d racing_game_prod

# 检查连接池状态
MIX_ENV=prod mix run -e "IO.inspect(DBConnection.status(RacingGame.Repo))"
```

3. **内存不足**
```bash
# 监控内存使用
free -h
ps aux --sort=-%mem | head

# 调整 Erlang VM 参数
export ERL_FLAGS="+hms 512 +hmbs 512"
```

---

**文档版本**: v1.0  
**创建日期**: 2024-12-19  
**最后更新**: 2024-12-19  
**维护团队**: Racing Game Development Team
