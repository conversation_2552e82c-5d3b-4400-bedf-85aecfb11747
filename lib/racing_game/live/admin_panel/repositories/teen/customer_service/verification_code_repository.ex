defmodule RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.VerificationCodeRepository do
  @moduledoc """
  验证码数据访问仓储

  提供验证码相关的数据访问抽象：
  - 验证码管理
  - 发送记录
  - 验证统计
  - 安全监控
  """

  require Logger
  alias Teen.CustomerService.VerificationCode
  require Ash.Query

  # 常量定义
  @default_limit 20
  @max_limit 100
  @cache_ttl 300  # 5分钟缓存

  # ============================================================================
  # 验证码管理
  # ============================================================================

  @doc """
  根据ID获取验证码

  ## 参数
  - `code_id` - 验证码ID
  - `options` - 选项

  ## 返回
  - `{:ok, code}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_code_by_id(code_id, options \\ []) do
    Logger.debug("📱 [验证码仓储] 获取验证码: #{code_id}")

    cache_key = build_cache_key("code", code_id, options[:preload])

    case get_from_cache(cache_key) do
      :miss ->
        fetch_and_cache_code_by_id(code_id, options[:preload] || [], cache_key)
      {:hit, code} ->
        Logger.debug("💾 [验证码仓储] 缓存命中: #{code_id}")
        {:ok, code}
    end
  end

  @doc """
  根据手机号获取最新验证码

  ## 参数
  - `phone` - 手机号
  - `code_type` - 验证码类型（可选）
  - `options` - 选项

  ## 返回
  - `{:ok, code}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_latest_code_by_phone(phone, code_type \\ nil, options \\ []) do
    Logger.debug("📱 [验证码仓储] 获取手机最新验证码: #{phone}")

    try do
      query = VerificationCode
      |> Ash.Query.filter(phone == ^phone)
      |> maybe_apply_code_type_filter(code_type)
      |> Ash.Query.sort([inserted_at: :desc])
      |> Ash.Query.limit(1)
      |> maybe_preload(options[:preload] || [])

      case Ash.read_one(query) do
        {:ok, code} when not is_nil(code) -> {:ok, code}
        {:ok, nil} -> {:error, :not_found}
        {:error, error} ->
          Logger.error("❌ [验证码仓储] 获取最新验证码失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [验证码仓储] 获取最新验证码异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  创建验证码

  ## 参数
  - `code_data` - 验证码数据

  ## 返回
  - `{:ok, code}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_code(code_data) do
    Logger.info("📝 [验证码仓储] 创建验证码: #{code_data[:phone]}")

    try do
      case VerificationCode.create(code_data) do
        {:ok, code} ->
          clear_cache(:code, nil)
          Logger.info("✅ [验证码仓储] 验证码创建成功: #{code.id}")
          {:ok, code}
        {:error, error} ->
          Logger.error("❌ [验证码仓储] 创建验证码失败: #{inspect(error)}")
          {:error, :create_failed}
      end
    rescue
      error ->
        Logger.error("❌ [验证码仓储] 创建验证码异常: #{inspect(error)}")
        {:error, :create_error}
    end
  end

  @doc """
  更新验证码

  ## 参数
  - `code_id` - 验证码ID
  - `update_data` - 更新数据

  ## 返回
  - `{:ok, code}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_code(code_id, update_data) do
    Logger.info("📝 [验证码仓储] 更新验证码: #{code_id}")

    with {:ok, code} <- get_code_by_id(code_id),
         {:ok, updated_code} <- do_update_code(code, update_data) do

      clear_cache(:code, code_id)
      Logger.info("✅ [验证码仓储] 验证码更新成功: #{code_id}")
      {:ok, updated_code}
    else
      error -> error
    end
  end

  @doc """
  验证验证码

  ## 参数
  - `phone` - 手机号
  - `code` - 验证码
  - `code_type` - 验证码类型
  - `options` - 选项

  ## 返回
  - `{:ok, verification_result}` - 成功
  - `{:error, reason}` - 失败
  """
  def verify_code(phone, code, code_type, options \\ []) do
    Logger.info("🔍 [验证码仓储] 验证验证码: #{phone} - #{code_type}")

    with {:ok, latest_code} <- get_latest_code_by_phone(phone, code_type),
         {:ok, verification_result} <- do_verify_code(latest_code, code, options) do

      # 更新验证状态
      if verification_result.valid do
        update_code(latest_code.id, %{
          is_used: true,
          used_at: DateTime.utc_now(),
          verify_count: (latest_code.verify_count || 0) + 1
        })
      else
        update_code(latest_code.id, %{
          verify_count: (latest_code.verify_count || 0) + 1
        })
      end

      {:ok, verification_result}
    else
      error -> error
    end
  end

  @doc """
  标记验证码为已使用

  ## 参数
  - `code_id` - 验证码ID

  ## 返回
  - `{:ok, code}` - 成功
  - `{:error, reason}` - 失败
  """
  def mark_code_as_used(code_id) do
    Logger.info("✅ [验证码仓储] 标记验证码已使用: #{code_id}")

    update_data = %{
      is_used: true,
      used_at: DateTime.utc_now()
    }

    update_code(code_id, update_data)
  end

  # ============================================================================
  # 验证码查询
  # ============================================================================

  @doc """
  分页获取验证码列表

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, %{items: codes, page_info: page_info}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_codes_paginated(params \\ %{}) do
    Logger.debug("📋 [验证码仓储] 分页获取验证码列表")

    normalized_params = normalize_list_params(params)
    cache_key = build_cache_key("codes_list", :paginated, normalized_params)

    case get_from_cache(cache_key) do
      :miss ->
        fetch_and_cache_codes_list(normalized_params, cache_key)
      {:hit, result} ->
        Logger.debug("💾 [验证码仓储] 缓存命中: 验证码列表")
        {:ok, result}
    end
  end

  @doc """
  获取手机号的验证码历史

  ## 参数
  - `phone` - 手机号
  - `options` - 选项

  ## 返回
  - `{:ok, codes}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_phone_code_history(phone, options \\ []) do
    Logger.debug("📱 [验证码仓储] 获取手机验证码历史: #{phone}")

    try do
      query = VerificationCode
      |> Ash.Query.filter(phone == ^phone)
      |> maybe_apply_code_type_filter(options[:code_type])
      |> maybe_apply_date_range(options[:date_range])
      |> apply_sort(options[:sort] || [inserted_at: :desc])
      |> maybe_limit(options[:limit])

      case Ash.read(query) do
        {:ok, codes} -> {:ok, codes}
        {:error, error} ->
          Logger.error("❌ [验证码仓储] 获取验证码历史失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [验证码仓储] 获取验证码历史异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  获取未使用的验证码

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, codes}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_unused_codes(options \\ []) do
    Logger.debug("⏳ [验证码仓储] 获取未使用验证码")

    cache_key = build_cache_key("unused_codes", :list, options)

    case get_from_cache(cache_key) do
      :miss ->
        fetch_and_cache_unused_codes(options, cache_key)
      {:hit, codes} ->
        Logger.debug("💾 [验证码仓储] 缓存命中: 未使用验证码")
        {:ok, codes}
    end
  end

  @doc """
  获取过期的验证码

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, codes}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_expired_codes(options \\ []) do
    Logger.debug("⏰ [验证码仓储] 获取过期验证码")

    try do
      current_time = DateTime.utc_now()

      query = VerificationCode
      |> Ash.Query.filter(expires_at < ^current_time and is_used == false)
      |> maybe_apply_code_type_filter(options[:code_type])
      |> apply_sort(options[:sort] || [expires_at: :asc])
      |> maybe_limit(options[:limit])

      case Ash.read(query) do
        {:ok, codes} -> {:ok, codes}
        {:error, error} ->
          Logger.error("❌ [验证码仓储] 获取过期验证码失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [验证码仓储] 获取过期验证码异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  检查手机号发送频率限制

  ## 参数
  - `phone` - 手机号
  - `code_type` - 验证码类型
  - `time_window` - 时间窗口（秒）
  - `max_count` - 最大次数

  ## 返回
  - `{:ok, %{allowed: boolean, remaining_count: integer, next_allowed_at: datetime}}` - 成功
  - `{:error, reason}` - 失败
  """
  def check_send_rate_limit(phone, code_type, time_window \\ 3600, max_count \\ 10) do
    Logger.debug("🚦 [验证码仓储] 检查发送频率限制: #{phone}")

    try do
      time_threshold = DateTime.utc_now() |> DateTime.add(-time_window, :second)

      query = VerificationCode
      |> Ash.Query.filter(phone == ^phone and code_type == ^code_type and inserted_at >= ^time_threshold)
      |> Ash.Query.aggregate(:count, :id)

      case Ash.read(query) do
        {:ok, [%{count: count}]} ->
          allowed = count < max_count
          remaining_count = max(0, max_count - count)

          # 计算下次允许发送时间
          next_allowed_at = if allowed do
            DateTime.utc_now()
          else
            # 获取最早的验证码时间，加上时间窗口
            case get_earliest_code_in_window(phone, code_type, time_threshold) do
              {:ok, earliest_code} ->
                DateTime.add(earliest_code.inserted_at, time_window, :second)
              _ ->
                DateTime.utc_now()
            end
          end

          result = %{
            allowed: allowed,
            remaining_count: remaining_count,
            next_allowed_at: next_allowed_at,
            current_count: count,
            max_count: max_count,
            time_window: time_window
          }

          {:ok, result}
        {:error, error} ->
          Logger.error("❌ [验证码仓储] 检查频率限制失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [验证码仓储] 检查频率限制异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # ============================================================================
  # 统计查询
  # ============================================================================

  @doc """
  获取验证码统计

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, statistics}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_code_statistics(options \\ []) do
    Logger.debug("📊 [验证码仓储] 获取验证码统计")

    try do
      stats = %{
        total_codes: get_total_codes(),
        used_codes: get_used_codes_count(),
        unused_codes: get_unused_codes_count(),
        expired_codes: get_expired_codes_count(),
        recent_codes: get_recent_codes_count(options[:days] || 7),
        codes_by_type: get_codes_by_type_count(),
        success_rate: get_verification_success_rate()
      }

      {:ok, stats}
    rescue
      error ->
        Logger.error("❌ [验证码仓储] 获取统计异常: #{inspect(error)}")
        {:error, :statistics_error}
    end
  end

  # ============================================================================
  # 缓存管理
  # ============================================================================

  @doc """
  清除验证码相关缓存

  ## 参数
  - `cache_type` - 缓存类型
  - `id` - ID（可选）
  """
  def clear_cache(cache_type, id \\ nil) do
    cache_patterns = case {cache_type, id} do
      {:code, nil} -> ["code:*"]
      {:code, code_id} -> ["code:#{code_id}:*"]
      {:phone_codes, phone} -> ["phone_codes:#{phone}:*"]
      {:unused_codes, _} -> ["unused_codes:*"]
      {:all, _} -> ["code:*", "phone_codes:*", "unused_codes:*", "codes_list:*"]
    end

    Logger.debug("🧹 [验证码仓储] 清除缓存: #{inspect(cache_patterns)}")
    Enum.each(cache_patterns, &clear_cache_pattern/1)
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 标准化列表参数
  defp normalize_list_params(params) do
    %{
      page: max(1, params[:page] || 1),
      limit: min(params[:limit] || @default_limit, @max_limit),
      search: String.trim(params[:search] || ""),
      filters: params[:filters] || %{},
      sort: params[:sort] || [inserted_at: :desc],
      preload: params[:preload] || []
    }
  end

  # 获取并缓存验证码
  defp fetch_and_cache_code_by_id(code_id, preload, cache_key) do
    case fetch_code_by_id(code_id, preload) do
      {:ok, code} ->
        cache_put(cache_key, code, @cache_ttl)
        {:ok, code}
      error -> error
    end
  end

  # 获取验证码
  defp fetch_code_by_id(code_id, preload) do
    try do
      case VerificationCode
           |> maybe_preload(preload)
           |> Ash.get(code_id) do
        {:ok, code} -> {:ok, code}
        {:error, %Ash.Error.Query.NotFound{}} -> {:error, :not_found}
        {:error, error} ->
          Logger.error("❌ [验证码仓储] 获取验证码失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [验证码仓储] 获取验证码异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # 执行更新验证码
  defp do_update_code(code, update_data) do
    try do
      case code
           |> Ash.Changeset.for_update(:update, update_data)
           |> Ash.update() do
        {:ok, updated_code} -> {:ok, updated_code}
        {:error, error} ->
          Logger.error("❌ [验证码仓储] 更新验证码失败: #{inspect(error)}")
          {:error, :update_failed}
      end
    rescue
      error ->
        Logger.error("❌ [验证码仓储] 更新验证码异常: #{inspect(error)}")
        {:error, :update_error}
    end
  end

  # 执行验证码验证
  defp do_verify_code(verification_code, input_code, options) do
    current_time = DateTime.utc_now()

    # 检查验证码是否已使用
    if verification_code.is_used do
      Logger.warn("⚠️ [验证码仓储] 验证码已使用: #{verification_code.id}")
      return {:ok, %{valid: false, reason: :already_used, code: verification_code}}
    end

    # 检查验证码是否过期
    if DateTime.compare(current_time, verification_code.expires_at) == :gt do
      Logger.warn("⚠️ [验证码仓储] 验证码已过期: #{verification_code.id}")
      return {:ok, %{valid: false, reason: :expired, code: verification_code}}
    end

    # 检查验证次数限制
    max_attempts = options[:max_attempts] || 5
    if (verification_code.verify_count || 0) >= max_attempts do
      Logger.warn("⚠️ [验证码仓储] 验证次数超限: #{verification_code.id}")
      return {:ok, %{valid: false, reason: :max_attempts_exceeded, code: verification_code}}
    end

    # 验证验证码内容
    if verification_code.code == input_code do
      Logger.info("✅ [验证码仓储] 验证码验证成功: #{verification_code.id}")
      {:ok, %{valid: true, reason: :success, code: verification_code}}
    else
      Logger.warn("⚠️ [验证码仓储] 验证码不匹配: #{verification_code.id}")
      {:ok, %{valid: false, reason: :code_mismatch, code: verification_code}}
    end
  end

  # 获取并缓存验证码列表
  defp fetch_and_cache_codes_list(params, cache_key) do
    case fetch_codes_list(params) do
      {:ok, result} ->
        cache_put(cache_key, result, @cache_ttl)
        {:ok, result}
      error -> error
    end
  end

  # 获取验证码列表
  defp fetch_codes_list(params) do
    try do
      query = VerificationCode
      |> apply_code_filters(params.filters)
      |> apply_code_search(params.search)
      |> apply_sort(params.sort)

      execute_paginated_query(query, params)
    rescue
      error ->
        Logger.error("❌ [验证码仓储] 获取验证码列表异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # 获取并缓存未使用验证码
  defp fetch_and_cache_unused_codes(options, cache_key) do
    case fetch_unused_codes(options) do
      {:ok, codes} ->
        cache_put(cache_key, codes, @cache_ttl)
        {:ok, codes}
      error -> error
    end
  end

  # 获取未使用验证码
  defp fetch_unused_codes(options) do
    try do
      query = VerificationCode
      |> Ash.Query.filter(is_used == false)
      |> maybe_apply_code_type_filter(options[:code_type])
      |> maybe_apply_date_range(options[:date_range])
      |> apply_sort(options[:sort] || [inserted_at: :desc])
      |> maybe_limit(options[:limit])

      case Ash.read(query) do
        {:ok, codes} -> {:ok, codes}
        {:error, error} ->
          Logger.error("❌ [验证码仓储] 获取未使用验证码失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [验证码仓储] 获取未使用验证码异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # 获取时间窗口内最早的验证码
  defp get_earliest_code_in_window(phone, code_type, time_threshold) do
    try do
      query = VerificationCode
      |> Ash.Query.filter(phone == ^phone and code_type == ^code_type and inserted_at >= ^time_threshold)
      |> Ash.Query.sort([inserted_at: :asc])
      |> Ash.Query.limit(1)

      case Ash.read_one(query) do
        {:ok, code} when not is_nil(code) -> {:ok, code}
        {:ok, nil} -> {:error, :not_found}
        {:error, error} ->
          Logger.error("❌ [验证码仓储] 获取最早验证码失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [验证码仓储] 获取最早验证码异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # 应用验证码过滤器
  defp apply_code_filters(query, filters) when map_size(filters) == 0, do: query
  defp apply_code_filters(query, filters) do
    Enum.reduce(filters, query, fn {key, value}, acc_query ->
      apply_code_filter(acc_query, key, value)
    end)
  end

  # 应用单个验证码过滤器
  defp apply_code_filter(query, :phone, phone) when not is_nil(phone) do
    Ash.Query.filter(query, phone == ^phone)
  end
  defp apply_code_filter(query, :code_type, code_type) when not is_nil(code_type) do
    Ash.Query.filter(query, code_type == ^code_type)
  end
  defp apply_code_filter(query, :is_used, is_used) when not is_nil(is_used) do
    Ash.Query.filter(query, is_used == ^is_used)
  end
  defp apply_code_filter(query, :expired, true) do
    current_time = DateTime.utc_now()
    Ash.Query.filter(query, expires_at < ^current_time)
  end
  defp apply_code_filter(query, :expired, false) do
    current_time = DateTime.utc_now()
    Ash.Query.filter(query, expires_at >= ^current_time)
  end
  defp apply_code_filter(query, _key, _value), do: query

  # 应用验证码搜索
  defp apply_code_search(query, search) when search == "" or is_nil(search), do: query
  defp apply_code_search(query, search) do
    search_term = "%#{search}%"
    Ash.Query.filter(query, ilike(phone, ^search_term))
  end

  # 执行分页查询
  defp execute_paginated_query(query, params) do
    offset = (params.page - 1) * params.limit

    case query
         |> maybe_preload(params.preload)
         |> Ash.Query.page(count: true, limit: params.limit, offset: offset)
         |> Ash.read() do
      {:ok, %Ash.Page.Offset{results: items, count: total_count}} ->
        page_info = %{
          total_count: total_count,
          page: params.page,
          limit: params.limit,
          total_pages: ceil(total_count / params.limit)
        }
        {:ok, %{items: items, page_info: page_info}}
      {:error, error} ->
        Logger.error("❌ [验证码仓储] 分页查询失败: #{inspect(error)}")
        {:error, :query_failed}
    end
  end

  # 统计函数
  defp get_total_codes do
    case VerificationCode |> Ash.Query.aggregate(:count, :id) |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_used_codes_count do
    case VerificationCode
         |> Ash.Query.filter(is_used == true)
         |> Ash.Query.aggregate(:count, :id)
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_unused_codes_count do
    case VerificationCode
         |> Ash.Query.filter(is_used == false)
         |> Ash.Query.aggregate(:count, :id)
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_expired_codes_count do
    current_time = DateTime.utc_now()
    case VerificationCode
         |> Ash.Query.filter(expires_at < ^current_time and is_used == false)
         |> Ash.Query.aggregate(:count, :id)
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_recent_codes_count(days) do
    date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)

    case VerificationCode
         |> Ash.Query.filter(inserted_at >= ^date_threshold)
         |> Ash.Query.aggregate(:count, :id)
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_codes_by_type_count do
    # 这里需要根据实际需求实现类型分组统计
    %{}
  end

  defp get_verification_success_rate do
    total = get_total_codes()
    used = get_used_codes_count()

    if total > 0 do
      Float.round(used / total * 100, 2)
    else
      0.0
    end
  end

  # 可能预加载关联数据
  defp maybe_preload(query, []), do: query
  defp maybe_preload(query, preload) when is_list(preload) do
    Ash.Query.load(query, preload)
  end

  # 可能限制数量
  defp maybe_limit(query, nil), do: query
  defp maybe_limit(query, limit) when is_integer(limit) and limit > 0 do
    Ash.Query.limit(query, limit)
  end
  defp maybe_limit(query, _), do: query

  # 应用排序
  defp apply_sort(query, sort) when is_list(sort) do
    Ash.Query.sort(query, sort)
  end
  defp apply_sort(query, _), do: query

  # 可能应用验证码类型过滤
  defp maybe_apply_code_type_filter(query, nil), do: query
  defp maybe_apply_code_type_filter(query, code_type) do
    Ash.Query.filter(query, code_type == ^code_type)
  end

  # 可能应用日期范围过滤
  defp maybe_apply_date_range(query, nil), do: query
  defp maybe_apply_date_range(query, {start_date, end_date}) do
    Ash.Query.filter(query, inserted_at >= ^start_date and inserted_at <= ^end_date)
  end
  defp maybe_apply_date_range(query, _), do: query

  # 构建缓存键
  defp build_cache_key(prefix, id, extra \\ nil) do
    case extra do
      nil -> "#{prefix}:#{id}"
      extra -> "#{prefix}:#{id}:#{:erlang.phash2(extra)}"
    end
  end

  # 缓存操作（需要实现具体的缓存后端）
  defp get_from_cache(_key), do: :miss
  defp cache_put(_key, _value, _ttl), do: :ok
  defp clear_cache_pattern(_pattern), do: :ok
end
