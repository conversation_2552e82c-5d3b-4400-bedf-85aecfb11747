# 故障排查指南

## 📋 概述

本文档提供 Racing Game Admin Panel 系统的完整故障排查指南，帮助运维人员和开发人员快速定位和解决系统问题。

## 🚨 紧急故障处理流程

### 故障响应优先级
```
P0 - 紧急 (系统完全不可用)     ⏰ 15分钟内响应
P1 - 高优先级 (核心功能异常)   ⏰ 30分钟内响应  
P2 - 中优先级 (部分功能异常)   ⏰ 2小时内响应
P3 - 低优先级 (性能问题)       ⏰ 24小时内响应
```

### 紧急故障处理步骤
1. **确认故障** - 验证故障现象和影响范围
2. **通知相关人员** - 根据故障级别通知相应人员
3. **快速恢复** - 优先恢复服务，后续分析根因
4. **记录过程** - 详细记录故障处理过程
5. **根因分析** - 故障恢复后进行深入分析
6. **预防措施** - 制定预防类似故障的措施

## 🔍 系统监控检查清单

### 基础健康检查
```bash
#!/bin/bash
# system_health_check.sh

echo "=== Racing Game 系统健康检查 ==="

# 1. 应用服务状态
echo "1. 检查应用服务状态..."
sudo systemctl status racing-game
curl -f http://localhost:4000/health || echo "❌ 应用健康检查失败"

# 2. 数据库连接
echo "2. 检查数据库连接..."
PGPASSWORD=$DB_PASSWORD psql -h localhost -U postgres -d racing_game_prod -c "SELECT 1;" > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ 数据库连接正常"
else
    echo "❌ 数据库连接失败"
fi

# 3. Redis 连接
echo "3. 检查 Redis 连接..."
redis-cli ping > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ Redis 连接正常"
else
    echo "❌ Redis 连接失败"
fi

# 4. 磁盘空间
echo "4. 检查磁盘空间..."
df -h | grep -E "(/$|/opt|/var)" | awk '{if($5+0 > 80) print "❌ 磁盘空间不足: " $0; else print "✅ 磁盘空间正常: " $0}'

# 5. 内存使用
echo "5. 检查内存使用..."
free -h | awk 'NR==2{printf "内存使用: %s/%s (%.2f%%)\n", $3,$2,$3*100/$2}'

# 6. 负载平均值
echo "6. 检查系统负载..."
uptime | awk '{print "系统负载: " $(NF-2) " " $(NF-1) " " $NF}'

echo "=== 健康检查完成 ==="
```

## 🚫 常见故障场景

### 1. 应用无法启动

#### 症状表现
- 服务启动失败
- 端口无法监听
- 日志显示启动错误

#### 排查步骤
```bash
# 1. 检查服务状态
sudo systemctl status racing-game

# 2. 查看详细日志
sudo journalctl -u racing-game -f --since "10 minutes ago"

# 3. 检查端口占用
netstat -tlnp | grep :4000

# 4. 检查配置文件
sudo -u racing_game /opt/racing_game/_build/prod/rel/racing_game/bin/racing_game eval "Application.get_all_env(:racing_game)"

# 5. 手动启动测试
cd /opt/racing_game
sudo -u racing_game MIX_ENV=prod mix phx.server
```

#### 常见原因和解决方案
```
原因1: 端口被占用
解决: lsof -ti:4000 | xargs kill -9

原因2: 数据库连接失败  
解决: 检查DATABASE_URL配置和数据库服务状态

原因3: 权限问题
解决: chown -R racing_game:racing_game /opt/racing_game

原因4: 环境变量缺失
解决: 检查.env.prod文件和systemd环境配置
```

### 2. 数据库连接问题

#### 症状表现
- 应用报数据库连接超时
- 查询执行缓慢
- 连接池耗尽

#### 排查步骤
```bash
# 1. 检查数据库服务状态
sudo systemctl status postgresql

# 2. 检查数据库连接
PGPASSWORD=$DB_PASSWORD psql -h localhost -U postgres -d racing_game_prod

# 3. 查看数据库日志
sudo tail -f /var/log/postgresql/postgresql-14-main.log

# 4. 检查连接数
PGPASSWORD=$DB_PASSWORD psql -h localhost -U postgres -d racing_game_prod -c "
SELECT count(*) as active_connections, 
       max_conn, 
       max_conn - count(*) as available_connections
FROM pg_stat_activity, 
     (SELECT setting::int as max_conn FROM pg_settings WHERE name='max_connections') mc
GROUP BY max_conn;"

# 5. 检查慢查询
PGPASSWORD=$DB_PASSWORD psql -h localhost -U postgres -d racing_game_prod -c "
SELECT query, calls, total_time, mean_time 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;"
```

#### 解决方案
```sql
-- 1. 终止长时间运行的查询
SELECT pg_terminate_backend(pid) 
FROM pg_stat_activity 
WHERE state = 'active' 
  AND query_start < now() - interval '5 minutes'
  AND query NOT LIKE '%pg_stat_activity%';

-- 2. 重置连接池统计
SELECT pg_stat_reset();

-- 3. 检查锁等待
SELECT blocked_locks.pid AS blocked_pid,
       blocked_activity.usename AS blocked_user,
       blocking_locks.pid AS blocking_pid,
       blocking_activity.usename AS blocking_user,
       blocked_activity.query AS blocked_statement,
       blocking_activity.query AS current_statement_in_blocking_process
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
JOIN pg_catalog.pg_locks blocking_locks ON blocking_locks.locktype = blocked_locks.locktype
JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
WHERE NOT blocked_locks.granted;
```

### 3. 内存不足问题

#### 症状表现
- 应用响应缓慢
- OOM (Out of Memory) 错误
- 系统负载过高

#### 排查步骤
```bash
# 1. 检查内存使用情况
free -h
ps aux --sort=-%mem | head -20

# 2. 检查 Erlang VM 内存
curl -s http://localhost:4000/metrics | grep vm_memory

# 3. 检查系统日志中的 OOM 记录
dmesg | grep -i "killed process"
journalctl --since "1 hour ago" | grep -i "out of memory"

# 4. 分析内存使用趋势
sar -r 1 10  # 需要安装 sysstat

# 5. 检查交换空间
swapon --show
```

#### 解决方案
```bash
# 1. 临时释放内存
echo 3 > /proc/sys/vm/drop_caches

# 2. 重启应用服务
sudo systemctl restart racing-game

# 3. 调整 Erlang VM 参数
export ERL_FLAGS="+hms 1024 +hmbs 1024 +A 64"

# 4. 增加交换空间 (临时措施)
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

### 4. 性能问题

#### 症状表现
- 响应时间过长
- 吞吐量下降
- CPU 使用率过高

#### 排查步骤
```bash
# 1. 检查应用性能指标
curl -s http://localhost:4000/metrics | grep -E "(phoenix_endpoint|racing_game_repo)"

# 2. 分析 CPU 使用
top -p $(pgrep -f racing_game)
htop  # 更友好的界面

# 3. 检查网络连接
netstat -an | grep :4000 | wc -l  # 当前连接数
ss -tuln | grep :4000

# 4. 分析慢查询
# 参考数据库连接问题部分的慢查询检查

# 5. 检查文件描述符使用
lsof -p $(pgrep -f racing_game) | wc -l
ulimit -n  # 检查限制
```

#### 性能优化建议
```elixir
# 1. 调整连接池配置
config :racing_game, RacingGame.Repo,
  pool_size: 20,
  queue_target: 5000,
  queue_interval: 5000

# 2. 启用查询缓存
config :racing_game, RacingGame.Repo,
  prepare: :named,
  parameters: [
    plan_cache_mode: "force_custom_plan"
  ]

# 3. 优化 Phoenix 配置
config :racing_game, RacingGameWeb.Endpoint,
  http: [
    port: 4000,
    transport_options: [
      socket_opts: [:inet6, :inet],
      num_acceptors: 100
    ]
  ]
```

## 📊 监控告警响应

### Prometheus 告警处理

#### 高响应时间告警
```bash
# 1. 检查当前响应时间
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:4000/

# 2. 分析慢请求
grep "duration=" /var/log/racing_game/info.log | tail -20

# 3. 检查数据库性能
# 参考数据库连接问题部分
```

#### 错误率告警
```bash
# 1. 查看错误日志
tail -f /var/log/racing_game/error.log

# 2. 分析错误类型
grep -E "(error|exception)" /var/log/racing_game/info.log | tail -20

# 3. 检查应用健康状态
curl -f http://localhost:4000/health
```

#### 内存使用告警
```bash
# 1. 立即检查内存状态
free -h
ps aux --sort=-%mem | head -10

# 2. 分析内存增长趋势
# 查看监控图表确定是否为内存泄漏

# 3. 必要时重启服务
sudo systemctl restart racing-game
```

## 🔧 日志分析技巧

### 日志文件位置
```
应用日志: /var/log/racing_game/
系统日志: /var/log/syslog
数据库日志: /var/log/postgresql/
Nginx日志: /var/log/nginx/
```

### 常用日志分析命令
```bash
# 1. 实时查看错误日志
tail -f /var/log/racing_game/error.log

# 2. 统计错误类型
grep -E "(error|exception)" /var/log/racing_game/info.log | awk '{print $4}' | sort | uniq -c | sort -nr

# 3. 分析响应时间分布
grep "duration=" /var/log/racing_game/info.log | awk -F'duration=' '{print $2}' | awk '{print $1}' | sort -n | tail -20

# 4. 查找特定用户的操作
grep "user_id=123" /var/log/racing_game/info.log

# 5. 分析访问模式
awk '{print $1}' /var/log/nginx/access.log | sort | uniq -c | sort -nr | head -20
```

### 结构化日志查询
```bash
# 使用 jq 分析 JSON 格式日志
cat /var/log/racing_game/info.log | jq 'select(.level == "error")'
cat /var/log/racing_game/info.log | jq 'select(.duration > 1000)'
```

## 🚀 故障恢复检查清单

### 服务恢复后验证
- [ ] 应用服务状态正常
- [ ] 健康检查端点响应正常
- [ ] 数据库连接正常
- [ ] Redis 连接正常
- [ ] 关键业务功能测试通过
- [ ] 监控指标恢复正常
- [ ] 告警状态清除
- [ ] 用户反馈确认

### 故障记录模板
```markdown
## 故障报告

**故障时间**: YYYY-MM-DD HH:MM:SS
**故障级别**: P0/P1/P2/P3
**影响范围**: 描述受影响的功能和用户
**故障现象**: 详细描述故障表现
**根本原因**: 故障的根本原因分析
**解决方案**: 采取的解决措施
**恢复时间**: YYYY-MM-DD HH:MM:SS
**预防措施**: 防止类似故障的措施
**经验教训**: 从故障中学到的经验
```

## 📞 紧急联系信息

### 故障升级流程
```
L1 - 运维工程师 (24/7)
L2 - 高级运维工程师 (工作时间)
L3 - 系统架构师 (紧急情况)
L4 - 技术总监 (重大故障)
```

### 外部服务联系方式
- **云服务商**: 技术支持热线
- **数据库厂商**: 企业支持
- **监控服务**: 技术支持
- **CDN服务**: 客户服务

---

**文档版本**: v1.0  
**创建日期**: 2024-12-19  
**最后更新**: 2024-12-19  
**维护团队**: Racing Game Development Team
