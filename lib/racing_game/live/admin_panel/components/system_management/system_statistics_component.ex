defmodule RacingGame.Live.AdminPanel.SystemStatisticsComponent do
  @moduledoc """
  系统统计管理组件 - 仅限管理员使用
  """
  use CypridinaWeb, :live_component

  alias CypridinaWeb.AuthHelper
  alias Cypridina.Accounts.User
  require Ash.Query
  require Logger

  def update(assigns, socket) do
    socket = assign(socket, assigns)

    if AuthHelper.has_permission?(socket.assigns.current_user, :admin) do
      socket =
        socket
        |> assign_defaults()
        |> load_statistics()

      {:ok, socket}
    else
      # 非管理员用户显示无权限信息
      socket =
        socket
        |> assign_defaults()
        |> assign(:unauthorized, true)
      {:ok, socket}
    end
  end

  defp assign_defaults(socket) do
    socket
    |> assign(:statistics, %{total_users: 0, total_bets: 0, total_games: 0, revenue: 0})
    |> assign(:date_range, %{start_date: nil, end_date: nil})
    |> assign(:loading, false)
    |> assign(:chart_data, %{user_growth: [], bet_trends: []})
    |> assign(:unauthorized, false)
  end

  defp load_statistics(socket) do
    # 使用模拟数据（实际项目中应该从数据库获取）
    statistics = %{
      total_users: 1250,
      total_bets: 8430,
      total_games: 156,
      revenue: 125600
    }

    # 模拟用户增长数据
    user_growth = [
      %{date: "2024-01-10", count: 15},
      %{date: "2024-01-11", count: 23},
      %{date: "2024-01-12", count: 18},
      %{date: "2024-01-13", count: 31},
      %{date: "2024-01-14", count: 27},
      %{date: "2024-01-15", count: 35}
    ]

    # 模拟投注趋势数据
    bet_trends = [
      %{date: "2024-01-10", amount: 15600},
      %{date: "2024-01-11", amount: 23400},
      %{date: "2024-01-12", amount: 18200},
      %{date: "2024-01-13", amount: 31800},
      %{date: "2024-01-14", amount: 27300},
      %{date: "2024-01-15", amount: 35900}
    ]

    socket
    |> assign(:statistics, statistics)
    |> assign(:chart_data, %{
      user_growth: user_growth,
      bet_trends: bet_trends
    })
  end

  def handle_event("refresh_statistics", %{"date_range" => date_range}, socket) do
    # 模拟根据日期范围刷新数据
    socket =
      socket
      |> load_statistics()
      |> put_flash(:info, "统计数据已刷新")

    {:noreply, socket}
  end

  def handle_event("export_statistics", _params, socket) do
    # 模拟生成CSV文件
    csv_data = generate_csv(socket.assigns.statistics, socket.assigns.chart_data)
    filename = "system_statistics_#{DateTime.utc_now() |> DateTime.to_string() |> String.replace(~r/[:\s]/, "-")}.csv"

    # 模拟下载（实际项目中应该设置下载响应）
    socket = put_flash(socket, :info, "统计数据导出成功：#{filename}")
    {:noreply, socket}
  end

  defp generate_csv(statistics, chart_data) do
    # 模拟CSV生成
    "统计项,数值\n总用户数,#{statistics.total_users}\n总投注数,#{statistics.total_bets}\n总游戏场次,#{statistics.total_games}\n总收入,#{statistics.revenue}"
  end

  # 格式化日期为HTML输入框格式
  defp format_date(nil), do: ""
  defp format_date(date), do: Date.to_iso8601(date)

  def render(assigns) do
    ~H"""
    <div class="p-6 bg-white rounded-lg shadow-md">
      <%= if assigns[:unauthorized] do %>
        <h3 class="text-xl font-bold text-red-600 mb-4">权限不足</h3>
        <p class="text-gray-700">您没有访问系统统计的权限。只有管理员可以查看此页面。</p>
      <% else %>
        <div class="flex flex-col md:flex-row md:justify-between md:items-center mb-6 gap-4">
          <h3 class="text-xl font-bold text-gray-800">系统统计管理</h3>
        <div class="flex flex-col sm:flex-row gap-3">
          <div class="flex gap-2">
            <input
              type="date"
              class="p-2 border border-gray-300 rounded-md"
              value={format_date(@date_range.start_date)}
              phx-debounce="blur"
              name="date_range[start_date]"
            />
            <span class="flex items-center">至</span>
            <input
              type="date"
              class="p-2 border border-gray-300 rounded-md"
              value={format_date(@date_range.end_date)}
              phx-debounce="blur"
              name="date_range[end_date]"
            />
            <button
              class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              phx-click="refresh_statistics"
              phx-target={@myself}
            >
              刷新数据
            </button>
          </div>
          <button
            class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            phx-click="export_statistics"
            phx-target={@myself}
          >
            导出数据
          </button>
        </div>
      </div>

      <!-- 关键指标卡片 -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div class="p-4 bg-gray-50 rounded-lg border border-gray-200">
          <h4 class="text-sm text-gray-500 mb-1">总用户数</h4>
          <p class="text-2xl font-bold text-gray-800"><%= @statistics.total_users %></p>
        </div>
        <div class="p-4 bg-gray-50 rounded-lg border border-gray-200">
          <h4 class="text-sm text-gray-500 mb-1">总投注数</h4>
          <p class="text-2xl font-bold text-gray-800"><%= @statistics.total_bets %></p>
        </div>
        <div class="p-4 bg-gray-50 rounded-lg border border-gray-200">
          <h4 class="text-sm text-gray-500 mb-1">总游戏场次</h4>
          <p class="text-2xl font-bold text-gray-800"><%= @statistics.total_games %></p>
        </div>
        <div class="p-4 bg-gray-50 rounded-lg border border-gray-200">
          <h4 class="text-sm text-gray-500 mb-1">总收入 (元)</h4>
          <p class="text-2xl font-bold text-green-600"><%= @statistics.revenue %></p>
        </div>
      </div>

      <!-- 图表区域 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div class="p-4 bg-gray-50 rounded-lg border border-gray-200">
          <h4 class="text-lg font-semibold text-gray-800 mb-4">用户增长趋势</h4>
          <div class="h-64 flex items-end justify-between space-x-2 p-4 bg-white rounded border">
            <%= for {data_point, index} <- Enum.with_index(@chart_data.user_growth) do %>
              <div class="flex flex-col items-center">
                <div
                  class="bg-blue-500 rounded-t w-8 transition-all duration-300 hover:bg-blue-600"
                  style={"height: #{min(data_point.count * 3, 200)}px"}
                  title={"#{data_point.date}: #{data_point.count} 用户"}
                ></div>
                <span class="text-xs text-gray-600 mt-2 transform -rotate-45 origin-left">
                  <%= String.slice(data_point.date, 5, 5) %>
                </span>
              </div>
            <% end %>
          </div>
          <div class="mt-2 text-sm text-gray-600">
            <span class="inline-flex items-center">
              <div class="w-3 h-3 bg-blue-500 rounded mr-2"></div>
              新增用户数
            </span>
          </div>
        </div>
        <div class="p-4 bg-gray-50 rounded-lg border border-gray-200">
          <h4 class="text-lg font-semibold text-gray-800 mb-4">投注金额趋势</h4>
          <div class="h-64 flex items-end justify-between space-x-2 p-4 bg-white rounded border">
            <%= for {data_point, index} <- Enum.with_index(@chart_data.bet_trends) do %>
              <div class="flex flex-col items-center">
                <div
                  class="bg-green-500 rounded-t w-8 transition-all duration-300 hover:bg-green-600"
                  style={"height: #{min(div(data_point.amount, 100), 200)}px"}
                  title={"#{data_point.date}: ¥#{data_point.amount}"}
                ></div>
                <span class="text-xs text-gray-600 mt-2 transform -rotate-45 origin-left">
                  <%= String.slice(data_point.date, 5, 5) %>
                </span>
              </div>
            <% end %>
          </div>
          <div class="mt-2 text-sm text-gray-600">
            <span class="inline-flex items-center">
              <div class="w-3 h-3 bg-green-500 rounded mr-2"></div>
              投注金额 (元)
            </span>
          </div>
        </div>
        </div>
      <% end %>
    </div>
    """
  end
end