defmodule RacingGame.Live.AdminPanel.QueryBuilders.Teen.SystemSettingsQueryBuilder do
  @moduledoc """
  ⚙️ 系统设置查询构建器

  负责构建系统设置相关的复杂查询，包括：
  - 管理员用户查询
  - 角色权限查询
  - 操作日志查询
  - IP白名单查询
  - 跨仓储统计查询
  - 业务逻辑查询
  - 性能优化查询
  """

  require Logger
  import Ash.Query

  alias RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.{
    AdminUserRepository,
    RoleRepository,
    PermissionRepository,
    OperationLogRepository,
    IpWhitelistRepository
  }

  # 常量定义
  @default_page_size 20
  @max_page_size 100

  # ==================== 管理员用户查询 ====================

  @doc """
  构建管理员用户查询

  ## 参数
  - `query_type` - 查询类型
  - `params` - 查询参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, result}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_admin_user_query(query_type, params \\ %{}, options \\ [])

  def build_admin_user_query(:list, params, options) do
    Logger.info("👤 [系统设置查询] 构建管理员用户列表查询: #{inspect(params)}")
    AdminUserRepository.list_admin_users(params, options)
  end

  def build_admin_user_query(:by_role, params, options) do
    role_id = Map.get(params, "role_id")
    Logger.info("👤 [系统设置查询] 根据角色查询管理员用户: #{role_id}")
    AdminUserRepository.list_admin_users_by_role(role_id, options)
  end

  def build_admin_user_query(:by_status, params, options) do
    status = Map.get(params, "status")
    Logger.info("👤 [系统设置查询] 根据状态查询管理员用户: #{status}")
    AdminUserRepository.list_admin_users_by_status(status, options)
  end

  def build_admin_user_query(:authenticate, params, options) do
    username = Map.get(params, "username")
    password = Map.get(params, "password")
    Logger.info("🔐 [系统设置查询] 管理员用户认证: #{username}")
    AdminUserRepository.authenticate_admin_user(username, password, options)
  end

  def build_admin_user_query(query_type, _params, _options) do
    Logger.error("❌ [系统设置查询] 不支持的管理员用户查询类型: #{query_type}")
    {:error, :unsupported_admin_user_query_type}
  end

  # ==================== 角色查询 ====================

  @doc """
  构建角色查询

  ## 参数
  - `query_type` - 查询类型
  - `params` - 查询参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, result}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_role_query(query_type, params \\ %{}, options \\ [])

  def build_role_query(:list, params, options) do
    Logger.info("🎭 [系统设置查询] 构建角色列表查询: #{inspect(params)}")
    RoleRepository.list_roles(params, options)
  end

  def build_role_query(:by_status, params, options) do
    status = Map.get(params, "status")
    Logger.info("🎭 [系统设置查询] 根据状态查询角色: #{status}")
    RoleRepository.list_roles_by_status(status, options)
  end

  def build_role_query(:by_level, params, options) do
    level = Map.get(params, "level")
    Logger.info("🎭 [系统设置查询] 根据级别查询角色: #{level}")
    RoleRepository.list_roles_by_level(level, options)
  end

  def build_role_query(:by_code, params, options) do
    role_code = Map.get(params, "code")
    Logger.info("🎭 [系统设置查询] 根据代码查询角色: #{role_code}")
    RoleRepository.get_role_by_code(role_code, options)
  end

  def build_role_query(query_type, _params, _options) do
    Logger.error("❌ [系统设置查询] 不支持的角色查询类型: #{query_type}")
    {:error, :unsupported_role_query_type}
  end

  # ==================== 权限查询 ====================

  @doc """
  构建权限查询

  ## 参数
  - `query_type` - 查询类型
  - `params` - 查询参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, result}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_permission_query(query_type, params \\ %{}, options \\ [])

  def build_permission_query(:list, params, options) do
    Logger.info("🔐 [系统设置查询] 构建权限列表查询: #{inspect(params)}")
    PermissionRepository.list_permissions(params, options)
  end

  def build_permission_query(:by_type, params, options) do
    permission_type = Map.get(params, "type")
    Logger.info("🔐 [系统设置查询] 根据类型查询权限: #{permission_type}")
    PermissionRepository.list_permissions_by_type(permission_type, options)
  end

  def build_permission_query(:by_parent, params, options) do
    parent_id = Map.get(params, "parent_id")
    Logger.info("🔐 [系统设置查询] 根据父权限查询子权限: #{parent_id}")
    PermissionRepository.list_permissions_by_parent(parent_id, options)
  end

  def build_permission_query(:by_status, params, options) do
    status = Map.get(params, "status")
    Logger.info("🔐 [系统设置查询] 根据状态查询权限: #{status}")
    PermissionRepository.list_permissions_by_status(status, options)
  end

  def build_permission_query(:tree, params, options) do
    Logger.info("🔐 [系统设置查询] 构建权限树查询")
    PermissionRepository.get_permission_tree(options)
  end

  def build_permission_query(:by_code, params, options) do
    permission_code = Map.get(params, "code")
    Logger.info("🔐 [系统设置查询] 根据代码查询权限: #{permission_code}")
    PermissionRepository.get_permission_by_code(permission_code, options)
  end

  def build_permission_query(query_type, _params, _options) do
    Logger.error("❌ [系统设置查询] 不支持的权限查询类型: #{query_type}")
    {:error, :unsupported_permission_query_type}
  end

  # ==================== 操作日志查询 ====================

  @doc """
  构建操作日志查询

  ## 参数
  - `query_type` - 查询类型
  - `params` - 查询参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, result}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_operation_log_query(query_type, params \\ %{}, options \\ [])

  def build_operation_log_query(:list, params, options) do
    Logger.info("📝 [系统设置查询] 构建操作日志列表查询: #{inspect(params)}")
    OperationLogRepository.list_operation_logs(params, options)
  end

  def build_operation_log_query(:by_admin, params, options) do
    admin_user_id = Map.get(params, "admin_user_id")
    Logger.info("📝 [系统设置查询] 根据管理员查询操作日志: #{admin_user_id}")
    OperationLogRepository.list_operation_logs_by_admin(admin_user_id, options)
  end

  def build_operation_log_query(:by_type, params, options) do
    operation_type = Map.get(params, "operation_type")
    Logger.info("📝 [系统设置查询] 根据操作类型查询日志: #{operation_type}")
    OperationLogRepository.list_operation_logs_by_type(operation_type, options)
  end

  def build_operation_log_query(:by_module, params, options) do
    module = Map.get(params, "module")
    Logger.info("📝 [系统设置查询] 根据模块查询操作日志: #{module}")
    OperationLogRepository.list_operation_logs_by_module(module, options)
  end

  def build_operation_log_query(:by_date_range, params, options) do
    start_date = Map.get(params, "start_date")
    end_date = Map.get(params, "end_date")
    Logger.info("📝 [系统设置查询] 根据日期范围查询操作日志: #{start_date} - #{end_date}")
    OperationLogRepository.list_operation_logs_by_date_range(start_date, end_date, options)
  end

  def build_operation_log_query(:stats, params, options) do
    Logger.info("📝 [系统设置查询] 构建操作日志统计查询")
    OperationLogRepository.get_operation_log_stats(options)
  end

  def build_operation_log_query(query_type, _params, _options) do
    Logger.error("❌ [系统设置查询] 不支持的操作日志查询类型: #{query_type}")
    {:error, :unsupported_operation_log_query_type}
  end

  # ==================== IP白名单查询 ====================

  @doc """
  构建IP白名单查询

  ## 参数
  - `query_type` - 查询类型
  - `params` - 查询参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, result}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_ip_whitelist_query(query_type, params \\ %{}, options \\ [])

  def build_ip_whitelist_query(:list, params, options) do
    Logger.info("🛡️ [系统设置查询] 构建IP白名单列表查询: #{inspect(params)}")
    IpWhitelistRepository.list_ip_whitelists(params, options)
  end

  def build_ip_whitelist_query(:by_status, params, options) do
    status = Map.get(params, "status")
    Logger.info("🛡️ [系统设置查询] 根据状态查询IP白名单: #{status}")
    IpWhitelistRepository.list_ip_whitelists_by_status(status, options)
  end

  def build_ip_whitelist_query(:by_type, params, options) do
    type = Map.get(params, "type")
    Logger.info("🛡️ [系统设置查询] 根据类型查询IP白名单: #{type}")
    IpWhitelistRepository.list_ip_whitelists_by_type(type, options)
  end

  def build_ip_whitelist_query(:check_allowed, params, options) do
    ip_address = Map.get(params, "ip_address")
    Logger.info("🛡️ [系统设置查询] 检查IP是否被允许: #{ip_address}")
    IpWhitelistRepository.check_ip_allowed(ip_address, options)
  end

  def build_ip_whitelist_query(query_type, _params, _options) do
    Logger.error("❌ [系统设置查询] 不支持的IP白名单查询类型: #{query_type}")
    {:error, :unsupported_ip_whitelist_query_type}
  end

  # ==================== 跨仓储统计查询 ====================

  @doc """
  构建系统设置综合统计查询

  ## 参数
  - `params` - 查询参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, comprehensive_stats}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_comprehensive_stats_query(params \\ %{}, options \\ []) do
    Logger.info("📊 [系统设置查询] 构建综合统计查询: #{inspect(params)}")

    try do
      # 并行获取各项统计
      tasks = [
        Task.async(fn -> get_admin_user_stats(options) end),
        Task.async(fn -> get_role_stats(options) end),
        Task.async(fn -> get_permission_stats(options) end),
        Task.async(fn -> get_operation_log_stats(options) end),
        Task.async(fn -> get_ip_whitelist_stats(options) end)
      ]

      # 等待所有任务完成
      results = Task.await_many(tasks, 10_000)

      case results do
        [
          {:ok, admin_user_stats},
          {:ok, role_stats},
          {:ok, permission_stats},
          {:ok, operation_log_stats},
          {:ok, ip_whitelist_stats}
        ] ->
          comprehensive_stats = %{
            admin_users: admin_user_stats,
            roles: role_stats,
            permissions: permission_stats,
            operation_logs: operation_log_stats,
            ip_whitelists: ip_whitelist_stats,
            generated_at: DateTime.utc_now()
          }

          Logger.info("✅ [系统设置查询] 综合统计查询成功")
          {:ok, comprehensive_stats}

        _ ->
          Logger.error("❌ [系统设置查询] 综合统计查询部分失败")
          {:error, :partial_stats_failure}
      end
    rescue
      exception ->
        Logger.error("💥 [系统设置查询] 综合统计查询异常: #{inspect(exception)}")
        {:error, :comprehensive_stats_exception}
    end
  end

  @doc """
  构建用户权限查询

  ## 参数
  - `admin_user_id` - 管理员用户ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, user_permissions}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_user_permissions_query(admin_user_id, options \\ []) do
    Logger.info("🔐 [系统设置查询] 构建用户权限查询: #{admin_user_id}")

    try do
      # 获取用户信息（包含角色）
      with {:ok, admin_user} <- AdminUserRepository.get_admin_user(admin_user_id, [load: [:role]]),
           {:ok, role} when not is_nil(role) <- {:ok, admin_user.role},
           {:ok, permissions} <- PermissionRepository.list_permissions_by_status(1, options) do
        
        # 根据角色权限代码过滤权限
        user_permissions = Enum.filter(permissions, fn permission ->
          permission.code in (role.permission_codes || [])
        end)

        # 构建权限树
        permission_tree = build_user_permission_tree(user_permissions)

        result = %{
          admin_user_id: admin_user_id,
          role: role,
          permissions: user_permissions,
          permission_tree: permission_tree,
          generated_at: DateTime.utc_now()
        }

        Logger.info("✅ [系统设置查询] 用户权限查询成功: #{length(user_permissions)}个权限")
        {:ok, result}
      else
        {:ok, nil} ->
          Logger.warn("⚠️ [系统设置查询] 用户无角色分配: #{admin_user_id}")
          {:error, :user_no_role}
        error ->
          Logger.error("❌ [系统设置查询] 用户权限查询失败: #{inspect(error)}")
          error
      end
    rescue
      exception ->
        Logger.error("💥 [系统设置查询] 用户权限查询异常: #{inspect(exception)}")
        {:error, :user_permissions_exception}
    end
  end

  # ==================== 私有辅助函数 ====================

  # 获取管理员用户统计
  defp get_admin_user_stats(options) do
    with {:ok, {_, total_count}} <- AdminUserRepository.list_admin_users(%{}, options),
         {:ok, active_users} <- AdminUserRepository.list_admin_users_by_status(1, options) do
      {:ok, %{
        total_count: total_count,
        active_count: length(active_users),
        inactive_count: total_count - length(active_users)
      }}
    else
      error -> error
    end
  end

  # 获取角色统计
  defp get_role_stats(options) do
    with {:ok, {_, total_count}} <- RoleRepository.list_roles(%{}, options),
         {:ok, active_roles} <- RoleRepository.list_roles_by_status(1, options) do
      {:ok, %{
        total_count: total_count,
        active_count: length(active_roles),
        inactive_count: total_count - length(active_roles)
      }}
    else
      error -> error
    end
  end

  # 获取权限统计
  defp get_permission_stats(options) do
    with {:ok, {_, total_count}} <- PermissionRepository.list_permissions(%{}, options),
         {:ok, active_permissions} <- PermissionRepository.list_permissions_by_status(1, options) do
      {:ok, %{
        total_count: total_count,
        active_count: length(active_permissions),
        inactive_count: total_count - length(active_permissions)
      }}
    else
      error -> error
    end
  end

  # 获取操作日志统计
  defp get_operation_log_stats(options) do
    OperationLogRepository.get_operation_log_stats(options)
  end

  # 获取IP白名单统计
  defp get_ip_whitelist_stats(options) do
    with {:ok, {_, total_count}} <- IpWhitelistRepository.list_ip_whitelists(%{}, options),
         {:ok, active_ips} <- IpWhitelistRepository.list_ip_whitelists_by_status(1, options) do
      {:ok, %{
        total_count: total_count,
        active_count: length(active_ips),
        inactive_count: total_count - length(active_ips)
      }}
    else
      error -> error
    end
  end

  # 构建用户权限树
  defp build_user_permission_tree(permissions) do
    # 按父子关系分组
    {root_permissions, child_permissions} = Enum.split_with(permissions, fn p -> is_nil(p.parent_id) end)
    
    # 为每个根权限构建子树
    Enum.map(root_permissions, fn root ->
      Map.put(root, :children, build_permission_children(root.id, child_permissions))
    end)
    |> Enum.sort_by(& &1.sort_order)
  end

  # 递归构建子权限
  defp build_permission_children(parent_id, all_permissions) do
    children = Enum.filter(all_permissions, fn p -> p.parent_id == parent_id end)
    
    Enum.map(children, fn child ->
      Map.put(child, :children, build_permission_children(child.id, all_permissions))
    end)
    |> Enum.sort_by(& &1.sort_order)
  end
end
