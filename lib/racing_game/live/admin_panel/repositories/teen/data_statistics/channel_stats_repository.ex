defmodule RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.ChannelStatsRepository do
  @moduledoc """
  📊 渠道统计仓储层

  负责渠道统计的数据访问操作，包括：
  - 渠道统计的CRUD操作
  - 渠道效果数据统计和分析
  - 渠道转化率和ROI分析
  - 渠道数据缓存管理
  - 渠道性能监控
  """

  require Logger
  import Ash.Query

  alias Teen.Statistics.ChannelStats

  # 缓存配置
  @cache_ttl 600_000  # 10分钟缓存
  @cache_prefix "channel_stats_repo"

  # ==================== 基础 CRUD 操作 ====================

  @doc """
  创建渠道统计记录

  ## 参数
  - `stats_data` - 统计数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, channel_stats}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_channel_stats(stats_data, options \\ []) do
    Logger.info("📊 [渠道统计仓储] 创建渠道统计记录: #{inspect(stats_data[:channel_name])}")

    try do
      result = ChannelStats
      |> Ash.Changeset.for_create(:create, stats_data)
      |> Ash.create()

      case result do
        {:ok, channel_stats} ->
          Logger.info("✅ [渠道统计仓储] 渠道统计记录创建成功: #{channel_stats.channel_name}")
          clear_cache()
          {:ok, channel_stats}
        {:error, error} ->
          Logger.error("❌ [渠道统计仓储] 渠道统计记录创建失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [渠道统计仓储] 创建渠道统计记录异常: #{inspect(exception)}")
        {:error, :create_failed}
    end
  end

  @doc """
  获取渠道统计记录

  ## 参数
  - `stats_id` - 统计ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, channel_stats}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_channel_stats(stats_id, options \\ []) do
    Logger.info("📊 [渠道统计仓储] 获取渠道统计记录: #{stats_id}")

    cache_key = "#{@cache_prefix}:get:#{stats_id}"
    
    case get_from_cache(cache_key) do
      {:ok, cached_result} ->
        Logger.info("🎯 [渠道统计仓储] 缓存命中: #{stats_id}")
        {:ok, cached_result}
      
      {:error, :not_found} ->
        try do
          result = ChannelStats |> Ash.get(stats_id)

          case result do
            {:ok, channel_stats} ->
              Logger.info("✅ [渠道统计仓储] 渠道统计记录获取成功: #{channel_stats.id}")
              put_to_cache(cache_key, channel_stats)
              {:ok, channel_stats}
            {:error, error} ->
              Logger.error("❌ [渠道统计仓储] 渠道统计记录获取失败: #{inspect(error)}")
              {:error, error}
          end
        rescue
          exception ->
            Logger.error("💥 [渠道统计仓储] 获取渠道统计记录异常: #{inspect(exception)}")
            {:error, :get_failed}
        end
    end
  end

  @doc """
  更新渠道统计记录

  ## 参数
  - `channel_stats` - 渠道统计实例
  - `update_data` - 更新数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, updated_channel_stats}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_channel_stats(channel_stats, update_data, options \\ []) do
    Logger.info("📊 [渠道统计仓储] 更新渠道统计记录: #{channel_stats.id}")

    try do
      result = channel_stats
      |> Ash.Changeset.for_update(:update, update_data)
      |> Ash.update()

      case result do
        {:ok, updated_channel_stats} ->
          Logger.info("✅ [渠道统计仓储] 渠道统计记录更新成功: #{updated_channel_stats.id}")
          clear_cache()
          {:ok, updated_channel_stats}
        {:error, error} ->
          Logger.error("❌ [渠道统计仓储] 渠道统计记录更新失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [渠道统计仓储] 更新渠道统计记录异常: #{inspect(exception)}")
        {:error, :update_failed}
    end
  end

  @doc """
  删除渠道统计记录

  ## 参数
  - `channel_stats` - 渠道统计实例
  - `options` - 选项参数

  ## 返回
  - `{:ok, deleted_channel_stats}` - 成功
  - `{:error, reason}` - 失败
  """
  def delete_channel_stats(channel_stats, options \\ []) do
    Logger.info("📊 [渠道统计仓储] 删除渠道统计记录: #{channel_stats.id}")

    try do
      result = channel_stats |> Ash.destroy()

      case result do
        :ok ->
          Logger.info("✅ [渠道统计仓储] 渠道统计记录删除成功: #{channel_stats.id}")
          clear_cache()
          {:ok, channel_stats}
        {:error, error} ->
          Logger.error("❌ [渠道统计仓储] 渠道统计记录删除失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [渠道统计仓储] 删除渠道统计记录异常: #{inspect(exception)}")
        {:error, :delete_failed}
    end
  end

  # ==================== 查询操作 ====================

  @doc """
  列出渠道统计记录

  ## 参数
  - `params` - 查询参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, {stats_list, total_count}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_channel_stats(params \\ %{}, options \\ []) do
    Logger.info("📊 [渠道统计仓储] 列出渠道统计记录: #{inspect(params)}")

    try do
      query = ChannelStats
      |> build_list_query(params)
      |> maybe_load_associations(options)

      # 获取总数
      total_count = query |> Ash.count!()

      # 应用分页
      query = query
      |> limit(Map.get(params, :page_size, 20))
      |> offset(Map.get(params, :offset, 0))

      stats_list = query |> Ash.read!()

      Logger.info("✅ [渠道统计仓储] 渠道统计记录列表获取成功，共 #{length(stats_list)} 条")
      {:ok, {stats_list, total_count}}
    rescue
      exception ->
        Logger.error("💥 [渠道统计仓储] 列出渠道统计记录异常: #{inspect(exception)}")
        {:error, :list_failed}
    end
  end

  @doc """
  按渠道名称获取统计

  ## 参数
  - `channel_name` - 渠道名称
  - `options` - 选项参数

  ## 返回
  - `{:ok, stats_list}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_stats_by_channel(channel_name, options \\ []) do
    Logger.info("📊 [渠道统计仓储] 按渠道名称获取统计: #{channel_name}")

    cache_key = "#{@cache_prefix}:by_channel:#{channel_name}"
    
    case get_from_cache(cache_key) do
      {:ok, cached_result} ->
        Logger.info("🎯 [渠道统计仓储] 缓存命中: #{channel_name}")
        {:ok, cached_result}
      
      {:error, :not_found} ->
        try do
          stats_list = ChannelStats
          |> Ash.Query.filter(channel_name == channel_name)
          |> Ash.Query.sort(stat_date: :desc)
          |> maybe_load_associations(options)
          |> Ash.read!()

          Logger.info("✅ [渠道统计仓储] 按渠道名称获取统计成功: #{length(stats_list)} 条")
          put_to_cache(cache_key, stats_list)
          {:ok, stats_list}
        rescue
          exception ->
            Logger.error("💥 [渠道统计仓储] 按渠道名称获取统计异常: #{inspect(exception)}")
            {:error, :get_by_channel_failed}
        end
    end
  end

  @doc """
  按日期范围获取渠道统计

  ## 参数
  - `start_date` - 开始日期
  - `end_date` - 结束日期
  - `options` - 选项参数

  ## 返回
  - `{:ok, stats_list}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_stats_by_date_range(start_date, end_date, options \\ []) do
    Logger.info("📊 [渠道统计仓储] 按日期范围获取渠道统计: #{start_date} - #{end_date}")

    try do
      stats_list = ChannelStats
      |> Ash.Query.filter(stat_date >= start_date and stat_date  <= end_date)
      |> Ash.Query.sort([stat_date: :desc, channel_name: :asc])
      |> maybe_load_associations(options)
      |> Ash.read!()

      Logger.info("✅ [渠道统计仓储] 按日期范围获取渠道统计成功: #{length(stats_list)} 条")
      {:ok, stats_list}
    rescue
      exception ->
        Logger.error("💥 [渠道统计仓储] 按日期范围获取渠道统计异常: #{inspect(exception)}")
        {:error, :get_by_date_range_failed}
    end
  end

  @doc """
  获取顶级渠道

  ## 参数
  - `limit` - 限制数量
  - `order_by` - 排序字段
  - `options` - 选项参数

  ## 返回
  - `{:ok, top_channels}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_top_channels(limit \\ 10, order_by \\ "new_users", options \\ []) do
    Logger.info("📊 [渠道统计仓储] 获取顶级渠道: #{limit} 个，按 #{order_by} 排序")

    try do
      sort_field = String.to_atom(order_by)
      
      top_channels = ChannelStats
      |> Ash.Query.sort([{:desc, sort_field}])
      |> Ash.Query.limit(limit)
      |> maybe_load_associations(options)
      |> Ash.read!()

      Logger.info("✅ [渠道统计仓储] 获取顶级渠道成功: #{length(top_channels)} 条")
      {:ok, top_channels}
    rescue
      exception ->
        Logger.error("💥 [渠道统计仓储] 获取顶级渠道异常: #{inspect(exception)}")
        {:error, :get_top_channels_failed}
    end
  end

  @doc """
  获取渠道性能

  ## 参数
  - `channel_name` - 渠道名称
  - `days` - 天数
  - `options` - 选项参数

  ## 返回
  - `{:ok, performance_data}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_channel_performance(channel_name, days \\ 30, options \\ []) do
    Logger.info("📊 [渠道统计仓储] 获取渠道性能: #{channel_name}，#{days} 天")

    try do
      start_date = Date.utc_today() |> Date.add(-days)
      
      performance_data = ChannelStats
      |> Ash.Query.filter(channel_name == channel_name and stat_date >= start_date)
      |> Ash.Query.sort(stat_date: :desc)
      |> maybe_load_associations(options)
      |> Ash.read!()

      Logger.info("✅ [渠道统计仓储] 获取渠道性能成功: #{length(performance_data)} 条")
      {:ok, performance_data}
    rescue
      exception ->
        Logger.error("💥 [渠道统计仓储] 获取渠道性能异常: #{inspect(exception)}")
        {:error, :get_channel_performance_failed}
    end
  end

  # ==================== 私有辅助函数 ====================

  # 构建列表查询
  defp build_list_query(query, params) do
    query
    |> maybe_filter_by_channel_name(params)
    |> maybe_filter_by_channel_type(params)
    |> maybe_filter_by_date_range(params)
    |> maybe_apply_search(params)
    |> maybe_apply_sort(params)
  end

  # 按渠道名称过滤
  defp maybe_filter_by_channel_name(query, %{channel_name: channel_name}) when channel_name != "" do
    query |> Ash.Query.filter(channel_name == channel_name)
  end
  defp maybe_filter_by_channel_name(query, _), do: query

  # 按渠道类型过滤
  defp maybe_filter_by_channel_type(query, %{channel_type: channel_type}) when channel_type != "" do
    query |> Ash.Query.filter(channel_type == channel_type)
  end
  defp maybe_filter_by_channel_type(query, _), do: query

  # 按日期范围过滤
  defp maybe_filter_by_date_range(query, %{start_date: start_date, end_date: end_date}) 
    when not is_nil(start_date) and not is_nil(end_date) do
    query |> Ash.Query.filter(stat_date  >= start_date and stat_date <= end_date)
  end
  defp maybe_filter_by_date_range(query, _), do: query

  # 应用搜索
  defp maybe_apply_search(query, %{search: search}) when search != "" do
    query |> Ash.Query.filter(contains(channel_name, search))
  end
  defp maybe_apply_search(query, _), do: query

  # 应用排序
  defp maybe_apply_sort(query, %{sort_by: sort_by, sort_order: sort_order}) do
    sort_field = String.to_atom(sort_by)
    sort_direction = String.to_atom(sort_order)
    query |> Ash.Query.sort([{sort_direction, sort_field}])
  end
  defp maybe_apply_sort(query, _) do
    query |> Ash.Query.sort([stat_date: :desc, channel_name: :asc])
  end

  # 加载关联数据
  defp maybe_load_associations(query, options) do
    case Keyword.get(options, :load_associations, false) do
      true -> query |> Ash.Query.load([])
      _ -> query
    end
  end

  # 缓存操作
  defp get_from_cache(_key), do: {:error, :not_found}
  defp put_to_cache(_key, _value), do: :ok
  defp clear_cache(), do: :ok
end
