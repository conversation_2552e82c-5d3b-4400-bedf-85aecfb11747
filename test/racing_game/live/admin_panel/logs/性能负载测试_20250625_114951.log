Compiling 65 files (.ex)
    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 33 │   def create_exchange_config(config_data, options \\ []) do
    │                                           ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/exchange_config_repository.ex:33:43: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.ExchangeConfigRepository.create_exchange_config/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 33 │   def create_payment_gateway(gateway_data, options \\ []) do
    │                                            ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/payment_gateway_repository.ex:33:44: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.PaymentGatewayRepository.create_payment_gateway/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 33 │   def create_payment_config(config_data, options \\ []) do
    │                                          ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/payment_config_repository.ex:33:42: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.PaymentConfigRepository.create_payment_config/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_user_stats(stats_data, options \\ []) do
    │                                     ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/user_stats_repository.ex:35:37: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.UserStatsRepository.create_user_stats/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_payment_stats(stats_data, options \\ []) do
    │                                        ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/payment_stats_repository.ex:35:40: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.PaymentStatsRepository.create_payment_stats/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 70 │   def get_user_stats(stats_id, options \\ []) do
    │                                ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/user_stats_repository.ex:70:32: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.UserStatsRepository.get_user_stats/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 70 │   def get_payment_stats(stats_id, options \\ []) do
    │                                   ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/payment_stats_repository.ex:70:35: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.PaymentStatsRepository.get_payment_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 113 │   def update_user_stats(user_stats, update_data, options \\ []) do
     │                                                  ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/user_stats_repository.ex:113:50: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.UserStatsRepository.update_user_stats/3

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_operation_log(log_data, options \\ []) do
    │                                      ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/operation_log_repository.ex:35:38: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.OperationLogRepository.create_operation_log/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 113 │   def update_payment_stats(payment_stats, update_data, options \\ []) do
     │                                                        ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/payment_stats_repository.ex:113:56: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.PaymentStatsRepository.update_payment_stats/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 148 │   def delete_user_stats(user_stats, options \\ []) do
     │                                     ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/user_stats_repository.ex:148:37: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.UserStatsRepository.delete_user_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 148 │   def delete_payment_stats(payment_stats, options \\ []) do
     │                                           ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/payment_stats_repository.ex:148:43: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.PaymentStatsRepository.delete_payment_stats/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_retention_stats(stats_data, options \\ []) do
    │                                          ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/retention_stats_repository.ex:35:42: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RetentionStatsRepository.create_retention_stats/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 70 │   def get_retention_stats(stats_id, options \\ []) do
    │                                     ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/retention_stats_repository.ex:70:37: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RetentionStatsRepository.get_retention_stats/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_system_report(report_data, options \\ []) do
    │                                         ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/system_report_repository.ex:35:41: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.SystemReportRepository.create_system_report/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 113 │   def update_retention_stats(retention_stats, update_data, options \\ []) do
     │                                                            ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/retention_stats_repository.ex:113:60: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RetentionStatsRepository.update_retention_stats/3

    warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
    │
 85 │           Logger.warn("⚠️ [兑换配置仓储] 兑换配置不存在: #{config_id}")
    │                  ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/exchange_config_repository.ex:85:18: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.ExchangeConfigRepository.get_exchange_config/2

    warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
    │
 85 │           Logger.warn("⚠️ [支付网关仓储] 支付网关不存在: #{gateway_id}")
    │                  ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/payment_gateway_repository.ex:85:18: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.PaymentGatewayRepository.get_payment_gateway/2

    warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
    │
 85 │           Logger.warn("⚠️ [支付配置仓储] 支付配置不存在: #{config_id}")
    │                  ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/payment_config_repository.ex:85:18: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.PaymentConfigRepository.get_payment_config/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 148 │   def delete_retention_stats(retention_stats, options \\ []) do
     │                                               ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/retention_stats_repository.ex:148:47: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RetentionStatsRepository.delete_retention_stats/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 70 │   def get_system_report(report_id, options \\ []) do
    │                                    ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/system_report_repository.ex:70:36: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.SystemReportRepository.get_system_report/2

     warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
     │
 125 │           Logger.warn("⚠️ [兑换配置仓储] 兑换配置不存在: #{config_name}")
     │                  ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/exchange_config_repository.ex:125:18: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.ExchangeConfigRepository.get_exchange_config_by_name/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 111 │   def update_payment_gateway(gateway_id, update_data, options \\ []) do
     │                                                       ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/payment_gateway_repository.ex:111:55: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.PaymentGatewayRepository.update_payment_gateway/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 111 │   def update_payment_config(config_id, update_data, options \\ []) do
     │                                                     ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/payment_config_repository.ex:111:53: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.PaymentConfigRepository.update_payment_config/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 114 │   def update_system_report(system_report, update_data, options \\ []) do
     │                                                        ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/system_report_repository.ex:114:56: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.SystemReportRepository.update_system_report/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 148 │   def delete_payment_gateway(gateway_id, options \\ []) do
     │                                          ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/payment_gateway_repository.ex:148:42: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.PaymentGatewayRepository.delete_payment_gateway/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 149 │   def delete_system_report(system_report, options \\ []) do
     │                                           ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/system_report_repository.ex:149:43: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.SystemReportRepository.delete_system_report/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 148 │   def delete_payment_config(config_id, options \\ []) do
     │                                        ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/payment_config_repository.ex:148:40: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.PaymentConfigRepository.delete_payment_config/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 151 │   def update_exchange_config(config_id, update_data, options \\ []) do
     │                                                      ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/exchange_config_repository.ex:151:54: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.ExchangeConfigRepository.update_exchange_config/3

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_robot_stats(stats_data, options \\ []) do
    │                                      ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/robot_stats_repository.ex:35:38: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RobotStatsRepository.create_robot_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 188 │   def delete_exchange_config(config_id, options \\ []) do
     │                                         ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/exchange_config_repository.ex:188:41: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.ExchangeConfigRepository.delete_exchange_config/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 70 │   def get_robot_stats(stats_id, options \\ []) do
    │                                 ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/robot_stats_repository.ex:70:33: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RobotStatsRepository.get_robot_stats/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_online_stats(stats_data, options \\ []) do
    │                                       ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/online_stats_repository.ex:35:39: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.OnlineStatsRepository.create_online_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 113 │   def update_robot_stats(robot_stats, update_data, options \\ []) do
     │                                                    ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/robot_stats_repository.ex:113:52: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RobotStatsRepository.update_robot_stats/3

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 70 │   def get_online_stats(stats_id, options \\ []) do
    │                                  ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/online_stats_repository.ex:70:34: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.OnlineStatsRepository.get_online_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 309 │   def activate_payment_gateway(gateway_id, options \\ []) do
     │                                            ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/payment_gateway_repository.ex:309:44: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.PaymentGatewayRepository.activate_payment_gateway/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 148 │   def delete_robot_stats(robot_stats, options \\ []) do
     │                                       ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/robot_stats_repository.ex:148:39: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RobotStatsRepository.delete_robot_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 113 │   def update_online_stats(online_stats, update_data, options \\ []) do
     │                                                      ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/online_stats_repository.ex:113:54: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.OnlineStatsRepository.update_online_stats/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 346 │   def deactivate_payment_gateway(gateway_id, options \\ []) do
     │                                              ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/payment_gateway_repository.ex:346:46: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.PaymentGatewayRepository.deactivate_payment_gateway/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 345 │   def activate_payment_config(config_id, options \\ []) do
     │                                          ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/payment_config_repository.ex:345:42: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.PaymentConfigRepository.activate_payment_config/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_ltv_stats(stats_data, options \\ []) do
    │                                    ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/ltv_stats_repository.ex:35:36: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.LtvStatsRepository.create_ltv_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 148 │   def delete_online_stats(online_stats, options \\ []) do
     │                                         ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/online_stats_repository.ex:148:41: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.OnlineStatsRepository.delete_online_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 382 │   def deactivate_payment_config(config_id, options \\ []) do
     │                                            ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/payment_config_repository.ex:382:44: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.PaymentConfigRepository.deactivate_payment_config/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 70 │   def get_ltv_stats(stats_id, options \\ []) do
    │                               ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/ltv_stats_repository.ex:70:31: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.LtvStatsRepository.get_ltv_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 113 │   def update_ltv_stats(ltv_stats, update_data, options \\ []) do
     │                                                ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/ltv_stats_repository.ex:113:48: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.LtvStatsRepository.update_ltv_stats/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 387 │   def activate_exchange_config(config_id, options \\ []) do
     │                                           ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/exchange_config_repository.ex:387:43: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.ExchangeConfigRepository.activate_exchange_config/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 148 │   def delete_ltv_stats(ltv_stats, options \\ []) do
     │                                   ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/ltv_stats_repository.ex:148:35: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.LtvStatsRepository.delete_ltv_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 424 │   def deactivate_exchange_config(config_id, options \\ []) do
     │                                             ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/exchange_config_repository.ex:424:45: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.ExchangeConfigRepository.deactivate_exchange_config/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_channel_stats(stats_data, options \\ []) do
    │                                        ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/channel_stats_repository.ex:35:40: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.ChannelStatsRepository.create_channel_stats/2

    warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
    │
 81 │           Logger.warn("⚠️ [操作日志仓储] 操作日志不存在: #{log_id}")
    │                  ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/operation_log_repository.ex:81:18: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.OperationLogRepository.get_operation_log/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 70 │   def get_channel_stats(stats_id, options \\ []) do
    │                                   ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/channel_stats_repository.ex:70:35: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.ChannelStatsRepository.get_channel_stats/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 69 │   def get_operation_log(log_id, options \\ []) do
    │                                 ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/operation_log_repository.ex:69:33: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.OperationLogRepository.get_operation_log/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_coin_stats(stats_data, options \\ []) do
    │                                     ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/coin_stats_repository.ex:35:37: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.CoinStatsRepository.create_coin_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 113 │   def update_channel_stats(channel_stats, update_data, options \\ []) do
     │                                                        ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/channel_stats_repository.ex:113:56: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.ChannelStatsRepository.update_channel_stats/3

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 70 │   def get_coin_stats(stats_id, options \\ []) do
    │                                ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/coin_stats_repository.ex:70:32: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.CoinStatsRepository.get_coin_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 148 │   def delete_channel_stats(channel_stats, options \\ []) do
     │                                           ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/channel_stats_repository.ex:148:43: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.ChannelStatsRepository.delete_channel_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 113 │   def update_coin_stats(coin_stats, update_data, options \\ []) do
     │                                                  ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/coin_stats_repository.ex:113:50: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.CoinStatsRepository.update_coin_stats/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 148 │   def delete_coin_stats(coin_stats, options \\ []) do
     │                                     ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/coin_stats_repository.ex:148:37: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.CoinStatsRepository.delete_coin_stats/2

     error: undefined variable "vip_level_required"
     │
 465 │         Ash.Query.filter(query, vip_level_required  <= vip_level)
     │                                 ^^^^^^^^^^^^^^^^^^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/exchange_config_repository.ex:465:33: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.ExchangeConfigRepository.apply_filters/2

     error: undefined variable "vip_level_required"
     │
 354 │       |> Ash.Query.filter(status == 1 and (is_nil(vip_level_required) or vip_level_required  <= vip_level))
     │                                                                          ^^^^^^^^^^^^^^^^^^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/exchange_config_repository.ex:354:74: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.ExchangeConfigRepository.list_exchange_configs_by_vip_level/2

     error: undefined variable "vip_level_required"
     │
 354 │       |> Ash.Query.filter(status == 1 and (is_nil(vip_level_required) or vip_level_required  <= vip_level))
     │                                                   ^^^^^^^^^^^^^^^^^^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/exchange_config_repository.ex:354:51: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.ExchangeConfigRepository.list_exchange_configs_by_vip_level/2

     error: undefined variable "status"
     │
 354 │       |> Ash.Query.filter(status == 1 and (is_nil(vip_level_required) or vip_level_required  <= vip_level))
     │                           ^^^^^^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/exchange_config_repository.ex:354:27: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.ExchangeConfigRepository.list_exchange_configs_by_vip_level/2

     error: undefined variable "status"
     │
 317 │       |> Ash.Query.filter(exchange_type == ^exchange_type and status == 1)
     │                                                               ^^^^^^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/exchange_config_repository.ex:317:63: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.ExchangeConfigRepository.list_exchange_configs_by_type/2

     error: misplaced operator ^exchange_type

     The pin operator ^ is supported only inside matches or inside custom macros. Make sure you are inside a match or all necessary macros have been required
     │
 317 │       |> Ash.Query.filter(exchange_type == ^exchange_type and status == 1)
     │                                            ^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/exchange_config_repository.ex:317:44: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.ExchangeConfigRepository.list_exchange_configs_by_type/2

    error: misplaced operator ^config_id

    The pin operator ^ is supported only inside matches or inside custom macros. Make sure you are inside a match or all necessary macros have been required
    │
 75 │       |> Ash.Query.filter(id == ^config_id)
    │                                 ^
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/payment_config_repository.ex:75:33: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.PaymentConfigRepository.get_payment_config/2

    error: misplaced operator ^gateway_id

    The pin operator ^ is supported only inside matches or inside custom macros. Make sure you are inside a match or all necessary macros have been required
    │
 75 │       |> Ash.Query.filter(id == ^gateway_id)
    │                                 ^
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/payment_gateway_repository.ex:75:33: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.PaymentGatewayRepository.get_payment_gateway/2

    error: misplaced operator ^config_id

    The pin operator ^ is supported only inside matches or inside custom macros. Make sure you are inside a match or all necessary macros have been required
    │
 75 │       |> Ash.Query.filter(id == ^config_id)
    │                                 ^
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/exchange_config_repository.ex:75:33: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.ExchangeConfigRepository.get_exchange_config/2

    error: undefined variable "id"
    │
 75 │       |> Ash.Query.filter(id == ^config_id)
    │                           ^^
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/payment_config_repository.ex:75:27: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.PaymentConfigRepository.get_payment_config/2

    error: undefined variable "id"
    │
 75 │       |> Ash.Query.filter(id == ^gateway_id)
    │                           ^^
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/payment_gateway_repository.ex:75:27: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.PaymentGatewayRepository.get_payment_gateway/2

    error: undefined variable "id"
    │
 75 │       |> Ash.Query.filter(id == ^config_id)
    │                           ^^
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/exchange_config_repository.ex:75:27: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.ExchangeConfigRepository.get_exchange_config/2


== Compilation error in file lib/racing_game/live/admin_panel/repositories/teen/payment_system/exchange_config_repository.ex ==
** (CompileError) lib/racing_game/live/admin_panel/repositories/teen/payment_system/exchange_config_repository.ex: cannot compile module RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.ExchangeConfigRepository (errors have been logged)

