defmodule RacingGame.Live.AdminPanel.Repositories.RepositoryIntegrationTest do
  @moduledoc """
  🧪 Repository层集成测试

  测试所有Repository类的CRUD操作、缓存机制、错误处理等功能
  """

  use ExUnit.Case, async: true
  
  alias RacingGame.Live.AdminPanel.TestHelper
  
  # Customer Service Repositories
  alias RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.{
    TicketRepository,
    FeedbackRepository,
    AnnouncementRepository
  }
  
  # Game Management Repositories  
  alias RacingGame.Live.AdminPanel.Repositories.Teen.GameManagement.{
    PlatformRepository,
    VipLevelRepository,
    RobotConfigRepository
  }
  
  # System Settings Repositories
  alias RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.{
    SystemConfigRepository,
    PermissionRepository,
    RoleRepository
  }
  
  # Payment System Repositories
  alias RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.{
    PaymentOrderRepository,
    PaymentChannelRepository,
    WithdrawalRepository
  }
  
  # Activity System Repositories
  alias RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.{
    ActivityRepository,
    ParticipationRepository,
    RewardRepository
  }
  
  # Data Statistics Repositories
  alias RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.{
    OnlineStatsRepository,
    UserStatsRepository,
    CoinStatsRepository
  }
  
  # Promotion System Repositories
  alias RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.{
    PromoterRepository,
    PromotionChannelRepository,
    PromotionSettlementRepository
  }

  setup do
    TestHelper.setup_test_environment()
    :ok
  end

  describe "Customer Service Repositories" do
    test "TicketRepository CRUD operations" do
      # 创建测试工单
      ticket_data = TestHelper.generate_test_ticket()
      
      # 测试创建
      {:ok, created_ticket} = TicketRepository.create_ticket(ticket_data)
      assert created_ticket.title == ticket_data.title
      assert created_ticket.status == ticket_data.status
      
      # 测试获取
      {:ok, fetched_ticket} = TicketRepository.get_ticket(created_ticket.id)
      assert fetched_ticket.id == created_ticket.id
      
      # 测试更新
      update_data = %{status: 2, priority: 1}
      {:ok, updated_ticket} = TicketRepository.update_ticket(created_ticket.id, update_data)
      assert updated_ticket.status == 2
      assert updated_ticket.priority == 1
      
      # 测试列表查询
      {:ok, tickets} = TicketRepository.list_tickets()
      assert length(tickets) >= 1
      
      # 测试删除
      {:ok, _} = TicketRepository.delete_ticket(created_ticket.id)
      {:error, :not_found} = TicketRepository.get_ticket(created_ticket.id)
    end

    test "TicketRepository caching mechanism" do
      ticket_data = TestHelper.generate_test_ticket()
      {:ok, created_ticket} = TicketRepository.create_ticket(ticket_data)
      
      # 第一次查询 - 从数据库
      {result1, time1} = TestHelper.measure_time(fn ->
        TicketRepository.get_ticket(created_ticket.id)
      end)
      
      # 第二次查询 - 从缓存
      {result2, time2} = TestHelper.measure_time(fn ->
        TicketRepository.get_ticket(created_ticket.id)
      end)
      
      assert {:ok, _} = result1
      assert {:ok, _} = result2
      # 缓存查询应该更快
      assert time2 < time1
    end

    test "FeedbackRepository operations" do
      feedback_data = %{
        user_id: :rand.uniform(10000),
        feedback_type: "suggestion",
        content: "测试反馈内容",
        rating: 5,
        status: 0
      }
      
      {:ok, created_feedback} = FeedbackRepository.create_feedback(feedback_data)
      assert created_feedback.content == feedback_data.content
      assert created_feedback.rating == feedback_data.rating
      
      # 测试按类型查询
      {:ok, feedbacks} = FeedbackRepository.list_feedbacks_by_type("suggestion")
      assert length(feedbacks) >= 1
    end
  end

  describe "Game Management Repositories" do
    test "PlatformRepository operations" do
      platform_data = TestHelper.generate_test_platform()
      
      # 测试创建
      {:ok, created_platform} = PlatformRepository.create_platform(platform_data)
      assert created_platform.platform_name == platform_data.platform_name
      
      # 测试按编号查询
      {:ok, platform_by_number} = PlatformRepository.get_platform_by_number(created_platform.platform_number)
      assert platform_by_number.id == created_platform.id
      
      # 测试状态切换
      {:ok, updated_platform} = PlatformRepository.toggle_platform_status(created_platform.id)
      assert updated_platform.status != created_platform.status
    end

    test "VipLevelRepository VIP计算" do
      # 创建VIP等级配置
      vip_levels = Enum.map(1..5, fn level ->
        vip_data = TestHelper.generate_test_vip_level(%{level: level})
        {:ok, vip_level} = VipLevelRepository.create_vip_level(vip_data)
        vip_level
      end)
      
      # 测试VIP等级计算
      recharge_amount = Decimal.new("2500")
      {:ok, calculated_level} = VipLevelRepository.calculate_vip_level_by_recharge(recharge_amount)
      
      assert calculated_level.level >= 1
      assert Decimal.compare(calculated_level.min_recharge_amount, recharge_amount) != :gt
    end

    test "RobotConfigRepository智能选择" do
      # 创建不同难度的机器人配置
      robot_configs = Enum.map(1..4, fn difficulty ->
        robot_data = TestHelper.generate_test_robot_config(%{difficulty_level: difficulty})
        {:ok, robot_config} = RobotConfigRepository.create_robot_config(robot_data)
        robot_config
      end)
      
      # 测试智能机器人选择
      user_skill_level = 25  # 应该匹配难度2
      {:ok, selected_robot} = RobotConfigRepository.select_robot_for_user(1, user_skill_level, "racing")
      
      assert selected_robot.difficulty_level == 2
      assert selected_robot.game_type == "racing"
    end
  end

  describe "Payment System Repositories" do
    test "PaymentOrderRepository operations" do
      order_data = TestHelper.generate_test_payment_order()
      
      # 测试创建订单
      {:ok, created_order} = PaymentOrderRepository.create_order(order_data)
      assert created_order.order_number == order_data.order_number
      assert Decimal.equal?(created_order.amount, order_data.amount)
      
      # 测试订单状态更新
      {:ok, updated_order} = PaymentOrderRepository.update_order_status(created_order.id, 1)
      assert updated_order.status == 1
      
      # 测试按用户查询订单
      {:ok, user_orders} = PaymentOrderRepository.list_orders_by_user(order_data.user_id)
      assert length(user_orders) >= 1
    end

    test "PaymentOrderRepository统计功能" do
      # 创建多个测试订单
      orders = Enum.map(1..10, fn _ ->
        order_data = TestHelper.generate_test_payment_order(%{status: 1}) # 已完成
        {:ok, order} = PaymentOrderRepository.create_order(order_data)
        order
      end)
      
      # 测试统计查询
      {:ok, stats} = PaymentOrderRepository.get_payment_stats()
      
      assert stats.total_orders >= 10
      assert Decimal.compare(stats.total_amount, Decimal.new("0")) == :gt
    end
  end

  describe "Activity System Repositories" do
    test "ActivityRepository operations" do
      activity_data = TestHelper.generate_test_activity()
      
      # 测试创建活动
      {:ok, created_activity} = ActivityRepository.create_activity(activity_data)
      assert created_activity.activity_name == activity_data.activity_name
      
      # 测试查询活跃活动
      {:ok, active_activities} = ActivityRepository.list_active_activities()
      assert length(active_activities) >= 1
      
      # 测试活动状态管理
      {:ok, updated_activity} = ActivityRepository.update_activity_status(created_activity.id, 2)
      assert updated_activity.status == 2
    end

    test "ParticipationRepository参与管理" do
      # 先创建活动
      activity_data = TestHelper.generate_test_activity()
      {:ok, activity} = ActivityRepository.create_activity(activity_data)
      
      # 创建参与记录
      participation_data = %{
        activity_id: activity.id,
        user_id: :rand.uniform(10000),
        participation_time: DateTime.utc_now(),
        status: 1
      }
      
      {:ok, participation} = ParticipationRepository.create_participation(participation_data)
      assert participation.activity_id == activity.id
      
      # 测试按活动查询参与者
      {:ok, participants} = ParticipationRepository.list_participants_by_activity(activity.id)
      assert length(participants) >= 1
    end
  end

  describe "Promotion System Repositories" do
    test "PromoterRepository operations" do
      promoter_data = TestHelper.generate_test_promoter()
      
      # 测试创建推广员
      {:ok, created_promoter} = PromoterRepository.create_promoter(promoter_data)
      assert created_promoter.promoter_code == promoter_data.promoter_code
      
      # 测试按用户ID查询
      {:ok, promoter_by_user} = PromoterRepository.get_promoter_by_user_id(promoter_data.user_id)
      assert promoter_by_user.id == created_promoter.id
      
      # 测试佣金增加
      commission_amount = Decimal.new("100.50")
      {:ok, updated_promoter} = PromoterRepository.increment_promoter_commission(
        created_promoter.id, 
        commission_amount
      )
      
      expected_total = Decimal.add(created_promoter.total_commission, commission_amount)
      assert Decimal.equal?(updated_promoter.total_commission, expected_total)
    end

    test "PromotionChannelRepository渠道管理" do
      # 先创建推广员
      promoter_data = TestHelper.generate_test_promoter()
      {:ok, promoter} = PromoterRepository.create_promoter(promoter_data)
      
      # 创建推广渠道
      channel_data = %{
        promoter_id: promoter.id,
        channel_name: "测试渠道",
        channel_type: "web",
        channel_link: "https://example.com/ref/#{promoter.promoter_code}",
        status: 1,
        click_count: 0,
        register_count: 0
      }
      
      {:ok, created_channel} = PromotionChannelRepository.create_channel(channel_data)
      assert created_channel.promoter_id == promoter.id
      
      # 测试点击计数增加
      {:ok, updated_channel} = PromotionChannelRepository.increment_channel_clicks(created_channel.id, 5)
      assert updated_channel.click_count == 5
      
      # 测试注册计数增加
      {:ok, final_channel} = PromotionChannelRepository.increment_channel_registers(updated_channel.id, 2)
      assert final_channel.register_count == 2
    end
  end

  describe "Cross-Repository Data Consistency" do
    test "用户相关数据一致性" do
      user_id = :rand.uniform(10000)
      
      # 创建用户相关的各种数据
      ticket_data = TestHelper.generate_test_ticket(%{user_id: user_id})
      {:ok, _ticket} = TicketRepository.create_ticket(ticket_data)
      
      order_data = TestHelper.generate_test_payment_order(%{user_id: user_id})
      {:ok, _order} = PaymentOrderRepository.create_order(order_data)
      
      promoter_data = TestHelper.generate_test_promoter(%{user_id: user_id})
      {:ok, _promoter} = PromoterRepository.create_promoter(promoter_data)
      
      # 验证数据一致性
      {:ok, user_tickets} = TicketRepository.list_tickets_by_user(user_id)
      {:ok, user_orders} = PaymentOrderRepository.list_orders_by_user(user_id)
      {:ok, user_promoter} = PromoterRepository.get_promoter_by_user_id(user_id)
      
      assert length(user_tickets) >= 1
      assert length(user_orders) >= 1
      assert user_promoter.user_id == user_id
    end
  end

  describe "Error Handling" do
    test "处理不存在的记录" do
      non_existent_id = 999999
      
      # 测试各Repository的错误处理
      assert {:error, :not_found} = TicketRepository.get_ticket(non_existent_id)
      assert {:error, :not_found} = PlatformRepository.get_platform(non_existent_id)
      assert {:error, :not_found} = PaymentOrderRepository.get_order(non_existent_id)
      assert {:error, :not_found} = ActivityRepository.get_activity(non_existent_id)
      assert {:error, :not_found} = PromoterRepository.get_promoter(non_existent_id)
    end

    test "处理无效数据" do
      # 测试创建无效数据
      invalid_ticket_data = %{title: nil, content: nil}
      assert {:error, _reason} = TicketRepository.create_ticket(invalid_ticket_data)
      
      invalid_platform_data = %{platform_name: nil}
      assert {:error, _reason} = PlatformRepository.create_platform(invalid_platform_data)
    end
  end

  describe "Performance Tests" do
    test "批量操作性能" do
      # 测试批量创建工单
      ticket_data_list = Enum.map(1..100, fn _ -> TestHelper.generate_test_ticket() end)
      
      {_results, time} = TestHelper.measure_time(fn ->
        Enum.map(ticket_data_list, fn ticket_data ->
          TicketRepository.create_ticket(ticket_data)
        end)
      end)
      
      # 100个工单创建应该在合理时间内完成（比如5秒）
      assert time < 5000
    end

    test "并发查询性能" do
      # 先创建一些测试数据
      ticket_data = TestHelper.generate_test_ticket()
      {:ok, ticket} = TicketRepository.create_ticket(ticket_data)
      
      # 并发查询测试
      results = TestHelper.concurrent_test(fn ->
        TicketRepository.get_ticket(ticket.id)
      end, 20)
      
      # 所有并发查询都应该成功
      assert Enum.all?(results, fn {:ok, _} -> true; _ -> false end)
    end
  end
end
