defmodule CypridinaWeb.GameChannel do
  @moduledoc """
  Phoenix Channel for handling Teen game messages.

  This channel processes messages sent by the client, handles authentication,
  and manages game-related communications.

  Since the WebSocket serializer has been changed to msgpack, the channel now
  receives already unpacked client messages in the format:
  %{"mainId" => main_id, "subId" => sub_id, "data" => data}

  And sends responses as map data without manual encoding.
  """
  use Phoenix.Channel
  require Logger

  alias Cypridina.Protocol.WebSocketHandler

  # Game 主协议常量
  @main_proto_game 4
  @main_proto_xc 5

  # Game 子协议常量
  @game_cs_mode1_enter_pipei_p 93
  @game_sc_mode1_enter_pipei_p 94
  @game_cs_room_player_ready 1102
  @game_cs_quit_p 40
  @game_cs_room_chat_p 6
  @game_sc_room_set_player_state_p 4
  @game_cs_room_set_player_state_p 3
  @game_cs_huanzhuo_p 43
  @game_sc_huanzhuo_p 44
  @game_cs_room_zanli_comback_p 10
  @game_cs_mode1_enter_p 45
  @game_sc_mode1_enter_p 47
  @game_cs_mode1_robot_enter_p 46
  @game_sc_room_info_p 2
  @game_sc_room_player_enter_p 12
  @game_sc_room_player_quit_p 14
  @game_sc_room_set_state_p 5
  @game_sc_room_zanli_success_p 9
  @game_sc_mode1_pipei_over_p 95
  @game_sc_mode1_quit_pipei_success_p 96
  @game_cs_game_player_num_p 34
  @game_sc_game_player_num_p 35

  @impl true
  def join("game:" <> topic, params, socket) do
    Logger.info("🎮 [CHANNEL_JOIN] 客户端加入游戏频道: #{topic}, 参数: #{inspect(params)}")

    # 解析游戏频道类型
    {channel_type, channel_info} = parse_game_channel(topic)
    # Assign channel info to socket
    socket =
      socket
      |> assign(:game_id, channel_info[:game_id])
      |> assign(:channel_type, channel_type)
      |> assign(:channel_info, channel_info)
      |> assign(:current_room, nil)



    Logger.info(
      "🎮 [CHANNEL_JOIN] 频道类型: #{channel_type}, 信息: #{inspect(channel_info)}, 频道数据: #{inspect(socket.assigns)}"
    )

    # Send a message to self to push the welcome message after join
    send(self(), :after_join)

    {:ok, socket}
  end

  # ==================== 游戏服务器登录处理 ====================
  @doc """
  处理游戏服务器登录 (RegLogin.CS_GAMESERVER_LOGIN_P)
  """
  def handle_in("message", %{"mainId" => 0, "subId" => 16, "data" => data} = payload, socket) do
    Logger.info("🎮 [GAME_LOGIN] 收到游戏服务器登录请求: #{inspect(payload)}")

    {:ok, user} = Cypridina.Protocol.QuickLoginHandler.handle_quick_login(data["hduc"])
    # user = user |> Ash.load!([:asset])

    player_id = Map.get(data, "playerid", socket.assigns.user_id)
    site_id = Map.get(data, "siteid", 1)
    # 从Channel上下文获取游戏房间信息
    money = Cypridina.Accounts.get_user_points(user.id)
    totalmoney = money

    {server_id, game_id} =
      case Map.get(socket.assigns, :channel_type) do
        :game_room ->
          channel_info = Map.get(socket.assigns, :channel_info, %{})
          {Map.get(channel_info, :server_id, 0), Map.get(channel_info, :game_id, 0)}

        _ ->
          # 如果不在游戏房间Channel中，尝试从消息数据获取
          {Map.get(data, "server_id", 0), Map.get(data, "game_id", 0)}
      end

    Logger.info(
      "🎮 [GAME_LOGIN] 解析得到 - GameID: #{game_id}, ServerID: #{server_id}, PlayerID: #{player_id}"
    )

    response_data =
      cond do
        game_id <= 0 ->
          Logger.warning("🎮 [GAME_LOGIN] 登录失败: 无效的游戏ID (#{game_id})")

          %{
            "status" => 2,
            "message" => "无效的游戏ID"
          }

        true ->
          Logger.info(
            "🎮 [GAME_LOGIN] 登录成功 - GameID: #{game_id}, PlayerID: #{player_id}"
          )

          # 构建响应数据，包含客户端发送的所有原始数据
          response_data =
            Map.merge(data, %{
              # 客户端检查的主要字段
              "code" => 0,
              # 错误消息（成功时为空）
              "msg" => "",
              # 0=正常登录, 1=断线重连
              "offline" => 0,
              # 确保playerid正确
              "playerid" => player_id,
              # 确保siteid正确
              "siteid" => site_id,

              # 游戏服务器信息
              "server_id" => 1,
              "game_id" => game_id,
              "session_token" => "game_session_#{:rand.uniform(1_000_000)}",
              "server_ip" => "127.0.0.1",
              "server_port" => 8080,

              # 用户相关信息（从数据库获取）
              # 用户金币 (使用真实积分)
              "money" => money,
              # 总金币 (使用真实积分)
              "totalmoney" => totalmoney,
              # 昵称
              "nickname" => "Player#{player_id}",
              # 头像URL
              "headurl" => "",
              # VIP等级
              "viplevel" => 0,
              # 性别
              "sex" => 1,
              # 是否需要GPS
              "needgps" => 0,
              # 作弊通道
              "cheatchannel" => 0,

              # 控制信息
              "Control" => %{
                "ControlScore" => 0,
                "Ratio" => 0,
                "RoundCount" => 0,
                "TakeCount" => 0,
                "TakeScore" => 0,
                "ControlStatus" => 0
              }
            })

          response_data
      end

    response_map = %{
      "mainId" => 0,
      "subId" => 17,
      "data" => response_data
    }

    socket =
      socket
      |> assign(:game_logged_in, response_data["status"] == 0)
      |> assign(:game_player_id, player_id)
      |> assign(:game_site_id, site_id)
      |> assign(:user_id, user.id)
      |> assign(:current_user, user)
      |> subscribe_topics("user:#{user.id}")

    {:reply, {:ok, response_map}, socket}
  end

  # 处理客户端发送的消息 - 现在payload已经是msgpack解包后的map格式
  @impl true
  def handle_in("message", %{"mainId" => @main_proto_game, "subId" => sub_id} = payload, socket) do
    Logger.info("🔵 [REQUEST] 收到客户端消息1: #{inspect(payload)}")

    # 提取消息字段 - 客户端发送格式（data字段可能为空）:
    data = Map.get(payload, "data", %{})

    Logger.info(
      "🔵 [REQUEST] 处理消息 - MainID: #{@main_proto_game}, SubID: #{sub_id}, Data: #{inspect(data)}"
    )

    handle_game_message(%{sub_id: sub_id, data: data}, socket)
  end

  # 房间内消息
  @impl true
  def handle_in("message", %{"mainId" => @main_proto_xc, "subId" => sub_id} = payload, socket) do
    data = Map.get(payload, "data", %{})

    current_room = socket.assigns.current_room
    user_id = socket.assigns.current_user.id

    cond do
      !current_room ->
        Logger.warning("🔴 [ERROR]玩家不在房间内，无法处理XC协议消息: #{inspect(payload)}")
        {:reply, {:error, %{reason: "not in room", details: "玩家不在房间内"}}, socket}

      !user_id ->
        Logger.warning("🔴 [ERROR]玩家未登录，无法处理XC协议消息: #{inspect(payload)}")
        {:reply, {:error, %{reason: "not logged in", details: "玩家未登录"}}, socket}

      true ->
        Cypridina.RoomSystem.RoomManager.send_to_room(
          current_room,
          {:game_message, user_id, payload}
        )

        Logger.info("🟢 [XC_MESSAGE] 处理XC协议消息 - SubID: #{sub_id}, Data: #{inspect(data)}")
        {:noreply, socket}
    end
  end

  # 其他消息
  @impl true
  def handle_in("message", %{"mainId" => main_id, "subId" => sub_id} = payload, socket)
      when is_map(payload) do
    Logger.info("🔵 [REQUEST] 收到客户端消息: #{inspect(payload)}")

    data = Map.get(payload, "data", %{})

    case WebSocketHandler.handle_message(
           %{main_id: main_id, sub_id: sub_id, data: data},
           socket.assigns
         ) do
      {:reply, response_map, new_assigns} ->
        Logger.info(
          "🟢 [RESPONSE] 处理消息成功，发送响应 - MainID: #{response_map["mainId"]}, SubID: #{response_map["subId"]}, Data: #{inspect(response_map["data"])}"
        )

        # 更新socket assigns
        socket = update_socket_assigns(socket, new_assigns)

        Logger.info("new_assigns #{inspect(new_assigns)}")
        # 正确的Phoenix Channel响应格式 - 回复客户端请求
        # {:reply, {:ok, response_map}, socket}
        {:noreply, socket}

      {:ok, new_assigns} ->
        Logger.info("🟡 [NO_RESPONSE] 处理消息成功，无需响应 - MainID: #{main_id}, SubID: #{sub_id}")

        # 更新socket assigns但不发送响应
        socket = update_socket_assigns(socket, new_assigns)
        {:noreply, socket}

      other ->
        Logger.warning(
          "🔴 [ERROR] WebSocketHandler返回未知格式 - MainID: #{main_id}, SubID: #{sub_id}, Result: #{inspect(other)}"
        )

        # 返回错误响应给客户端
        {:reply, {:error, %{reason: "handler_error", details: inspect(other)}}, socket}
    end
  end

  # Handle info messages from other processes
  @impl true
  def handle_info(:after_join, socket) do
    # 根据频道类型发送不同的欢迎消息
    %{server_id: server_id} = socket.assigns.channel_info
    game_id = socket.assigns.game_id
    user_id = socket.assigns.user_id

    Logger.info(
      "🎮 [CHANNEL_READY] 游戏频道准备就绪 - GameID: #{game_id}, ServerID: #{server_id}, UserID: #{user_id}"
    )

    welcome_data = %{
      "type" => "game_channel_ready",
      "message" => "游戏频道已准备就绪，请发送匹配请求",
      "game_id" => game_id,
      "server_id" => server_id,
      "session_id" => socket.assigns.session_id,
      "server_time" => System.system_time(:millisecond)
    }

    welcome_msg = %{
      "mainId" => 0,
      "subId" => 0,
      "data" => welcome_data
    }

    Logger.info("🎮 [WELCOME] 发送频道准备就绪消息")

    # Push welcome message - msgpack serializer will handle encoding
    push(socket, "message", welcome_msg)

    {:noreply, socket}
  end

  # 处理来自 PubSub 的广播消息
  def handle_info(
        %Phoenix.Socket.Broadcast{topic: _, event: "room_message", payload: payload},
        socket
      ) do
    push(socket, "message", payload)
    {:noreply, socket}
  end

  # 处理排除特定玩家的广播消息
  def handle_info(
        %Phoenix.Socket.Broadcast{topic: _, event: "room_message_exclude", payload: payload},
        socket
      ) do
    # 检查是否需要排除当前用户
    exclude_user_ids = Map.get(payload, "_exclude_user_ids")
    current_user_id = socket.assigns.user_id

    if current_user_id not in exclude_user_ids do
      # 移除排除标记后发送消息
      clean_payload = Map.delete(payload, "_exclude_user_ids")
      push(socket, "message", clean_payload)
    else
      Logger.debug("排除玩家 #{current_user_id} 的广播消息")
    end

    {:noreply, socket}
  end

  def handle_info(
        %Phoenix.Socket.Broadcast{topic: _, event: "private_message", payload: payload},
        socket
      ) do
    push(socket, "message", payload)
    {:noreply, socket}
  end

  @impl true
  def handle_info(info, socket) do
    Logger.debug("未处理的info消息: #{inspect(info)}")
    {:noreply, socket}
  end

  # Handle client disconnect
  @impl true
  def terminate(reason, socket) do
    Logger.info("Channel终止 [#{socket.assigns.session_id}]: #{inspect(reason)}")

    room_id = socket.assigns.current_room
    user_id = socket.assigns.user_id

    Logger.info("🎮 [AUTO_LEAVE] 自动离开房间: #{room_id}, 用户: #{user_id}")

    case Cypridina.RoomSystem.RoomManager.call_room(room_id, {:leave_room, user_id}) do
      {:ok, :left} ->
        Logger.info("🎮 [AUTO_LEAVE] 成功离开房间: #{room_id}")

      {:error, reason} ->
        Logger.warning("🎮 [AUTO_LEAVE] 离开房间失败: #{room_id}, 原因: #{reason}")
    end

    :ok
  end

  defp parse_game_channel("room_" <> room_info) do
    case String.split(room_info, "_") do
      [game_id, server_id] ->
        {:game_room,
         %{game_id: String.to_integer(game_id), server_id: String.to_integer(server_id)}}

      _ ->
        {:unknown, %{raw: room_info}}
    end
  end

  defp parse_game_channel(other), do: {:unknown, %{raw: other}}

  # 辅助函数：更新socket assigns
  defp update_socket_assigns(socket, new_assigns) when is_map(new_assigns) do
    Enum.reduce(new_assigns, socket, fn {key, value}, acc ->
      assign(acc, key, value)
    end)
  end

  defp update_socket_assigns(socket, _), do: socket

  # ==================== Game 协议处理 ====================

  # 处理游戏协议消息
  defp handle_game_message(
         %{sub_id: sub_id, data: data} = message,
         socket
       ) do
    Logger.info("🎮 [GAME] 处理游戏协议 - SubID: #{sub_id}, Data: #{inspect(data)}")

    case sub_id do
      @game_cs_mode1_enter_pipei_p ->
        handle_match_request(message, socket)

      @game_cs_room_player_ready ->
        handle_player_ready(message, socket)

      @game_cs_quit_p ->
        handle_quit_request(message, socket)

      @game_cs_room_chat_p ->
        handle_chat_message(message, socket)

      @game_cs_room_set_player_state_p ->
        handle_set_player_state(message, socket)

      @game_cs_huanzhuo_p ->
        handle_change_table(message, socket)

      @game_cs_room_zanli_comback_p ->
        handle_reconnect(message, socket)

      @game_cs_mode1_enter_p ->
        handle_mode1_enter(message, socket)

      # @game_cs_mode1_robot_enter_p ->
      #   handle_robot_enter(message, socket)

      @game_cs_game_player_num_p ->
        handle_player_count_request(message, socket)

      _ ->
        handle_generic_game_message(message, socket)
    end
  end

  # 处理匹配请求
  defp handle_match_request(%{sub_id: @game_cs_mode1_enter_pipei_p, data: data}, socket) do
    Logger.info("📥 [MATCH] 玩家请求匹配 - Data: #{inspect(data)}")

    # 获取用户ID和游戏信息
    user_id = socket.assigns.current_user.id
    game_id = socket.assigns.game_id

    # 调用房间管理器进行匹配
    case Cypridina.RoomSystem.RoomManager.match_room(user_id, game_id, data || %{}) do
      {:ok, room_info} ->
        room_id = room_info.room_id
        Logger.info("🎮 [MATCH] 匹配成功 - RoomID: #{room_id}")

        # 检查并订阅房间主题
        socket = subscribe_topics(socket, room_info.topic)

        # 更新socket状态
        socket = socket |> assign(:current_room, room_id)

        response_data = %{
          "status" => 0,
          "room_id" => room_id,
          "seat_id" => room_info.seat_id,
          "game_id" => game_id,
          "server_id" => 1,
          "max_players" => 6,
          "current_players" => room_info.current_players,
          "room_type" => 1,
          "server_time" => System.system_time(:millisecond)
        }

        response_map = %{
          "mainId" => @main_proto_game,
          "subId" => @game_sc_mode1_enter_pipei_p,
          "data" => response_data
        }

        Logger.info("📤 [MATCH] 返回匹配响应 - RoomID: #{room_info.room_id}")
        {:reply, {:ok, response_map}, socket}

      {:error, reason} ->
        Logger.warning("🎮 [MATCH] 匹配失败 - Reason: #{reason}")

        response_data = %{
          "status" => 1,
          "error" => reason,
          "room_id" => nil,
          "seat_id" => 0,
          "game_id" => game_id,
          "server_id" => 1,
          "room_type" => 0,
          "server_time" => System.system_time(:millisecond)
        }

        response_map = %{
          "mainId" => @main_proto_game,
          "subId" => @game_sc_mode1_enter_pipei_p,
          "data" => response_data
        }

        {:reply, {:ok, response_map}, socket}
    end
  end

  # 处理玩家准备
  defp handle_player_ready(%{data: data}, socket) do
    Logger.info("🎮 [READY] 玩家准备游戏: #{inspect(data)}")

    response_data = %{
      "status" => 0,
      "message" => "ready_success"
    }

    response_map = %{
      "mainId" => @main_proto_game,
      "subId" => @game_cs_room_player_ready + 1,
      "data" => response_data
    }

    {:reply, {:ok, response_map}, socket}
  end

  # 处理退出请求
  defp handle_quit_request(%{data: data}, socket) do
    Logger.info("🚪 [QUIT] 玩家请求退出: #{inspect(data)}")

    # 如果玩家在房间中，从房间中移除
    if socket.assigns[:current_room] do
      room_id = socket.assigns.current_room
      user_id = socket.assigns.user_id

      Logger.info("🚪 [QUIT] 玩家从房间 #{room_id} 退出")

      case Cypridina.RoomSystem.RoomManager.call_room(room_id, {:leave_room, user_id}) do
        {:ok, :left} ->
          Logger.info("🚪 [QUIT] 成功离开房间")

        {:error, reason} ->
          Logger.warning("🚪 [QUIT] 离开房间失败: #{reason}")
      end
    end

    # 清除房间状态
    socket = socket |> assign(:current_room, nil)

    # 不发送响应，让客户端自行处理退出逻辑
    {:noreply, socket}
  end

  # 处理聊天消息
  defp handle_chat_message(%{data: data}, socket) do
    Logger.info("💬 [CHAT] 玩家聊天: #{inspect(data)}")

    # 广播聊天消息给房间内其他玩家
    response_data =
      Map.merge(data || %{}, %{
        "user_id" => socket.assigns.user_id,
        "timestamp" => System.system_time(:millisecond)
      })

    response_map = %{
      "mainId" => @main_proto_game,
      "subId" => @game_cs_room_chat_p + 1,
      "data" => response_data
    }

    {:reply, {:ok, response_map}, socket}
  end

  # 处理设置玩家状态
  defp handle_set_player_state(%{data: data}, socket) do
    Logger.info("⚙️ [STATE] 设置玩家状态: #{inspect(data)}")

    player_state = Map.get(data || %{}, "player_state", 0)

    response_data = %{
      "status" => 0,
      "user_id" => socket.assigns.user_id,
      "player_state" => player_state,
      "message" => "状态设置成功"
    }

    response_map = %{
      "mainId" => @main_proto_game,
      "subId" => @game_sc_room_set_player_state_p,
      "data" => response_data
    }

    {:reply, {:ok, response_map}, socket}
  end

  # 处理换桌请求
  defp handle_change_table(%{data: data}, socket) do
    Logger.info("🔄 [CHANGE_TABLE] 玩家请求换桌: #{inspect(data)}")

    # 模拟换桌成功
    new_room_id = "room_#{:rand.uniform(1000)}"
    socket = socket |> assign(:current_room, new_room_id)

    response_data = %{
      "status" => 0,
      "room_id" => new_room_id,
      "seat_id" => :rand.uniform(6),
      "message" => "换桌成功"
    }

    response_map = %{
      "mainId" => @main_proto_game,
      "subId" => @game_sc_huanzhuo_p,
      "data" => response_data
    }

    {:reply, {:ok, response_map}, socket}
  end

  # 处理断线重连
  defp handle_reconnect(%{data: data}, socket) do
    Logger.info("🔌 [RECONNECT] 玩家断线重连: #{inspect(data)}")

    response_data = %{
      "status" => 0,
      "room_id" => socket.assigns[:current_room],
      "user_id" => socket.assigns.user_id,
      "message" => "重连成功"
    }

    response_map = %{
      "mainId" => @main_proto_game,
      "subId" => @game_cs_room_zanli_comback_p + 1,
      "data" => response_data
    }

    {:reply, {:ok, response_map}, socket}
  end

  # 处理模式1进入请求
  defp handle_mode1_enter(%{data: data}, socket) do
    Logger.info("🎮 [MODE1_ENTER] 玩家请求进入模式1: #{inspect(data)}")

    game_id = Map.get(data || %{}, "game_id", socket.assigns.game_id)
    server_id = Map.get(data || %{}, "server_id", socket.assigns.channel_info.server_id || 1)
    # user_id = socket.assigns.user_id
    user_id = socket.assigns.current_user.id

    # 调用房间管理器匹配房间
    case Cypridina.RoomSystem.RoomManager.match_room(user_id, game_id, %{}) do
      {:ok, room_info} ->
        Logger.info("🎮 [MODE1_ENTER] 匹配成功 - RoomID: #{room_info.room_id}")

        # 检查并订阅房间主题
        socket = subscribe_topics(socket, room_info.topic)

        # 更新socket状态
        socket =
          socket
          |> assign(:current_room, room_info.room_id)
          |> assign(:game_id, game_id)

        response_data = %{
          "code" => 0,
          "status" => 0,
          "room_id" => room_info.room_id,
          "seat_id" => room_info.seat_id,
          "game_id" => game_id,
          "server_id" => 1,
          "user_id" => user_id,
          "player_count" => room_info.current_players,
          "max_players" => 6,
          "message" => "进入成功"
        }

        Logger.info(
          "🎮 [MODE1_ENTER] 进入成功 - GameID: #{game_id}, ServerID: #{server_id}, UserID: #{user_id}"
        )

        response_map = %{
          "mainId" => @main_proto_game,
          "subId" => @game_sc_mode1_enter_p,
          "data" => response_data
        }

        {:reply, {:ok, response_map}, socket}

      {:error, reason} ->
        Logger.warning("🎮 [MODE1_ENTER] 匹配失败 - Reason: #{reason}")

        response_data = %{
          "status" => 1,
          "error" => reason,
          "game_id" => game_id,
          "server_id" => server_id,
          "message" => "进入失败"
        }

        response_map = %{
          "mainId" => @main_proto_game,
          "subId" => @game_sc_mode1_enter_p,
          "data" => response_data
        }

        {:reply, {:ok, response_map}, socket}
    end
  end

  # 处理机器人进入请求
  # defp handle_robot_enter(%{data: data}, socket) do
  #   Logger.info("🤖 [ROBOT] 机器人进入请求: #{inspect(data)}")

  #   response_data = %{
  #     "status" => 0,
  #     "robot_count" => :rand.uniform(3),
  #     "message" => "机器人已加入"
  #   }

  #   response_map = %{
  #     "mainId" => @main_proto_game,
  #     "subId" => @game_sc_mode1_enter_p,
  #     "data" => response_data
  #   }

  #   {:reply, {:ok, response_map}, socket}
  # end

  # 处理玩家人数请求
  defp handle_player_count_request(%{data: data}, socket) do
    Logger.info("📊 [PLAYER_COUNT] 请求游戏玩家人数表: #{inspect(data)}")

    # 模拟各个游戏的玩家人数数据
    response_data = %{
      "status" => 0,
      "game_list" => [
        %{
          "game_id" => 201,
          "game_name" => "龙虎斗",
          "total_players" => :rand.uniform(500) + 100,
          "servers" => [
            %{"server_id" => 1, "server_name" => "初级场", "players" => :rand.uniform(100) + 20},
            %{"server_id" => 2, "server_name" => "中级场", "players" => :rand.uniform(80) + 15},
            %{"server_id" => 3, "server_name" => "高级场", "players" => :rand.uniform(60) + 10}
          ]
        },
        %{
          "game_id" => 202,
          "game_name" => "百家乐",
          "total_players" => :rand.uniform(400) + 80,
          "servers" => [
            %{"server_id" => 1, "server_name" => "初级场", "players" => :rand.uniform(90) + 18},
            %{"server_id" => 2, "server_name" => "中级场", "players" => :rand.uniform(70) + 12},
            %{"server_id" => 3, "server_name" => "高级场", "players" => :rand.uniform(50) + 8}
          ]
        },
        %{
          "game_id" => 203,
          "game_name" => "牛牛",
          "total_players" => :rand.uniform(300) + 60,
          "servers" => [
            %{"server_id" => 1, "server_name" => "初级场", "players" => :rand.uniform(80) + 15},
            %{"server_id" => 2, "server_name" => "中级场", "players" => :rand.uniform(60) + 10},
            %{"server_id" => 3, "server_name" => "高级场", "players" => :rand.uniform(40) + 5}
          ]
        }
      ],
      "server_time" => System.system_time(:millisecond),
      "message" => "获取游戏玩家人数成功"
    }

    response_map = %{
      "mainId" => @main_proto_game,
      "subId" => @game_sc_game_player_num_p,
      "data" => response_data
    }

    {:reply, {:ok, response_map}, socket}
  end

  # 处理通用游戏消息
  defp handle_generic_game_message(%{sub_id: sub_id, data: data}, socket) do
    Logger.info("🎮 [GAME_GENERIC] 通用游戏消息 - SubID: #{sub_id}, Data: #{inspect(data)}")

    response_data = %{
      "status" => 0,
      "message" => "received",
      "original_sub_id" => sub_id
    }

    response_map = %{
      "mainId" => @main_proto_game,
      "subId" => sub_id + 1,
      "data" => response_data
    }

    Logger.info("🎮 [GAME_GENERIC] 通用响应: MainID=#{@main_proto_game}, SubID=#{sub_id + 1}")

    {:reply, {:ok, response_map}, socket}
  end

  # ==================== 订阅管理函数 ====================

  # 智能订阅房间主题，避免重复订阅
  defp subscribe_topics(socket, topic) do
    # 获取当前已订阅的主题
    current_subscriptions = socket.assigns[:subscribed_topics] || MapSet.new()

    # 检查并订阅房间主题
    {socket, new_subscriptions} = if MapSet.member?(current_subscriptions, topic) do
      Logger.info("🔔 [SUBSCRIBE] 已订阅消息，跳过: #{topic}")
      {socket, current_subscriptions}
    else
      Logger.info("🔔 [SUBSCRIBE] 订阅消息: #{topic}")
      CypridinaWeb.Endpoint.subscribe(topic)
      {socket, MapSet.put(current_subscriptions, topic)}
    end
    # 更新socket中的订阅记录
    assign(socket, :subscribed_topics, new_subscriptions)
  end

  # 取消订阅房间主题
  defp unsubscribe_topics(socket, topic) do
    current_subscriptions = socket.assigns[:subscribed_topics] || MapSet.new()

    # 取消订阅房间主题
    new_subscriptions = if MapSet.member?(current_subscriptions, topic) do
      Logger.info("🔕 [UNSUBSCRIBE] 取消订阅消息: #{topic}")
      CypridinaWeb.Endpoint.unsubscribe(topic)
      MapSet.delete(current_subscriptions, topic)
    else
      current_subscriptions
    end

    # 更新socket中的订阅记录
    assign(socket, :subscribed_topics, new_subscriptions)
  end
end
