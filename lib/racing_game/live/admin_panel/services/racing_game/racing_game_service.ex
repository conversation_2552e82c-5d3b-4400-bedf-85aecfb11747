defmodule RacingGame.Live.AdminPanel.Services.RacingGame.RacingGameService do
  @moduledoc """
  赛车游戏业务逻辑服务

  提供赛车游戏系统的业务逻辑协调：
  - 比赛管理业务逻辑
  - 投注业务流程
  - 股票交易业务
  - 综合统计和报表
  """

  require Logger
  alias RacingGame.Live.AdminPanel.Repositories.RacingGame.{
    RaceRepository,
    BetRepository,
    StockRepository,
    SystemCommunicationRepository
  }
  alias RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder
  alias Phoenix.PubSub

  # 常量定义
  @pubsub_topic "racing_game_updates"
  @default_currency :XAA

  # ============================================================================
  # 比赛管理业务逻辑
  # ============================================================================

  @doc """
  创建比赛

  ## 参数
  - `race_data` - 比赛数据
  - `options` - 选项

  ## 返回
  - `{:ok, race}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_race(race_data, options \\ []) do
    Logger.info("🏁 [赛车服务] 创建比赛: #{race_data[:name]}")

    with :ok <- validate_race_data(race_data),
         {:ok, race} <- RaceRepository.create_race(race_data),
         :ok <- schedule_race_notifications(race),
         :ok <- broadcast_race_created(race) do

      Logger.info("✅ [赛车服务] 比赛创建成功: #{race.id}")
      {:ok, race}
    else
      {:error, reason} = error ->
        Logger.error("❌ [赛车服务] 创建比赛失败: #{inspect(reason)}")
        error
    end
  end

  @doc """
  开始比赛

  ## 参数
  - `race_id` - 比赛ID
  - `options` - 选项

  ## 返回
  - `{:ok, race}` - 成功
  - `{:error, reason}` - 失败
  """
  def start_race(race_id, options \\ []) do
    Logger.info("🚀 [赛车服务] 开始比赛: #{race_id}")

    with {:ok, race} <- RaceRepository.get_race_by_id(race_id),
         :ok <- validate_race_start(race),
         {:ok, updated_race} <- RaceRepository.update_race_status(race_id, :running),
         :ok <- process_race_start_effects(updated_race),
         :ok <- broadcast_race_started(updated_race) do

      Logger.info("✅ [赛车服务] 比赛开始成功: #{race_id}")
      {:ok, updated_race}
    else
      error -> error
    end
  end

  @doc """
  结束比赛

  ## 参数
  - `race_id` - 比赛ID
  - `results_data` - 比赛结果数据
  - `options` - 选项

  ## 返回
  - `{:ok, race}` - 成功
  - `{:error, reason}` - 失败
  """
  def finish_race(race_id, results_data, options \\ []) do
    Logger.info("🏆 [赛车服务] 结束比赛: #{race_id}")

    with {:ok, race} <- RaceRepository.get_race_by_id(race_id),
         :ok <- validate_race_finish(race),
         {:ok, race_results} <- create_race_results(race_id, results_data),
         {:ok, updated_race} <- RaceRepository.update_race_status(race_id, :finished),
         :ok <- settle_race_bets(race_id, race_results),
         :ok <- broadcast_race_finished(updated_race, race_results) do

      Logger.info("✅ [赛车服务] 比赛结束成功: #{race_id}")
      {:ok, updated_race}
    else
      error -> error
    end
  end

  @doc """
  取消比赛

  ## 参数
  - `race_id` - 比赛ID
  - `reason` - 取消原因
  - `options` - 选项

  ## 返回
  - `{:ok, race}` - 成功
  - `{:error, reason}` - 失败
  """
  def cancel_race(race_id, reason, options \\ []) do
    Logger.info("❌ [赛车服务] 取消比赛: #{race_id} - #{reason}")

    with {:ok, race} <- RaceRepository.get_race_by_id(race_id),
         :ok <- validate_race_cancellation(race),
         {:ok, updated_race} <- RaceRepository.update_race_status(race_id, :cancelled, reason: reason),
         :ok <- refund_race_bets(race_id, reason),
         :ok <- broadcast_race_cancelled(updated_race, reason) do

      Logger.info("✅ [赛车服务] 比赛取消成功: #{race_id}")
      {:ok, updated_race}
    else
      error -> error
    end
  end

  # ============================================================================
  # 投注业务逻辑
  # ============================================================================

  @doc """
  创建投注

  ## 参数
  - `user_id` - 用户ID
  - `race_id` - 比赛ID
  - `bet_data` - 投注数据
  - `options` - 选项

  ## 返回
  - `{:ok, bet}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_bet(user_id, race_id, bet_data, options \\ []) do
    Logger.info("💰 [赛车服务] 创建投注: 用户#{user_id} 比赛#{race_id} 金额#{bet_data[:amount]}")

    bet_data = Map.merge(bet_data, %{user_id: user_id, race_id: race_id})

    with {:ok, race} <- RaceRepository.get_race_by_id(race_id),
         :ok <- validate_bet_creation(race, bet_data),
         :ok <- validate_user_betting_limits(user_id, bet_data),
         {:ok, bet} <- BetRepository.create_bet(bet_data),
         :ok <- process_bet_payment(bet),
         :ok <- broadcast_bet_created(bet) do

      Logger.info("✅ [赛车服务] 投注创建成功: #{bet.id}")
      {:ok, bet}
    else
      error -> error
    end
  end

  @doc """
  取消投注

  ## 参数
  - `bet_id` - 投注ID
  - `reason` - 取消原因
  - `options` - 选项

  ## 返回
  - `{:ok, bet}` - 成功
  - `{:error, reason}` - 失败
  """
  def cancel_bet(bet_id, reason \\ "用户取消", options \\ []) do
    Logger.info("❌ [赛车服务] 取消投注: #{bet_id} - #{reason}")

    with {:ok, bet} <- BetRepository.get_bet_by_id(bet_id),
         :ok <- validate_bet_cancellation(bet),
         {:ok, cancelled_bet} <- BetRepository.cancel_bet(bet_id, reason),
         :ok <- process_bet_refund(cancelled_bet),
         :ok <- broadcast_bet_cancelled(cancelled_bet) do

      Logger.info("✅ [赛车服务] 投注取消成功: #{bet_id}")
      {:ok, cancelled_bet}
    else
      error -> error
    end
  end

  @doc """
  批量结算投注

  ## 参数
  - `race_id` - 比赛ID
  - `results` - 比赛结果
  - `options` - 选项

  ## 返回
  - `{:ok, settlement_results}` - 成功
  - `{:error, reason}` - 失败
  """
  def settle_race_bets(race_id, results, options \\ []) do
    Logger.info("💰 [赛车服务] 批量结算投注: 比赛#{race_id}")

    with {:ok, bets} <- BetRepository.get_race_bets(race_id),
         {:ok, settlement_results} <- process_bet_settlements(bets, results),
         :ok <- broadcast_bets_settled(race_id, settlement_results) do

      Logger.info("✅ [赛车服务] 投注结算成功: 比赛#{race_id}, 处理#{length(bets)}笔投注")
      {:ok, settlement_results}
    else
      error -> error
    end
  end

  # ============================================================================
  # 股票交易业务逻辑
  # ============================================================================

  @doc """
  执行股票交易

  ## 参数
  - `user_id` - 用户ID
  - `stock_id` - 股票ID
  - `transaction_type` - 交易类型 (:buy | :sell)
  - `quantity` - 数量
  - `price` - 价格
  - `options` - 选项

  ## 返回
  - `{:ok, transaction}` - 成功
  - `{:error, reason}` - 失败
  """
  def execute_stock_transaction(user_id, stock_id, transaction_type, quantity, price, options \\ []) do
    Logger.info("📈 [赛车服务] 执行股票交易: 用户#{user_id} #{transaction_type} #{quantity}股 价格#{price}")

    transaction_data = %{
      user_id: user_id,
      stock_id: stock_id,
      transaction_type: transaction_type,
      quantity: quantity,
      price: price,
      total_amount: Money.multiply(price, quantity)
    }

    with {:ok, stock} <- StockRepository.get_stock_by_id(stock_id),
         :ok <- validate_stock_transaction(stock, transaction_data),
         :ok <- validate_user_stock_limits(user_id, transaction_data),
         {:ok, transaction} <- StockRepository.create_stock_transaction(transaction_data),
         :ok <- process_stock_transaction_effects(transaction),
         :ok <- broadcast_stock_transaction(transaction) do

      Logger.info("✅ [赛车服务] 股票交易成功: #{transaction.id}")
      {:ok, transaction}
    else
      error -> error
    end
  end

  @doc """
  获取用户投资组合

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - `{:ok, portfolio}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_portfolio(user_id, options \\ []) do
    Logger.debug("👤 [赛车服务] 获取用户投资组合: #{user_id}")

    with {:ok, holdings} <- StockRepository.get_user_holdings(user_id, options),
         {:ok, transactions} <- StockRepository.get_user_transactions(user_id, options),
         {:ok, statistics} <- StockRepository.get_user_stock_statistics(user_id, options) do

      portfolio = %{
        user_id: user_id,
        holdings: holdings,
        recent_transactions: transactions,
        statistics: statistics,
        generated_at: DateTime.utc_now()
      }

      {:ok, portfolio}
    else
      error -> error
    end
  end

  # ============================================================================
  # 系统通信管理
  # ============================================================================

  @doc """
  创建系统消息

  ## 参数
  - `communication_data` - 消息数据
  - `options` - 选项

  ## 返回
  - `{:ok, communication}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_system_communication(communication_data, options \\ []) do
    Logger.info("📢 [赛车服务] 创建系统消息: #{communication_data[:title]}")

    with :ok <- validate_communication_data(communication_data),
         {:ok, communication} <- SystemCommunicationRepository.create_communication(communication_data),
         :ok <- broadcast_communication_created(communication) do

      Logger.info("✅ [赛车服务] 系统消息创建成功: #{communication.id}")
      {:ok, communication}
    else
      error -> error
    end
  end

  @doc """
  标记消息为已读

  ## 参数
  - `user_id` - 用户ID
  - `communication_id` - 消息ID
  - `options` - 选项

  ## 返回
  - `{:ok, read_record}` - 成功
  - `{:error, reason}` - 失败
  """
  def mark_communication_as_read(user_id, communication_id, options \\ []) do
    Logger.info("👁️ [赛车服务] 标记消息已读: 用户#{user_id} 消息#{communication_id}")

    with {:ok, communication} <- SystemCommunicationRepository.get_communication_by_id(communication_id),
         {:ok, read_record} <- SystemCommunicationRepository.mark_as_read(user_id, communication_id),
         :ok <- broadcast_communication_read(user_id, communication_id) do

      Logger.info("✅ [赛车服务] 消息标记已读成功")
      {:ok, read_record}
    else
      error -> error
    end
  end

  @doc """
  获取用户未读消息

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - `{:ok, communications}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_unread_communications(user_id, options \\ []) do
    Logger.debug("👤 [赛车服务] 获取用户未读消息: #{user_id}")

    SystemCommunicationRepository.get_user_unread_communications(user_id, options)
  end

  # ============================================================================
  # 综合统计和报表
  # ============================================================================

  @doc """
  获取系统综合报表

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, report}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_system_comprehensive_report(options \\ []) do
    Logger.debug("📊 [赛车服务] 获取系统综合报表")

    with {:ok, race_stats} <- RaceRepository.get_race_statistics(options),
         {:ok, bet_stats} <- BetRepository.get_bet_statistics(options),
         {:ok, stock_stats} <- StockRepository.get_stock_statistics(options),
         {:ok, communication_stats} <- SystemCommunicationRepository.get_communication_statistics(options) do

      report = %{
        races: race_stats,
        bets: bet_stats,
        stocks: stock_stats,
        communications: communication_stats,
        summary: calculate_system_summary(race_stats, bet_stats, stock_stats, communication_stats),
        generated_at: DateTime.utc_now()
      }

      {:ok, report}
    else
      error -> error
    end
  end

  @doc """
  获取用户综合报表

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - `{:ok, report}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_comprehensive_report(user_id, options \\ []) do
    Logger.debug("👤 [赛车服务] 获取用户综合报表: #{user_id}")

    with {:ok, bet_stats} <- BetRepository.get_user_bet_statistics(user_id, options),
         {:ok, stock_stats} <- StockRepository.get_user_stock_statistics(user_id, options),
         {:ok, portfolio} <- get_user_portfolio(user_id, options) do

      report = %{
        user_id: user_id,
        betting: bet_stats,
        stocks: stock_stats,
        portfolio: portfolio,
        summary: calculate_user_summary(bet_stats, stock_stats),
        generated_at: DateTime.utc_now()
      }

      {:ok, report}
    else
      error -> error
    end
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 验证比赛数据
  defp validate_race_data(race_data) do
    cond do
      is_nil(race_data[:name]) or String.trim(race_data[:name]) == "" ->
        {:error, :invalid_race_name}
      is_nil(race_data[:start_time]) ->
        {:error, :invalid_start_time}
      DateTime.compare(race_data[:start_time], DateTime.utc_now()) != :gt ->
        {:error, :start_time_in_past}
      true ->
        :ok
    end
  end

  # 验证比赛开始
  defp validate_race_start(race) do
    case race.status do
      :waiting -> :ok
      :running -> {:error, :race_already_running}
      :finished -> {:error, :race_already_finished}
      :cancelled -> {:error, :race_cancelled}
      _ -> {:error, :invalid_race_status}
    end
  end

  # 验证比赛结束
  defp validate_race_finish(race) do
    case race.status do
      :running -> :ok
      :waiting -> {:error, :race_not_started}
      :finished -> {:error, :race_already_finished}
      :cancelled -> {:error, :race_cancelled}
      _ -> {:error, :invalid_race_status}
    end
  end

  # 验证比赛取消
  defp validate_race_cancellation(race) do
    case race.status do
      :waiting -> :ok
      :running -> :ok
      :finished -> {:error, :race_already_finished}
      :cancelled -> {:error, :race_already_cancelled}
      _ -> {:error, :invalid_race_status}
    end
  end

  # 验证投注创建
  defp validate_bet_creation(race, bet_data) do
    cond do
      race.status != :waiting ->
        {:error, :race_not_accepting_bets}
      Money.negative?(bet_data[:amount]) ->
        {:error, :invalid_bet_amount}
      DateTime.compare(race.start_time, DateTime.utc_now()) != :gt ->
        {:error, :betting_closed}
      true ->
        :ok
    end
  end

  # 验证用户投注限制
  defp validate_user_betting_limits(user_id, bet_data) do
    # 这里可以实现用户投注限制逻辑
    # 简化实现
    :ok
  end

  # 验证投注取消
  defp validate_bet_cancellation(bet) do
    case bet.status do
      :pending -> :ok
      :won -> {:error, :bet_already_won}
      :lost -> {:error, :bet_already_lost}
      :cancelled -> {:error, :bet_already_cancelled}
      :paid -> {:error, :bet_already_paid}
      _ -> {:error, :invalid_bet_status}
    end
  end

  # 验证股票交易
  defp validate_stock_transaction(stock, transaction_data) do
    cond do
      not stock.is_active ->
        {:error, :stock_not_active}
      transaction_data[:quantity] <= 0 ->
        {:error, :invalid_quantity}
      Money.negative?(transaction_data[:price]) ->
        {:error, :invalid_price}
      true ->
        :ok
    end
  end

  # 验证用户股票限制
  defp validate_user_stock_limits(user_id, transaction_data) do
    # 这里可以实现用户股票交易限制逻辑
    # 简化实现
    :ok
  end

  # 处理比赛开始效果
  defp process_race_start_effects(race) do
    # 这里可以实现比赛开始的副作用
    # 例如：锁定投注、发送通知等
    Logger.info("🚀 [赛车服务] 处理比赛开始效果: #{race.id}")
    :ok
  end

  # 创建比赛结果
  defp create_race_results(race_id, results_data) do
    # 这里需要实现创建比赛结果的逻辑
    Logger.info("🏆 [赛车服务] 创建比赛结果: #{race_id}")
    {:ok, results_data}
  end

  # 处理投注结算
  defp process_bet_settlements(bets, results) do
    # 这里需要实现投注结算逻辑
    Logger.info("💰 [赛车服务] 处理投注结算: #{length(bets)}笔投注")

    settlement_results = Enum.map(bets, fn bet ->
      # 简化的结算逻辑
      settlement_data = determine_bet_outcome(bet, results)
      BetRepository.settle_bet(bet.id, settlement_data)
    end)

    {:ok, settlement_results}
  end

  # 确定投注结果
  defp determine_bet_outcome(bet, results) do
    # 这里需要根据比赛结果确定投注输赢
    # 简化实现
    %{
      status: :won,
      payout_amount: Money.multiply(bet.amount, 2)
    }
  end

  # 退还比赛投注
  defp refund_race_bets(race_id, reason) do
    Logger.info("💸 [赛车服务] 退还比赛投注: #{race_id} - #{reason}")

    case BetRepository.get_race_bets(race_id) do
      {:ok, bets} ->
        Enum.each(bets, fn bet ->
          BetRepository.update_bet_status(bet.id, :refunded, reason: reason)
        end)
        :ok
      error -> error
    end
  end

  # 处理投注支付
  defp process_bet_payment(bet) do
    # 这里需要实现投注支付逻辑
    Logger.info("💳 [赛车服务] 处理投注支付: #{bet.id}")
    :ok
  end

  # 处理投注退款
  defp process_bet_refund(bet) do
    # 这里需要实现投注退款逻辑
    Logger.info("💸 [赛车服务] 处理投注退款: #{bet.id}")
    :ok
  end

  # 处理股票交易效果
  defp process_stock_transaction_effects(transaction) do
    # 这里需要实现股票交易的副作用
    # 例如：更新持仓、处理资金等
    Logger.info("📈 [赛车服务] 处理股票交易效果: #{transaction.id}")

    quantity_change = case transaction.transaction_type do
      :buy -> transaction.quantity
      :sell -> -transaction.quantity
    end

    StockRepository.update_holding(
      transaction.user_id,
      transaction.stock_id,
      quantity_change,
      price: transaction.price
    )
  end

  # 安排比赛通知
  defp schedule_race_notifications(race) do
    # 这里可以实现比赛通知调度逻辑
    Logger.info("📅 [赛车服务] 安排比赛通知: #{race.id}")
    :ok
  end

  # 验证通信数据
  defp validate_communication_data(communication_data) do
    cond do
      is_nil(communication_data[:title]) or String.trim(communication_data[:title]) == "" ->
        {:error, :invalid_title}
      is_nil(communication_data[:content]) or String.trim(communication_data[:content]) == "" ->
        {:error, :invalid_content}
      true ->
        :ok
    end
  end

  # 计算系统摘要
  defp calculate_system_summary(race_stats, bet_stats, stock_stats, communication_stats \\ %{}) do
    %{
      total_races: race_stats.total_races,
      total_bets: bet_stats.total_bets,
      total_stocks: stock_stats.total_stocks,
      total_communications: Map.get(communication_stats, :total_communications, 0),
      active_races: race_stats.active_races,
      active_bets: bet_stats.active_bets,
      active_communications: Map.get(communication_stats, :active_communications, 0)
    }
  end

  # 计算用户摘要
  defp calculate_user_summary(bet_stats, stock_stats) do
    %{
      total_bets: bet_stats.total_bets,
      total_holdings: stock_stats.total_holdings,
      win_rate: bet_stats.win_rate,
      portfolio_diversity: stock_stats.portfolio_diversity
    }
  end

  # ============================================================================
  # 事件广播
  # ============================================================================

  defp broadcast_race_created(race) do
    PubSub.broadcast(RacingGame.PubSub, @pubsub_topic, {:race_created, race})
  end

  defp broadcast_race_started(race) do
    PubSub.broadcast(RacingGame.PubSub, @pubsub_topic, {:race_started, race})
  end

  defp broadcast_race_finished(race, results) do
    PubSub.broadcast(RacingGame.PubSub, @pubsub_topic, {:race_finished, race, results})
  end

  defp broadcast_race_cancelled(race, reason) do
    PubSub.broadcast(RacingGame.PubSub, @pubsub_topic, {:race_cancelled, race, reason})
  end

  defp broadcast_bet_created(bet) do
    PubSub.broadcast(RacingGame.PubSub, @pubsub_topic, {:bet_created, bet})
  end

  defp broadcast_bet_cancelled(bet) do
    PubSub.broadcast(RacingGame.PubSub, @pubsub_topic, {:bet_cancelled, bet})
  end

  defp broadcast_bets_settled(race_id, results) do
    PubSub.broadcast(RacingGame.PubSub, @pubsub_topic, {:bets_settled, race_id, results})
  end

  defp broadcast_stock_transaction(transaction) do
    PubSub.broadcast(RacingGame.PubSub, @pubsub_topic, {:stock_transaction, transaction})
  end

  defp broadcast_communication_created(communication) do
    PubSub.broadcast(RacingGame.PubSub, @pubsub_topic, {:communication_created, communication})
  end

  defp broadcast_communication_read(user_id, communication_id) do
    PubSub.broadcast(RacingGame.PubSub, @pubsub_topic, {:communication_read, user_id, communication_id})
  end
end
