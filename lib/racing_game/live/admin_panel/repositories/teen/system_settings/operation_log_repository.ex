defmodule RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.OperationLogRepository do
  @moduledoc """
  📝 操作日志仓储层

  负责操作日志的数据访问操作，包括：
  - 操作日志的CRUD操作
  - 日志查询和过滤
  - 日志统计分析
  - 日志清理管理
  - 审计追踪功能
  """

  require Logger
  import Ash.Query

  alias Teen.SystemSettings.OperationLog

  # 缓存配置
  @cache_ttl 180_000  # 3分钟缓存（日志数据实时性要求高）
  @cache_prefix "operation_log_repo"

  # ==================== 基础 CRUD 操作 ====================

  @doc """
  创建操作日志

  ## 参数
  - `log_data` - 日志数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, operation_log}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_operation_log(log_data, options \\ []) do
    Logger.info("📝 [操作日志仓储] 创建操作日志: #{inspect(log_data[:operation_type])}")

    try do
      result = OperationLog
      |> Ash.Changeset.for_create(:create, log_data)
      |> Ash.create()

      case result do
        {:ok, operation_log} ->
          Logger.info("✅ [操作日志仓储] 操作日志创建成功: #{operation_log.operation_type}")
          {:ok, operation_log}
        {:error, error} ->
          Logger.error("❌ [操作日志仓储] 操作日志创建失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [操作日志仓储] 创建操作日志异常: #{inspect(exception)}")
        {:error, :create_operation_log_exception}
    end
  end

  @doc """
  根据ID获取操作日志

  ## 参数
  - `log_id` - 日志ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, operation_log}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_operation_log(log_id, options \\ []) do
    Logger.info("📝 [操作日志仓储] 获取操作日志: #{log_id}")

    try do
      result = OperationLog
      |> Ash.get(log_id)

      case result do
        {:ok, operation_log} when not is_nil(operation_log) ->
          Logger.info("✅ [操作日志仓储] 操作日志获取成功: #{operation_log.operation_type}")
          {:ok, operation_log}
        {:ok, nil} ->
          Logger.warn("⚠️ [操作日志仓储] 操作日志不存在: #{log_id}")
          {:error, :operation_log_not_found}
        {:error, error} ->
          Logger.error("❌ [操作日志仓储] 操作日志获取失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [操作日志仓储] 获取操作日志异常: #{inspect(exception)}")
        {:error, :get_operation_log_exception}
    end
  end

  @doc """
  删除操作日志

  ## 参数
  - `log_id` - 日志ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, operation_log}` - 成功
  - `{:error, reason}` - 失败
  """
  def delete_operation_log(log_id, options \\ []) do
    Logger.info("📝 [操作日志仓储] 删除操作日志: #{log_id}")

    try do
      with {:ok, operation_log} <- get_operation_log(log_id, options) do
        result = operation_log |> Ash.destroy()

        case result do
          :ok ->
            Logger.info("✅ [操作日志仓储] 操作日志删除成功: #{operation_log.operation_type}")
            {:ok, operation_log}
          {:error, error} ->
            Logger.error("❌ [操作日志仓储] 操作日志删除失败: #{inspect(error)}")
            {:error, error}
        end
      end
    rescue
      exception ->
        Logger.error("💥 [操作日志仓储] 删除操作日志异常: #{inspect(exception)}")
        {:error, :delete_operation_log_exception}
    end
  end

  # ==================== 查询操作 ====================

  @doc """
  获取操作日志列表

  ## 参数
  - `params` - 查询参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, {operation_logs, total_count}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_operation_logs(params \\ %{}, options \\ []) do
    Logger.info("📝 [操作日志仓储] 获取操作日志列表: #{inspect(params)}")

    try do
      query = OperationLog
      |> build_list_query(params)
      |> maybe_load_associations(options)

      # 获取总数
      count_result = query |> Ash.count()
      
      # 获取数据
      list_result = query |> Ash.read()

      case {count_result, list_result} do
        {{:ok, total_count}, {:ok, operation_logs}} ->
          Logger.info("✅ [操作日志仓储] 操作日志列表获取成功: #{length(operation_logs)}条记录")
          {:ok, {operation_logs, total_count}}
        {{:error, error}, _} ->
          Logger.error("❌ [操作日志仓储] 获取日志总数失败: #{inspect(error)}")
          {:error, error}
        {_, {:error, error}} ->
          Logger.error("❌ [操作日志仓储] 获取日志列表失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [操作日志仓储] 获取操作日志列表异常: #{inspect(exception)}")
        {:error, :list_operation_logs_exception}
    end
  end

  @doc """
  根据管理员获取操作日志

  ## 参数
  - `admin_user_id` - 管理员用户ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, operation_logs}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_operation_logs_by_admin(admin_user_id, options \\ []) do
    Logger.info("📝 [操作日志仓储] 根据管理员获取操作日志: #{admin_user_id}")

    try do
      result = OperationLog
      |> Ash.Query.for_read(:list_by_admin, %{admin_user_id: admin_user_id})
      |> maybe_load_associations(options)
      |> Ash.read()

      case result do
        {:ok, operation_logs} ->
          Logger.info("✅ [操作日志仓储] 管理员日志获取成功: #{length(operation_logs)}条记录")
          {:ok, operation_logs}
        {:error, error} ->
          Logger.error("❌ [操作日志仓储] 管理员日志获取失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [操作日志仓储] 根据管理员获取日志异常: #{inspect(exception)}")
        {:error, :list_by_admin_exception}
    end
  end

  @doc """
  根据操作类型获取日志

  ## 参数
  - `operation_type` - 操作类型
  - `options` - 选项参数

  ## 返回
  - `{:ok, operation_logs}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_operation_logs_by_type(operation_type, options \\ []) do
    Logger.info("📝 [操作日志仓储] 根据操作类型获取日志: #{operation_type}")

    try do
      result = OperationLog
      |> Ash.Query.for_read(:list_by_type, %{operation_type: operation_type})
      |> maybe_load_associations(options)
      |> Ash.read()

      case result do
        {:ok, operation_logs} ->
          Logger.info("✅ [操作日志仓储] 类型日志获取成功: #{length(operation_logs)}条记录")
          {:ok, operation_logs}
        {:error, error} ->
          Logger.error("❌ [操作日志仓储] 类型日志获取失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [操作日志仓储] 根据操作类型获取日志异常: #{inspect(exception)}")
        {:error, :list_by_type_exception}
    end
  end

  @doc """
  根据模块获取日志

  ## 参数
  - `module` - 模块名称
  - `options` - 选项参数

  ## 返回
  - `{:ok, operation_logs}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_operation_logs_by_module(module, options \\ []) do
    Logger.info("📝 [操作日志仓储] 根据模块获取日志: #{module}")

    try do
      result = OperationLog
      |> Ash.Query.for_read(:list_by_module, %{module: module})
      |> maybe_load_associations(options)
      |> Ash.read()

      case result do
        {:ok, operation_logs} ->
          Logger.info("✅ [操作日志仓储] 模块日志获取成功: #{length(operation_logs)}条记录")
          {:ok, operation_logs}
        {:error, error} ->
          Logger.error("❌ [操作日志仓储] 模块日志获取失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [操作日志仓储] 根据模块获取日志异常: #{inspect(exception)}")
        {:error, :list_by_module_exception}
    end
  end

  @doc """
  根据日期范围获取日志

  ## 参数
  - `start_date` - 开始日期
  - `end_date` - 结束日期
  - `options` - 选项参数

  ## 返回
  - `{:ok, operation_logs}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_operation_logs_by_date_range(start_date, end_date, options \\ []) do
    Logger.info("📝 [操作日志仓储] 根据日期范围获取日志: #{start_date} - #{end_date}")

    try do
      result = OperationLog
      |> Ash.Query.for_read(:list_by_date_range, %{start_date: start_date, end_date: end_date})
      |> maybe_load_associations(options)
      |> Ash.read()

      case result do
        {:ok, operation_logs} ->
          Logger.info("✅ [操作日志仓储] 日期范围日志获取成功: #{length(operation_logs)}条记录")
          {:ok, operation_logs}
        {:error, error} ->
          Logger.error("❌ [操作日志仓储] 日期范围日志获取失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [操作日志仓储] 根据日期范围获取日志异常: #{inspect(exception)}")
        {:error, :list_by_date_range_exception}
    end
  end

  # ==================== 统计操作 ====================

  @doc """
  获取操作日志统计

  ## 参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, stats}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_operation_log_stats(options \\ []) do
    Logger.info("📝 [操作日志仓储] 获取操作日志统计")

    try do
      # 获取总数统计
      total_count_result = OperationLog |> Ash.count()
      
      # 获取今日统计
      today = Date.utc_today()
      today_start = DateTime.new!(today, ~T[00:00:00])
      today_end = DateTime.new!(today, ~T[23:59:59])
      
      today_count_result = OperationLog
      |> Ash.Query.filter(inserted_at >= ^today_start and inserted_at <= ^today_end)
      |> Ash.count()

      case {total_count_result, today_count_result} do
        {{:ok, total_count}, {:ok, today_count}} ->
          stats = %{
            total_count: total_count,
            today_count: today_count,
            generated_at: DateTime.utc_now()
          }
          Logger.info("✅ [操作日志仓储] 日志统计获取成功: 总计#{total_count}条，今日#{today_count}条")
          {:ok, stats}
        {{:error, error}, _} ->
          Logger.error("❌ [操作日志仓储] 获取总数统计失败: #{inspect(error)}")
          {:error, error}
        {_, {:error, error}} ->
          Logger.error("❌ [操作日志仓储] 获取今日统计失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [操作日志仓储] 获取日志统计异常: #{inspect(exception)}")
        {:error, :get_stats_exception}
    end
  end

  # ==================== 私有辅助函数 ====================

  # 构建列表查询
  defp build_list_query(query, params) do
    query
    |> maybe_filter_by_admin_user(params)
    |> maybe_filter_by_operation_type(params)
    |> maybe_filter_by_module(params)
    |> maybe_filter_by_ip_address(params)
    |> maybe_filter_by_date_range(params)
    |> apply_pagination(params)
    |> apply_sorting(params)
  end

  # 按管理员用户过滤
  defp maybe_filter_by_admin_user(query, %{"admin_user_id" => admin_user_id}) when is_binary(admin_user_id) and admin_user_id != "" do
    query |> Ash.Query.filter(admin_user_id == ^admin_user_id)
  end
  defp maybe_filter_by_admin_user(query, _), do: query

  # 按操作类型过滤
  defp maybe_filter_by_operation_type(query, %{"operation_type" => operation_type}) when is_binary(operation_type) and operation_type != "" do
    query |> Ash.Query.filter(operation_type == ^operation_type)
  end
  defp maybe_filter_by_operation_type(query, _), do: query

  # 按模块过滤
  defp maybe_filter_by_module(query, %{"module" => module}) when is_binary(module) and module != "" do
    query |> Ash.Query.filter(contains(module, ^module))
  end
  defp maybe_filter_by_module(query, _), do: query

  # 按IP地址过滤
  defp maybe_filter_by_ip_address(query, %{"ip_address" => ip_address}) when is_binary(ip_address) and ip_address != "" do
    query |> Ash.Query.filter(ip_address == ^ip_address)
  end
  defp maybe_filter_by_ip_address(query, _), do: query

  # 按日期范围过滤
  defp maybe_filter_by_date_range(query, %{"start_date" => start_date, "end_date" => end_date}) 
    when is_binary(start_date) and is_binary(end_date) and start_date != "" and end_date != "" do
    
    with {:ok, start_datetime} <- parse_date(start_date),
         {:ok, end_datetime} <- parse_date(end_date) do
      query |> Ash.Query.filter(inserted_at >= ^start_datetime and inserted_at <= ^end_datetime)
    else
      _ -> query
    end
  end
  defp maybe_filter_by_date_range(query, _), do: query

  # 解析日期
  defp parse_date(date_string) do
    case Date.from_iso8601(date_string) do
      {:ok, date} -> {:ok, DateTime.new!(date, ~T[00:00:00])}
      {:error, _} -> {:error, :invalid_date}
    end
  end

  # 应用分页
  defp apply_pagination(query, params) do
    page = Map.get(params, "page", 1) |> ensure_integer()
    page_size = Map.get(params, "page_size", 20) |> ensure_integer() |> min(100)
    offset = (page - 1) * page_size

    query
    |> Ash.Query.limit(page_size)
    |> Ash.Query.offset(offset)
  end

  # 应用排序
  defp apply_sorting(query, params) do
    sort_by = Map.get(params, "sort_by", "inserted_at")
    sort_order = Map.get(params, "sort_order", "desc")

    case {sort_by, sort_order} do
      {"inserted_at", "desc"} -> query |> Ash.Query.sort(inserted_at: :desc)
      {"inserted_at", "asc"} -> query |> Ash.Query.sort(inserted_at: :asc)
      {"operation_type", "asc"} -> query |> Ash.Query.sort(operation_type: :asc)
      {"operation_type", "desc"} -> query |> Ash.Query.sort(operation_type: :desc)
      {"module", "asc"} -> query |> Ash.Query.sort(module: :asc)
      {"module", "desc"} -> query |> Ash.Query.sort(module: :desc)
      _ -> query |> Ash.Query.sort(inserted_at: :desc)
    end
  end

  # 加载关联数据
  defp maybe_load_associations(query, options) do
    load_associations = Keyword.get(options, :load, [])
    if Enum.empty?(load_associations) do
      query
    else
      query |> Ash.Query.load(load_associations)
    end
  end

  # 确保整数类型
  defp ensure_integer(value) when is_integer(value), do: value
  defp ensure_integer(value) when is_binary(value) do
    case Integer.parse(value) do
      {int, _} -> int
      :error -> 1
    end
  end
  defp ensure_integer(_), do: 1
end
