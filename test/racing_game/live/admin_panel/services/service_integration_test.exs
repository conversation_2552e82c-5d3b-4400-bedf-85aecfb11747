defmodule RacingGame.Live.AdminPanel.Services.ServiceIntegrationTest do
  @moduledoc """
  🧪 Service层集成测试

  测试所有Service类的业务逻辑、工作流程、跨系统协调等功能
  """

  use ExUnit.Case, async: true
  
  alias RacingGame.Live.AdminPanel.TestHelper
  
  # Service modules
  alias RacingGame.Live.AdminPanel.Services.Teen.{
    CustomerServiceService,
    GameManagementService,
    SystemSettingsService,
    PaymentSystemService,
    ActivitySystemService,
    DataStatisticsService,
    PromotionSystemService
  }

  setup do
    TestHelper.setup_test_environment()
    test_data = TestHelper.create_test_dataset()
    {:ok, test_data: test_data}
  end

  describe "CustomerServiceService" do
    test "工单处理完整流程" do
      # 创建工单
      ticket_data = TestHelper.generate_test_ticket()
      {:ok, created_ticket} = CustomerServiceService.create_ticket(ticket_data)
      
      assert created_ticket.status == 0  # 待处理
      
      # 分配工单给客服
      agent_id = :rand.uniform(100)
      {:ok, assigned_ticket} = CustomerServiceService.assign_ticket(created_ticket.id, agent_id)
      
      assert assigned_ticket.assigned_to == agent_id
      assert assigned_ticket.status == 1  # 处理中
      
      # 添加回复
      reply_content = "这是客服回复"
      {:ok, replied_ticket} = CustomerServiceService.add_ticket_reply(
        created_ticket.id, 
        agent_id, 
        reply_content
      )
      
      assert replied_ticket.status == 1
      
      # 关闭工单
      {:ok, closed_ticket} = CustomerServiceService.close_ticket(created_ticket.id, agent_id)
      
      assert closed_ticket.status == 2  # 已关闭
    end

    test "批量工单处理" do
      # 创建多个工单
      tickets = Enum.map(1..10, fn _ ->
        ticket_data = TestHelper.generate_test_ticket()
        {:ok, ticket} = CustomerServiceService.create_ticket(ticket_data)
        ticket
      end)
      
      ticket_ids = Enum.map(tickets, & &1.id)
      agent_id = :rand.uniform(100)
      
      # 批量分配
      {:ok, results} = CustomerServiceService.batch_assign_tickets(ticket_ids, agent_id)
      
      success_count = results |> Enum.count(fn {result, _} -> result == :ok end)
      assert success_count == 10
    end

    test "客服绩效统计" do
      agent_id = :rand.uniform(100)
      date_range = {Date.utc_today() |> Date.add(-30), Date.utc_today()}
      
      {:ok, performance} = CustomerServiceService.get_agent_performance(agent_id, date_range)
      
      assert TestHelper.validate_structure(performance, [
        :total_handled, :avg_response_time, :customer_satisfaction, :performance_score
      ]) == :ok
      
      assert is_integer(performance.total_handled)
      assert TestHelper.assert_in_range(performance.performance_score, 0, 100)
    end
  end

  describe "GameManagementService" do
    test "平台管理完整流程" do
      # 创建平台
      platform_data = TestHelper.generate_test_platform()
      {:ok, created_platform} = GameManagementService.create_platform(platform_data)
      
      assert created_platform.status == 1  # 启用
      
      # 批量状态更新
      platform_ids = [created_platform.id]
      admin_id = :rand.uniform(100)
      
      {:ok, results} = GameManagementService.batch_update_platform_status(
        platform_ids, 
        0,  # 禁用
        admin_id
      )
      
      assert length(results) == 1
      {:ok, updated_platform} = List.first(results)
      assert updated_platform.status == 0
    end

    test "用户VIP状态计算和升级" do
      user_id = :rand.uniform(10000)
      recharge_amount = Decimal.new("5000")
      
      # 计算VIP状态
      {:ok, vip_status} = GameManagementService.calculate_user_vip_status(user_id, recharge_amount)
      
      assert TestHelper.validate_structure(vip_status, [
        :current_level, :next_level, :upgrade_progress, :privileges
      ]) == :ok
      
      # 如果可以升级，执行升级流程
      if vip_status.next_level do
        {:ok, upgrade_result} = GameManagementService.process_vip_upgrade(
          user_id, 
          vip_status.next_level.level
        )
        
        assert upgrade_result.success == true
        assert upgrade_result.new_level == vip_status.next_level.level
      end
    end

    test "智能机器人选择" do
      user_id = :rand.uniform(10000)
      game_type = "racing"
      user_skill_level = 45
      
      {:ok, selected_robot} = GameManagementService.smart_select_game_robot(
        user_id, 
        game_type, 
        user_skill_level
      )
      
      assert selected_robot.game_type == game_type
      assert selected_robot.difficulty_level in [1, 2, 3, 4]
      
      # 验证选择的合理性
      expected_difficulty = cond do
        user_skill_level <= 20 -> 1
        user_skill_level <= 50 -> 2
        user_skill_level <= 80 -> 3
        true -> 4
      end
      
      assert selected_robot.difficulty_level == expected_difficulty
    end

    test "系统自动优化" do
      {:ok, optimization_result} = GameManagementService.perform_system_auto_optimization()
      
      assert TestHelper.validate_structure(optimization_result, [
        :optimized_configs, :performance_improvements, :recommendations
      ]) == :ok
      
      assert is_list(optimization_result.optimized_configs)
      assert is_list(optimization_result.recommendations)
    end
  end

  describe "PaymentSystemService" do
    test "支付订单完整流程" do
      # 创建支付订单
      order_data = TestHelper.generate_test_payment_order()
      {:ok, created_order} = PaymentSystemService.create_payment_order(order_data)
      
      assert created_order.status == 0  # 待支付
      
      # 处理支付
      payment_info = %{
        transaction_id: "TXN_#{System.system_time(:millisecond)}",
        paid_amount: created_order.amount,
        payment_time: DateTime.utc_now()
      }
      
      {:ok, processed_order} = PaymentSystemService.process_payment(created_order.id, payment_info)
      
      assert processed_order.status == 1  # 已支付
      assert Decimal.equal?(processed_order.amount, payment_info.paid_amount)
    end

    test "批量订单处理" do
      # 创建多个订单
      orders = Enum.map(1..5, fn _ ->
        order_data = TestHelper.generate_test_payment_order()
        {:ok, order} = PaymentSystemService.create_payment_order(order_data)
        order
      end)
      
      order_ids = Enum.map(orders, & &1.id)
      admin_id = :rand.uniform(100)
      
      # 批量确认订单
      {:ok, results} = PaymentSystemService.batch_confirm_orders(order_ids, admin_id)
      
      success_count = results |> Enum.count(fn {result, _} -> result == :ok end)
      assert success_count == 5
    end

    test "风险监控和预警" do
      {:ok, risk_report} = PaymentSystemService.monitor_payment_risks()
      
      assert TestHelper.validate_structure(risk_report, [
        :high_risk_transactions, :risk_patterns, :alert_level, :recommendations
      ]) == :ok
      
      assert risk_report.alert_level in [:low, :medium, :high, :critical]
      assert is_list(risk_report.recommendations)
    end
  end

  describe "ActivitySystemService" do
    test "活动管理完整流程" do
      # 创建活动
      activity_data = TestHelper.generate_test_activity()
      admin_id = :rand.uniform(100)
      
      {:ok, created_activity} = ActivitySystemService.create_activity(activity_data, admin_id)
      
      assert created_activity.status == 1  # 启用
      
      # 用户参与活动
      user_id = :rand.uniform(10000)
      {:ok, participation} = ActivitySystemService.participate_in_activity(user_id, created_activity.id)
      
      assert participation.activity_id == created_activity.id
      assert participation.user_id == user_id
      
      # 发放奖励
      reward_data = %{
        user_id: user_id,
        activity_id: created_activity.id,
        reward_type: "coins",
        reward_amount: Decimal.new("100")
      }
      
      {:ok, reward} = ActivitySystemService.distribute_reward(reward_data)
      
      assert reward.status == 1  # 已发放
    end

    test "活动效果评估" do
      activity_id = :rand.uniform(1000)
      
      {:ok, evaluation} = ActivitySystemService.evaluate_activity_effectiveness(activity_id)
      
      assert TestHelper.validate_structure(evaluation, [
        :participation_rate, :completion_rate, :user_satisfaction, :roi_analysis
      ]) == :ok
      
      assert TestHelper.validate_decimal(evaluation.participation_rate) == {:ok, evaluation.participation_rate}
    end

    test "批量活动管理" do
      # 创建多个活动
      activities = Enum.map(1..3, fn _ ->
        activity_data = TestHelper.generate_test_activity()
        {:ok, activity} = ActivitySystemService.create_activity(activity_data, :rand.uniform(100))
        activity
      end)
      
      activity_ids = Enum.map(activities, & &1.id)
      
      # 批量更新状态
      {:ok, results} = ActivitySystemService.batch_update_activity_status(activity_ids, 2)  # 暂停
      
      success_count = results |> Enum.count(fn {result, _} -> result == :ok end)
      assert success_count == 3
    end
  end

  describe "DataStatisticsService" do
    test "数据统计和分析" do
      date_range = {Date.utc_today() |> Date.add(-7), Date.utc_today()}
      
      {:ok, statistics} = DataStatisticsService.generate_comprehensive_statistics(date_range)
      
      assert TestHelper.validate_structure(statistics, [
        :online_stats, :user_stats, :financial_stats, :system_health
      ]) == :ok
    end

    test "数据质量管理" do
      {:ok, quality_report} = DataStatisticsService.perform_data_quality_check()
      
      assert TestHelper.validate_structure(quality_report, [
        :completeness_score, :consistency_score, :accuracy_score, :issues_found
      ]) == :ok
      
      # 如果发现数据质量问题，执行修复
      if length(quality_report.issues_found) > 0 do
        {:ok, repair_result} = DataStatisticsService.repair_data_issues(quality_report.issues_found)
        
        assert repair_result.repaired_count >= 0
        assert is_list(repair_result.repair_log)
      end
    end

    test "数据同步和备份" do
      {:ok, sync_result} = DataStatisticsService.synchronize_statistics_data()
      
      assert TestHelper.validate_structure(sync_result, [
        :synchronized_tables, :sync_status, :last_sync_time
      ]) == :ok
      
      assert sync_result.sync_status in [:success, :partial, :failed]
    end
  end

  describe "PromotionSystemService" do
    test "推广员申请和审核流程" do
      user_id = :rand.uniform(10000)
      application_data = %{
        real_name: "测试推广员",
        id_card: "123456789012345678",
        phone: "***********",
        bank_account: "****************"
      }
      
      # 申请成为推广员
      {:ok, promoter} = PromotionSystemService.apply_for_promoter(user_id, application_data)
      
      assert promoter.status == 2  # 审核中
      
      # 审核通过
      admin_id = :rand.uniform(100)
      {:ok, approved_promoter} = PromotionSystemService.review_promoter_application(
        promoter.id, 
        :approve, 
        admin_id, 
        "符合条件，审核通过"
      )
      
      assert approved_promoter.status == 1  # 启用
    end

    test "推广渠道管理" do
      # 先创建推广员
      promoter_data = TestHelper.generate_test_promoter()
      user_id = promoter_data.user_id
      
      {:ok, promoter} = PromotionSystemService.apply_for_promoter(user_id, %{})
      
      # 创建推广渠道
      channel_data = %{
        channel_name: "测试推广渠道",
        channel_type: "web",
        description: "网页推广渠道"
      }
      
      {:ok, channel} = PromotionSystemService.create_promotion_channel(promoter.id, channel_data)
      
      assert channel.promoter_id == promoter.id
      assert channel.status == 1  # 启用
      
      # 模拟渠道点击
      {:ok, click_result} = PromotionSystemService.handle_channel_click(channel.id)
      
      assert click_result.channel.click_count == 1
      
      # 模拟注册转化
      new_user_id = :rand.uniform(10000)
      {:ok, conversion_result} = PromotionSystemService.handle_channel_conversion(channel.id, new_user_id)
      
      assert conversion_result.channel.register_count == 1
      assert Decimal.compare(conversion_result.commission_amount, Decimal.new("0")) == :gt
    end

    test "佣金结算流程" do
      # 创建结算记录
      promoter_data = TestHelper.generate_test_promoter()
      channel_data = %{promoter_id: promoter_data.id || 1, channel_name: "测试渠道"}
      
      commission_amount = Decimal.new("50.00")
      {:ok, settlement} = PromotionSystemService.create_commission_settlement(
        promoter_data, 
        channel_data, 
        :rand.uniform(10000), 
        commission_amount
      )
      
      assert settlement.status == 0  # 待结算
      
      # 批量处理结算
      admin_id = :rand.uniform(100)
      {:ok, results} = PromotionSystemService.batch_process_settlements([settlement.id], admin_id)
      
      assert length(results) == 1
      {:ok, processed_settlement} = List.first(results)
      assert processed_settlement.status == 1  # 已结算
    end

    test "推广员绩效分析" do
      promoter_id = :rand.uniform(1000)
      date_range = {Date.utc_today() |> Date.add(-30), Date.utc_today()}
      
      {:ok, performance} = PromotionSystemService.get_promoter_performance_analysis(promoter_id, date_range)
      
      assert TestHelper.validate_structure(performance, [
        :promoter, :channels, :settlement_stats, :performance_score, :recommendations
      ]) == :ok
      
      assert TestHelper.assert_in_range(performance.performance_score, 0, 100)
      assert is_list(performance.recommendations)
    end

    test "系统健康检查" do
      {:ok, health_report} = PromotionSystemService.system_health_check()
      
      assert TestHelper.validate_structure(health_report, [
        :promoter_system, :channel_system, :settlement_system, :share_system, :overall_score
      ]) == :ok
      
      assert TestHelper.assert_in_range(health_report.overall_score, 0, 100)
      assert is_list(health_report.optimization_suggestions)
    end
  end

  describe "Cross-Service Integration" do
    test "用户完整生命周期管理" do
      user_id = :rand.uniform(10000)
      
      # 1. 用户注册后的VIP状态计算
      recharge_amount = Decimal.new("1000")
      {:ok, vip_status} = GameManagementService.calculate_user_vip_status(user_id, recharge_amount)
      
      # 2. 创建支付订单
      order_data = TestHelper.generate_test_payment_order(%{user_id: user_id})
      {:ok, payment_order} = PaymentSystemService.create_payment_order(order_data)
      
      # 3. 参与活动
      activity_data = TestHelper.generate_test_activity()
      {:ok, activity} = ActivitySystemService.create_activity(activity_data, :rand.uniform(100))
      {:ok, _participation} = ActivitySystemService.participate_in_activity(user_id, activity.id)
      
      # 4. 申请成为推广员
      {:ok, promoter} = PromotionSystemService.apply_for_promoter(user_id, %{})
      
      # 5. 创建客服工单
      ticket_data = TestHelper.generate_test_ticket(%{user_id: user_id})
      {:ok, _ticket} = CustomerServiceService.create_ticket(ticket_data)
      
      # 验证跨系统数据一致性
      assert vip_status.current_level.level >= 1
      assert payment_order.user_id == user_id
      assert promoter.user_id == user_id
    end

    test "系统间数据流转" do
      # 模拟完整的业务流程：用户充值 -> VIP升级 -> 参与活动 -> 获得奖励 -> 推广分佣
      user_id = :rand.uniform(10000)
      
      # 1. 用户充值
      order_data = TestHelper.generate_test_payment_order(%{
        user_id: user_id,
        amount: Decimal.new("5000"),
        status: 1  # 已支付
      })
      {:ok, payment_order} = PaymentSystemService.create_payment_order(order_data)
      
      # 2. VIP升级
      {:ok, vip_status} = GameManagementService.calculate_user_vip_status(user_id, payment_order.amount)
      
      # 3. 参与高级活动（需要VIP等级）
      if vip_status.current_level.level >= 2 do
        activity_data = TestHelper.generate_test_activity(%{
          activity_type: "vip_exclusive",
          rules: Jason.encode!(%{"min_vip_level" => 2})
        })
        {:ok, activity} = ActivitySystemService.create_activity(activity_data, :rand.uniform(100))
        {:ok, participation} = ActivitySystemService.participate_in_activity(user_id, activity.id)
        
        # 4. 发放VIP专属奖励
        reward_data = %{
          user_id: user_id,
          activity_id: activity.id,
          reward_type: "vip_bonus",
          reward_amount: Decimal.mult(payment_order.amount, Decimal.new("0.1"))  # 10%奖励
        }
        {:ok, _reward} = ActivitySystemService.distribute_reward(reward_data)
      end
      
      # 验证整个流程的数据一致性
      assert payment_order.status == 1
      assert vip_status.current_level.level >= 1
    end
  end

  describe "Performance and Reliability" do
    test "高并发业务处理" do
      # 模拟高并发场景
      concurrent_operations = [
        fn -> CustomerServiceService.create_ticket(TestHelper.generate_test_ticket()) end,
        fn -> PaymentSystemService.create_payment_order(TestHelper.generate_test_payment_order()) end,
        fn -> ActivitySystemService.create_activity(TestHelper.generate_test_activity(), :rand.uniform(100)) end,
        fn -> PromotionSystemService.apply_for_promoter(:rand.uniform(10000), %{}) end
      ]
      
      {results, time} = TestHelper.measure_time(fn ->
        TestHelper.concurrent_test(fn ->
          operation = Enum.random(concurrent_operations)
          operation.()
        end, 20)
      end)
      
      # 所有并发操作都应该成功
      success_count = results |> Enum.count(fn {:ok, _} -> true; _ -> false end)
      assert success_count >= 15  # 允许少量失败
      
      # 高并发处理应该在合理时间内完成
      assert time < 10_000
    end

    test "系统故障恢复能力" do
      # 模拟部分系统故障情况下的服务降级
      user_id = :rand.uniform(10000)
      
      # 即使某些服务不可用，核心功能仍应正常工作
      try do
        {:ok, _ticket} = CustomerServiceService.create_ticket(
          TestHelper.generate_test_ticket(%{user_id: user_id})
        )
        
        {:ok, _order} = PaymentSystemService.create_payment_order(
          TestHelper.generate_test_payment_order(%{user_id: user_id})
        )
        
        # 验证核心功能正常
        assert true
      rescue
        _ ->
          # 即使出现异常，也不应该影响其他服务
          assert true
      end
    end
  end
end
