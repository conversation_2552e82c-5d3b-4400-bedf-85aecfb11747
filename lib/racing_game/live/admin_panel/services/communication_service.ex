defmodule RacingGame.Live.AdminPanel.Services.CommunicationService do
  @moduledoc """
  系统通信管理服务
  
  提供系统通信相关的业务逻辑：
  - 通信创建和管理
  - 消息发送和通知
  - 通信状态管理
  - 业务规则验证
  """

  require Logger
  alias RacingGame.Live.AdminPanel.Repositories.{RepositoryManager, CommunicationRepository}
  alias RacingGame.SystemCommunication
  alias RacingGame.Live.AdminPanel.Utils.{ValidationHelper, NotificationHelper}

  # 常量定义
  @valid_types [:message, :announcement, :notification]
  @valid_priorities [:low, :medium, :high, :urgent]
  @valid_recipient_types [:all, :user, :admin]
  @max_title_length 200
  @max_content_length 5000

  # ============================================================================
  # 通信创建和管理
  # ============================================================================

  @doc """
  创建系统通信

  ## 参数
  - `params` - 通信参数
    - `:type` - 通信类型
    - `:title` - 标题
    - `:content` - 内容
    - `:recipient_type` - 接收者类型
    - `:recipient_id` - 接收者ID（可选）
    - `:priority` - 优先级
    - `:expires_at` - 过期时间（可选）
  - `creator_id` - 创建者ID

  ## 返回
  - `{:ok, communication}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_communication(params, creator_id) do
    Logger.info("📝 [通信服务] 创建系统通信: #{params[:type]} - #{params[:title]}")
    
    with {:ok, validated_params} <- validate_communication_params(params),
         {:ok, enriched_params} <- enrich_communication_params(validated_params, creator_id),
         {:ok, communication} <- create_communication_record(enriched_params),
         :ok <- send_notifications(communication) do
      
      Logger.info("✅ [通信服务] 通信创建成功: #{communication.id}")
      {:ok, communication}
    else
      {:error, reason} ->
        Logger.error("❌ [通信服务] 通信创建失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  更新系统通信

  ## 参数
  - `communication_id` - 通信ID
  - `params` - 更新参数
  - `updater_id` - 更新者ID

  ## 返回
  - `{:ok, communication}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_communication(communication_id, params, updater_id) do
    Logger.info("✏️ [通信服务] 更新系统通信: #{communication_id}")
    
    with {:ok, communication} <- get_communication_for_update(communication_id),
         {:ok, validated_params} <- validate_update_params(params),
         {:ok, updated_communication} <- update_communication_record(communication, validated_params),
         :ok <- handle_update_notifications(updated_communication, params) do
      
      Logger.info("✅ [通信服务] 通信更新成功: #{communication_id}")
      clear_communication_cache(communication_id)
      {:ok, updated_communication}
    else
      {:error, reason} ->
        Logger.error("❌ [通信服务] 通信更新失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  删除系统通信

  ## 参数
  - `communication_id` - 通信ID
  - `deleter_id` - 删除者ID

  ## 返回
  - `:ok` - 成功
  - `{:error, reason}` - 失败
  """
  def delete_communication(communication_id, deleter_id) do
    Logger.info("🗑️ [通信服务] 删除系统通信: #{communication_id}")
    
    with {:ok, communication} <- get_communication_for_delete(communication_id),
         :ok <- validate_delete_permission(communication, deleter_id),
         {:ok, _} <- delete_communication_record(communication) do
      
      Logger.info("✅ [通信服务] 通信删除成功: #{communication_id}")
      clear_communication_cache(communication_id)
      :ok
    else
      {:error, reason} ->
        Logger.error("❌ [通信服务] 通信删除失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  # ============================================================================
  # 通信查询
  # ============================================================================

  @doc """
  获取通信详情

  ## 参数
  - `communication_id` - 通信ID
  - `options` - 选项

  ## 返回
  - `{:ok, communication}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_communication(communication_id, options \\ []) do
    Logger.debug("🔍 [通信服务] 获取通信详情: #{communication_id}")
    
    CommunicationRepository.get_by_id(communication_id, options)
  end

  @doc """
  分页查询通信列表

  ## 参数
  - `params` - 查询参数
  - `options` - 选项

  ## 返回
  - `{:ok, %{communications: communications, page_info: page_info}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_communications(params \\ %{}, options \\ []) do
    Logger.debug("📋 [通信服务] 分页查询通信列表")
    
    # 应用业务过滤规则
    filtered_params = apply_business_filters(params, options)
    
    CommunicationRepository.list_paginated(filtered_params)
  end

  @doc """
  获取用户相关通信

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - `{:ok, communications}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_communications(user_id, options \\ []) do
    Logger.debug("👤 [通信服务] 获取用户通信: #{user_id}")
    
    CommunicationRepository.get_user_communications(user_id, options)
  end

  @doc """
  获取活跃通信

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, communications}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_active_communications(options \\ []) do
    Logger.debug("📢 [通信服务] 获取活跃通信")
    
    CommunicationRepository.list_active(options)
  end

  # ============================================================================
  # 通信状态管理
  # ============================================================================

  @doc """
  激活通信

  ## 参数
  - `communication_id` - 通信ID
  - `operator_id` - 操作者ID

  ## 返回
  - `{:ok, communication}` - 成功
  - `{:error, reason}` - 失败
  """
  def activate_communication(communication_id, operator_id) do
    Logger.info("🟢 [通信服务] 激活通信: #{communication_id}")
    
    update_communication_status(communication_id, true, operator_id)
  end

  @doc """
  停用通信

  ## 参数
  - `communication_id` - 通信ID
  - `operator_id` - 操作者ID

  ## 返回
  - `{:ok, communication}` - 成功
  - `{:error, reason}` - 失败
  """
  def deactivate_communication(communication_id, operator_id) do
    Logger.info("🔴 [通信服务] 停用通信: #{communication_id}")
    
    update_communication_status(communication_id, false, operator_id)
  end

  @doc """
  批量更新通信状态

  ## 参数
  - `communication_ids` - 通信ID列表
  - `active` - 状态
  - `operator_id` - 操作者ID

  ## 返回
  - `{:ok, results}` - 成功
  - `{:error, reason}` - 失败
  """
  def batch_update_status(communication_ids, active, operator_id) do
    Logger.info("📦 [通信服务] 批量更新状态: #{length(communication_ids)}个通信")
    
    operations = Enum.map(communication_ids, fn id ->
      {:update_communication, [id, %{active: active}]}
    end)
    
    case RepositoryManager.batch_execute(:communication, operations) do
      {:ok, results} ->
        # 清除相关缓存
        Enum.each(communication_ids, &clear_communication_cache/1)
        {:ok, results}
      error -> error
    end
  end

  # ============================================================================
  # 统计和分析
  # ============================================================================

  @doc """
  获取通信统计信息

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, statistics}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_statistics(options \\ []) do
    Logger.debug("📊 [通信服务] 获取通信统计")
    
    with {:ok, basic_stats} <- CommunicationRepository.get_statistics(options),
         {:ok, business_stats} <- calculate_business_statistics(options) do
      
      combined_stats = Map.merge(basic_stats, business_stats)
      {:ok, combined_stats}
    else
      error -> error
    end
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 验证通信参数
  defp validate_communication_params(params) do
    with :ok <- ValidationHelper.validate_required(params, [:type, :title, :content]),
         :ok <- validate_communication_type(params[:type]),
         :ok <- validate_title_length(params[:title]),
         :ok <- validate_content_length(params[:content]),
         :ok <- validate_priority(params[:priority]),
         :ok <- validate_recipient_type(params[:recipient_type]) do
      {:ok, params}
    else
      error -> error
    end
  end

  # 验证通信类型
  defp validate_communication_type(type) when type in @valid_types, do: :ok
  defp validate_communication_type(_), do: {:error, "无效的通信类型"}

  # 验证标题长度
  defp validate_title_length(title) when is_binary(title) do
    if String.length(title) <= @max_title_length do
      :ok
    else
      {:error, "标题长度不能超过#{@max_title_length}字符"}
    end
  end
  defp validate_title_length(_), do: {:error, "标题必须是字符串"}

  # 验证内容长度
  defp validate_content_length(content) when is_binary(content) do
    if String.length(content) <= @max_content_length do
      :ok
    else
      {:error, "内容长度不能超过#{@max_content_length}字符"}
    end
  end
  defp validate_content_length(_), do: {:error, "内容必须是字符串"}

  # 验证优先级
  defp validate_priority(nil), do: :ok
  defp validate_priority(priority) when priority in @valid_priorities, do: :ok
  defp validate_priority(_), do: {:error, "无效的优先级"}

  # 验证接收者类型
  defp validate_recipient_type(nil), do: :ok
  defp validate_recipient_type(type) when type in @valid_recipient_types, do: :ok
  defp validate_recipient_type(_), do: {:error, "无效的接收者类型"}

  # 丰富通信参数
  defp enrich_communication_params(params, creator_id) do
    enriched = params
    |> Map.put(:created_by, creator_id)
    |> Map.put_new(:priority, :medium)
    |> Map.put_new(:recipient_type, :all)
    |> Map.put_new(:active, true)
    
    {:ok, enriched}
  end

  # 创建通信记录
  defp create_communication_record(params) do
    case SystemCommunication.create(params) do
      {:ok, communication} -> {:ok, communication}
      {:error, error} -> {:error, "创建通信失败: #{inspect(error)}"}
    end
  end

  # 发送通知
  defp send_notifications(communication) do
    case communication.recipient_type do
      :all -> NotificationHelper.broadcast_to_all(communication)
      :user -> NotificationHelper.send_to_user(communication.recipient_id, communication)
      :admin -> NotificationHelper.broadcast_to_admins(communication)
    end
  end

  # 其他私有函数的简化实现
  defp validate_update_params(params), do: {:ok, params}
  defp get_communication_for_update(id), do: CommunicationRepository.get_by_id(id)
  defp get_communication_for_delete(id), do: CommunicationRepository.get_by_id(id)
  defp validate_delete_permission(_communication, _deleter_id), do: :ok
  defp update_communication_record(communication, params) do
    case SystemCommunication.update(communication, params) do
      {:ok, updated} -> {:ok, updated}
      error -> error
    end
  end
  defp delete_communication_record(communication) do
    case SystemCommunication.destroy(communication) do
      :ok -> {:ok, communication}
      error -> error
    end
  end
  defp handle_update_notifications(_communication, _params), do: :ok
  defp apply_business_filters(params, _options), do: params
  defp update_communication_status(id, status, _operator_id) do
    case get_communication(id) do
      {:ok, communication} ->
        update_communication_record(communication, %{active: status})
      error -> error
    end
  end
  defp calculate_business_statistics(_options), do: {:ok, %{}}
  defp clear_communication_cache(id), do: CommunicationRepository.clear_cache(id)
end
