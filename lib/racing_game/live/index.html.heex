<div class="racing-assistant">
  <!-- 添加Flash消息组件 -->
  <div class="flash-messages" id="flash-messages" phx-hook="FlashMessages">
    <%= if flash = Phoenix.Flash.get(@flash, :info) do %>
      <div class="flash-message info" phx-click="lv:clear-flash" phx-value-key="info">
        <div class="flash-content">
          <i class="fas fa-info-circle"></i> {flash}
        </div>
        <button class="flash-close">&times;</button>
      </div>
    <% end %>

    <%= if flash = Phoenix.Flash.get(@flash, :error) do %>
      <div class="flash-message error" phx-click="lv:clear-flash" phx-value-key="error">
        <div class="flash-content">
          <i class="fas fa-exclamation-circle"></i> {flash}
        </div>
        <button class="flash-close">&times;</button>
      </div>
    <% end %>
  </div>

  <div class="racing-main-content">
    <div class="iframe-container" id="iframe-container" phx-update="ignore">
      <iframe src={@racing_game_url} title="动物运动会"></iframe>
      <%!-- <iframe src={~p"/racing_game"} title="动物运动会"></iframe> --%>
    </div>
    
<!-- 顶部信息区域 -->
    <div class="top-info-container">
      <!-- 左侧账户信息 -->
      <%= if @current_user do %>
        <div class="account-info">
          <i class="fas fa-user-circle"></i>
          账户:
          <span class="account-name">
            {@current_user.username || @current_user.display_id}
          </span>
        </div>
      <% end %>
      
<!-- 右侧积分信息 -->
      <div class="points-info">
        <i class="fas fa-coins"></i>
        积分:
        <span>
          {@points}
        </span>
        <%= if @current_user do %>
          <.live_component
            module={CypridinaWeb.Components.PointsHistoryComponent}
            id="main-points-history"
            user_id={@current_user.id}
            user_info={%{
              username: @current_user.username,
              numeric_id: @current_user.numeric_id,
              current_points: @points
            }}
          />
        <% end %>
      </div>
    </div>
  </div>
  <!-- 下注历史弹窗 -->
  <%= if @show_bet_history do %>
    <div class="bet-history-modal">
      <div class="modal-content">
        <h2>下注记录</h2>

        <div class="bet-list">
          <%= if Enum.empty?(@bet_history) do %>
            <p class="no-bets">暂无下注记录</p>
          <% else %>
            <table>
              <thead>
                <tr>
                  <th>期号</th>

                  <th>选手</th>

                  <th>金额</th>

                  <th>状态</th>

                  <th>收益</th>
                </tr>
              </thead>

              <tbody>
                <%= for bet <- @bet_history do %>
                  <tr>
                    <td>
                      {bet.race_issue}
                    </td>

                    <td>
                      {bet.selection}
                    </td>

                    <td>
                      {bet.amount}
                    </td>

                    <td>
                      <%= case bet.status do %>
                        <% 0 -> %>
                          <span class="pending">待开奖</span>
                        <% 1 -> %>
                          <span class="win">已中奖</span>
                        <% 2 -> %>
                          <span class="lose">未中奖</span>
                      <% end %>
                    </td>

                    <td>
                      {bet.payout}
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          <% end %>
        </div>
        <button class="close-btn" phx-click="toggle_bet_history">关闭</button>
      </div>
    </div>
  <% end %>

  <div class="champion-selection">
    <div class="animal-grid">
      <%= for animal <- @animals do %>
        <div class="animal-card">
          <!-- 信息展示区域 -->
          <div class="animal-display-area">
            <img
              src={"/images/#{animal.name}.png"}
              class="animal-avatar"
              alt={"#{animal.name}头像"}
            />
            <div class="animal-name">
              {animal.name}
            </div>

            <div class="animal-bet-amount">
              <span class="bet-label">身价:</span>
              <span class="bet-value">
                {@current_race.betMap[animal.id]}
              </span>
            </div>

            <%= if Map.get(@my_bets, animal.id, 0)> 0 do %>
              <div class="my-bet-amount">
                <span class="my-bet-label">我的投注:</span>
                <span class="my-bet-value">
                  {Map.get(@my_bets, animal.id, 0)}
                </span>
              </div>
            <% end %>

            <div class="my-stock-amount">
              <span class="my-stock-label">我的持仓:</span>
              <span class="my-stock-value">
                {Map.get(@my_stocks, animal.id, 0)}
              </span>
            </div>
          </div>

          <div class="stock-actions">
            <button
              class={"stock-btn buy #{unless @betting_enabled, do: ~c(disabled)}"}
              phx-click="show_action_modal"
              phx-value-action="buy"
              phx-value-animal={animal.id}
              disabled={!@betting_enabled}
            >
              <i class="fas fa-plus-circle"></i> 买入
            </button>

            <button
              class={"stock-btn sell #{unless @betting_enabled, do: ~c(disabled)}"}
              phx-click="show_action_modal"
              phx-value-action="sell"
              phx-value-animal={animal.id}
              disabled={!@betting_enabled || Map.get(@my_stocks, animal.id, 0) <= 0}
            >
              <i class="fas fa-minus-circle"></i> 卖出
            </button>

            <button
              class={"stock-btn bet #{unless @betting_enabled, do: ~c(disabled)}"}
              phx-click="show_action_modal"
              phx-value-action="bet"
              phx-value-animal={animal.id}
              disabled={!@betting_enabled}
            >
              <i class="fas fa-trophy"></i> 竞猜
            </button>
          </div>
        </div>
      <% end %>
    </div>
    <%!-- <div class="racing-footer"> --%> <%!-- 移除了竞猜按钮 --%> <%!-- </div> --%>
    
<!-- 选手名次走势图 -->
    <.ranking_chart
      id="ranking-chart"
      races={@recent_races}
      animals={@animals}
      title="选手身价走势图 (最近10场)"
    />
    
<!-- 底部操作按钮区域 -->
    <div class="bottom-actions">
      <button class="bottom-btn logout-btn" phx-click="logout">
        <i class="fas fa-sign-out-alt"></i>
        注销
      </button>
      <button class="bottom-btn liquidate-btn" phx-click="show_liquidate_modal" disabled={!@betting_enabled}>
        <i class="fas fa-fire"></i>
        清仓
      </button>
      <button class="bottom-btn admin-btn" phx-click="to_admin">
        <i class="fas fa-cog"></i> 管理后台
      </button>
    </div>
  </div>
  
<!-- 操作弹窗 (买入/卖出/竞猜) -->
  <%= if @show_action_modal do %>
    <div class="action-modal">
      <div class="modal-content">
        <h2>
          <%= case @current_action do %>
            <% "buy" -> %>
              买入 <span class="highlighted">{@current_animal_name}</span>
            <% "sell" -> %>
              卖出 <span class="highlighted">{@current_animal_name}</span>
            <% "bet" -> %>
              竞猜 <span class="highlighted">{@current_animal_name}</span>
          <% end %>
        </h2>

        <div class="modal-body">
          <div class="amount-selector">
            <label for="action-amount">
              <%= case @current_action do %>
                <% "bet" -> %>
                  选择下注积分<i class="fas fa-hand-point-down" style="margin-left: 5px;"></i>
                <% _ -> %>
                  选择数量<i class="fas fa-hand-point-down" style="margin-left: 5px;"></i>
              <% end %>
            </label>

            <div class="amount-controls" id="amount-controls">
              <button class="amount-btn decrease" phx-click="decrease_amount"
                      title={if @current_action == "bet", do: "减少100积分", else: "减少数量"}>
                -
              </button>

              <form phx-change="update_action_amount" phx-submit="prevent_default">
                <input
                  type="number"
                  id="action-amount"
                  name="action-amount"
                  min="1"
                  max="10000"
                  step="1"
                  value={@action_amount}
                  phx-debounce="blur"
                  inputmode="numeric"
                  pattern="[0-9]*"
                  title={if @current_action == "bet", do: "点击输入或使用按钮调整积分", else: "点击输入或使用按钮调整数量"}
                />
              </form>

              <button class="amount-btn increase" phx-click="increase_amount"
                      title={if @current_action == "bet", do: "增加100积分", else: "增加数量"}>
                +
              </button>
            </div>

            <div
              class="amount-hint"
              style="text-align: center; margin-top: 8px; font-size: 12px; color: #888;"
            >
              <%= case @current_action do %>
                <% "bet" -> %>
                  点击按钮或直接输入修改积分
                <% _ -> %>
                  点击按钮或直接输入修改数量
              <% end %>
            </div>
          </div>

          <div class="action-summary">
            <%= case @current_action do %>
              <% "buy" -> %>
                <p>
                  购买 <strong>{@action_amount}</strong>
                  份 <strong>{@current_animal_name}</strong>
                  股份
                </p>

                <p>总花费: <strong>{@action_amount * @current_price}</strong> 积分</p>
              <% "sell" -> %>
                <p>
                  卖出 <strong>{@action_amount}</strong>
                  份 <strong>{@current_animal_name}</strong>
                  股份
                </p>

                <p>总收益: <strong>{@action_amount * @current_price}</strong> 积分</p>
              <% "bet" -> %>
                <p>
                  下注 <strong>{@action_amount}</strong>
                  积分到 <strong>{@current_animal_name}</strong>
                  竞猜冠军
                </p>

                <p>下注金额: <strong>{@action_amount}</strong> 积分</p>

                <p>
                  可能收益: <strong>{floor(@action_amount * 6 * 0.987)}</strong> 积分
                </p>

                <p style="font-size: 12px; color: #888; margin-top: 10px;">
                  * 中奖可获得6倍奖金，扣除代理抽水后约为 {Float.round(6 * 0.987, 2)} 倍
                </p>
            <% end %>
          </div>
        </div>

        <div class="modal-actions">
          <button class="cancel-btn" phx-click="cancel_action">取消</button>
          <button
            class="confirm-btn"
            phx-click="confirm_action"
            phx-value-action={@current_action}
            phx-value-animal={@current_animal_id}
            phx-value-amount={@action_amount}
          >
            确认
          </button>
        </div>
      </div>
    </div>
  <% end %>

  <!-- 清仓确认弹窗 -->
  <%= if @show_liquidate_modal do %>
    <div class="action-modal">
      <div class="modal-content">
        <h2>
          <i class="fas fa-exclamation-triangle" style="color: #ff6b6b; margin-right: 8px;"></i>
          确认清仓所有股票
        </h2>

        <div class="modal-body">
          <div class="liquidate-warning">
            <p><strong>警告：</strong>此操作将卖出您持有的所有股票！</p>
            <p>您当前持有的股票：</p>
            <ul class="stock-list">
              <%= for {animal_id, quantity} <- @my_stocks do %>
                <%= if quantity > 0 do %>
                  <li>
                    <%= Enum.find(@animals, fn a -> a.id == animal_id end).name %>:
                    <strong><%= quantity %></strong> 份
                    (当前价值: <strong><%= @current_race.betMap[animal_id] * quantity %></strong> 积分)
                  </li>
                <% end %>
              <% end %>
            </ul>
            <p class="total-value">
              预计获得总积分: <strong><%= calculate_total_liquidation_value(@my_stocks, @current_race.betMap) %></strong>
            </p>
            <p class="warning-text">
              <i class="fas fa-info-circle"></i>
              此操作不可撤销，请谨慎操作！
            </p>
          </div>
        </div>

        <div class="modal-actions">
          <button class="btn btn-secondary" phx-click="hide_liquidate_modal">
            <i class="fas fa-times"></i> 取消
          </button>
          <button class="btn btn-danger" phx-click="confirm_liquidate">
            <i class="fas fa-fire"></i> 确认清仓
          </button>
        </div>
      </div>
    </div>
  <% end %>

  <%!-- <div class="history-container">
    <div class="alltitle">
      <div class="title">
        <img src="/images/player-DxPp290A.png" alt="往期数据" />
        <span>往期数据</span>
      </div>
      <div class="date-picker-container">
        <a href="#" phx-click="select_date" phx-value-date="today">今天</a>
        <a href="#" phx-click="select_date" phx-value-date="yesterday">昨天</a>
        <div class="block">
          <div class="block">
            <input type="date" phx-change="change_date" value={@selected_date} />
          </div>
        </div>
      </div>
    </div>
    <div class="playnum">
      <div class="title-with-line">
        <h2 class="title">参赛动物队列号</h2>
      </div>
      <div class="playerimg">
        <div class="image-gallery">
          <img src="/images/饿小宝.png" loading="lazy" alt="饿小宝" />
          <div class="playertitle">
            <a href="#" class="one">1</a>
            <span>饿小宝</span>
          </div>
        </div>
        <div class="image-gallery">
          <img src="/images/盒马.png" loading="lazy" alt="盒马" />
          <div class="playertitle">
            <a href="#" class="two">2</a>
            <span>盒 马</span>
          </div>
        </div>
        <div class="image-gallery">
          <img src="/images/票票.png" loading="lazy" alt="票票" />
          <div class="playertitle">
            <a href="#" class="three">3</a>
            <span>票 票</span>
          </div>
        </div>
        <div class="image-gallery">
          <img src="/images/虾仔.png" loading="lazy" alt="虾仔" />
          <div class="playertitle">
            <a href="#" class="four">4</a>
            <span>虾 仔</span>
          </div>
        </div>
        <div class="image-gallery">
          <img src="/images/支小宝.png" loading="lazy" alt="支小宝" />
          <div class="playertitle">
            <a href="#" class="five">5</a>
            <span>支小宝</span>
          </div>
        </div>
        <div class="image-gallery">
          <img src="/images/欢猩.png" loading="lazy" alt="欢猩" />
          <div class="playertitle">
            <a href="#" class="six">6</a>
            <span>欢 狸</span>
          </div>
        </div>
      </div>
    </div>
    <div class="table-container">
      <div>
        <table>
          <thead>
            <tr>
              <th>期次</th>
              <th>时间</th>
              <th>冠军</th>
              <th>亚军</th>
              <th>季军</th>
              <th>No4</th>
              <th>No5</th>
              <th>No6</th>
            </tr>
          </thead>
          <tbody>
            <%= for race <- @race_history do %>
              <tr>
                <td class="table-date">
                  {race.issue}
                </td>
                <td class="time">
                  {race.time}
                </td>
                <td>
                  <div class="front">
                    <img
                      src={"/images/image#{race.champion}.png"}
                      alt={
                        Enum.at(
                          @animals,
                          race.champion - 1
                        ).name
                      }
                    />
                  </div>
                </td>
                <td>
                  <div class="front">
                    <img
                      src={"/images/image#{race.second}.png"}
                      alt={Enum.at(@animals, race.second - 1).name}
                    />
                  </div>
                </td>
                <td>
                  <div class="front">
                    <img
                      src={"/images/image#{race.third}.png"}
                      alt={Enum.at(@animals, race.third - 1).name}
                    />
                  </div>
                </td>
                <td>
                  <div class="front">
                    <img
                      src={"/images/image#{race.fourth}.png"}
                      alt={Enum.at(@animals, race.fourth - 1).name}
                    />
                  </div>
                </td>
                <td>
                  <div class="front">
                    <img
                      src={"/images/image#{race.fifth}.png"}
                      alt={Enum.at(@animals, race.fifth - 1).name}
                    />
                  </div>
                </td>
                <td>
                  <div class="front">
                    <img
                      src={"/images/image#{race.sixth}.png"}
                      alt={Enum.at(@animals, race.sixth - 1).name}
                    />
                  </div>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
        <div class="pagination">
          <div class="el-pagination is-background el-pagination--small">
            <button
              type="button"
              class="btn-prev"
              phx-click="prev_page"
              disabled={@current_page == 1}
            >
              <i class="fas fa-chevron-left"></i>
            </button>
            <ul class="el-pager">
              <%= for page <- max(1, @current_page - 2)..min(@total_pages, @current_page + 2) do %>
                <li
                  class={if @current_page == page, do: "is-active", else: ""}
                  phx-click="goto_page"
                  phx-value-page={page}
                >
                  {page}
                </li>
              <% end %>
            </ul>
            <button
              type="button"
              class="btn-next"
              phx-click="next_page"
              disabled={@current_page == @total_pages}
            >
              <i class="fas fa-chevron-right"></i>
            </button>
            <span class="el-pagination__jump">
              {@current_page} / {@total_pages} 页
            </span>
          </div>
        </div>
      </div>
    </div>
  </div> --%>
</div>

<style>
  /* 基础布局和组件样式 */
  .racing-assistant {
    font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    display: flex;
    flex-direction: column;
    width: 100%;
    margin: 0;
    background-color: #fff4e6;
    position: relative;
    overflow-y: auto; /* 整体可滚动 */
    min-height: 100vh; /* 确保至少占满整个视口高度 */
    -webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
    touch-action: pan-y; /* 明确允许垂直滚动 */
  }

  /* 操作弹窗样式 */
  .action-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }

  .action-modal .modal-content {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  }

  .action-modal h2 {
    color: #333;
    margin-bottom: 20px;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
  }

  .action-modal .highlighted {
    color: #ff7a00;
  }

  .modal-body {
    margin-bottom: 20px;
  }

  .amount-selector {
    margin-bottom: 20px;
    background-color: rgba(255, 242, 230, 0.5);
    padding: 15px;
    border-radius: 8px;
    border: 1px dashed #ffb273;
  }

  .amount-selector label {
    display: block;
    margin-bottom: 10px;
    font-weight: bold;
    color: #ff7a00;
    font-size: 16px;
    text-align: center;
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.8);
  }

  .amount-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10px;
    box-shadow: 0 2px 5px rgba(255, 122, 0, 0.15);
    border-radius: 4px;
  }

  .amount-controls form {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    margin: 0;
    padding: 0;
  }

  .amount-btn {
    background-color: #fff8f0;
    border: 1px solid #ffb273;
    width: 40px;
    height: 40px;
    font-size: 22px;
    font-weight: bold;
    color: #ff7a00;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    user-select: none;
    transition: all 0.2s ease;
  }

  .amount-btn.decrease {
    border-radius: 4px 0 0 4px;
  }

  .amount-btn.increase {
    border-radius: 0 4px 4px 0;
  }

  .amount-btn:hover {
    background-color: #ffe0c2;
  }

  .amount-btn:active {
    background-color: #ffd1a3;
    transform: scale(0.95);
  }

  #action-amount {
    width: 80px;
    height: 40px;
    text-align: center;
    border: 1px solid #ffb273;
    border-left: none;
    border-right: none;
    font-size: 22px;
    font-weight: bold;
    color: #ff7a00; /* 橙色文字 */
    -moz-appearance: textfield;
    background-color: #fff8f0; /* 淡橙色背景 */
    caret-color: #ff7a00; /* 光标颜色 */
    transition: all 0.2s ease;
  }

  #action-amount:focus {
    outline: none;
    background-color: #fff2e6; /* 聚焦时背景加深 */
    box-shadow: inset 0 0 3px rgba(255, 122, 0, 0.3);
  }

  #action-amount::-webkit-inner-spin-button,
  #action-amount::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  .action-summary {
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 4px;
    text-align: center;
  }

  .action-summary p {
    margin: 5px 0;
    color: #555;
  }

  .action-summary strong {
    color: #ff7a00;
  }

  .modal-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
  }

  .cancel-btn {
    flex: 1;
    padding: 10px;
    background-color: #f0f0f0;
    color: #555;
    border: none;
    border-radius: 4px;
    margin-right: 10px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .cancel-btn:hover {
    background-color: #e0e0e0;
  }

  .confirm-btn {
    flex: 1;
    padding: 10px;
    background-color: #ff7a00;
    color: white;
    border: none;
    border-radius: 4px;
    margin-left: 10px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .confirm-btn:hover {
    background-color: #e56e00;
  }

  /* 清仓弹窗样式 */
  .liquidate-warning {
    background-color: #fff5f5;
    border: 1px solid #fed7d7;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
  }

  .liquidate-warning p {
    margin: 8px 0;
    color: #333;
  }

  .liquidate-warning .stock-list {
    background-color: #f8f9fa;
    border-radius: 4px;
    padding: 10px;
    margin: 10px 0;
    list-style: none;
  }

  .liquidate-warning .stock-list li {
    padding: 5px 0;
    border-bottom: 1px solid #e9ecef;
    color: #495057;
  }

  .liquidate-warning .stock-list li:last-child {
    border-bottom: none;
  }

  .liquidate-warning .total-value {
    background-color: #e8f5e8;
    border: 1px solid #c3e6c3;
    border-radius: 4px;
    padding: 10px;
    text-align: center;
    font-size: 16px;
    color: #155724;
  }

  .liquidate-warning .warning-text {
    color: #dc3545;
    font-weight: bold;
    text-align: center;
    margin-top: 10px;
  }

  .btn {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 5px;
  }

  .btn-secondary {
    background-color: #6c757d;
    color: white;
  }

  .btn-secondary:hover {
    background-color: #5a6268;
  }

  .btn-danger {
    background-color: #dc3545;
    color: white;
  }

  .btn-danger:hover {
    background-color: #c82333;
  }

  /* 主内容区域 - iframe容器 */
  .racing-main-content {
    width: 100%;
    height: 55vh; /* 固定游戏区域为视口高度的55% */
    position: relative;
    overflow: hidden;
    flex: 0 0 55vh; /* 固定高度 */
  }

  /* iframe容器 */
  .iframe-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
  }

  .iframe-container iframe {
    width: 100%;
    height: 100%;
    border: none;
  }

  /* 顶部信息容器 */
  .top-info-container {
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    z-index: 10;
    gap: 10px;
  }

  /* 左侧账户信息 */
  .account-info {
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 20px;
    padding: 8px 15px;
    color: #87CEEB;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 5px;
    backdrop-filter: blur(5px);
    flex: 0 0 auto;
  }

  .account-info i {
    color: #4CAF50;
  }

  .account-name {
    color: #87CEEB;
  }

  /* 右侧积分信息 */
  .points-info {
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 20px;
    padding: 8px 15px;
    color: #FFD700;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 5px;
    backdrop-filter: blur(5px);
    flex: 0 0 auto;
    flex-wrap: wrap;
    min-width: 200px;
  }

  .points-info .points-history-btn {
    font-size: 12px;
    padding: 4px 8px;
    margin-left: 8px;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #3b82f6;
    color: #3b82f6;
    transition: all 0.2s ease;
    white-space: nowrap;
  }

  .points-info .points-history-btn:hover {
    background: #3b82f6;
    color: white;
    transform: scale(1.05);
  }

  /* 底部操作按钮区域 */
  .bottom-actions {
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    padding: 20px;
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px 20px 0 0;
  }

  .bottom-btn {
    background-color: rgba(231, 76, 60, 0.9);
    color: white;
    border: none;
    border-radius: 25px;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
    text-align: center;
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  }

  .bottom-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
  }

  .bottom-btn.logout-btn {
    background-color: rgba(231, 76, 60, 0.9);
  }

  .bottom-btn.logout-btn:hover {
    background-color: rgba(192, 57, 43, 1);
  }

  .bottom-btn.admin-btn {
    background-color: rgba(52, 152, 219, 0.9);
  }

  .bottom-btn.admin-btn:hover {
    background-color: rgba(41, 128, 185, 1);
  }

  .bottom-btn.liquidate-btn {
    background-color: rgba(255, 107, 107, 0.9);
  }

  .bottom-btn.liquidate-btn:hover:not(:disabled) {
    background-color: rgba(220, 53, 69, 1);
  }

  .bottom-btn.liquidate-btn:disabled {
    background-color: rgba(108, 117, 125, 0.5);
    cursor: not-allowed;
    opacity: 0.6;
  }

  .bottom-btn i {
    font-size: 16px;
  }

  /* 猜冠军区域 */
  .champion-selection {
    padding: 8px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(255, 140, 56, 0.2);
    overflow: visible; /* 内容不滚动 */
    height: 40vh; /* 固定高度为视口的40% */
    max-height: 40vh;
    flex: 0 0 40vh; /* 固定高度 */
    margin: 0;
    box-sizing: border-box; /* 确保padding不增加元素总高度 */
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border-bottom: 1px solid #eee;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
  }

  .champion-title {
    color: black;
    /* 白色文字 */
    font-size: 18px;
    font-weight: bold;

  }

  .champion-title small {
    font-size: 12px;
    opacity: 0.9;
    color:rgb(10, 10, 10);
    /* 浅色文字 */
  }

  /* 动物网格 */
  .animal-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 5px;
    overflow-y: visible; /* 内容不滚动 */
    flex: 1; /* 自动适应可用空间 */
    padding-bottom: 5px; /* 底部留出空间 */
  }

  .animal-card {
    background-color: white;
    /* 白色卡片 */
    border-radius: 8px;
    padding: 8px 5px;
    text-align: center;
    /* 移除过渡动画效果 */
    border: 1px solid #ffb273;
    /* 淡橘色边框 */
    display: flex;
    flex-direction: column;
    align-items: center;
    user-select: none; /* 防止文字被选中 */
  }

  /* 添加以下规则以禁用所有点击状态效果 */
  .animal-card:active,
  .animal-card:focus,
  .animal-display-area:active,
  .animal-display-area:focus {
    outline: none;
    box-shadow: none;
    transform: none;
  }

  /* 添加特殊的规则，彻底禁用动物卡片的点击效果，但不影响页面滚动 */
  .animal-card {
    -webkit-tap-highlight-color: transparent; /* 禁用移动设备上的点击高亮 */
    -webkit-touch-callout: none; /* 禁止iOS上的长按菜单 */
    /* 移除 touch-action: none，允许滚动操作 */
    transform: translate3d(0,0,0); /* 强制硬件加速，避免点击时的渲染问题 */
  }

  /* 确保所有可能的点击状态都禁用视觉反馈，但不影响滚动 */
  .animal-card:active,
  .animal-card:hover,
  .animal-card:focus,
  .animal-display-area:active,
  .animal-display-area:hover,
  .animal-display-area:focus {
    outline: none !important;
    box-shadow: none !important;
    transform: none !important;
    background-color: white !important; /* 保持背景色不变 */
    opacity: 1 !important;
    filter: none !important;
    -webkit-filter: none !important;
  }

  /* 注意：我们只禁用卡片自身的样式，而不是所有子元素，以免影响滚动和按钮功能 */

  .animal-display-area {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  /* 重新设置按钮区域能够正常工作 */
  .stock-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    width: 100%;
    pointer-events: auto !important; /* 强制启用鼠标事件 */
    position: relative;
    z-index: 5; /* 提高层级，确保按钮可点击 */
  }

  .stock-btn {
    pointer-events: auto !important;
    cursor: pointer !important;
    touch-action: manipulation !important; /* 优化触摸操作，减少延迟 */
  }

  .animal-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin: 0 auto 5px;
    border: 2px solid #fff;
    /* 白色边框 */
    box-shadow: 0 2px 5px rgba(255, 122, 0, 0.2);
  }

  .animal-name {
    color: #ff7a00;
    /* 橘色文字 */
    font-weight: bold;
    margin-bottom: 5px;
  }

  .animal-pinyin {
    color: #ff9a40;
    /* 淡橘色文字 */
    font-size: 12px;
  }

  .animal-bet-amount {
    margin-top: 5px;
    color: #ff7a00;
    /* 橘色文字 */
    font-size: 14px;
  }

  .bet-label {
    font-weight: bold;
  }

  .bet-value {
    margin-left: 5px;
  }

  .my-bet-amount {
    margin-top: 5px;
    color: #ff7a00;
    /* 橘色文字 */
    font-size: 14px;
  }

  .my-stock-amount {
    margin-top: 5px;
    color: #2ecc71;
    /* 绿色文字 */
    font-size: 14px;
  }

  .my-stock-label {
    font-weight: bold;
  }

  .my-stock-value {
    margin-left: 5px;
  }

  .stock-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    width: 100%;
    pointer-events: auto; /* 确保按钮可以点击 */
    position: relative; /* 为按钮创建新的堆叠上下文 */
    z-index: 2; /* 确保按钮在上层 */
  }

  .stock-btn {
    flex: 1;
    padding: 6px 0;  /* 增加按钮高度 */
    border: none;
    border-radius: 4px;
    color: white;
    font-size: 12px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;  /* 为鼠标点击区域做准备 */
    z-index: 2;  /* 确保按钮在卡片上方 */
    min-height: 28px;  /* 设置最小高度以便于点击 */
    margin: 0 2px;  /* 所有按钮都有一致的边距 */
  }

  .stock-btn i {
    margin-right: 3px;
  }

  .stock-btn.buy {
    background-color: #2ecc71;
  }

  .stock-btn.buy:hover {
    background-color: #27ae60;
  }

  .stock-btn.sell {
    background-color: #e74c3c;
  }

  .stock-btn.sell:hover {
    background-color: #c0392b;
  }

  .stock-btn.bet {
    background-color: #3498db;
  }

  .stock-btn.bet:hover {
    background-color: #2980b9;
  }

  .stock-btn:disabled {
    background-color: #95a5a6;
    cursor: not-allowed;
  }

  /* 页脚区域 */
  .racing-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #ffffff;
    margin-top: 10px; /* 将页脚推到底部 */
    padding: 5px 10px;
    border-top: 1px solid #eee;
    min-height: 20px;
  }

  .bet-button {
    background-color: #e74c3c;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 18px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s;
    margin-left: auto;
    align-self: flex-end;
  }

  .bet-button:hover {
    background-color: #c0392b;
  }

  .bet-button.disabled {
    background-color: #777;
    cursor: not-allowed;
  }

  /* 下注历史弹窗 */
  .bet-history-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 100;
  }

  .modal-content {
    background-color: #2a2a2a;
    border-radius: 8px;
    width: 80%;
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
    padding: 20px;
  }

  .modal-content h2 {
    color: #FFD700;
    margin-bottom: 20px;
    text-align: center;
  }

  .bet-list table {
    width: 100%;
    border-collapse: collapse;
    color: white;
  }

  .bet-list th,
  .bet-list td {
    padding: 10px;
    text-align: center;
    border-bottom: 1px solid #444;
  }

  .bet-list th {
    background-color: #333;
    color: #FFD700;
  }

  .no-bets {
    text-align: center;
    color: #999;
    padding: 20px;
  }

  .pending {
    color: #f39c12;
  }

  .win {
    color: #2ecc71;
    font-weight: bold;
  }

  .lose {
    color: #999;
  }

  .close-btn {
    display: block;
    margin: 20px auto 0;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 15px;
    cursor: pointer;
  }

  /* Flash消息样式 */
  .flash-messages {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    width: 90%;
    max-width: 500px;
  }

  .flash-message {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    margin-bottom: 15px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: slide-down 0.3s ease-out forwards;
    color: white;
    transition: opacity 0.3s, transform 0.3s;
  }

  .flash-message.info {
    background-color: #2ecc71;
    border-left: 5px solid #27ae60;
  }

  .flash-message.error {
    background-color: #e74c3c;
    border-left: 5px solid #c0392b;
  }

  .flash-content {
    display: flex;
    align-items: center;
    flex: 1;
    font-weight: 500;
  }

  .flash-content i {
    margin-right: 10px;
    font-size: 18px;
  }

  .flash-close {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.2s;
    padding: 0;
    margin-left: 15px;
    line-height: 1;
  }

  .flash-close:hover {
    opacity: 1;
  }

  @keyframes slide-down {
    0% {
      opacity: 0;
      transform: translateY(-20px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* 往期数据容器样式 */
  .history-container {
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin: 15px 15px 15px 15px;
    height: auto;
    min-height: 600px;
    overflow: hidden; /* 内容不单独滚动 */
    flex: 0 0 auto; /* 固定高度，不拉伸 */
    /* 确保历史区域不会挤压前两个部分 */
    margin-top: 20px;
    border-top: 2px solid #ffb273; /* 添加边框以区分和上方内容 */
    padding-top: 10px;
  }

  .history-container .alltitle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #eee;
  }

  .history-container .title {
    display: flex;
    align-items: center;
    font-weight: bold;
    font-size: 16px;
  }

  .history-container .title img {
    width: 24px;
    height: 24px;
    margin-right: 8px;
  }

  .date-picker-container {
    display: flex;
    align-items: center;
  }

  .date-picker-container a {
    margin: 0 8px;
    color: #666;
    text-decoration: none;
    font-size: 14px;
  }

  .date-picker-container a:hover {
    color: #e74c3c;
  }

  /* 参赛动物队列号样式 */
  .playnum {
    padding: 15px;
  }

  .title-with-line {
    position: relative;
    margin-bottom: 20px;
  }

  .title-with-line .title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 15px;
  }

  .title-with-line:after {
    content: '';
    display: block;
    height: 1px;
    background-color: #eee;
    position: absolute;
    bottom: -5px;
    left: 0;
    right: 0;
  }

  .playerimg {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    margin-top: 15px;
  }

  .image-gallery {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 15px;
  }

  .image-gallery img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-bottom: 5px;
  }

  .playertitle {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 12px;
  }

  .playertitle a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-bottom: 3px;
    color: white;
    text-decoration: none;
  }

  .playertitle a.one {
    background-color: #e74c3c;
  }

  .playertitle a.two {
    background-color: #3498db;
  }

  .playertitle a.three {
    background-color: #2ecc71;
  }

  .playertitle a.four {
    background-color: #f39c12;
  }

  .playertitle a.five {
    background-color: #9b59b6;
  }

  .playertitle a.six {
    background-color: #1abc9c;
  }

  /* 表格样式 */
  .table-container {
    padding: 15px;
    overflow-x: auto;
  }

  .table-container table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
  }

  .table-container th,
  .table-container td {
    padding: 10px;
    text-align: center;
  }

  .table-container th {
    background-color: #f8f8f8;
    font-weight: bold;
  }

  .table-container .tableheadcolor {
    color: #e74c3c;
  }

  .table-container tr:nth-child(even) {
    background-color: #f8f8f8;
  }

  .table-container .table-date {
    font-weight: bold;
  }

  .table-container .time {
    color: #666;
  }

  .table-container .front img {
    width: 42px;
    height: 36px;
  }

  .table-container .back {
    margin-top: 5px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
  }

  .table-container .circle-bg.one {
    background-color: #e74c3c;
  }

  .table-container .circle-bg.two {
    background-color: #3498db;
  }

  .table-container .circle-bg.three {
    background-color: #2ecc71;
  }

  .table-container .circle-bg.four {
    background-color: #f39c12;
  }

  .table-container .circle-bg.five {
    background-color: #9b59b6;
  }

  .table-container .circle-bg.six {
    background-color: #1abc9c;
  }

  /* 分页样式 */
  .pagination {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }

  .el-pagination {
    --el-pagination-bg-color: #fff;
    --el-pagination-button-color: #606266;
    --el-pagination-hover-color: #409eff;
    --el-pagination-button-bg-color: #f4f4f5;
    --el-pagination-button-disabled-color: #c0c4cc;
    --el-pagination-button-disabled-bg-color: #fff;
    --el-pagination-border-radius: 4px;
    white-space: nowrap;
    color: var(--el-pagination-button-color);
    justify-content: center;
    display: flex;
    align-items: center;
  }

  .el-pagination .btn-prev,
  .el-pagination .btn-next {
    margin: 0 5px;
    cursor: pointer;
    background-color: var(--el-pagination-button-bg-color);
    color: var(--el-pagination-button-color);
    border-radius: var(--el-pagination-border-radius);
    padding: 0 6px;
    height: 22px;
    line-height: 22px;
  }

  .el-pagination .el-pager {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
  }

  .el-pagination .el-pager li {
    margin: 0 5px;
    cursor: pointer;
    background-color: var(--el-pagination-button-bg-color);
    color: var(--el-pagination-button-color);
    border-radius: var(--el-pagination-border-radius);
    padding: 0 6px;
    height: 22px;
    min-width: 22px;
    line-height: 22px;
    text-align: center;
  }

  .el-pagination .el-pager li.is-active {
    background-color: #e74c3c;
    color: white;
  }

  /* 移动设备上的数量选择器优化 */
  @media (max-width: 480px) {
    .amount-selector {
      margin-bottom: 15px;
      padding: 10px;
    }

    #action-amount {
      width: 60px;
      height: 38px;
      font-size: 20px;
    }

    .amount-btn {
      width: 38px;
      height: 38px;
      font-size: 20px;
    }

    .amount-controls {
      margin-top: 5px;
    }

    .amount-hint {
      font-size: 11px !important;
    }
  }

  @media (max-width: 768px) {
    .image-gallery {
      margin-right: 15px;
    }

    .animal-grid {
      grid-template-columns: repeat(3, 1fr);
    }

    .champion-selection {
      padding: 5px;
      height: 42vh;
      flex: 0 0 42vh;
    }

    .animal-card {
      padding: 5px 2px;
    }

    /* 调整主内容区域高度 */
    .racing-main-content {
      height: 50vh;
      flex: 0 0 50vh;
    }

    .animal-avatar {
      width: 38px;
      height: 38px;
      margin-bottom: 3px;
    }

    /* 在中等屏幕上调整按钮样式 */
    .action-btn {
      font-size: 11px;
      padding: 3px 8px;
      min-width: 50px;
    }

    .action-buttons {
      gap: 6px;
    }

    .top-row {
      gap: 8px;
    }
  }

  @media (max-width: 480px) {
    .animal-name {
      font-size: 12px;
    }

    .animal-pinyin {
      font-size: 8px;
    }

    .animal-grid {
      gap: 3px;
    }

    .modal-content {
      padding: 15px;
    }

    .form-actions button {
      padding: 8px 15px;
    }

    /* 在小屏幕设备上调整高度 */
    .racing-main-content {
      height: 50vh;
      flex: 0 0 50vh;
    }

    .champion-selection {
      height: 45vh;
      flex: 0 0 45vh;
    }

    .animal-avatar {
      width: 35px;
      height: 35px;
      margin-bottom: 2px;
    }

    .racing-footer {
      padding: 3px 5px;
    }

    /* Flash消息响应式调整 */
    .flash-messages {
      width: 95%;
      max-width: none;
      top: 10px;
    }

    .flash-message {
      padding: 10px 15px;
      margin-bottom: 10px;
    }

    .flash-content {
      font-size: 14px;
    }

    .flash-content i {
      font-size: 16px;
    }

    /* 在小屏幕上进一步调整按钮样式 */
    .action-btn {
      font-size: 10px;
      padding: 2px 6px;
      min-width: 45px;
    }

    .points-row {
      gap: 6px;
    }

    .left-action,
    .right-action {
      min-width: 45px;
    }
  }

  @media (max-width: 375px) {
    .animal-avatar {
      width: 30px;
      height: 30px;
      margin-bottom: 2px;
    }

    /* 确保小屏幕上动物选择区域也有良好体验 */
    .animal-display-area {
      padding: 3px 0;
    }

    .animal-card {
      padding: 6px 4px;
    }

    .bet-button {
      padding: 5px 12px;
      font-size: 14px;
    }

    .racing-main-content {
      height: 45vh; /* 在极小屏幕上进一步减少游戏区域比例 */
      flex: 0 0 45vh;
    }

    .champion-selection {
      height: 50vh;
      flex: 0 0 50vh;
    }

    .animal-card {
      padding: 3px 1px;
    }

    .animal-bet-amount, .my-bet-amount, .my-stock-amount {
      font-size: 10px;
      margin-top: 2px;
    }

    .stock-actions {
      margin-top: 4px;
    }

    .stock-btn {
      padding: 5px 0;  /* 在移动设备上增加按钮高度 */
      font-size: 11px;
      min-height: 30px;  /* 确保在移动设备上有足够的点击区域 */
    }

    .stock-btn i {
      font-size: 8px;
    }

    /* 在极小屏幕上进一步调整按钮样式 */
    .action-btn {
      font-size: 9px;
      padding: 2px 4px;
      min-width: 40px;
    }

    .points-row {
      gap: 4px;
    }

    .left-action,
    .right-action {
      min-width: 40px;
    }

    .points-display {
      font-size: 12px;
    }
  }

  /* 添加更精确的选择器，确保stock-actions内的元素不受animal-card禁用规则影响 */
  .animal-card .stock-actions,
  .animal-card .stock-actions * {
    pointer-events: auto !important;
    touch-action: manipulation !important; /* 允许元素的触摸交互，且优化触摸操作体验 */
    transform: none !important;
    transition: background-color 0.2s !important; /* 仅允许背景色变化的过渡效果 */
  }

  /* 确保按钮的悬停状态正常工作 */
  .stock-btn.buy:hover {
    background-color: #27ae60 !important;
  }

  .stock-btn.sell:hover {
    background-color: #c0392b !important;
  }

  .stock-btn.bet:hover {
    background-color: #2980b9 !important;
  }
</style>
