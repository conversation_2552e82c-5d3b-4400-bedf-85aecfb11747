defmodule RacingGame.Live.AdminPanel.Handlers.ModalEventHandler do
  @moduledoc """
  模态框事件处理器

  处理系统通信管理组件中的模态框显示、关闭和验证操作。
  """

  require Logger
  alias RacingGame.SystemCommunication
  alias RacingGame.Live.AdminPanel.InputValidator
  import RacingGame.Utils.DialogHelper
  import Phoenix.Component, only: [assign: 3]
  import Phoenix.LiveView, only: [put_flash: 3]

  @doc """
  处理显示创建模态框事件

  ## 参数
  - `type` - 通信类型
  - `socket` - Phoenix LiveView socket

  ## 返回
  - `{:noreply, socket}` - 更新后的socket
  """
  def handle_show_create(type, socket) do
    Logger.info("🎯 [模态框] 显示创建对话框，类型: #{type}")

    modal_data = build_create_modal_data(type)

    socket =
      socket
      |> reset_modal_state()
      |> assign(:show_create_modal, true)
      |> assign(:modal_type, type)
      |> assign(:modal_mode, :create)
      |> assign(:modal_data, modal_data)
      |> assign(:buttons_hidden, false)

    {:noreply, socket}
  end

  @doc """
  处理显示编辑模态框事件

  ## 参数
  - `id` - 记录ID
  - `socket` - Phoenix LiveView socket

  ## 返回
  - `{:noreply, socket}` - 更新后的socket
  """
  def handle_show_edit(id, socket) do
    Logger.info("🎯 [模态框] 显示编辑对话框，ID: #{id}")

    case InputValidator.validate_uuid(id) do
      {:ok, valid_id} ->
        handle_edit_dialog_with_valid_id(socket, valid_id)
      {:error, message} ->
        handle_edit_dialog_error(socket, "参数错误: #{message}")
    end
  end

  @doc """
  处理关闭模态框事件

  ## 参数
  - `socket` - Phoenix LiveView socket

  ## 返回
  - `{:noreply, socket}` - 更新后的socket
  """
  def handle_close_modal(socket) do
    Logger.info("🎯 [模态框] 关闭模态框")

    socket = reset_modal_state(socket)
    {:noreply, socket}
  end

  @doc """
  处理表单验证事件

  ## 参数
  - `params` - 表单参数
  - `socket` - Phoenix LiveView socket

  ## 返回
  - `{:noreply, socket}` - 更新后的socket
  """
  def handle_validate_form(params, socket) do
    Logger.debug("🎯 [模态框] 表单验证事件")

    updated_modal_data = update_modal_data(socket.assigns.modal_data, params)
    socket = assign(socket, :modal_data, updated_modal_data)
    {:noreply, socket}
  end

  # 私有函数 - 重置模态框状态
  defp reset_modal_state(socket) do
    socket
    |> assign(:show_create_modal, false)
    |> assign(:show_edit_modal, false)
    |> assign(:current_communication, nil)
    |> assign(:modal_data, nil)
    |> assign(:modal_mode, nil)
  end

  # 私有函数 - 构建创建模态框数据
  defp build_create_modal_data(type) do
    %{
      type: type,
      title: "",
      content: "",
      recipient_type: "all",
      recipient_id: "",
      priority: "medium",
      active: true,
      expires_at: nil
    }
  end

  # 私有函数 - 处理有效ID的编辑对话框
  defp handle_edit_dialog_with_valid_id(socket, valid_id) do
    case SystemCommunication.read(valid_id) do
      {:ok, communication} ->
        modal_data = communication_to_modal_data(communication)

        socket =
          socket
          |> reset_modal_state()
          |> assign(:show_edit_modal, true)
          |> assign(:current_communication, communication)
          |> assign(:modal_type, communication.type)
          |> assign(:modal_mode, :edit)
          |> assign(:modal_data, modal_data)

        {:noreply, socket}

      {:error, %Ash.Error.Query.NotFound{}} ->
        handle_edit_dialog_error(socket, "指定的记录不存在，可能已被删除。请刷新页面后重试。")

      {:error, _error} ->
        handle_edit_dialog_error(socket, "无法读取记录信息，请稍后重试。")
    end
  end

  # 私有函数 - 处理编辑对话框错误
  defp handle_edit_dialog_error(socket, message) do
    socket =
      socket
      |> put_flash(:error, message)
      |> show_error_dialog(:operation_error,
          title: "编辑失败",
          message: "❌ #{message}",
          confirm_action: "dialog:hide",
          confirm_data: %{"dialog" => "operation_error"}
        )
    {:noreply, socket}
  end

  # 私有函数 - 更新模态框数据
  defp update_modal_data(existing_data, params) when is_map(existing_data) do
    existing_data
    |> Map.put(:title, params["title"] || "")
    |> Map.put(:content, params["content"] || "")
    |> Map.put(:recipient_type, params["recipient_type"] || "all")
    |> Map.put(:recipient_id, params["recipient_id"] || "")
    |> Map.put(:priority, params["priority"] || "medium")
    |> Map.put(:active, params["active"] == "true")
    |> Map.put(:id, Map.get(existing_data, :id))
  end

  defp update_modal_data(_existing_data, params) do
    %{
      title: params["title"] || "",
      content: params["content"] || "",
      recipient_type: params["recipient_type"] || "all",
      recipient_id: params["recipient_id"] || "",
      priority: params["priority"] || "medium",
      active: params["active"] == "true"
    }
  end

  # 私有函数 - 通信记录转换为模态框数据
  defp communication_to_modal_data(communication) do
    %{
      id: communication.id,
      type: communication.type,
      title: communication.title || "",
      content: communication.content || "",
      recipient_type: communication.recipient_type || "all",
      recipient_id: communication.recipient_id || "",
      priority: communication.priority || "medium",
      active: communication.active || false,
      expires_at: communication.expires_at
    }
  end
end
