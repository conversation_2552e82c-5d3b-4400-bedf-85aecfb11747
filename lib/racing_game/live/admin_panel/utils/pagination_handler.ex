defmodule RacingGame.Utils.PaginationHandler do
  @moduledoc """
  分页事件处理器

  提供标准化的分页事件处理函数，可以在LiveView组件中使用。
  """

  alias RacingGame.Live.AdminPanel.InputValidator

  @doc """
  处理页码变化事件

  ## 参数
  - `params`: 事件参数，包含 "page" 字段
  - `socket`: LiveView socket
  - `reload_callback`: 重新加载数据的回调函数

  ## 返回
  - `{:noreply, socket}`: 更新后的socket
  """
  def handle_page_change(params, socket, reload_callback \\ nil) do
    case validate_page_number(params["page"]) do
      {:ok, valid_page} ->
        socket =
          socket
          |> assign(:page, valid_page)
          |> maybe_reload_data(reload_callback)
        {:noreply, socket}

      {:error, message} ->
        socket = put_flash(socket, :error, message)
        {:noreply, socket}
    end
  end

  @doc """
  处理每页数量变化事件

  ## 参数
  - `params`: 事件参数，包含 "value" 字段
  - `socket`: LiveView socket
  - `reload_callback`: 重新加载数据的回调函数

  ## 返回
  - `{:noreply, socket}`: 更新后的socket
  """
  def handle_per_page_change(params, socket, reload_callback \\ nil) do
    # 尝试从不同的参数键获取值
    per_page_value = params["per_page"] || params["value"] || params["_target"]
    case validate_per_page(per_page_value) do
      {:ok, valid_per_page} ->
        socket =
          socket
          |> assign(:per_page, valid_per_page)
          |> assign(:page, 1)  # 重置到第一页
          |> maybe_reload_data(reload_callback)
        {:noreply, socket}

      {:error, message} ->
        socket = put_flash(socket, :error, message)
        {:noreply, socket}
    end
  end

  @doc """
  处理页面跳转事件

  ## 参数
  - `params`: 事件参数，包含 "value" 字段
  - `socket`: LiveView socket
  - `reload_callback`: 重新加载数据的回调函数

  ## 返回
  - `{:noreply, socket}`: 更新后的socket
  """
  def handle_page_jump(params, socket, reload_callback \\ nil) do
    # 尝试从不同的参数键获取页码
    page_value = params["page"] || params["value"]
    case validate_page_number(page_value) do
      {:ok, valid_page} ->
        # 确保页码在有效范围内
        total_pages = calculate_total_pages(socket.assigns.total_count, socket.assigns.per_page)
        valid_page = max(1, min(valid_page, total_pages))

        socket =
          socket
          |> assign(:page, valid_page)
          |> maybe_reload_data(reload_callback)
        {:noreply, socket}

      {:error, message} ->
        socket = put_flash(socket, :error, message)
        {:noreply, socket}
    end
  end

  @doc """
  初始化分页相关的assigns

  ## 参数
  - `socket`: LiveView socket
  - `opts`: 选项
    - `:page`: 初始页码，默认为 1
    - `:per_page`: 每页数量，默认为 20
    - `:total_count`: 总记录数，默认为 0

  ## 返回
  - `socket`: 更新后的socket
  """
  def init_pagination(socket, opts \\ []) do
    socket
    |> assign(:page, Keyword.get(opts, :page, 1))
    |> assign(:per_page, Keyword.get(opts, :per_page, 20))
    |> assign(:total_count, Keyword.get(opts, :total_count, 0))
  end

  @doc """
  更新总记录数

  ## 参数
  - `socket`: LiveView socket
  - `total_count`: 新的总记录数

  ## 返回
  - `socket`: 更新后的socket
  """
  def update_total_count(socket, total_count) do
    current_page = socket.assigns.page
    per_page = socket.assigns.per_page
    total_pages = calculate_total_pages(total_count, per_page)

    # 如果当前页超出了总页数，调整到最后一页
    adjusted_page = if current_page > total_pages and total_pages > 0 do
      total_pages
    else
      current_page
    end

    socket
    |> assign(:total_count, total_count)
    |> assign(:page, adjusted_page)
  end

  @doc """
  获取当前页的数据范围

  ## 参数
  - `page`: 当前页码
  - `per_page`: 每页数量

  ## 返回
  - `{start_index, end_index}`: 数据范围（基于0的索引）
  """
  def get_page_range(page, per_page) do
    start_index = (page - 1) * per_page
    end_index = start_index + per_page - 1
    {start_index, end_index}
  end

  @doc """
  对数据进行分页处理

  ## 参数
  - `data`: 要分页的数据列表
  - `page`: 当前页码
  - `per_page`: 每页数量

  ## 返回
  - `{paged_data, total_count}`: 分页后的数据和总数
  """
  def paginate_data(data, page, per_page) do
    total_count = length(data)
    {start_index, _end_index} = get_page_range(page, per_page)

    paged_data =
      data
      |> Enum.drop(start_index)
      |> Enum.take(per_page)

    {paged_data, total_count}
  end

  @doc """
  生成分页的SQL LIMIT和OFFSET

  ## 参数
  - `page`: 当前页码
  - `per_page`: 每页数量

  ## 返回
  - `{limit, offset}`: SQL分页参数
  """
  def get_sql_pagination(page, per_page) do
    offset = (page - 1) * per_page
    {per_page, offset}
  end

  # 私有函数：验证页码
  defp validate_page_number(page) when is_binary(page) do
    case Integer.parse(page) do
      {num, ""} when num > 0 -> {:ok, num}
      _ -> {:error, "页码必须是大于0的整数"}
    end
  end

  defp validate_page_number(page) when is_integer(page) and page > 0 do
    {:ok, page}
  end

  defp validate_page_number(_) do
    {:error, "页码必须是大于0的整数"}
  end

  # 私有函数：验证每页数量
  defp validate_per_page(per_page) when is_binary(per_page) do
    case Integer.parse(per_page) do
      {num, ""} when num > 0 and num <= 1000 -> {:ok, num}
      {num, ""} when num > 1000 -> {:error, "每页数量不能超过1000"}
      _ -> {:error, "每页数量必须是1-1000之间的整数"}
    end
  end

  defp validate_per_page(per_page) when is_integer(per_page) and per_page > 0 and per_page <= 1000 do
    {:ok, per_page}
  end

  defp validate_per_page(_) do
    {:error, "每页数量必须是1-1000之间的整数"}
  end

  # 私有函数：计算总页数
  defp calculate_total_pages(total_count, per_page) when total_count > 0 and per_page > 0 do
    ceil(total_count / per_page)
  end

  defp calculate_total_pages(_, _), do: 1

  # 私有函数：可能重新加载数据
  defp maybe_reload_data(socket, nil), do: socket
  defp maybe_reload_data(socket, reload_callback) when is_function(reload_callback, 1) do
    reload_callback.(socket)
  end

  # 私有函数：assign辅助函数
  defp assign(socket, key, value) do
    Phoenix.Component.assign(socket, key, value)
  end

  # 私有函数：put_flash辅助函数
  defp put_flash(socket, type, message) do
    Phoenix.LiveView.put_flash(socket, type, message)
  end


end
