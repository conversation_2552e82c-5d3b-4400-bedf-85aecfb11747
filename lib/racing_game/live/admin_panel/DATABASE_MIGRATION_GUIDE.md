# 数据库迁移指南

## 📋 概述

本文档提供 Racing Game Admin Panel 系统数据库迁移的完整指南，包括迁移策略、操作步骤、风险控制和回滚方案，确保数据库变更的安全性和可靠性。

## 🎯 迁移原则

### 核心原则
- **零停机时间**: 迁移过程不影响业务运行
- **数据完整性**: 确保数据不丢失、不损坏
- **可回滚性**: 任何迁移都必须可以安全回滚
- **渐进式迁移**: 采用分阶段、小步快跑的策略
- **充分测试**: 在测试环境完整验证后再执行

### 安全保障
- **备份优先**: 迁移前必须完成数据备份
- **权限控制**: 严格控制迁移操作权限
- **监控告警**: 实时监控迁移过程和系统状态
- **应急预案**: 准备完整的应急处理方案

## 🗂️ 迁移类型分类

### 1. 结构迁移 (Schema Migration)
```sql
-- 添加新表
CREATE TABLE new_feature_table (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(50) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 添加新列 (向后兼容)
ALTER TABLE users ADD COLUMN avatar_url VARCHAR(500);
ALTER TABLE users ADD COLUMN last_login_ip INET;

-- 添加索引 (并发创建)
CREATE INDEX CONCURRENTLY idx_users_last_login ON users(last_login_at);
CREATE INDEX CONCURRENTLY idx_payments_status_date ON payments(status, created_at);

-- 添加约束 (分步骤)
-- 步骤1: 添加不验证的约束
ALTER TABLE users ADD CONSTRAINT users_email_format CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$') NOT VALID;
-- 步骤2: 验证约束
ALTER TABLE users VALIDATE CONSTRAINT users_email_format;
```

### 2. 数据迁移 (Data Migration)
```sql
-- 数据转换迁移
UPDATE users 
SET status = 'active' 
WHERE status IS NULL AND created_at > '2024-01-01';

-- 数据清理迁移
DELETE FROM temp_data 
WHERE created_at < CURRENT_DATE - INTERVAL '30 days';

-- 数据格式转换
UPDATE payments 
SET amount_cents = ROUND(amount * 100)
WHERE amount_cents IS NULL;

-- 批量数据迁移 (分批处理)
DO $$
DECLARE
    batch_size INTEGER := 1000;
    processed INTEGER := 0;
    total_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO total_count FROM old_table WHERE migrated = false;
    
    WHILE processed < total_count LOOP
        UPDATE old_table 
        SET new_column = transform_function(old_column),
            migrated = true
        WHERE id IN (
            SELECT id FROM old_table 
            WHERE migrated = false 
            ORDER BY id 
            LIMIT batch_size
        );
        
        processed := processed + batch_size;
        RAISE NOTICE 'Processed % of % records', processed, total_count;
        
        -- 暂停以减少系统负载
        PERFORM pg_sleep(0.1);
    END LOOP;
END $$;
```

### 3. 性能优化迁移
```sql
-- 分区表迁移
-- 步骤1: 创建分区表
CREATE TABLE payments_partitioned (
    LIKE payments INCLUDING ALL
) PARTITION BY RANGE (created_at);

-- 步骤2: 创建分区
CREATE TABLE payments_2024_q1 PARTITION OF payments_partitioned
FOR VALUES FROM ('2024-01-01') TO ('2024-04-01');

CREATE TABLE payments_2024_q2 PARTITION OF payments_partitioned
FOR VALUES FROM ('2024-04-01') TO ('2024-07-01');

-- 步骤3: 数据迁移
INSERT INTO payments_partitioned SELECT * FROM payments;

-- 步骤4: 切换表名
BEGIN;
ALTER TABLE payments RENAME TO payments_old;
ALTER TABLE payments_partitioned RENAME TO payments;
COMMIT;
```

## 🔄 迁移流程

### 标准迁移流程
```mermaid
graph TD
    A[迁移需求分析] --> B[制定迁移计划]
    B --> C[开发环境测试]
    C --> D[测试环境验证]
    D --> E[预生产环境测试]
    E --> F[生产环境备份]
    F --> G[执行迁移]
    G --> H[验证迁移结果]
    H --> I{迁移成功?}
    I -->|是| J[清理临时数据]
    I -->|否| K[执行回滚]
    K --> L[问题分析]
    L --> B
    J --> M[迁移完成]
```

### 迁移前检查清单
```bash
#!/bin/bash
# 迁移前检查脚本

echo "=== 数据库迁移前检查 ==="

# 1. 检查数据库连接
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "SELECT version();" || {
    echo "❌ 数据库连接失败"
    exit 1
}

# 2. 检查磁盘空间
DISK_USAGE=$(df -h /var/lib/postgresql | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "❌ 磁盘空间不足: ${DISK_USAGE}%"
    exit 1
fi

# 3. 检查数据库大小
DB_SIZE=$(psql -h $DB_HOST -U $DB_USER -d $DB_NAME -t -c "SELECT pg_size_pretty(pg_database_size('$DB_NAME'));")
echo "📊 数据库大小: $DB_SIZE"

# 4. 检查活跃连接数
ACTIVE_CONNECTIONS=$(psql -h $DB_HOST -U $DB_USER -d $DB_NAME -t -c "SELECT count(*) FROM pg_stat_activity WHERE state = 'active';")
echo "🔗 活跃连接数: $ACTIVE_CONNECTIONS"

# 5. 检查长时间运行的查询
LONG_QUERIES=$(psql -h $DB_HOST -U $DB_USER -d $DB_NAME -t -c "SELECT count(*) FROM pg_stat_activity WHERE state = 'active' AND now() - query_start > interval '5 minutes';")
if [ $LONG_QUERIES -gt 0 ]; then
    echo "⚠️  发现长时间运行的查询: $LONG_QUERIES 个"
fi

# 6. 检查复制延迟 (如果有从库)
if [ ! -z "$REPLICA_HOST" ]; then
    REPLICATION_LAG=$(psql -h $REPLICA_HOST -U $DB_USER -d $DB_NAME -t -c "SELECT EXTRACT(EPOCH FROM (now() - pg_last_xact_replay_timestamp()));")
    echo "🔄 复制延迟: ${REPLICATION_LAG}秒"
fi

echo "✅ 迁移前检查完成"
```

## 📦 Phoenix/Ecto 迁移管理

### 创建迁移文件
```bash
# 创建新的迁移文件
mix ecto.gen.migration add_avatar_to_users

# 创建带有内容的迁移
mix ecto.gen.migration create_user_profiles --change "create table(:user_profiles) do
  add :user_id, references(:users, on_delete: :delete_all), null: false
  add :bio, :text
  add :website, :string
  timestamps()
end

create unique_index(:user_profiles, [:user_id])"
```

### 迁移文件模板
```elixir
defmodule RacingGame.Repo.Migrations.AddAvatarToUsers do
  use Ecto.Migration

  def up do
    alter table(:users) do
      add :avatar_url, :string, size: 500
      add :avatar_updated_at, :utc_datetime
    end

    create index(:users, [:avatar_updated_at])
    
    # 数据迁移
    execute """
    UPDATE users 
    SET avatar_url = 'https://example.com/default-avatar.png'
    WHERE avatar_url IS NULL
    """
  end

  def down do
    alter table(:users) do
      remove :avatar_url
      remove :avatar_updated_at
    end
  end
end
```

### 复杂迁移示例
```elixir
defmodule RacingGame.Repo.Migrations.MigratePaymentAmountToCents do
  use Ecto.Migration
  import Ecto.Query

  def up do
    # 添加新列
    alter table(:payments) do
      add :amount_cents, :integer
    end

    # 数据迁移 - 分批处理
    flush()
    migrate_payment_amounts()

    # 添加约束
    create constraint(:payments, :amount_cents_positive, check: "amount_cents > 0")
    
    # 创建索引
    create index(:payments, [:amount_cents])
  end

  def down do
    alter table(:payments) do
      remove :amount_cents
    end
  end

  defp migrate_payment_amounts do
    batch_size = 1000
    
    Stream.unfold(0, fn offset ->
      payments = 
        from(p in "payments",
          where: is_nil(p.amount_cents),
          limit: ^batch_size,
          offset: ^offset,
          select: [:id, :amount]
        )
        |> repo().all()

      case payments do
        [] -> nil
        payments ->
          Enum.each(payments, fn payment ->
            amount_cents = round(payment.amount * 100)
            
            from(p in "payments", where: p.id == ^payment.id)
            |> repo().update_all(set: [amount_cents: amount_cents])
          end)
          
          IO.puts("Migrated batch starting at offset #{offset}")
          {payments, offset + batch_size}
      end
    end)
    |> Stream.run()
  end
end
```

## 🔄 零停机迁移策略

### 在线模式变更 (Online Schema Change)
```sql
-- 1. 添加新列 (兼容旧版本)
ALTER TABLE users ADD COLUMN new_email VARCHAR(255);

-- 2. 双写阶段 - 应用代码同时写入新旧字段
-- 应用代码更新: 写入时同时更新 email 和 new_email

-- 3. 数据回填
UPDATE users SET new_email = email WHERE new_email IS NULL;

-- 4. 应用代码切换到读取新字段
-- 应用代码更新: 读取 new_email 而不是 email

-- 5. 删除旧列
ALTER TABLE users DROP COLUMN email;
ALTER TABLE users RENAME COLUMN new_email TO email;
```

### 蓝绿部署迁移
```bash
#!/bin/bash
# 蓝绿部署迁移脚本

# 1. 创建新的数据库实例 (绿环境)
createdb racing_game_green

# 2. 在绿环境执行迁移
pg_dump racing_game_blue | psql racing_game_green
psql racing_game_green -f migration.sql

# 3. 数据同步 (使用逻辑复制)
psql racing_game_blue -c "CREATE PUBLICATION migration_pub FOR ALL TABLES;"
psql racing_game_green -c "CREATE SUBSCRIPTION migration_sub CONNECTION 'host=localhost dbname=racing_game_blue' PUBLICATION migration_pub;"

# 4. 等待数据同步完成
while [ $(psql racing_game_green -t -c "SELECT count(*) FROM pg_subscription_rel WHERE srsubstate != 'r';") -gt 0 ]; do
    echo "等待数据同步..."
    sleep 5
done

# 5. 切换应用到绿环境
# 更新应用配置指向 racing_game_green

# 6. 验证切换成功后删除蓝环境
# dropdb racing_game_blue
```

## 🚨 风险控制和回滚

### 迁移风险评估
```
高风险迁移:
- 删除表或列
- 修改数据类型
- 添加NOT NULL约束
- 大表的结构变更

中风险迁移:
- 添加索引
- 数据格式转换
- 批量数据更新

低风险迁移:
- 添加新表
- 添加可空列
- 添加新索引 (CONCURRENTLY)
```

### 自动回滚脚本
```bash
#!/bin/bash
# 自动回滚脚本

MIGRATION_ID=$1
BACKUP_FILE="backup_before_migration_${MIGRATION_ID}_$(date +%Y%m%d_%H%M%S).sql"

echo "开始回滚迁移: $MIGRATION_ID"

# 1. 检查备份文件是否存在
if [ ! -f "$BACKUP_FILE" ]; then
    echo "❌ 备份文件不存在: $BACKUP_FILE"
    exit 1
fi

# 2. 停止应用服务
systemctl stop racing-game

# 3. 恢复数据库
echo "恢复数据库从备份: $BACKUP_FILE"
psql -h $DB_HOST -U $DB_USER -d $DB_NAME < $BACKUP_FILE

# 4. 验证数据完整性
echo "验证数据完整性..."
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "
SELECT 
    schemaname,
    tablename,
    n_tup_ins as inserts,
    n_tup_upd as updates,
    n_tup_del as deletes
FROM pg_stat_user_tables 
ORDER BY schemaname, tablename;
"

# 5. 重启应用服务
systemctl start racing-game

# 6. 健康检查
sleep 10
curl -f http://localhost:4000/health || {
    echo "❌ 应用健康检查失败"
    exit 1
}

echo "✅ 回滚完成"
```

## 📊 迁移监控

### 迁移进度监控
```sql
-- 监控长时间运行的迁移
SELECT 
    pid,
    now() - pg_stat_activity.query_start AS duration,
    query 
FROM pg_stat_activity 
WHERE (now() - pg_stat_activity.query_start) > interval '5 minutes'
AND state = 'active';

-- 监控锁等待
SELECT 
    blocked_locks.pid AS blocked_pid,
    blocked_activity.usename AS blocked_user,
    blocking_locks.pid AS blocking_pid,
    blocking_activity.usename AS blocking_user,
    blocked_activity.query AS blocked_statement,
    blocking_activity.query AS current_statement_in_blocking_process
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
JOIN pg_catalog.pg_locks blocking_locks ON blocking_locks.locktype = blocked_locks.locktype
JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
WHERE NOT blocked_locks.granted;

-- 监控表大小变化
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

### 性能影响监控
```bash
#!/bin/bash
# 迁移性能监控脚本

echo "=== 迁移性能监控 ==="

# 监控CPU使用率
echo "CPU使用率:"
top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//'

# 监控内存使用
echo "内存使用:"
free -h | grep Mem | awk '{print "使用: " $3 "/" $2 " (" $3/$2*100 "%)"}'

# 监控磁盘I/O
echo "磁盘I/O:"
iostat -x 1 1 | grep -E "(Device|sda|nvme)"

# 监控数据库连接数
echo "数据库连接数:"
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -t -c "
SELECT 
    count(*) as total_connections,
    count(*) FILTER (WHERE state = 'active') as active_connections,
    count(*) FILTER (WHERE state = 'idle') as idle_connections
FROM pg_stat_activity;
"

# 监控慢查询
echo "慢查询 (>1秒):"
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "
SELECT 
    query_start,
    now() - query_start as duration,
    left(query, 100) as query_preview
FROM pg_stat_activity 
WHERE now() - query_start > interval '1 second'
AND state = 'active'
ORDER BY duration DESC;
"
```

## 📋 迁移最佳实践

### 开发阶段最佳实践
```elixir
# 1. 使用事务包装迁移
defmodule MyApp.Repo.Migrations.SafeMigration do
  use Ecto.Migration

  def change do
    # 所有操作在一个事务中
    create table(:new_table) do
      add :name, :string, null: false
      timestamps()
    end
    
    create unique_index(:new_table, [:name])
  end
end

# 2. 提供回滚方法
defmodule MyApp.Repo.Migrations.ReversibleMigration do
  use Ecto.Migration

  def up do
    create table(:temp_table) do
      add :data, :text
    end
  end

  def down do
    drop table(:temp_table)
  end
end

# 3. 数据验证
defmodule MyApp.Repo.Migrations.ValidatedMigration do
  use Ecto.Migration

  def up do
    alter table(:users) do
      add :age, :integer
    end
    
    # 验证数据
    execute "UPDATE users SET age = 0 WHERE age IS NULL"
    
    alter table(:users) do
      modify :age, :integer, null: false
    end
  end
end
```

### 生产环境最佳实践
```bash
# 1. 迁移前完整备份
pg_dump -h $DB_HOST -U $DB_USER -d $DB_NAME -f "backup_$(date +%Y%m%d_%H%M%S).sql"

# 2. 在维护窗口执行
# 选择业务低峰期执行迁移

# 3. 分阶段执行
# 将大的迁移拆分为多个小的迁移

# 4. 监控和告警
# 设置迁移过程的监控和告警

# 5. 准备回滚计划
# 每个迁移都要有对应的回滚计划
```

---

**文档版本**: v1.0  
**创建日期**: 2024-12-19  
**最后更新**: 2024-12-19  
**维护团队**: Racing Game Development Team
