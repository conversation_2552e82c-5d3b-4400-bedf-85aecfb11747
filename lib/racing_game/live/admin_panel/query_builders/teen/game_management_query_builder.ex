defmodule RacingGame.Live.AdminPanel.QueryBuilders.Teen.GameManagementQueryBuilder do
  @moduledoc """
  🎮 游戏管理查询构建器

  负责构建游戏管理相关的复杂查询，包括：
  - 平台配置查询
  - VIP等级查询
  - 机器人配置查询
  - 跨仓储统计查询
  - 业务逻辑查询
  - 性能优化查询
  """

  require Logger
  import Ash.Query

  alias RacingGame.Live.AdminPanel.Repositories.Teen.GameManagement.{
    PlatformRepository,
    VipLevelRepository,
    RobotConfigRepository
  }

  # ==================== 平台配置查询 ====================

  @doc """
  构建平台配置查询

  ## 参数
  - `query_type` - 查询类型
  - `params` - 查询参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, result}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_platform_query(query_type, params \\ %{}, options \\ [])

  def build_platform_query(:list, params, options) do
    Logger.info("🏢 [游戏管理查询] 构建平台列表查询: #{inspect(params)}")
    PlatformRepository.list_platforms(params, options)
  end

  def build_platform_query(:active, params, options) do
    Logger.info("🏢 [游戏管理查询] 构建活跃平台查询")
    PlatformRepository.list_active_platforms(options)
  end

  def build_platform_query(:by_number, params, options) do
    platform_number = Map.get(params, "platform_number")
    Logger.info("🏢 [游戏管理查询] 根据编号查询平台: #{platform_number}")
    PlatformRepository.get_platform_by_number(platform_number, options)
  end

  def build_platform_query(:stats, params, options) do
    Logger.info("📊 [游戏管理查询] 构建平台统计查询")
    build_platform_stats_query(params, options)
  end

  def build_platform_query(query_type, _params, _options) do
    Logger.error("❌ [游戏管理查询] 不支持的平台查询类型: #{query_type}")
    {:error, :unsupported_platform_query_type}
  end

  # ==================== VIP等级查询 ====================

  @doc """
  构建VIP等级查询

  ## 参数
  - `query_type` - 查询类型
  - `params` - 查询参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, result}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_vip_level_query(query_type, params \\ %{}, options \\ [])

  def build_vip_level_query(:list, params, options) do
    Logger.info("👑 [游戏管理查询] 构建VIP等级列表查询: #{inspect(params)}")
    VipLevelRepository.list_vip_levels(params, options)
  end

  def build_vip_level_query(:active, params, options) do
    Logger.info("👑 [游戏管理查询] 构建活跃VIP等级查询")
    VipLevelRepository.list_active_vip_levels(options)
  end

  def build_vip_level_query(:by_level, params, options) do
    level = Map.get(params, "level")
    Logger.info("👑 [游戏管理查询] 根据等级查询VIP: #{level}")
    VipLevelRepository.get_vip_level_by_level(level, options)
  end

  def build_vip_level_query(:calculate_level, params, options) do
    total_recharge = Map.get(params, "total_recharge")
    Logger.info("🧮 [游戏管理查询] 计算VIP等级，充值: #{total_recharge}")
    VipLevelRepository.calculate_vip_level_by_recharge(total_recharge, options)
  end

  def build_vip_level_query(:privileges, params, options) do
    level = Map.get(params, "level")
    Logger.info("🎁 [游戏管理查询] 获取VIP特权: #{level}")
    VipLevelRepository.get_vip_privileges(level, options)
  end

  def build_vip_level_query(query_type, _params, _options) do
    Logger.error("❌ [游戏管理查询] 不支持的VIP查询类型: #{query_type}")
    {:error, :unsupported_vip_query_type}
  end

  # ==================== 机器人配置查询 ====================

  @doc """
  构建机器人配置查询

  ## 参数
  - `query_type` - 查询类型
  - `params` - 查询参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, result}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_robot_config_query(query_type, params \\ %{}, options \\ [])

  def build_robot_config_query(:list, params, options) do
    Logger.info("🤖 [游戏管理查询] 构建机器人配置列表查询: #{inspect(params)}")
    RobotConfigRepository.list_robot_configs(params, options)
  end

  def build_robot_config_query(:active, params, options) do
    Logger.info("🤖 [游戏管理查询] 构建活跃机器人查询")
    RobotConfigRepository.list_active_robot_configs(options)
  end

  def build_robot_config_query(:by_game_type, params, options) do
    game_type = Map.get(params, "game_type")
    Logger.info("🎮 [游戏管理查询] 根据游戏类型查询机器人: #{game_type}")
    RobotConfigRepository.list_robots_by_game_type(game_type, options)
  end

  def build_robot_config_query(:by_difficulty, params, options) do
    difficulty_level = Map.get(params, "difficulty_level")
    Logger.info("⚡ [游戏管理查询] 根据难度查询机器人: #{difficulty_level}")
    RobotConfigRepository.list_robots_by_difficulty(difficulty_level, options)
  end

  def build_robot_config_query(:select_for_user, params, options) do
    game_type = Map.get(params, "game_type")
    user_skill_level = Map.get(params, "user_skill_level")
    Logger.info("🎯 [游戏管理查询] 为用户选择机器人: #{game_type}, 技能: #{user_skill_level}")
    RobotConfigRepository.select_robot_for_user(game_type, user_skill_level, options)
  end

  def build_robot_config_query(:stats, params, options) do
    Logger.info("📊 [游戏管理查询] 构建机器人统计查询")
    RobotConfigRepository.get_robot_stats(options)
  end

  def build_robot_config_query(query_type, _params, _options) do
    Logger.error("❌ [游戏管理查询] 不支持的机器人查询类型: #{query_type}")
    {:error, :unsupported_robot_query_type}
  end

  # ==================== 跨仓储统计查询 ====================

  @doc """
  构建游戏管理综合统计查询

  ## 参数
  - `params` - 查询参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, stats}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_comprehensive_stats_query(params \\ %{}, options \\ []) do
    Logger.info("📊 [游戏管理查询] 构建综合统计查询: #{inspect(params)}")

    try do
      # 并行获取各项统计数据
      tasks = [
        Task.async(fn -> get_platform_summary(options) end),
        Task.async(fn -> get_vip_level_summary(options) end),
        Task.async(fn -> get_robot_config_summary(options) end),
        Task.async(fn -> get_game_type_distribution(options) end)
      ]

      results = Task.await_many(tasks, 10_000)

      case results do
        [
          {:ok, platform_stats},
          {:ok, vip_stats},
          {:ok, robot_stats},
          {:ok, game_distribution}
        ] ->
          comprehensive_stats = %{
            platform_summary: platform_stats,
            vip_level_summary: vip_stats,
            robot_config_summary: robot_stats,
            game_type_distribution: game_distribution,
            generated_at: DateTime.utc_now()
          }

          Logger.info("✅ [游戏管理查询] 综合统计查询完成")
          {:ok, comprehensive_stats}

        errors ->
          Logger.error("❌ [游戏管理查询] 综合统计查询失败: #{inspect(errors)}")
          {:error, :comprehensive_stats_query_failed}
      end
    rescue
      exception ->
        Logger.error("💥 [游戏管理查询] 综合统计查询异常: #{inspect(exception)}")
        {:error, :comprehensive_stats_query_exception}
    end
  end

  @doc """
  构建用户VIP信息查询

  ## 参数
  - `user_id` - 用户ID
  - `params` - 查询参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, user_vip_info}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_user_vip_info_query(user_id, params \\ %{}, options \\ []) do
    Logger.info("👤 [游戏管理查询] 构建用户VIP信息查询: #{user_id}")

    try do
      # 获取用户总充值金额（这里需要从用户系统获取）
      total_recharge = Map.get(params, "total_recharge", Decimal.new("0"))

      with {:ok, current_level} <- VipLevelRepository.calculate_vip_level_by_recharge(total_recharge, options),
           {:ok, current_privileges} <- VipLevelRepository.get_vip_privileges(current_level, options),
           {:ok, next_level_info} <- get_next_vip_level_info(current_level, options) do

        user_vip_info = %{
          user_id: user_id,
          current_level: current_level,
          current_privileges: current_privileges,
          total_recharge: total_recharge,
          next_level: next_level_info.level,
          next_level_requirement: next_level_info.requirement,
          recharge_needed: calculate_recharge_needed(total_recharge, next_level_info.requirement),
          progress_percentage: calculate_progress_percentage(total_recharge, current_level, next_level_info),
          generated_at: DateTime.utc_now()
        }

        Logger.info("✅ [游戏管理查询] 用户VIP信息查询完成: 等级#{current_level}")
        {:ok, user_vip_info}
      else
        error ->
          Logger.error("❌ [游戏管理查询] 用户VIP信息查询失败: #{inspect(error)}")
          error
      end
    rescue
      exception ->
        Logger.error("💥 [游戏管理查询] 用户VIP信息查询异常: #{inspect(exception)}")
        {:error, :user_vip_info_query_exception}
    end
  end

  @doc """
  构建游戏房间机器人配置查询

  ## 参数
  - `room_config` - 房间配置
  - `params` - 查询参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, robot_configs}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_room_robot_config_query(room_config, params \\ %{}, options \\ []) do
    Logger.info("🏠 [游戏管理查询] 构建房间机器人配置查询: #{inspect(room_config)}")

    game_type = Map.get(room_config, "game_type")
    room_level = Map.get(room_config, "room_level", 1)
    max_robots = Map.get(room_config, "max_robots", 4)

    try do
      with {:ok, available_robots} <- RobotConfigRepository.list_robots_by_game_type(game_type, options) do
        # 根据房间等级筛选合适的机器人
        suitable_robots = filter_robots_by_room_level(available_robots, room_level)

        # 随机选择指定数量的机器人
        selected_robots = Enum.take_random(suitable_robots, max_robots)

        room_robot_config = %{
          game_type: game_type,
          room_level: room_level,
          max_robots: max_robots,
          selected_robots: selected_robots,
          robot_count: length(selected_robots),
          average_difficulty: calculate_average_difficulty(selected_robots),
          average_win_rate: calculate_average_win_rate(selected_robots)
        }

        Logger.info("✅ [游戏管理查询] 房间机器人配置查询完成: #{length(selected_robots)}个机器人")
        {:ok, room_robot_config}
      else
        error ->
          Logger.error("❌ [游戏管理查询] 房间机器人配置查询失败: #{inspect(error)}")
          error
      end
    rescue
      exception ->
        Logger.error("💥 [游戏管理查询] 房间机器人配置查询异常: #{inspect(exception)}")
        {:error, :room_robot_config_query_exception}
    end
  end

  # ==================== 私有辅助函数 ====================

  # 构建平台统计查询
  defp build_platform_stats_query(params, options) do
    with {:ok, platforms_result} <- PlatformRepository.list_platforms(%{}, options) do
      platforms = platforms_result.platforms

      stats = %{
        total_count: length(platforms),
        active_count: Enum.count(platforms, & &1.status == 1),
        inactive_count: Enum.count(platforms, & &1.status == 0),
        agent_recharge_enabled: Enum.count(platforms, & &1.agent_recharge_switch == 1),
        agent_recharge_disabled: Enum.count(platforms, & &1.agent_recharge_switch == 0)
      }

      {:ok, stats}
    end
  end

  # 获取平台摘要统计
  defp get_platform_summary(options) do
    case PlatformRepository.list_active_platforms(options) do
      {:ok, platforms} ->
        summary = %{
          total_platforms: length(platforms),
          active_platforms: length(platforms),
          platform_names: Enum.map(platforms, & &1.platform_name)
        }
        {:ok, summary}

      error ->
        error
    end
  end

  # 获取VIP等级摘要统计
  defp get_vip_level_summary(options) do
    case VipLevelRepository.list_active_vip_levels(options) do
      {:ok, vip_levels} ->
        summary = %{
          total_levels: length(vip_levels),
          max_level: if(length(vip_levels) > 0, do: Enum.max_by(vip_levels, & &1.level).level, else: 0),
          level_names: Enum.map(vip_levels, & &1.level_name)
        }
        {:ok, summary}

      error ->
        error
    end
  end

  # 获取机器人配置摘要统计
  defp get_robot_config_summary(options) do
    case RobotConfigRepository.get_robot_stats(options) do
      {:ok, stats} -> {:ok, stats}
      error -> error
    end
  end

  # 获取游戏类型分布
  defp get_game_type_distribution(options) do
    case RobotConfigRepository.list_active_robot_configs(options) do
      {:ok, robots} ->
        distribution = Enum.group_by(robots, & &1.game_type)
        |> Enum.map(fn {game_type, robots} -> {game_type, length(robots)} end)
        |> Enum.into(%{})

        {:ok, distribution}

      error ->
        error
    end
  end

  # 获取下一个VIP等级信息
  defp get_next_vip_level_info(current_level, options) do
    case VipLevelRepository.list_active_vip_levels(options) do
      {:ok, vip_levels} ->
        next_level = Enum.find(vip_levels, fn level ->
          level.level > current_level
        end)

        case next_level do
          nil ->
            {:ok, %{level: current_level, requirement: Decimal.new("0")}}

          level ->
            {:ok, %{level: level.level, requirement: level.recharge_requirement}}
        end

      error ->
        error
    end
  end

  # 计算还需充值金额
  defp calculate_recharge_needed(current_recharge, next_requirement) do
    if Decimal.compare(next_requirement, current_recharge) == :gt do
      Decimal.sub(next_requirement, current_recharge)
    else
      Decimal.new("0")
    end
  end

  # 计算进度百分比
  defp calculate_progress_percentage(current_recharge, current_level, next_level_info) do
    if next_level_info.level == current_level do
      100.0
    else
      case Decimal.compare(next_level_info.requirement, Decimal.new("0")) do
        :gt ->
          percentage = Decimal.div(current_recharge, next_level_info.requirement)
          |> Decimal.mult(Decimal.new("100"))
          |> Decimal.to_float()

          min(percentage, 100.0)

        _ ->
          0.0
      end
    end
  end

  # 根据房间等级筛选机器人
  defp filter_robots_by_room_level(robots, room_level) do
    # 根据房间等级确定机器人难度范围
    difficulty_range = case room_level do
      1 -> [1, 2]      # 初级房间：简单和普通机器人
      2 -> [2, 3]      # 中级房间：普通和困难机器人
      3 -> [3, 4]      # 高级房间：困难和专家机器人
      _ -> [1, 2, 3, 4] # 其他：所有难度
    end

    Enum.filter(robots, fn robot ->
      robot.difficulty_level in difficulty_range and robot.status == 1
    end)
  end

  # 计算平均难度
  defp calculate_average_difficulty(robots) do
    if length(robots) > 0 do
      total_difficulty = Enum.sum(Enum.map(robots, & &1.difficulty_level))
      total_difficulty / length(robots)
    else
      0.0
    end
  end

  # 计算平均胜率
  defp calculate_average_win_rate(robots) do
    if length(robots) > 0 do
      total_win_rate = Enum.reduce(robots, Decimal.new("0"), fn robot, acc ->
        Decimal.add(acc, robot.win_rate)
      end)

      Decimal.div(total_win_rate, Decimal.new(length(robots)))
    else
      Decimal.new("0")
    end
  end

  # ==================== 高级业务查询 ====================

  @doc """
  构建平台健康状态查询

  ## 参数
  - `params` - 查询参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, health_status}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_platform_health_query(params \\ %{}, options \\ []) do
    Logger.info("🏥 [游戏管理查询] 构建平台健康状态查询")

    try do
      with {:ok, platforms_result} <- PlatformRepository.list_platforms(%{}, options) do
        platforms = platforms_result.platforms

        health_status = %{
          total_platforms: length(platforms),
          healthy_platforms: Enum.count(platforms, &is_platform_healthy?/1),
          warning_platforms: Enum.count(platforms, &is_platform_warning?/1),
          critical_platforms: Enum.count(platforms, &is_platform_critical?/1),
          platform_details: Enum.map(platforms, &build_platform_health_detail/1),
          overall_health_score: calculate_overall_health_score(platforms),
          recommendations: generate_platform_recommendations(platforms)
        }

        Logger.info("✅ [游戏管理查询] 平台健康状态查询完成")
        {:ok, health_status}
      end
    rescue
      exception ->
        Logger.error("💥 [游戏管理查询] 平台健康状态查询异常: #{inspect(exception)}")
        {:error, :platform_health_query_exception}
    end
  end

  @doc """
  构建VIP等级优化建议查询

  ## 参数
  - `params` - 查询参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, optimization_suggestions}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_vip_optimization_query(params \\ %{}, options \\ []) do
    Logger.info("💡 [游戏管理查询] 构建VIP等级优化建议查询")

    try do
      with {:ok, vip_levels} <- VipLevelRepository.list_active_vip_levels(options) do
        optimization_suggestions = %{
          level_gap_analysis: analyze_vip_level_gaps(vip_levels),
          recharge_threshold_analysis: analyze_recharge_thresholds(vip_levels),
          privilege_distribution: analyze_privilege_distribution(vip_levels),
          recommendations: generate_vip_optimization_recommendations(vip_levels),
          suggested_adjustments: suggest_vip_level_adjustments(vip_levels)
        }

        Logger.info("✅ [游戏管理查询] VIP等级优化建议查询完成")
        {:ok, optimization_suggestions}
      end
    rescue
      exception ->
        Logger.error("💥 [游戏管理查询] VIP等级优化建议查询异常: #{inspect(exception)}")
        {:error, :vip_optimization_query_exception}
    end
  end

  @doc """
  构建机器人性能分析查询

  ## 参数
  - `params` - 查询参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, performance_analysis}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_robot_performance_query(params \\ %{}, options \\ []) do
    Logger.info("⚡ [游戏管理查询] 构建机器人性能分析查询")

    try do
      with {:ok, robot_stats} <- RobotConfigRepository.get_robot_stats(options),
           {:ok, active_robots} <- RobotConfigRepository.list_active_robot_configs(options) do

        performance_analysis = %{
          overall_stats: robot_stats,
          win_rate_distribution: analyze_win_rate_distribution(active_robots),
          difficulty_balance: analyze_difficulty_balance(active_robots),
          game_type_coverage: analyze_game_type_coverage(active_robots),
          reaction_time_analysis: analyze_reaction_time_patterns(active_robots),
          performance_recommendations: generate_robot_performance_recommendations(active_robots),
          optimization_opportunities: identify_robot_optimization_opportunities(active_robots)
        }

        Logger.info("✅ [游戏管理查询] 机器人性能分析查询完成")
        {:ok, performance_analysis}
      end
    rescue
      exception ->
        Logger.error("💥 [游戏管理查询] 机器人性能分析查询异常: #{inspect(exception)}")
        {:error, :robot_performance_query_exception}
    end
  end

  @doc """
  构建游戏配置一致性检查查询

  ## 参数
  - `params` - 查询参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, consistency_report}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_config_consistency_query(params \\ %{}, options \\ []) do
    Logger.info("🔍 [游戏管理查询] 构建配置一致性检查查询")

    try do
      tasks = [
        Task.async(fn -> PlatformRepository.list_active_platforms(options) end),
        Task.async(fn -> VipLevelRepository.list_active_vip_levels(options) end),
        Task.async(fn -> RobotConfigRepository.list_active_robot_configs(options) end)
      ]

      case Task.await_many(tasks, 10_000) do
        [
          {:ok, platforms},
          {:ok, vip_levels},
          {:ok, robots}
        ] ->
          consistency_report = %{
            platform_consistency: check_platform_consistency(platforms),
            vip_level_consistency: check_vip_level_consistency(vip_levels),
            robot_config_consistency: check_robot_config_consistency(robots),
            cross_system_consistency: check_cross_system_consistency(platforms, vip_levels, robots),
            inconsistency_warnings: generate_inconsistency_warnings(platforms, vip_levels, robots),
            recommended_fixes: recommend_consistency_fixes(platforms, vip_levels, robots)
          }

          Logger.info("✅ [游戏管理查询] 配置一致性检查查询完成")
          {:ok, consistency_report}

        errors ->
          Logger.error("❌ [游戏管理查询] 配置一致性检查查询失败: #{inspect(errors)}")
          {:error, :config_consistency_query_failed}
      end
    rescue
      exception ->
        Logger.error("💥 [游戏管理查询] 配置一致性检查查询异常: #{inspect(exception)}")
        {:error, :config_consistency_query_exception}
    end
  end

  # ==================== 平台健康检查辅助函数 ====================

  # 判断平台是否健康
  defp is_platform_healthy?(platform) do
    platform.status == 1 and not is_nil(platform.platform_name) and platform.platform_name != ""
  end

  # 判断平台是否处于警告状态
  defp is_platform_warning?(platform) do
    platform.status == 1 and (is_nil(platform.platform_name) or platform.platform_name == "")
  end

  # 判断平台是否处于严重状态
  defp is_platform_critical?(platform) do
    platform.status == 0
  end

  # 构建平台健康详情
  defp build_platform_health_detail(platform) do
    %{
      id: platform.id,
      platform_name: platform.platform_name,
      platform_number: platform.platform_number,
      status: platform.status,
      health_status: determine_platform_health_status(platform),
      issues: identify_platform_issues(platform)
    }
  end

  # 确定平台健康状态
  defp determine_platform_health_status(platform) do
    cond do
      is_platform_healthy?(platform) -> :healthy
      is_platform_warning?(platform) -> :warning
      is_platform_critical?(platform) -> :critical
      true -> :unknown
    end
  end

  # 识别平台问题
  defp identify_platform_issues(platform) do
    issues = []

    issues = if platform.status == 0, do: ["平台已禁用" | issues], else: issues
    issues = if is_nil(platform.platform_name) or platform.platform_name == "", do: ["平台名称为空" | issues], else: issues
    issues = if is_nil(platform.platform_number), do: ["平台编号为空" | issues], else: issues

    issues
  end

  # 计算整体健康评分
  defp calculate_overall_health_score(platforms) do
    if length(platforms) == 0 do
      0.0
    else
      healthy_count = Enum.count(platforms, &is_platform_healthy?/1)
      (healthy_count / length(platforms)) * 100
    end
  end

  # 生成平台建议
  defp generate_platform_recommendations(platforms) do
    recommendations = []

    critical_count = Enum.count(platforms, &is_platform_critical?/1)
    warning_count = Enum.count(platforms, &is_platform_warning?/1)

    recommendations = if critical_count > 0, do: ["启用 #{critical_count} 个已禁用的平台" | recommendations], else: recommendations
    recommendations = if warning_count > 0, do: ["完善 #{warning_count} 个平台的配置信息" | recommendations], else: recommendations

    if length(recommendations) == 0 do
      ["所有平台配置正常"]
    else
      recommendations
    end
  end

  # ==================== VIP等级优化辅助函数 ====================

  # 分析VIP等级间隔
  defp analyze_vip_level_gaps(vip_levels) do
    sorted_levels = Enum.sort_by(vip_levels, & &1.level)

    gaps = Enum.zip(sorted_levels, tl(sorted_levels))
    |> Enum.map(fn {current, next} ->
      %{
        from_level: current.level,
        to_level: next.level,
        recharge_gap: Decimal.sub(next.recharge_requirement, current.recharge_requirement),
        level_gap: next.level - current.level
      }
    end)

    %{
      total_gaps: length(gaps),
      gaps: gaps,
      largest_gap: Enum.max_by(gaps, & &1.recharge_gap, fn -> %{recharge_gap: Decimal.new("0")} end),
      smallest_gap: Enum.min_by(gaps, & &1.recharge_gap, fn -> %{recharge_gap: Decimal.new("0")} end)
    }
  end

  # 分析充值阈值
  defp analyze_recharge_thresholds(vip_levels) do
    requirements = Enum.map(vip_levels, & &1.recharge_requirement)

    %{
      min_requirement: Enum.min(requirements, fn -> Decimal.new("0") end),
      max_requirement: Enum.max(requirements, fn -> Decimal.new("0") end),
      average_requirement: calculate_average_decimal(requirements),
      threshold_distribution: group_thresholds_by_range(requirements)
    }
  end

  # 分析特权分布
  defp analyze_privilege_distribution(vip_levels) do
    privilege_counts = Enum.map(vip_levels, fn level ->
      privilege_count = if level.privileges, do: length(level.privileges), else: 0
      %{level: level.level, privilege_count: privilege_count}
    end)

    %{
      privilege_counts: privilege_counts,
      total_privileges: Enum.sum(Enum.map(privilege_counts, & &1.privilege_count)),
      average_privileges_per_level: calculate_average_privileges(privilege_counts),
      levels_without_privileges: Enum.count(privilege_counts, & &1.privilege_count == 0)
    }
  end

  # 生成VIP优化建议
  defp generate_vip_optimization_recommendations(vip_levels) do
    recommendations = []

    # 检查等级间隔
    gap_analysis = analyze_vip_level_gaps(vip_levels)
    if length(gap_analysis.gaps) > 0 do
      largest_gap = gap_analysis.largest_gap
      if Decimal.compare(largest_gap.recharge_gap, Decimal.new("10000")) == :gt do
        recommendations = ["考虑在等级#{largest_gap.from_level}和#{largest_gap.to_level}之间添加中间等级" | recommendations]
      end
    end

    # 检查特权分布
    privilege_analysis = analyze_privilege_distribution(vip_levels)
    if privilege_analysis.levels_without_privileges > 0 do
      recommendations = ["为#{privilege_analysis.levels_without_privileges}个等级添加专属特权" | recommendations]
    end

    if length(recommendations) == 0 do
      ["VIP等级配置合理"]
    else
      recommendations
    end
  end

  # 建议VIP等级调整
  defp suggest_vip_level_adjustments(vip_levels) do
    adjustments = []

    # 基于间隔分析建议调整
    gap_analysis = analyze_vip_level_gaps(vip_levels)
    large_gaps = Enum.filter(gap_analysis.gaps, fn gap ->
      Decimal.compare(gap.recharge_gap, Decimal.new("5000")) == :gt
    end)

    adjustments = Enum.reduce(large_gaps, adjustments, fn gap, acc ->
      suggestion = %{
        type: :add_intermediate_level,
        between_levels: [gap.from_level, gap.to_level],
        suggested_level: gap.from_level + 1,
        suggested_requirement: Decimal.div(Decimal.add(gap.from_level, gap.to_level), Decimal.new("2"))
      }
      [suggestion | acc]
    end)

    adjustments
  end

  # ==================== 机器人性能分析辅助函数 ====================

  # 分析胜率分布
  defp analyze_win_rate_distribution(robots) do
    win_rates = Enum.map(robots, & &1.win_rate)

    %{
      min_win_rate: Enum.min(win_rates, fn -> Decimal.new("0") end),
      max_win_rate: Enum.max(win_rates, fn -> Decimal.new("100") end),
      average_win_rate: calculate_average_decimal(win_rates),
      win_rate_ranges: group_win_rates_by_range(win_rates),
      balanced_robots: Enum.count(robots, &is_win_rate_balanced?/1),
      unbalanced_robots: Enum.count(robots, &(!is_win_rate_balanced?(&1)))
    }
  end

  # 分析难度平衡
  defp analyze_difficulty_balance(robots) do
    difficulty_distribution = Enum.group_by(robots, & &1.difficulty_level)
    |> Enum.map(fn {level, robots} -> {level, length(robots)} end)
    |> Enum.into(%{})

    %{
      difficulty_distribution: difficulty_distribution,
      most_common_difficulty: find_most_common_difficulty(difficulty_distribution),
      least_common_difficulty: find_least_common_difficulty(difficulty_distribution),
      balance_score: calculate_difficulty_balance_score(difficulty_distribution)
    }
  end

  # 分析游戏类型覆盖
  defp analyze_game_type_coverage(robots) do
    game_type_distribution = Enum.group_by(robots, & &1.game_type)
    |> Enum.map(fn {game_type, robots} -> {game_type, length(robots)} end)
    |> Enum.into(%{})

    %{
      game_type_distribution: game_type_distribution,
      covered_game_types: Map.keys(game_type_distribution),
      total_game_types: length(Map.keys(game_type_distribution)),
      robots_per_game_type: game_type_distribution,
      underserved_game_types: find_underserved_game_types(game_type_distribution)
    }
  end

  # 分析反应时间模式
  defp analyze_reaction_time_patterns(robots) do
    reaction_times = Enum.map(robots, fn robot ->
      %{
        min: robot.reaction_time_min,
        max: robot.reaction_time_max,
        average: (robot.reaction_time_min + robot.reaction_time_max) / 2
      }
    end)

    %{
      min_reaction_time: Enum.min(Enum.map(reaction_times, & &1.min)),
      max_reaction_time: Enum.max(Enum.map(reaction_times, & &1.max)),
      average_reaction_time: Enum.sum(Enum.map(reaction_times, & &1.average)) / length(reaction_times),
      reaction_time_ranges: group_reaction_times_by_range(reaction_times)
    }
  end

  # 生成机器人性能建议
  defp generate_robot_performance_recommendations(robots) do
    recommendations = []

    # 检查胜率分布
    win_rate_analysis = analyze_win_rate_distribution(robots)
    unbalanced_count = win_rate_analysis.unbalanced_robots
    if unbalanced_count > 0 do
      recommendations = ["调整#{unbalanced_count}个机器人的胜率以提高平衡性" | recommendations]
    end

    # 检查难度分布
    difficulty_analysis = analyze_difficulty_balance(robots)
    if difficulty_analysis.balance_score < 0.7 do
      recommendations = ["重新分配机器人难度等级以提高平衡性" | recommendations]
    end

    # 检查游戏类型覆盖
    coverage_analysis = analyze_game_type_coverage(robots)
    underserved_count = length(coverage_analysis.underserved_game_types)
    if underserved_count > 0 do
      recommendations = ["为#{underserved_count}个游戏类型增加更多机器人配置" | recommendations]
    end

    if length(recommendations) == 0 do
      ["机器人性能配置良好"]
    else
      recommendations
    end
  end

  # 识别机器人优化机会
  defp identify_robot_optimization_opportunities(robots) do
    opportunities = []

    # 识别胜率过高或过低的机器人
    extreme_win_rate_robots = Enum.filter(robots, fn robot ->
      win_rate_float = Decimal.to_float(robot.win_rate)
      win_rate_float < 30.0 or win_rate_float > 80.0
    end)

    if length(extreme_win_rate_robots) > 0 do
      opportunities = [%{
        type: :adjust_win_rates,
        description: "调整#{length(extreme_win_rate_robots)}个机器人的极端胜率",
        affected_robots: Enum.map(extreme_win_rate_robots, & &1.id)
      } | opportunities]
    end

    # 识别反应时间异常的机器人
    abnormal_reaction_robots = Enum.filter(robots, fn robot ->
      robot.reaction_time_min > robot.reaction_time_max or
      robot.reaction_time_min < 100 or
      robot.reaction_time_max > 10000
    end)

    if length(abnormal_reaction_robots) > 0 do
      opportunities = [%{
        type: :fix_reaction_times,
        description: "修复#{length(abnormal_reaction_robots)}个机器人的异常反应时间",
        affected_robots: Enum.map(abnormal_reaction_robots, & &1.id)
      } | opportunities]
    end

    opportunities
  end

  # ==================== 配置一致性检查辅助函数 ====================

  # 检查平台一致性
  defp check_platform_consistency(platforms) do
    issues = []

    # 检查重复的平台编号
    platform_numbers = Enum.map(platforms, & &1.platform_number)
    duplicate_numbers = platform_numbers -- Enum.uniq(platform_numbers)
    issues = if length(duplicate_numbers) > 0, do: ["发现重复的平台编号: #{inspect(duplicate_numbers)}" | issues], else: issues

    # 检查空的平台名称
    empty_name_count = Enum.count(platforms, fn p -> is_nil(p.platform_name) or p.platform_name == "" end)
    issues = if empty_name_count > 0, do: ["#{empty_name_count}个平台缺少名称" | issues], else: issues

    %{
      is_consistent: length(issues) == 0,
      issues: issues,
      total_platforms: length(platforms)
    }
  end

  # 检查VIP等级一致性
  defp check_vip_level_consistency(vip_levels) do
    issues = []

    # 检查等级序列是否连续
    levels = Enum.map(vip_levels, & &1.level) |> Enum.sort()
    expected_levels = if length(levels) > 0, do: Enum.to_list(1..Enum.max(levels)), else: []
    missing_levels = expected_levels -- levels
    issues = if length(missing_levels) > 0, do: ["缺少VIP等级: #{inspect(missing_levels)}" | issues], else: issues

    # 检查充值要求是否递增
    sorted_by_level = Enum.sort_by(vip_levels, & &1.level)
    non_increasing_pairs = Enum.zip(sorted_by_level, tl(sorted_by_level))
    |> Enum.filter(fn {current, next} ->
      Decimal.compare(current.recharge_requirement, next.recharge_requirement) != :lt
    end)

    issues = if length(non_increasing_pairs) > 0, do: ["VIP等级充值要求未递增" | issues], else: issues

    %{
      is_consistent: length(issues) == 0,
      issues: issues,
      total_levels: length(vip_levels)
    }
  end

  # 检查机器人配置一致性
  defp check_robot_config_consistency(robots) do
    issues = []

    # 检查反应时间配置
    invalid_reaction_time_robots = Enum.filter(robots, fn robot ->
      robot.reaction_time_min > robot.reaction_time_max
    end)
    issues = if length(invalid_reaction_time_robots) > 0, do: ["#{length(invalid_reaction_time_robots)}个机器人反应时间配置错误" | issues], else: issues

    # 检查胜率范围
    invalid_win_rate_robots = Enum.filter(robots, fn robot ->
      win_rate_float = Decimal.to_float(robot.win_rate)
      win_rate_float < 0 or win_rate_float > 100
    end)
    issues = if length(invalid_win_rate_robots) > 0, do: ["#{length(invalid_win_rate_robots)}个机器人胜率超出有效范围" | issues], else: issues

    # 检查难度等级
    invalid_difficulty_robots = Enum.filter(robots, fn robot ->
      robot.difficulty_level < 1 or robot.difficulty_level > 4
    end)
    issues = if length(invalid_difficulty_robots) > 0, do: ["#{length(invalid_difficulty_robots)}个机器人难度等级无效" | issues], else: issues

    %{
      is_consistent: length(issues) == 0,
      issues: issues,
      total_robots: length(robots)
    }
  end

  # 检查跨系统一致性
  defp check_cross_system_consistency(platforms, vip_levels, robots) do
    issues = []

    # 检查是否有足够的机器人支持所有游戏类型
    game_types_in_config = get_configured_game_types()
    robot_game_types = Enum.map(robots, & &1.game_type) |> Enum.uniq()
    unsupported_game_types = game_types_in_config -- robot_game_types
    issues = if length(unsupported_game_types) > 0, do: ["游戏类型#{inspect(unsupported_game_types)}缺少机器人配置" | issues], else: issues

    # 检查平台数量与VIP等级的合理性
    if length(platforms) > 0 and length(vip_levels) == 0 do
      issues = ["有平台配置但缺少VIP等级配置" | issues]
    end

    %{
      is_consistent: length(issues) == 0,
      issues: issues
    }
  end

  # 生成不一致性警告
  defp generate_inconsistency_warnings(platforms, vip_levels, robots) do
    warnings = []

    # 平台相关警告
    inactive_platforms = Enum.count(platforms, & &1.status == 0)
    warnings = if inactive_platforms > length(platforms) / 2, do: ["超过一半的平台处于禁用状态" | warnings], else: warnings

    # VIP等级相关警告
    if length(vip_levels) < 3 do
      warnings = ["VIP等级数量过少，建议至少设置3个等级" | warnings]
    end

    # 机器人相关警告
    inactive_robots = Enum.count(robots, & &1.status == 0)
    warnings = if inactive_robots > length(robots) / 3, do: ["超过三分之一的机器人处于禁用状态" | warnings], else: warnings

    warnings
  end

  # 推荐一致性修复方案
  defp recommend_consistency_fixes(platforms, vip_levels, robots) do
    fixes = []

    # 平台修复建议
    platform_consistency = check_platform_consistency(platforms)
    if not platform_consistency.is_consistent do
      fixes = ["修复平台配置问题：#{Enum.join(platform_consistency.issues, "，")}" | fixes]
    end

    # VIP等级修复建议
    vip_consistency = check_vip_level_consistency(vip_levels)
    if not vip_consistency.is_consistent do
      fixes = ["修复VIP等级配置问题：#{Enum.join(vip_consistency.issues, "，")}" | fixes]
    end

    # 机器人修复建议
    robot_consistency = check_robot_config_consistency(robots)
    if not robot_consistency.is_consistent do
      fixes = ["修复机器人配置问题：#{Enum.join(robot_consistency.issues, "，")}" | fixes]
    end

    if length(fixes) == 0 do
      ["所有配置一致性良好"]
    else
      fixes
    end
  end

  # ==================== 通用辅助函数 ====================

  # 计算平均Decimal值
  defp calculate_average_decimal(decimal_list) do
    if length(decimal_list) > 0 do
      total = Enum.reduce(decimal_list, Decimal.new("0"), &Decimal.add/2)
      Decimal.div(total, Decimal.new(length(decimal_list)))
    else
      Decimal.new("0")
    end
  end

  # 计算平均特权数量
  defp calculate_average_privileges(privilege_counts) do
    if length(privilege_counts) > 0 do
      total = Enum.sum(Enum.map(privilege_counts, & &1.privilege_count))
      total / length(privilege_counts)
    else
      0.0
    end
  end

  # 按范围分组阈值
  defp group_thresholds_by_range(requirements) do
    Enum.group_by(requirements, fn req ->
      req_float = Decimal.to_float(req)
      cond do
        req_float < 1000 -> "低额 (<1000)"
        req_float < 5000 -> "中额 (1000-5000)"
        req_float < 10000 -> "高额 (5000-10000)"
        true -> "超高额 (>10000)"
      end
    end)
    |> Enum.map(fn {range, reqs} -> {range, length(reqs)} end)
    |> Enum.into(%{})
  end

  # 按范围分组胜率
  defp group_win_rates_by_range(win_rates) do
    Enum.group_by(win_rates, fn rate ->
      rate_float = Decimal.to_float(rate)
      cond do
        rate_float < 40 -> "低胜率 (<40%)"
        rate_float < 60 -> "平衡胜率 (40-60%)"
        rate_float < 80 -> "高胜率 (60-80%)"
        true -> "极高胜率 (>80%)"
      end
    end)
    |> Enum.map(fn {range, rates} -> {range, length(rates)} end)
    |> Enum.into(%{})
  end

  # 判断胜率是否平衡
  defp is_win_rate_balanced?(robot) do
    win_rate_float = Decimal.to_float(robot.win_rate)
    win_rate_float >= 40.0 and win_rate_float <= 70.0
  end

  # 找到最常见的难度
  defp find_most_common_difficulty(difficulty_distribution) do
    if map_size(difficulty_distribution) > 0 do
      Enum.max_by(difficulty_distribution, fn {_level, count} -> count end)
    else
      {0, 0}
    end
  end

  # 找到最少见的难度
  defp find_least_common_difficulty(difficulty_distribution) do
    if map_size(difficulty_distribution) > 0 do
      Enum.min_by(difficulty_distribution, fn {_level, count} -> count end)
    else
      {0, 0}
    end
  end

  # 计算难度平衡评分
  defp calculate_difficulty_balance_score(difficulty_distribution) do
    if map_size(difficulty_distribution) == 0 do
      0.0
    else
      counts = Map.values(difficulty_distribution)
      max_count = Enum.max(counts)
      min_count = Enum.min(counts)

      if max_count == 0 do
        0.0
      else
        min_count / max_count
      end
    end
  end

  # 找到服务不足的游戏类型
  defp find_underserved_game_types(game_type_distribution) do
    min_robots_per_type = 2

    Enum.filter(game_type_distribution, fn {_game_type, count} ->
      count < min_robots_per_type
    end)
    |> Enum.map(fn {game_type, _count} -> game_type end)
  end

  # 按范围分组反应时间
  defp group_reaction_times_by_range(reaction_times) do
    Enum.group_by(reaction_times, fn time ->
      avg_time = time.average
      cond do
        avg_time < 1000 -> "快速 (<1s)"
        avg_time < 2000 -> "正常 (1-2s)"
        avg_time < 3000 -> "较慢 (2-3s)"
        true -> "很慢 (>3s)"
      end
    end)
    |> Enum.map(fn {range, times} -> {range, length(times)} end)
    |> Enum.into(%{})
  end

  # 获取配置的游戏类型
  defp get_configured_game_types do
    # 这里应该从配置文件或其他地方获取支持的游戏类型
    # 暂时返回一些常见的游戏类型
    ["teen_patti", "longhu", "slot777", "explorer", "jhandi_munda", "pot_blind", "safari_of_wealth"]
  end
end
