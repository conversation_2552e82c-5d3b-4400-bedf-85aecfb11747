defmodule RacingGame.Live.AdminPanel.SubordinateManagementComponent do
  @moduledoc """
  下线管理组件 - 仅限代理使用
  """
  use CypridinaWeb, :live_component

  alias CypridinaWeb.AuthHelper
  alias Cypridina.Accounts.{User, AgentRelationship}
  alias RacingGame.Live.AdminPanel.PermissionFilter
  alias Cypridina.Utils.TimeHelper
  alias CypridinaWeb.Components.PointsHistoryComponent
  require Ash.Query

  def update(assigns, socket) do
    socket =
      socket
      |> assign(assigns)
      |> assign_defaults()
      |> load_subordinates_data()

    {:ok, socket}
  end

  defp assign_defaults(socket) do
    socket
    |> assign(:show_create_modal, false)
    |> assign(:create_form, %{})
    |> assign(:search_query, "")
    |> assign(:page, 1)
    |> assign(:per_page, 20)
    |> assign(:page_info, nil)
    |> assign(:show_reset_password_modal, false)
    |> assign(:show_transfer_modal, false)
    |> assign(:show_commission_modal, false)
    |> assign(:show_reject_modal, false)
    |> assign(:show_refund_requests_modal, false)
    |> assign(:show_refund_reject_modal, false)
    |> assign(:selected_subordinate, nil)
    |> assign(:selected_request, nil)
    |> assign(:selected_refund_request, nil)
    |> assign(:pending_refund_requests, [])
    |> assign(:pending_refund_requests_count, 0)
  end

  def handle_event("refresh", _params, socket) do
    socket = load_subordinates_data(socket)
    {:noreply, socket}
  end

  def handle_event("search", %{"search" => %{"query" => query}}, socket) do
    socket =
      socket
      |> assign(:search_query, query)
      |> assign(:page, 1)
      |> load_subordinates_data()

    {:noreply, socket}
  end

  def handle_event("clear_search", _params, socket) do
    socket =
      socket
      |> assign(:search_query, "")
      |> assign(:page, 1)
      |> load_subordinates_data()

    {:noreply, socket}
  end

  def handle_event("page_change", %{"page" => page}, socket) do
    page = String.to_integer(page)

    socket =
      socket
      |> assign(:page, page)
      |> load_subordinates_data()

    {:noreply, socket}
  end

  def handle_event("show_create_modal", _params, socket) do
    socket =
      socket
      |> assign(:show_create_modal, true)
      |> assign(:create_form, %{
        "username" => "",
        "password" => "",
        "password_confirmation" => "",
        "is_agent" => "false",
        "commission_rate" => "5.0"
      })

    {:noreply, socket}
  end

  def handle_event("hide_create_modal", _params, socket) do
    {:noreply, assign(socket, :show_create_modal, false)}
  end

  def handle_event("create_subordinate", %{"subordinate" => subordinate_params}, socket) do
    user = socket.assigns.current_user

    case create_new_subordinate(user.id, subordinate_params) do
      {:ok, _result} ->
        send(self(), {:flash, :info, "下线创建成功"})

        socket =
          socket
          |> assign(:show_create_modal, false)
          |> load_subordinates_data()

        {:noreply, socket}

      {:error, _reason} ->
        send(self(), {:flash, :error, "下线创建失败，请检查输入信息"})
        {:noreply, socket}
    end
  end

  # 重置密码相关事件
  def handle_event("show_reset_password_modal", %{"user_id" => user_id}, socket) do
    subordinate = Enum.find(socket.assigns.subordinates_data, &(&1.id == user_id))

    socket =
      socket
      |> assign(:show_reset_password_modal, true)
      |> assign(:selected_subordinate, subordinate)

    {:noreply, socket}
  end

  def handle_event("hide_reset_password_modal", _params, socket) do
    socket =
      socket
      |> assign(:show_reset_password_modal, false)
      |> assign(:selected_subordinate, nil)

    {:noreply, socket}
  end

  def handle_event("reset_password", %{"password" => password_params}, socket) do
    subordinate = socket.assigns.selected_subordinate

    case reset_subordinate_password(subordinate.id, password_params) do
      {:ok, _} ->
        send(self(), {:flash, :info, "密码重置成功"})

        socket =
          socket
          |> assign(:show_reset_password_modal, false)
          |> assign(:selected_subordinate, nil)

        {:noreply, socket}

      {:error, _} ->
        send(self(), {:flash, :error, "密码重置失败"})
        {:noreply, socket}
    end
  end

  # 转账相关事件
  def handle_event("show_transfer_modal", %{"user_id" => user_id}, socket) do
    subordinate = Enum.find(socket.assigns.subordinates_data, &(&1.id == user_id))

    socket =
      socket
      |> assign(:show_transfer_modal, true)
      |> assign(:selected_subordinate, subordinate)

    {:noreply, socket}
  end

  def handle_event("hide_transfer_modal", _params, socket) do
    socket =
      socket
      |> assign(:show_transfer_modal, false)
      |> assign(:selected_subordinate, nil)

    {:noreply, socket}
  end

  def handle_event("transfer_points", %{"transfer" => transfer_params}, socket) do
    agent = socket.assigns.current_user
    subordinate = socket.assigns.selected_subordinate

    case transfer_points_to_subordinate(agent.id, subordinate.id, transfer_params) do
      {:ok, _} ->
        send(self(), {:flash, :info, "转账成功"})

        socket =
          socket
          |> assign(:show_transfer_modal, false)
          |> assign(:selected_subordinate, nil)
          |> load_subordinates_data()

        {:noreply, socket}

      {:error, reason} ->
        send(self(), {:flash, :error, "转账失败: #{reason}"})
        {:noreply, socket}
    end
  end

  # 调整抽水比例相关事件
  def handle_event("show_commission_modal", %{"user_id" => user_id}, socket) do
    subordinate = Enum.find(socket.assigns.subordinates_data, &(&1.id == user_id))

    socket =
      socket
      |> assign(:show_commission_modal, true)
      |> assign(:selected_subordinate, subordinate)

    {:noreply, socket}
  end

  def handle_event("hide_commission_modal", _params, socket) do
    socket =
      socket
      |> assign(:show_commission_modal, false)
      |> assign(:selected_subordinate, nil)

    {:noreply, socket}
  end

  def handle_event("update_commission", %{"commission" => commission_params}, socket) do
    agent = socket.assigns.current_user
    subordinate = socket.assigns.selected_subordinate

    case update_commission_rate(agent.id, subordinate.id, commission_params) do
      {:ok, _} ->
        send(self(), {:flash, :info, "抽水比例更新成功"})

        socket =
          socket
          |> assign(:show_commission_modal, false)
          |> assign(:selected_subordinate, nil)
          |> load_subordinates_data()

        {:noreply, socket}

      {:error, reason} ->
        error_msg = "抽水比例更新失败: #{inspect(reason)}"
        send(self(), {:flash, :error, error_msg})
        {:noreply, socket}
    end
  end



  # 退费请求相关事件
  def handle_event("show_refund_requests", _params, socket) do
    socket =
      socket
      |> assign(:show_refund_requests_modal, true)
      |> load_pending_refund_requests()

    {:noreply, socket}
  end

  def handle_event("hide_refund_requests_modal", _params, socket) do
    socket =
      socket
      |> assign(:show_refund_requests_modal, false)
      |> assign(:pending_refund_requests, [])

    {:noreply, socket}
  end

  # 批准退费请求
  def handle_event("approve_refund", %{"request_id" => request_id}, socket) do
    current_user = socket.assigns.current_user

    case Cypridina.Ledger.approve_refund_request(request_id, current_user.id) do
      {:ok, _transfer} ->
        send(self(), {:flash, :info, "退费请求批准成功"})

        socket =
          socket
          |> load_pending_refund_requests()
          |> load_pending_refund_requests_count()

        {:noreply, socket}

      {:error, error} ->
        error_message =
          case error do
            %Ash.Error.Invalid{} -> "批准失败，请检查请求状态"
            _ -> "批准失败，请稍后重试"
          end

        send(self(), {:flash, :error, error_message})
        {:noreply, socket}
    end
  end

  # 显示拒绝退费请求模态框
  def handle_event("show_refund_reject_modal", %{"request_id" => request_id}, socket) do
    request = Enum.find(socket.assigns.pending_refund_requests, &(&1.id == request_id))

    socket =
      socket
      |> assign(:show_refund_reject_modal, true)
      |> assign(:selected_refund_request, request)

    {:noreply, socket}
  end

  # 隐藏拒绝退费请求模态框
  def handle_event("hide_refund_reject_modal", _params, socket) do
    socket =
      socket
      |> assign(:show_refund_reject_modal, false)
      |> assign(:selected_refund_request, nil)

    {:noreply, socket}
  end

  # 拒绝退费请求
  def handle_event("reject_refund", %{"reject" => reject_params}, socket) do
    current_user = socket.assigns.current_user
    request = socket.assigns.selected_refund_request
    reject_reason = reject_params["reason"] || "无原因"

    case Cypridina.Ledger.reject_refund_request(request.id, current_user.id, reject_reason) do
      {:ok, _transfer} ->
        send(self(), {:flash, :info, "退费请求已拒绝，积分已返还给申请人"})

        socket =
          socket
          |> assign(:show_refund_reject_modal, false)
          |> assign(:selected_refund_request, nil)
          |> load_pending_refund_requests()
          |> load_pending_refund_requests_count()

        {:noreply, socket}

      {:error, error} ->
        error_message =
          case error do
            %Ash.Error.Invalid{} ->
              "拒绝失败，请检查请求状态"

            _ ->
              Logger.error("拒绝退费请求失败: #{inspect(error)}")
              "拒绝失败，请稍后重试"
          end

        send(self(), {:flash, :error, error_message})

        socket =
          socket
          |> assign(:show_refund_reject_modal, false)
          |> assign(:selected_refund_request, nil)

        {:noreply, socket}
    end
  end

  defp load_subordinates_data(socket) do
    user = socket.assigns.current_user
    search_query = socket.assigns.search_query || ""
    page = socket.assigns.page
    per_page = socket.assigns.per_page

    # 获取代理关系，然后获取下线用户信息
    {subordinates, total_count} = get_agent_subordinates(user.id, search_query, page, per_page)

    socket
    |> assign(:subordinates_data, subordinates)
    |> assign(:page_info, %{total_count: total_count, page: page, per_page: per_page})
    |> load_pending_refund_requests_count()
  end

  # 加载待处理退费请求
  defp load_pending_refund_requests(socket) do
    user = socket.assigns.current_user

    case Cypridina.Ledger.get_agent_refund_requests(user.id, :pending) do
      {:ok, requests} ->
        assign(socket, :pending_refund_requests, requests)

      {:error, _} ->
        assign(socket, :pending_refund_requests, [])
    end
  end

  # 加载待处理退费请求总数
  defp load_pending_refund_requests_count(socket) do
    user = socket.assigns.current_user

    case Cypridina.Ledger.get_agent_refund_requests(user.id, :pending) do
      {:ok, requests} ->
        assign(socket, :pending_refund_requests_count, length(requests))

      {:error, _} ->
        assign(socket, :pending_refund_requests_count, 0)
    end
  end

  # 获取代理的下线用户
  defp get_agent_subordinates(agent_id, search_query \\ "", page \\ 1, per_page \\ 20) do
    # 获取代理关系
    case Cypridina.Accounts.AgentRelationship
         |> Ash.Query.filter(agent_id == ^agent_id and status == 1)
         |> Ash.Query.load([:subordinate])
         |> Ash.read() do
      {:ok, relationships} ->
        # 转换为包含关系信息的数据结构
        all_subordinates =
          Enum.map(relationships, fn rel ->
            subordinate = rel.subordinate

            # 获取用户积分
            points = Cypridina.Accounts.get_user_points(subordinate.id)

            %{
              id: subordinate.id,
              username: subordinate.username,
              commission_rate: Decimal.mult(rel.commission_rate, 100) |> Decimal.to_float(),
              is_agent: subordinate.agent_level >= 0,
              agent_level: subordinate.agent_level,
              points: points,
              updated_at: subordinate.updated_at
            }
          end)

        # 应用搜索过滤
        filtered_subordinates =
          if search_query != "" do
            Enum.filter(all_subordinates, fn sub ->
              String.contains?(
                String.downcase(to_string(sub.username)),
                String.downcase(search_query)
              )
            end)
          else
            all_subordinates
          end

        # 按更新时间倒序排列
        sorted_subordinates =
          Enum.sort_by(filtered_subordinates, & &1.updated_at, {:desc, DateTime})

        # 计算总数
        total_count = length(sorted_subordinates)

        # 分页
        paginated_subordinates =
          sorted_subordinates
          |> Enum.drop((page - 1) * per_page)
          |> Enum.take(per_page)

        {paginated_subordinates, total_count}

      _ ->
        {[], 0}
    end
  end

  # 创建新的下线用户
  defp create_new_subordinate(agent_id, params) do
    # 准备用户参数
    is_agent = params["is_agent"] == "true"

    # 处理抽水比例，转换为 Decimal 类型
    commission_rate_str = params["commission_rate"] || "5.0"

    commission_rate =
      commission_rate_str
      |> Float.parse()
      |> elem(0)
      # 转换为小数（5.0% -> 0.05）
      |> Kernel./(100.0)
      |> Decimal.from_float()

    user_params = %{
      username: params["username"],
      password: params["password"],
      password_confirmation: params["password_confirmation"],
      permission_level: 0,
      # agent_level 将在 create_subordinate_user 中根据上线的级别自动计算
      # 如果是代理：上线的 agent_level + 1
      # 如果不是代理：-1
      agent_level: if(is_agent, do: :auto, else: -1),
      confirmed_at: DateTime.utc_now(),
      # 注意：commission_rate 不传递给用户创建，而是在创建代理关系时使用
      commission_rate: commission_rate
    }

    # 使用 Accounts 模块创建下线账号
    case Cypridina.Accounts.create_subordinate_account(agent_id, user_params) do
      {:ok, result} ->
        {:ok, result}

      {:error, reason} ->
        {:error, reason}
    end
  end

  # 重置下线密码
  defp reset_subordinate_password(user_id, password_params) do
    case User |> Ash.Query.filter(id == ^user_id) |> Ash.read_one() do
      {:ok, user} when not is_nil(user) ->
        # 验证密码和确认密码是否一致
        new_password = password_params["new_password"]
        password_confirmation = password_params["password_confirmation"]

        if new_password == password_confirmation do
          # 使用专门的管理员重置密码 action
          user
          |> Ash.Changeset.for_update(:admin_reset_password, %{
            password: new_password,
            password_confirmation: password_confirmation
          })
          |> Ash.update()
        else
          {:error, "密码和确认密码不一致"}
        end

      _ ->
        {:error, "用户不存在"}
    end
  end

  # 转账给下线
  defp transfer_points_to_subordinate(agent_id, subordinate_id, transfer_params) do
    amount = String.to_integer(transfer_params["amount"])
    reason = Map.get(transfer_params, "remarks", "代理转账给下线")

    # 使用统一的转账接口，这样会自动记录到points_transaction表
    case Cypridina.Accounts.transfer_points(agent_id, subordinate_id, amount, reason) do
      {:ok, _result} ->
        {:ok, "转账成功"}

      {:error, error_reason} ->
        {:error, error_reason}
    end
  end

  # 更新抽水比例
  defp update_commission_rate(agent_id, subordinate_id, commission_params) do
    try do
      # 获取输入的百分比值
      rate_percent = commission_params["rate"] |> Float.parse() |> elem(0)

      # 验证百分比范围（0-13%）
      if rate_percent < 0 or rate_percent > 13 do
        {:error, "抽水比例必须在0-13%之间"}
      else
        # 将百分比转换为小数，并转换为 Decimal 类型
        commission_rate =
          rate_percent
          |> Kernel./(100.0)
          |> Decimal.from_float()

        Cypridina.Accounts.set_commission_rate(agent_id, subordinate_id, commission_rate)
      end
    rescue
      e ->
        {:error, "参数格式错误: #{Exception.message(e)}"}
    end
  end

  def render(assigns) do
    ~H"""
    <div class="p-6">
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-xl font-semibold">下线管理</h2>
        <div class="flex items-center space-x-4">
          <div class="text-sm text-base-content/60">
            <%= if @page_info do %>
              共 {@page_info.total_count} 个下线，第 {@page_info.page} 页
            <% else %>
              加载中...
            <% end %>
          </div>
          <div class="flex gap-2">
            <button
              phx-click="show_refund_requests"
              phx-target={@myself}
              class="btn btn-warning btn-sm relative"
            >
              <.icon name="hero-arrow-uturn-left" class="w-4 h-4" /> 退费请求
              <%= if @pending_refund_requests_count > 0 do %>
                <span class="badge badge-error badge-xs absolute -top-1 -right-1">
                  {@pending_refund_requests_count}
                </span>
              <% end %>
            </button>
            <button phx-click="show_create_modal" phx-target={@myself} class="btn btn-primary btn-sm">
              <.icon name="hero-plus" class="w-4 h-4" /> 新增下线
            </button>
          </div>
        </div>
      </div>

    <!-- 搜索框 -->
      <div class="flex justify-between items-center mb-4">
        <form phx-submit="search" phx-target={@myself} class="flex space-x-2">
          <input
            type="text"
            name="search[query]"
            value={@search_query}
            placeholder="按用户名搜索..."
            class="input input-bordered input-sm w-64"
          />
          <button type="submit" class="btn btn-outline btn-sm">
            <.icon name="hero-magnifying-glass" class="w-4 h-4" /> 搜索
          </button>
        </form>
        <%= if @search_query != "" do %>
          <button phx-click="clear_search" phx-target={@myself} class="btn btn-ghost btn-sm">
            <.icon name="hero-x-mark" class="w-4 h-4" /> 清除搜索
          </button>
        <% end %>
      </div>

      <%= if @subordinates_data && length(@subordinates_data) > 0 do %>
        <div class="overflow-x-auto">
          <table class="table table-zebra w-full">
            <thead>
              <tr>
                <th>用户名</th>
                <th>身份</th>
                <th>积分余额</th>
                <th>抽水比例</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <%= for subordinate <- @subordinates_data do %>
                <tr>
                  <td class="font-medium">{to_string(subordinate.username || "N/A")}</td>
                  <td>
                    <%= if subordinate.is_agent do %>
                      <span class="badge badge-info">代理L{subordinate.agent_level}</span>
                    <% else %>
                      <span class="badge badge-ghost">普通用户</span>
                    <% end %>
                  </td>
                  <td class="font-medium text-primary">{subordinate.points} 积分</td>
                  <td class="font-medium">
                    {:erlang.float_to_binary(subordinate.commission_rate || 0.0, decimals: 2)}%
                  </td>
                  <td>
                    <div class="flex flex-wrap gap-1">
                      <button
                        phx-click="show_reset_password_modal"
                        phx-value-user_id={subordinate.id}
                        phx-target={@myself}
                        class="btn btn-ghost btn-xs"
                      >
                        重置密码
                      </button>
                      <button
                        phx-click="show_transfer_modal"
                        phx-value-user_id={subordinate.id}
                        phx-target={@myself}
                        class="btn btn-ghost btn-xs"
                      >
                        转账
                      </button>
                      <button
                        phx-click="show_commission_modal"
                        phx-value-user_id={subordinate.id}
                        phx-target={@myself}
                        class="btn btn-ghost btn-xs"
                      >
                        调整抽水
                      </button>
                      <.live_component
                        module={PointsHistoryComponent}
                        id={"points_history_#{subordinate.id}"}
                        user_id={subordinate.id}
                        current_user={@current_user}
                        show_admin_actions={true}
                        user_info={%{
                          username: subordinate.username,
                          numeric_id: subordinate.id,
                          current_points: subordinate.points
                        }}
                      />
                    </div>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>

    <!-- 分页组件 -->
        <%= if @page_info && @page_info.total_count > @page_info.per_page do %>
          <div class="flex justify-center mt-6">
            <div class="join">
              <%= if @page_info.page > 1 do %>
                <button
                  phx-click="page_change"
                  phx-value-page={@page_info.page - 1}
                  phx-target={@myself}
                  class="join-item btn btn-sm"
                >
                  «
                </button>
              <% end %>

              <%= for page_num <- max(1, @page_info.page - 2)..min(ceil(@page_info.total_count / @page_info.per_page), @page_info.page + 2) do %>
                <button
                  phx-click="page_change"
                  phx-value-page={page_num}
                  phx-target={@myself}
                  class={[
                    "join-item btn btn-sm",
                    if(page_num == @page_info.page, do: "btn-active", else: "")
                  ]}
                >
                  {page_num}
                </button>
              <% end %>

              <%= if @page_info.page < ceil(@page_info.total_count / @page_info.per_page) do %>
                <button
                  phx-click="page_change"
                  phx-value-page={@page_info.page + 1}
                  phx-target={@myself}
                  class="join-item btn btn-sm"
                >
                  »
                </button>
              <% end %>
            </div>
          </div>
        <% end %>
      <% else %>
        <div class="text-center py-12">
          <div class="text-base-content/40 mb-4">
            <.icon name="hero-user-group" class="w-16 h-16 mx-auto" />
          </div>
          <p class="text-base-content/60">暂无下线用户</p>
          <button
            phx-click="show_create_modal"
            phx-target={@myself}
            class="btn btn-primary btn-sm mt-4"
          >
            <.icon name="hero-plus" class="w-4 h-4" /> 新增第一个下线
          </button>
        </div>
      <% end %>

    <!-- 新增下线模态框 -->
      <%= if @show_create_modal do %>
        <div class="modal modal-open">
          <div class="modal-box w-11/12 max-w-md">
            <h3 class="font-bold text-lg mb-4">新增下线</h3>

            <form phx-submit="create_subordinate" phx-target={@myself} class="space-y-4">
              <!-- 用户名 -->
              <div class="form-control">
                <label class="label">
                  <span class="label-text">用户名 *</span>
                </label>
                <input
                  type="text"
                  name="subordinate[username]"
                  value={@create_form["username"]}
                  placeholder="请输入用户名"
                  class="input input-bordered"
                  required
                />
              </div>

    <!-- 密码 -->
              <div class="form-control">
                <label class="label">
                  <span class="label-text">密码 *</span>
                </label>
                <input
                  type="password"
                  name="subordinate[password]"
                  value={@create_form["password"]}
                  placeholder="请输入密码"
                  class="input input-bordered"
                  required
                />
              </div>

    <!-- 确认密码 -->
              <div class="form-control">
                <label class="label">
                  <span class="label-text">确认密码 *</span>
                </label>
                <input
                  type="password"
                  name="subordinate[password_confirmation]"
                  value={@create_form["password_confirmation"]}
                  placeholder="请再次输入密码"
                  class="input input-bordered"
                  required
                />
              </div>

    <!-- 身份选择 -->
              <div class="form-control">
                <label class="label">
                  <span class="label-text">身份</span>
                </label>
                <select name="subordinate[is_agent]" class="select select-bordered">
                  <option value="false" selected={@create_form["is_agent"] == "false"}>普通用户</option>
                  <option value="true" selected={@create_form["is_agent"] == "true"}>代理</option>
                </select>
              </div>

    <!-- 抽水比例 -->
              <div class="form-control">
                <label class="label">
                  <span class="label-text">抽水比例 (%)</span>
                  <span class="label-text-alt">0-13之间的数值</span>
                </label>
                <input
                  type="number"
                  name="subordinate[commission_rate]"
                  value={@create_form["commission_rate"]}
                  placeholder="5.0"
                  class="input input-bordered"
                  min="0"
                  max="13"
                  step="0.1"
                  required
                />
              </div>

              <div class="modal-action">
                <button
                  type="button"
                  phx-click="hide_create_modal"
                  phx-target={@myself}
                  class="btn btn-ghost"
                >
                  取消
                </button>
                <button type="submit" class="btn btn-primary">创建下线</button>
              </div>
            </form>
          </div>
        </div>
      <% end %>

    <!-- 重置密码模态框 -->
      <%= if @show_reset_password_modal do %>
        <div class="modal modal-open">
          <div class="modal-box w-11/12 max-w-md">
            <h3 class="font-bold text-lg mb-4">重置密码 - {@selected_subordinate.username}</h3>

            <form phx-submit="reset_password" phx-target={@myself} class="space-y-4">
              <div class="form-control">
                <label class="label">
                  <span class="label-text">新密码 *</span>
                </label>
                <input
                  type="password"
                  name="password[new_password]"
                  placeholder="请输入新密码"
                  class="input input-bordered"
                  required
                  minlength="6"
                />
              </div>

              <div class="form-control">
                <label class="label">
                  <span class="label-text">确认新密码 *</span>
                </label>
                <input
                  type="password"
                  name="password[password_confirmation]"
                  placeholder="请再次输入新密码"
                  class="input input-bordered"
                  required
                  minlength="6"
                />
              </div>

              <div class="modal-action">
                <button
                  type="button"
                  phx-click="hide_reset_password_modal"
                  phx-target={@myself}
                  class="btn btn-ghost"
                >
                  取消
                </button>
                <button type="submit" class="btn btn-primary">重置密码</button>
              </div>
            </form>
          </div>
        </div>
      <% end %>

    <!-- 转账模态框 -->
      <%= if @show_transfer_modal do %>
        <div class="modal modal-open">
          <div class="modal-box w-11/12 max-w-md">
            <h3 class="font-bold text-lg mb-4">转账给 {@selected_subordinate.username}</h3>
            <p class="text-sm text-base-content/60 mb-4">当前积分: {@selected_subordinate.points}</p>

            <form phx-submit="transfer_points" phx-target={@myself} class="space-y-4">
              <div class="form-control">
                <label class="label">
                  <span class="label-text">转账金额 *</span>
                </label>
                <input
                  type="number"
                  name="transfer[amount]"
                  placeholder="请输入转账金额"
                  class="input input-bordered"
                  required
                  min="1"
                />
              </div>

              <div class="form-control">
                <label class="label">
                  <span class="label-text">备注</span>
                </label>
                <textarea
                  name="transfer[remarks]"
                  placeholder="转账备注（可选）"
                  class="textarea textarea-bordered"
                  rows="3"
                ></textarea>
              </div>

              <div class="modal-action">
                <button
                  type="button"
                  phx-click="hide_transfer_modal"
                  phx-target={@myself}
                  class="btn btn-ghost"
                >
                  取消
                </button>
                <button type="submit" class="btn btn-primary">确认转账</button>
              </div>
            </form>
          </div>
        </div>
      <% end %>

    <!-- 调整抽水比例模态框 -->
      <%= if @show_commission_modal do %>
        <div class="modal modal-open">
          <div class="modal-box w-11/12 max-w-md">
            <h3 class="font-bold text-lg mb-4">调整抽水比例 - {@selected_subordinate.username}</h3>
            <p class="text-sm text-base-content/60 mb-4">
              当前抽水比例: {:erlang.float_to_binary(@selected_subordinate.commission_rate, decimals: 2)}%
            </p>

            <form phx-submit="update_commission" phx-target={@myself} class="space-y-4">
              <div class="form-control">
                <label class="label">
                  <span class="label-text">新抽水比例 (%) *</span>
                  <span class="label-text-alt">0-13之间的数值</span>
                </label>
                <input
                  type="number"
                  name="commission[rate]"
                  value={@selected_subordinate.commission_rate}
                  placeholder="5.0"
                  class="input input-bordered"
                  required
                  min="0"
                  max="13"
                  step="0.1"
                />
              </div>

              <div class="modal-action">
                <button
                  type="button"
                  phx-click="hide_commission_modal"
                  phx-target={@myself}
                  class="btn btn-ghost"
                >
                  取消
                </button>
                <button type="submit" class="btn btn-primary">更新比例</button>
              </div>
            </form>
          </div>
        </div>
      <% end %>







    <!-- 退费请求模态框 -->
      <%= if @show_refund_requests_modal do %>
        <div class="modal modal-open">
          <div class="modal-box points-history-modal-box overflow-hidden flex flex-col">
            <div class="flex justify-between items-center mb-4">
              <h3 class="font-bold text-lg">
                待处理退费请求
              </h3>
              <button
                type="button"
                phx-click="hide_refund_requests_modal"
                phx-target={@myself}
                class="btn btn-sm btn-circle btn-ghost"
              >
                ✕
              </button>
            </div>

    <!-- 退费请求列表 -->
            <div class="flex-1 overflow-hidden flex flex-col">
              <%= if length(@pending_refund_requests) > 0 do %>
                <div class="border rounded-lg overflow-hidden">
                  <div class="points-history-scroll points-history-table-container">
                    <table class="table table-zebra w-full text-sm points-history-table">
                      <thead class="bg-base-200">
                        <tr>
                          <th class="w-24">申请人</th>
                          <th class="w-20">金额</th>
                          <th class="w-28">申请时间</th>
                          <th class="w-32">申请原因</th>
                          <th class="w-16">状态</th>
                          <th class="w-24">操作</th>
                        </tr>
                      </thead>
                      <tbody>
                        <%= for request <- @pending_refund_requests do %>
                          <tr class="hover">
                            <!-- 申请人 -->
                            <td class="py-2">
                              <div class="font-medium">
                                {request.metadata["requester_username"] || "未知用户"}
                              </div>
                            </td>

    <!-- 金额 -->
                            <td class="py-2">
                              <div class="font-medium text-warning">
                                {Money.to_decimal(request.amount) |> Decimal.to_integer()} 积分
                              </div>
                            </td>

    <!-- 申请时间 -->
                            <td class="text-xs py-2">
                              <div class="font-mono">
                                {TimeHelper.format_local_datetime(request.timestamp)}
                              </div>
                            </td>

    <!-- 申请原因 -->
                            <td class="py-2">
                              <div class="text-xs">
                                {request.metadata["refund_reason"] || request.description || "无原因"}
                              </div>
                            </td>

    <!-- 状态 -->
                            <td class="py-2">
                              <div class="flex justify-center">
                                <div class="tooltip tooltip-top" data-tip="待审核">
                                  <.icon name="hero-clock" class="w-4 h-4 text-warning" />
                                </div>
                              </div>
                            </td>

    <!-- 操作 -->
                            <td class="py-2">
                              <div class="flex gap-1">
                                <button
                                  phx-click="approve_refund"
                                  phx-value-request_id={request.id}
                                  phx-target={@myself}
                                  class="btn btn-success btn-xs"
                                  data-confirm="确认批准这个退费请求吗？积分将保留在您的账户中。"
                                >
                                  批准
                                </button>
                                <button
                                  phx-click="show_refund_reject_modal"
                                  phx-value-request_id={request.id}
                                  phx-target={@myself}
                                  class="btn btn-error btn-xs"
                                >
                                  拒绝
                                </button>
                              </div>
                            </td>
                          </tr>
                        <% end %>
                      </tbody>
                    </table>
                  </div>
                </div>
              <% else %>
                <div class="text-center py-8">
                  <div class="text-base-content/40 mb-2">
                    <.icon name="hero-check-circle" class="w-12 h-12 mx-auto" />
                  </div>
                  <p class="text-base-content/60">暂无待处理的退费请求</p>
                </div>
              <% end %>
            </div>

            <div class="modal-action mt-4 pt-4 border-t">
              <button
                type="button"
                phx-click="hide_refund_requests_modal"
                phx-target={@myself}
                class="btn btn-ghost"
              >
                关闭
              </button>
            </div>
          </div>
        </div>
      <% end %>

    <!-- 拒绝退费请求模态框 -->
      <%= if @show_refund_reject_modal and @selected_refund_request do %>
        <div class="modal modal-open">
          <div class="modal-box w-11/12 max-w-md">
            <h3 class="font-bold text-lg mb-4">拒绝退费请求</h3>

    <!-- 请求详情 -->
            <div class="bg-base-200 p-4 rounded-lg mb-4">
              <div class="text-sm space-y-1">
                <div>
                  <strong>申请人:</strong> {@selected_refund_request.metadata["requester_username"] ||
                    "未知用户"}
                </div>
                <div>
                  <strong>金额:</strong> {Money.to_decimal(@selected_refund_request.amount)
                  |> Decimal.to_integer()} 积分
                </div>
                <div>
                  <strong>申请时间:</strong> {TimeHelper.format_local_datetime(
                    @selected_refund_request.timestamp
                  )}
                </div>
                <%= if @selected_refund_request.metadata["refund_reason"] do %>
                  <div>
                    <strong>申请原因:</strong> {@selected_refund_request.metadata["refund_reason"]}
                  </div>
                <% end %>
              </div>
            </div>

            <form phx-submit="reject_refund" phx-target={@myself} class="space-y-4">
              <div class="form-control">
                <label class="label">
                  <span class="label-text">拒绝原因 *</span>
                </label>
                <textarea
                  name="reject[reason]"
                  placeholder="请说明拒绝原因..."
                  class="textarea textarea-bordered"
                  required
                  rows="3"
                ></textarea>
              </div>

              <div class="alert alert-warning">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="stroke-current shrink-0 h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
                <span>拒绝后，积分将自动返还给申请人账户</span>
              </div>

              <div class="modal-action">
                <button
                  type="button"
                  phx-click="hide_refund_reject_modal"
                  phx-target={@myself}
                  class="btn btn-ghost"
                >
                  取消
                </button>
                <button type="submit" class="btn btn-error">确认拒绝</button>
              </div>
            </form>
          </div>
        </div>
      <% end %>
    </div>
    """
  end


end
