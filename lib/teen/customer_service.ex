defmodule Teen.CustomerService do
  @moduledoc """
  客服管理域

  包含客服聊天、用户问题、兑换订单、支付订单等功能
  """

  use Ash.Domain,
    otp_app: :cypridina,
    extensions: [AshAdmin.Domain]

  alias Teen.CustomerService.{CustomerChat, ExchangeOrder, SensitiveWord, VerificationCode}
  alias Cypridina.Accounts.User

  admin do
    show? true
  end

  resources do
    resource Teen.CustomerService.CustomerChat
    resource Teen.CustomerService.UserQuestion
    resource Teen.CustomerService.ExchangeOrder
    resource Teen.CustomerService.SensitiveWord
    resource Teen.CustomerService.UserTag
    resource Teen.CustomerService.VerificationCode
  end

  # ==================== 业务逻辑函数 ====================

  @doc """
  处理客服聊天消息，包括敏感词过滤
  """
  def process_customer_chat(attrs) do
    with {:ok, filtered_question} <- filter_sensitive_words(attrs["question"]),
         attrs <- Map.put(attrs, "question", filtered_question),
         {:ok, chat} <- CustomerChat.create(attrs) do
      # 自动分配客服
      assign_customer_service(chat)
      {:ok, chat}
    end
  end

  @doc """
  过滤敏感词
  """
  def filter_sensitive_words(content) when is_binary(content) do
    case SensitiveWord.list_active_words() do
      {:ok, words} ->
        filtered_content = Enum.reduce(words, content, fn word, acc ->
          case word.action_type do
            1 -> String.replace(acc, word.keyword, word.replacement || "***")
            2 -> String.replace(acc, word.keyword, "***")
            3 -> acc # 警告但不替换
          end
        end)
        {:ok, filtered_content}

      {:error, reason} ->
        {:error, reason}
    end
  end

  def filter_sensitive_words(_), do: {:error, "Invalid content"}

  @doc """
  批量回复客服消息
  """
  def batch_reply_messages(chat_ids, reply_content) when is_list(chat_ids) do
    results = Enum.map(chat_ids, fn chat_id ->
      case CustomerChat.read(chat_id) do
        {:ok, chat} ->
          CustomerChat.batch_reply(chat, %{reply_content: reply_content})

        {:error, reason} ->
          {:error, {chat_id, reason}}
      end
    end)

    {successes, failures} = Enum.split_with(results, fn
      {:ok, _} -> true
      _ -> false
    end)

    %{
      success_count: length(successes),
      failure_count: length(failures),
      failures: failures
    }
  end

  @doc """
  审核兑换订单
  """
  def audit_exchange_order(order_id, auditor_id, action, opts \\ []) do
    case ExchangeOrder.read(order_id) do
      {:ok, order} ->
        case action do
          :approve ->
            approve_exchange_order(order, auditor_id)

          :reject ->
            feedback = Keyword.get(opts, :feedback, "审核不通过")
            reject_exchange_order(order, auditor_id, feedback)

          _ ->
            {:error, "Invalid action"}
        end

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  批量审核兑换订单
  """
  def batch_audit_orders(order_ids, auditor_id, action, opts \\ []) when is_list(order_ids) do
    results = Enum.map(order_ids, fn order_id ->
      audit_exchange_order(order_id, auditor_id, action, opts)
    end)

    {successes, failures} = Enum.split_with(results, fn
      {:ok, _} -> true
      _ -> false
    end)

    %{
      success_count: length(successes),
      failure_count: length(failures),
      failures: failures
    }
  end

  @doc """
  计算兑换手续费和税费
  """
  def calculate_exchange_fees(amount, user_vip_level \\ 0) do
    base_fee_rate = Decimal.new("0.05") # 5%基础手续费
    tax_rate = Decimal.new("0.20") # 20%税率

    # VIP用户享受手续费减免
    vip_discount = case user_vip_level do
      level when level >= 5 -> Decimal.new("0.5") # 50%减免
      level when level >= 3 -> Decimal.new("0.3") # 30%减免
      level when level >= 1 -> Decimal.new("0.1") # 10%减免
      _ -> Decimal.new("0")
    end

    actual_fee_rate = Decimal.mult(base_fee_rate, Decimal.sub(Decimal.new("1"), vip_discount))
    fee_amount = Decimal.mult(amount, actual_fee_rate)
    tax_amount = Decimal.mult(amount, tax_rate)

    %{
      original_amount: amount,
      fee_rate: actual_fee_rate,
      fee_amount: fee_amount,
      tax_amount: tax_amount,
      final_amount: Decimal.sub(Decimal.sub(amount, fee_amount), tax_amount)
    }
  end

  @doc """
  发送验证码
  """
  def send_verification_code(phone_number, code_type) do
    case VerificationCode.send_code(%{phone_number: phone_number, code_type: code_type}) do
      {:ok, verification} ->
        # 这里应该调用实际的短信发送服务
        case send_sms(phone_number, verification.code) do
          :ok ->
            VerificationCode.mark_sent(verification)

          {:error, reason} ->
            VerificationCode.mark_failed(verification, %{failure_reason: reason})
        end

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  验证验证码
  """
  def verify_code(phone_number, code) do
    case VerificationCode.list_by_phone(phone_number) do
      {:ok, codes} ->
        valid_code = Enum.find(codes, fn c ->
          c.code == code and c.status == 1 and
          DateTime.compare(c.expires_at, DateTime.utc_now()) == :gt
        end)

        case valid_code do
          nil ->
            {:error, "Invalid or expired verification code"}

          code_record ->
            VerificationCode.mark_used(code_record)
            {:ok, "Verification successful"}
        end

      {:error, reason} ->
        {:error, reason}
    end
  end

  # ==================== 私有函数 ====================

  defp assign_customer_service(chat) do
    # 简单的轮询分配策略
    case get_available_customer_service() do
      {:ok, cs_user} ->
        CustomerChat.mark_as_processed(chat, %{
          customer_service_id: cs_user.id,
          reply_content: "您好，我是客服，请问有什么可以帮助您的？"
        })

      {:error, _} ->
        # 没有可用客服，保持未处理状态
        :ok
    end
  end

  defp get_available_customer_service do
    # 这里应该查询可用的客服人员
    {:error, "No available customer service"}
  end

  defp approve_exchange_order(order, auditor_id) do
    with {:ok, user} <- User.read(order.user_id),
         :ok <- validate_exchange_eligibility(user, order),
         {:ok, updated_order} <- ExchangeOrder.approve_order(order, %{auditor_id: auditor_id}) do

      ExchangeOrder.update_progress(updated_order, %{progress_status: 0})
      notify_user_order_approved(user, updated_order)
      {:ok, updated_order}
    end
  end

  defp reject_exchange_order(order, auditor_id, feedback) do
    case ExchangeOrder.reject_order(order, %{auditor_id: auditor_id, feedback: feedback}) do
      {:ok, updated_order} ->
        notify_user_order_rejected(order.user_id, updated_order, feedback)
        {:ok, updated_order}

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp validate_exchange_eligibility(user, order) do
    cond do
      user.status != 1 ->
        {:error, "用户状态异常"}

      order.exchange_amount < Decimal.new("10000") ->
        {:error, "兑换金额低于最小限额"}

      true ->
        :ok
    end
  end

  defp send_sms(_phone_number, _code) do
    # 模拟短信发送
    :ok
  end

  defp notify_user_order_approved(_user, _order), do: :ok
  defp notify_user_order_rejected(_user_id, _order, _feedback), do: :ok
end
