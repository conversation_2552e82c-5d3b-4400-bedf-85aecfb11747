defmodule Teen.PromotionSystem.ShareSettlement do
  @moduledoc """
  分享结算资源

  管理用户分享行为的奖励结算，包括分享奖励、点击奖励、转化奖励等
  """

  use Ash.Resource,
    otp_app: :cypridina,
    domain: Teen.PromotionSystem,
    data_layer: AshPostgres.DataLayer,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id, :user_id, :share_id, :settlement_type, :reward_amount,
      :status, :settlement_date, :inserted_at
    ]
  end

  postgres do
    table "share_settlements"
    repo Cypridina.Repo
  end

  attributes do
    uuid_primary_key :id

    attribute :user_id, :uuid do
      allow_nil? false
      public? true
      description "用户ID"
    end

    attribute :share_id, :uuid do
      allow_nil? false
      public? true
      description "分享记录ID"
    end

    attribute :settlement_type, :string do
      allow_nil? false
      public? true
      description "结算类型：share=分享奖励，view=查看奖励，click=点击奖励，conversion=转化奖励"
      constraints max_length: 20
    end

    attribute :reward_amount, :decimal do
      allow_nil? false
      public? true
      description "奖励金额"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :bonus_amount, :decimal do
      allow_nil? false
      public? true
      description "额外奖励金额"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :total_amount, :decimal do
      allow_nil? false
      public? true
      description "总奖励金额"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :settlement_period, :string do
      allow_nil? false
      public? true
      description "结算周期（如：2024-01）"
      constraints max_length: 20
    end

    attribute :status, :integer do
      allow_nil? false
      public? true
      description "状态：0=待结算，1=已结算，2=已支付，3=已取消"
      default 0
      constraints min: 0, max: 3
    end

    attribute :settlement_date, :date do
      allow_nil? true
      public? true
      description "结算日期"
    end

    attribute :payment_date, :date do
      allow_nil? true
      public? true
      description "支付日期"
    end

    attribute :remarks, :string do
      allow_nil? true
      public? true
      description "备注"
      constraints max_length: 500
    end

    timestamps()
  end

  relationships do
    belongs_to :user, Cypridina.Accounts.User do
      destination_attribute :id
      source_attribute :user_id
    end


  end

  calculations do
    calculate :is_pending, :boolean, expr(status == 0)
    calculate :is_settled, :boolean, expr(status == 1)
    calculate :is_paid, :boolean, expr(status == 2)
    calculate :is_cancelled, :boolean, expr(status == 3)

    calculate :settlement_type_name, :string do
      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.settlement_type do
            "share" -> "分享奖励"
            "view" -> "查看奖励"
            "click" -> "点击奖励"
            "conversion" -> "转化奖励"
            _ -> "其他奖励"
          end
        end)
      end
    end

    calculate :status_name, :string do
      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.status do
            0 -> "待结算"
            1 -> "已结算"
            2 -> "已支付"
            3 -> "已取消"
            _ -> "未知状态"
          end
        end)
      end
    end
  end

  identities do
    identity :unique_user_share_type_period, [:user_id, :share_id, :settlement_type, :settlement_period]
  end

  validations do
    validate match(:settlement_type, ~r/^(share|view|click|conversion)$/) do
      message "结算类型必须是：share, view, click, conversion 之一"
    end

    validate match(:settlement_period, ~r/^\d{4}-\d{2}$/) do
      message "结算周期格式应为：YYYY-MM"
    end

    validate compare(:total_amount, greater_than_or_equal_to: 0) do
      message "总奖励金额不能为负数"
    end
  end

  actions do
    defaults [:read, :destroy]

    create :create do
      accept [:user_id, :share_id, :settlement_type, :reward_amount, :bonus_amount, :settlement_period, :remarks]

      change fn changeset, _context ->
        reward = Ash.Changeset.get_attribute(changeset, :reward_amount) || Decimal.new("0")
        bonus = Ash.Changeset.get_attribute(changeset, :bonus_amount) || Decimal.new("0")
        total = Decimal.add(reward, bonus)

        Ash.Changeset.change_attribute(changeset, :total_amount, total)
      end
    end

    update :update do
      accept [:reward_amount, :bonus_amount, :remarks]

      change fn changeset, _context ->
        reward = Ash.Changeset.get_attribute(changeset, :reward_amount) || changeset.data.reward_amount
        bonus = Ash.Changeset.get_attribute(changeset, :bonus_amount) || changeset.data.bonus_amount
        total = Decimal.add(reward, bonus)

        Ash.Changeset.change_attribute(changeset, :total_amount, total)
      end
    end

    update :settle do
      accept [:settlement_date]

      change fn changeset, _context ->
        settlement_date = Ash.Changeset.get_attribute(changeset, :settlement_date) || Date.utc_today()

        changeset
        |> Ash.Changeset.change_attribute(:status, 1)
        |> Ash.Changeset.change_attribute(:settlement_date, settlement_date)
      end
    end

    update :pay do
      accept [:payment_date]

      change fn changeset, _context ->
        payment_date = Ash.Changeset.get_attribute(changeset, :payment_date) || Date.utc_today()

        changeset
        |> Ash.Changeset.change_attribute(:status, 2)
        |> Ash.Changeset.change_attribute(:payment_date, payment_date)
      end
    end

    update :cancel do
      accept [:remarks]

      change fn changeset, _context ->
        Ash.Changeset.change_attribute(changeset, :status, 3)
      end
    end

    update :reopen do
      accept []

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, 0)
        |> Ash.Changeset.change_attribute(:settlement_date, nil)
        |> Ash.Changeset.change_attribute(:payment_date, nil)
      end
    end
  end

  # 批量结算功能
  def batch_settle_by_period(settlement_period) do
    # 批量结算指定周期的分享奖励
    {:ok, "批量结算功能待实现"}
  end

  def calculate_user_rewards(user_id, period) do
    # 计算用户在指定周期的分享奖励
    {:ok, "用户奖励计算功能待实现"}
  end
end
