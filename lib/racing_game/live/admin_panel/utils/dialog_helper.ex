defmodule RacingGame.Utils.DialogHelper do
  @moduledoc """
  对话框辅助模块

  提供便捷的对话框状态管理和预设配置功能。
  简化在 LiveView 组件中使用动态对话框的流程。

  ## 使用示例

      # 在 LiveView 组件中
      defmodule MyComponent do
        use Phoenix.LiveComponent
        import RacingGame.Utils.DialogHelper

        def mount(socket) do
          socket = init_dialogs(socket, [
            :delete_confirm,
            :save_confirm,
            :info_dialog
          ])
          {:ok, socket}
        end

        def handle_event("show_delete_dialog", %{"id" => record_id}, socket) do
          socket = show_confirm_dialog(socket, :delete_confirm,
            title: "确认删除",
            message: "您确定要删除ID为 \#{record_id} 的记录吗？",
            confirm_action: "confirm_delete",
            confirm_data: %{"id" => record_id}
          )
          {:noreply, socket}
        end

        def handle_event("confirm_delete", %{"id" => record_id}, socket) do
          # 执行删除操作
          socket =
            socket
            |> hide_dialog(:delete_confirm)
            |> show_success_dialog(:info_dialog,
                title: "删除成功",
                message: "记录已成功删除！"
              )
          {:noreply, socket}
        end
      end
  """

  @doc """
  初始化对话框状态

  为指定的对话框名称列表初始化状态。

  ## 参数
  - `socket` - LiveView socket
  - `dialog_names` - 对话框名称列表

  ## 返回
  - 更新后的 socket
  """
  def init_dialogs(socket, dialog_names) when is_list(dialog_names) do
    Enum.reduce(dialog_names, socket, fn name, acc_socket ->
      Phoenix.Component.assign(acc_socket, dialog_state_key(name), %{
        show: false,
        type: "info",
        title: "",
        message: "",
        confirm_text: "确定",
        cancel_text: "取消",
        confirm_action: nil,
        cancel_action: "hide_dialog",
        confirm_data: %{},
        danger: false,
        size: "md",
        closable: true
      })
    end)
  end

  @doc """
  显示确认对话框

  ## 参数
  - `socket` - LiveView socket
  - `dialog_name` - 对话框名称
  - `opts` - 对话框选项

  ## 选项
  - `:title` - 标题
  - `:message` - 消息内容
  - `:confirm_text` - 确认按钮文字
  - `:cancel_text` - 取消按钮文字
  - `:confirm_action` - 确认事件名称
  - `:confirm_data` - 确认事件数据
  - `:danger` - 是否为危险操作
  - `:size` - 对话框大小
  """
  def show_confirm_dialog(socket, dialog_name, opts \\ []) do
    show_dialog(socket, dialog_name, Keyword.merge([type: "confirm"], opts))
  end

  @doc """
  显示警告对话框
  """
  def show_warning_dialog(socket, dialog_name, opts \\ []) do
    show_dialog(socket, dialog_name, Keyword.merge([type: "warning"], opts))
  end

  @doc """
  显示信息对话框
  """
  def show_info_dialog(socket, dialog_name, opts \\ []) do
    show_dialog(socket, dialog_name, Keyword.merge([
      type: "info",
      cancel_action: nil
    ], opts))
  end

  @doc """
  显示错误对话框
  """
  def show_error_dialog(socket, dialog_name, opts \\ []) do
    show_dialog(socket, dialog_name, Keyword.merge([
      type: "error",
      cancel_action: nil
    ], opts))
  end

  @doc """
  显示成功对话框
  """
  def show_success_dialog(socket, dialog_name, opts \\ []) do
    show_dialog(socket, dialog_name, Keyword.merge([
      type: "success",
      cancel_action: nil
    ], opts))
  end

  @doc """
  显示自定义对话框（用于复杂内容）
  """
  def show_custom_dialog(socket, dialog_name, opts) do
    show_dialog(socket, dialog_name, Keyword.merge([
      type: "custom",
      size: "xl",
      closable: true
    ], opts))
  end

  @doc """
  显示自定义对话框
  """
  def show_custom_dialog(socket, dialog_name, opts \\ []) do
    show_dialog(socket, dialog_name, Keyword.merge([type: "custom"], opts))
  end

  @doc """
  隐藏对话框
  """
  def hide_dialog(socket, dialog_name) do
    state_key = dialog_state_key(dialog_name)
    current_state = Phoenix.Component.assign(socket, state_key) |> Map.get(state_key, %{})

    Phoenix.Component.assign(socket, state_key, Map.put(current_state, :show, false))
  end

  @doc """
  获取对话框状态
  """
  def get_dialog_state(socket, dialog_name) do
    state_key = dialog_state_key(dialog_name)
    Map.get(socket.assigns, state_key, %{
      show: false,
      type: "info",
      title: "",
      message: "",
      confirm_text: "确定",
      cancel_text: "取消",
      confirm_action: nil,
      cancel_action: "hide_dialog",
      confirm_data: %{},
      danger: false,
      size: "md",
      closable: true
    })
  end

  @doc """
  检查对话框是否显示
  """
  def dialog_visible?(socket, dialog_name) do
    get_dialog_state(socket, dialog_name).show
  end

  # 私有函数 - 显示对话框
  defp show_dialog(socket, dialog_name, opts) do
    state_key = dialog_state_key(dialog_name)
    current_state = Map.get(socket.assigns, state_key, %{})

    new_state =
      current_state
      |> Map.put(:show, true)
      |> Map.put(:type, Keyword.get(opts, :type, "info"))
      |> Map.put(:title, Keyword.get(opts, :title, "提示"))
      |> Map.put(:message, Keyword.get(opts, :message, ""))
      |> Map.put(:confirm_text, Keyword.get(opts, :confirm_text, "确定"))
      |> Map.put(:cancel_text, Keyword.get(opts, :cancel_text, "取消"))
      |> Map.put(:confirm_action, Keyword.get(opts, :confirm_action))
      |> Map.put(:cancel_action, Keyword.get(opts, :cancel_action, "hide_dialog"))
      |> Map.put(:confirm_data, Keyword.get(opts, :confirm_data, %{}))
      |> Map.put(:danger, Keyword.get(opts, :danger, false))
      |> Map.put(:size, Keyword.get(opts, :size, "md"))
      |> Map.put(:closable, Keyword.get(opts, :closable, true))

    Phoenix.Component.assign(socket, state_key, new_state)
  end

  # 私有函数 - 生成对话框状态键名
  defp dialog_state_key(dialog_name) do
    :"dialog_#{dialog_name}"
  end

  @doc """
  预设对话框配置

  提供常用的对话框配置预设。

  ## 预设类型
  - `:delete_confirm` - 删除确认对话框
  - `:save_confirm` - 保存确认对话框
  - `:logout_confirm` - 登出确认对话框
  - `:reset_confirm` - 重置确认对话框
  - `:operation_success` - 操作成功提示
  - `:operation_error` - 操作错误提示
  """
  def preset_config(:delete_confirm) do
    [
      type: "confirm",
      title: "确认删除",
      message: "您确定要删除这条记录吗？此操作不可撤销。",
      confirm_text: "删除",
      cancel_text: "取消",
      danger: true,
      icon: "fas fa-trash"
    ]
  end

  def preset_config(:save_confirm) do
    [
      type: "confirm",
      title: "确认保存",
      message: "您确定要保存当前更改吗？",
      confirm_text: "保存",
      cancel_text: "取消",
      icon: "fas fa-save"
    ]
  end

  def preset_config(:logout_confirm) do
    [
      type: "warning",
      title: "确认登出",
      message: "您确定要退出登录吗？未保存的更改可能会丢失。",
      confirm_text: "登出",
      cancel_text: "取消",
      icon: "fas fa-sign-out-alt"
    ]
  end

  def preset_config(:reset_confirm) do
    [
      type: "warning",
      title: "确认重置",
      message: "您确定要重置所有设置吗？此操作将清除所有自定义配置。",
      confirm_text: "重置",
      cancel_text: "取消",
      danger: true,
      icon: "fas fa-undo"
    ]
  end

  def preset_config(:operation_success) do
    [
      type: "success",
      title: "操作成功",
      message: "操作已成功完成！",
      confirm_text: "确定",
      cancel_action: nil,
      icon: "fas fa-check-circle"
    ]
  end

  def preset_config(:operation_error) do
    [
      type: "error",
      title: "操作失败",
      message: "操作执行失败，请稍后重试。",
      confirm_text: "确定",
      cancel_action: nil,
      icon: "fas fa-exclamation-triangle"
    ]
  end

  def preset_config(_), do: []

  @doc """
  使用预设配置显示对话框
  """
  def show_preset_dialog(socket, dialog_name, preset_type, custom_opts \\ []) do
    preset_opts = preset_config(preset_type)
    final_opts = Keyword.merge(preset_opts, custom_opts)
    show_dialog(socket, dialog_name, final_opts)
  end
end
