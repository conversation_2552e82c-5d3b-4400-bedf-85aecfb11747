# 开发指南

## 📋 概述

本文档为 Racing Game Admin Panel 系统的开发人员提供详细的开发指南，包括环境搭建、代码规范、开发流程、测试要求和部署说明。

## 🛠️ 开发环境搭建

### 1. 系统要求

**基础环境**:
- **操作系统**: Ubuntu 20.04+ / macOS 12+ / Windows 10+ (WSL2)
- **内存**: 最少 8GB，推荐 16GB+
- **磁盘空间**: 最少 20GB 可用空间
- **网络**: 稳定的互联网连接

**必需软件**:
```bash
# Elixir & Erlang
Elixir 1.15+
Erlang/OTP 26+

# 数据库
PostgreSQL 15+
Redis 7+

# Node.js (前端资源)
Node.js 18+
npm 9+

# 版本控制
Git 2.30+
```

### 2. 环境安装步骤

#### 2.1 安装 Elixir 和 Erlang
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install elixir erlang-dev erlang-xmerl

# macOS (使用 Homebrew)
brew install elixir

# 验证安装
elixir --version
```

#### 2.2 安装数据库
```bash
# PostgreSQL
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Redis
sudo apt install redis-server
sudo systemctl start redis
sudo systemctl enable redis

# 创建数据库用户
sudo -u postgres createuser --interactive --pwprompt racing_game
sudo -u postgres createdb racing_game_dev -O racing_game
sudo -u postgres createdb racing_game_test -O racing_game
```

#### 2.3 克隆项目并安装依赖
```bash
# 克隆项目
git clone <repository-url>
cd racing_game_admin_panel

# 安装 Elixir 依赖
mix deps.get

# 安装前端依赖
cd assets && npm install && cd ..

# 设置数据库
mix ecto.setup

# 编译项目
mix compile
```

### 3. 开发环境配置

#### 3.1 环境变量配置
创建 `.env` 文件：
```bash
# 数据库配置
DATABASE_URL=postgresql://racing_game:password@localhost/racing_game_dev
TEST_DATABASE_URL=postgresql://racing_game:password@localhost/racing_game_test

# Redis 配置
REDIS_URL=redis://localhost:6379/0

# 应用配置
SECRET_KEY_BASE=your_secret_key_base_here
PHX_HOST=localhost
PHX_PORT=4000

# 外部服务配置
PAYMENT_API_KEY=your_payment_api_key
NOTIFICATION_SERVICE_URL=your_notification_service_url
```

#### 3.2 IDE 配置推荐

**VSCode 扩展**:
```json
{
  "recommendations": [
    "jakebecker.elixir-ls",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml",
    "ms-vscode.vscode-typescript-next"
  ]
}
```

**VSCode 设置**:
```json
{
  "elixirLS.dialyzerEnabled": true,
  "elixirLS.fetchDeps": true,
  "elixirLS.suggestSpecs": true,
  "editor.formatOnSave": true,
  "files.trimTrailingWhitespace": true
}
```

## 📝 代码规范

### 1. Elixir 代码规范

#### 1.1 命名约定
```elixir
# 模块名使用 PascalCase
defmodule RacingGame.Live.AdminPanel.CustomerService do
end

# 函数名使用 snake_case
def list_customers(filters \\ %{}, options \\ []) do
end

# 变量名使用 snake_case
customer_data = %{name: "客服001"}

# 常量使用 SCREAMING_SNAKE_CASE
@default_timeout 5000
```

#### 1.2 文档规范
```elixir
@doc """
获取客服列表

## 参数
- `filters` - 过滤条件 (Map)
  - `:status` - 客服状态 (atom)
  - `:department` - 部门 (string)
- `options` - 选项参数 (Keyword List)
  - `:limit` - 限制数量 (integer, 默认: 20)
  - `:offset` - 偏移量 (integer, 默认: 0)

## 返回
- `{:ok, customers}` - 成功返回客服列表
- `{:error, reason}` - 失败返回错误原因

## 示例
    iex> CustomerService.list_customers(%{status: :active}, limit: 10)
    {:ok, [%Customer{}, ...]}
"""
def list_customers(filters \\ %{}, options \\ []) do
  # 实现代码
end
```

#### 1.3 错误处理规范
```elixir
# 使用 with 语句处理多步骤操作
def create_customer_with_account(customer_data) do
  with {:ok, customer} <- create_customer(customer_data),
       {:ok, account} <- create_account(customer.id),
       {:ok, _} <- send_welcome_email(customer.email) do
    {:ok, %{customer: customer, account: account}}
  else
    {:error, :customer_creation_failed} = error ->
      Logger.error("客服创建失败: #{inspect(customer_data)}")
      error
      
    {:error, reason} = error ->
      Logger.error("客服账户创建流程失败: #{inspect(reason)}")
      error
  end
end

# 使用 case 语句处理简单条件
def get_customer_status(customer_id) do
  case get_customer(customer_id) do
    {:ok, %{status: status}} -> {:ok, status}
    {:error, :not_found} -> {:error, :customer_not_found}
    error -> error
  end
end
```

### 2. 数据库规范

#### 2.1 迁移文件规范
```elixir
defmodule RacingGame.Repo.Migrations.CreateCustomers do
  use Ecto.Migration

  def change do
    create table(:customers) do
      add :name, :string, null: false, comment: "客服姓名"
      add :email, :string, null: false, comment: "邮箱地址"
      add :status, :integer, default: 1, comment: "状态: 1-正常, 0-禁用"
      add :department, :string, comment: "所属部门"
      
      timestamps(type: :utc_datetime)
    end
    
    create unique_index(:customers, [:email])
    create index(:customers, [:status])
    create index(:customers, [:department])
  end
end
```

#### 2.2 Schema 定义规范
```elixir
defmodule RacingGame.CustomerService.Customer do
  use Ecto.Schema
  import Ecto.Changeset

  @type t :: %__MODULE__{
    id: integer(),
    name: String.t(),
    email: String.t(),
    status: integer(),
    department: String.t() | nil,
    inserted_at: DateTime.t(),
    updated_at: DateTime.t()
  }

  schema "customers" do
    field :name, :string
    field :email, :string
    field :status, :integer, default: 1
    field :department, :string

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(customer, attrs) do
    customer
    |> cast(attrs, [:name, :email, :status, :department])
    |> validate_required([:name, :email])
    |> validate_format(:email, ~r/^[^\s]+@[^\s]+\.[^\s]+$/)
    |> validate_inclusion(:status, [0, 1])
    |> unique_constraint(:email)
  end
end
```

### 3. 测试规范

#### 3.1 单元测试
```elixir
defmodule RacingGame.CustomerServiceTest do
  use RacingGame.DataCase, async: true
  
  alias RacingGame.CustomerService
  
  describe "list_customers/2" do
    test "returns all customers when no filters provided" do
      customer1 = insert(:customer)
      customer2 = insert(:customer)
      
      assert {:ok, customers} = CustomerService.list_customers()
      assert length(customers) == 2
      assert customer1 in customers
      assert customer2 in customers
    end
    
    test "filters customers by status" do
      active_customer = insert(:customer, status: 1)
      inactive_customer = insert(:customer, status: 0)
      
      assert {:ok, [^active_customer]} = 
        CustomerService.list_customers(%{status: 1})
    end
    
    test "respects limit option" do
      insert_list(5, :customer)
      
      assert {:ok, customers} = 
        CustomerService.list_customers(%{}, limit: 3)
      assert length(customers) == 3
    end
  end
end
```

#### 3.2 集成测试
```elixir
defmodule RacingGameWeb.CustomerControllerTest do
  use RacingGameWeb.ConnCase, async: true
  
  setup %{conn: conn} do
    admin_user = insert(:admin_user)
    conn = log_in_admin(conn, admin_user)
    {:ok, conn: conn, admin_user: admin_user}
  end
  
  describe "GET /admin/customers" do
    test "renders customer list", %{conn: conn} do
      customer = insert(:customer)
      
      conn = get(conn, ~p"/admin/customers")
      
      assert html_response(conn, 200) =~ "客服管理"
      assert html_response(conn, 200) =~ customer.name
    end
  end
end
```

## 🔄 开发流程

### 1. Git 工作流程

#### 1.1 分支策略
```
main (生产分支)
├── develop (开发分支)
│   ├── feature/customer-management (功能分支)
│   ├── feature/payment-system (功能分支)
│   └── bugfix/fix-login-issue (修复分支)
└── hotfix/critical-security-fix (热修复分支)
```

#### 1.2 提交规范
```bash
# 提交消息格式
<type>(<scope>): <subject>

<body>

<footer>

# 示例
feat(customer): 添加客服管理功能

- 实现客服列表查询
- 添加客服创建和编辑功能
- 完善客服状态管理

Closes #123
```

**提交类型**:
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建或辅助工具变动

### 2. 开发步骤

#### 2.1 新功能开发
```bash
# 1. 创建功能分支
git checkout develop
git pull origin develop
git checkout -b feature/new-feature

# 2. 开发功能
# 编写代码、测试、文档

# 3. 运行测试
mix test
mix credo
mix dialyzer

# 4. 提交代码
git add .
git commit -m "feat(scope): 功能描述"

# 5. 推送分支
git push origin feature/new-feature

# 6. 创建 Pull Request
# 在 GitHub/GitLab 上创建 PR
```

#### 2.2 代码审查清单
- [ ] 代码符合项目规范
- [ ] 包含适当的测试
- [ ] 文档已更新
- [ ] 无安全漏洞
- [ ] 性能影响可接受
- [ ] 向后兼容性考虑

### 3. 质量保证

#### 3.1 自动化检查
```bash
# 代码格式检查
mix format --check-formatted

# 代码质量检查
mix credo --strict

# 类型检查
mix dialyzer

# 安全检查
mix sobelow

# 依赖检查
mix deps.audit
```

#### 3.2 测试覆盖率
```bash
# 运行测试并生成覆盖率报告
mix test --cover

# 目标覆盖率: 80%+
# 核心业务逻辑: 90%+
```

## 🚀 部署指南

### 1. 生产环境准备

#### 1.1 环境配置
```bash
# 生产环境变量
export MIX_ENV=prod
export SECRET_KEY_BASE=your_production_secret
export DATABASE_URL=your_production_db_url
export REDIS_URL=your_production_redis_url
```

#### 1.2 构建发布版本
```bash
# 安装依赖
mix deps.get --only prod

# 编译资源
mix assets.deploy

# 创建发布版本
mix release
```

### 2. 部署步骤

#### 2.1 数据库迁移
```bash
# 运行数据库迁移
_build/prod/rel/racing_game/bin/racing_game eval "RacingGame.Release.migrate"
```

#### 2.2 启动应用
```bash
# 启动应用
_build/prod/rel/racing_game/bin/racing_game start

# 或者作为守护进程
_build/prod/rel/racing_game/bin/racing_game daemon
```

### 3. 监控和维护

#### 3.1 日志监控
```bash
# 查看应用日志
tail -f /var/log/racing_game/application.log

# 查看错误日志
tail -f /var/log/racing_game/error.log
```

#### 3.2 健康检查
```bash
# 应用健康检查
curl http://localhost:4000/health

# 数据库连接检查
_build/prod/rel/racing_game/bin/racing_game rpc "RacingGame.Repo.query!(\"SELECT 1\")"
```

## 📚 常用命令

### 开发命令
```bash
# 启动开发服务器
mix phx.server

# 交互式启动
iex -S mix phx.server

# 运行测试
mix test
mix test --watch

# 代码格式化
mix format

# 生成文档
mix docs
```

### 数据库命令
```bash
# 创建数据库
mix ecto.create

# 运行迁移
mix ecto.migrate

# 回滚迁移
mix ecto.rollback

# 重置数据库
mix ecto.reset

# 生成迁移文件
mix ecto.gen.migration create_customers
```

---

**文档版本**: v1.0  
**创建日期**: 2024-12-19  
**最后更新**: 2024-12-19  
**维护团队**: Racing Game Development Team
