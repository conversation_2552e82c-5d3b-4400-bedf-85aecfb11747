defmodule RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.LtvStatsRepository do
  @moduledoc """
  📊 LTV统计仓储层

  负责LTV(生命周期价值)统计的数据访问操作，包括：
  - LTV统计的CRUD操作
  - 用户生命周期价值分析
  - LTV预测和趋势分析
  - 用户价值分群统计
  - LTV数据缓存管理
  """

  require Logger
  import Ash.Query

  alias Teen.Statistics.LtvStats

  # 缓存配置
  @cache_ttl 600_000  # 10分钟缓存
  @cache_prefix "ltv_stats_repo"

  # ==================== 基础 CRUD 操作 ====================

  @doc """
  创建LTV统计记录

  ## 参数
  - `stats_data` - 统计数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, ltv_stats}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_ltv_stats(stats_data, options \\ []) do
    Logger.info("📊 [LTV统计仓储] 创建LTV统计记录: #{inspect(stats_data[:cohort_date])}")

    try do
      result = LtvStats
      |> Ash.Changeset.for_create(:create, stats_data)
      |> Ash.create()

      case result do
        {:ok, ltv_stats} ->
          Logger.info("✅ [LTV统计仓储] LTV统计记录创建成功: #{ltv_stats.cohort_date}")
          clear_cache()
          {:ok, ltv_stats}
        {:error, error} ->
          Logger.error("❌ [LTV统计仓储] LTV统计记录创建失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [LTV统计仓储] 创建LTV统计记录异常: #{inspect(exception)}")
        {:error, :create_failed}
    end
  end

  @doc """
  获取LTV统计记录

  ## 参数
  - `stats_id` - 统计ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, ltv_stats}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_ltv_stats(stats_id, options \\ []) do
    Logger.info("📊 [LTV统计仓储] 获取LTV统计记录: #{stats_id}")

    cache_key = "#{@cache_prefix}:get:#{stats_id}"
    
    case get_from_cache(cache_key) do
      {:ok, cached_result} ->
        Logger.info("🎯 [LTV统计仓储] 缓存命中: #{stats_id}")
        {:ok, cached_result}
      
      {:error, :not_found} ->
        try do
          result = LtvStats |> Ash.get(stats_id)

          case result do
            {:ok, ltv_stats} ->
              Logger.info("✅ [LTV统计仓储] LTV统计记录获取成功: #{ltv_stats.id}")
              put_to_cache(cache_key, ltv_stats)
              {:ok, ltv_stats}
            {:error, error} ->
              Logger.error("❌ [LTV统计仓储] LTV统计记录获取失败: #{inspect(error)}")
              {:error, error}
          end
        rescue
          exception ->
            Logger.error("💥 [LTV统计仓储] 获取LTV统计记录异常: #{inspect(exception)}")
            {:error, :get_failed}
        end
    end
  end

  @doc """
  更新LTV统计记录

  ## 参数
  - `ltv_stats` - LTV统计实例
  - `update_data` - 更新数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, updated_ltv_stats}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_ltv_stats(ltv_stats, update_data, options \\ []) do
    Logger.info("📊 [LTV统计仓储] 更新LTV统计记录: #{ltv_stats.id}")

    try do
      result = ltv_stats
      |> Ash.Changeset.for_update(:update, update_data)
      |> Ash.update()

      case result do
        {:ok, updated_ltv_stats} ->
          Logger.info("✅ [LTV统计仓储] LTV统计记录更新成功: #{updated_ltv_stats.id}")
          clear_cache()
          {:ok, updated_ltv_stats}
        {:error, error} ->
          Logger.error("❌ [LTV统计仓储] LTV统计记录更新失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [LTV统计仓储] 更新LTV统计记录异常: #{inspect(exception)}")
        {:error, :update_failed}
    end
  end

  @doc """
  删除LTV统计记录

  ## 参数
  - `ltv_stats` - LTV统计实例
  - `options` - 选项参数

  ## 返回
  - `{:ok, deleted_ltv_stats}` - 成功
  - `{:error, reason}` - 失败
  """
  def delete_ltv_stats(ltv_stats, options \\ []) do
    Logger.info("📊 [LTV统计仓储] 删除LTV统计记录: #{ltv_stats.id}")

    try do
      result = ltv_stats |> Ash.destroy()

      case result do
        :ok ->
          Logger.info("✅ [LTV统计仓储] LTV统计记录删除成功: #{ltv_stats.id}")
          clear_cache()
          {:ok, ltv_stats}
        {:error, error} ->
          Logger.error("❌ [LTV统计仓储] LTV统计记录删除失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [LTV统计仓储] 删除LTV统计记录异常: #{inspect(exception)}")
        {:error, :delete_failed}
    end
  end

  # ==================== 查询操作 ====================

  @doc """
  列出LTV统计记录

  ## 参数
  - `params` - 查询参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, {stats_list, total_count}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_ltv_stats(params \\ %{}, options \\ []) do
    Logger.info("📊 [LTV统计仓储] 列出LTV统计记录: #{inspect(params)}")

    try do
      query = LtvStats
      |> build_list_query(params)
      |> maybe_load_associations(options)

      # 获取总数
      total_count = query |> Ash.count!()

      # 应用分页
      query = query
      |> limit(Map.get(params, :page_size, 20))
      |> offset(Map.get(params, :offset, 0))

      stats_list = query |> Ash.read!()

      Logger.info("✅ [LTV统计仓储] LTV统计记录列表获取成功，共 #{length(stats_list)} 条")
      {:ok, {stats_list, total_count}}
    rescue
      exception ->
        Logger.error("💥 [LTV统计仓储] 列出LTV统计记录异常: #{inspect(exception)}")
        {:error, :list_failed}
    end
  end

  @doc """
  按队列日期获取LTV统计

  ## 参数
  - `cohort_date` - 队列日期
  - `options` - 选项参数

  ## 返回
  - `{:ok, stats_list}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_stats_by_cohort(cohort_date, options \\ []) do
    Logger.info("📊 [LTV统计仓储] 按队列日期获取LTV统计: #{cohort_date}")

    cache_key = "#{@cache_prefix}:by_cohort:#{cohort_date}"
    
    case get_from_cache(cache_key) do
      {:ok, cached_result} ->
        Logger.info("🎯 [LTV统计仓储] 缓存命中: #{cohort_date}")
        {:ok, cached_result}
      
      {:error, :not_found} ->
        try do
          stats_list = LtvStats
          |> Ash.Query.filter(cohort_date == ^cohort_date)
          |> Ash.Query.sort(ltv_day: :asc)
          |> maybe_load_associations(options)
          |> Ash.read!()

          Logger.info("✅ [LTV统计仓储] 按队列日期获取LTV统计成功: #{length(stats_list)} 条")
          put_to_cache(cache_key, stats_list)
          {:ok, stats_list}
        rescue
          exception ->
            Logger.error("💥 [LTV统计仓储] 按队列日期获取LTV统计异常: #{inspect(exception)}")
            {:error, :get_by_cohort_failed}
        end
    end
  end

  @doc """
  按日期范围获取LTV统计

  ## 参数
  - `start_date` - 开始日期
  - `end_date` - 结束日期
  - `options` - 选项参数

  ## 返回
  - `{:ok, stats_list}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_stats_by_date_range(start_date, end_date, options \\ []) do
    Logger.info("📊 [LTV统计仓储] 按日期范围获取LTV统计: #{start_date} - #{end_date}")

    try do
      stats_list = LtvStats
      |> Ash.Query.filter(cohort_date >= ^start_date and cohort_date <= ^end_date)
      |> Ash.Query.sort([cohort_date: :desc, ltv_day: :asc])
      |> maybe_load_associations(options)
      |> Ash.read!()

      Logger.info("✅ [LTV统计仓储] 按日期范围获取LTV统计成功: #{length(stats_list)} 条")
      {:ok, stats_list}
    rescue
      exception ->
        Logger.error("💥 [LTV统计仓储] 按日期范围获取LTV统计异常: #{inspect(exception)}")
        {:error, :get_by_date_range_failed}
    end
  end

  @doc """
  获取LTV趋势

  ## 参数
  - `ltv_day` - LTV天数
  - `days` - 分析天数
  - `options` - 选项参数

  ## 返回
  - `{:ok, trend_stats}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_ltv_trend(ltv_day, days \\ 30, options \\ []) do
    Logger.info("📊 [LTV统计仓储] 获取LTV趋势: 第#{ltv_day}天，#{days}天数据")

    try do
      start_date = Date.utc_today() |> Date.add(-days)
      
      trend_stats = LtvStats
      |> Ash.Query.filter(ltv_day == ^ltv_day and cohort_date >= ^start_date)
      |> Ash.Query.sort(cohort_date: :desc)
      |> maybe_load_associations(options)
      |> Ash.read!()

      Logger.info("✅ [LTV统计仓储] 获取LTV趋势成功: #{length(trend_stats)} 条")
      {:ok, trend_stats}
    rescue
      exception ->
        Logger.error("💥 [LTV统计仓储] 获取LTV趋势异常: #{inspect(exception)}")
        {:error, :get_ltv_trend_failed}
    end
  end

  @doc """
  获取LTV预测

  ## 参数
  - `cohort_date` - 队列日期
  - `options` - 选项参数

  ## 返回
  - `{:ok, prediction_stats}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_ltv_prediction(cohort_date, options \\ []) do
    Logger.info("📊 [LTV统计仓储] 获取LTV预测: #{cohort_date}")

    try do
      prediction_stats = LtvStats
      |> Ash.Query.filter(cohort_date == ^cohort_date)
      |> Ash.Query.sort(ltv_day: :asc)
      |> maybe_load_associations(options)
      |> Ash.read!()

      Logger.info("✅ [LTV统计仓储] 获取LTV预测成功: #{length(prediction_stats)} 条")
      {:ok, prediction_stats}
    rescue
      exception ->
        Logger.error("💥 [LTV统计仓储] 获取LTV预测异常: #{inspect(exception)}")
        {:error, :get_ltv_prediction_failed}
    end
  end

  # ==================== 私有辅助函数 ====================

  # 构建列表查询
  defp build_list_query(query, params) do
    query
    |> maybe_filter_by_cohort_date(params)
    |> maybe_filter_by_ltv_day(params)
    |> maybe_filter_by_date_range(params)
    |> maybe_apply_search(params)
    |> maybe_apply_sort(params)
  end

  # 按队列日期过滤
  defp maybe_filter_by_cohort_date(query, %{cohort_date: cohort_date}) when not is_nil(cohort_date) do
    query |> Ash.Query.filter(cohort_date == ^cohort_date)
  end
  defp maybe_filter_by_cohort_date(query, _), do: query

  # 按LTV天数过滤
  defp maybe_filter_by_ltv_day(query, %{ltv_day: ltv_day}) when not is_nil(ltv_day) do
    query |> Ash.Query.filter(ltv_day == ^ltv_day)
  end
  defp maybe_filter_by_ltv_day(query, _), do: query

  # 按日期范围过滤
  defp maybe_filter_by_date_range(query, %{start_date: start_date, end_date: end_date}) 
    when not is_nil(start_date) and not is_nil(end_date) do
    query |> Ash.Query.filter(cohort_date >= ^start_date and cohort_date <= ^end_date)
  end
  defp maybe_filter_by_date_range(query, _), do: query

  # 应用搜索
  defp maybe_apply_search(query, %{search: search}) when search != "" do
    query
  end
  defp maybe_apply_search(query, _), do: query

  # 应用排序
  defp maybe_apply_sort(query, %{sort_by: sort_by, sort_order: sort_order}) do
    sort_field = String.to_atom(sort_by)
    sort_direction = String.to_atom(sort_order)
    query |> Ash.Query.sort([{sort_direction, sort_field}])
  end
  defp maybe_apply_sort(query, _) do
    query |> Ash.Query.sort([cohort_date: :desc, ltv_day: :asc])
  end

  # 加载关联数据
  defp maybe_load_associations(query, options) do
    case Keyword.get(options, :load_associations, false) do
      true -> query |> Ash.Query.load([])
      _ -> query
    end
  end

  # 缓存操作
  defp get_from_cache(_key), do: {:error, :not_found}
  defp put_to_cache(_key, _value), do: :ok
  defp clear_cache(), do: :ok
end
