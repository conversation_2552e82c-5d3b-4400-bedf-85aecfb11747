defmodule Teen.SystemSettings.Permission do
  @moduledoc """
  权限资源

  管理系统权限，包括菜单权限、操作权限等
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.SystemSettings,
    extensions: [AshAdmin.Resource]

  postgres do
    table "permissions"
    repo Cypridina.Repo
  end

  admin do
    table_columns [:id, :name, :code, :type, :parent_id, :status, :sort_order, :inserted_at]
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_by_type
    define :list_by_parent
    define :list_by_status
    define :get_by_code
  end

  actions do
    defaults [:create, :read, :update, :destroy]

    read :list_by_type do
      argument :type, :string, allow_nil?: false
      filter expr(type == ^arg(:type))
    end

    read :list_by_parent do
      argument :parent_id, :uuid, allow_nil?: false
      filter expr(parent_id == ^arg(:parent_id))
    end

    read :list_by_status do
      argument :status, :integer, allow_nil?: false
      filter expr(status == ^arg(:status))
    end

    read :get_by_code do
      argument :code, :string, allow_nil?: false
      get? true
      filter expr(code == ^arg(:code))
    end

    read :get_tree do
      description "获取权限树结构"
      filter expr(status == 1)
      prepare build(sort: [sort_order: :asc])
    end

    update :activate do
      accept []
      change set_attribute(:status, 1)
      change set_attribute(:updated_at, &DateTime.utc_now/0)
    end

    update :deactivate do
      accept []
      change set_attribute(:status, 0)
      change set_attribute(:updated_at, &DateTime.utc_now/0)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :name, :string do
      allow_nil? false
      public? true
      description "权限名称"
      constraints min_length: 2, max_length: 100
    end

    attribute :code, :string do
      allow_nil? false
      public? true
      description "权限代码"
      constraints min_length: 2, max_length: 100
    end

    attribute :type, :string do
      allow_nil? false
      public? true
      description "权限类型：menu=菜单，button=按钮，api=接口"
      constraints max_length: 20
    end

    attribute :parent_id, :uuid do
      allow_nil? true
      public? true
      description "父权限ID"
    end

    attribute :path, :string do
      allow_nil? true
      public? true
      description "菜单路径或API路径"
      constraints max_length: 255
    end

    attribute :component, :string do
      allow_nil? true
      public? true
      description "前端组件路径"
      constraints max_length: 255
    end

    attribute :icon, :string do
      allow_nil? true
      public? true
      description "图标"
      constraints max_length: 100
    end

    attribute :sort_order, :integer do
      allow_nil? false
      public? true
      description "排序顺序"
      default 0
    end

    attribute :status, :integer do
      allow_nil? false
      public? true
      description "状态：0=禁用，1=启用"
      default 1
      constraints min: 0, max: 1
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "权限描述"
      constraints max_length: 500
    end

    attribute :is_external, :boolean do
      allow_nil? false
      public? true
      description "是否外部链接"
      default false
    end

    attribute :is_hidden, :boolean do
      allow_nil? false
      public? true
      description "是否隐藏"
      default false
    end

    attribute :meta_data, :map do
      allow_nil? true
      public? true
      description "元数据"
      default %{}
    end

    timestamps()
  end

  relationships do
    belongs_to :parent, __MODULE__ do
      attribute_writable? true
    end

    has_many :children, __MODULE__ do
      destination_attribute :parent_id
    end

    many_to_many :roles, Teen.SystemSettings.Role do
      through Teen.SystemSettings.RolePermission
      source_attribute_on_join_resource :permission_id
      destination_attribute_on_join_resource :role_id
    end
  end

  calculations do
    calculate :is_active, :boolean, expr(status == 1)
    calculate :is_menu, :boolean, expr(type == "menu")
    calculate :is_button, :boolean, expr(type == "button")
    calculate :is_api, :boolean, expr(type == "api")
    calculate :has_children, :boolean, expr(count(children) > 0)
  end

  identities do
    identity :unique_code, [:code]
  end

  validations do
    validate match(:code, ~r/^[a-zA-Z][a-zA-Z0-9_:]*$/) do
      message "权限代码只能包含字母、数字、下划线和冒号，且必须以字母开头"
    end
  end
end
