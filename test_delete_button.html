<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>删除按钮测试</title>
    <script src="https://unpkg.com/phoenix@1.7.0/priv/static/phoenix.js"></script>
    <script src="https://unpkg.com/phoenix_live_view@0.18.0/priv/static/phoenix_live_view.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            padding: 8px 12px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }
        .delete-button {
            background-color: #dc3545;
            color: white;
        }
        .delete-button:hover {
            background-color: #c82333;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>删除按钮测试</h1>
        <p>这个页面用于测试删除按钮的点击事件是否正常工作。</p>
        
        <h2>测试按钮</h2>
        
        <!-- 模拟AdminButtonGroup.delete_button的HTML结构 -->
        <button
            type="button"
            class="test-button delete-button"
            onclick="testDeleteClick('test-id-1')"
            data-confirm=""
        >
            <i class="fas fa-trash"></i> 删除按钮 1
        </button>
        
        <button
            type="button"
            class="test-button delete-button"
            onclick="testDeleteClick('test-id-2')"
            data-confirm=""
        >
            <i class="fas fa-trash"></i> 删除按钮 2
        </button>
        
        <!-- 模拟带有phx-click的按钮 -->
        <button
            type="button"
            class="test-button delete-button"
            data-phx-click="show_delete_dialog"
            data-phx-value-id="test-id-3"
            onclick="testPhxClick('test-id-3')"
        >
            <i class="fas fa-trash"></i> PHX删除按钮
        </button>
        
        <h2>事件日志</h2>
        <div id="log" class="log">
            <div>等待按钮点击事件...</div>
        </div>
        
        <h2>测试说明</h2>
        <ul>
            <li>点击上面的删除按钮应该会在日志中显示相应的事件</li>
            <li>如果按钮没有反应，可能是事件处理器有问题</li>
            <li>检查浏览器控制台是否有JavaScript错误</li>
        </ul>
    </div>

    <script>
        function addLog(message) {
            const log = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.textContent = `[${timestamp}] ${message}`;
            log.appendChild(logEntry);
            log.scrollTop = log.scrollHeight;
        }
        
        function testDeleteClick(id) {
            addLog(`🗑️ 删除按钮被点击，ID: ${id}`);
            console.log('删除按钮点击事件', { id });
            
            // 模拟显示确认对话框
            if (confirm(`确定要删除ID为 ${id} 的项目吗？`)) {
                addLog(`✅ 用户确认删除 ID: ${id}`);
                console.log('用户确认删除', { id });
            } else {
                addLog(`❌ 用户取消删除 ID: ${id}`);
                console.log('用户取消删除', { id });
            }
        }
        
        function testPhxClick(id) {
            addLog(`🔄 PHX删除按钮被点击，ID: ${id}`);
            console.log('PHX删除按钮点击事件', { id });
            
            // 模拟Phoenix LiveView事件
            addLog(`📡 模拟发送 phx-click="show_delete_dialog" 事件，ID: ${id}`);
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            addLog('🚀 测试页面已加载完成');
            
            // 检查所有删除按钮
            const deleteButtons = document.querySelectorAll('.delete-button');
            addLog(`🔍 找到 ${deleteButtons.length} 个删除按钮`);
            
            deleteButtons.forEach((button, index) => {
                addLog(`📋 按钮 ${index + 1}: ${button.textContent.trim()}`);
            });
        });
    </script>
</body>
</html>
