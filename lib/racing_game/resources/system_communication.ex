defmodule RacingGame.SystemCommunication do
  @moduledoc """
  系统通信资源

  管理系统消息、公告、通知等通信内容
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: RacingGame,
    extensions: [AshAdmin.Resource]

  postgres do
    table "system_communications"
    repo Cypridina.Repo
  end

  admin do
    table_columns [:id, :type, :title, :priority, :active, :recipient_type, :inserted_at]
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active
    define :list_by_type
    define :list_by_priority
    define :get_user_messages
    define :mark_as_read
    define :list_with_filters
  end

  actions do
    defaults [:read, :destroy]

    create :create do
      accept [
        :type, :title, :content, :recipient_id, :recipient_type,
        :priority, :active, :expires_at, :created_by
      ]

      change before_action(fn changeset, _context ->
        # 设置默认值，使用 force_change_attribute 来绕过验证限制
        changeset
        |> Ash.Changeset.force_change_attribute(:active, true)
        |> Ash.Changeset.force_change_attribute(:recipient_type, :all)
      end)
    end

    update :update do
      accept [
        :title, :content, :recipient_id, :recipient_type,
        :priority, :active, :expires_at
      ]
    end

    read :list_active do
      filter expr(active == true and (expires_at > now() or is_nil(expires_at)))
    end

    read :list_by_type do
      argument :type, :atom, allow_nil?: false
      filter expr(type == ^arg(:type))
    end

    read :list_by_priority do
      argument :priority, :atom, allow_nil?: false
      filter expr(priority == ^arg(:priority))
    end

    read :get_user_messages do
      argument :user_id, :uuid, allow_nil?: false

      filter expr(
        (recipient_type == "all" or
         (recipient_type == "user" and recipient_id == ^arg(:user_id))) and
        active == true and
        (expires_at > now() or is_nil(expires_at))
      )
    end

    update :mark_as_read do
      accept []
      argument :user_id, :uuid, allow_nil?: false

      change after_action(fn _changeset, communication, context ->
        # 创建阅读记录
        case RacingGame.SystemCommunicationRead.create(%{
          communication_id: communication.id,
          user_id: context.arguments.user_id,
          read_at: DateTime.utc_now()
        }) do
          {:ok, _read_record} -> {:ok, communication}
          {:error, error} -> {:error, error}
        end
      end)
    end

    read :list_with_filters do
      argument :type_filter, :atom, allow_nil?: true
      argument :status_filter, :atom, allow_nil?: true
      argument :search_query, :string, allow_nil?: true
      argument :page, :integer, allow_nil?: true, default: 1
      argument :per_page, :integer, allow_nil?: true, default: 20

      prepare build(sort: [inserted_at: :desc])

      filter expr(
        if(not is_nil(^arg(:type_filter)) and ^arg(:type_filter) != :all,
           do: type == ^arg(:type_filter),
           else: true) and
        if(not is_nil(^arg(:status_filter)) and ^arg(:status_filter) != :all,
           do: if(^arg(:status_filter) == :active, do: active == true, else: active == false),
           else: true) and
        if(not is_nil(^arg(:search_query)) and ^arg(:search_query) != "",
           do: contains(title, ^arg(:search_query)) or contains(content, ^arg(:search_query)),
           else: true)
      )

      prepare build(load: [:recipient, :creator])

      pagination offset?: true, keyset?: false, countable: true, default_limit: 20
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :type, :atom do
      allow_nil? false
      public? true
      description "通信类型：message(消息), announcement(公告), notification(通知)"
      constraints one_of: [:message, :announcement, :notification]
    end

    attribute :title, :string do
      allow_nil? false
      public? true
      description "标题"
      constraints max_length: 200
    end

    attribute :content, :string do
      allow_nil? false
      public? true
      description "内容"
      constraints max_length: 5000
    end

    attribute :recipient_id, :uuid do
      allow_nil? true
      public? true
      description "接收者用户ID，为空表示发送给所有用户"
    end

    attribute :recipient_type, :atom do
      allow_nil? false
      public? true
      description "接收者类型：all(所有用户), user(特定用户), admin(管理员)"
      constraints one_of: [:all, :user, :admin]
      default :all
    end

    attribute :priority, :atom do
      allow_nil? false
      public? true
      description "优先级：low(低), medium(中), high(高)"
      constraints one_of: [:low, :medium, :high]
      default :medium
    end

    attribute :active, :boolean do
      allow_nil? false
      public? true
      description "是否启用"
      default true
    end

    attribute :expires_at, :utc_datetime do
      allow_nil? true
      public? true
      description "过期时间"
    end

    attribute :created_by, :uuid do
      allow_nil? true
      public? true
      description "创建者ID"
    end

    timestamps()
  end

  relationships do
    belongs_to :recipient, Cypridina.Accounts.User do
      source_attribute :recipient_id
      destination_attribute :id
    end

    belongs_to :creator, Cypridina.Accounts.User do
      source_attribute :created_by
      destination_attribute :id
    end

    has_many :read_records, RacingGame.SystemCommunicationRead do
      source_attribute :id
      destination_attribute :communication_id
    end
  end

  validations do
    validate present([:type, :title, :content])
    validate string_length(:title, max: 200)
    validate string_length(:content, max: 5000)
  end

  calculations do
    calculate :is_expired, :boolean, expr(expires_at < now() and not is_nil(expires_at))

    calculate :read_count, :integer do
      calculation fn records, _context ->
        records
        |> Enum.map(fn record ->
          read_count = length(record.read_records || [])
          {record.id, read_count}
        end)
        |> Map.new()
      end
    end
  end
end
