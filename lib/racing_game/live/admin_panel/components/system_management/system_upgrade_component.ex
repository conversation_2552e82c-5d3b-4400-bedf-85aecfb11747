defmodule RacingGame.Live.AdminPanel.SystemUpgradeComponent do
  @moduledoc """
  系统升级管理组件 - 仅限管理员使用
  """
  use CypridinaWeb, :live_component

  alias CypridinaWeb.AuthHelper
  alias Cypridina.Accounts.User
  require Ash.Query
  require Logger

  def update(assigns, socket) do
    socket = assign(socket, assigns)

    if AuthHelper.has_permission?(socket.assigns.current_user, :admin) do
      socket =
        socket
        |> assign_defaults()
        |> load_upgrade_info()

      {:ok, socket}
    else
      # 非管理员用户显示无权限信息
      socket =
        socket
        |> assign_defaults()
        |> assign(:unauthorized, true)
      {:ok, socket}
    end
  end

  defp assign_defaults(socket) do
    socket
    |> assign(:current_version, "v1.0.0")
    |> assign(:available_upgrades, [])
    |> assign(:upgrade_history, [])
    |> assign(:upgrade_in_progress, false)
    |> assign(:selected_version, nil)
    |> assign(:show_confirm_modal, false)
    |> assign(:upgrade_log, "")
    |> assign(:compatibility_issues, [])
    |> assign(:upgrade_progress, 0)
    |> assign(:current_step, 0)
    |> assign(:total_steps, 5)
    |> assign(:current_status, "准备中...")
    |> assign(:unauthorized, false)
  end

  defp load_upgrade_info(socket) do
    # 模拟升级信息（实际项目中应该从数据库或API获取）
    current_version = "v1.0.0"

    available_upgrades = [
      %{
        version: "v1.1.0",
        release_date: "2024-01-15",
        changelog: ["新增用户管理功能", "修复已知bug", "性能优化"],
        size: "25.6 MB"
      },
      %{
        version: "v1.2.0",
        release_date: "2024-02-01",
        changelog: ["新增系统监控", "增强安全性", "UI界面优化"],
        size: "32.1 MB"
      }
    ]

    upgrade_history = [
      %{
        version: "v1.0.0",
        timestamp: "2023-12-01 10:30:00",
        status: :success,
        user: "admin"
      }
    ]

    socket
    |> assign(:current_version, current_version)
    |> assign(:available_upgrades, available_upgrades)
    |> assign(:upgrade_history, upgrade_history)
  end

  def handle_event("select_version", %{"version" => version}, socket) do
    # 获取所选版本的详细信息
    version_details = Enum.find(socket.assigns.available_upgrades, &(&1.version == version))

    # 检查兼容性问题（模拟）
    compatibility_issues = case version do
      "v1.2.0" -> ["需要Elixir 1.15+", "数据库需要迁移"]
      _ -> []
    end

    socket =
      socket
      |> assign(:selected_version, version_details)
      |> assign(:compatibility_issues, compatibility_issues)
    {:noreply, socket}
  end

  def handle_event("confirm_upgrade", _params, socket) do
    if socket.assigns.selected_version do
      # 模拟升级过程
      Task.start(fn ->
        # 模拟升级步骤
        steps = [
          "正在备份当前系统...",
          "正在下载升级包...",
          "正在验证升级包...",
          "正在应用升级...",
          "正在重启服务..."
        ]

        Enum.with_index(steps, 1)
        |> Enum.each(fn {step, index} ->
          progress = div(index * 100, length(steps))

          send_update(__MODULE__,
            id: socket.assigns.id,
            upgrade_progress: progress,
            current_step: index,
            current_status: step,
            upgrade_log: socket.assigns.upgrade_log <> "#{step}\n"
          )

          # 模拟处理时间
          Process.sleep(2000)
        end)

        # 升级完成
        send_update(__MODULE__,
          id: socket.assigns.id,
          upgrade_in_progress: false,
          upgrade_progress: 100,
          current_status: "升级完成",
          upgrade_log: socket.assigns.upgrade_log <> "升级成功完成！\n"
        )
      end)

      socket =
        socket
        |> assign(:show_confirm_modal, false)
        |> assign(:upgrade_in_progress, true)
        |> assign(:upgrade_progress, 0)
        |> assign(:current_step, 0)
        |> assign(:current_status, "开始升级...")
        |> assign(:upgrade_log, "开始升级到版本 #{socket.assigns.selected_version.version}\n")

      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  def handle_event("cancel_upgrade", _params, socket) do
    socket = assign(socket, :show_confirm_modal, false)
    {:noreply, socket}
  end

  def handle_event("show_confirm", _params, socket) do
    socket = assign(socket, :show_confirm_modal, true)
    {:noreply, socket}
  end

  def render(assigns) do
    ~H"""
    <div class="p-6 bg-white rounded-lg shadow-md">
      <%= if @unauthorized do %>
        <h3 class="text-xl font-bold text-red-600 mb-4">权限不足</h3>
        <p class="text-gray-700">您没有访问系统升级管理的权限。只有管理员可以查看此页面。</p>
      <% else %>
        <h3 class="text-xl font-bold text-gray-800 mb-6">系统升级管理</h3>

      <!-- 当前版本信息 -->
      <div class="mb-8 p-4 border border-gray-200 rounded-lg bg-gray-50">
        <h4 class="text-lg font-semibold text-gray-800 mb-2">当前系统版本</h4>
        <p class="text-2xl font-bold text-gray-800"><%= @current_version %></p>
      </div>

      <!-- 可用升级 -->
      <div class="mb-8 p-4 border border-gray-200 rounded-lg bg-gray-50">
        <h4 class="text-lg font-semibold text-gray-800 mb-4">可用升级</h4>

        <%= if Enum.empty?(@available_upgrades) do %>
          <p class="text-gray-500 italic">当前系统已是最新版本，没有可用升级。</p>
        <% else %>
          <div class="space-y-4">
            <%= for upgrade <- @available_upgrades do %>
              <div class="p-4 border border-blue-200 rounded-md bg-blue-50 hover:border-blue-300 transition">
                <div class="flex justify-between items-start">
                  <div>
                    <h5 class="text-lg font-medium text-blue-800">版本 <%= upgrade.version %></h5>
                    <p class="text-sm text-blue-700 mt-1"><%= upgrade.release_date %></p>
                  </div>
                  <button
                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    phx-click="select_version"
                    phx-target={@myself}
                    phx-value-version={upgrade.version}
                  >
                    查看详情
                  </button>
                </div>

                <%= if @selected_version && @selected_version.version == upgrade.version do %>
                  <div class="mt-4 p-3 bg-white rounded-md border border-gray-200">
                    <h6 class="font-medium text-gray-800 mb-2">更新内容:</h6>
                    <ul class="list-disc list-inside text-gray-700 space-y-1 text-sm mb-4">
                      <%= for change <- @selected_version.changelog do %>
                        <li><%= change %></li>
                      <% end %>
                    </ul>
                <%= if @compatibility_issues != [] do %>
                  <div class="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                    <h6 class="font-medium text-yellow-800 mb-2">兼容性警告:</h6>
                    <ul class="list-disc list-inside text-yellow-700 space-y-1 text-sm">
                      <%= for issue <- @compatibility_issues do %>
                        <li><%= issue %></li>
                      <% end %>
                    </ul>
                  </div>
                <% end %>

                <div class="flex justify-end">
                  <button
                    class={"px-4 py-2 rounded-md #{if @compatibility_issues != [], do: "bg-gray-400 text-gray-700 cursor-not-allowed", else: "bg-green-600 text-white hover:bg-green-700"}"}
                    phx-click="show_confirm"
                    phx-target={@myself}
                    disabled={@compatibility_issues != []}
                    title={if @compatibility_issues != [], do: "存在兼容性问题，无法升级", else: ""}
                  >
                    升级到此版本
                  </button>
                </div>
                  </div>
                <% end %>
              </div>
            <% end %>
          </div>
        <% end %>
      </div>

      <!-- 升级历史 -->
      <div class="mb-8 p-4 border border-gray-200 rounded-lg bg-gray-50">
        <h4 class="text-lg font-semibold text-gray-800 mb-4">升级历史</h4>

        <div class="overflow-x-auto">
          <table class="min-w-full bg-white">
            <thead>
              <tr class="bg-gray-100">
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">版本</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">升级时间</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作人</th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
              <%= if Enum.empty?(@upgrade_history) do %>
                <tr>
                  <td colspan="4" class="py-4 px-4 text-center text-gray-500 italic">暂无升级历史</td>
                </tr>
              <% else %>
                <%= for history <- @upgrade_history do %>
                  <tr>
                    <td class="py-2 px-4 text-sm text-gray-900"><%= history.version %></td>
                    <td class="py-2 px-4 text-sm text-gray-900"><%= history.timestamp %></td>
                    <td class="py-2 px-4 text-sm">
                      <%= if history.status == :success do %>
                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">成功</span>
                      <% else %>
                        <span class="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">失败</span>
                      <% end %>
                    </td>
                    <td class="py-2 px-4 text-sm text-gray-900"><%= history.user %></td>
                  </tr>
                <% end %>
              <% end %>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 升级确认模态框 -->
      <%= if @show_confirm_modal do %>
        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div class="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 class="text-lg font-bold text-gray-800 mb-4">确认升级</h3>
            <p class="text-gray-700 mb-4">
              您确定要将系统升级到版本 <%= @selected_version.version %> 吗？
              升级过程中系统可能会暂时不可用，请确保在非高峰期执行此操作。
            </p>
            <div class="flex justify-end space-x-4 pt-4 border-t border-gray-200">
              <button
                class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-400 transition-colors duration-200 font-medium"
                phx-click="cancel_upgrade"
                phx-target={@myself}
              >
                取消
              </button>
              <button
                class="px-6 py-2 border border-green-600 bg-green-600 text-white rounded-md hover:bg-green-700 hover:border-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors duration-200 font-medium shadow-sm"
                phx-click="confirm_upgrade"
                phx-target={@myself}
              >
                确认升级
              </button>
            </div>
          </div>
        </div>
      <% end %>

      <!-- 升级进度模态框 -->
      <%= if @upgrade_in_progress do %>
        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div class="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 class="text-lg font-bold text-gray-800 mb-4">系统升级中</h3>
            <div class="mb-4">
              <div class="w-full bg-gray-200 rounded-full h-2.5 overflow-hidden">
                <div class="bg-blue-600 h-2.5 rounded-full transition-all duration-500 ease-in-out" style={"width: #{@upgrade_progress}%"}></div>
              </div>
              <div class="mt-2 text-sm text-gray-700 flex justify-between">
                <span><%= @upgrade_progress %>% 完成</span>
                <span><%= @current_step %> / <%= @total_steps %></span>
              </div>
            </div>
            <p class="text-gray-700 text-sm mb-4"><%= @current_status %></p>
            <div class="bg-gray-50 p-3 rounded-md h-32 overflow-y-auto text-xs text-gray-700 font-mono">
              <%= @upgrade_log %>
            </div>
          </div>
        </div>
      <% end %>
      <% end %>
    </div>
    """
  end
end
