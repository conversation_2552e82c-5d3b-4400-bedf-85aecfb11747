defmodule RacingGame.Live.AdminPanel.Repositories.Accounts.UserIdentityRepository do
  @moduledoc """
  用户身份认证数据访问仓储
  
  提供用户身份认证相关的数据访问抽象：
  - 用户身份管理
  - 认证令牌管理
  - 登录记录查询
  - 身份验证历史
  """

  require Logger
  alias Ash.Query
  import Ash.Query
  alias Cypridina.Accounts.{UserIdentity, Token}
  alias RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder
  require Ash.Query

  # 常量定义
  @cache_ttl 300  # 5分钟缓存
  @default_limit 20
  @max_limit 100

  # ============================================================================
  # 用户身份查询
  # ============================================================================

  @doc """
  根据用户ID获取身份信息

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - `{:ok, identities}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_identities(user_id, options \\ []) do
    Logger.debug("🔍 [身份仓储] 获取用户身份: #{user_id}")
    
    try do
      query = UserIdentity
      |> Ash.Query.filter(user_id == user_id)
      |> maybe_preload(options[:preload])
      |> apply_sort([inserted_at: :desc])
      |> maybe_limit(options[:limit])
      
      case Ash.read(query) do
        {:ok, identities} -> {:ok, identities}
        {:error, error} ->
          Logger.error("❌ [身份仓储] 获取身份失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [身份仓储] 获取身份异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  根据身份类型和值查找用户身份

  ## 参数
  - `strategy` - 身份策略
  - `uid` - 身份唯一标识
  - `options` - 选项

  ## 返回
  - `{:ok, identity}` - 成功
  - `{:error, :not_found}` - 未找到
  - `{:error, reason}` - 其他错误
  """
  def get_identity_by_strategy_and_uid(strategy, uid, options \\ []) do
    Logger.debug("🔍 [身份仓储] 根据策略和UID获取身份: #{strategy}/#{uid}")
    
    try do
      query = UserIdentity
      |> Ash.Query.filter(strategy == strategy and uid == uid)
      |> maybe_preload(options[:preload])
      
      case Ash.read_one(query) do
        {:ok, identity} when not is_nil(identity) -> {:ok, identity}
        {:ok, nil} -> {:error, :not_found}
        {:error, error} ->
          Logger.error("❌ [身份仓储] 获取身份失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [身份仓储] 获取身份异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  分页查询身份列表

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, %{identities: identities, page_info: page_info}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_identities_paginated(params \\ %{}) do
    Logger.debug("📋 [身份仓储] 分页查询身份列表")
    
    try do
      normalized_params = normalize_list_params(params)
      
      with {:ok, query} <- AccountsQueryBuilder.build_identity_list_query(normalized_params),
           {:ok, results} <- execute_paginated_query(query, normalized_params) do
        {:ok, %{identities: results.items, page_info: results.page_info}}
      else
        {:error, reason} -> {:error, reason}
      end
    rescue
      error ->
        Logger.error("❌ [身份仓储] 分页查询异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # ============================================================================
  # 认证令牌管理
  # ============================================================================

  @doc """
  根据用户ID获取令牌

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - `{:ok, tokens}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_tokens(user_id, options \\ []) do
    Logger.debug("🎫 [身份仓储] 获取用户令牌: #{user_id}")
    
    try do
      query = Token
      |> Ash.Query.filter(user_id == user_id)
      |> maybe_filter_token_purpose(options[:purpose])
      |> maybe_filter_active_tokens(options[:active_only])
      |> apply_sort([inserted_at: :desc])
      |> maybe_limit(options[:limit])
      
      case Ash.read(query) do
        {:ok, tokens} -> {:ok, tokens}
        {:error, error} ->
          Logger.error("❌ [身份仓储] 获取令牌失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [身份仓储] 获取令牌异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  根据令牌值获取令牌信息

  ## 参数
  - `token_value` - 令牌值
  - `options` - 选项

  ## 返回
  - `{:ok, token}` - 成功
  - `{:error, :not_found}` - 未找到
  - `{:error, reason}` - 其他错误
  """
  def get_token_by_value(token_value, options \\ []) do
    Logger.debug("🎫 [身份仓储] 根据值获取令牌")
    
    try do
      query = Token
      |> Ash.Query.filter(token == token_value)
      |> maybe_preload(options[:preload])
      
      case Ash.read_one(query) do
        {:ok, token} when not is_nil(token) -> {:ok, token}
        {:ok, nil} -> {:error, :not_found}
        {:error, error} ->
          Logger.error("❌ [身份仓储] 获取令牌失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [身份仓储] 获取令牌异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  验证令牌有效性

  ## 参数
  - `token_value` - 令牌值
  - `purpose` - 令牌用途（可选）

  ## 返回
  - `{:ok, token}` - 令牌有效
  - `{:error, :invalid}` - 令牌无效
  - `{:error, :expired}` - 令牌过期
  - `{:error, reason}` - 其他错误
  """
  def validate_token(token_value, purpose \\ nil) do
    Logger.debug("✅ [身份仓储] 验证令牌")
    
    with {:ok, token} <- get_token_by_value(token_value, preload: [:user]),
         :ok <- check_token_purpose(token, purpose),
         :ok <- check_token_expiry(token) do
      {:ok, token}
    else
      {:error, :not_found} -> {:error, :invalid}
      error -> error
    end
  end

  # ============================================================================
  # 身份创建和更新
  # ============================================================================

  @doc """
  创建用户身份

  ## 参数
  - `identity_data` - 身份数据

  ## 返回
  - `{:ok, identity}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_identity(identity_data) do
    Logger.info("➕ [身份仓储] 创建用户身份")
    
    try do
      case UserIdentity
           |> Ash.Changeset.for_create(:create, identity_data)
           |> Ash.create() do
        {:ok, identity} ->
          clear_cache(:identity)
          Logger.info("✅ [身份仓储] 身份创建成功: #{identity.id}")
          {:ok, identity}
        {:error, error} ->
          Logger.error("❌ [身份仓储] 创建身份失败: #{inspect(error)}")
          {:error, :create_failed}
      end
    rescue
      error ->
        Logger.error("❌ [身份仓储] 创建身份异常: #{inspect(error)}")
        {:error, :create_error}
    end
  end

  @doc """
  更新用户身份

  ## 参数
  - `identity_id` - 身份ID
  - `update_data` - 更新数据

  ## 返回
  - `{:ok, identity}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_identity(identity_id, update_data) do
    Logger.info("✏️ [身份仓储] 更新用户身份: #{identity_id}")
    
    with {:ok, identity} <- get_identity_by_id(identity_id),
         {:ok, updated_identity} <- do_update_identity(identity, update_data) do
      
      clear_cache(:identity, identity_id)
      Logger.info("✅ [身份仓储] 身份更新成功: #{identity_id}")
      {:ok, updated_identity}
    else
      error -> error
    end
  end

  @doc """
  删除用户身份

  ## 参数
  - `identity_id` - 身份ID

  ## 返回
  - `:ok` - 成功
  - `{:error, reason}` - 失败
  """
  def delete_identity(identity_id) do
    Logger.info("🗑️ [身份仓储] 删除用户身份: #{identity_id}")
    
    with {:ok, identity} <- get_identity_by_id(identity_id),
         :ok <- do_delete_identity(identity) do
      
      clear_cache(:identity, identity_id)
      Logger.info("✅ [身份仓储] 身份删除成功: #{identity_id}")
      :ok
    else
      error -> error
    end
  end

  # ============================================================================
  # 统计查询
  # ============================================================================

  @doc """
  获取身份统计信息

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, statistics}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_identity_statistics(options \\ []) do
    Logger.debug("📊 [身份仓储] 获取身份统计")
    
    try do
      stats = %{
        total_identities: get_total_identities(),
        identities_by_strategy: get_identities_by_strategy(),
        active_tokens: get_active_tokens_count(),
        expired_tokens: get_expired_tokens_count(),
        recent_logins: get_recent_logins_count(options[:days] || 7)
      }
      
      {:ok, stats}
    rescue
      error ->
        Logger.error("❌ [身份仓储] 获取统计异常: #{inspect(error)}")
        {:error, :statistics_error}
    end
  end

  # ============================================================================
  # 缓存管理
  # ============================================================================

  @doc """
  清除身份相关缓存

  ## 参数
  - `cache_type` - 缓存类型
  - `id` - ID（可选）
  """
  def clear_cache(cache_type, id \\ nil) do
    cache_patterns = case {cache_type, id} do
      {:identity, nil} -> ["identity:*"]
      {:identity, identity_id} -> ["identity:#{identity_id}:*"]
      {:token, nil} -> ["token:*"]
      {:token, token_id} -> ["token:#{token_id}:*"]
      {:all, _} -> ["identity:*", "token:*"]
    end
    
    Logger.debug("🧹 [身份仓储] 清除缓存: #{inspect(cache_patterns)}")
    Enum.each(cache_patterns, &clear_cache_pattern/1)
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 标准化列表参数
  defp normalize_list_params(params) do
    %{
      page: max(1, params[:page] || 1),
      limit: min(params[:limit] || @default_limit, @max_limit),
      search: String.trim(params[:search] || ""),
      filters: params[:filters] || %{},
      sort: params[:sort] || [inserted_at: :desc],
      preload: params[:preload] || []
    }
  end

  # 根据ID获取身份
  defp get_identity_by_id(identity_id) do
    try do
      case UserIdentity |> Ash.get(identity_id) do
        {:ok, identity} -> {:ok, identity}
        {:error, %Ash.Error.Query.NotFound{}} -> {:error, :not_found}
        {:error, error} ->
          Logger.error("❌ [身份仓储] 获取身份失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [身份仓储] 获取身份异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # 执行更新身份
  defp do_update_identity(identity, update_data) do
    try do
      case identity
           |> Ash.Changeset.for_update(:update, update_data)
           |> Ash.update() do
        {:ok, updated_identity} -> {:ok, updated_identity}
        {:error, error} ->
          Logger.error("❌ [身份仓储] 更新身份失败: #{inspect(error)}")
          {:error, :update_failed}
      end
    rescue
      error ->
        Logger.error("❌ [身份仓储] 更新身份异常: #{inspect(error)}")
        {:error, :update_error}
    end
  end

  # 执行删除身份
  defp do_delete_identity(identity) do
    try do
      case Ash.destroy(identity) do
        :ok -> :ok
        {:error, error} ->
          Logger.error("❌ [身份仓储] 删除身份失败: #{inspect(error)}")
          {:error, :delete_failed}
      end
    rescue
      error ->
        Logger.error("❌ [身份仓储] 删除身份异常: #{inspect(error)}")
        {:error, :delete_error}
    end
  end

  # 执行分页查询
  defp execute_paginated_query(query, params) do
    offset = (params.page - 1) * params.limit
    
    case query
         |> maybe_preload(params.preload)
         |> Ash.Query.page(count: true, limit: params.limit, offset: offset)
         |> Ash.read() do
      {:ok, %Ash.Page.Offset{results: items, count: total_count}} ->
        page_info = %{
          total_count: total_count,
          page: params.page,
          limit: params.limit,
          total_pages: ceil(total_count / params.limit)
        }
        {:ok, %{items: items, page_info: page_info}}
      {:error, error} ->
        Logger.error("❌ [身份仓储] 分页查询失败: #{inspect(error)}")
        {:error, :query_failed}
    end
  end

  # 可能预加载关联数据
  defp maybe_preload(query, []), do: query
  defp maybe_preload(query, preload) when is_list(preload) do
    Ash.Query.load(query, preload)
  end

  # 可能限制数量
  defp maybe_limit(query, nil), do: query
  defp maybe_limit(query, limit) when is_integer(limit) and limit > 0 do
    Ash.Query.limit(query, limit)
  end
  defp maybe_limit(query, _), do: query

  # 应用排序
  defp apply_sort(query, sort) when is_list(sort) do
    Ash.Query.sort(query, sort)
  end
  defp apply_sort(query, _), do: query

  # 可能过滤令牌用途
  defp maybe_filter_token_purpose(query, nil), do: query
  defp maybe_filter_token_purpose(query, purpose) do
    Ash.Query.filter(query, purpose  == purpose)
  end

  # 可能过滤活跃令牌
  defp maybe_filter_active_tokens(query, true) do
    now = DateTime.utc_now()
    Ash.Query.filter(query, expires_at  > now)
  end
  defp maybe_filter_active_tokens(query, _), do: query

  # 检查令牌用途
  defp check_token_purpose(_token, nil), do: :ok
  defp check_token_purpose(token, purpose) do
    if token.purpose == purpose do
      :ok
    else
      {:error, :invalid_purpose}
    end
  end

  # 检查令牌过期
  defp check_token_expiry(token) do
    if token.expires_at && DateTime.compare(token.expires_at, DateTime.utc_now()) == :lt do
      {:error, :expired}
    else
      :ok
    end
  end

  # 统计函数（简化实现）
  defp get_total_identities do
    case UserIdentity |> Ash.Query.aggregate(:count, :id) |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_identities_by_strategy do
    case UserIdentity 
         |> Ash.Query.aggregate(:count, :id, group: [:strategy]) 
         |> Ash.read() do
      {:ok, results} -> 
        Enum.into(results, %{}, fn %{strategy: strategy, count: count} -> 
          {strategy, count} 
        end)
      _ -> %{}
    end
  end

  defp get_active_tokens_count do
    now = DateTime.utc_now()
    case Token 
         |> Ash.Query.filter(expires_at > now)
         |> Ash.Query.aggregate(:count, :id) 
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_expired_tokens_count do
    now = DateTime.utc_now()
    case Token 
         |> Ash.Query.filter(expires_at <= now)
         |> Ash.Query.aggregate(:count, :id) 
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_recent_logins_count(days) do
    date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
    
    case UserIdentity 
         |> Ash.Query.filter(updated_at >= date_threshold)
         |> Ash.Query.aggregate(:count, :id) 
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  # 缓存操作（需要实现具体的缓存后端）
  defp clear_cache_pattern(_pattern), do: :ok
end
