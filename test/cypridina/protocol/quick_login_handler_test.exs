defmodule Cypridina.Protocol.QuickLoginHandlerTest do
  use ExUnit.Case

  alias Cypridina.Protocol.QuickLoginHandler

  describe "handle_quick_login/1" do
    test "returns error for empty client_uniq_id" do
      assert {:error, "无效的客户端唯一标识"} = QuickLoginHandler.handle_quick_login("")
      assert {:error, "无效的客户端唯一标识"} = QuickLoginHandler.handle_quick_login(nil)
    end

    test "creates a new guest user when client_uniq_id doesn't exist" do
      client_uniq_id = "test_device_#{:rand.uniform(999999)}"

      assert {:ok, user} = QuickLoginHandler.handle_quick_login(client_uniq_id)

      # 验证用户创建成功
      assert user.client_uniq_id == client_uniq_id
      assert String.starts_with?(to_string(user.username), "Guest")
      assert user.numeric_id != nil
    end

    test "returns existing user when client_uniq_id already exists" do
      client_uniq_id = "existing_device_#{:rand.uniform(999999)}"

      # 第一次调用创建用户
      assert {:ok, user1} = QuickLoginHandler.handle_quick_login(client_uniq_id)

      # 第二次调用应该返回相同用户
      assert {:ok, user2} = QuickLoginHandler.handle_quick_login(client_uniq_id)

      assert user1.id == user2.id
      assert user1.client_uniq_id == user2.client_uniq_id
    end
  end
end
