defmodule RacingGame.Live.AdminPanel.Handlers.FilterEventHandler do
  @moduledoc """
  过滤和搜索事件处理器

  处理系统通信管理组件中的过滤、搜索和清除操作。
  提供统一的过滤器管理和搜索功能。
  """

  require Logger
  alias RacingGame.Live.AdminPanel.Operations.SearchOperations
  import Phoenix.Component, only: [assign: 3]
  import Phoenix.LiveView, only: [put_flash: 3]

  # 常量定义
  @valid_types ~w[message announcement notification]a
  @valid_statuses [true, false, :active, :inactive]
  @default_flash_messages %{
    filter_updated: "过滤条件已更新",
    search_completed: "搜索完成",
    filters_cleared: "搜索和过滤条件已清除"
  }

  @doc """
  处理过滤器变更事件

  ## 参数
  - `params` - 包含过滤参数的映射
  - `socket` - Phoenix LiveView socket

  ## 返回
  - `{:noreply, socket}` - 更新后的socket
  """
  def handle_filter_change(params, socket) do
    Logger.info("🔍 [过滤器] 过滤条件变更事件触发")
    Logger.debug("🔍 [过滤器] 参数: #{inspect(params)}")

    filter_changes = extract_filter_changes(params, socket)

    if has_filter_changes?(filter_changes, socket) do
      socket = apply_filter_changes(socket, filter_changes)
      {:noreply, socket}
    else
      Logger.debug("🔍 [过滤器] 无变更，跳过更新")
      {:noreply, socket}
    end
  end

  @doc """
  处理搜索事件

  ## 参数
  - `query` - 搜索查询字符串
  - `socket` - Phoenix LiveView socket

  ## 返回
  - `{:noreply, socket}` - 更新后的socket
  """
  def handle_search(query, socket) do
    trimmed_query = String.trim(query || "")
    Logger.info("🔍 [过滤器] 搜索事件触发，查询内容: #{inspect(trimmed_query)}")

    if trimmed_query != socket.assigns.search_query do
      SearchOperations.perform_search_with_filters(socket, trimmed_query)
    else
      Logger.debug("🔍 [过滤器] 搜索查询无变更，跳过搜索")
      {:noreply, socket}
    end
  end

  @doc """
  处理清除过滤器事件

  ## 参数
  - `socket` - Phoenix LiveView socket

  ## 返回
  - `{:noreply, socket}` - 更新后的socket
  """
  def handle_clear_filters(socket) do
    Logger.info("🧹 [过滤器] 清除搜索和过滤条件")

    if has_active_filters?(socket) do
      socket =
        socket
        |> reset_filters_and_search()
        |> SearchOperations.load_communications()
        |> put_flash(:info, @default_flash_messages.filters_cleared)

      {:noreply, socket}
    else
      Logger.debug("🧹 [过滤器] 无活动过滤器，跳过清除")
      {:noreply, socket}
    end
  end

  # ============================================================================
  # 私有辅助函数
  # ============================================================================

  # 提取过滤器变更
  defp extract_filter_changes(params, socket) do
    %{
      type: parse_filter_type(Map.get(params, "type"), socket.assigns.selected_type),
      status: parse_filter_status(Map.get(params, "status"), socket.assigns.selected_status),
      query: String.trim(Map.get(params, "query", socket.assigns.search_query))
    }
  end

  # 检查是否有过滤器变更
  defp has_filter_changes?(changes, socket) do
    changes.type != socket.assigns.selected_type ||
    changes.status != socket.assigns.selected_status ||
    changes.query != socket.assigns.search_query
  end

  # 应用过滤器变更
  defp apply_filter_changes(socket, changes) do
    socket
    |> assign(:selected_type, changes.type)
    |> assign(:selected_status, changes.status)
    |> assign(:search_query, changes.query)
    |> assign(:page, 1)
    |> SearchOperations.load_communications()
    |> put_flash(:info, @default_flash_messages.filter_updated)
  end

  # 检查是否有活动的过滤器
  defp has_active_filters?(socket) do
    socket.assigns.selected_type != nil ||
    socket.assigns.selected_status != nil ||
    (socket.assigns.search_query || "") != ""
  end

  # 解析过滤器类型
  defp parse_filter_type(nil, current), do: current
  defp parse_filter_type("all", _current), do: nil
  defp parse_filter_type(type_str, _current) when is_binary(type_str) do
    type_atom = String.to_atom(type_str)
    if type_atom in @valid_types, do: type_atom, else: nil
  end
  defp parse_filter_type(type, _current) when type in @valid_types, do: type
  defp parse_filter_type(_, _current), do: nil

  # 解析过滤器状态
  defp parse_filter_status(nil, current), do: current
  defp parse_filter_status("all", _current), do: nil
  defp parse_filter_status(value, _current) when value in ["true", "active"], do: true
  defp parse_filter_status(value, _current) when value in ["false", "inactive"], do: false
  defp parse_filter_status(status, _current) when status in @valid_statuses, do: status
  defp parse_filter_status(_, _current), do: nil

  # 重置过滤器和搜索
  defp reset_filters_and_search(socket) do
    socket
    |> assign(:search_query, "")
    |> assign(:selected_type, nil)
    |> assign(:selected_status, nil)
    |> assign(:page, 1)
  end
end
