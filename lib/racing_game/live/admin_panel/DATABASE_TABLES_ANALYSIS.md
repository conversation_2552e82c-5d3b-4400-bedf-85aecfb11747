# 数据库表结构分析与分层架构实现计划

## 📊 数据库表结构总览

### 1. 用户账户系统 (Cypridina.Accounts)
```
users                    - 用户基础信息
user_identities         - 用户身份认证
tokens                   - 认证令牌
agent_relationships      - 代理关系
api_keys                 - API密钥
```

### 2. 账本系统 (Cypridina.Ledger)
```
ledger_accounts         - 账户表
ledger_transfers        - 转账记录
ledger_balances         - 余额记录
```

### 3. 赛车游戏系统 (RacingGame)
```
races                   - 比赛记录
racing_game_bets        - 投注记录
racing_game_stocks      - 股票持仓
system_communications   - 系统通信
system_communication_reads - 通信阅读记录
```

### 4. Teen后台系统

#### 4.1 客服管理 (Teen.CustomerService)
```
customer_chats          - 客服聊天
user_questions          - 用户问题
exchange_orders         - 兑换订单
sensitive_words         - 敏感词
user_tags              - 用户标签
verification_codes      - 验证码
```

#### 4.2 游戏管理 (Teen.GameManagement)
```
platforms              - 平台配置
vip_levels            - VIP等级
robot_configs         - 机器人配置
```

#### 4.3 系统设置 (Teen.SystemSettings)
```
admin_users           - 管理员用户
roles                 - 角色管理
permissions           - 权限管理
operation_logs        - 操作日志
ip_whitelists        - IP白名单
```

#### 4.4 支付系统 (Teen.PaymentSystem)
```
payment_gateways      - 支付网关
payment_configs       - 支付配置
exchange_configs      - 兑换配置
```

#### 4.5 活动系统 (Teen.ActivitySystem)
```
sign_in_activities    - 签到活动
cdkey_activities      - 兑换码活动
activity_configs      - 活动配置
```

#### 4.6 数据统计 (Teen.DataStatistics)
```
system_reports        - 系统报表
online_stats          - 在线统计
channel_stats         - 渠道统计
user_stats           - 用户统计
coin_stats           - 金币统计
retention_stats      - 留存率统计
payment_stats        - 支付统计
ltv_stats           - LTV统计
robot_stats         - 机器人统计
```

#### 4.7 推广系统 (Teen.PromotionSystem)
```
promoters            - 推广员
promotion_channels   - 推广渠道
promotion_settlements - 推广结算
share_managements    - 分享管理
share_settlements    - 分享结算
share_configs        - 分成配置
```

## 🏗️ 分层架构实现计划

### 阶段1: 核心系统分层架构
1. **Cypridina.Accounts** - 用户账户系统
2. **Cypridina.Ledger** - 账本系统
3. **RacingGame** - 赛车游戏系统

### 阶段2: Teen后台系统分层架构
1. **Teen.CustomerService** - 客服管理系统
2. **Teen.GameManagement** - 游戏管理系统
3. **Teen.SystemSettings** - 系统设置

### 阶段3: 扩展系统分层架构
1. ✅ **Teen.PaymentSystem** - 支付系统 **已完成**
2. **Teen.ActivitySystem** - 活动系统
3. **Teen.DataStatistics** - 数据统计系统
4. **Teen.PromotionSystem** - 推广系统

## 📁 目标目录结构

```
lib/racing_game/live/admin_panel/
├── repositories/                  # 数据访问层
│   ├── accounts/                 # 用户账户仓储
│   │   ├── user_repository.ex
│   │   ├── user_identity_repository.ex
│   │   └── agent_relationship_repository.ex
│   ├── ledger/                   # 账本仓储
│   │   ├── account_repository.ex
│   │   ├── transfer_repository.ex
│   │   └── balance_repository.ex
│   ├── racing_game/              # 赛车游戏仓储
│   │   ├── race_repository.ex
│   │   ├── bet_repository.ex
│   │   └── stock_repository.ex
│   ├── teen/                     # Teen系统仓储
│   │   ├── customer_service/
│   │   ├── game_management/
│   │   ├── system_settings/
│   │   ├── payment_system/
│   │   ├── activity_system/
│   │   ├── data_statistics/
│   │   └── promotion_system/
│   └── query_builders/           # 查询构建器
│       ├── accounts_query_builder.ex
│       ├── ledger_query_builder.ex
│       ├── racing_game_query_builder.ex
│       └── teen/
├── services/                     # 业务逻辑层
│   ├── accounts/                 # 用户账户服务
│   ├── ledger/                   # 账本服务
│   ├── racing_game/              # 赛车游戏服务
│   └── teen/                     # Teen系统服务
├── infrastructure/               # 基础设施层
│   ├── cache/                    # 缓存管理
│   ├── events/                   # 事件处理
│   └── notifications/            # 通知系统
└── utils/                        # 工具层
    ├── database_utils.ex         # 数据库工具
    ├── query_utils.ex           # 查询工具
    └── cache_utils.ex           # 缓存工具
```

## 🎯 实现优先级

### 高优先级 (立即实现)
1. **Cypridina.Accounts** - 用户系统是核心基础
2. **Teen.CustomerService** - 客服系统业务重要
3. **Teen.GameManagement** - 游戏管理核心功能

### 中优先级 (第二阶段)
1. **Cypridina.Ledger** - 账本系统
2. **RacingGame** - 赛车游戏系统
3. **Teen.SystemSettings** - 系统设置

### 低优先级 (第三阶段)
1. ✅ **Teen.PaymentSystem** - 支付系统 **已完成**
2. **Teen.ActivitySystem** - 活动系统
3. **Teen.DataStatistics** - 数据统计
4. **Teen.PromotionSystem** - 推广系统

## 📋 实现标准

### Repository层标准
- 统一的错误处理模式
- 缓存策略实现
- 分页查询支持
- 复杂查询构建
- 统计查询功能

### Service层标准
- 业务逻辑封装
- 参数验证
- 事务管理
- 通知集成
- 权限检查

### QueryBuilder层标准
- 动态查询构建
- 过滤条件组合
- 排序和分页
- 搜索功能
- 聚合查询

## 🔧 技术规范

### 命名规范
- Repository: `{Domain}{Entity}Repository`
- Service: `{Domain}{Entity}Service`
- QueryBuilder: `{Domain}QueryBuilder`

### 错误处理规范
- 统一错误返回格式: `{:ok, result}` | `{:error, reason}`
- 详细的错误日志记录
- 用户友好的错误消息

### 缓存策略规范
- TTL缓存机制
- 缓存键命名规范
- 缓存失效策略
- 缓存预热机制

### 日志规范
- 结构化日志记录
- 操作追踪
- 性能监控
- 错误告警

## 📈 预期收益

### 1. 代码质量提升
- 清晰的职责分离
- 统一的代码风格
- 更好的可测试性

### 2. 开发效率提升
- 标准化的开发模式
- 可复用的组件
- 减少重复代码

### 3. 系统性能优化
- 统一的缓存策略
- 优化的查询性能
- 更好的资源利用

### 4. 维护成本降低
- 模块化的架构
- 清晰的依赖关系
- 便于扩展和修改

## 🚀 实现进度

### ✅ 已完成系统

#### 1. Cypridina.Accounts 用户账户系统
- ✅ UserRepository - 用户数据访问仓储
- ✅ UserIdentityRepository - 用户身份认证仓储
- ✅ AgentRelationshipRepository - 代理关系仓储
- ✅ AccountsQueryBuilder - 账户系统查询构建器
- ✅ UserService - 用户业务逻辑服务

#### 2. Cypridina.Ledger 账本系统
- ✅ AccountRepository - 账本账户数据访问仓储
- ✅ TransferRepository - 转账交易数据访问仓储
- ✅ BalanceRepository - 余额数据访问仓储
- ✅ LedgerQueryBuilder - 账本系统查询构建器
- ✅ LedgerService - 账本业务逻辑服务

#### 3. RacingGame 赛车游戏系统
- ✅ RaceRepository - 比赛数据访问仓储
- ✅ BetRepository - 投注数据访问仓储
- ✅ StockRepository - 股票持仓数据访问仓储
- ✅ SystemCommunicationRepository - 系统通信数据访问仓储
- ✅ RacingGameQueryBuilder - 赛车游戏系统查询构建器
- ✅ RacingGameService - 赛车游戏业务逻辑服务

**核心功能完整实现**:
- 🏁 比赛管理 (创建、开始、结束、取消)
- 💰 投注管理 (创建、取消、结算)
- 📈 股票交易 (买入、卖出、持仓管理)
- 📢 系统通信 (消息管理、阅读状态)
- 📊 综合统计和报表
- 🔄 实时事件广播

### ✅ 已完成系统

#### 1. Cypridina.Accounts 用户账户系统 - 完成
#### 2. Cypridina.Ledger 账本系统 - 完成
#### 3. RacingGame 赛车游戏系统 - 完成
#### 4. Teen.CustomerService 客服管理系统 - 完成 ✅

**Teen.CustomerService 实现详情** (100% 完成):
- ✅ CustomerChatRepository - 客服聊天记录管理 (完成)
- ✅ UserQuestionRepository - 用户问题管理 (完成)
- ✅ ExchangeOrderRepository - 兑换订单管理 (完成)
- ✅ SensitiveWordRepository - 敏感词管理 (完成)
- ✅ UserTagRepository - 用户标签管理 (完成)
- ✅ VerificationCodeRepository - 验证码管理 (完成)
- ✅ CustomerServiceQueryBuilder - 查询构建器 (777行代码，完成)
- ✅ CustomerServiceService - 业务服务层 (719行代码，完成)

**系统特性**:
- 🔄 完整的分层架构实现
- 📊 跨仓储查询和统计
- 🔍 敏感词过滤和内容审核
- 📱 短信验证码管理
- 🏷️ 用户标签系统
- 💬 智能客服分配
- 📈 系统健康监控

## 🎮 Teen.GameManagement 游戏管理系统 (✅ 100% 完成)

**实现状态**: Repository层 100% 完成, QueryBuilder 100% 完成, Service层 100% 完成 (5/5 组件)

### 已完成组件:

#### 1. PlatformRepository (✅ 完成 - 316行)
- **功能**: 平台配置数据访问层
- **核心特性**:
  - 平台CRUD操作与缓存管理 (5分钟TTL)
  - 平台状态管理 (启用/禁用)
  - 代理充值开关控制
  - 平台编号查询与验证
  - 高级过滤与分页支持
- **关键方法**: `create_platform/2`, `get_platform_by_number/2`, `list_active_platforms/1`, `toggle_agent_recharge/2`

#### 2. VipLevelRepository (✅ 完成 - 300+行)
- **功能**: VIP等级配置与业务逻辑
- **核心特性**:
  - VIP等级CRUD操作与缓存 (10分钟TTL)
  - 基于充值金额的VIP等级计算 (Decimal精度)
  - VIP特权管理与查询
  - 等级升级条件验证
  - 业务规则引擎集成
- **关键方法**: `calculate_vip_level_by_recharge/2`, `get_vip_privileges/2`, `list_active_vip_levels/1`

#### 3. RobotConfigRepository (✅ 完成 - 609行)
- **功能**: 机器人配置与智能选择
- **核心特性**:
  - 机器人配置CRUD与缓存 (5分钟TTL)
  - 游戏类型与难度等级过滤
  - 智能机器人选择算法 (用户技能匹配)
  - 机器人性能统计分析
  - 策略配置管理 (JSONB)
- **关键方法**: `select_robot_for_user/3`, `list_robots_by_game_type/2`, `get_robot_stats/1`
- **算法**: 用户技能等级 0-20→难度1, 21-50→2, 51-80→3, 81+→4

#### 4. GameManagementQueryBuilder (✅ 完成 - 1228行)
- **功能**: 复杂查询构建器与业务分析
- **核心特性**:
  - 跨Repository查询协调 (平台、VIP、机器人)
  - 综合统计查询与报表生成
  - 用户VIP信息查询 (等级计算、特权、进度)
  - 房间机器人配置查询 (智能匹配)
  - 平台健康状态分析
  - VIP等级优化建议 (间隔分析、特权分布)
  - 机器人性能分析 (胜率分布、难度平衡)
  - 配置一致性检查 (跨系统验证)
- **关键方法**:
  - `build_comprehensive_stats_query/2` - 综合统计
  - `build_user_vip_info_query/3` - 用户VIP信息
  - `build_platform_health_query/2` - 平台健康检查
  - `build_vip_optimization_query/2` - VIP优化建议
  - `build_robot_performance_query/2` - 机器人性能分析
  - `build_config_consistency_query/2` - 配置一致性检查

#### 5. GameManagementService (✅ 完成 - 1804行)
- **功能**: 业务服务协调层
- **核心特性**:
  - 高级业务逻辑协调与工作流管理
  - 跨Repository事务管理 (Ash.DataLayer.transaction)
  - 平台配置管理 (创建、批量状态更新、健康检查)
  - VIP等级计算与特权管理工作流 (升级处理、奖励发放)
  - 机器人智能选择与配置优化 (算法选择、策略定制)
  - 系统健康监控与自动优化 (一致性检查、自动修复)
  - 综合统计分析与业务建议生成
- **关键方法**:
  - `create_platform/2`, `batch_update_platform_status/4` - 平台管理
  - `calculate_user_vip_status/3`, `process_vip_upgrade/3` - VIP管理
  - `smart_select_game_robot/4`, `batch_optimize_robot_configs/3` - 机器人管理
  - `get_comprehensive_statistics/2`, `perform_system_auto_optimization/2` - 系统管理
- **实际规模**: 1,804行代码 (超出预期)

### 系统特性:
- **缓存策略**: 差异化TTL (平台/机器人5分钟, VIP等级10分钟)
- **业务逻辑**: VIP等级计算, 机器人智能选择, 平台状态管理
- **数据精度**: Decimal算术用于VIP充值计算
- **性能优化**: 查询优化, 缓存失效, 统计预计算, 并行查询
- **错误处理**: 结构化日志记录 (emoji前缀)
- **事务管理**: 跨Repository协调操作
- **智能分析**: 健康状态监控, 性能分析, 优化建议生成
- **配置验证**: 一致性检查, 自动修复建议

### � 下一步实现计划

#### 即将开始: GameManagementService 业务服务层
**实施步骤**:
1. 🏗️ 实现 GameManagementService 业务协调层
2. 🔧 集成所有Repository和QueryBuilder组件
3. 💼 实现高级业务工作流
4. 🧪 测试和验证完整功能

### 📋 待实现系统队列

#### 6. Teen.SystemSettings 系统设置管理 (✅ 100% 完成)
- ✅ 管理员用户管理 (AdminUserRepository - 300+ lines)
- ✅ 角色管理 (RoleRepository - 300+ lines)
- ✅ 权限管理 (PermissionRepository - 450+ lines)
- ✅ 操作日志 (OperationLogRepository - 300+ lines)
- ✅ IP白名单 (IpWhitelistRepository - 300+ lines)
- ✅ 查询构建器 (SystemSettingsQueryBuilder - 300+ lines)
- ✅ 业务服务层 (SystemSettingsService - 1,370+ lines)

#### 7. Teen.PaymentSystem - 支付系统 ✅ **已完成**
- ✅ 支付网关 (PaymentGatewayRepository - 300+ lines)
- ✅ 支付配置 (PaymentConfigRepository - 350+ lines)
- ✅ 兑换配置 (ExchangeConfigRepository - 350+ lines)
- ✅ 查询构建器 (PaymentSystemQueryBuilder - 450+ lines)
- ✅ 业务服务层 (PaymentSystemService - 1,480+ lines)

#### 8. Teen.ActivitySystem - 活动系统 ✅ **已完成**
- ✅ 活动管理 (ActivityRepository - 300+ lines)
- ✅ 活动奖励 (ActivityRewardRepository - 300+ lines)
- ✅ 活动参与 (ActivityParticipationRepository - 300+ lines)
- ✅ 活动配置 (ActivityConfigRepository - 300+ lines)
- ✅ 兑换码活动 (CdkeyActivityRepository - 300+ lines)
- ✅ 查询构建器 (ActivitySystemQueryBuilder - 1,500+ lines)
- ✅ 业务服务层 (ActivitySystemService - 2,400+ lines)

#### 9. Teen.DataStatistics - 数据统计系统 ✅ **已完成**
- ✅ 系统报表 (SystemReportRepository - 300 lines)
- ✅ 在线统计 (OnlineStatsRepository - 300 lines)
- ✅ 渠道统计 (ChannelStatsRepository - 300 lines)
- ✅ 用户统计 (UserStatsRepository - 300 lines)
- ✅ 金币统计 (CoinStatsRepository - 300 lines)
- ✅ 留存率统计 (RetentionStatsRepository - 300 lines)
- ✅ 支付统计 (PaymentStatsRepository - 300 lines)
- ✅ LTV统计 (LtvStatsRepository - 300 lines)
- ✅ 机器人统计 (RobotStatsRepository - 300 lines)
- ✅ 查询构建器 (DataStatisticsQueryBuilder - 1,037 lines)
- ✅ 业务服务层 (DataStatisticsService - 1,519 lines)

**Teen.DataStatistics 核心特性**:
- 📊 **9类统计数据管理**: 全面覆盖系统各维度统计
- 📈 **实时数据仪表板**: 实时监控系统运行状态
- 📋 **综合统计报告**: 跨仓储数据整合与分析
- 🔍 **高级数据分析**: 队列分析、用户行为分析、收入分析
- 👥 **用户价值分析**: LTV分群、支付分群、留存分群
- 🏥 **系统健康监控**: 健康评分、告警生成、性能监控
- 🔄 **数据同步机制**: 多源数据同步、批量处理、并行执行
- 🔍 **数据质量管理**: 4维度质量检查(完整性、一致性、准确性、时效性)
- 🔧 **数据修复功能**: 智能数据修复、备份回滚机制
- 💾 **备份回滚机制**: 修复前备份、回滚计划、修复验证

#### 10. 其他扩展系统
- Teen.PromotionSystem - 推广系统

**架构标准**: 每个系统将遵循 Teen.CustomerService 建立的完整分层架构模式，包含 Repository、QueryBuilder、Service 三层实现。

## 📊 总体实现进度

### 已完成系统:
1. **Teen.CustomerService** - ✅ 100% 完成 (6个Repository + QueryBuilder + Service)
2. **Teen.GameManagement** - ✅ 100% 完成 (3个Repository + QueryBuilder + Service)
3. **Teen.SystemSettings** - ✅ 100% 完成 (5个Repository + QueryBuilder + Service)
4. **Teen.PaymentSystem** - ✅ 100% 完成 (3个Repository + QueryBuilder + Service)
5. **Teen.ActivitySystem** - ✅ 100% 完成 (5个Repository + QueryBuilder + Service)
6. **Teen.DataStatistics** - ✅ 100% 完成 (9个Repository + QueryBuilder + Service)

### 代码统计:
- **Teen.CustomerService**: ~2,200行代码 (完整实现)
- **Teen.GameManagement**: ~4,257行代码 (完整实现)
- **Teen.SystemSettings**: ~3,320行代码 (完整实现)
- **Teen.PaymentSystem**: ~2,930行代码 (完整实现)
- **Teen.ActivitySystem**: ~4,500行代码 (完整实现)
- **Teen.DataStatistics**: ~6,200行代码 (完整实现)
- **总计**: ~23,407行高质量分层架构代码

### 🎉 重要里程碑:
**Teen.DataStatistics 数据统计系统现已100%完成！** 这是一个包含9类统计数据管理、实时监控、高级分析、数据质量管理和智能修复功能的完整数据统计系统，总计6,200+行高质量代码。

### 下一步目标:
1. 🎯 实现 Teen.PromotionSystem - 推广系统 (最后一个系统)
2. 📋 完善系统集成和测试
3. 📚 完善文档和部署指南
