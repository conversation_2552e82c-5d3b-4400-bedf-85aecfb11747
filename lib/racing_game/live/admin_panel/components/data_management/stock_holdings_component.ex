defmodule RacingGame.Live.AdminPanel.StockHoldingsComponent do
  @moduledoc """
  股票持仓组件 - 仅限管理员使用

  提供股票持仓数据的查看和管理功能：
  - 持仓记录查看
  - 用户搜索过滤
  - 分页浏览
  - 成本计算
  - 数据导出

  ## 功能特性
  - 权限控制的数据访问
  - 实时数据更新
  - 响应式设计
  - 详细的持仓分析
  - 搜索和过滤功能
  """
  use CypridinaWeb, :live_component

  alias CypridinaWeb.AuthHelper
  alias RacingGame.Stock
  alias RacingGame.Live.AdminPanel.PermissionFilter
  alias Cypridina.Utils.TimeHelper
  require Logger

  # 常量定义
  @default_per_page 20
  @max_per_page 100
  @search_placeholder "按用户名搜索..."
  @decimal_precision 2
  @default_sort_field :updated_at
  @default_sort_direction :desc

  def update(assigns, socket) do
    Logger.debug("📊 [股票持仓] 组件更新")

    socket =
      socket
      |> assign(assigns)
      |> assign_defaults()
      |> load_stocks_data()

    {:ok, socket}
  end

  # ============================================================================
  # 事件处理函数
  # ============================================================================

  def handle_event("refresh", _params, socket) do
    Logger.info("🔄 [股票持仓] 刷新股票持仓数据")
    socket = load_stocks_data(socket)
    {:noreply, socket}
  end

  def handle_event("search", %{"search" => %{"query" => query}}, socket) do
    trimmed_query = String.trim(query || "")
    Logger.info("🔍 [股票持仓] 搜索股票持仓: #{trimmed_query}")

    socket = perform_search(socket, trimmed_query)
    {:noreply, socket}
  end

  def handle_event("clear_search", _params, socket) do
    Logger.info("🧹 [股票持仓] 清除搜索条件")
    socket = clear_search(socket)
    {:noreply, socket}
  end

  def handle_event("page_change", %{"page" => page_str}, socket) do
    case Integer.parse(page_str) do
      {page, ""} when page > 0 ->
        Logger.info("📄 [股票持仓] 切换到第 #{page} 页")
        socket = change_page(socket, page)
        {:noreply, socket}
      _ ->
        Logger.warning("⚠️ [股票持仓] 无效的页码: #{page_str}")
        {:noreply, socket}
    end
  end

  def handle_event("export_data", _params, socket) do
    Logger.info("📤 [股票持仓] 导出股票持仓数据")
    # TODO: 实现数据导出功能
    {:noreply, socket}
  end

  # ============================================================================
  # 私有辅助函数
  # ============================================================================

  defp assign_defaults(socket) do
    socket
    |> assign(:search_query, "")
    |> assign(:page, 1)
    |> assign(:per_page, @default_per_page)
    |> assign(:page_info, nil)
    |> assign(:loading, false)
    |> assign(:search_placeholder, @search_placeholder)
    |> assign(:sort_field, @default_sort_field)
    |> assign(:sort_direction, @default_sort_direction)
  end

  defp perform_search(socket, query) do
    socket
    |> assign(:search_query, query)
    |> assign(:page, 1)
    |> load_stocks_data()
  end

  defp clear_search(socket) do
    socket
    |> assign(:search_query, "")
    |> assign(:page, 1)
    |> load_stocks_data()
  end

  defp change_page(socket, page) do
    socket
    |> assign(:page, page)
    |> load_stocks_data()
  end

  # ============================================================================
  # 数据加载函数
  # ============================================================================

  defp load_stocks_data(socket) do
    Logger.debug("📊 [股票持仓] 开始加载股票持仓数据")

    socket = assign(socket, :loading, true)

    try do
      query_params = extract_query_params(socket)
      query = build_stocks_query(query_params, socket.assigns.current_user)

      case execute_stocks_query(query, query_params) do
        {:ok, results} ->
          Logger.info("✅ [股票持仓] 成功加载 #{length(results.stocks)} 条持仓记录")
          assign_stocks_results(socket, results, query_params)
        {:error, error} ->
          Logger.error("❌ [股票持仓] 加载失败: #{inspect(error)}")
          assign_stocks_error_state(socket, query_params)
      end
    rescue
      error ->
        Logger.error("❌ [股票持仓] 加载异常: #{inspect(error)}")
        assign_stocks_error_state(socket, %{page: 1, per_page: @default_per_page})
    end
  end

  defp extract_query_params(socket) do
    %{
      search_query: String.trim(socket.assigns.search_query || ""),
      page: socket.assigns.page,
      per_page: min(socket.assigns.per_page, @max_per_page),
      sort_field: socket.assigns.sort_field,
      sort_direction: socket.assigns.sort_direction
    }
  end

  defp build_stocks_query(params, user) do
    Stock
    |> Ash.Query.load([:user])
    |> PermissionFilter.apply_user_filter(user, :user_id)
    |> apply_search_filter(params.search_query)
    |> apply_sort(params.sort_field, params.sort_direction)
  end

  defp apply_search_filter(query, ""), do: query
  defp apply_search_filter(query, search_query) do
    import Ash.Query

    # 通过关联的用户名进行搜索
    matching_user_ids = get_matching_user_ids(search_query)

    if length(matching_user_ids) > 0 do
      filter(query, user_id in ^matching_user_ids)
    else
      # 如果没有匹配的用户，返回空结果
      filter(query, user_id == "no-match-found")
    end
  end

  defp get_matching_user_ids(search_query) do
    import Ash.Query

    case Cypridina.Accounts.User
         |> filter(contains(username, ^search_query))
         |> select([:id])
         |> Ash.read() do
      {:ok, users} -> Enum.map(users, & &1.id)
      {:error, error} ->
        Logger.warning("⚠️ [股票持仓] 搜索用户失败: #{inspect(error)}")
        []
    end
  end

  defp apply_sort(query, sort_field, sort_direction) do
    Ash.Query.sort(query, [{sort_field, sort_direction}])
  end

  defp execute_stocks_query(query, params) do
    offset = (params.page - 1) * params.per_page

    case query
         |> Ash.Query.page(count: true, limit: params.per_page, offset: offset)
         |> Ash.read() do
      {:ok, %Ash.Page.Offset{results: stocks, count: total_count}} ->
        {:ok, %{stocks: stocks, total_count: total_count}}
      {:error, error} ->
        {:error, error}
    end
  end

  defp assign_stocks_results(socket, results, params) do
    page_info = %{
      total_count: results.total_count,
      page: params.page,
      per_page: params.per_page
    }

    socket
    |> assign(:stocks_data, results.stocks)
    |> assign(:page_info, page_info)
    |> assign(:loading, false)
  end

  defp assign_stocks_error_state(socket, params) do
    page_info = %{
      total_count: 0,
      page: params.page,
      per_page: params.per_page
    }

    socket
    |> assign(:stocks_data, [])
    |> assign(:page_info, page_info)
    |> assign(:loading, false)
  end

  # ============================================================================
  # 渲染函数
  # ============================================================================

  def render(assigns) do
    ~H"""
    <div class="p-6">
      <!-- 页面头部 -->
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-xl font-semibold">股票持仓</h2>
        <div class="flex items-center space-x-4">
          <%= if @loading do %>
            <div class="loading loading-spinner loading-sm"></div>
            <span class="text-sm text-base-content/60">加载中...</span>
          <% else %>
            <div class="text-sm text-base-content/60">
              <%= if @page_info do %>
                共 <%= @page_info.total_count %> 条记录，第 <%= @page_info.page %> 页
              <% else %>
                暂无数据
              <% end %>
            </div>
          <% end %>
          <button
            phx-click="refresh"
            phx-target={@myself}
            class="btn btn-ghost btn-sm"
            disabled={@loading}
          >
            <.icon name="hero-arrow-path" class="w-4 h-4" />
            刷新
          </button>
        </div>
      </div>

      <!-- 搜索框 -->
      <div class="flex justify-between items-center mb-4">
        <form phx-submit="search" phx-target={@myself} class="flex space-x-2">
          <input
            type="text"
            name="search[query]"
            value={@search_query}
            placeholder={@search_placeholder}
            class="input input-bordered input-sm w-64"
            disabled={@loading}
          />
          <button
            type="submit"
            class="btn btn-outline btn-sm"
            disabled={@loading}
          >
            <.icon name="hero-magnifying-glass" class="w-4 h-4" /> 搜索
          </button>
        </form>
        <div class="flex space-x-2">
          <%= if @search_query != "" do %>
            <button
              phx-click="clear_search"
              phx-target={@myself}
              class="btn btn-ghost btn-sm"
              disabled={@loading}
            >
              <.icon name="hero-x-mark" class="w-4 h-4" /> 清除搜索
            </button>
          <% end %>
          <button
            phx-click="export_data"
            phx-target={@myself}
            class="btn btn-outline btn-sm"
            disabled={@loading or length(@stocks_data || []) == 0}
          >
            <.icon name="hero-arrow-down-tray" class="w-4 h-4" /> 导出
          </button>
        </div>
      </div>

      <%= if @stocks_data && length(@stocks_data) > 0 do %>
        <div class="overflow-x-auto">
          <table class="table table-zebra w-full">
            <thead>
              <tr>
                <th>用户名</th>
                <th>用户ID</th>
                <th>动物ID</th>
                <th>持仓数量</th>
                <th>总成本</th>
                <th>平均成本</th>
                <th>更新时间</th>
              </tr>
            </thead>
            <tbody>
              <%= for stock <- @stocks_data do %>
                <% average_cost = calculate_average_cost(stock) %>
                <% profit_loss = calculate_profit_loss(stock) %>
                <tr>
                  <td class="font-medium">
                    <%= format_user_display(stock) %>
                  </td>
                  <td class="font-mono text-sm">
                    <%= format_user_id(stock.user_id) %>
                  </td>
                  <td>
                    <span class="badge badge-primary"><%= stock.racer_id %></span>
                  </td>
                  <td class="font-medium">
                    <span class="badge badge-outline badge-lg">
                      <%= format_quantity(stock.quantity) %>
                    </span>
                  </td>
                  <td class="font-medium text-info">
                    <div class="flex items-center space-x-1">
                      <.icon name="hero-currency-dollar" class="w-4 h-4" />
                      <span><%= format_decimal(stock.total_cost) %></span>
                    </div>
                  </td>
                  <td class="font-medium text-accent">
                    <%= if average_cost do %>
                      <div class="flex items-center space-x-1">
                        <.icon name="hero-calculator" class="w-4 h-4" />
                        <span><%= format_decimal(average_cost) %></span>
                      </div>
                    <% else %>
                      <span class="text-base-content/40">-</span>
                    <% end %>
                  </td>
                  <td>
                    <div class="flex flex-col">
                      <span class="text-sm"><%= TimeHelper.format_local_short_time(stock.updated_at) %></span>
                      <span class="text-xs text-base-content/60"><%= format_relative_time(stock.updated_at) %></span>
                    </div>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>

        <!-- 分页组件 -->
        <%= if @page_info && @page_info.total_count > @page_info.per_page do %>
          <div class="flex justify-center mt-6">
            <div class="join">
              <%= if @page_info.page > 1 do %>
                <button
                  phx-click="page_change"
                  phx-value-page={@page_info.page - 1}
                  phx-target={@myself}
                  class="join-item btn btn-sm"
                >
                  «
                </button>
              <% end %>

              <%= for page_num <- max(1, @page_info.page - 2)..min(ceil(@page_info.total_count / @page_info.per_page), @page_info.page + 2) do %>
                <button
                  phx-click="page_change"
                  phx-value-page={page_num}
                  phx-target={@myself}
                  class={[
                    "join-item btn btn-sm",
                    if(page_num == @page_info.page, do: "btn-active", else: "")
                  ]}
                >
                  <%= page_num %>
                </button>
              <% end %>

              <%= if @page_info.page < ceil(@page_info.total_count / @page_info.per_page) do %>
                <button
                  phx-click="page_change"
                  phx-value-page={@page_info.page + 1}
                  phx-target={@myself}
                  class="join-item btn btn-sm"
                >
                  »
                </button>
              <% end %>
            </div>
          </div>
        <% end %>
      <% else %>
        <div class="text-center py-12">
          <div class="text-base-content/40 mb-4">
            <.icon name="hero-chart-bar" class="w-16 h-16 mx-auto" />
          </div>
          <p class="text-base-content/60">暂无股票持仓记录</p>
        </div>
      <% end %>
    </div>
    """
  end

  # ============================================================================
  # 计算和格式化函数
  # ============================================================================

  # 计算平均成本
  defp calculate_average_cost(stock) do
    if stock.quantity > 0 and stock.total_cost do
      Decimal.div(stock.total_cost, stock.quantity)
    else
      nil
    end
  end

  # 计算盈亏（如果有当前价格的话）
  defp calculate_profit_loss(stock) do
    # TODO: 实现盈亏计算，需要获取当前股价
    # current_price = get_current_price(stock.racer_id)
    # if current_price and stock.quantity > 0 do
    #   current_value = Decimal.mult(current_price, stock.quantity)
    #   Decimal.sub(current_value, stock.total_cost)
    # else
    #   nil
    # end
    nil
  end

  # 格式化用户显示
  defp format_user_display(stock) do
    if stock.user do
      to_string(stock.user.username)
    else
      "未知用户"
    end
  end

  # 格式化用户ID
  defp format_user_id(user_id) when is_binary(user_id) do
    String.slice(user_id, 0, 8) <> "..."
  end
  defp format_user_id(_), do: "N/A"

  # 格式化数量
  defp format_quantity(quantity) when is_integer(quantity) do
    if quantity >= 1000 do
      "#{Float.round(quantity / 1000, 1)}K"
    else
      to_string(quantity)
    end
  end
  defp format_quantity(quantity), do: to_string(quantity)

  # 格式化相对时间
  defp format_relative_time(datetime) do
    case datetime do
      %DateTime{} ->
        now = DateTime.utc_now()
        diff = DateTime.diff(now, datetime, :second)

        cond do
          diff < 60 -> "刚刚"
          diff < 3600 -> "#{div(diff, 60)}分钟前"
          diff < 86400 -> "#{div(diff, 3600)}小时前"
          diff < 2592000 -> "#{div(diff, 86400)}天前"
          true -> "很久以前"
        end
      _ -> ""
    end
  end



  # 格式化 Decimal 数字显示
  defp format_decimal(nil), do: "0.00"
  defp format_decimal(decimal) when is_struct(decimal, Decimal) do
    decimal
    |> Decimal.round(@decimal_precision)
    |> Decimal.to_string()
    |> format_number_with_commas()
  end
  defp format_decimal(number) when is_number(number) do
    number
    |> Decimal.new()
    |> format_decimal()
  end
  defp format_decimal(_), do: "0.00"

  # 添加千分位分隔符
  defp format_number_with_commas(number_string) do
    case String.split(number_string, ".") do
      [integer_part] ->
        format_integer_with_commas(integer_part) <> ".00"
      [integer_part, decimal_part] ->
        formatted_integer = format_integer_with_commas(integer_part)
        padded_decimal = String.pad_trailing(decimal_part, @decimal_precision, "0")
        "#{formatted_integer}.#{padded_decimal}"
    end
  end

  # 格式化整数部分添加千分位分隔符
  defp format_integer_with_commas(integer_string) do
    integer_string
    |> String.reverse()
    |> String.graphemes()
    |> Enum.chunk_every(3)
    |> Enum.map(&Enum.join/1)
    |> Enum.join(",")
    |> String.reverse()
  end
end
