# 调试指南

## 📋 概述

本文档提供 Racing Game Admin Panel 系统的全面调试指南，包括调试工具使用、常见问题诊断、性能分析和故障排除方法，帮助开发人员快速定位和解决问题。

## 🛠️ 调试工具配置

### Elixir/Phoenix 调试工具
```elixir
# config/dev.exs - 开发环境调试配置
config :racing_game, RacingGameWeb.Endpoint,
  debug_errors: true,
  code_reloader: true,
  check_origin: false,
  watchers: [
    esbuild: {Esbuild, :install_and_run, [:default, ~w(--sourcemap=inline --watch)]},
    tailwind: {Tailwind, :install_and_run, [:default, ~w(--watch)]}
  ]

# 启用详细日志
config :logger, :console,
  format: "[$level] $message\n",
  level: :debug

# 启用Ecto查询日志
config :racing_game, RacingGame.Repo,
  log: :debug,
  stacktrace: true

# 启用Phoenix调试工具
config :phoenix, :plug_init_mode, :runtime
```

### IEx 调试会话
```elixir
# 启动带调试的IEx会话
iex -S mix phx.server

# 在代码中设置断点
require IEx; IEx.pry()

# 调试特定模块
iex> recompile()
iex> RacingGame.CustomerService.UserService.get_user(1)

# 查看进程状态
iex> :observer.start()

# 检查内存使用
iex> :erlang.memory()

# 查看所有进程
iex> Process.list() |> length()

# 检查特定进程
iex> pid = spawn(fn -> :timer.sleep(10000) end)
iex> Process.info(pid)
```

### 远程调试配置
```bash
# 启动远程调试节点
iex --name debug@127.0.0.1 --cookie racing_game_cookie

# 连接到生产节点
iex> Node.connect(:"racing_game@production_server")
iex> Node.list()

# 在远程节点执行代码
iex> :rpc.call(:"racing_game@production_server", RacingGame.Repo, :all, [RacingGame.User])
```

## 🔍 日志分析和调试

### 结构化日志查询
```bash
# 查看实时日志
tail -f /var/log/racing_game/app.log

# 按级别过滤日志
grep "ERROR" /var/log/racing_game/app.log | tail -20

# 查看特定用户的操作日志
grep "user_id.*123" /var/log/racing_game/app.log

# 查看支付相关错误
grep -E "(payment|refund).*ERROR" /var/log/racing_game/app.log

# 分析慢查询日志
grep "SLOW QUERY" /var/log/racing_game/app.log | awk '{print $NF}' | sort | uniq -c | sort -nr
```

### 日志分析脚本
```bash
#!/bin/bash
# log_analyzer.sh - 日志分析脚本

LOG_FILE="/var/log/racing_game/app.log"
DATE_FILTER=${1:-$(date +%Y-%m-%d)}

echo "=== Racing Game 日志分析报告 ($DATE_FILTER) ==="

# 错误统计
echo "## 错误统计"
grep "$DATE_FILTER" $LOG_FILE | grep "ERROR" | awk '{print $5}' | sort | uniq -c | sort -nr | head -10

# 慢查询统计
echo "## 慢查询统计 (>1000ms)"
grep "$DATE_FILTER" $LOG_FILE | grep "SLOW QUERY" | wc -l

# 用户活动统计
echo "## 活跃用户统计"
grep "$DATE_FILTER" $LOG_FILE | grep "user_id" | awk '{print $6}' | sort | uniq | wc -l

# 支付异常统计
echo "## 支付异常统计"
grep "$DATE_FILTER" $LOG_FILE | grep -E "(payment.*ERROR|refund.*ERROR)" | wc -l

# 系统性能指标
echo "## 系统性能指标"
grep "$DATE_FILTER" $LOG_FILE | grep "response_time" | awk '{sum+=$NF; count++} END {print "平均响应时间: " sum/count "ms"}'
```

### Elixir 日志调试
```elixir
# 在代码中添加调试日志
defmodule RacingGame.CustomerService.UserService do
  require Logger

  def get_user(id) do
    Logger.debug("Getting user with id: #{id}")
    
    case RacingGame.CustomerService.UserRepository.get_user(id) do
      {:ok, user} ->
        Logger.info("User found", %{user_id: id, username: user.username})
        {:ok, user}
      
      {:error, reason} ->
        Logger.error("Failed to get user", %{user_id: id, reason: reason})
        {:error, reason}
    end
  end

  # 性能监控日志
  def create_user(attrs) do
    start_time = System.monotonic_time(:millisecond)
    
    result = RacingGame.CustomerService.UserRepository.create_user(attrs)
    
    duration = System.monotonic_time(:millisecond) - start_time
    Logger.info("User creation completed", %{
      duration_ms: duration,
      success: match?({:ok, _}, result)
    })
    
    result
  end
end
```

## 🐛 常见问题调试

### 数据库连接问题
```elixir
# 检查数据库连接
iex> RacingGame.Repo.query("SELECT 1")

# 检查连接池状态
iex> :poolboy.status(RacingGame.Repo.Pool)

# 检查数据库配置
iex> Application.get_env(:racing_game, RacingGame.Repo)

# 手动测试数据库连接
defmodule DatabaseDebug do
  def test_connection do
    case RacingGame.Repo.query("SELECT version()") do
      {:ok, result} -> 
        IO.puts("数据库连接正常: #{inspect(result)}")
      {:error, error} -> 
        IO.puts("数据库连接失败: #{inspect(error)}")
    end
  end
  
  def check_pool_status do
    pool_status = :poolboy.status(RacingGame.Repo.Pool)
    IO.puts("连接池状态: #{inspect(pool_status)}")
  end
end
```

### 内存泄漏调试
```elixir
# 内存使用分析
defmodule MemoryDebug do
  def memory_report do
    memory = :erlang.memory()
    
    IO.puts("=== 内存使用报告 ===")
    IO.puts("总内存: #{memory[:total] / 1024 / 1024 |> Float.round(2)} MB")
    IO.puts("进程内存: #{memory[:processes] / 1024 / 1024 |> Float.round(2)} MB")
    IO.puts("原子内存: #{memory[:atom] / 1024 / 1024 |> Float.round(2)} MB")
    IO.puts("二进制内存: #{memory[:binary] / 1024 / 1024 |> Float.round(2)} MB")
    IO.puts("ETS内存: #{memory[:ets] / 1024 / 1024 |> Float.round(2)} MB")
  end
  
  def top_processes(limit \\ 10) do
    Process.list()
    |> Enum.map(fn pid ->
      info = Process.info(pid, [:memory, :message_queue_len, :registered_name])
      {pid, info}
    end)
    |> Enum.sort_by(fn {_pid, info} -> info[:memory] end, :desc)
    |> Enum.take(limit)
    |> Enum.each(fn {pid, info} ->
      name = info[:registered_name] || "unnamed"
      memory_mb = info[:memory] / 1024 / 1024 |> Float.round(2)
      queue_len = info[:message_queue_len]
      IO.puts("PID: #{inspect(pid)}, Name: #{name}, Memory: #{memory_mb}MB, Queue: #{queue_len}")
    end)
  end
end
```

### Phoenix LiveView 调试
```elixir
# LiveView 调试工具
defmodule RacingGameWeb.AdminLive.UserManagement do
  use RacingGameWeb, :live_view
  require Logger

  def mount(_params, _session, socket) do
    Logger.debug("UserManagement LiveView mounted")
    
    # 启用调试模式
    if connected?(socket) do
      Logger.debug("LiveView connected")
    end
    
    {:ok, assign(socket, users: [], loading: true)}
  end

  def handle_event("load_users", _params, socket) do
    Logger.debug("Loading users event received")
    
    users = RacingGame.CustomerService.UserService.list_users()
    Logger.debug("Loaded #{length(users)} users")
    
    {:noreply, assign(socket, users: users, loading: false)}
  end

  # 调试辅助函数
  defp debug_socket_state(socket) do
    Logger.debug("Socket state", %{
      assigns: Map.keys(socket.assigns),
      connected: connected?(socket),
      transport_pid: socket.transport_pid
    })
  end
end
```

## 📊 性能调试和分析

### 性能分析工具
```elixir
# 使用 :fprof 进行性能分析
defmodule PerformanceDebug do
  def profile_function(module, function, args) do
    :fprof.start()
    :fprof.trace(:start)
    
    result = apply(module, function, args)
    
    :fprof.trace(:stop)
    :fprof.profile()
    :fprof.analyse()
    
    result
  end
  
  # 使用 :eprof 进行性能分析
  def eprof_analysis(fun) do
    :eprof.start()
    :eprof.start_profiling([self()])
    
    result = fun.()
    
    :eprof.stop_profiling()
    :eprof.analyze()
    :eprof.stop()
    
    result
  end
end

# 使用示例
# PerformanceDebug.profile_function(RacingGame.CustomerService.UserService, :list_users, [])
```

### 数据库查询性能调试
```elixir
# 查询性能分析
defmodule QueryDebug do
  import Ecto.Query
  
  def analyze_slow_queries do
    # 启用查询日志
    Ecto.Adapters.SQL.query(RacingGame.Repo, "SET log_statement = 'all'")
    Ecto.Adapters.SQL.query(RacingGame.Repo, "SET log_min_duration_statement = 1000")
    
    # 分析查询计划
    query = from(u in RacingGame.User, where: u.status == "active")
    explain_query(query)
  end
  
  def explain_query(query) do
    {sql, params} = RacingGame.Repo.to_sql(:all, query)
    explain_sql = "EXPLAIN ANALYZE " <> sql
    
    case RacingGame.Repo.query(explain_sql, params) do
      {:ok, result} ->
        IO.puts("查询执行计划:")
        Enum.each(result.rows, fn [plan] -> IO.puts(plan) end)
      {:error, error} ->
        IO.puts("查询分析失败: #{inspect(error)}")
    end
  end
  
  # 检查索引使用情况
  def check_index_usage do
    sql = """
    SELECT 
      schemaname,
      tablename,
      indexname,
      idx_tup_read,
      idx_tup_fetch
    FROM pg_stat_user_indexes 
    ORDER BY idx_tup_read DESC;
    """
    
    case RacingGame.Repo.query(sql) do
      {:ok, result} ->
        IO.puts("索引使用统计:")
        Enum.each(result.rows, fn [schema, table, index, reads, fetches] ->
          IO.puts("#{schema}.#{table}.#{index}: reads=#{reads}, fetches=#{fetches}")
        end)
      {:error, error} ->
        IO.puts("查询索引统计失败: #{inspect(error)}")
    end
  end
end
```

### 并发问题调试
```elixir
# 并发调试工具
defmodule ConcurrencyDebug do
  def deadlock_detection do
    sql = """
    SELECT 
      blocked_locks.pid AS blocked_pid,
      blocked_activity.usename AS blocked_user,
      blocking_locks.pid AS blocking_pid,
      blocking_activity.usename AS blocking_user,
      blocked_activity.query AS blocked_statement,
      blocking_activity.query AS current_statement_in_blocking_process
    FROM pg_catalog.pg_locks blocked_locks
    JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
    JOIN pg_catalog.pg_locks blocking_locks ON blocking_locks.locktype = blocked_locks.locktype
    JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
    WHERE NOT blocked_locks.granted;
    """
    
    case RacingGame.Repo.query(sql) do
      {:ok, %{rows: []}} ->
        IO.puts("未检测到死锁")
      {:ok, result} ->
        IO.puts("检测到死锁:")
        Enum.each(result.rows, fn row ->
          IO.puts("阻塞进程: #{Enum.at(row, 0)}, 被阻塞进程: #{Enum.at(row, 2)}")
        end)
    end
  end
  
  # 进程消息队列监控
  def monitor_message_queues do
    Process.list()
    |> Enum.map(fn pid ->
      case Process.info(pid, [:message_queue_len, :registered_name]) do
        nil -> nil
        info -> {pid, info}
      end
    end)
    |> Enum.reject(&is_nil/1)
    |> Enum.filter(fn {_pid, info} -> info[:message_queue_len] > 100 end)
    |> Enum.each(fn {pid, info} ->
      name = info[:registered_name] || "unnamed"
      queue_len = info[:message_queue_len]
      IO.puts("进程 #{inspect(pid)} (#{name}) 消息队列长度: #{queue_len}")
    end)
  end
end
```

## 🔧 调试工作流程

### 问题诊断流程
```mermaid
graph TD
    A[问题报告] --> B[收集信息]
    B --> C[重现问题]
    C --> D[分析日志]
    D --> E[检查系统状态]
    E --> F[定位问题根因]
    F --> G[制定解决方案]
    G --> H[测试修复]
    H --> I[部署修复]
    I --> J[验证解决]
    J --> K[文档记录]
```

### 调试检查清单
```bash
#!/bin/bash
# debug_checklist.sh - 调试检查清单

echo "=== Racing Game 调试检查清单 ==="

# 1. 系统基础检查
echo "## 1. 系统基础检查"
echo "系统负载: $(uptime)"
echo "内存使用: $(free -h | grep Mem)"
echo "磁盘使用: $(df -h / | tail -1)"

# 2. 应用状态检查
echo "## 2. 应用状态检查"
systemctl is-active racing-game && echo "✅ 应用服务运行正常" || echo "❌ 应用服务异常"

# 3. 数据库连接检查
echo "## 3. 数据库连接检查"
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "SELECT 1;" > /dev/null 2>&1 && \
echo "✅ 数据库连接正常" || echo "❌ 数据库连接异常"

# 4. 缓存服务检查
echo "## 4. 缓存服务检查"
redis-cli ping > /dev/null 2>&1 && echo "✅ Redis服务正常" || echo "❌ Redis服务异常"

# 5. 网络连接检查
echo "## 5. 网络连接检查"
curl -f http://localhost:4000/health > /dev/null 2>&1 && \
echo "✅ HTTP服务正常" || echo "❌ HTTP服务异常"

# 6. 日志错误检查
echo "## 6. 最近错误日志"
tail -20 /var/log/racing_game/app.log | grep ERROR | tail -5

# 7. 性能指标检查
echo "## 7. 性能指标"
echo "活跃连接数: $(ss -tn | grep :4000 | wc -l)"
echo "数据库连接数: $(psql -h $DB_HOST -U $DB_USER -d $DB_NAME -t -c 'SELECT count(*) FROM pg_stat_activity;')"
```

### 生产环境调试注意事项
```elixir
# 生产环境安全调试
defmodule ProductionDebug do
  @doc "安全的生产环境调试函数"
  def safe_debug(module, function, args, timeout \\ 5000) do
    task = Task.async(fn ->
      try do
        apply(module, function, args)
      rescue
        error -> {:error, error}
      catch
        :exit, reason -> {:exit, reason}
        error -> {:catch, error}
      end
    end)
    
    case Task.yield(task, timeout) do
      {:ok, result} -> result
      nil -> 
        Task.shutdown(task, :brutal_kill)
        {:error, :timeout}
    end
  end
  
  # 限制调试输出大小
  def safe_inspect(data, limit \\ 1000) do
    data
    |> inspect(limit: limit, pretty: true)
    |> String.slice(0, limit)
  end
  
  # 生产环境日志采样
  def sample_log(message, sample_rate \\ 0.01) do
    if :rand.uniform() < sample_rate do
      Logger.info(message)
    end
  end
end
```

## 📋 调试最佳实践

### 代码调试最佳实践
```elixir
# 1. 使用结构化日志
Logger.info("User operation completed", %{
  user_id: user.id,
  operation: "update_profile",
  duration_ms: duration,
  success: true
})

# 2. 添加调试断言
def process_payment(payment) do
  assert payment.amount > 0, "Payment amount must be positive"
  assert payment.status == "pending", "Payment must be in pending status"
  
  # 处理逻辑...
end

# 3. 使用模式匹配进行调试
case RacingGame.PaymentService.process_payment(payment) do
  {:ok, result} -> 
    Logger.info("Payment processed successfully", %{payment_id: payment.id})
    result
  {:error, :insufficient_funds} ->
    Logger.warn("Payment failed: insufficient funds", %{payment_id: payment.id})
    {:error, :insufficient_funds}
  {:error, reason} ->
    Logger.error("Payment processing failed", %{payment_id: payment.id, reason: reason})
    {:error, reason}
end

# 4. 使用 with 语句进行错误处理
def create_user_with_payment(user_attrs, payment_attrs) do
  with {:ok, user} <- create_user(user_attrs),
       {:ok, payment} <- create_payment(user, payment_attrs),
       {:ok, _} <- send_welcome_email(user) do
    Logger.info("User created successfully", %{user_id: user.id})
    {:ok, user}
  else
    {:error, :user_creation_failed} = error ->
      Logger.error("Failed to create user", %{attrs: user_attrs})
      error
    {:error, :payment_failed} = error ->
      Logger.error("Failed to create payment", %{user_attrs: user_attrs})
      error
    error ->
      Logger.error("Unexpected error in user creation", %{error: error})
      error
  end
end
```

---

**文档版本**: v1.0  
**创建日期**: 2024-12-19  
**最后更新**: 2024-12-19  
**维护团队**: Racing Game Development Team
