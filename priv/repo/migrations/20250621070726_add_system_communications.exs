defmodule Cypridina.Repo.Migrations.AddSystemCommunications do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:system_communication_reads, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :communication_id, :uuid, null: false
      add :user_id, :uuid, null: false
      add :read_at, :utc_datetime, null: false, default: fragment("(now() AT TIME ZONE 'utc')")
      add :ip_address, :text
      add :user_agent, :text

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:system_communications, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
    end

    alter table(:system_communication_reads) do
      modify :communication_id,
             references(:system_communications,
               column: :id,
               name: "system_communication_reads_communication_id_fkey",
               type: :uuid,
               prefix: "public"
             )

      modify :user_id,
             references(:users,
               column: :id,
               name: "system_communication_reads_user_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    create unique_index(:system_communication_reads, [:user_id, :communication_id],
             name: "system_communication_reads_unique_user_communication_index"
           )

    alter table(:system_communications) do
      add :type, :text, null: false
      add :title, :text, null: false
      add :content, :text, null: false

      add :recipient_id,
          references(:users,
            column: :id,
            name: "system_communications_recipient_id_fkey",
            type: :uuid,
            prefix: "public"
          )

      add :recipient_type, :text, null: false, default: "all"
      add :priority, :text, null: false, default: "medium"
      add :active, :boolean, null: false, default: true
      add :expires_at, :utc_datetime

      add :created_by,
          references(:users,
            column: :id,
            name: "system_communications_created_by_fkey",
            type: :uuid,
            prefix: "public"
          )

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end
  end

  def down do
    drop constraint(:system_communications, "system_communications_recipient_id_fkey")

    drop constraint(:system_communications, "system_communications_created_by_fkey")

    alter table(:system_communications) do
      remove :updated_at
      remove :inserted_at
      remove :created_by
      remove :expires_at
      remove :active
      remove :priority
      remove :recipient_type
      remove :recipient_id
      remove :content
      remove :title
      remove :type
    end

    drop_if_exists unique_index(:system_communication_reads, [:user_id, :communication_id],
                     name: "system_communication_reads_unique_user_communication_index"
                   )

    drop constraint(
           :system_communication_reads,
           "system_communication_reads_communication_id_fkey"
         )

    drop constraint(:system_communication_reads, "system_communication_reads_user_id_fkey")

    alter table(:system_communication_reads) do
      modify :user_id, :uuid
      modify :communication_id, :uuid
    end

    drop table(:system_communications)

    drop table(:system_communication_reads)
  end
end
