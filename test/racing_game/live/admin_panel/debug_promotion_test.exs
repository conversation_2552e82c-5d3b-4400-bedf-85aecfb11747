defmodule RacingGame.Live.AdminPanel.DebugPromotionTest do
  @moduledoc """
  调试PromotionSystem测试
  """
  use ExUnit.Case, async: true

  alias RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionChannelRepository

  describe "PromotionChannelRepository调试测试" do
    test "测试list_channels_by_type函数" do
      try do
        result = PromotionChannelRepository.list_channels_by_type("link", page_size: 1)
        IO.puts("✅ 函数调用成功: #{inspect(result)}")
        assert {:ok, _channels} = result
      rescue
        error ->
          IO.puts("❌ 函数调用失败: #{inspect(error)}")
          IO.puts("错误类型: #{error.__struct__}")
          IO.puts("错误消息: #{Exception.message(error)}")
          flunk("函数调用失败: #{Exception.message(error)}")
      end
    end

    test "测试所有有效的渠道类型" do
      valid_types = ["link", "qr_code", "social", "email", "sms"]
      
      Enum.each(valid_types, fn channel_type ->
        try do
          result = PromotionChannelRepository.list_channels_by_type(channel_type, page_size: 1)
          IO.puts("✅ 渠道类型 #{channel_type}: #{inspect(result)}")
          assert {:ok, _channels} = result
        rescue
          error ->
            IO.puts("❌ 渠道类型 #{channel_type} 失败: #{Exception.message(error)}")
            flunk("渠道类型 #{channel_type} 失败: #{Exception.message(error)}")
        end
      end)
    end
  end
end
