defmodule Teen.CustomerService.VerificationCode do
  @moduledoc """
  验证码查询资源

  管理短信验证码的发送记录和状态
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.CustomerService,
    extensions: [AshAdmin.Resource]

  postgres do
    table "verification_codes"
    repo Cypridina.Repo
  end

  admin do
    table_columns [:id, :phone_number, :code, :status, :inserted_at, :sent_at]
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_by_phone
    define :list_by_status
    define :list_by_date_range
  end

  actions do
    defaults [:create, :read, :update, :destroy]

    read :list_by_phone do
      argument :phone_number, :string, allow_nil?: false
      filter expr(phone_number == ^arg(:phone_number))
    end

    read :list_by_status do
      argument :status, :integer, allow_nil?: false
      filter expr(status == ^arg(:status))
    end

    read :list_by_date_range do
      argument :start_date, :utc_datetime, allow_nil?: false
      argument :end_date, :utc_datetime, allow_nil?: false
      filter expr(inserted_at >= ^arg(:start_date) and inserted_at <= ^arg(:end_date))
    end

    create :send_code do
      argument :phone_number, :string, allow_nil?: false
      argument :code_type, :integer, allow_nil?: false

      change set_attribute(:phone_number, arg(:phone_number))
      change set_attribute(:code_type, arg(:code_type))
      change set_attribute(:status, 0)

      change before_action(fn changeset, _context ->
        # 生成6位随机验证码
        code = :rand.uniform(999999) |> Integer.to_string() |> String.pad_leading(6, "0")
        # 设置过期时间（5分钟后）
        expires_at = DateTime.add(DateTime.utc_now(), 300, :second)

        changeset
        |> Ash.Changeset.change_attribute(:code, code)
        |> Ash.Changeset.change_attribute(:expires_at, expires_at)
      end)
    end

    update :mark_sent do
      change set_attribute(:status, 1)
      change before_action(fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:sent_at, DateTime.utc_now())
      end)
    end

    update :mark_failed do
      argument :failure_reason, :string, allow_nil?: false
      change set_attribute(:status, 2)
      change set_attribute(:failure_reason, arg(:failure_reason))
    end

    update :mark_used do
      change set_attribute(:status, 3)
      change before_action(fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:used_at, DateTime.utc_now())
      end)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :phone_number, :string do
      allow_nil? false
      public? true
      description "手机号码"
      constraints max_length: 20
    end

    attribute :code, :string do
      allow_nil? false
      public? true
      description "验证码"
      constraints max_length: 10
    end

    attribute :code_type, :integer do
      allow_nil? false
      public? true
      description "验证码类型：1-注册，2-登录，3-找回密码，4-绑定手机"
      constraints min: 1, max: 4
    end

    attribute :status, :integer do
      allow_nil? false
      public? true
      description "状态：0-待发送，1-已发送，2-发送失败，3-已使用"
      default 0
      constraints min: 0, max: 3
    end

    attribute :expires_at, :utc_datetime do
      allow_nil? false
      public? true
      description "过期时间"
    end

    attribute :sent_at, :utc_datetime do
      allow_nil? true
      public? true
      description "发送时间"
    end

    attribute :used_at, :utc_datetime do
      allow_nil? true
      public? true
      description "使用时间"
    end

    attribute :failure_reason, :string do
      allow_nil? true
      public? true
      description "发送失败原因"
      constraints max_length: 500
    end

    attribute :ip_address, :string do
      allow_nil? true
      public? true
      description "请求IP地址"
      constraints max_length: 45
    end

    timestamps()
  end

  identities do
    identity :unique_phone_code_time, [:phone_number, :code, :inserted_at]
  end
end
