/**
 * Racing Game Admin Panel - 主题管理器
 * 
 * 负责主题切换、用户偏好保存、动画控制等前端交互功能
 */

class ThemeManager {
  constructor() {
    this.currentTheme = 'racing-professional';
    this.themes = {
      'racing-professional': {
        name: '专业版',
        description: '适合日常办公使用的专业主题',
        icon: '🏢'
      },
      'racing-dark': {
        name: '暗色版',
        description: '护眼的暗色主题，适合长时间使用',
        icon: '🌙'
      },
      'racing-high-contrast': {
        name: '高对比度',
        description: '无障碍访问优化的高对比度主题',
        icon: '🔍'
      }
    };
    
    this.init();
  }

  init() {
    this.loadSavedTheme();
    this.setupThemeToggle();
    this.setupAnimationControls();
    this.setupAccessibilityFeatures();
    this.detectSystemPreferences();
  }

  /**
   * 加载保存的主题设置
   */
  loadSavedTheme() {
    const savedTheme = localStorage.getItem('racing-admin-theme');
    if (savedTheme && this.themes[savedTheme]) {
      this.setTheme(savedTheme);
    } else {
      // 检测系统偏好
      if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
        this.setTheme('racing-dark');
      }
    }
  }

  /**
   * 设置主题
   */
  setTheme(themeName) {
    if (!this.themes[themeName]) {
      console.warn(`Theme ${themeName} not found`);
      return;
    }

    this.currentTheme = themeName;
    document.documentElement.setAttribute('data-theme', themeName);
    localStorage.setItem('racing-admin-theme', themeName);
    
    // 触发主题变更事件
    this.dispatchThemeChangeEvent(themeName);
    
    // 更新主题选择器UI
    this.updateThemeSelector();
  }

  /**
   * 获取当前主题
   */
  getCurrentTheme() {
    return this.currentTheme;
  }

  /**
   * 获取所有可用主题
   */
  getAvailableThemes() {
    return this.themes;
  }

  /**
   * 设置主题切换器
   */
  setupThemeToggle() {
    // 创建主题选择器
    this.createThemeSelector();
    
    // 监听主题切换按钮
    document.addEventListener('click', (e) => {
      if (e.target.matches('[data-theme-toggle]')) {
        const theme = e.target.getAttribute('data-theme-toggle');
        this.setTheme(theme);
      }
    });
  }

  /**
   * 创建主题选择器UI
   */
  createThemeSelector() {
    const selector = document.createElement('div');
    selector.className = 'theme-selector dropdown dropdown-end';
    selector.innerHTML = `
      <label tabindex="0" class="btn btn-ghost btn-circle">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z">
          </path>
        </svg>
      </label>
      <ul tabindex="0" class="dropdown-content menu p-2 shadow bg-base-100 rounded-box w-64">
        ${Object.entries(this.themes).map(([key, theme]) => `
          <li>
            <a data-theme-toggle="${key}" class="flex items-center space-x-3">
              <span class="text-lg">${theme.icon}</span>
              <div>
                <div class="font-medium">${theme.name}</div>
                <div class="text-xs text-gray-500">${theme.description}</div>
              </div>
            </a>
          </li>
        `).join('')}
      </ul>
    `;

    // 插入到导航栏
    const navbar = document.querySelector('.navbar-end, .topbar');
    if (navbar) {
      navbar.appendChild(selector);
    }
  }

  /**
   * 更新主题选择器UI状态
   */
  updateThemeSelector() {
    const toggles = document.querySelectorAll('[data-theme-toggle]');
    toggles.forEach(toggle => {
      const theme = toggle.getAttribute('data-theme-toggle');
      toggle.classList.toggle('active', theme === this.currentTheme);
    });
  }

  /**
   * 设置动画控制
   */
  setupAnimationControls() {
    // 检测用户是否偏好减少动画
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
    
    if (prefersReducedMotion.matches) {
      document.documentElement.classList.add('reduce-motion');
    }

    // 监听偏好变化
    prefersReducedMotion.addEventListener('change', (e) => {
      document.documentElement.classList.toggle('reduce-motion', e.matches);
    });
  }

  /**
   * 设置无障碍访问功能
   */
  setupAccessibilityFeatures() {
    // 高对比度检测
    const prefersHighContrast = window.matchMedia('(prefers-contrast: high)');
    
    if (prefersHighContrast.matches) {
      this.setTheme('racing-high-contrast');
    }

    // 监听对比度偏好变化
    prefersHighContrast.addEventListener('change', (e) => {
      if (e.matches) {
        this.setTheme('racing-high-contrast');
      }
    });

    // 键盘导航增强
    this.setupKeyboardNavigation();
  }

  /**
   * 设置键盘导航
   */
  setupKeyboardNavigation() {
    document.addEventListener('keydown', (e) => {
      // Alt + T: 打开主题选择器
      if (e.altKey && e.key === 't') {
        e.preventDefault();
        const themeSelector = document.querySelector('.theme-selector label');
        if (themeSelector) {
          themeSelector.click();
        }
      }

      // Escape: 关闭所有下拉菜单
      if (e.key === 'Escape') {
        document.querySelectorAll('.dropdown-open').forEach(dropdown => {
          dropdown.classList.remove('dropdown-open');
        });
      }
    });

    // 焦点管理
    document.addEventListener('focusin', (e) => {
      if (e.target.matches('.dropdown label')) {
        e.target.closest('.dropdown').classList.add('dropdown-open');
      }
    });

    document.addEventListener('focusout', (e) => {
      setTimeout(() => {
        const dropdown = e.target.closest('.dropdown');
        if (dropdown && !dropdown.contains(document.activeElement)) {
          dropdown.classList.remove('dropdown-open');
        }
      }, 100);
    });
  }

  /**
   * 检测系统偏好
   */
  detectSystemPreferences() {
    // 监听系统主题变化
    const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    darkModeQuery.addEventListener('change', (e) => {
      // 只有在用户没有手动设置主题时才自动切换
      if (!localStorage.getItem('racing-admin-theme')) {
        this.setTheme(e.matches ? 'racing-dark' : 'racing-professional');
      }
    });
  }

  /**
   * 触发主题变更事件
   */
  dispatchThemeChangeEvent(themeName) {
    const event = new CustomEvent('themeChanged', {
      detail: {
        theme: themeName,
        themeData: this.themes[themeName]
      }
    });
    document.dispatchEvent(event);
  }

  /**
   * 获取主题CSS变量值
   */
  getThemeVariable(variableName) {
    return getComputedStyle(document.documentElement)
      .getPropertyValue(`--color-${variableName}`)
      .trim();
  }

  /**
   * 动态设置主题变量
   */
  setThemeVariable(variableName, value) {
    document.documentElement.style.setProperty(`--color-${variableName}`, value);
  }
}

/**
 * 交互增强管理器
 */
class InteractionEnhancer {
  constructor() {
    this.init();
  }

  init() {
    this.setupHoverEffects();
    this.setupClickEffects();
    this.setupLoadingStates();
    this.setupTooltips();
    this.setupSmoothScrolling();
  }

  /**
   * 设置悬停效果
   */
  setupHoverEffects() {
    // 卡片悬停效果
    document.addEventListener('mouseenter', (e) => {
      if (e.target.matches('.card, .stat')) {
        e.target.style.transform = 'translateY(-2px)';
        e.target.style.boxShadow = 'var(--shadow-lg)';
      }
    }, true);

    document.addEventListener('mouseleave', (e) => {
      if (e.target.matches('.card, .stat')) {
        e.target.style.transform = '';
        e.target.style.boxShadow = '';
      }
    }, true);
  }

  /**
   * 设置点击效果
   */
  setupClickEffects() {
    document.addEventListener('mousedown', (e) => {
      if (e.target.matches('.btn, button')) {
        e.target.style.transform = 'scale(0.98)';
      }
    });

    document.addEventListener('mouseup', (e) => {
      if (e.target.matches('.btn, button')) {
        setTimeout(() => {
          e.target.style.transform = '';
        }, 100);
      }
    });
  }

  /**
   * 设置加载状态
   */
  setupLoadingStates() {
    // 监听Phoenix LiveView事件
    window.addEventListener('phx:page-loading-start', () => {
      this.showGlobalLoading();
    });

    window.addEventListener('phx:page-loading-stop', () => {
      this.hideGlobalLoading();
    });
  }

  /**
   * 显示全局加载状态
   */
  showGlobalLoading() {
    const loader = document.createElement('div');
    loader.id = 'global-loader';
    loader.className = 'fixed inset-0 bg-base-100 bg-opacity-75 flex items-center justify-center z-50';
    loader.innerHTML = `
      <div class="flex flex-col items-center space-y-4">
        <div class="loading loading-spinner loading-lg text-primary"></div>
        <p class="text-sm text-gray-600">加载中...</p>
      </div>
    `;
    document.body.appendChild(loader);
  }

  /**
   * 隐藏全局加载状态
   */
  hideGlobalLoading() {
    const loader = document.getElementById('global-loader');
    if (loader) {
      loader.remove();
    }
  }

  /**
   * 设置工具提示
   */
  setupTooltips() {
    // 简单的工具提示实现
    document.addEventListener('mouseenter', (e) => {
      const tooltip = e.target.getAttribute('data-tooltip');
      if (tooltip) {
        this.showTooltip(e.target, tooltip);
      }
    }, true);

    document.addEventListener('mouseleave', (e) => {
      if (e.target.hasAttribute('data-tooltip')) {
        this.hideTooltip();
      }
    }, true);
  }

  /**
   * 显示工具提示
   */
  showTooltip(element, text) {
    const tooltip = document.createElement('div');
    tooltip.id = 'tooltip';
    tooltip.className = 'absolute z-50 px-2 py-1 text-xs text-white bg-gray-900 rounded shadow-lg';
    tooltip.textContent = text;
    
    document.body.appendChild(tooltip);
    
    const rect = element.getBoundingClientRect();
    tooltip.style.left = `${rect.left + rect.width / 2 - tooltip.offsetWidth / 2}px`;
    tooltip.style.top = `${rect.top - tooltip.offsetHeight - 5}px`;
  }

  /**
   * 隐藏工具提示
   */
  hideTooltip() {
    const tooltip = document.getElementById('tooltip');
    if (tooltip) {
      tooltip.remove();
    }
  }

  /**
   * 设置平滑滚动
   */
  setupSmoothScrolling() {
    document.addEventListener('click', (e) => {
      if (e.target.matches('a[href^="#"]')) {
        e.preventDefault();
        const target = document.querySelector(e.target.getAttribute('href'));
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      }
    });
  }
}

// 初始化管理器
document.addEventListener('DOMContentLoaded', () => {
  window.themeManager = new ThemeManager();
  window.interactionEnhancer = new InteractionEnhancer();
});

// 导出供其他模块使用
export { ThemeManager, InteractionEnhancer };
