defmodule RacingGame.Live.AdminPanel.Operations.SystemCommunicationsOperations do
  @moduledoc """
  系统通信操作模块

  处理系统通信的业务逻辑操作，包括创建、更新、删除等。
  """

  require Logger
  alias RacingGame.SystemCommunication
  alias RacingGame.Live.AdminPanel.Validators.CommunicationValidator
  alias RacingGame.Live.AdminPanel.Mappings.SystemCommunicationsMappings

  @doc """
  创建系统通信（带验证）

  ## 参数
  - `params` - 创建参数
  - `creator_id` - 创建者ID

  ## 返回
  - `{:ok, communication}` - 成功
  - `{:error, error}` - 失败
  """
  def create_communication(params, creator_id) do
    Logger.info("📝 [系统通信操作] 开始创建通信（带验证）")

    case CommunicationValidator.validate_communication_params(params) do
      {:ok, validated_params} ->
        create_params = prepare_create_params(validated_params, creator_id)
        execute_create(create_params)
      {:error, error} ->
        Logger.error("❌ [系统通信操作] 验证失败: #{error}")
        {:error, error}
    end
  end

  @doc """
  直接创建系统通信（跳过验证）

  ## 参数
  - `params` - 已验证的创建参数
  - `creator_id` - 创建者ID

  ## 返回
  - `{:ok, communication}` - 成功
  - `{:error, error}` - 失败
  """
  def create_communication_direct(params, creator_id) do
    Logger.info("📝 [系统通信操作] 开始直接创建通信（跳过验证）")

    create_params = prepare_create_params(params, creator_id)
    execute_create(create_params)
  end

  @doc """
  更新系统通信（带验证）

  ## 参数
  - `communication` - 要更新的通信记录
  - `params` - 更新参数

  ## 返回
  - `{:ok, communication}` - 成功
  - `{:error, error}` - 失败
  """
  def update_communication(communication, params) do
    Logger.info("📝 [系统通信操作] 开始更新通信（带验证）: #{communication.id}")

    case CommunicationValidator.validate_communication_params(params) do
      {:ok, validated_params} ->
        update_params = prepare_update_params(validated_params)
        execute_update(communication, update_params)
      {:error, error} ->
        Logger.error("❌ [系统通信操作] 验证失败: #{error}")
        {:error, error}
    end
  end

  @doc """
  直接更新系统通信（跳过验证）

  ## 参数
  - `communication` - 要更新的通信记录
  - `params` - 已验证的更新参数

  ## 返回
  - `{:ok, communication}` - 成功
  - `{:error, error}` - 失败
  """
  def update_communication_direct(communication, params) do
    Logger.info("📝 [系统通信操作] 开始直接更新通信（跳过验证）: #{communication.id}")

    update_params = prepare_update_params(params)
    execute_update(communication, update_params)
  end

  @doc """
  删除系统通信

  ## 参数
  - `communication_id` - 通信ID

  ## 返回
  - `{:ok, message}` - 成功
  - `{:error, error}` - 失败
  """
  def delete_communication(communication_id) do
    Logger.info("🗑️ [系统通信操作] 开始删除通信: #{communication_id}")

    case SystemCommunication.read(communication_id) do
      {:ok, communication} ->
        execute_delete(communication)
      {:error, %Ash.Error.Query.NotFound{}} ->
        {:error, "记录不存在，可能已被删除"}
      {:error, error} ->
        Logger.error("❌ [系统通信操作] 读取失败: #{inspect(error)}")
        {:error, "无法读取记录"}
    end
  end

  @doc """
  切换通信状态

  ## 参数
  - `communication_id` - 通信ID

  ## 返回
  - `{:ok, communication, action}` - 成功
  - `{:error, error}` - 失败
  """
  def toggle_communication_status(communication_id) do
    Logger.info("🔄 [系统通信操作] 开始切换状态: #{communication_id}")

    case SystemCommunication.read(communication_id) do
      {:ok, communication} ->
        new_status = !communication.active
        action = if new_status, do: "启用", else: "禁用"

        case execute_status_update(communication, new_status) do
          {:ok, updated_communication} ->
            Logger.info("✅ [系统通信操作] 状态切换成功: #{action}")
            {:ok, updated_communication, action}
          {:error, error} ->
            Logger.error("❌ [系统通信操作] 状态切换失败: #{inspect(error)}")
            {:error, "状态切换失败"}
        end
      {:error, %Ash.Error.Query.NotFound{}} ->
        {:error, "记录不存在，可能已被删除"}
      {:error, error} ->
        Logger.error("❌ [系统通信操作] 读取失败: #{inspect(error)}")
        {:error, "无法读取记录"}
    end
  end

  @doc """
  批量删除通信

  ## 参数
  - `communication_ids` - 通信ID列表

  ## 返回
  - `{:ok, {success_count, error_count}}` - 成功
  - `{:error, error}` - 失败
  """
  def batch_delete_communications(communication_ids) when is_list(communication_ids) do
    Logger.info("🗑️ [系统通信操作] 开始批量删除: #{length(communication_ids)}条记录")

    results = Enum.map(communication_ids, &delete_communication/1)

    success_count = Enum.count(results, fn {status, _} -> status == :ok end)
    error_count = Enum.count(results, fn {status, _} -> status == :error end)

    Logger.info("✅ [系统通信操作] 批量删除完成: 成功#{success_count}条，失败#{error_count}条")
    {:ok, {success_count, error_count}}
  end

  @doc """
  批量更新通信状态

  ## 参数
  - `communication_ids` - 通信ID列表
  - `status` - 新状态

  ## 返回
  - `{:ok, {success_count, error_count}}` - 成功
  - `{:error, error}` - 失败
  """
  def batch_update_status(communication_ids, status) when is_list(communication_ids) and is_boolean(status) do
    Logger.info("🔄 [系统通信操作] 开始批量更新状态: #{length(communication_ids)}条记录")

    results = Enum.map(communication_ids, fn id ->
      case SystemCommunication.read(id) do
        {:ok, communication} ->
          execute_status_update(communication, status)
        {:error, _} ->
          {:error, "记录不存在"}
      end
    end)

    success_count = Enum.count(results, fn {status, _} -> status == :ok end)
    error_count = Enum.count(results, fn {status, _} -> status == :error end)

    Logger.info("✅ [系统通信操作] 批量状态更新完成: 成功#{success_count}条，失败#{error_count}条")
    {:ok, {success_count, error_count}}
  end

  @doc """
  获取通信统计信息

  ## 返回
  - 统计信息映射
  """
  def get_communication_statistics do
    Logger.debug("📊 [系统通信操作] 获取统计信息")

    # TODO: 实现统计逻辑
    %{
      total_count: 0,
      active_count: 0,
      inactive_count: 0,
      by_type: %{
        message: 0,
        announcement: 0,
        notification: 0
      },
      by_priority: %{
        low: 0,
        medium: 0,
        high: 0,
        urgent: 0
      }
    }
  end

  # 私有函数 - 准备创建参数
  defp prepare_create_params(validated_params, creator_id) do
    validated_params
    |> Map.put(:creator_id, creator_id)
    # 移除手动时间戳设置，让Ash资源自动管理
  end

  # 私有函数 - 准备更新参数
  defp prepare_update_params(validated_params) do
    validated_params
    # 移除手动时间戳设置，让Ash资源自动管理
    |> Map.drop([:id, :creator_id, :inserted_at])
  end

  # 私有函数 - 执行创建
  defp execute_create(create_params) do
    case SystemCommunication.create(create_params) do
      {:ok, communication} ->
        Logger.info("✅ [系统通信操作] 创建成功: #{communication.title}")
        {:ok, communication}
      {:error, error} ->
        Logger.error("❌ [系统通信操作] 创建失败: #{inspect(error)}")
        {:error, format_error(error)}
    end
  end

  # 私有函数 - 执行更新
  defp execute_update(communication, update_params) do
    case SystemCommunication.update(communication, update_params) do
      {:ok, updated_communication} ->
        Logger.info("✅ [系统通信操作] 更新成功: #{updated_communication.title}")
        {:ok, updated_communication}
      {:error, error} ->
        Logger.error("❌ [系统通信操作] 更新失败: #{inspect(error)}")
        {:error, format_error(error)}
    end
  end

  # 私有函数 - 执行删除
  defp execute_delete(communication) do
    case SystemCommunication.destroy(communication) do
      :ok ->
        type_name = SystemCommunicationsMappings.get_type_display_name(communication.type)
        message = "✅ #{type_name}「#{communication.title}」已成功删除！"
        Logger.info("✅ [系统通信操作] 删除成功: #{communication.title}")
        {:ok, message}
      {:error, error} ->
        Logger.error("❌ [系统通信操作] 删除失败: #{inspect(error)}")
        {:error, format_error(error)}
    end
  end

  # 私有函数 - 执行状态更新
  defp execute_status_update(communication, new_status) do
    SystemCommunication.update(communication, %{active: new_status})
  end

  # 私有函数 - 格式化错误
  defp format_error(error) when is_binary(error), do: error
  defp format_error(%{message: message}) when is_binary(message), do: message
  defp format_error(%Ash.Error.Invalid{errors: errors}) do
    errors
    |> Enum.map(&format_single_error/1)
    |> Enum.filter(& &1)
    |> case do
      [] -> "操作失败，请检查输入数据"
      error_messages -> Enum.join(error_messages, "；")
    end
  end
  defp format_error(%Ash.Error.Query.NotFound{}), do: "记录不存在"
  defp format_error(%{__struct__: struct_name} = error) when is_atom(struct_name) do
    case Atom.to_string(struct_name) do
      "Elixir.Ash.Error" <> _ -> "操作失败: #{inspect(error)}"
      _ -> "操作失败，请重试"
    end
  end
  defp format_error(_), do: "操作失败，请重试"

  # 私有函数 - 格式化单个错误
  defp format_single_error(%{field: field, message: message}) when is_binary(message) do
    field_name = get_field_display_name(field)
    "#{field_name}: #{message}"
  end
  defp format_single_error(%{message: message}) when is_binary(message), do: message
  defp format_single_error(_), do: nil

  # 私有函数 - 获取字段显示名称
  defp get_field_display_name(:title), do: "标题"
  defp get_field_display_name(:content), do: "内容"
  defp get_field_display_name(:priority), do: "优先级"
  defp get_field_display_name(:recipient_type), do: "接收者类型"
  defp get_field_display_name(:recipient_id), do: "接收者ID"
  defp get_field_display_name(:active), do: "状态"
  defp get_field_display_name(field), do: to_string(field)
end
