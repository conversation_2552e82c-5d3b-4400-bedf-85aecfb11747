defmodule Cypridina.RoomSystem.RoomLogFilter do
  @moduledoc """
  房间日志过滤器 - 为每个房间创建独立的日志文件

  功能：
  - 动态创建房间日志handler
  - 根据metadata中的room_id过滤日志
  - 房间结束时自动清理handler
  """

  require Logger

  @doc """
  为房间创建日志过滤器
  """
  def create_room_logger(%{room_id: room_id, game_id: game_id}) do
    handler_id = room_handler_id(room_id)
    log_file = room_log_file(game_id, room_id)

    # 确保日志目录存在
    log_dir = Path.dirname(log_file)
    File.mkdir_p!(log_dir)

    # 配置房间专用的日志handler
    handler_config = %{
      config: %{
        file: String.to_charlist(log_file),
        filesync_repeat_interval: 5000,
        file_check: 5000,
        # 5MB per room log
        max_no_bytes: 5_000_000,
        max_no_files: 3,
        compress_on_rotate: true
      },
      formatter: {
        :logger_formatter,
        %{
          template: [:time, " [", :level, "] [ROOM:", room_id, "] ", :msg, "\n"],
          time_designator: ?\s
        }
      },
      filter_default: :stop,
      filters: [
        {room_filter_id(room_id), {&room_log_filter/2, {game_id, room_id}}}
      ]
    }

    case :logger.add_handler(handler_id, :logger_std_h, handler_config) do
      :ok ->
        Logger.info("🏠 [ROOM_LOG] 创建房间日志过滤器: #{room_id} -> #{log_file}")
        {:ok, handler_id}

      {:error, reason} ->
        Logger.error("🏠 [ROOM_LOG] 创建房间日志过滤器失败: #{room_id}, 原因: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  删除房间日志过滤器
  """
  def remove_room_logger(room_id) do
    handler_id = room_handler_id(room_id)

    case :logger.remove_handler(handler_id) do
      :ok ->
        Logger.info("🏠 [ROOM_LOG] 删除房间日志过滤器: #{room_id}")
        :ok

      {:error, reason} ->
        Logger.warning("🏠 [ROOM_LOG] 删除房间日志过滤器失败: #{room_id}, 原因: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  """
  def room_log_filter(log_event, {game_id, room_id}) do
    case log_event do
      %{meta: %{game_id: ^game_id, room_id: ^room_id}} ->
        log_event
      _ ->
        # 其他情况，不记录
        :stop
    end
  end

  @doc """
  全局日志过滤函数 - 排除游戏日志
  """
  def global_log_filter(log_event, _config) do
    case log_event do
      %{meta: %{game_id: _game_id}} ->
        :stop
      _ ->
        # 其他日志，正常记录
        :ignore
    end
  end

  # 私有函数

  defp room_handler_id(room_id) do
    :"room_logger_#{room_id}"
  end

  defp room_filter_id(room_id) do
    :"room_filter_#{room_id}"
  end

  defp room_log_file(game_id, room_id) do
    "logs/games/game_#{game_id}_#{room_id}.log"
  end
end
