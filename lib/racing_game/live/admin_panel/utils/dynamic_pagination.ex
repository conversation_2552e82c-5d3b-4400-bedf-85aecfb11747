defmodule RacingGame.Utils.DynamicPagination do
  @moduledoc """
  动态分页组件

  提供灵活的分页功能，支持：
  - 自定义每页显示数量
  - 页码跳转
  - 上一页/下一页导航
  - 总数和当前页信息显示
  - 响应式设计
  """

  use Phoenix.Component
  import Phoenix.HTML.Form

  @doc """
  渲染分页组件

  ## 属性
  - `current_page`: 当前页码 (必需)
  - `total_count`: 总记录数 (必需)
  - `per_page`: 每页显示数量 (必需)
  - `target`: 事件目标组件
  - `page_change_event`: 页码变化事件名，默认为 "page_change"
  - `per_page_change_event`: 每页数量变化事件名，默认为 "per_page_change"
  - `show_per_page_selector`: 是否显示每页数量选择器，默认为 true
  - `show_page_info`: 是否显示页面信息，默认为 true
  - `show_quick_jump`: 是否显示快速跳转，默认为 true
  - `per_page_options`: 每页数量选项，默认为 [10, 20, 50, 100]
  - `max_visible_pages`: 最大显示页码数，默认为 7
  """
  attr :current_page, :integer, required: true
  attr :total_count, :integer, required: true
  attr :per_page, :integer, required: true
  attr :target, :string, default: nil
  attr :page_change_event, :string, default: "page_change"
  attr :per_page_change_event, :string, default: "per_page_change"
  attr :show_per_page_selector, :boolean, default: true
  attr :show_page_info, :boolean, default: true
  attr :show_quick_jump, :boolean, default: true
  attr :per_page_options, :list, default: [10, 20, 50, 100]
  attr :max_visible_pages, :integer, default: 7

  def pagination(assigns) do
    assigns = assign_pagination_data(assigns)

    ~H"""
    <div class="flex flex-col sm:flex-row items-center justify-between gap-4 p-4 bg-white border-t border-gray-200">
      <!-- 左侧：每页数量选择器和页面信息 -->
      <div class="flex flex-col sm:flex-row items-center gap-4">
        <%= if @show_per_page_selector do %>
          <div class="flex items-center gap-2">
            <label for="per-page-select" class="text-sm text-gray-700 whitespace-nowrap">
              每页显示:
            </label>
            <form phx-change={@per_page_change_event} phx-target={@target} class="inline">
              <select
                id="per-page-select"
                name="per_page"
                class="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <%= for option <- @per_page_options do %>
                  <option value={option} {if option == @per_page, do: [selected: true], else: []}>
                    <%= option %>
                  </option>
                <% end %>
              </select>
            </form>
            <span class="text-sm text-gray-700">条</span>
          </div>
        <% end %>

        <%= if @show_page_info do %>
          <div class="text-sm text-gray-700">
            显示第 <span class="font-medium"><%= @start_index %></span> 到
            <span class="font-medium"><%= @end_index %></span> 条，
            共 <span class="font-medium"><%= @total_count %></span> 条记录
          </div>
        <% end %>
      </div>

      <!-- 右侧：分页导航 -->
      <div class="flex items-center gap-2">
        <%= if @show_quick_jump and @total_pages > 1 do %>
          <div class="flex items-center gap-2 mr-4">
            <label for="page-jump" class="text-sm text-gray-700 whitespace-nowrap">
              跳转到:
            </label>
            <form phx-submit="page_jump" phx-target={@target} class="inline">
              <input
                id="page-jump"
                name="page"
                type="number"
                min="1"
                max={@total_pages}
                placeholder={@current_page}
                class="w-16 border border-gray-300 rounded-md px-2 py-1 text-sm text-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </form>
            <span class="text-sm text-gray-700">页</span>
          </div>
        <% end %>

        <%= if @total_pages > 1 do %>
          <nav class="flex items-center gap-1">
            <!-- 上一页按钮 -->
            <button
              type="button"
              phx-click={@page_change_event}
              phx-value-page={@current_page - 1}
              phx-target={@target}
              disabled={@current_page == 1}
              class={[
                "px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200",
                if(@current_page == 1,
                  do: "text-gray-400 cursor-not-allowed",
                  else: "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                )
              ]}
            >
              <i class="fas fa-chevron-left mr-1"></i>
              上一页
            </button>

            <!-- 页码按钮 -->
            <%= for page <- @visible_pages do %>
              <%= if page == :ellipsis do %>
                <span class="px-3 py-2 text-sm text-gray-500">...</span>
              <% else %>
                <button
                  type="button"
                  phx-click={@page_change_event}
                  phx-value-page={page}
                  phx-target={@target}
                  class={[
                    "px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200",
                    if(page == @current_page,
                      do: "bg-blue-600 text-white",
                      else: "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                    )
                  ]}
                >
                  <%= page %>
                </button>
              <% end %>
            <% end %>

            <!-- 下一页按钮 -->
            <button
              type="button"
              phx-click={@page_change_event}
              phx-value-page={@current_page + 1}
              phx-target={@target}
              disabled={@current_page == @total_pages}
              class={[
                "px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200",
                if(@current_page == @total_pages,
                  do: "text-gray-400 cursor-not-allowed",
                  else: "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                )
              ]}
            >
              下一页
              <i class="fas fa-chevron-right ml-1"></i>
            </button>
          </nav>
        <% end %>
      </div>
    </div>
    """
  end

  @doc """
  简化版分页组件，只显示基本的上一页/下一页导航
  """
  attr :current_page, :integer, required: true
  attr :total_count, :integer, required: true
  attr :per_page, :integer, required: true
  attr :target, :string, default: nil
  attr :page_change_event, :string, default: "page_change"

  def simple_pagination(assigns) do
    assigns = assign_pagination_data(assigns)

    ~H"""
    <div class="flex items-center justify-between p-4 bg-white border-t border-gray-200">
      <div class="text-sm text-gray-700">
        第 <%= @current_page %> 页，共 <%= @total_pages %> 页
        （总计 <%= @total_count %> 条记录）
      </div>

      <%= if @total_pages > 1 do %>
        <div class="flex items-center gap-2">
          <button
            type="button"
            phx-click={@page_change_event}
            phx-value-page={@current_page - 1}
            phx-target={@target}
            disabled={@current_page == 1}
            class={[
              "px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200",
              if(@current_page == 1,
                do: "text-gray-400 cursor-not-allowed bg-gray-100",
                else: "text-gray-700 bg-white border border-gray-300 hover:bg-gray-50"
              )
            ]}
          >
            上一页
          </button>

          <button
            type="button"
            phx-click={@page_change_event}
            phx-value-page={@current_page + 1}
            phx-target={@target}
            disabled={@current_page == @total_pages}
            class={[
              "px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200",
              if(@current_page == @total_pages,
                do: "text-gray-400 cursor-not-allowed bg-gray-100",
                else: "text-gray-700 bg-white border border-gray-300 hover:bg-gray-50"
              )
            ]}
          >
            下一页
          </button>
        </div>
      <% end %>
    </div>
    """
  end

  @doc """
  计算分页数据的辅助函数
  """
  def calculate_pagination_data(current_page, total_count, per_page, max_visible_pages \\ 7) do
    # 确保参数有效
    total_count = if is_nil(total_count) or total_count < 0, do: 0, else: total_count
    per_page = if is_nil(per_page) or per_page <= 0, do: 10, else: per_page
    current_page = if is_nil(current_page) or current_page <= 0, do: 1, else: current_page

    # 计算总页数，至少为1页
    total_pages = if total_count == 0, do: 1, else: max(1, ceil(total_count / per_page))
    current_page = max(1, min(current_page, total_pages))

    # 计算索引范围
    start_index = if total_count == 0, do: 0, else: (current_page - 1) * per_page + 1
    end_index = if total_count == 0, do: 0, else: min(current_page * per_page, total_count)

    visible_pages = calculate_visible_pages(current_page, total_pages, max_visible_pages)

    %{
      current_page: current_page,
      total_pages: total_pages,
      start_index: start_index,
      end_index: end_index,
      visible_pages: visible_pages,
      has_prev: current_page > 1,
      has_next: current_page < total_pages
    }
  end

  # 私有函数：为assigns添加分页数据
  defp assign_pagination_data(assigns) do
    pagination_data = calculate_pagination_data(
      assigns.current_page,
      assigns.total_count,
      assigns.per_page,
      Map.get(assigns, :max_visible_pages, 7)
    )

    Map.merge(assigns, pagination_data)
  end

  # 私有函数：计算可见页码
  defp calculate_visible_pages(current_page, total_pages, max_visible_pages) when total_pages <= max_visible_pages do
    Enum.to_list(1..total_pages)
  end

  defp calculate_visible_pages(current_page, total_pages, max_visible_pages) do
    half_visible = div(max_visible_pages - 3, 2)  # 减去首页、末页和省略号

    cond do
      # 当前页靠近开始
      current_page <= half_visible + 2 ->
        Enum.to_list(1..(max_visible_pages - 2)) ++ [:ellipsis, total_pages]

      # 当前页靠近结束
      current_page >= total_pages - half_visible - 1 ->
        [1, :ellipsis] ++ Enum.to_list((total_pages - max_visible_pages + 3)..total_pages)

      # 当前页在中间
      true ->
        start_page = current_page - half_visible
        end_page = current_page + half_visible
        [1, :ellipsis] ++ Enum.to_list(start_page..end_page) ++ [:ellipsis, total_pages]
    end
  end
end
