defmodule RacingGame.Live.AdminPanel.PerformanceLoadTest do
  @moduledoc """
  🧪 性能和负载测试

  测试系统在高负载下的表现、响应时间、吞吐量、资源使用等指标
  """

  use ExUnit.Case, async: false  # 性能测试不能并行执行

  alias RacingGame.Live.AdminPanel.TestHelper

  # All Service modules
  alias RacingGame.Live.AdminPanel.Services.Teen.{
    CustomerServiceService,
    GameManagementService,
    SystemSettingsService,
    PaymentSystemService,
    ActivitySystemService,
    DataStatisticsService,
    PromotionSystemService
  }

  @high_concurrency 50
  @medium_concurrency 20
  @low_concurrency 10
  @performance_timeout 30_000

  setup do
    # 简化的测试环境设置
    setup_test_environment()

    # 预热系统
    warmup_system()

    :ok
  end

  defp setup_test_environment do
    # 简化的测试环境设置
    Application.put_env(:racing_game, :test_mode, true)

    # 清理测试数据（如果需要）
    cleanup_test_data()
  end

  defp cleanup_test_data do
    # 简单的清理操作
    :ok
  end

  defp warmup_system do
    # 执行一些基础操作来预热系统
    Enum.each(1..5, fn _ ->
      # 简单的查询操作来预热系统
      try do
        CustomerServiceRepository.list_customers(%{}, limit: 1)
        PaymentSystemRepository.list_payment_orders(%{}, limit: 1)
      rescue
        _ -> :ok
      end
    end)
  end

  describe "Repository层性能测试" do
    test "高并发CRUD操作性能" do
      # 测试Repository层在高并发下的CRUD性能
      {results, total_time} = TestHelper.measure_time(fn ->
        TestHelper.concurrent_test(fn ->
          # 随机执行不同的CRUD操作
          case :rand.uniform(4) do
            1 ->
              # Create
              ticket_data = TestHelper.generate_test_ticket()
              CustomerServiceService.create_ticket(ticket_data)
            2 ->
              # Read
              CustomerServiceService.get_service_statistics({Date.utc_today(), Date.utc_today()})
            3 ->
              # Update (模拟)
              order_data = TestHelper.generate_test_payment_order()
              {:ok, order} = PaymentSystemService.create_payment_order(order_data)
              PaymentSystemService.update_order_status(order.id, 1)
            4 ->
              # Delete (模拟软删除)
              platform_data = TestHelper.generate_test_platform()
              {:ok, platform} = GameManagementService.create_platform(platform_data)
              GameManagementService.toggle_platform_status(platform.id)
          end
        end, @high_concurrency)
      end)

      success_count = results |> Enum.count(fn {:ok, _} -> true; _ -> false end)
      success_rate = success_count / @high_concurrency
      avg_response_time = total_time / @high_concurrency

      # 性能指标验证
      assert success_rate >= 0.9  # 成功率应该 >= 90%
      assert avg_response_time < 1000  # 平均响应时间 < 1秒
      assert total_time < 15_000  # 总时间 < 15秒

      IO.puts("🚀 Repository层高并发测试结果:")
      IO.puts("   并发数: #{@high_concurrency}")
      IO.puts("   成功率: #{Float.round(success_rate * 100, 2)}%")
      IO.puts("   平均响应时间: #{Float.round(avg_response_time, 2)}ms")
      IO.puts("   总执行时间: #{total_time}ms")
    end

    test "大数据量查询性能" do
      # 先创建大量测试数据
      IO.puts("📊 创建大量测试数据...")

      {_results, setup_time} = TestHelper.measure_time(fn ->
        # 创建1000个工单
        Enum.map(1..1000, fn _ ->
          ticket_data = TestHelper.generate_test_ticket()
          CustomerServiceService.create_ticket(ticket_data)
        end)
      end)

      IO.puts("   数据创建时间: #{setup_time}ms")

      # 测试大数据量查询性能
      {result, query_time} = TestHelper.measure_time(fn ->
        CustomerServiceService.get_service_statistics({Date.utc_today() |> Date.add(-30), Date.utc_today()})
      end)

      assert {:ok, _stats} = result
      assert query_time < 5000  # 大数据量查询应该在5秒内完成

      IO.puts("🔍 大数据量查询测试结果:")
      IO.puts("   查询时间: #{query_time}ms")
      IO.puts("   数据量: 1000+ 记录")
    end
  end

  describe "Service层性能测试" do
    test "复杂业务流程性能" do
      # 测试复杂业务流程的性能
      user_id = :rand.uniform(10000)

      {result, execution_time} = TestHelper.measure_time(fn ->
        # 模拟完整的用户业务流程
        with {:ok, _config} <- SystemSettingsService.initialize_user_settings(user_id),
             {:ok, order} <- PaymentSystemService.create_payment_order(
               TestHelper.generate_test_payment_order(%{user_id: user_id})
             ),
             {:ok, _paid_order} <- PaymentSystemService.process_payment(order.id, %{
               transaction_id: "PERF_TEST_#{System.system_time(:millisecond)}",
               paid_amount: order.amount,
               payment_time: DateTime.utc_now()
             }),
             {:ok, vip_status} <- GameManagementService.calculate_user_vip_status(user_id, order.amount),
             {:ok, activity} <- ActivitySystemService.create_activity(
               TestHelper.generate_test_activity(), :rand.uniform(100)
             ),
             {:ok, _participation} <- ActivitySystemService.participate_in_activity(user_id, activity.id),
             {:ok, promoter} <- PromotionSystemService.apply_for_promoter(user_id, %{}),
             {:ok, _approved} <- PromotionSystemService.review_promoter_application(
               promoter.id, :approve, :rand.uniform(100), "性能测试审核"
             ) do
          {:ok, %{vip_level: vip_status.current_level.level, promoter_id: promoter.id}}
        end
      end)

      assert {:ok, _business_result} = result
      assert execution_time < 3000  # 复杂业务流程应该在3秒内完成

      IO.puts("⚡ 复杂业务流程性能测试结果:")
      IO.puts("   执行时间: #{execution_time}ms")
      IO.puts("   流程步骤: 8个主要步骤")
    end

    test "并发业务处理性能" do
      # 测试多个用户同时执行业务操作的性能
      {results, total_time} = TestHelper.measure_time(fn ->
        TestHelper.concurrent_test(fn ->
          user_id = :rand.uniform(100000)  # 使用更大的用户ID范围避免冲突

          # 随机执行不同的业务操作
          case :rand.uniform(5) do
            1 ->
              # 客服业务
              ticket_data = TestHelper.generate_test_ticket(%{user_id: user_id})
              CustomerServiceService.create_ticket(ticket_data)
            2 ->
              # 支付业务
              order_data = TestHelper.generate_test_payment_order(%{user_id: user_id})
              PaymentSystemService.create_payment_order(order_data)
            3 ->
              # 游戏管理业务
              GameManagementService.calculate_user_vip_status(user_id, Decimal.new("1000"))
            4 ->
              # 活动业务
              activity_data = TestHelper.generate_test_activity()
              ActivitySystemService.create_activity(activity_data, :rand.uniform(100))
            5 ->
              # 推广业务
              PromotionSystemService.apply_for_promoter(user_id, %{})
          end
        end, @medium_concurrency)
      end)

      success_count = results |> Enum.count(fn {:ok, _} -> true; _ -> false end)
      success_rate = success_count / @medium_concurrency
      throughput = @medium_concurrency / (total_time / 1000)  # 每秒处理数

      assert success_rate >= 0.85  # 并发业务成功率 >= 85%
      assert throughput >= 2  # 吞吐量 >= 2 ops/sec

      IO.puts("🔄 并发业务处理性能测试结果:")
      IO.puts("   并发数: #{@medium_concurrency}")
      IO.puts("   成功率: #{Float.round(success_rate * 100, 2)}%")
      IO.puts("   吞吐量: #{Float.round(throughput, 2)} ops/sec")
      IO.puts("   总执行时间: #{total_time}ms")
    end
  end

  describe "QueryBuilder层性能测试" do
    test "复杂统计查询性能" do
      # 先创建一些测试数据
      setup_statistical_data()

      # 测试各种复杂统计查询的性能
      queries = [
        {"客服统计", fn -> CustomerServiceService.get_service_statistics({Date.utc_today() |> Date.add(-30), Date.utc_today()}) end},
        {"支付统计", fn -> PaymentSystemService.get_payment_statistics({Date.utc_today() |> Date.add(-30), Date.utc_today()}) end},
        {"活动统计", fn -> ActivitySystemService.get_activity_statistics({Date.utc_today() |> Date.add(-30), Date.utc_today()}) end},
        {"推广统计", fn -> PromotionSystemService.get_promotion_statistics({Date.utc_today() |> Date.add(-30), Date.utc_today()}) end},
        {"综合统计", fn -> DataStatisticsService.generate_comprehensive_statistics({Date.utc_today() |> Date.add(-7), Date.utc_today()}) end}
      ]

      query_results = Enum.map(queries, fn {name, query_fn} ->
        {result, time} = TestHelper.measure_time(query_fn)

        assert {:ok, _stats} = result
        assert time < 8000  # 每个复杂查询应该在8秒内完成

        {name, time}
      end)

      total_query_time = query_results |> Enum.map(fn {_, time} -> time end) |> Enum.sum()
      avg_query_time = total_query_time / length(query_results)

      IO.puts("📈 复杂统计查询性能测试结果:")
      Enum.each(query_results, fn {name, time} ->
        IO.puts("   #{name}: #{time}ms")
      end)
      IO.puts("   平均查询时间: #{Float.round(avg_query_time, 2)}ms")
    end

    test "并行统计查询性能" do
      # 测试多个统计查询并行执行的性能
      {results, total_time} = TestHelper.measure_time(fn ->
        tasks = [
          Task.async(fn -> CustomerServiceService.get_service_statistics({Date.utc_today() |> Date.add(-7), Date.utc_today()}) end),
          Task.async(fn -> PaymentSystemService.get_payment_statistics({Date.utc_today() |> Date.add(-7), Date.utc_today()}) end),
          Task.async(fn -> ActivitySystemService.get_activity_statistics({Date.utc_today() |> Date.add(-7), Date.utc_today()}) end),
          Task.async(fn -> PromotionSystemService.get_promotion_statistics({Date.utc_today() |> Date.add(-7), Date.utc_today()}) end),
          Task.async(fn -> GameManagementService.get_platform_statistics({Date.utc_today() |> Date.add(-7), Date.utc_today()}) end)
        ]

        Task.await_many(tasks, @performance_timeout)
      end)

      success_count = results |> Enum.count(fn {:ok, _} -> true; _ -> false end)
      success_rate = success_count / length(results)

      assert success_rate >= 0.9  # 并行查询成功率 >= 90%
      assert total_time < 15_000  # 并行查询总时间 < 15秒

      IO.puts("⚡ 并行统计查询性能测试结果:")
      IO.puts("   并行查询数: #{length(results)}")
      IO.puts("   成功率: #{Float.round(success_rate * 100, 2)}%")
      IO.puts("   总执行时间: #{total_time}ms")
    end
  end

  describe "内存和资源使用测试" do
    test "内存使用优化验证" do
      # 记录初始内存使用
      initial_memory = :erlang.memory(:total)
      initial_processes = :erlang.system_info(:process_count)

      # 执行大量操作
      {_results, _time} = TestHelper.measure_time(fn ->
        Enum.each(1..100, fn _ ->
          # 创建各种业务对象
          CustomerServiceService.create_ticket(TestHelper.generate_test_ticket())
          PaymentSystemService.create_payment_order(TestHelper.generate_test_payment_order())
          ActivitySystemService.create_activity(TestHelper.generate_test_activity(), :rand.uniform(100))
          PromotionSystemService.apply_for_promoter(:rand.uniform(100000), %{})
        end)
      end)

      # 强制垃圾回收
      :erlang.garbage_collect()
      Process.sleep(1000)  # 等待垃圾回收完成

      # 记录最终内存使用
      final_memory = :erlang.memory(:total)
      final_processes = :erlang.system_info(:process_count)

      memory_increase = final_memory - initial_memory
      process_increase = final_processes - initial_processes

      # 内存增长应该在合理范围内
      assert memory_increase < 100 * 1024 * 1024  # < 100MB
      assert process_increase < 1000  # 进程数增长 < 1000

      IO.puts("💾 内存和资源使用测试结果:")
      IO.puts("   内存增长: #{Float.round(memory_increase / 1024 / 1024, 2)}MB")
      IO.puts("   进程增长: #{process_increase}")
      IO.puts("   初始内存: #{Float.round(initial_memory / 1024 / 1024, 2)}MB")
      IO.puts("   最终内存: #{Float.round(final_memory / 1024 / 1024, 2)}MB")
    end

    test "连接池和资源池性能" do
      # 测试数据库连接池在高并发下的表现
      {results, total_time} = TestHelper.measure_time(fn ->
        TestHelper.concurrent_test(fn ->
          # 执行需要数据库连接的操作
          user_id = :rand.uniform(100000)

          with {:ok, ticket} <- CustomerServiceService.create_ticket(
                 TestHelper.generate_test_ticket(%{user_id: user_id})
               ),
               {:ok, order} <- PaymentSystemService.create_payment_order(
                 TestHelper.generate_test_payment_order(%{user_id: user_id})
               ),
               {:ok, _stats} <- CustomerServiceService.get_service_statistics(
                 {Date.utc_today(), Date.utc_today()}
               ) do
            {:ok, %{ticket_id: ticket.id, order_id: order.id}}
          end
        end, @high_concurrency)
      end)

      success_count = results |> Enum.count(fn {:ok, _} -> true; _ -> false end)
      success_rate = success_count / @high_concurrency

      assert success_rate >= 0.8  # 连接池压力下成功率 >= 80%
      assert total_time < 20_000  # 高并发连接池测试 < 20秒

      IO.puts("🔗 连接池和资源池性能测试结果:")
      IO.puts("   并发连接数: #{@high_concurrency}")
      IO.puts("   成功率: #{Float.round(success_rate * 100, 2)}%")
      IO.puts("   总执行时间: #{total_time}ms")
    end
  end

  describe "系统极限测试" do
    test "系统最大负载测试" do
      # 测试系统能承受的最大负载
      max_concurrency = 100

      IO.puts("🚨 开始系统最大负载测试 (并发数: #{max_concurrency})...")

      {results, total_time} = TestHelper.measure_time(fn ->
        TestHelper.concurrent_test(fn ->
          user_id = :rand.uniform(1000000)  # 使用更大范围避免冲突

          # 执行轻量级操作避免系统过载
          case :rand.uniform(3) do
            1 ->
              CustomerServiceService.create_ticket(TestHelper.generate_test_ticket(%{user_id: user_id}))
            2 ->
              PaymentSystemService.create_payment_order(TestHelper.generate_test_payment_order(%{user_id: user_id}))
            3 ->
              GameManagementService.calculate_user_vip_status(user_id, Decimal.new("500"))
          end
        end, max_concurrency)
      end)

      success_count = results |> Enum.count(fn {:ok, _} -> true; _ -> false end)
      failure_count = max_concurrency - success_count
      success_rate = success_count / max_concurrency

      # 在极限负载下，允许一定的失败率
      assert success_rate >= 0.6  # 极限负载下成功率 >= 60%

      IO.puts("⚠️  系统最大负载测试结果:")
      IO.puts("   最大并发数: #{max_concurrency}")
      IO.puts("   成功数: #{success_count}")
      IO.puts("   失败数: #{failure_count}")
      IO.puts("   成功率: #{Float.round(success_rate * 100, 2)}%")
      IO.puts("   总执行时间: #{total_time}ms")

      if success_rate < 0.8 do
        IO.puts("⚠️  警告: 系统在高负载下性能下降，建议优化")
      end
    end
  end

  # 辅助函数：创建统计测试数据
  defp setup_statistical_data do
    # 创建一些基础数据用于统计查询
    Enum.each(1..50, fn _ ->
      user_id = :rand.uniform(10000)

      # 创建工单
      CustomerServiceService.create_ticket(TestHelper.generate_test_ticket(%{user_id: user_id}))

      # 创建订单
      PaymentSystemService.create_payment_order(TestHelper.generate_test_payment_order(%{user_id: user_id}))

      # 创建活动
      ActivitySystemService.create_activity(TestHelper.generate_test_activity(), :rand.uniform(100))

      # 创建推广员
      if :rand.uniform(10) == 1 do  # 10%概率创建推广员
        PromotionSystemService.apply_for_promoter(user_id, %{})
      end
    end)
  end
end
