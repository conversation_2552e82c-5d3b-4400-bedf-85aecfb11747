defmodule RacingGame.Utils.DynamicDialog do
  @moduledoc """
  动态对话提示框组件

  提供灵活的对话框功能，支持多种类型的提示：
  - 确认对话框 (confirm)
  - 警告对话框 (warning)
  - 信息对话框 (info)
  - 错误对话框 (error)
  - 成功对话框 (success)
  - 自定义对话框 (custom)

  ## 使用示例

      # 在 LiveView 中使用
      <.dynamic_dialog
        id="confirm-delete"
        type="confirm"
        title="确认删除"
        message="您确定要删除这条记录吗？此操作不可撤销。"
        confirm_text="删除"
        cancel_text="取消"
        confirm_action="delete_record"
        cancel_action="close_dialog"
        target={@myself}
        show={@show_delete_dialog}
        danger={true}
      />

      # 简单信息提示
      <.dynamic_dialog
        id="info-dialog"
        type="info"
        title="操作成功"
        message="数据已成功保存！"
        confirm_text="确定"
        confirm_action="close_dialog"
        target={@myself}
        show={@show_info_dialog}
      />
  """

  use Phoenix.Component
  alias CypridinaWeb.Components.AdminButtonGroup

  @doc """
  动态对话提示框组件

  ## 属性

  - `id` - 对话框唯一标识符
  - `type` - 对话框类型: "confirm", "warning", "info", "error", "success", "custom"
  - `title` - 对话框标题
  - `message` - 对话框消息内容
  - `show` - 是否显示对话框
  - `confirm_text` - 确认按钮文字，默认"确定"
  - `cancel_text` - 取消按钮文字，默认"取消"
  - `confirm_action` - 确认按钮事件名称
  - `cancel_action` - 取消按钮事件名称
  - `target` - 事件目标组件
  - `danger` - 是否为危险操作，影响按钮样式
  - `size` - 对话框大小: "sm", "md", "lg", "xl"
  - `closable` - 是否可通过点击遮罩或ESC键关闭
  - `icon` - 自定义图标类名
  - `custom_content` - 自定义内容插槽
  """
  attr :id, :string, required: true
  attr :type, :string, default: "info"
  attr :title, :string, default: "提示"
  attr :message, :string, default: ""
  attr :show, :boolean, default: false
  attr :confirm_text, :string, default: "确定"
  attr :cancel_text, :string, default: "取消"
  attr :confirm_action, :string, default: nil
  attr :cancel_action, :string, default: nil
  attr :confirm_data, :map, default: %{}
  attr :target, :any, default: nil
  attr :danger, :boolean, default: false
  attr :size, :string, default: "md"
  attr :closable, :boolean, default: true
  attr :icon, :string, default: nil

  slot :custom_content

  def dynamic_dialog(assigns) do
    assigns = assign_defaults(assigns)

    ~H"""
    <div
      id={@id}
      class={[
        "fixed inset-0 z-50 overflow-y-auto transition-all duration-300",
        if(@show, do: "opacity-100 pointer-events-auto", else: "opacity-0 pointer-events-none")
      ]}
      phx-window-keydown={if @closable and @cancel_action, do: @cancel_action}
      phx-key="escape"
      phx-target={@target}
    >
      <!-- 美化的遮罩层 -->
      <div
        class={[
          "fixed inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 transition-opacity duration-300",
          if(@show, do: "bg-opacity-60", else: "bg-opacity-0")
        ]}
        phx-click={if @closable and @cancel_action, do: @cancel_action}
        phx-target={@target}
      >
      </div>

      <!-- 对话框容器 - 完全居中显示，大小为背景遮罩的一半，蓝色边框，4px边距 -->
      <div class="flex min-h-full items-center justify-center p-4">
        <div
          id="draggable-dialog"
          class={[
            "relative transform overflow-hidden rounded-2xl bg-white shadow-2xl transition-all duration-300 border-4 border-blue-500 m-1",
            get_size_classes(@size),
            if(@show, do: "scale-100 translate-y-0 opacity-100", else: "scale-95 translate-y-4 opacity-0")
          ]}
          style="width: 50vw; height: 50vh; min-width: 400px; max-width: 90vw; max-height: 90vh; cursor: move;"
          phx-hook="DraggableDialog"
        >
          <!-- 美化的对话框头部 - 作为拖动手柄 -->
          <div class={[
            "px-6 py-4 border-b border-gray-100 bg-gradient-to-r drag-handle",
            get_header_gradient_class(@type)
          ]}
          style="cursor: move;"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <!-- 美化的图标 -->
                <div class={[
                  "flex-shrink-0 w-10 h-10 rounded-xl flex items-center justify-center mr-3 shadow-lg",
                  get_icon_bg_class(@type)
                ]}>
                  <i class={[
                    "text-lg",
                    get_icon_class(@type, @icon),
                    get_icon_color_class(@type)
                  ]}></i>
                </div>

                <!-- 美化的标题 -->
                <div>
                  <h3 class={[
                    "text-lg font-bold tracking-wide",
                    get_title_color_class(@type)
                  ]}>
                    <%= @title %>
                  </h3>
                  <p class="text-xs text-gray-500 mt-1">
                    <%= get_type_description(@type) %>
                  </p>
                </div>
              </div>

              <!-- 美化的关闭按钮 -->
              <%= if @closable and @cancel_action do %>
                <AdminButtonGroup.admin_button
                  text=""
                  action={@cancel_action}
                  target={@target}
                  icon="fas fa-times"
                  class="w-7 h-7 rounded-full bg-gray-100 hover:bg-gray-200 text-gray-400 hover:text-gray-600 transition-all duration-200 hover:scale-110 p-0"
                  bg_color="#f3f4f6"
                  style="cursor: pointer;"
                />
              <% end %>
            </div>
          </div>

          <!-- 美化的对话框内容 - 自适应高度，支持滚动，居中显示 -->
          <div class="px-6 py-4 flex-1 overflow-y-auto flex items-start justify-center" style="max-height: calc(50vh - 140px);">
            <%= if @custom_content do %>
              <div>
                <%= render_slot(@custom_content) %>
              </div>
            <% else %>
              <div class="text-center">
                <div class="max-w-sm mx-auto">
                  <p class="text-gray-700 leading-relaxed text-base">
                    <%= @message %>
                  </p>
                  <%= if @type == "warning" do %>
                    <div class="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <p class="text-sm text-yellow-800">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        请仔细确认您的操作
                      </p>
                    </div>
                  <% end %>
                  <%= if @type == "error" do %>
                    <div class="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                      <p class="text-sm text-red-800">
                        <i class="fas fa-info-circle mr-2"></i>
                        如需帮助，请联系系统管理员
                      </p>
                    </div>
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>

          <!-- 美化的对话框按钮区域 -->
          <div class="px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 border-t border-gray-100">
            <div class="flex justify-center space-x-3">
              <!-- 取消按钮 - 使用 AdminButtonGroup -->
              <%= if @cancel_action do %>
                <AdminButtonGroup.secondary_button
                  text={@cancel_text}
                  action={@cancel_action}
                  target={@target}
                  icon="fas fa-times"
                  class="min-w-[100px] shadow-md hover:shadow-lg transition-all duration-200"
                />
              <% end %>

              <!-- 确认按钮 - 使用 AdminButtonGroup -->
              <%= if @confirm_action do %>
                <%= case {@type, @danger} do %>
                  <% {"error", _} -> %>
                    <AdminButtonGroup.danger_button
                      text={@confirm_text}
                      action={@confirm_action}
                      target={@target}
                      icon={get_confirm_icon(@type)}
                      confirm_message=""
                      id={Map.get(@confirm_data, "id")}
                      class="min-w-[100px] shadow-md hover:shadow-lg transition-all duration-200"
                    />
                  <% {_, true} -> %>
                    <AdminButtonGroup.danger_button
                      text={@confirm_text}
                      action={@confirm_action}
                      target={@target}
                      icon={get_confirm_icon(@type)}
                      confirm_message=""
                      id={Map.get(@confirm_data, "id")}
                      class="min-w-[100px] shadow-md hover:shadow-lg transition-all duration-200"
                    />
                  <% {"warning", _} -> %>
                    <AdminButtonGroup.warning_button
                      text={@confirm_text}
                      action={@confirm_action}
                      target={@target}
                      icon={get_confirm_icon(@type)}
                      id={Map.get(@confirm_data, "id")}
                      class="min-w-[100px] shadow-md hover:shadow-lg transition-all duration-200"
                    />
                  <% {"success", _} -> %>
                    <AdminButtonGroup.success_button
                      text={@confirm_text}
                      action={@confirm_action}
                      target={@target}
                      icon={get_confirm_icon(@type)}
                      id={Map.get(@confirm_data, "id")}
                      class="min-w-[100px] shadow-md hover:shadow-lg transition-all duration-200"
                    />
                  <% _ -> %>
                    <AdminButtonGroup.primary_button
                      text={@confirm_text}
                      action={@confirm_action}
                      target={@target}
                      icon={get_confirm_icon(@type)}
                      id={Map.get(@confirm_data, "id")}
                      class="min-w-[100px] shadow-md hover:shadow-lg transition-all duration-200"
                    />
                <% end %>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end

  # 私有函数 - 设置默认值
  defp assign_defaults(assigns) do
    assigns
    |> assign_new(:show, fn -> false end)
    |> assign_new(:type, fn -> "info" end)
    |> assign_new(:title, fn -> "提示" end)
    |> assign_new(:message, fn -> "" end)
    |> assign_new(:confirm_text, fn -> "确定" end)
    |> assign_new(:cancel_text, fn -> "取消" end)
    |> assign_new(:confirm_action, fn -> nil end)
    |> assign_new(:cancel_action, fn -> nil end)
    |> assign_new(:confirm_data, fn -> %{} end)
    |> assign_new(:target, fn -> nil end)
    |> assign_new(:danger, fn -> false end)
    |> assign_new(:size, fn -> "md" end)
    |> assign_new(:closable, fn -> true end)
    |> assign_new(:icon, fn -> nil end)
    |> assign_new(:custom_content, fn -> nil end)
  end

  # 私有函数 - 获取尺寸类名（现在主要用于最小尺寸限制）
  defp get_size_classes("sm"), do: "min-w-[350px] min-h-[250px]"
  defp get_size_classes("md"), do: "min-w-[400px] min-h-[300px]"
  defp get_size_classes("lg"), do: "min-w-[500px] min-h-[400px]"
  defp get_size_classes("xl"), do: "min-w-[600px] min-h-[500px]"
  defp get_size_classes(_), do: "min-w-[400px] min-h-[300px]"

  # 私有函数 - 获取头部渐变类名
  defp get_header_gradient_class("error"), do: "from-red-50 to-red-100"
  defp get_header_gradient_class("warning"), do: "from-yellow-50 to-yellow-100"
  defp get_header_gradient_class("success"), do: "from-green-50 to-green-100"
  defp get_header_gradient_class("confirm"), do: "from-blue-50 to-blue-100"
  defp get_header_gradient_class(_), do: "from-gray-50 to-gray-100"

  # 私有函数 - 获取类型描述
  defp get_type_description("error"), do: "系统错误提示"
  defp get_type_description("warning"), do: "重要警告信息"
  defp get_type_description("success"), do: "操作成功确认"
  defp get_type_description("confirm"), do: "操作确认提示"
  defp get_type_description(_), do: "系统信息提示"

  # 私有函数 - 获取确认按钮图标
  defp get_confirm_icon("error"), do: "fas fa-exclamation-triangle"
  defp get_confirm_icon("warning"), do: "fas fa-exclamation-circle"
  defp get_confirm_icon("success"), do: "fas fa-check"
  defp get_confirm_icon("confirm"), do: "fas fa-check"
  defp get_confirm_icon(_), do: "fas fa-check"

  # 私有函数 - 获取头部背景类名
  defp get_header_bg_class("error"), do: "bg-red-50"
  defp get_header_bg_class("warning"), do: "bg-yellow-50"
  defp get_header_bg_class("success"), do: "bg-green-50"
  defp get_header_bg_class("confirm"), do: "bg-blue-50"
  defp get_header_bg_class(_), do: "bg-gray-50"

  # 私有函数 - 获取图标背景类名
  defp get_icon_bg_class("error"), do: "bg-red-100"
  defp get_icon_bg_class("warning"), do: "bg-yellow-100"
  defp get_icon_bg_class("success"), do: "bg-green-100"
  defp get_icon_bg_class("confirm"), do: "bg-blue-100"
  defp get_icon_bg_class(_), do: "bg-gray-100"

  # 私有函数 - 获取图标类名
  defp get_icon_class(_, icon) when is_binary(icon), do: icon
  defp get_icon_class("error", _), do: "fas fa-exclamation-triangle"
  defp get_icon_class("warning", _), do: "fas fa-exclamation-circle"
  defp get_icon_class("success", _), do: "fas fa-check-circle"
  defp get_icon_class("confirm", _), do: "fas fa-question-circle"
  defp get_icon_class(_, _), do: "fas fa-info-circle"

  # 私有函数 - 获取图标颜色类名
  defp get_icon_color_class("error"), do: "text-red-600"
  defp get_icon_color_class("warning"), do: "text-yellow-600"
  defp get_icon_color_class("success"), do: "text-green-600"
  defp get_icon_color_class("confirm"), do: "text-blue-600"
  defp get_icon_color_class(_), do: "text-gray-600"

  # 私有函数 - 获取标题颜色类名
  defp get_title_color_class("error"), do: "text-red-900"
  defp get_title_color_class("warning"), do: "text-yellow-900"
  defp get_title_color_class("success"), do: "text-green-900"
  defp get_title_color_class("confirm"), do: "text-blue-900"
  defp get_title_color_class(_), do: "text-gray-900"

  # 私有函数 - 获取确认按钮类名
  defp get_confirm_button_class(_, true) do
    "bg-red-600 hover:bg-red-700 focus:ring-red-500"
  end
  defp get_confirm_button_class("error", _) do
    "bg-red-600 hover:bg-red-700 focus:ring-red-500"
  end
  defp get_confirm_button_class("warning", _) do
    "bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500"
  end
  defp get_confirm_button_class("success", _) do
    "bg-green-600 hover:bg-green-700 focus:ring-green-500"
  end
  defp get_confirm_button_class(_, _) do
    "bg-blue-600 hover:bg-blue-700 focus:ring-blue-500"
  end

  @doc """
  简化版对话框组件

  提供更简单的API，适用于快速创建基本对话框。

  ## 使用示例

      <.simple_dialog
        id="quick-confirm"
        show={@show_dialog}
        title="确认操作"
        message="您确定要执行此操作吗？"
        on_confirm="confirm_action"
        on_cancel="cancel_action"
        target={@myself}
      />
  """
  def simple_dialog(assigns) do
    assigns = assign_simple_defaults(assigns)

    ~H"""
    <.dynamic_dialog
      id={@id}
      type={@type}
      title={@title}
      message={@message}
      show={@show}
      confirm_text={@confirm_text}
      cancel_text={@cancel_text}
      confirm_action={@on_confirm}
      cancel_action={@on_cancel}
      target={@target}
      danger={@danger}
      size={@size}
      closable={@closable}
      icon={@icon}
    />
    """
  end

  # 私有函数 - 简化版默认值
  defp assign_simple_defaults(assigns) do
    assigns
    |> assign_new(:show, fn -> false end)
    |> assign_new(:type, fn -> "confirm" end)
    |> assign_new(:title, fn -> "确认" end)
    |> assign_new(:message, fn -> "您确定要执行此操作吗？" end)
    |> assign_new(:confirm_text, fn -> "确定" end)
    |> assign_new(:cancel_text, fn -> "取消" end)
    |> assign_new(:on_confirm, fn -> nil end)
    |> assign_new(:on_cancel, fn -> nil end)
    |> assign_new(:target, fn -> nil end)
    |> assign_new(:danger, fn -> false end)
    |> assign_new(:size, fn -> "md" end)
    |> assign_new(:closable, fn -> true end)
    |> assign_new(:icon, fn -> nil end)
  end

  @doc """
  快速创建确认对话框的便捷函数

  ## 使用示例

      <.confirm_dialog
        id="delete-confirm"
        show={@show_delete_dialog}
        title="确认删除"
        message="此操作不可撤销"
        on_confirm="delete_item"
        on_cancel="close_dialog"
        target={@myself}
        danger={true}
      />
  """
  def confirm_dialog(assigns) do
    assigns = assign(assigns, :type, "confirm")
    simple_dialog(assigns)
  end

  @doc """
  快速创建信息对话框的便捷函数
  """
  def info_dialog(assigns) do
    assigns =
      assigns
      |> assign(:type, "info")
      |> assign_new(:on_cancel, fn -> nil end)
    simple_dialog(assigns)
  end

  @doc """
  快速创建警告对话框的便捷函数
  """
  def warning_dialog(assigns) do
    assigns = assign(assigns, :type, "warning")
    simple_dialog(assigns)
  end

  @doc """
  快速创建错误对话框的便捷函数
  """
  def error_dialog(assigns) do
    assigns =
      assigns
      |> assign(:type, "error")
      |> assign_new(:on_cancel, fn -> nil end)
    simple_dialog(assigns)
  end

  @doc """
  快速创建成功对话框的便捷函数
  """
  def success_dialog(assigns) do
    assigns =
      assigns
      |> assign(:type, "success")
      |> assign_new(:on_cancel, fn -> nil end)
    simple_dialog(assigns)
  end
end
