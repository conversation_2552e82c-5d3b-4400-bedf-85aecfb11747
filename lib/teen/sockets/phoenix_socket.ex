defmodule CypridinaWeb.PhoenixSocket do
  @moduledoc """
  Phoenix Framework Socket for Teen game.

  This module implements a Phoenix Socket to replace the original cowboy_websocket-based TeenSocket.
  It matches the client-side implementation using the Phoenix library.
  """

  use Phoenix.Socket
  require Logger

  ## Channels
  channel "game:lobby", CypridinaWeb.LobbyChannel
  channel "game:room_*", CypridinaWeb.GameChannel

  @impl true
  def connect(params, socket, connect_info) do
    peer_data = Map.get(connect_info, :peer_data, %{})
    session_id = :crypto.strong_rand_bytes(16) |> Base.encode16(case: :lower)

    # Logger.info("Phoenix Socket 连接请求: #{inspect(socket.endpoint)} - IP: #{inspect(peer_data)}")
    # Logger.info("Phoenix Socket 连接参数: #{inspect(params)}")

    session = Map.get(connect_info, :session)
    Logger.info("Phoenix Socket 连接Session: #{inspect(connect_info)}")

    socket =
      assign(socket, %{
        session_id: session_id,
        user_id: params["user_id"],
        connected_at: System.system_time(:millisecond),
        socket_info: %{
          peer_data: peer_data,
          x_headers: Map.get(connect_info, :x_headers),
          uri: Map.get(connect_info, :uri),
          session: Map.get(connect_info, :session)
        },
        authenticate: false,
        params: params
      })

    {:ok, socket}
  end

  @impl true
  def id(socket), do: "socket:#{socket.assigns.session_id}"
end
