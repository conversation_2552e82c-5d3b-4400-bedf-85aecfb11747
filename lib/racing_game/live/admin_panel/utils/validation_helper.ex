defmodule RacingGame.Live.AdminPanel.Utils.ValidationHelper do
  @moduledoc """
  验证助手工具
  
  提供通用的数据验证功能：
  - 必填字段验证
  - 数据类型验证
  - 格式验证
  - 业务规则验证
  """

  require Logger

  # ============================================================================
  # 基础验证
  # ============================================================================

  @doc """
  验证必填字段

  ## 参数
  - `data` - 数据映射
  - `required_fields` - 必填字段列表

  ## 返回
  - `:ok` - 验证通过
  - `{:error, reason}` - 验证失败
  """
  def validate_required(data, required_fields) when is_map(data) and is_list(required_fields) do
    missing_fields = Enum.filter(required_fields, fn field ->
      case Map.get(data, field) do
        nil -> true
        "" -> true
        _ -> false
      end
    end)
    
    case missing_fields do
      [] -> :ok
      fields -> {:error, "缺少必填字段: #{Enum.join(fields, ", ")}"}
    end
  end

  @doc """
  验证字符串长度

  ## 参数
  - `value` - 字符串值
  - `options` - 选项
    - `:min` - 最小长度
    - `:max` - 最大长度

  ## 返回
  - `:ok` - 验证通过
  - `{:error, reason}` - 验证失败
  """
  def validate_string_length(value, options \\ []) when is_binary(value) do
    length = String.length(value)
    min_length = Keyword.get(options, :min, 0)
    max_length = Keyword.get(options, :max, :infinity)
    
    cond do
      length < min_length ->
        {:error, "字符串长度不能少于#{min_length}个字符"}
      
      max_length != :infinity and length > max_length ->
        {:error, "字符串长度不能超过#{max_length}个字符"}
      
      true -> :ok
    end
  end
  def validate_string_length(_, _), do: {:error, "值必须是字符串"}

  @doc """
  验证数字范围

  ## 参数
  - `value` - 数字值
  - `options` - 选项
    - `:min` - 最小值
    - `:max` - 最大值

  ## 返回
  - `:ok` - 验证通过
  - `{:error, reason}` - 验证失败
  """
  def validate_number_range(value, options \\ []) when is_number(value) do
    min_value = Keyword.get(options, :min, :negative_infinity)
    max_value = Keyword.get(options, :max, :infinity)
    
    cond do
      min_value != :negative_infinity and value < min_value ->
        {:error, "数值不能小于#{min_value}"}
      
      max_value != :infinity and value > max_value ->
        {:error, "数值不能大于#{max_value}"}
      
      true -> :ok
    end
  end
  def validate_number_range(_, _), do: {:error, "值必须是数字"}

  @doc """
  验证枚举值

  ## 参数
  - `value` - 值
  - `valid_values` - 有效值列表

  ## 返回
  - `:ok` - 验证通过
  - `{:error, reason}` - 验证失败
  """
  def validate_enum(value, valid_values) when is_list(valid_values) do
    if value in valid_values do
      :ok
    else
      {:error, "无效的值，有效值为: #{inspect(valid_values)}"}
    end
  end

  # ============================================================================
  # 格式验证
  # ============================================================================

  @doc """
  验证邮箱格式

  ## 参数
  - `email` - 邮箱地址

  ## 返回
  - `:ok` - 验证通过
  - `{:error, reason}` - 验证失败
  """
  def validate_email(email) when is_binary(email) do
    email_regex = ~r/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
    
    if Regex.match?(email_regex, email) do
      :ok
    else
      {:error, "邮箱格式无效"}
    end
  end
  def validate_email(_), do: {:error, "邮箱必须是字符串"}

  @doc """
  验证手机号格式

  ## 参数
  - `phone` - 手机号

  ## 返回
  - `:ok` - 验证通过
  - `{:error, reason}` - 验证失败
  """
  def validate_phone(phone) when is_binary(phone) do
    # 简单的手机号验证（中国大陆）
    phone_regex = ~r/^1[3-9]\d{9}$/
    
    if Regex.match?(phone_regex, phone) do
      :ok
    else
      {:error, "手机号格式无效"}
    end
  end
  def validate_phone(_), do: {:error, "手机号必须是字符串"}

  @doc """
  验证UUID格式

  ## 参数
  - `uuid` - UUID字符串

  ## 返回
  - `:ok` - 验证通过
  - `{:error, reason}` - 验证失败
  """
  def validate_uuid(uuid) when is_binary(uuid) do
    uuid_regex = ~r/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
    
    if Regex.match?(uuid_regex, uuid) do
      :ok
    else
      {:error, "UUID格式无效"}
    end
  end
  def validate_uuid(_), do: {:error, "UUID必须是字符串"}

  @doc """
  验证日期格式

  ## 参数
  - `date_string` - 日期字符串
  - `format` - 日期格式（可选，默认为ISO 8601）

  ## 返回
  - `:ok` - 验证通过
  - `{:error, reason}` - 验证失败
  """
  def validate_date_format(date_string, format \\ :iso8601) when is_binary(date_string) do
    case format do
      :iso8601 ->
        case DateTime.from_iso8601(date_string) do
          {:ok, _, _} -> :ok
          {:error, _} -> {:error, "日期格式无效，应为ISO 8601格式"}
        end
      
      _ -> {:error, "不支持的日期格式"}
    end
  end
  def validate_date_format(_, _), do: {:error, "日期必须是字符串"}

  # ============================================================================
  # 业务验证
  # ============================================================================

  @doc """
  验证密码强度

  ## 参数
  - `password` - 密码
  - `options` - 选项
    - `:min_length` - 最小长度
    - `:require_uppercase` - 是否需要大写字母
    - `:require_lowercase` - 是否需要小写字母
    - `:require_digit` - 是否需要数字
    - `:require_special` - 是否需要特殊字符

  ## 返回
  - `:ok` - 验证通过
  - `{:error, reason}` - 验证失败
  """
  def validate_password_strength(password, options \\ []) when is_binary(password) do
    min_length = Keyword.get(options, :min_length, 8)
    require_uppercase = Keyword.get(options, :require_uppercase, true)
    require_lowercase = Keyword.get(options, :require_lowercase, true)
    require_digit = Keyword.get(options, :require_digit, true)
    require_special = Keyword.get(options, :require_special, false)
    
    errors = []
    
    errors = if String.length(password) < min_length do
      ["密码长度至少#{min_length}位" | errors]
    else
      errors
    end
    
    errors = if require_uppercase and not Regex.match?(~r/[A-Z]/, password) do
      ["密码必须包含大写字母" | errors]
    else
      errors
    end
    
    errors = if require_lowercase and not Regex.match?(~r/[a-z]/, password) do
      ["密码必须包含小写字母" | errors]
    else
      errors
    end
    
    errors = if require_digit and not Regex.match?(~r/\d/, password) do
      ["密码必须包含数字" | errors]
    else
      errors
    end
    
    errors = if require_special and not Regex.match?(~r/[!@#$%^&*(),.?":{}|<>]/, password) do
      ["密码必须包含特殊字符" | errors]
    else
      errors
    end
    
    case errors do
      [] -> :ok
      _ -> {:error, Enum.join(Enum.reverse(errors), "；")}
    end
  end
  def validate_password_strength(_, _), do: {:error, "密码必须是字符串"}

  @doc """
  验证用户名格式

  ## 参数
  - `username` - 用户名

  ## 返回
  - `:ok` - 验证通过
  - `{:error, reason}` - 验证失败
  """
  def validate_username(username) when is_binary(username) do
    # 用户名规则：3-20位，只能包含字母、数字、下划线，不能以数字开头
    username_regex = ~r/^[a-zA-Z_][a-zA-Z0-9_]{2,19}$/
    
    cond do
      String.length(username) < 3 ->
        {:error, "用户名长度不能少于3位"}
      
      String.length(username) > 20 ->
        {:error, "用户名长度不能超过20位"}
      
      not Regex.match?(username_regex, username) ->
        {:error, "用户名只能包含字母、数字、下划线，且不能以数字开头"}
      
      true -> :ok
    end
  end
  def validate_username(_), do: {:error, "用户名必须是字符串"}

  # ============================================================================
  # 批量验证
  # ============================================================================

  @doc """
  批量验证

  ## 参数
  - `validations` - 验证列表，每个元素为 {validation_function, args}

  ## 返回
  - `:ok` - 所有验证通过
  - `{:error, errors}` - 验证失败，返回错误列表
  """
  def validate_batch(validations) when is_list(validations) do
    errors = Enum.reduce(validations, [], fn {validation_func, args}, acc ->
      case apply(__MODULE__, validation_func, args) do
        :ok -> acc
        {:error, error} -> [error | acc]
      end
    end)
    
    case errors do
      [] -> :ok
      _ -> {:error, Enum.reverse(errors)}
    end
  end

  @doc """
  条件验证

  ## 参数
  - `condition` - 条件函数或布尔值
  - `validation_func` - 验证函数
  - `args` - 验证函数参数

  ## 返回
  - `:ok` - 验证通过或条件不满足
  - `{:error, reason}` - 验证失败
  """
  def validate_if(condition, validation_func, args) do
    should_validate = if is_function(condition, 0) do
      condition.()
    else
      condition
    end
    
    if should_validate do
      apply(__MODULE__, validation_func, args)
    else
      :ok
    end
  end

  # ============================================================================
  # 自定义验证
  # ============================================================================

  @doc """
  自定义验证

  ## 参数
  - `value` - 要验证的值
  - `validator_func` - 验证函数，接收值并返回 :ok 或 {:error, reason}

  ## 返回
  - `:ok` - 验证通过
  - `{:error, reason}` - 验证失败
  """
  def validate_custom(value, validator_func) when is_function(validator_func, 1) do
    try do
      validator_func.(value)
    rescue
      error ->
        Logger.error("自定义验证函数执行异常: #{inspect(error)}")
        {:error, "验证执行异常"}
    end
  end

  @doc """
  验证数据结构

  ## 参数
  - `data` - 数据
  - `schema` - 验证模式

  ## 返回
  - `:ok` - 验证通过
  - `{:error, errors}` - 验证失败
  """
  def validate_schema(data, schema) when is_map(data) and is_map(schema) do
    errors = Enum.reduce(schema, [], fn {field, rules}, acc ->
      value = Map.get(data, field)
      
      case validate_field_rules(value, rules) do
        :ok -> acc
        {:error, error} -> [{field, error} | acc]
      end
    end)
    
    case errors do
      [] -> :ok
      _ -> {:error, Enum.reverse(errors)}
    end
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 验证字段规则
  defp validate_field_rules(value, rules) when is_list(rules) do
    Enum.reduce_while(rules, :ok, fn rule, _acc ->
      case apply_rule(value, rule) do
        :ok -> {:cont, :ok}
        {:error, error} -> {:halt, {:error, error}}
      end
    end)
  end
  defp validate_field_rules(value, rule) do
    apply_rule(value, rule)
  end

  # 应用单个规则
  defp apply_rule(value, {:required, true}) do
    if value == nil or value == "" do
      {:error, "字段为必填项"}
    else
      :ok
    end
  end
  defp apply_rule(value, {:type, type}) do
    validate_type(value, type)
  end
  defp apply_rule(value, {:length, options}) when is_binary(value) do
    validate_string_length(value, options)
  end
  defp apply_rule(value, {:range, options}) when is_number(value) do
    validate_number_range(value, options)
  end
  defp apply_rule(value, {:enum, valid_values}) do
    validate_enum(value, valid_values)
  end
  defp apply_rule(value, {:custom, validator_func}) do
    validate_custom(value, validator_func)
  end
  defp apply_rule(_value, _rule) do
    :ok
  end

  # 验证类型
  defp validate_type(value, :string) when is_binary(value), do: :ok
  defp validate_type(value, :integer) when is_integer(value), do: :ok
  defp validate_type(value, :float) when is_float(value), do: :ok
  defp validate_type(value, :number) when is_number(value), do: :ok
  defp validate_type(value, :boolean) when is_boolean(value), do: :ok
  defp validate_type(value, :atom) when is_atom(value), do: :ok
  defp validate_type(value, :list) when is_list(value), do: :ok
  defp validate_type(value, :map) when is_map(value), do: :ok
  defp validate_type(_value, type), do: {:error, "类型不匹配，期望: #{type}"}
end
