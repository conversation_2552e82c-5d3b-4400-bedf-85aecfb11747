defmodule RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.LimitedGiftRepository do
  @moduledoc """
  🎁 限时礼包仓储层
  
  负责限时礼包相关的数据访问操作，包括：
  - 限时礼包的CRUD操作
  - 礼包状态管理（启用/禁用）
  - 礼包类型查询
  - 时间范围查询
  - 使用统计管理
  """

  require Logger
  alias Teen.ActivitySystem.LimitedGift
  alias Ash.Query

  # ==================== 基础CRUD操作 ====================

  @doc """
  创建限时礼包
  """
  def create_limited_gift(gift_data, options \\ []) do
    Logger.info("🎁 [限时礼包仓储] 创建限时礼包: #{inspect(gift_data[:gift_name])}")
    
    try do
      result = LimitedGift
      |> Ash.Changeset.for_create(:create, gift_data)
      |> Ash.create()
      
      case result do
        {:ok, gift} ->
          Logger.info("✅ [限时礼包仓储] 限时礼包创建成功: #{gift.gift_name}")
          maybe_load_associations({:ok, gift}, options)
          
        {:error, reason} ->
          Logger.error("❌ [限时礼包仓储] 限时礼包创建失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [限时礼包仓储] 创建限时礼包异常: #{inspect(exception)}")
        {:error, :create_failed}
    end
  end

  @doc """
  根据ID获取限时礼包
  """
  def get_limited_gift(gift_id, options \\ []) do
    Logger.info("🎁 [限时礼包仓储] 获取限时礼包: #{gift_id}")
    
    try do
      result = LimitedGift
      |> Ash.Query.for_read(:read)
      |> Ash.Query.filter(id == ^gift_id)
      |> maybe_load_associations(options)
      |> Ash.read_one()
      
      case result do
        {:ok, nil} ->
          Logger.warn("⚠️ [限时礼包仓储] 限时礼包不存在: #{gift_id}")
          {:error, :not_found}
          
        {:ok, gift} ->
          Logger.info("✅ [限时礼包仓储] 获取限时礼包成功: #{gift.gift_name}")
          {:ok, gift}
          
        {:error, reason} ->
          Logger.error("❌ [限时礼包仓储] 获取限时礼包失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [限时礼包仓储] 获取限时礼包异常: #{inspect(exception)}")
        {:error, :query_failed}
    end
  end

  @doc """
  更新限时礼包
  """
  def update_limited_gift(gift_id, update_data, options \\ []) do
    Logger.info("🎁 [限时礼包仓储] 更新限时礼包: #{gift_id}")
    
    try do
      with {:ok, gift} <- get_limited_gift(gift_id),
           {:ok, updated_gift} <- LimitedGift.update(gift, update_data) do
        Logger.info("✅ [限时礼包仓储] 限时礼包更新成功: #{updated_gift.gift_name}")
        maybe_load_associations({:ok, updated_gift}, options)
      else
        {:error, reason} ->
          Logger.error("❌ [限时礼包仓储] 更新限时礼包失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [限时礼包仓储] 更新限时礼包异常: #{inspect(exception)}")
        {:error, :update_failed}
    end
  end

  @doc """
  删除限时礼包
  """
  def delete_limited_gift(gift_id) do
    Logger.info("🎁 [限时礼包仓储] 删除限时礼包: #{gift_id}")
    
    try do
      with {:ok, gift} <- get_limited_gift(gift_id),
           :ok <- LimitedGift.destroy(gift) do
        Logger.info("✅ [限时礼包仓储] 限时礼包删除成功: #{gift_id}")
        {:ok, :deleted}
      else
        {:error, reason} ->
          Logger.error("❌ [限时礼包仓储] 删除限时礼包失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [限时礼包仓储] 删除限时礼包异常: #{inspect(exception)}")
        {:error, :delete_failed}
    end
  end

  # ==================== 列表查询操作 ====================

  @doc """
  获取限时礼包列表
  """
  def list_limited_gifts(options \\ []) do
    Logger.info("🎁 [限时礼包仓储] 获取限时礼包列表")
    
    try do
      query = LimitedGift
      |> Ash.Query.for_read(:read)
      |> apply_list_filters(options)
      |> apply_sorting(options)
      |> maybe_load_associations(options)
      
      result = if options[:paginate] do
        Ash.read(query, page: build_page_params(options))
      else
        Ash.read(query)
      end
      
      case result do
        {:ok, gifts} when is_list(gifts) ->
          Logger.info("✅ [限时礼包仓储] 获取限时礼包列表成功: #{length(gifts)}条")
          {:ok, gifts}
          
        {:ok, %{results: gifts, more?: more?, count: count}} ->
          Logger.info("✅ [限时礼包仓储] 分页获取限时礼包成功: #{length(gifts)}条，总数: #{count}")
          {:ok, %{gifts: gifts, more?: more?, count: count}}
          
        {:error, reason} ->
          Logger.error("❌ [限时礼包仓储] 获取限时礼包列表失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [限时礼包仓储] 获取限时礼包列表异常: #{inspect(exception)}")
        {:error, :query_failed}
    end
  end

  @doc """
  获取启用的限时礼包列表
  """
  def list_active_limited_gifts(options \\ []) do
    Logger.info("🎁 [限时礼包仓储] 获取启用的限时礼包列表")
    
    try do
      result = LimitedGift
      |> Ash.Query.for_read(:list_active_gifts)
      |> apply_sorting(options)
      |> maybe_load_associations(options)
      |> Ash.read()
      
      case result do
        {:ok, gifts} ->
          Logger.info("✅ [限时礼包仓储] 获取启用限时礼包成功: #{length(gifts)}条")
          {:ok, gifts}
          
        {:error, reason} ->
          Logger.error("❌ [限时礼包仓储] 获取启用限时礼包失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [限时礼包仓储] 获取启用限时礼包异常: #{inspect(exception)}")
        {:error, :query_failed}
    end
  end

  @doc """
  获取当前进行中的限时礼包列表
  """
  def list_current_limited_gifts(options \\ []) do
    Logger.info("🎁 [限时礼包仓储] 获取当前进行中的限时礼包列表")
    
    try do
      result = LimitedGift
      |> Ash.Query.for_read(:list_current_gifts)
      |> apply_sorting(options)
      |> maybe_load_associations(options)
      |> Ash.read()
      
      case result do
        {:ok, gifts} ->
          Logger.info("✅ [限时礼包仓储] 获取当前限时礼包成功: #{length(gifts)}条")
          {:ok, gifts}
          
        {:error, reason} ->
          Logger.error("❌ [限时礼包仓储] 获取当前限时礼包失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [限时礼包仓储] 获取当前限时礼包异常: #{inspect(exception)}")
        {:error, :query_failed}
    end
  end

  @doc """
  根据类型获取限时礼包列表
  """
  def list_limited_gifts_by_type(gift_type, options \\ []) do
    Logger.info("🎁 [限时礼包仓储] 根据类型获取限时礼包列表: #{gift_type}")
    
    try do
      result = LimitedGift
      |> Ash.Query.for_read(:list_by_type, %{gift_type: gift_type})
      |> apply_sorting(options)
      |> maybe_load_associations(options)
      |> Ash.read()
      
      case result do
        {:ok, gifts} ->
          Logger.info("✅ [限时礼包仓储] 获取类型限时礼包成功: #{length(gifts)}条")
          {:ok, gifts}
          
        {:error, reason} ->
          Logger.error("❌ [限时礼包仓储] 获取类型限时礼包失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [限时礼包仓储] 获取类型限时礼包异常: #{inspect(exception)}")
        {:error, :query_failed}
    end
  end

  # ==================== 状态管理操作 ====================

  @doc """
  启用限时礼包
  """
  def enable_limited_gift(gift_id) do
    Logger.info("🎁 [限时礼包仓储] 启用限时礼包: #{gift_id}")
    
    try do
      with {:ok, gift} <- get_limited_gift(gift_id),
           {:ok, updated_gift} <- LimitedGift.enable(gift) do
        Logger.info("✅ [限时礼包仓储] 限时礼包启用成功: #{updated_gift.gift_name}")
        {:ok, updated_gift}
      else
        {:error, reason} ->
          Logger.error("❌ [限时礼包仓储] 启用限时礼包失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [限时礼包仓储] 启用限时礼包异常: #{inspect(exception)}")
        {:error, :enable_failed}
    end
  end

  @doc """
  禁用限时礼包
  """
  def disable_limited_gift(gift_id) do
    Logger.info("🎁 [限时礼包仓储] 禁用限时礼包: #{gift_id}")
    
    try do
      with {:ok, gift} <- get_limited_gift(gift_id),
           {:ok, updated_gift} <- LimitedGift.disable(gift) do
        Logger.info("✅ [限时礼包仓储] 限时礼包禁用成功: #{updated_gift.gift_name}")
        {:ok, updated_gift}
      else
        {:error, reason} ->
          Logger.error("❌ [限时礼包仓储] 禁用限时礼包失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [限时礼包仓储] 禁用限时礼包异常: #{inspect(exception)}")
        {:error, :disable_failed}
    end
  end

  # ==================== 私有辅助函数 ====================

  # 应用列表过滤条件
  defp apply_list_filters(query, options) do
    query
    |> maybe_filter_by_name(options[:name])
    |> maybe_filter_by_type(options[:gift_type])
    |> maybe_filter_by_status(options[:status])
    |> maybe_filter_by_time_range(options[:time_range])
    |> maybe_filter_by_level(options[:level])
  end

  # 按礼包名称过滤
  defp maybe_filter_by_name(query, nil), do: query
  defp maybe_filter_by_name(query, name) when is_binary(name) do
    Ash.Query.filter(query, contains(gift_name, ^name))
  end

  # 按礼包类型过滤
  defp maybe_filter_by_type(query, nil), do: query
  defp maybe_filter_by_type(query, gift_type) when is_integer(gift_type) do
    Ash.Query.filter(query, gift_type == ^gift_type)
  end

  # 按状态过滤
  defp maybe_filter_by_status(query, nil), do: query
  defp maybe_filter_by_status(query, status) when is_integer(status) do
    Ash.Query.filter(query, status == ^status)
  end

  # 按时间范围过滤
  defp maybe_filter_by_time_range(query, nil), do: query
  defp maybe_filter_by_time_range(query, {start_time, end_time}) do
    Ash.Query.filter(query, start_time >= ^start_time and end_time <= ^end_time)
  end

  # 按等级过滤
  defp maybe_filter_by_level(query, nil), do: query
  defp maybe_filter_by_level(query, level) when is_integer(level) do
    Ash.Query.filter(query, is_nil(required_level) or required_level <= ^level)
  end

  # 应用排序
  defp apply_sorting(query, options) do
    case options[:sort] do
      nil -> Ash.Query.sort(query, priority: :desc, inserted_at: :desc)
      sort_params -> Ash.Query.sort(query, sort_params)
    end
  end

  # 构建分页参数
  defp build_page_params(options) do
    %{
      limit: options[:limit] || 20,
      offset: options[:offset] || 0,
      count: true
    }
  end

  # 可能加载关联数据
  defp maybe_load_associations({:ok, data}, options) do
    case options[:load] do
      nil -> {:ok, data}
      associations -> 
        case Ash.load(data, associations) do
          {:ok, loaded_data} -> {:ok, loaded_data}
          {:error, reason} -> {:error, reason}
        end
    end
  end

  defp maybe_load_associations(query, options) do
    case options[:load] do
      nil -> query
      associations -> Ash.Query.load(query, associations)
    end
  end
end
