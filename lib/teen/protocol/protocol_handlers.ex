defmodule Cypridina.Protocol.ProtocolHandlers do
  @moduledoc """
  cypridina项目的协议处理函数模块
  实现IndiaGameClient中Protocol.ts定义的所有协议处理
  参考IndiaGameServer的处理逻辑
  """

  require Logger

  # ==================== FindPsw 协议处理 ====================

  @doc """
  处理找回密码请求 (FindPsw.CS_FINDPSW_P)
  """
  def handle_find_password(%{main_id: 1, sub_id: 0} = message, state) do
    Logger.info("找回密码请求: #{inspect(message.data)}")

    data = message.data || %{}
    username = Map.get(data, "username", "")

    # 模拟找回密码验证
    response_data =
      cond do
        String.length(username) < 3 ->
          %{
            "status" => 1,
            "message" => "用户名不能少于3个字符"
          }

        true ->
          %{
            "status" => 0,
            "message" => "找回密码请求已发送",
            "username" => username,
            "reset_token" => "reset_#{:rand.uniform(100_000)}"
          }
      end

    response_map = %{
      "mainId" => 1,
      "subId" => 1,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理请求手机验证码 (FindPsw.CS_FINDPSW_REQUEST_CODE_P)
  """
  def handle_find_password_request_code(%{main_id: 1, sub_id: 2} = message, state) do
    Logger.info("请求手机验证码: #{inspect(message.data)}")

    data = message.data || %{}
    phone = Map.get(data, "phone", "")

    response_data = %{
      "status" => 0,
      "message" => "验证码已发送",
      "phone" => phone,
      "code_id" => "code_#{:rand.uniform(100_000)}"
    }

    response_map = %{
      "mainId" => 1,
      "subId" => 3,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理输入密保答案 (FindPsw.CS_FINDPSW_CRYPT_P)
  """
  def handle_find_password_crypt(%{main_id: 1, sub_id: 4} = message, state) do
    Logger.info("输入密保答案: #{inspect(message.data)}")

    data = message.data || %{}
    answer = Map.get(data, "answer", "")

    # 模拟密保验证
    is_correct = String.length(answer) > 0

    response_data = %{
      "status" => if(is_correct, do: 0, else: 1),
      "message" => if(is_correct, do: "密保验证成功", else: "密保答案错误"),
      "verified" => is_correct
    }

    response_map = %{
      "mainId" => 1,
      "subId" => 6,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理输入手机验证码 (FindPsw.CS_FINDPSW_PHONECODE_P)
  """
  def handle_find_password_phone_code(%{main_id: 1, sub_id: 5} = message, state) do
    Logger.info("输入手机验证码: #{inspect(message.data)}")

    data = message.data || %{}
    code = Map.get(data, "code", "")

    # 模拟验证码验证
    is_correct = String.length(code) == 6

    response_data = %{
      "status" => if(is_correct, do: 0, else: 1),
      "message" => if(is_correct, do: "验证码验证成功", else: "验证码错误"),
      "verified" => is_correct
    }

    response_map = %{
      "mainId" => 1,
      "subId" => 6,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理设置新密码 (FindPsw.CS_FINDPSW_SET_NEW_PSW_P)
  """
  def handle_find_password_set_new_password(%{main_id: 1, sub_id: 7} = message, state) do
    Logger.info("设置新密码: #{inspect(message.data)}")

    data = message.data || %{}
    new_password = Map.get(data, "new_password", "")

    response_data =
      cond do
        String.length(new_password) < 6 ->
          %{
            "status" => 1,
            "message" => "密码长度不能少于6个字符"
          }

        true ->
          %{
            "status" => 0,
            "message" => "密码重置成功"
          }
      end

    response_map = %{
      "mainId" => 1,
      "subId" => 8,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # ==================== NoticeManager 协议处理 ====================

  @doc """
  处理发送公告请求 (NoticeManager.CS_SEND_NOTICE_P)
  """
  def handle_send_notice(%{main_id: 15, sub_id: 1} = message, state) do
    Logger.info("发送公告请求: #{inspect(message.data)}")

    data = message.data || %{}
    notice_content = Map.get(data, "content", "")
    notice_type = Map.get(data, "type", 1)

    response_data = %{
      "status" => 0,
      "message" => "公告发送成功",
      "notice_id" => "notice_#{:rand.uniform(100_000)}",
      "content" => notice_content,
      "type" => notice_type
    }

    response_map = %{
      "mainId" => 15,
      "subId" => 2,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理请求发送公告所需信息 (NoticeManager.CS_REQUEST_NOTICE_NEED_P)
  """
  def handle_request_notice_need(%{main_id: 15, sub_id: 3} = message, state) do
    Logger.info("请求发送公告所需信息: #{inspect(message.data)}")

    response_data = %{
      "status" => 0,
      "max_length" => 500,
      "notice_types" => [
        %{"id" => 1, "name" => "系统公告"},
        %{"id" => 2, "name" => "活动公告"},
        %{"id" => 3, "name" => "维护公告"}
      ],
      "user_level_required" => 5
    }

    response_map = %{
      "mainId" => 15,
      "subId" => 4,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理请求系统公告内容 (NoticeManager.CD_REQUEST_SYSTEM_NOTICE_P)
  """
  def handle_request_system_notice(%{main_id: 15, sub_id: 5} = message, state) do
    Logger.info("请求系统公告内容: #{inspect(message.data)}")

    # 模拟系统公告列表
    notices = [
      %{
        "id" => 1,
        "title" => "系统维护公告",
        "content" => "系统将于今晚22:00-24:00进行维护，请提前做好准备。",
        "type" => 3,
        "priority" => 1,
        "start_time" => System.system_time(:millisecond),
        "end_time" => System.system_time(:millisecond) + 86_400_000
      },
      %{
        "id" => 2,
        "title" => "新版本更新",
        "content" => "游戏已更新至v2.1.0版本，新增多项功能。",
        "type" => 1,
        "priority" => 2,
        "start_time" => System.system_time(:millisecond) - 3_600_000,
        "end_time" => System.system_time(:millisecond) + 86_400_000 * 7
      }
    ]

    response_data = %{
      "status" => 0,
      "notices" => notices,
      "total_count" => length(notices)
    }

    response_map = %{
      "mainId" => 15,
      "subId" => 6,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # ==================== MailManager 协议处理 ====================

  @doc """
  处理请求邮件内容 (MailManager.CS_REQUEST_MAIL_INFO_P)
  """
  def handle_request_mail_info(%{main_id: 14, sub_id: 0} = message, state) do
    Logger.info("请求邮件内容: #{inspect(message.data)}")

    data = message.data || %{}
    mail_id = Map.get(data, "mail_id", 0)

    # 模拟邮件详情
    mail_info = %{
      "id" => mail_id,
      "title" => "系统邮件",
      "sender" => "系统管理员",
      "content" => "这是一封系统邮件，包含重要信息。",
      "send_time" => System.system_time(:millisecond) - 3_600_000,
      "is_read" => false,
      "has_attachment" => true,
      "attachments" => [
        %{"type" => "money", "amount" => 1000},
        %{"type" => "item", "item_id" => 1, "count" => 5}
      ]
    }

    response_data = %{
      "status" => 0,
      "mail_info" => mail_info
    }

    response_map = %{
      "mainId" => 14,
      "subId" => 1,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理设置邮件为已读 (MailManager.CS_MAIL_SET_READ_P)
  """
  def handle_mail_set_read(%{main_id: 14, sub_id: 2} = message, state) do
    Logger.info("设置邮件为已读: #{inspect(message.data)}")

    data = message.data || %{}
    mail_id = Map.get(data, "mail_id", 0)

    response_data = %{
      "status" => 0,
      "mail_id" => mail_id,
      "message" => "邮件已标记为已读"
    }

    # 由于这是设置操作，不需要特定的响应协议，使用通用响应
    response_map = %{
      "mainId" => 14,
      "subId" => 2,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理删除邮件 (MailManager.CS_DEL_MAIL_INFO_P)
  """
  def handle_delete_mail(%{main_id: 14, sub_id: 3} = message, state) do
    Logger.info("删除邮件: #{inspect(message.data)}")

    data = message.data || %{}
    mail_id = Map.get(data, "mail_id", 0)

    response_data = %{
      "status" => 0,
      "mail_id" => mail_id,
      "message" => "邮件删除成功"
    }

    response_map = %{
      "mainId" => 14,
      "subId" => 3,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理请求邮件列表 (MailManager.CS_REQUEST_MAILLIST_P)
  """
  def handle_request_mail_list(%{main_id: 14, sub_id: 5} = message, state) do
    Logger.info("请求邮件列表: #{inspect(message.data)}")

    data = message.data || %{}
    page = Map.get(data, "page", 1)
    page_size = Map.get(data, "page_size", 10)

    # 模拟邮件列表
    mail_list = [
      %{
        "id" => 1,
        "title" => "欢迎来到游戏",
        "sender" => "系统",
        "send_time" => System.system_time(:millisecond) - 86_400_000,
        "is_read" => false,
        "has_attachment" => true
      },
      %{
        "id" => 2,
        "title" => "每日签到奖励",
        "sender" => "系统",
        "send_time" => System.system_time(:millisecond) - 3_600_000,
        "is_read" => true,
        "has_attachment" => false
      },
      %{
        "id" => 3,
        "title" => "活动通知",
        "sender" => "活动管理员",
        "send_time" => System.system_time(:millisecond) - 7_200_000,
        "is_read" => false,
        "has_attachment" => true
      }
    ]

    response_data = %{
      "status" => 0,
      "mail_list" => mail_list,
      "total_count" => length(mail_list),
      "page" => page,
      "page_size" => page_size,
      "unread_count" => Enum.count(mail_list, fn mail -> !mail["is_read"] end)
    }

    response_map = %{
      "mainId" => 14,
      "subId" => 6,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理请求新邮件数量 (MailManager.CS_REQUEST_NEW_MAIL_COUNT_P)
  """
  def handle_request_new_mail_count(%{main_id: 14, sub_id: 7} = message, state) do
    Logger.info("请求新邮件数量: #{inspect(message.data)}")

    # 模拟新邮件数量
    new_mail_count = :rand.uniform(5)

    response_data = %{
      "status" => 0,
      "new_mail_count" => new_mail_count,
      "total_mail_count" => new_mail_count + :rand.uniform(10)
    }

    response_map = %{
      "mainId" => 14,
      "subId" => 8,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # ==================== Rank 协议处理 ====================

  @doc """
  处理获取排行榜信息 (Rank.CS_RANK_DATA)
  """
  def handle_rank_data(%{main_id: 40, sub_id: 0} = message, state) do
    Logger.info("获取排行榜信息: #{inspect(message.data)}")

    data = message.data || %{}
    # 1-金币排行, 2-胜率排行, 3-等级排行
    rank_type = Map.get(data, "type", 1)

    # 模拟排行榜数据
    rank_list = [
      %{
        "rank" => 1,
        "user_id" => "user_001",
        "nickname" => "排行榜第一",
        "avatar" => "avatar1.jpg",
        "score" => 1_000_000,
        "level" => 50
      },
      %{
        "rank" => 2,
        "user_id" => "user_002",
        "nickname" => "排行榜第二",
        "avatar" => "avatar2.jpg",
        "score" => 800_000,
        "level" => 45
      },
      %{
        "rank" => 3,
        "user_id" => "user_003",
        "nickname" => "排行榜第三",
        "avatar" => "avatar3.jpg",
        "score" => 600_000,
        "level" => 40
      }
    ]

    response_data = %{
      "status" => 0,
      "rank_type" => rank_type,
      "rank_list" => rank_list,
      "my_rank" => :rand.uniform(100),
      "my_score" => :rand.uniform(100_000)
    }

    response_map = %{
      "mainId" => 40,
      "subId" => 1,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理获取自己的排行榜数据 (Rank.CS_SELF_RANK_DATA_P)
  """
  def handle_self_rank_data(%{main_id: 40, sub_id: 4} = message, state) do
    Logger.info("获取自己的排行榜数据: #{inspect(message.data)}")

    data = message.data || %{}
    rank_type = Map.get(data, "type", 1)

    response_data = %{
      "status" => 0,
      "rank_type" => rank_type,
      "my_rank" => :rand.uniform(100),
      "my_score" => :rand.uniform(100_000),
      "total_players" => 10000,
      # -10到+10的排名变化
      "rank_change" => :rand.uniform(20) - 10
    }

    response_map = %{
      "mainId" => 40,
      "subId" => 5,
      "data" => response_data
    }

    {:reply, response_map, state}
  end
end
