defmodule RacingGame.Live.AdminPanel.Repositories.QueryBuilders.StockQueryBuilder do
  @moduledoc """
  股票持仓查询构建器
  
  提供复杂股票持仓查询的构建功能：
  - 动态查询条件构建
  - 用户和赛车手过滤
  - 数量和成本过滤
  - 统计查询构建
  """

  require Logger
  alias RacingGame.Stock
  alias Cypridina.Accounts.User
  require Ash.Query

  # 常量定义
  @filterable_fields [:user_id, :racer_id, :quantity, :total_cost, :updated_at, :inserted_at]
  @sortable_fields [:quantity, :total_cost, :updated_at, :inserted_at]

  # ============================================================================
  # 公共API
  # ============================================================================

  @doc """
  构建列表查询

  ## 参数
  - `params` - 查询参数
    - `:user_id` - 用户ID过滤
    - `:racer_id` - 赛车手ID过滤
    - `:quantity_range` - 数量范围过滤
    - `:cost_range` - 成本范围过滤
    - `:date_from` - 开始日期
    - `:date_to` - 结束日期
    - `:filters` - 其他过滤条件
    - `:sort` - 排序条件

  ## 返回
  - `{:ok, query}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_list_query(params) do
    Logger.debug("🔨 [股票查询构建器] 构建股票列表查询")
    
    try do
      query = Stock
      |> apply_user_filter(params[:user_id])
      |> apply_racer_filter(params[:racer_id])
      |> apply_quantity_range_filter(params[:quantity_range])
      |> apply_cost_range_filter(params[:cost_range])
      |> apply_date_range_filter(params[:date_from], params[:date_to])
      |> apply_filter_conditions(params[:filters])
      |> apply_sort_conditions(params[:sort])
      
      {:ok, query}
    rescue
      error ->
        Logger.error("❌ [股票查询构建器] 构建查询失败: #{inspect(error)}")
        {:error, "查询构建失败"}
    end
  end

  @doc """
  构建用户持仓查询

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - 查询对象
  """
  def build_user_stocks_query(user_id, options \\ []) do
    Logger.debug("👤 [股票查询构建器] 构建用户持仓查询: #{user_id}")
    
    Stock
    |> Ash.Query.filter(user_id == ^user_id)
    |> apply_racer_filter(options[:racer_id])
    |> apply_quantity_filter(options[:min_quantity])
    |> apply_date_range_filter(options[:date_from], options[:date_to])
    |> Ash.Query.sort(updated_at: :desc)
  end

  @doc """
  构建赛车手持仓查询

  ## 参数
  - `racer_id` - 赛车手ID
  - `options` - 选项

  ## 返回
  - 查询对象
  """
  def build_racer_holdings_query(racer_id, options \\ []) do
    Logger.debug("🏎️ [股票查询构建器] 构建赛车手持仓查询: #{racer_id}")
    
    Stock
    |> Ash.Query.filter(racer_id == ^racer_id)
    |> Ash.Query.filter(quantity > 0)
    |> apply_user_filter(options[:user_id])
    |> apply_quantity_filter(options[:min_quantity])
    |> Ash.Query.load([:user])
    |> Ash.Query.sort(quantity: :desc, updated_at: :desc)
  end

  @doc """
  构建统计查询

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, query}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_statistics_query(params \\ %{}) do
    Logger.debug("📊 [股票查询构建器] 构建统计查询")
    
    try do
      query = Stock
      |> apply_user_filter(params[:user_id])
      |> apply_racer_filter(params[:racer_id])
      |> apply_quantity_filter(params[:min_quantity])
      |> apply_date_range_filter(params[:date_from], params[:date_to])
      |> apply_filter_conditions(params[:filters])
      
      {:ok, query}
    rescue
      error ->
        Logger.error("❌ [股票查询构建器] 构建统计查询失败: #{inspect(error)}")
        {:error, "统计查询构建失败"}
    end
  end

  @doc """
  构建热门赛车手查询

  ## 参数
  - `options` - 选项

  ## 返回
  - 查询对象
  """
  def build_popular_racers_query(options \\ []) do
    Logger.debug("🔥 [股票查询构建器] 构建热门赛车手查询")
    
    query = Stock
    |> Ash.Query.filter(quantity > 0)
    |> apply_date_range_filter(options[:date_from], options[:date_to])
    
    # TODO: 添加按赛车手分组的聚合查询
    query
  end

  @doc """
  构建用户搜索查询

  ## 参数
  - `search_term` - 搜索词（用户名）
  - `options` - 选项

  ## 返回
  - 查询对象
  """
  def build_user_search_query(search_term, options \\ []) do
    Logger.debug("🔍 [股票查询构建器] 构建用户搜索查询: #{search_term}")
    
    # 首先查找匹配的用户
    matching_user_ids = get_matching_user_ids(search_term)
    
    if length(matching_user_ids) > 0 do
      Stock
      |> Ash.Query.filter(user_id in ^matching_user_ids)
      |> apply_racer_filter(options[:racer_id])
      |> apply_quantity_filter(options[:min_quantity])
      |> apply_date_range_filter(options[:date_from], options[:date_to])
      |> Ash.Query.load([:user])
      |> Ash.Query.sort(updated_at: :desc)
    else
      # 返回空结果查询
      Stock
      |> Ash.Query.filter(user_id == "no-match-found")
    end
  end

  @doc """
  构建大户持仓查询

  ## 参数
  - `min_quantity` - 最小持仓数量
  - `options` - 选项

  ## 返回
  - 查询对象
  """
  def build_large_holders_query(min_quantity, options \\ []) do
    Logger.debug("🐋 [股票查询构建器] 构建大户持仓查询: #{min_quantity}")
    
    Stock
    |> Ash.Query.filter(quantity >= ^min_quantity)
    |> apply_racer_filter(options[:racer_id])
    |> apply_user_filter(options[:user_id])
    |> Ash.Query.load([:user])
    |> Ash.Query.sort(quantity: :desc, total_cost: :desc)
  end

  # ============================================================================
  # 过滤条件应用
  # ============================================================================

  # 应用用户过滤
  defp apply_user_filter(query, nil), do: query
  defp apply_user_filter(query, user_id) when is_binary(user_id) do
    Ash.Query.filter(query, user_id == ^user_id)
  end
  defp apply_user_filter(query, user_ids) when is_list(user_ids) do
    Ash.Query.filter(query, user_id in ^user_ids)
  end

  # 应用赛车手过滤
  defp apply_racer_filter(query, nil), do: query
  defp apply_racer_filter(query, racer_id) when is_integer(racer_id) do
    Ash.Query.filter(query, racer_id == ^racer_id)
  end
  defp apply_racer_filter(query, racer_ids) when is_list(racer_ids) do
    Ash.Query.filter(query, racer_id in ^racer_ids)
  end

  # 应用数量过滤
  defp apply_quantity_filter(query, nil), do: query
  defp apply_quantity_filter(query, min_quantity) when is_integer(min_quantity) do
    Ash.Query.filter(query, quantity >= ^min_quantity)
  end

  # 应用数量范围过滤
  defp apply_quantity_range_filter(query, nil), do: query
  defp apply_quantity_range_filter(query, %{min: min_quantity, max: max_quantity}) do
    query = if min_quantity do
      Ash.Query.filter(query, quantity >= ^min_quantity)
    else
      query
    end
    
    if max_quantity do
      Ash.Query.filter(query, quantity <= ^max_quantity)
    else
      query
    end
  end
  defp apply_quantity_range_filter(query, %{min: min_quantity}) do
    apply_quantity_range_filter(query, %{min: min_quantity, max: nil})
  end
  defp apply_quantity_range_filter(query, %{max: max_quantity}) do
    apply_quantity_range_filter(query, %{min: nil, max: max_quantity})
  end

  # 应用成本范围过滤
  defp apply_cost_range_filter(query, nil), do: query
  defp apply_cost_range_filter(query, %{min: min_cost, max: max_cost}) do
    query = if min_cost do
      Ash.Query.filter(query, total_cost >= ^min_cost)
    else
      query
    end
    
    if max_cost do
      Ash.Query.filter(query, total_cost <= ^max_cost)
    else
      query
    end
  end
  defp apply_cost_range_filter(query, %{min: min_cost}) do
    apply_cost_range_filter(query, %{min: min_cost, max: nil})
  end
  defp apply_cost_range_filter(query, %{max: max_cost}) do
    apply_cost_range_filter(query, %{min: nil, max: max_cost})
  end

  # 应用日期范围过滤
  defp apply_date_range_filter(query, nil, nil), do: query
  defp apply_date_range_filter(query, from_date, nil) do
    Ash.Query.filter(query, updated_at >= ^from_date)
  end
  defp apply_date_range_filter(query, nil, to_date) do
    Ash.Query.filter(query, updated_at <= ^to_date)
  end
  defp apply_date_range_filter(query, from_date, to_date) do
    query
    |> Ash.Query.filter(updated_at >= ^from_date)
    |> Ash.Query.filter(updated_at <= ^to_date)
  end

  # 应用其他过滤条件
  defp apply_filter_conditions(query, nil), do: query
  defp apply_filter_conditions(query, filters) when map_size(filters) == 0, do: query
  defp apply_filter_conditions(query, filters) do
    Enum.reduce(filters, query, fn {field, value}, acc_query ->
      apply_single_filter(acc_query, field, value)
    end)
  end

  # 应用单个过滤条件
  defp apply_single_filter(query, field, value) when field in @filterable_fields do
    case {field, value} do
      {:quantity, quantity} when is_integer(quantity) ->
        Ash.Query.filter(query, quantity == ^quantity)
      
      {:total_cost, cost} when is_number(cost) ->
        Ash.Query.filter(query, total_cost == ^cost)
      
      {:updated_at, %{from: from_date, to: to_date}} ->
        apply_date_range_filter(query, from_date, to_date)
      
      {:inserted_at, %{from: from_date, to: to_date}} ->
        query
        |> Ash.Query.filter(inserted_at >= ^from_date)
        |> Ash.Query.filter(inserted_at <= ^to_date)
      
      _ ->
        Logger.warning("⚠️ [股票查询构建器] 忽略无效过滤条件: #{field} = #{inspect(value)}")
        query
    end
  end
  defp apply_single_filter(query, field, _value) do
    Logger.warning("⚠️ [股票查询构建器] 忽略不支持的过滤字段: #{field}")
    query
  end

  # ============================================================================
  # 排序条件应用
  # ============================================================================

  # 应用排序条件
  defp apply_sort_conditions(query, nil), do: Ash.Query.sort(query, updated_at: :desc)
  defp apply_sort_conditions(query, []), do: Ash.Query.sort(query, updated_at: :desc)
  defp apply_sort_conditions(query, sort_conditions) when is_list(sort_conditions) do
    validated_sorts = validate_sort_conditions(sort_conditions)
    
    case validated_sorts do
      [] -> Ash.Query.sort(query, updated_at: :desc)
      sorts -> Ash.Query.sort(query, sorts)
    end
  end
  defp apply_sort_conditions(query, sort_condition) do
    apply_sort_conditions(query, [sort_condition])
  end

  # 验证排序条件
  defp validate_sort_conditions(sort_conditions) do
    Enum.reduce(sort_conditions, [], fn sort_condition, acc ->
      case validate_single_sort_condition(sort_condition) do
        {:ok, validated_sort} -> [validated_sort | acc]
        :error -> acc
      end
    end)
    |> Enum.reverse()
  end

  # 验证单个排序条件
  defp validate_single_sort_condition({field, direction}) 
       when field in @sortable_fields and direction in [:asc, :desc] do
    {:ok, {field, direction}}
  end
  defp validate_single_sort_condition(field) when field in @sortable_fields do
    {:ok, {field, :asc}}
  end
  defp validate_single_sort_condition(sort_condition) do
    Logger.warning("⚠️ [股票查询构建器] 忽略无效排序条件: #{inspect(sort_condition)}")
    :error
  end

  # ============================================================================
  # 辅助函数
  # ============================================================================

  # 获取匹配的用户ID
  defp get_matching_user_ids(search_term) do
    try do
      case User
           |> Ash.Query.filter(contains(username, ^search_term))
           |> Ash.Query.select([:id])
           |> Ash.read() do
        {:ok, users} -> Enum.map(users, & &1.id)
        {:error, error} ->
          Logger.warning("⚠️ [股票查询构建器] 搜索用户失败: #{inspect(error)}")
          []
      end
    rescue
      error ->
        Logger.error("❌ [股票查询构建器] 搜索用户异常: #{inspect(error)}")
        []
    end
  end

  # ============================================================================
  # 查询优化
  # ============================================================================

  @doc """
  优化查询性能

  ## 参数
  - `query` - 查询对象
  - `options` - 优化选项

  ## 返回
  - 优化后的查询对象
  """
  def optimize_query(query, options \\ []) do
    Logger.debug("⚡ [股票查询构建器] 优化查询性能")
    
    query
    |> maybe_add_index_hints(options)
    |> maybe_limit_results(options)
    |> maybe_select_fields(options)
  end

  # 可能添加索引提示
  defp maybe_add_index_hints(query, _options) do
    # TODO: 根据查询条件添加索引提示
    query
  end

  # 可能限制结果数量
  defp maybe_limit_results(query, options) do
    case Keyword.get(options, :max_results) do
      nil -> query
      max_results when is_integer(max_results) and max_results > 0 ->
        Ash.Query.limit(query, max_results)
      _ -> query
    end
  end

  # 可能选择特定字段
  defp maybe_select_fields(query, options) do
    case Keyword.get(options, :select_fields) do
      nil -> query
      fields when is_list(fields) ->
        Ash.Query.select(query, fields)
      _ -> query
    end
  end
end
