defmodule RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.SignInActivityRepository do
  @moduledoc """
  🎯 签到活动仓储层
  
  负责签到活动相关的数据访问操作，包括：
  - 签到活动的CRUD操作
  - 活动状态管理（启用/禁用）
  - 活动时间范围查询
  - 奖励配置管理
  - 用户签到记录查询
  - 活动统计数据获取
  """

  require Logger
  alias Teen.ActivitySystem.SignInActivity
  alias Ash.Query

  # ==================== 基础CRUD操作 ====================

  @doc """
  创建签到活动
  
  ## 参数
  - `activity_data` - 活动数据
  - `options` - 选项参数
  
  ## 返回
  - `{:ok, activity}` - 创建成功
  - `{:error, reason}` - 创建失败
  """
  def create_sign_in_activity(activity_data, options \\ []) do
    Logger.info("🎯 [签到活动仓储] 创建签到活动: #{inspect(activity_data[:activity_name])}")
    
    try do
      result = SignInActivity
      |> Ash.Changeset.for_create(:create, activity_data)
      |> Ash.create()
      
      case result do
        {:ok, activity} ->
          Logger.info("✅ [签到活动仓储] 签到活动创建成功: #{activity.id}")
          maybe_load_associations({:ok, activity}, options)
          
        {:error, reason} ->
          Logger.error("❌ [签到活动仓储] 签到活动创建失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [签到活动仓储] 创建签到活动异常: #{inspect(exception)}")
        {:error, :create_failed}
    end
  end

  @doc """
  根据ID获取签到活动
  """
  def get_sign_in_activity(activity_id, options \\ []) do
    Logger.info("🎯 [签到活动仓储] 获取签到活动: #{activity_id}")
    
    try do
      result = SignInActivity
      |> Ash.Query.for_read(:read)
      |> Ash.Query.filter(id == ^activity_id)
      |> maybe_load_associations(options)
      |> Ash.read_one()
      
      case result do
        {:ok, nil} ->
          Logger.warn("⚠️ [签到活动仓储] 签到活动不存在: #{activity_id}")
          {:error, :not_found}
          
        {:ok, activity} ->
          Logger.info("✅ [签到活动仓储] 获取签到活动成功: #{activity.activity_name}")
          {:ok, activity}
          
        {:error, reason} ->
          Logger.error("❌ [签到活动仓储] 获取签到活动失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [签到活动仓储] 获取签到活动异常: #{inspect(exception)}")
        {:error, :query_failed}
    end
  end

  @doc """
  更新签到活动
  """
  def update_sign_in_activity(activity_id, update_data, options \\ []) do
    Logger.info("🎯 [签到活动仓储] 更新签到活动: #{activity_id}")
    
    try do
      with {:ok, activity} <- get_sign_in_activity(activity_id),
           {:ok, updated_activity} <- SignInActivity.update(activity, update_data) do
        Logger.info("✅ [签到活动仓储] 签到活动更新成功: #{updated_activity.activity_name}")
        maybe_load_associations({:ok, updated_activity}, options)
      else
        {:error, reason} ->
          Logger.error("❌ [签到活动仓储] 更新签到活动失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [签到活动仓储] 更新签到活动异常: #{inspect(exception)}")
        {:error, :update_failed}
    end
  end

  @doc """
  删除签到活动
  """
  def delete_sign_in_activity(activity_id) do
    Logger.info("🎯 [签到活动仓储] 删除签到活动: #{activity_id}")
    
    try do
      with {:ok, activity} <- get_sign_in_activity(activity_id),
           :ok <- SignInActivity.destroy(activity) do
        Logger.info("✅ [签到活动仓储] 签到活动删除成功: #{activity_id}")
        {:ok, :deleted}
      else
        {:error, reason} ->
          Logger.error("❌ [签到活动仓储] 删除签到活动失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [签到活动仓储] 删除签到活动异常: #{inspect(exception)}")
        {:error, :delete_failed}
    end
  end

  # ==================== 列表查询操作 ====================

  @doc """
  获取签到活动列表
  """
  def list_sign_in_activities(options \\ []) do
    Logger.info("🎯 [签到活动仓储] 获取签到活动列表")
    
    try do
      query = SignInActivity
      |> Ash.Query.for_read(:read)
      |> apply_list_filters(options)
      |> apply_sorting(options)
      |> maybe_load_associations(options)
      
      result = if options[:paginate] do
        Ash.read(query, page: build_page_params(options))
      else
        Ash.read(query)
      end
      
      case result do
        {:ok, activities} when is_list(activities) ->
          Logger.info("✅ [签到活动仓储] 获取签到活动列表成功: #{length(activities)}条")
          {:ok, activities}
          
        {:ok, %{results: activities, more?: more?, count: count}} ->
          Logger.info("✅ [签到活动仓储] 分页获取签到活动成功: #{length(activities)}条，总数: #{count}")
          {:ok, %{activities: activities, more?: more?, count: count}}
          
        {:error, reason} ->
          Logger.error("❌ [签到活动仓储] 获取签到活动列表失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [签到活动仓储] 获取签到活动列表异常: #{inspect(exception)}")
        {:error, :query_failed}
    end
  end

  @doc """
  获取启用的签到活动列表
  """
  def list_active_sign_in_activities(options \\ []) do
    Logger.info("🎯 [签到活动仓储] 获取启用的签到活动列表")
    
    try do
      result = SignInActivity
      |> Ash.Query.for_read(:list_active_activities)
      |> apply_sorting(options)
      |> maybe_load_associations(options)
      |> Ash.read()
      
      case result do
        {:ok, activities} ->
          Logger.info("✅ [签到活动仓储] 获取启用签到活动成功: #{length(activities)}条")
          {:ok, activities}
          
        {:error, reason} ->
          Logger.error("❌ [签到活动仓储] 获取启用签到活动失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [签到活动仓储] 获取启用签到活动异常: #{inspect(exception)}")
        {:error, :query_failed}
    end
  end

  @doc """
  获取当前进行中的签到活动列表
  """
  def list_current_sign_in_activities(options \\ []) do
    Logger.info("🎯 [签到活动仓储] 获取当前进行中的签到活动列表")
    
    try do
      result = SignInActivity
      |> Ash.Query.for_read(:list_current_activities)
      |> apply_sorting(options)
      |> maybe_load_associations(options)
      |> Ash.read()
      
      case result do
        {:ok, activities} ->
          Logger.info("✅ [签到活动仓储] 获取当前签到活动成功: #{length(activities)}条")
          {:ok, activities}
          
        {:error, reason} ->
          Logger.error("❌ [签到活动仓储] 获取当前签到活动失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [签到活动仓储] 获取当前签到活动异常: #{inspect(exception)}")
        {:error, :query_failed}
    end
  end

  # ==================== 状态管理操作 ====================

  @doc """
  启用签到活动
  """
  def enable_sign_in_activity(activity_id) do
    Logger.info("🎯 [签到活动仓储] 启用签到活动: #{activity_id}")
    
    try do
      with {:ok, activity} <- get_sign_in_activity(activity_id),
           {:ok, updated_activity} <- SignInActivity.enable(activity) do
        Logger.info("✅ [签到活动仓储] 签到活动启用成功: #{updated_activity.activity_name}")
        {:ok, updated_activity}
      else
        {:error, reason} ->
          Logger.error("❌ [签到活动仓储] 启用签到活动失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [签到活动仓储] 启用签到活动异常: #{inspect(exception)}")
        {:error, :enable_failed}
    end
  end

  @doc """
  禁用签到活动
  """
  def disable_sign_in_activity(activity_id) do
    Logger.info("🎯 [签到活动仓储] 禁用签到活动: #{activity_id}")
    
    try do
      with {:ok, activity} <- get_sign_in_activity(activity_id),
           {:ok, updated_activity} <- SignInActivity.disable(activity) do
        Logger.info("✅ [签到活动仓储] 签到活动禁用成功: #{updated_activity.activity_name}")
        {:ok, updated_activity}
      else
        {:error, reason} ->
          Logger.error("❌ [签到活动仓储] 禁用签到活动失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [签到活动仓储] 禁用签到活动异常: #{inspect(exception)}")
        {:error, :disable_failed}
    end
  end

  # ==================== 私有辅助函数 ====================

  # 应用列表过滤条件
  defp apply_list_filters(query, options) do
    query
    |> maybe_filter_by_name(options[:name])
    |> maybe_filter_by_status(options[:status])
    |> maybe_filter_by_date_range(options[:date_range])
  end

  # 按活动名称过滤
  defp maybe_filter_by_name(query, nil), do: query
  defp maybe_filter_by_name(query, name) when is_binary(name) do
    Ash.Query.filter(query, contains(activity_name, ^name))
  end

  # 按状态过滤
  defp maybe_filter_by_status(query, nil), do: query
  defp maybe_filter_by_status(query, status) when is_integer(status) do
    Ash.Query.filter(query, status == ^status)
  end

  # 按日期范围过滤
  defp maybe_filter_by_date_range(query, nil), do: query
  defp maybe_filter_by_date_range(query, {start_date, end_date}) do
    Ash.Query.filter(query, start_date >= ^start_date and end_date <= ^end_date)
  end

  # 应用排序
  defp apply_sorting(query, options) do
    case options[:sort] do
      nil -> Ash.Query.sort(query, inserted_at: :desc)
      sort_params -> Ash.Query.sort(query, sort_params)
    end
  end

  # 构建分页参数
  defp build_page_params(options) do
    %{
      limit: options[:limit] || 20,
      offset: options[:offset] || 0,
      count: true
    }
  end

  # 可能加载关联数据
  defp maybe_load_associations({:ok, data}, options) do
    case options[:load] do
      nil -> {:ok, data}
      associations -> 
        case Ash.load(data, associations) do
          {:ok, loaded_data} -> {:ok, loaded_data}
          {:error, reason} -> {:error, reason}
        end
    end
  end

  defp maybe_load_associations(query, options) do
    case options[:load] do
      nil -> query
      associations -> Ash.Query.load(query, associations)
    end
  end
end
