defmodule RacingGame.SystemCommunicationRead do
  @moduledoc """
  系统通信阅读记录资源
  
  记录用户对系统消息的阅读状态
  """
  
  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: RacingGame,
    extensions: [AshAdmin.Resource]

  postgres do
    table "system_communication_reads"
    repo Cypridina.Repo
  end

  admin do
    table_columns [:id, :communication_id, :user_id, :read_at, :inserted_at]
  end

  code_interface do
    define :create
    define :read
    define :list_by_user
    define :list_by_communication
    define :get_user_read_status
  end

  actions do
    defaults [:read]

    create :create do
      accept [:communication_id, :user_id, :read_at, :ip_address, :user_agent]
      
      change before_action(fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:read_at, DateTime.utc_now())
      end)
    end

    read :list_by_user do
      argument :user_id, :uuid, allow_nil?: false
      filter expr(user_id == ^arg(:user_id))
    end

    read :list_by_communication do
      argument :communication_id, :uuid, allow_nil?: false
      filter expr(communication_id == ^arg(:communication_id))
    end

    read :get_user_read_status do
      argument :user_id, :uuid, allow_nil?: false
      argument :communication_id, :uuid, allow_nil?: false
      
      filter expr(
        user_id == ^arg(:user_id) and 
        communication_id == ^arg(:communication_id)
      )
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :communication_id, :uuid do
      allow_nil? false
      public? true
      description "系统通信ID"
    end

    attribute :user_id, :uuid do
      allow_nil? false
      public? true
      description "用户ID"
    end

    attribute :read_at, :utc_datetime do
      allow_nil? false
      public? true
      description "阅读时间"
      default &DateTime.utc_now/0
    end

    attribute :ip_address, :string do
      allow_nil? true
      public? true
      description "IP地址"
      constraints max_length: 45
    end

    attribute :user_agent, :string do
      allow_nil? true
      public? true
      description "用户代理"
      constraints max_length: 500
    end

    timestamps()
  end

  relationships do
    belongs_to :communication, RacingGame.SystemCommunication do
      source_attribute :communication_id
      destination_attribute :id
    end

    belongs_to :user, Cypridina.Accounts.User do
      source_attribute :user_id
      destination_attribute :id
    end
  end

  identities do
    identity :unique_user_communication, [:user_id, :communication_id]
  end

  validations do
    validate present([:communication_id, :user_id, :read_at])
  end
end
