defmodule Cypridina.Ledger.Account do
  @moduledoc """
  账户资源

  支持不同类型的账户：
  - 用户账户：每个用户的积分账户
  - 系统账户：用于系统操作（奖励、费用等）
  """

  use Ash.Resource,
    domain: Cypridina.Ledger,
    data_layer: AshPostgres.DataLayer,
    extensions: [AshDoubleEntry.Account, AshAdmin.Resource]

  postgres do
    table "ledger_accounts"
    repo Cypridina.Repo
  end

  admin do
    table_columns [:id, :identifier, :account_type, :currency, :inserted_at]
  end

  account do
    # 配置相关资源
    transfer_resource Cypridina.Ledger.Transfer
    balance_resource Cypridina.Ledger.Balance
    # 在open action中接受自定义属性
    open_action_accept [:account_type, :user_id, :system_account_type, :description, :is_active]
  end

  code_interface do
    define :open, action: :open
    define :read
    define :get_by_identifier, action: :read, get_by_identity: :unique_identifier
    define :by_user_id
    define :by_system_type
    define :activate
    define :deactivate
  end

  # 账户类型枚举
  @account_types [:user, :system, :game]
  @system_account_types [:main, :rewards, :fees, :commission, :refund]

  attributes do
    # ash_double_entry会自动添加：id, currency, inserted_at, identifier

    # 账户类型
    attribute :account_type, :atom do
      allow_nil? false
      constraints one_of: @account_types
      description "账户类型：user(用户), system(系统), game(游戏)"
    end

    # 关联的用户ID（仅用户账户）
    attribute :user_id, :uuid do
      allow_nil? true
      description "关联的用户ID，仅用户账户使用"
    end

    # 系统账户子类型（仅系统账户）
    attribute :system_account_type, :atom do
      allow_nil? true
      constraints one_of: @system_account_types
      description "系统账户子类型：main(主账户), rewards(奖励), fees(费用), commission(佣金), refund(退款)"
    end

    # 账户描述
    attribute :description, :string do
      allow_nil? true
      description "账户描述"
    end

    # 是否激活
    attribute :is_active, :boolean do
      allow_nil? false
      default true
      description "账户是否激活"
    end
  end

  relationships do
    # 关联到用户（仅用户账户）
    belongs_to :user, Cypridina.Accounts.User do
      public? true
      source_attribute :user_id
      destination_attribute :id
      allow_nil? true
    end
  end

  # 简化验证，避免复杂的where条件
  # 这些验证可以在应用层实现

  actions do
    # 将open action设置为主要创建action
    create :create do
      primary? true
      # 委托给ash_double_entry的open action
      manual fn changeset, _context ->
        attrs = changeset.attributes
        Ash.create(__MODULE__, attrs, action: :open)
      end
    end

    # 自定义读取操作
    read :read do
      primary? true
      pagination offset?: true, keyset?: true, countable: true
    end

    # 按用户ID查找账户
    read :by_user_id do
      argument :user_id, :uuid, allow_nil?: false
      filter account_type: :user, user_id: arg(:user_id)
      get? true
    end

    # 按系统账户类型查找
    read :by_system_type do
      argument :system_account_type, :atom, allow_nil?: false
      filter account_type: :system, system_account_type: arg(:system_account_type)
      get? true
    end

    # 激活账户
    update :activate do
      change set_attribute(:is_active, true)
    end

    # 停用账户
    update :deactivate do
      change set_attribute(:is_active, false)
    end
  end

  # 简化calculations，避免复杂的expr语法
  # 这些可以在应用层实现

  preparations do
    # 默认只显示激活的账户
    prepare build(filter: [is_active: true])
  end
end
