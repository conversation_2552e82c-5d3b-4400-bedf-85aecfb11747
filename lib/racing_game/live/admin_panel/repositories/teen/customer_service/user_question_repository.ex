defmodule RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.UserQuestionRepository do
  @moduledoc """
  用户问题数据访问仓储

  提供用户问题相关的数据访问抽象：
  - 问题管理
  - 状态跟踪
  - 分配处理
  - 问题统计
  """

  require Logger
  alias Teen.CustomerService.UserQuestion
  require Ash.Query

  # 常量定义
  @default_limit 20
  @max_limit 100
  @cache_ttl 300  # 5分钟缓存

  # ============================================================================
  # 问题管理
  # ============================================================================

  @doc """
  根据ID获取问题

  ## 参数
  - `question_id` - 问题ID
  - `options` - 选项

  ## 返回
  - `{:ok, question}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_question_by_id(question_id, options \\ []) do
    Logger.debug("❓ [用户问题仓储] 获取问题: #{question_id}")

    cache_key = build_cache_key("question", question_id, options[:preload])

    case get_from_cache(cache_key) do
      :miss ->
        fetch_and_cache_question_by_id(question_id, options[:preload] || [], cache_key)
      {:hit, question} ->
        Logger.debug("💾 [用户问题仓储] 缓存命中: #{question_id}")
        {:ok, question}
    end
  end

  @doc """
  创建问题

  ## 参数
  - `question_data` - 问题数据

  ## 返回
  - `{:ok, question}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_question(question_data) do
    Logger.info("📝 [用户问题仓储] 创建问题: 用户#{question_data[:user_id]}")

    try do
      case UserQuestion.create(question_data) do
        {:ok, question} ->
          clear_cache(:question, nil)
          Logger.info("✅ [用户问题仓储] 问题创建成功: #{question.id}")
          {:ok, question}
        {:error, error} ->
          Logger.error("❌ [用户问题仓储] 创建问题失败: #{inspect(error)}")
          {:error, :create_failed}
      end
    rescue
      error ->
        Logger.error("❌ [用户问题仓储] 创建问题异常: #{inspect(error)}")
        {:error, :create_error}
    end
  end

  @doc """
  更新问题

  ## 参数
  - `question_id` - 问题ID
  - `update_data` - 更新数据

  ## 返回
  - `{:ok, question}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_question(question_id, update_data) do
    Logger.info("📝 [用户问题仓储] 更新问题: #{question_id}")

    with {:ok, question} <- get_question_by_id(question_id),
         {:ok, updated_question} <- do_update_question(question, update_data) do

      clear_cache(:question, question_id)
      Logger.info("✅ [用户问题仓储] 问题更新成功: #{question_id}")
      {:ok, updated_question}
    else
      error -> error
    end
  end

  @doc """
  分配问题给客服

  ## 参数
  - `question_id` - 问题ID
  - `staff_id` - 客服ID

  ## 返回
  - `{:ok, question}` - 成功
  - `{:error, reason}` - 失败
  """
  def assign_to_staff(question_id, staff_id) do
    Logger.info("👨‍💼 [用户问题仓储] 分配问题给客服: #{question_id} -> #{staff_id}")

    with {:ok, question} <- get_question_by_id(question_id),
         {:ok, assigned_question} <- do_assign_to_staff(question, staff_id) do

      clear_cache(:question, question_id)
      Logger.info("✅ [用户问题仓储] 问题分配成功: #{question_id}")
      {:ok, assigned_question}
    else
      error -> error
    end
  end

  @doc """
  标记问题为已完成

  ## 参数
  - `question_id` - 问题ID

  ## 返回
  - `{:ok, question}` - 成功
  - `{:error, reason}` - 失败
  """
  def mark_completed(question_id) do
    Logger.info("✅ [用户问题仓储] 标记问题完成: #{question_id}")

    with {:ok, question} <- get_question_by_id(question_id),
         {:ok, completed_question} <- do_mark_completed(question) do

      clear_cache(:question, question_id)
      Logger.info("✅ [用户问题仓储] 问题标记完成成功: #{question_id}")
      {:ok, completed_question}
    else
      error -> error
    end
  end

  # ============================================================================
  # 问题查询
  # ============================================================================

  @doc """
  分页获取问题列表

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, %{items: questions, page_info: page_info}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_questions_paginated(params \\ %{}) do
    Logger.debug("📋 [用户问题仓储] 分页获取问题列表")

    normalized_params = normalize_list_params(params)
    cache_key = build_cache_key("questions_list", :paginated, normalized_params)

    case get_from_cache(cache_key) do
      :miss ->
        fetch_and_cache_questions_list(normalized_params, cache_key)
      {:hit, result} ->
        Logger.debug("💾 [用户问题仓储] 缓存命中: 问题列表")
        {:ok, result}
    end
  end

  @doc """
  获取用户问题

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - `{:ok, questions}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_questions(user_id, options \\ []) do
    Logger.debug("👤 [用户问题仓储] 获取用户问题: #{user_id}")

    try do
      query = UserQuestion
      |> Ash.Query.filter(user_id == ^user_id)
      |> maybe_apply_status_filter(options[:status])
      |> maybe_apply_type_filter(options[:question_type])
      |> maybe_apply_priority_filter(options[:priority])
      |> apply_sort(options[:sort] || [inserted_at: :desc])
      |> maybe_limit(options[:limit])

      case Ash.read(query) do
        {:ok, questions} -> {:ok, questions}
        {:error, error} ->
          Logger.error("❌ [用户问题仓储] 获取用户问题失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [用户问题仓储] 获取用户问题异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  获取待处理问题

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, questions}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_pending_questions(options \\ []) do
    Logger.debug("⏳ [用户问题仓储] 获取待处理问题")

    cache_key = build_cache_key("pending_questions", :list, options)

    case get_from_cache(cache_key) do
      :miss ->
        fetch_and_cache_pending_questions(options, cache_key)
      {:hit, questions} ->
        Logger.debug("💾 [用户问题仓储] 缓存命中: 待处理问题")
        {:ok, questions}
    end
  end

  @doc """
  获取客服分配的问题

  ## 参数
  - `staff_id` - 客服ID
  - `options` - 选项

  ## 返回
  - `{:ok, questions}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_staff_questions(staff_id, options \\ []) do
    Logger.debug("👨‍💼 [用户问题仓储] 获取客服分配问题: #{staff_id}")

    try do
      query = UserQuestion
      |> Ash.Query.filter(assigned_staff_id == ^staff_id)
      |> maybe_apply_status_filter(options[:status])
      |> maybe_apply_type_filter(options[:question_type])
      |> apply_sort(options[:sort] || [inserted_at: :desc])
      |> maybe_limit(options[:limit])

      case Ash.read(query) do
        {:ok, questions} -> {:ok, questions}
        {:error, error} ->
          Logger.error("❌ [用户问题仓储] 获取客服问题失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [用户问题仓储] 获取客服问题异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # ============================================================================
  # 统计查询
  # ============================================================================

  @doc """
  获取问题统计

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, statistics}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_question_statistics(options \\ []) do
    Logger.debug("📊 [用户问题仓储] 获取问题统计")

    try do
      stats = %{
        total_questions: get_total_questions(),
        pending_questions: get_pending_questions_count(),
        processing_questions: get_processing_questions_count(),
        completed_questions: get_completed_questions_count(),
        recent_questions: get_recent_questions_count(options[:days] || 7),
        questions_by_type: get_questions_by_type(),
        questions_by_priority: get_questions_by_priority(),
        average_resolution_time: get_average_resolution_time()
      }

      {:ok, stats}
    rescue
      error ->
        Logger.error("❌ [用户问题仓储] 获取统计异常: #{inspect(error)}")
        {:error, :statistics_error}
    end
  end

  @doc """
  获取客服工作统计

  ## 参数
  - `staff_id` - 客服ID
  - `options` - 选项

  ## 返回
  - `{:ok, statistics}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_staff_statistics(staff_id, options \\ []) do
    Logger.debug("👨‍💼 [用户问题仓储] 获取客服工作统计: #{staff_id}")

    try do
      stats = %{
        total_assigned: get_staff_assigned_count(staff_id),
        completed_count: get_staff_completed_count(staff_id),
        recent_completed: get_staff_recent_completed_count(staff_id, options[:days] || 7),
        average_resolution_time: get_staff_average_resolution_time(staff_id),
        completion_rate: get_staff_completion_rate(staff_id)
      }

      {:ok, stats}
    rescue
      error ->
        Logger.error("❌ [用户问题仓储] 获取客服统计异常: #{inspect(error)}")
        {:error, :statistics_error}
    end
  end

  # ============================================================================
  # 缓存管理
  # ============================================================================

  @doc """
  清除问题相关缓存

  ## 参数
  - `cache_type` - 缓存类型
  - `id` - ID（可选）
  """
  def clear_cache(cache_type, id \\ nil) do
    cache_patterns = case {cache_type, id} do
      {:question, nil} -> ["question:*"]
      {:question, question_id} -> ["question:#{question_id}:*"]
      {:user_questions, user_id} -> ["user_questions:#{user_id}:*"]
      {:pending_questions, _} -> ["pending_questions:*"]
      {:all, _} -> ["question:*", "user_questions:*", "pending_questions:*", "questions_list:*"]
    end

    Logger.debug("🧹 [用户问题仓储] 清除缓存: #{inspect(cache_patterns)}")
    Enum.each(cache_patterns, &clear_cache_pattern/1)
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 标准化列表参数
  defp normalize_list_params(params) do
    %{
      page: max(1, params[:page] || 1),
      limit: min(params[:limit] || @default_limit, @max_limit),
      search: String.trim(params[:search] || ""),
      filters: params[:filters] || %{},
      sort: params[:sort] || [inserted_at: :desc],
      preload: params[:preload] || []
    }
  end

  # 获取并缓存问题
  defp fetch_and_cache_question_by_id(question_id, preload, cache_key) do
    case fetch_question_by_id(question_id, preload) do
      {:ok, question} ->
        cache_put(cache_key, question, @cache_ttl)
        {:ok, question}
      error -> error
    end
  end

  # 获取问题
  defp fetch_question_by_id(question_id, preload) do
    try do
      case UserQuestion
           |> maybe_preload(preload)
           |> Ash.get(question_id) do
        {:ok, question} -> {:ok, question}
        {:error, %Ash.Error.Query.NotFound{}} -> {:error, :not_found}
        {:error, error} ->
          Logger.error("❌ [用户问题仓储] 获取问题失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [用户问题仓储] 获取问题异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # 执行更新问题
  defp do_update_question(question, update_data) do
    try do
      case question
           |> Ash.Changeset.for_update(:update, update_data)
           |> Ash.update() do
        {:ok, updated_question} -> {:ok, updated_question}
        {:error, error} ->
          Logger.error("❌ [用户问题仓储] 更新问题失败: #{inspect(error)}")
          {:error, :update_failed}
      end
    rescue
      error ->
        Logger.error("❌ [用户问题仓储] 更新问题异常: #{inspect(error)}")
        {:error, :update_error}
    end
  end

  # 执行分配给客服
  defp do_assign_to_staff(question, staff_id) do
    try do
      case question
           |> Ash.Changeset.for_update(:assign_to_staff, %{staff_id: staff_id})
           |> Ash.update() do
        {:ok, assigned_question} -> {:ok, assigned_question}
        {:error, error} ->
          Logger.error("❌ [用户问题仓储] 分配问题失败: #{inspect(error)}")
          {:error, :assign_failed}
      end
    rescue
      error ->
        Logger.error("❌ [用户问题仓储] 分配问题异常: #{inspect(error)}")
        {:error, :assign_error}
    end
  end

  # 执行标记完成
  defp do_mark_completed(question) do
    try do
      case question
           |> Ash.Changeset.for_update(:mark_completed, %{})
           |> Ash.update() do
        {:ok, completed_question} -> {:ok, completed_question}
        {:error, error} ->
          Logger.error("❌ [用户问题仓储] 标记完成失败: #{inspect(error)}")
          {:error, :mark_completed_failed}
      end
    rescue
      error ->
        Logger.error("❌ [用户问题仓储] 标记完成异常: #{inspect(error)}")
        {:error, :mark_completed_error}
    end
  end

  # 获取并缓存问题列表
  defp fetch_and_cache_questions_list(params, cache_key) do
    case fetch_questions_list(params) do
      {:ok, result} ->
        cache_put(cache_key, result, @cache_ttl)
        {:ok, result}
      error -> error
    end
  end

  # 获取问题列表
  defp fetch_questions_list(params) do
    try do
      query = UserQuestion
      |> apply_question_filters(params.filters)
      |> apply_question_search(params.search)
      |> apply_sort(params.sort)

      execute_paginated_query(query, params)
    rescue
      error ->
        Logger.error("❌ [用户问题仓储] 获取问题列表异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # 获取并缓存待处理问题
  defp fetch_and_cache_pending_questions(options, cache_key) do
    case fetch_pending_questions(options) do
      {:ok, questions} ->
        cache_put(cache_key, questions, @cache_ttl)
        {:ok, questions}
      error -> error
    end
  end

  # 获取待处理问题
  defp fetch_pending_questions(options) do
    try do
      query = UserQuestion
      |> Ash.Query.filter(status == 0)
      |> maybe_apply_type_filter(options[:question_type])
      |> maybe_apply_priority_filter(options[:priority])
      |> apply_sort(options[:sort] || [priority: :desc, inserted_at: :asc])
      |> maybe_limit(options[:limit])

      case Ash.read(query) do
        {:ok, questions} -> {:ok, questions}
        {:error, error} ->
          Logger.error("❌ [用户问题仓储] 获取待处理问题失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [用户问题仓储] 获取待处理问题异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # 应用问题过滤器
  defp apply_question_filters(query, filters) when map_size(filters) == 0, do: query
  defp apply_question_filters(query, filters) do
    Enum.reduce(filters, query, fn {key, value}, acc_query ->
      apply_question_filter(acc_query, key, value)
    end)
  end

  # 应用单个问题过滤器
  defp apply_question_filter(query, :status, status) when not is_nil(status) do
    Ash.Query.filter(query, status == ^status)
  end
  defp apply_question_filter(query, :question_type, question_type) when not is_nil(question_type) do
    Ash.Query.filter(query, question_type == ^question_type)
  end
  defp apply_question_filter(query, :priority, priority) when not is_nil(priority) do
    Ash.Query.filter(query, priority == ^priority)
  end
  defp apply_question_filter(query, :user_id, user_id) when not is_nil(user_id) do
    Ash.Query.filter(query, user_id == ^user_id)
  end
  defp apply_question_filter(query, :assigned_staff_id, staff_id) when not is_nil(staff_id) do
    Ash.Query.filter(query, assigned_staff_id == ^staff_id)
  end
  defp apply_question_filter(query, _key, _value), do: query

  # 应用问题搜索
  defp apply_question_search(query, search) when search == "" or is_nil(search), do: query
  defp apply_question_search(query, search) do
    search_term = "%#{search}%"
    Ash.Query.filter(query, ilike(title, ^search_term) or ilike(content, ^search_term))
  end

  # 执行分页查询
  defp execute_paginated_query(query, params) do
    offset = (params.page - 1) * params.limit

    case query
         |> maybe_preload(params.preload)
         |> Ash.Query.page(count: true, limit: params.limit, offset: offset)
         |> Ash.read() do
      {:ok, %Ash.Page.Offset{results: items, count: total_count}} ->
        page_info = %{
          total_count: total_count,
          page: params.page,
          limit: params.limit,
          total_pages: ceil(total_count / params.limit)
        }
        {:ok, %{items: items, page_info: page_info}}
      {:error, error} ->
        Logger.error("❌ [用户问题仓储] 分页查询失败: #{inspect(error)}")
        {:error, :query_failed}
    end
  end

  # 统计函数
  defp get_total_questions do
    case UserQuestion |> Ash.Query.aggregate(:count, :id) |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_pending_questions_count do
    case UserQuestion
         |> Ash.Query.filter(status == 0)
         |> Ash.Query.aggregate(:count, :id)
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_processing_questions_count do
    case UserQuestion
         |> Ash.Query.filter(status == 1)
         |> Ash.Query.aggregate(:count, :id)
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_completed_questions_count do
    case UserQuestion
         |> Ash.Query.filter(status == 2)
         |> Ash.Query.aggregate(:count, :id)
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_recent_questions_count(days) do
    date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)

    case UserQuestion
         |> Ash.Query.filter(inserted_at >= ^date_threshold)
         |> Ash.Query.aggregate(:count, :id)
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_questions_by_type do
    # 这里需要根据实际需求实现类型分组统计
    %{}
  end

  defp get_questions_by_priority do
    # 这里需要根据实际需求实现优先级分组统计
    %{}
  end

  defp get_average_resolution_time do
    # 这里需要根据实际需求计算平均解决时间
    0
  end

  defp get_staff_assigned_count(staff_id) do
    case UserQuestion
         |> Ash.Query.filter(assigned_staff_id == ^staff_id)
         |> Ash.Query.aggregate(:count, :id)
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_staff_completed_count(staff_id) do
    case UserQuestion
         |> Ash.Query.filter(assigned_staff_id == ^staff_id and status == 2)
         |> Ash.Query.aggregate(:count, :id)
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_staff_recent_completed_count(staff_id, days) do
    date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)

    case UserQuestion
         |> Ash.Query.filter(assigned_staff_id == ^staff_id and status == 2 and completed_at >= ^date_threshold)
         |> Ash.Query.aggregate(:count, :id)
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_staff_average_resolution_time(_staff_id) do
    # 这里需要根据实际需求计算客服平均解决时间
    0
  end

  defp get_staff_completion_rate(staff_id) do
    assigned = get_staff_assigned_count(staff_id)
    completed = get_staff_completed_count(staff_id)

    if assigned > 0 do
      completed / assigned * 100
    else
      0.0
    end
  end

  # 可能预加载关联数据
  defp maybe_preload(query, []), do: query
  defp maybe_preload(query, preload) when is_list(preload) do
    Ash.Query.load(query, preload)
  end

  # 可能限制数量
  defp maybe_limit(query, nil), do: query
  defp maybe_limit(query, limit) when is_integer(limit) and limit > 0 do
    Ash.Query.limit(query, limit)
  end
  defp maybe_limit(query, _), do: query

  # 应用排序
  defp apply_sort(query, sort) when is_list(sort) do
    Ash.Query.sort(query, sort)
  end
  defp apply_sort(query, _), do: query

  # 可能应用状态过滤
  defp maybe_apply_status_filter(query, nil), do: query
  defp maybe_apply_status_filter(query, status) do
    Ash.Query.filter(query, status == ^status)
  end

  # 可能应用类型过滤
  defp maybe_apply_type_filter(query, nil), do: query
  defp maybe_apply_type_filter(query, question_type) do
    Ash.Query.filter(query, question_type == ^question_type)
  end

  # 可能应用优先级过滤
  defp maybe_apply_priority_filter(query, nil), do: query
  defp maybe_apply_priority_filter(query, priority) do
    Ash.Query.filter(query, priority == ^priority)
  end

  # 构建缓存键
  defp build_cache_key(prefix, id, extra \\ nil) do
    case extra do
      nil -> "#{prefix}:#{id}"
      extra -> "#{prefix}:#{id}:#{:erlang.phash2(extra)}"
    end
  end

  # 缓存操作（需要实现具体的缓存后端）
  defp get_from_cache(_key), do: :miss
  defp cache_put(_key, _value, _ttl), do: :ok
  defp clear_cache_pattern(_pattern), do: :ok
end
