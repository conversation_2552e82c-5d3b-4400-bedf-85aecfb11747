/**
 * Racing Game Admin Panel - 智能反馈系统
 * 
 * 提供加载状态、用户操作反馈、错误提示和成功确认的统一管理
 */

/**
 * 反馈系统核心类
 */
class FeedbackSystem {
  constructor(options = {}) {
    this.options = {
      defaultDuration: 3000,
      maxConcurrentNotifications: 5,
      enableSound: false,
      enableVibration: true,
      position: 'top-right',
      enableAutoHide: true,
      enableProgressBar: true,
      ...options
    };

    this.notifications = new Map();
    this.loadingStates = new Map();
    this.notificationQueue = [];
    this.soundEnabled = this.options.enableSound && 'Audio' in window;
    
    this.init();
  }

  init() {
    this.createNotificationContainer();
    this.setupGlobalLoadingIndicator();
    this.setupKeyboardShortcuts();
    this.setupSounds();
  }

  /**
   * 创建通知容器
   */
  createNotificationContainer() {
    this.container = document.createElement('div');
    this.container.id = 'feedback-notifications';
    this.container.className = `notification-container position-${this.options.position}`;
    document.body.appendChild(this.container);
  }

  /**
   * 设置全局加载指示器
   */
  setupGlobalLoadingIndicator() {
    this.globalLoader = document.createElement('div');
    this.globalLoader.id = 'global-loading-indicator';
    this.globalLoader.className = 'global-loader hidden';
    this.globalLoader.innerHTML = `
      <div class="loader-backdrop"></div>
      <div class="loader-content">
        <div class="loading-spinner"></div>
        <div class="loader-text">处理中...</div>
        <div class="loader-progress">
          <div class="progress-bar"></div>
        </div>
      </div>
    `;
    document.body.appendChild(this.globalLoader);
  }

  /**
   * 设置键盘快捷键
   */
  setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
      // Esc键关闭所有通知
      if (e.key === 'Escape') {
        this.clearAllNotifications();
      }
    });
  }

  /**
   * 设置声音效果
   */
  setupSounds() {
    if (!this.soundEnabled) return;

    this.sounds = {
      success: new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBTuR2O/Eeyw='),
      error: new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm1QIBAAAAABAAEAQBwAAEAcAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBTuR2O/Eeyw='),
      warning: new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBTuR2O/Eeyw='),
      info: new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBTuR2O/Eeyw=')
    };
  }

  /**
   * 显示通知
   */
  showNotification(type, message, options = {}) {
    const config = {
      duration: this.options.defaultDuration,
      closable: true,
      actions: [],
      icon: this.getDefaultIcon(type),
      ...options
    };

    // 检查并发限制
    if (this.notifications.size >= this.options.maxConcurrentNotifications) {
      this.queueNotification(type, message, config);
      return;
    }

    const notification = this.createNotification(type, message, config);
    const id = this.generateId();
    
    this.notifications.set(id, notification);
    this.container.appendChild(notification.element);

    // 播放声音
    this.playSound(type);

    // 触发振动
    this.triggerVibration(type);

    // 显示动画
    requestAnimationFrame(() => {
      notification.element.classList.add('notification-show');
    });

    // 自动隐藏
    if (config.duration > 0 && this.options.enableAutoHide) {
      notification.timer = setTimeout(() => {
        this.hideNotification(id);
      }, config.duration);
    }

    return id;
  }

  /**
   * 创建通知元素
   */
  createNotification(type, message, config) {
    const element = document.createElement('div');
    element.className = `notification notification-${type}`;
    
    const progressBar = this.options.enableProgressBar && config.duration > 0 ? 
      `<div class="notification-progress">
         <div class="progress-bar" style="animation-duration: ${config.duration}ms"></div>
       </div>` : '';

    const actions = config.actions.length > 0 ? 
      `<div class="notification-actions">
         ${config.actions.map(action => 
           `<button class="notification-action" data-action="${action.id}">${action.label}</button>`
         ).join('')}
       </div>` : '';

    const closeButton = config.closable ? 
      `<button class="notification-close" aria-label="关闭">×</button>` : '';

    element.innerHTML = `
      <div class="notification-content">
        <div class="notification-icon">${config.icon}</div>
        <div class="notification-body">
          <div class="notification-message">${message}</div>
          ${actions}
        </div>
        ${closeButton}
      </div>
      ${progressBar}
    `;

    // 绑定事件
    this.bindNotificationEvents(element, config);

    return { element, config };
  }

  /**
   * 绑定通知事件
   */
  bindNotificationEvents(element, config) {
    // 关闭按钮
    const closeBtn = element.querySelector('.notification-close');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        const id = this.findNotificationId(element);
        if (id) this.hideNotification(id);
      });
    }

    // 操作按钮
    const actionBtns = element.querySelectorAll('.notification-action');
    actionBtns.forEach(btn => {
      btn.addEventListener('click', (e) => {
        const actionId = e.target.dataset.action;
        const action = config.actions.find(a => a.id === actionId);
        if (action && action.handler) {
          action.handler();
        }
        
        if (action && action.closeOnClick !== false) {
          const id = this.findNotificationId(element);
          if (id) this.hideNotification(id);
        }
      });
    });

    // 悬停暂停自动隐藏
    element.addEventListener('mouseenter', () => {
      const id = this.findNotificationId(element);
      const notification = this.notifications.get(id);
      if (notification && notification.timer) {
        clearTimeout(notification.timer);
      }
    });

    element.addEventListener('mouseleave', () => {
      const id = this.findNotificationId(element);
      const notification = this.notifications.get(id);
      if (notification && notification.config.duration > 0 && this.options.enableAutoHide) {
        notification.timer = setTimeout(() => {
          this.hideNotification(id);
        }, 1000); // 延迟1秒后继续自动隐藏
      }
    });
  }

  /**
   * 隐藏通知
   */
  hideNotification(id) {
    const notification = this.notifications.get(id);
    if (!notification) return;

    notification.element.classList.add('notification-hide');
    
    setTimeout(() => {
      if (notification.timer) {
        clearTimeout(notification.timer);
      }
      
      if (notification.element.parentNode) {
        notification.element.parentNode.removeChild(notification.element);
      }
      
      this.notifications.delete(id);
      this.processNotificationQueue();
    }, 300);
  }

  /**
   * 队列通知
   */
  queueNotification(type, message, config) {
    this.notificationQueue.push({ type, message, config });
  }

  /**
   * 处理通知队列
   */
  processNotificationQueue() {
    if (this.notificationQueue.length === 0) return;
    if (this.notifications.size >= this.options.maxConcurrentNotifications) return;

    const { type, message, config } = this.notificationQueue.shift();
    this.showNotification(type, message, config);
  }

  /**
   * 清除所有通知
   */
  clearAllNotifications() {
    for (const [id] of this.notifications) {
      this.hideNotification(id);
    }
    this.notificationQueue = [];
  }

  /**
   * 显示加载状态
   */
  showLoading(target, options = {}) {
    const config = {
      text: '加载中...',
      spinner: 'default',
      overlay: true,
      disableInteraction: true,
      ...options
    };

    const id = this.generateId();
    const targetElement = typeof target === 'string' ? document.querySelector(target) : target;
    
    if (!targetElement) return null;

    const loader = this.createLoader(config);
    const loadingState = {
      id,
      target: targetElement,
      loader,
      config,
      originalState: {
        position: targetElement.style.position,
        pointerEvents: targetElement.style.pointerEvents
      }
    };

    // 设置目标元素样式
    if (config.overlay) {
      if (getComputedStyle(targetElement).position === 'static') {
        targetElement.style.position = 'relative';
      }
    }

    if (config.disableInteraction) {
      targetElement.style.pointerEvents = 'none';
    }

    // 添加加载器
    targetElement.appendChild(loader);
    this.loadingStates.set(id, loadingState);

    // 显示动画
    requestAnimationFrame(() => {
      loader.classList.add('loader-show');
    });

    return id;
  }

  /**
   * 创建加载器
   */
  createLoader(config) {
    const loader = document.createElement('div');
    loader.className = `loading-overlay ${config.overlay ? 'overlay-mode' : 'inline-mode'}`;
    
    const spinnerHtml = this.getSpinnerHtml(config.spinner);
    
    loader.innerHTML = `
      <div class="loading-content">
        ${spinnerHtml}
        <div class="loading-text">${config.text}</div>
      </div>
    `;

    return loader;
  }

  /**
   * 获取加载器HTML
   */
  getSpinnerHtml(type) {
    const spinners = {
      default: '<div class="loading-spinner"></div>',
      dots: '<div class="loading-dots"><span></span><span></span><span></span></div>',
      wave: '<div class="loading-wave"><span></span><span></span><span></span><span></span><span></span></div>',
      pulse: '<div class="loading-pulse"></div>',
      bars: '<div class="loading-bars"><span></span><span></span><span></span><span></span></span></div>'
    };

    return spinners[type] || spinners.default;
  }

  /**
   * 隐藏加载状态
   */
  hideLoading(id) {
    const loadingState = this.loadingStates.get(id);
    if (!loadingState) return;

    const { target, loader, originalState } = loadingState;

    loader.classList.add('loader-hide');
    
    setTimeout(() => {
      // 恢复目标元素状态
      target.style.position = originalState.position;
      target.style.pointerEvents = originalState.pointerEvents;
      
      // 移除加载器
      if (loader.parentNode) {
        loader.parentNode.removeChild(loader);
      }
      
      this.loadingStates.delete(id);
    }, 300);
  }

  /**
   * 显示全局加载
   */
  showGlobalLoading(text = '处理中...', progress = null) {
    const textElement = this.globalLoader.querySelector('.loader-text');
    const progressBar = this.globalLoader.querySelector('.progress-bar');
    
    textElement.textContent = text;
    
    if (progress !== null) {
      progressBar.style.width = `${Math.min(100, Math.max(0, progress))}%`;
      progressBar.parentElement.style.display = 'block';
    } else {
      progressBar.parentElement.style.display = 'none';
    }
    
    this.globalLoader.classList.remove('hidden');
    
    requestAnimationFrame(() => {
      this.globalLoader.classList.add('show');
    });
  }

  /**
   * 隐藏全局加载
   */
  hideGlobalLoading() {
    this.globalLoader.classList.remove('show');
    
    setTimeout(() => {
      this.globalLoader.classList.add('hidden');
    }, 300);
  }

  /**
   * 便捷方法
   */
  success(message, options = {}) {
    return this.showNotification('success', message, options);
  }

  error(message, options = {}) {
    return this.showNotification('error', { ...options, duration: 5000 }, message);
  }

  warning(message, options = {}) {
    return this.showNotification('warning', message, options);
  }

  info(message, options = {}) {
    return this.showNotification('info', message, options);
  }

  /**
   * 工具方法
   */
  getDefaultIcon(type) {
    const icons = {
      success: '✅',
      error: '❌',
      warning: '⚠️',
      info: 'ℹ️'
    };
    return icons[type] || 'ℹ️';
  }

  playSound(type) {
    if (!this.soundEnabled || !this.sounds[type]) return;
    
    try {
      this.sounds[type].currentTime = 0;
      this.sounds[type].play().catch(() => {
        // 忽略播放失败
      });
    } catch (e) {
      // 忽略音频错误
    }
  }

  triggerVibration(type) {
    if (!this.options.enableVibration || !navigator.vibrate) return;
    
    const patterns = {
      success: [100],
      error: [100, 50, 100],
      warning: [150],
      info: [50]
    };
    
    navigator.vibrate(patterns[type] || [50]);
  }

  findNotificationId(element) {
    for (const [id, notification] of this.notifications) {
      if (notification.element === element) {
        return id;
      }
    }
    return null;
  }

  generateId() {
    return `feedback_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 销毁反馈系统
   */
  destroy() {
    this.clearAllNotifications();
    
    for (const [id] of this.loadingStates) {
      this.hideLoading(id);
    }
    
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
    }
    
    if (this.globalLoader && this.globalLoader.parentNode) {
      this.globalLoader.parentNode.removeChild(this.globalLoader);
    }
  }
}

// 全局实例
window.feedbackSystem = new FeedbackSystem();

// Phoenix LiveView Hook
const FeedbackHook = {
  mounted() {
    // 监听服务器反馈事件
    this.handleEvent('show-notification', ({ type, message, options }) => {
      window.feedbackSystem.showNotification(type, message, options);
    });

    this.handleEvent('show-loading', ({ target, options }) => {
      const targetElement = target ? document.querySelector(target) : this.el;
      window.feedbackSystem.showLoading(targetElement, options);
    });

    this.handleEvent('hide-loading', ({ id }) => {
      if (id) {
        window.feedbackSystem.hideLoading(id);
      }
    });

    this.handleEvent('show-global-loading', ({ text, progress }) => {
      window.feedbackSystem.showGlobalLoading(text, progress);
    });

    this.handleEvent('hide-global-loading', () => {
      window.feedbackSystem.hideGlobalLoading();
    });
  },

  destroyed() {
    // 清理相关的加载状态
    for (const [id, loadingState] of window.feedbackSystem.loadingStates) {
      if (loadingState.target === this.el || this.el.contains(loadingState.target)) {
        window.feedbackSystem.hideLoading(id);
      }
    }
  }
};

// 导出
export { FeedbackSystem, FeedbackHook };
