defmodule Teen.GameManagement do
  @moduledoc """
  游戏管理域

  包含平台配置、游戏配置、服务器管理、机器人管理、渠道包管理、VIP等级管理等功能
  """

  use Ash.Domain,
    otp_app: :cypridina,
    extensions: [AshAdmin.Domain]

  alias Teen.GameManagement.{Platform, VipLevel, RobotConfig}
  alias Cypridina.Accounts.User

  admin do
    show? true
  end

  resources do
    resource Teen.GameManagement.Platform
    resource Teen.GameManagement.VipLevel
    resource Teen.GameManagement.RobotConfig
  end

  # ==================== 业务逻辑函数 ====================

  @doc """
  获取用户的VIP等级信息
  """
  def get_user_vip_info(user_id) do
    with {:ok, user} <- User.read(user_id),
         {:ok, vip_level} <- calculate_user_vip_level(user.total_recharge || Decimal.new("0")) do

      # 获取VIP等级详细信息
      case VipLevel.get_by_level(vip_level) do
        {:ok, [level_info]} ->
          {:ok, %{
            current_level: vip_level,
            level_info: level_info,
            total_recharge: user.total_recharge || Decimal.new("0"),
            next_level_requirement: get_next_level_requirement(vip_level)
          }}

        {:ok, []} ->
          {:ok, %{
            current_level: 0,
            level_info: nil,
            total_recharge: user.total_recharge || Decimal.new("0"),
            next_level_requirement: get_next_level_requirement(0)
          }}

        {:error, reason} ->
          {:error, reason}
      end
    end
  end

  @doc """
  计算用户VIP等级
  """
  def calculate_user_vip_level(total_recharge) do
    case VipLevel.list_active_levels() do
      {:ok, levels} ->
        # 按充值要求降序排列，找到符合条件的最高等级
        sorted_levels = Enum.sort_by(levels, & &1.recharge_requirement, {:desc, Decimal})

        matching_level = Enum.find(sorted_levels, fn level ->
          Decimal.compare(total_recharge, level.recharge_requirement) != :lt
        end)

        level = if matching_level, do: matching_level.level, else: 0
        {:ok, level}

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  获取用户VIP特权
  """
  def get_user_vip_privileges(user_id) do
    case get_user_vip_info(user_id) do
      {:ok, %{level_info: nil}} ->
        {:ok, []}

      {:ok, %{level_info: level_info}} ->
        {:ok, level_info.privileges || []}

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  计算VIP每日奖励
  """
  def calculate_vip_daily_bonus(user_id) do
    case get_user_vip_info(user_id) do
      {:ok, %{level_info: nil}} ->
        {:ok, Decimal.new("0")}

      {:ok, %{level_info: level_info}} ->
        {:ok, level_info.daily_bonus}

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  获取平台配置信息
  """
  def get_platform_config(platform_number) do
    case Platform.get_by_platform_number(platform_number) do
      {:ok, [platform]} ->
        {:ok, %{
          platform: platform,
          download_urls: %{
            default: platform.default_download_url,
            ios: platform.ios_download_url,
            android: platform.android_download_url
          },
          features: %{
            agent_recharge_enabled: platform.agent_recharge_switch == 1,
            qr_code_visible: platform.show_qr_code == 1
          }
        }}

      {:ok, []} ->
        {:error, "Platform not found"}

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  获取游戏机器人配置
  """
  def get_game_robots(game_type, difficulty_level \\ nil) do
    base_query = RobotConfig.list_by_game_type(game_type)

    case base_query do
      {:ok, robots} ->
        filtered_robots = if difficulty_level do
          Enum.filter(robots, & &1.difficulty_level == difficulty_level)
        else
          robots
        end

        {:ok, filtered_robots}

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  选择合适的机器人
  """
  def select_robot_for_user(game_type, user_skill_level \\ 2) do
    # 根据用户技能水平选择合适难度的机器人
    difficulty_level = case user_skill_level do
      level when level <= 1 -> 1 # 新手对简单机器人
      level when level <= 3 -> 2 # 普通玩家对普通机器人
      level when level <= 5 -> 3 # 高手对困难机器人
      _ -> 4 # 专家对专家机器人
    end

    case get_game_robots(game_type, difficulty_level) do
      {:ok, robots} when robots != [] ->
        # 随机选择一个机器人
        selected_robot = Enum.random(robots)
        {:ok, selected_robot}

      {:ok, []} ->
        # 如果没有对应难度的机器人，选择普通难度
        case get_game_robots(game_type, 2) do
          {:ok, robots} when robots != [] ->
            {:ok, Enum.random(robots)}

          _ ->
            {:error, "No robots available"}
        end

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  更新平台状态
  """
  def update_platform_status(platform_id, action) do
    case Platform.read(platform_id) do
      {:ok, platform} ->
        case action do
          :enable -> Platform.enable_platform(platform)
          :disable -> Platform.disable_platform(platform)
          :toggle_agent_recharge -> Platform.toggle_agent_recharge(platform)
          _ -> {:error, "Invalid action"}
        end

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  批量更新VIP等级状态
  """
  def batch_update_vip_levels(level_ids, status) when is_list(level_ids) do
    results = Enum.map(level_ids, fn level_id ->
      case VipLevel.read(level_id) do
        {:ok, level} ->
          case status do
            :enable -> VipLevel.enable(level)
            :disable -> VipLevel.disable(level)
            _ -> {:error, "Invalid status"}
          end

        {:error, reason} ->
          {:error, {level_id, reason}}
      end
    end)

    {successes, failures} = Enum.split_with(results, fn
      {:ok, _} -> true
      _ -> false
    end)

    %{
      success_count: length(successes),
      failure_count: length(failures),
      failures: failures
    }
  end

  @doc """
  生成机器人聊天消息
  """
  def generate_robot_chat_message(robot_id, context \\ %{}) do
    case RobotConfig.read(robot_id) do
      {:ok, robot} ->
        messages = robot.chat_messages || []

        if length(messages) > 0 do
          # 根据上下文选择合适的消息
          selected_message = select_contextual_message(messages, context)
          {:ok, selected_message}
        else
          {:ok, "..."}
        end

      {:error, reason} ->
        {:error, reason}
    end
  end

  # ==================== 私有函数 ====================

  defp get_next_level_requirement(current_level) do
    case VipLevel.list_active_levels() do
      {:ok, levels} ->
        next_level = Enum.find(levels, & &1.level == current_level + 1)
        if next_level, do: next_level.recharge_requirement, else: nil

      _ ->
        nil
    end
  end

  defp select_contextual_message(messages, context) do
    # 根据游戏状态、时间等上下文选择合适的消息
    case context do
      %{game_state: "winning"} ->
        winning_messages = Enum.filter(messages, &String.contains?(&1, ["好运", "厉害", "不错"]))
        if length(winning_messages) > 0, do: Enum.random(winning_messages), else: Enum.random(messages)

      %{game_state: "losing"} ->
        encouraging_messages = Enum.filter(messages, &String.contains?(&1, ["加油", "别灰心", "下次"]))
        if length(encouraging_messages) > 0, do: Enum.random(encouraging_messages), else: Enum.random(messages)

      _ ->
        Enum.random(messages)
    end
  end
end
