defmodule Teen.ActivitySystem.FreeBonus do
  @moduledoc """
  Free Bonus资源

  管理Free Bonus活动配置和奖励
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  postgres do
    table "free_bonuses"
    repo Cy<PERSON><PERSON><PERSON>.Repo
  end

  admin do
    table_columns [:id, :bonus_name, :reward_points, :share_count, :exchange_count, :status]
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_bonuses
    define :list_by_game
  end

  actions do
    defaults [:create, :read, :update, :destroy]

    read :list_active_bonuses do
      filter expr(status == 1)
    end

    read :list_by_game do
      argument :game_type, :string, allow_nil?: false
      filter expr(game_type == ^arg(:game_type) and status == 1)
    end

    update :enable do
      change set_attribute(:status, 1)
    end

    update :disable do
      change set_attribute(:status, 0)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :bonus_name, :string do
      allow_nil? false
      public? true
      description "Bonus名称"
      constraints max_length: 100
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "Bonus描述"
      constraints max_length: 1000
    end

    attribute :reward_points, :decimal do
      allow_nil? false
      public? true
      description "奖励积分"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :share_count, :integer do
      allow_nil? false
      public? true
      description "分享次数要求"
      default 1
      constraints min: 0
    end

    attribute :exchange_count, :integer do
      allow_nil? false
      public? true
      description "全民兑换次数"
      default 0
      constraints min: 0
    end

    attribute :game_type, :string do
      allow_nil? true
      public? true
      description "游戏类型"
      constraints max_length: 50
    end

    attribute :game_win_score, :decimal do
      allow_nil? false
      public? true
      description "游戏赢分要求"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :daily_limit, :integer do
      allow_nil? false
      public? true
      description "每日限制次数（0为无限制）"
      default 1
      constraints min: 0
    end

    attribute :total_limit, :integer do
      allow_nil? false
      public? true
      description "总限制次数（0为无限制）"
      default 0
      constraints min: 0
    end

    attribute :used_count, :integer do
      allow_nil? false
      public? true
      description "已使用次数"
      default 0
      constraints min: 0
    end

    attribute :min_level, :integer do
      allow_nil? false
      public? true
      description "最低等级要求"
      default 1
      constraints min: 1
    end

    attribute :vip_level, :integer do
      allow_nil? true
      public? true
      description "VIP等级要求"
      constraints min: 0
    end

    attribute :cooldown_hours, :integer do
      allow_nil? false
      public? true
      description "冷却时间（小时）"
      default 24
      constraints min: 0
    end

    attribute :status, :integer do
      allow_nil? false
      public? true
      description "状态：0-禁用，1-启用"
      default 1
      constraints min: 0, max: 1
    end

    attribute :priority, :integer do
      allow_nil? false
      public? true
      description "优先级（数字越大优先级越高）"
      default 0
      constraints min: 0
    end

    attribute :conditions, :map do
      allow_nil? true
      public? true
      description "额外条件配置"
    end

    timestamps()
  end

  identities do
    identity :unique_bonus_name, [:bonus_name]
  end
end
