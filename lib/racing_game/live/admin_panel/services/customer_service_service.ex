defmodule RacingGame.Live.AdminPanel.Services.CustomerServiceService do
  @moduledoc """
  客服系统业务逻辑服务

  提供客服系统的高级业务逻辑：
  - 工作流管理
  - 跨Repository事务
  - 业务规则验证
  - 系统集成协调
  - 性能监控
  """

  require Logger
  alias RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.{
    CustomerChatRepository,
    UserQuestionRepository,
    ExchangeOrderRepository,
    SensitiveWordRepository,
    UserTagRepository,
    VerificationCodeRepository
  }
  alias RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder

  # 常量定义
  @default_page_size 20
  @max_page_size 100
  @cache_ttl 300  # 5分钟缓存

  # ============================================================================
  # 客服聊天业务逻辑
  # ============================================================================

  @doc """
  创建客服聊天记录

  ## 参数
  - `chat_params` - 聊天参数
  - `options` - 选项

  ## 返回
  - `{:ok, chat}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_chat(chat_params, options \\ []) do
    Logger.info("💬 [客服服务] 创建聊天记录")

    with {:ok, validated_params} <- validate_chat_params(chat_params),
         {:ok, filtered_content} <- filter_sensitive_content(validated_params.content),
         {:ok, chat} <- CustomerChatRepository.create_chat(%{validated_params | content: filtered_content}) do

      # 异步处理
      if options[:async_processing] do
        Task.start(fn -> process_new_chat(chat) end)
      end

      {:ok, chat}
    else
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  分配客服处理聊天

  ## 参数
  - `chat_id` - 聊天ID
  - `customer_service_id` - 客服ID
  - `options` - 选项

  ## 返回
  - `{:ok, chat}` - 成功
  - `{:error, reason}` - 失败
  """
  def assign_chat_to_customer_service(chat_id, customer_service_id, options \\ []) do
    Logger.info("👨‍💼 [客服服务] 分配聊天给客服: #{customer_service_id}")

    with {:ok, chat} <- CustomerChatRepository.get_chat_by_id(chat_id),
         {:ok, _} <- validate_customer_service_availability(customer_service_id),
         {:ok, updated_chat} <- CustomerChatRepository.assign_to_customer_service(chat_id, customer_service_id, options[:notes]) do

      # 通知相关方
      notify_chat_assignment(updated_chat, customer_service_id)

      {:ok, updated_chat}
    else
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  处理聊天完成

  ## 参数
  - `chat_id` - 聊天ID
  - `resolution_notes` - 解决备注
  - `options` - 选项

  ## 返回
  - `{:ok, chat}` - 成功
  - `{:error, reason}` - 失败
  """
  def complete_chat(chat_id, resolution_notes, options \\ []) do
    Logger.info("✅ [客服服务] 完成聊天处理: #{chat_id}")

    with {:ok, chat} <- CustomerChatRepository.get_chat_by_id(chat_id),
         {:ok, _} <- validate_chat_can_be_completed(chat),
         {:ok, updated_chat} <- CustomerChatRepository.mark_as_processed(chat_id, resolution_notes, options[:customer_service_id]) do

      # 更新统计和标签
      update_chat_completion_stats(updated_chat)
      maybe_update_user_tags(updated_chat, options)

      {:ok, updated_chat}
    else
      {:error, reason} -> {:error, reason}
    end
  end

  # ============================================================================
  # 用户问题业务逻辑
  # ============================================================================

  @doc """
  创建用户问题

  ## 参数
  - `question_params` - 问题参数
  - `options` - 选项

  ## 返回
  - `{:ok, question}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_user_question(question_params, options \\ []) do
    Logger.info("❓ [客服服务] 创建用户问题")

    with {:ok, validated_params} <- validate_question_params(question_params),
         {:ok, filtered_content} <- filter_sensitive_content(validated_params.content),
         {:ok, priority} <- calculate_question_priority(validated_params),
         {:ok, question} <- UserQuestionRepository.create_question(%{validated_params | content: filtered_content, priority: priority}) do

      # 自动分配处理
      if options[:auto_assign] do
        Task.start(fn -> auto_assign_question(question) end)
      end

      {:ok, question}
    else
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  智能分配问题给员工

  ## 参数
  - `question_id` - 问题ID
  - `options` - 选项

  ## 返回
  - `{:ok, question}` - 成功
  - `{:error, reason}` - 失败
  """
  def smart_assign_question(question_id, options \\ []) do
    Logger.info("🤖 [客服服务] 智能分配问题: #{question_id}")

    with {:ok, question} <- UserQuestionRepository.get_question_by_id(question_id),
         {:ok, best_staff} <- find_best_staff_for_question(question, options),
         {:ok, updated_question} <- UserQuestionRepository.assign_to_staff(question_id, best_staff.id) do

      # 通知员工
      notify_question_assignment(updated_question, best_staff)

      {:ok, updated_question}
    else
      {:error, reason} -> {:error, reason}
    end
  end

  # ============================================================================
  # 兑换订单业务逻辑
  # ============================================================================

  @doc """
  创建兑换订单

  ## 参数
  - `order_params` - 订单参数
  - `options` - 选项

  ## 返回
  - `{:ok, order}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_exchange_order(order_params, options \\ []) do
    Logger.info("🔄 [客服服务] 创建兑换订单")

    with {:ok, validated_params} <- validate_order_params(order_params),
         {:ok, _} <- validate_user_eligibility(validated_params.user_id),
         {:ok, order_number} <- generate_order_number(),
         {:ok, order} <- ExchangeOrderRepository.create_order(%{validated_params | order_number: order_number}) do

      # 启动审核流程
      if options[:auto_audit] do
        Task.start(fn -> initiate_order_audit(order) end)
      end

      {:ok, order}
    else
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  批量审核订单

  ## 参数
  - `order_ids` - 订单ID列表
  - `audit_result` - 审核结果
  - `auditor_id` - 审核员ID
  - `notes` - 审核备注

  ## 返回
  - `{:ok, results}` - 成功
  - `{:error, reason}` - 失败
  """
  def batch_audit_orders(order_ids, audit_result, auditor_id, notes) do
    Logger.info("📋 [客服服务] 批量审核订单: #{length(order_ids)} 个")

    # 使用事务确保一致性
    Ash.DataLayer.transaction(fn ->
      results = Enum.map(order_ids, fn order_id ->
        case ExchangeOrderRepository.audit_order(order_id, audit_result, auditor_id, notes) do
          {:ok, order} -> {:ok, order}
          {:error, reason} -> {:error, {order_id, reason}}
        end
      end)

      # 检查是否有失败的
      failed = Enum.filter(results, fn {status, _} -> status == :error end)

      if length(failed) > 0 do
        {:error, {:batch_audit_failed, failed}}
      else
        successful = Enum.map(results, fn {:ok, order} -> order end)

        # 批量通知
        notify_batch_audit_completion(successful, audit_result, auditor_id)

        {:ok, successful}
      end
    end)
  end

  # ============================================================================
  # 敏感词管理业务逻辑
  # ============================================================================

  @doc """
  批量导入敏感词

  ## 参数
  - `words_data` - 敏感词数据列表
  - `options` - 选项

  ## 返回
  - `{:ok, results}` - 成功
  - `{:error, reason}` - 失败
  """
  def batch_import_sensitive_words(words_data, options \\ []) do
    Logger.info("📥 [客服服务] 批量导入敏感词: #{length(words_data)} 个")

    with {:ok, validated_words} <- validate_words_data(words_data),
         {:ok, deduplicated_words} <- deduplicate_words(validated_words),
         {:ok, results} <- SensitiveWordRepository.batch_create_words(deduplicated_words) do

      # 清理缓存
      clear_sensitive_words_cache()

      # 统计结果
      stats = %{
        total: length(words_data),
        imported: length(results),
        duplicates: length(words_data) - length(deduplicated_words)
      }

      {:ok, %{words: results, stats: stats}}
    else
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  内容敏感词检测和过滤

  ## 参数
  - `content` - 内容文本
  - `options` - 选项

  ## 返回
  - `{:ok, filtered_content}` - 成功
  - `{:error, reason}` - 失败
  """
  def filter_sensitive_content(content, options \\ []) do
    Logger.debug("🔍 [客服服务] 检测敏感词")

    with {:ok, sensitive_words} <- SensitiveWordRepository.check_text_for_sensitive_words(content, options),
         {:ok, filtered_content} <- apply_content_filtering(content, sensitive_words, options) do

      # 记录检测结果
      if length(sensitive_words) > 0 do
        log_sensitive_content_detection(content, sensitive_words, options)
      end

      {:ok, filtered_content}
    else
      {:error, reason} -> {:error, reason}
    end
  end

  # ============================================================================
  # 系统统计和监控
  # ============================================================================

  @doc """
  获取客服系统综合统计

  ## 参数
  - `params` - 查询参数
  - `options` - 选项

  ## 返回
  - `{:ok, statistics}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_comprehensive_statistics(params \\ %{}, options \\ []) do
    Logger.info("📊 [客服服务] 获取综合统计")

    try do
      # 并行获取各种统计
      tasks = [
        Task.async(fn -> CustomerChatRepository.get_chat_statistics(params) end),
        Task.async(fn -> UserQuestionRepository.get_question_statistics(params) end),
        Task.async(fn -> ExchangeOrderRepository.get_order_statistics(params) end),
        Task.async(fn -> SensitiveWordRepository.get_word_statistics(params) end),
        Task.async(fn -> UserTagRepository.get_tag_statistics(params) end),
        Task.async(fn -> VerificationCodeRepository.get_code_statistics(params) end)
      ]

      results = Task.await_many(tasks, 10_000)

      statistics = %{
        chat_stats: Enum.at(results, 0),
        question_stats: Enum.at(results, 1),
        order_stats: Enum.at(results, 2),
        word_stats: Enum.at(results, 3),
        tag_stats: Enum.at(results, 4),
        code_stats: Enum.at(results, 5),
        generated_at: DateTime.utc_now()
      }

      {:ok, statistics}
    rescue
      error ->
        Logger.error("❌ [客服服务] 获取统计失败: #{inspect(error)}")
        {:error, :statistics_failed}
    end
  end

  @doc """
  系统健康检查

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, health_status}` - 成功
  - `{:error, reason}` - 失败
  """
  def system_health_check(options \\ []) do
    Logger.info("🏥 [客服服务] 系统健康检查")

    try do
      with {:ok, queries} <- CustomerServiceQueryBuilder.build_cross_repository_query(:system_health_check, %{}) do

        # 执行健康检查查询
        health_results = %{
          recent_chats_count: count_query_results(queries.recent_chats),
          pending_questions_count: count_query_results(queries.pending_questions),
          pending_orders_count: count_query_results(queries.pending_orders),
          recent_codes_count: count_query_results(queries.recent_codes),
          enabled_words_count: count_query_results(queries.enabled_words)
        }

        # 评估系统状态
        system_status = evaluate_system_health(health_results)

        health_status = %{
          status: system_status,
          metrics: health_results,
          checked_at: DateTime.utc_now(),
          recommendations: generate_health_recommendations(health_results)
        }

        {:ok, health_status}
      else
        {:error, reason} -> {:error, reason}
      end
    rescue
      error ->
        Logger.error("❌ [客服服务] 健康检查失败: #{inspect(error)}")
        {:error, :health_check_failed}
    end
  end

  # ============================================================================
  # 私有辅助函数
  # ============================================================================

  # 验证聊天参数
  defp validate_chat_params(params) do
    required_fields = [:user_id, :content]

    case validate_required_fields(params, required_fields) do
      :ok -> {:ok, params}
      {:error, missing_fields} -> {:error, {:missing_fields, missing_fields}}
    end
  end

  # 验证问题参数
  defp validate_question_params(params) do
    required_fields = [:user_id, :title, :content]

    case validate_required_fields(params, required_fields) do
      :ok -> {:ok, params}
      {:error, missing_fields} -> {:error, {:missing_fields, missing_fields}}
    end
  end

  # 验证订单参数
  defp validate_order_params(params) do
    required_fields = [:user_id, :exchange_type, :amount]

    case validate_required_fields(params, required_fields) do
      :ok -> {:ok, params}
      {:error, missing_fields} -> {:error, {:missing_fields, missing_fields}}
    end
  end

  # 验证必填字段
  defp validate_required_fields(params, required_fields) do
    missing_fields = Enum.filter(required_fields, fn field ->
      is_nil(params[field]) or params[field] == ""
    end)

    if length(missing_fields) == 0 do
      :ok
    else
      {:error, missing_fields}
    end
  end

  # 验证客服可用性
  defp validate_customer_service_availability(customer_service_id) do
    # 这里应该检查客服的工作状态、工作负载等
    # 简化实现
    if not is_nil(customer_service_id) do
      {:ok, :available}
    else
      {:error, :invalid_customer_service}
    end
  end

  # 验证聊天是否可以完成
  defp validate_chat_can_be_completed(chat) do
    case chat.status do
      "in_progress" -> {:ok, :can_complete}
      "assigned" -> {:ok, :can_complete}
      "completed" -> {:error, :already_completed}
      _ -> {:error, :invalid_status}
    end
  end

  # 计算问题优先级
  defp calculate_question_priority(params) do
    # 基于关键词、用户等级等计算优先级
    priority = cond do
      String.contains?(params.content || "", ["紧急", "urgent", "重要"]) -> "high"
      String.contains?(params.content || "", ["一般", "normal"]) -> "medium"
      true -> "low"
    end

    {:ok, priority}
  end

  # 查找最适合的员工
  defp find_best_staff_for_question(question, options) do
    # 简化实现 - 实际应该基于工作负载、专业领域等
    # 这里返回一个模拟的员工
    staff = %{id: "staff_001", name: "客服专员", workload: 5}
    {:ok, staff}
  end

  # 验证用户资格
  defp validate_user_eligibility(user_id) do
    # 检查用户状态、余额等
    if not is_nil(user_id) do
      {:ok, :eligible}
    else
      {:error, :invalid_user}
    end
  end

  # 生成订单号
  defp generate_order_number do
    timestamp = DateTime.utc_now() |> DateTime.to_unix()
    random = :rand.uniform(9999) |> Integer.to_string() |> String.pad_leading(4, "0")
    order_number = "EO#{timestamp}#{random}"
    {:ok, order_number}
  end

  # 验证敏感词数据
  defp validate_words_data(words_data) do
    validated = Enum.filter(words_data, fn word_data ->
      not is_nil(word_data[:word]) and String.length(word_data[:word]) > 0
    end)

    {:ok, validated}
  end

  # 去重敏感词
  defp deduplicate_words(words_data) do
    unique_words = Enum.uniq_by(words_data, fn word_data -> word_data[:word] end)
    {:ok, unique_words}
  end

  # 应用内容过滤
  defp apply_content_filtering(content, sensitive_words, options) do
    replacement = options[:replacement] || "***"

    filtered_content = Enum.reduce(sensitive_words, content, fn word, acc ->
      String.replace(acc, word.word, replacement, global: true)
    end)

    {:ok, filtered_content}
  end

  # 清理敏感词缓存
  defp clear_sensitive_words_cache do
    # 实现缓存清理逻辑
    Logger.debug("🧹 [客服服务] 清理敏感词缓存")
    :ok
  end

  # 记录敏感内容检测
  defp log_sensitive_content_detection(content, sensitive_words, options) do
    Logger.warn("⚠️ [客服服务] 检测到敏感内容: #{length(sensitive_words)} 个敏感词")
    # 可以记录到审计日志
    :ok
  end

  # 统计查询结果数量
  defp count_query_results(query) do
    case Ash.read(query) do
      {:ok, results} when is_list(results) -> length(results)
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  # 评估系统健康状态
  defp evaluate_system_health(metrics) do
    cond do
      metrics.pending_questions_count > 100 -> :critical
      metrics.pending_orders_count > 50 -> :warning
      metrics.recent_chats_count == 0 -> :warning
      true -> :healthy
    end
  end

  # 生成健康建议
  defp generate_health_recommendations(metrics) do
    recommendations = []

    recommendations = if metrics.pending_questions_count > 50 do
      ["考虑增加客服人员处理待处理问题" | recommendations]
    else
      recommendations
    end

    recommendations = if metrics.pending_orders_count > 30 do
      ["加快订单审核流程" | recommendations]
    else
      recommendations
    end

    recommendations = if metrics.recent_chats_count == 0 do
      ["检查聊天系统是否正常运行" | recommendations]
    else
      recommendations
    end

    if length(recommendations) == 0 do
      ["系统运行正常"]
    else
      recommendations
    end
  end

  # ============================================================================
  # 异步处理和通知函数
  # ============================================================================

  # 处理新聊天
  defp process_new_chat(chat) do
    Logger.debug("🔄 [客服服务] 异步处理新聊天: #{chat.id}")

    # 自动标记用户标签
    maybe_auto_tag_user(chat.user_id, chat.content)

    # 检查是否需要优先处理
    if is_urgent_chat?(chat) do
      notify_urgent_chat(chat)
    end

    :ok
  end

  # 自动分配问题
  defp auto_assign_question(question) do
    Logger.debug("🤖 [客服服务] 自动分配问题: #{question.id}")

    case find_best_staff_for_question(question, []) do
      {:ok, staff} ->
        UserQuestionRepository.assign_to_staff(question.id, staff.id)
        notify_question_assignment(question, staff)
      {:error, _reason} ->
        Logger.warn("⚠️ [客服服务] 自动分配失败，问题将保持未分配状态")
    end

    :ok
  end

  # 启动订单审核
  defp initiate_order_audit(order) do
    Logger.debug("🔍 [客服服务] 启动订单审核: #{order.id}")

    # 自动审核逻辑（简化）
    if order.amount < 1000 do
      ExchangeOrderRepository.audit_order(order.id, "approved", "system", "自动审核通过")
    end

    :ok
  end

  # 通知聊天分配
  defp notify_chat_assignment(chat, customer_service_id) do
    Logger.info("📢 [客服服务] 通知聊天分配: #{chat.id} -> #{customer_service_id}")
    # 实现通知逻辑
    :ok
  end

  # 通知问题分配
  defp notify_question_assignment(question, staff) do
    Logger.info("📢 [客服服务] 通知问题分配: #{question.id} -> #{staff.id}")
    # 实现通知逻辑
    :ok
  end

  # 通知批量审核完成
  defp notify_batch_audit_completion(orders, audit_result, auditor_id) do
    Logger.info("📢 [客服服务] 通知批量审核完成: #{length(orders)} 个订单, 结果: #{audit_result}")
    # 实现通知逻辑
    :ok
  end

  # 更新聊天完成统计
  defp update_chat_completion_stats(chat) do
    Logger.debug("📊 [客服服务] 更新聊天完成统计")
    # 实现统计更新逻辑
    :ok
  end

  # 可能更新用户标签
  defp maybe_update_user_tags(chat, options) do
    if options[:auto_tag] do
      maybe_auto_tag_user(chat.user_id, chat.notes)
    end
    :ok
  end

  # 自动标记用户
  defp maybe_auto_tag_user(user_id, content) do
    Logger.debug("🏷️ [客服服务] 检查自动标签: #{user_id}")

    # 基于内容自动添加标签
    cond do
      String.contains?(content || "", ["投诉", "complaint"]) ->
        UserTagRepository.add_user_tag(user_id, "complaint_user", "system", "自动标记")
      String.contains?(content || "", ["VIP", "重要客户"]) ->
        UserTagRepository.add_user_tag(user_id, "vip_user", "system", "自动标记")
      true ->
        :ok
    end
  end

  # 检查是否紧急聊天
  defp is_urgent_chat?(chat) do
    urgent_keywords = ["紧急", "urgent", "重要", "投诉"]
    String.contains?(chat.content || "", urgent_keywords)
  end

  # 通知紧急聊天
  defp notify_urgent_chat(chat) do
    Logger.warn("🚨 [客服服务] 紧急聊天通知: #{chat.id}")
    # 实现紧急通知逻辑
    :ok
  end
end
