defmodule RacingGame.Live.AdminPanel.Repositories.QueryBuilders.LedgerQueryBuilder do
  @moduledoc """
  账本系统查询构建器
  
  提供账本系统相关的动态查询构建功能：
  - 账户查询构建
  - 转账查询构建
  - 余额查询构建
  - 复杂条件组合
  """

  require Logger
  alias Cypridina.Ledger.{Account, Transfer, Balance}
  require Ash.Query

  # ============================================================================
  # 账户查询构建
  # ============================================================================

  @doc """
  构建账户列表查询

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, query}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_account_list_query(params) do
    Logger.debug("🔨 [账本查询构建器] 构建账户列表查询")
    
    try do
      query = Account
      |> apply_account_filters(params.filters)
      |> apply_search(params.search, [:identifier, :description])
      |> apply_sort(params.sort)
      
      {:ok, query}
    rescue
      error ->
        Logger.error("❌ [账本查询构建器] 构建账户查询失败: #{inspect(error)}")
        {:error, :query_build_failed}
    end
  end

  @doc """
  构建账户搜索查询

  ## 参数
  - `search_term` - 搜索词
  - `fields` - 搜索字段

  ## 返回
  - `query` - 查询对象
  """
  def build_account_search_query(search_term, fields \\ [:identifier, :description]) do
    Logger.debug("🔍 [账本查询构建器] 构建账户搜索查询: #{search_term}")
    
    Account
    |> apply_search(search_term, fields)
    |> apply_sort([inserted_at: :desc])
  end

  @doc """
  构建账户统计查询

  ## 参数
  - `filters` - 过滤条件

  ## 返回
  - `{:ok, query}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_account_statistics_query(filters \\ %{}) do
    Logger.debug("📊 [账本查询构建器] 构建账户统计查询")
    
    try do
      query = Account
      |> apply_account_filters(filters)
      
      {:ok, query}
    rescue
      error ->
        Logger.error("❌ [账本查询构建器] 构建账户统计查询失败: #{inspect(error)}")
        {:error, :query_build_failed}
    end
  end

  # ============================================================================
  # 转账查询构建
  # ============================================================================

  @doc """
  构建转账列表查询

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, query}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_transfer_list_query(params) do
    Logger.debug("🔨 [账本查询构建器] 构建转账列表查询")
    
    try do
      query = Transfer
      |> apply_transfer_filters(params.filters)
      |> apply_search(params.search, [:description, :reference])
      |> apply_sort(params.sort)
      
      {:ok, query}
    rescue
      error ->
        Logger.error("❌ [账本查询构建器] 构建转账查询失败: #{inspect(error)}")
        {:error, :query_build_failed}
    end
  end

  @doc """
  构建转账搜索查询

  ## 参数
  - `search_term` - 搜索词
  - `fields` - 搜索字段

  ## 返回
  - `query` - 查询对象
  """
  def build_transfer_search_query(search_term, fields \\ [:description, :reference]) do
    Logger.debug("🔍 [账本查询构建器] 构建转账搜索查询: #{search_term}")
    
    Transfer
    |> apply_search(search_term, fields)
    |> apply_sort([inserted_at: :desc])
  end

  @doc """
  构建账户转账查询

  ## 参数
  - `account_id` - 账户ID
  - `filters` - 过滤条件

  ## 返回
  - `{:ok, query}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_account_transfer_query(account_id, filters \\ %{}) do
    Logger.debug("🔨 [账本查询构建器] 构建账户转账查询: #{account_id}")
    
    try do
      query = Transfer
      |> Ash.Query.filter(from_account_id == account_id or to_account_id == account_id)
      |> apply_transfer_filters(filters)
      |> apply_sort([inserted_at: :desc])
      
      {:ok, query}
    rescue
      error ->
        Logger.error("❌ [账本查询构建器] 构建账户转账查询失败: #{inspect(error)}")
        {:error, :query_build_failed}
    end
  end

  # ============================================================================
  # 余额查询构建
  # ============================================================================

  @doc """
  构建余额列表查询

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, query}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_balance_list_query(params) do
    Logger.debug("🔨 [账本查询构建器] 构建余额列表查询")
    
    try do
      query = Balance
      |> apply_balance_filters(params.filters)
      |> apply_search(params.search, [])  # 余额通常不需要文本搜索
      |> apply_sort(params.sort)
      
      {:ok, query}
    rescue
      error ->
        Logger.error("❌ [账本查询构建器] 构建余额查询失败: #{inspect(error)}")
        {:error, :query_build_failed}
    end
  end

  @doc """
  构建账户余额历史查询

  ## 参数
  - `account_id` - 账户ID
  - `filters` - 过滤条件

  ## 返回
  - `{:ok, query}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_account_balance_history_query(account_id, filters \\ %{}) do
    Logger.debug("🔨 [账本查询构建器] 构建账户余额历史查询: #{account_id}")
    
    try do
      query = Balance
      |> Ash.Query.filter(account_id == account_id)
      |> apply_balance_filters(filters)
      |> apply_sort([inserted_at: :desc])
      
      {:ok, query}
    rescue
      error ->
        Logger.error("❌ [账本查询构建器] 构建账户余额历史查询失败: #{inspect(error)}")
        {:error, :query_build_failed}
    end
  end

  # ============================================================================
  # 复合查询构建
  # ============================================================================

  @doc """
  构建用户完整财务查询

  ## 参数
  - `user_id` - 用户ID
  - `currency` - 货币类型
  - `options` - 选项

  ## 返回
  - `{:ok, queries}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_user_financial_query(user_id, currency \\ :XAA, options \\ %{}) do
    Logger.debug("🔨 [账本查询构建器] 构建用户财务查询: #{user_id} - #{currency}")
    
    try do
      identifier = "user:#{currency}:#{user_id}"
      
      # 构建账户查询
      account_query = Account
      |> Ash.Query.filter(identifier == identifier)
      
      # 构建转账查询（需要先获取账户ID）
      transfer_query = case get_account_id_by_identifier(identifier) do
        {:ok, account_id} ->
          Transfer
          |> Ash.Query.filter(from_account_id == account_id or to_account_id == account_id)
          |> maybe_limit_by_days(options[:days])
        {:error, _} -> nil
      end
      
      # 构建余额查询
      balance_query = case get_account_id_by_identifier(identifier) do
        {:ok, account_id} ->
          Balance
          |> Ash.Query.filter(account_id == account_id)
          |> apply_sort([inserted_at: :desc])
          |> maybe_limit_by_days(options[:days])
        {:error, _} -> nil
      end
      
      queries = %{
        account: account_query,
        transfers: transfer_query,
        balances: balance_query
      }
      
      {:ok, queries}
    rescue
      error ->
        Logger.error("❌ [账本查询构建器] 构建用户财务查询失败: #{inspect(error)}")
        {:error, :query_build_failed}
    end
  end

  # ============================================================================
  # 私有函数 - 过滤器应用
  # ============================================================================

  # 应用账户过滤器
  defp apply_account_filters(query, filters) when map_size(filters) == 0, do: query
  defp apply_account_filters(query, filters) do
    Enum.reduce(filters, query, fn {key, value}, acc_query ->
      apply_account_filter(acc_query, key, value)
    end)
  end

  # 应用单个账户过滤器
  defp apply_account_filter(query, :account_type, value) when not is_nil(value) do
    Ash.Query.filter(query, account_type == value)
  end
  defp apply_account_filter(query, :currency, value) when not is_nil(value) do
    Ash.Query.filter(query, currency == value)
  end
  defp apply_account_filter(query, :is_active, value) when is_boolean(value) do
    Ash.Query.filter(query, is_active == value)
  end
  defp apply_account_filter(query, :created_after, value) when not is_nil(value) do
    Ash.Query.filter(query, inserted_at >= value)
  end
  defp apply_account_filter(query, :created_before, value) when not is_nil(value) do
    Ash.Query.filter(query, inserted_at <= value)
  end
  defp apply_account_filter(query, _key, _value), do: query

  # 应用转账过滤器
  defp apply_transfer_filters(query, filters) when map_size(filters) == 0, do: query
  defp apply_transfer_filters(query, filters) do
    Enum.reduce(filters, query, fn {key, value}, acc_query ->
      apply_transfer_filter(acc_query, key, value)
    end)
  end

  # 应用单个转账过滤器
  defp apply_transfer_filter(query, :status, value) when not is_nil(value) do
    Ash.Query.filter(query, status == value)
  end
  defp apply_transfer_filter(query, :currency, value) when not is_nil(value) do
    Ash.Query.filter(query, currency == value)
  end
  defp apply_transfer_filter(query, :from_account_id, value) when not is_nil(value) do
    Ash.Query.filter(query, from_account_id == value)
  end
  defp apply_transfer_filter(query, :to_account_id, value) when not is_nil(value) do
    Ash.Query.filter(query, to_account_id == value)
  end
  defp apply_transfer_filter(query, :amount_min, value) when not is_nil(value) do
    Ash.Query.filter(query, amount >= value)
  end
  defp apply_transfer_filter(query, :amount_max, value) when not is_nil(value) do
    Ash.Query.filter(query, amount <= value)
  end
  defp apply_transfer_filter(query, :created_after, value) when not is_nil(value) do
    Ash.Query.filter(query, inserted_at >= value)
  end
  defp apply_transfer_filter(query, :created_before, value) when not is_nil(value) do
    Ash.Query.filter(query, inserted_at <= value)
  end
  defp apply_transfer_filter(query, _key, _value), do: query

  # 应用余额过滤器
  defp apply_balance_filters(query, filters) when map_size(filters) == 0, do: query
  defp apply_balance_filters(query, filters) do
    Enum.reduce(filters, query, fn {key, value}, acc_query ->
      apply_balance_filter(acc_query, key, value)
    end)
  end

  # 应用单个余额过滤器
  defp apply_balance_filter(query, :account_id, value) when not is_nil(value) do
    Ash.Query.filter(query, account_id == value)
  end
  defp apply_balance_filter(query, :balance_min, value) when not is_nil(value) do
    Ash.Query.filter(query, balance >= value)
  end
  defp apply_balance_filter(query, :balance_max, value) when not is_nil(value) do
    Ash.Query.filter(query, balance <= value)
  end
  defp apply_balance_filter(query, :created_after, value) when not is_nil(value) do
    Ash.Query.filter(query, inserted_at >= value)
  end
  defp apply_balance_filter(query, :created_before, value) when not is_nil(value) do
    Ash.Query.filter(query, inserted_at <= value)
  end
  defp apply_balance_filter(query, _key, _value), do: query

  # ============================================================================
  # 私有函数 - 通用功能
  # ============================================================================

  # 应用搜索
  defp apply_search(query, "", _fields), do: query
  defp apply_search(query, search_term, []) when byte_size(search_term) > 0, do: query
  defp apply_search(query, search_term, fields) when byte_size(search_term) > 0 do
    search_pattern = "%#{search_term}%"
    
    search_conditions = Enum.map(fields, fn field ->
      case field do
        :identifier -> {:ilike, field, search_pattern}
        :description -> {:ilike, field, search_pattern}
        :reference -> {:ilike, field, search_pattern}
        _ -> nil
      end
    end)
    |> Enum.filter(&(!is_nil(&1)))
    
    case search_conditions do
      [] -> query
      [condition] -> apply_single_search_condition(query, condition)
      conditions -> apply_multiple_search_conditions(query, conditions)
    end
  end
  defp apply_search(query, _search_term, _fields), do: query

  # 应用单个搜索条件
  defp apply_single_search_condition(query, {:ilike, field, pattern}) do
    Ash.Query.filter(query, ilike(^field, pattern))
  end

  # 应用多个搜索条件（OR关系）
  defp apply_multiple_search_conditions(query, conditions) do
    # 这里需要根据Ash的实际OR语法调整
    # 简化实现，只使用第一个条件
    case List.first(conditions) do
      {:ilike, field, pattern} -> Ash.Query.filter(query, ilike(^field, pattern))
      _ -> query
    end
  end

  # 应用排序
  defp apply_sort(query, []), do: query
  defp apply_sort(query, sort) when is_list(sort) do
    Ash.Query.sort(query, sort)
  end
  defp apply_sort(query, _), do: query

  # 根据天数限制
  defp maybe_limit_by_days(query, nil), do: query
  defp maybe_limit_by_days(query, days) when is_integer(days) and days > 0 do
    date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
    Ash.Query.filter(query, inserted_at >= date_threshold)
  end
  defp maybe_limit_by_days(query, _), do: query

  # 获取账户ID（辅助函数）
  defp get_account_id_by_identifier(identifier) do
    case Account
         |> Ash.Query.filter(identifier == identifier)
         |> Ash.Query.select([:id])
         |> Ash.read_one() do
      {:ok, %{id: account_id}} -> {:ok, account_id}
      {:ok, nil} -> {:error, :not_found}
      {:error, _} -> {:error, :query_failed}
    end
  end
end
