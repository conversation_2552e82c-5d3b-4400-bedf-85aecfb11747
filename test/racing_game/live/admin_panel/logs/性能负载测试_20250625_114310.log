Compiling 48 files (.ex)
    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 32 │   def create_promoter(promoter_data, options \\ []) do
    │                                      ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promoter_repository.ex:32:38: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromoterRepository.create_promoter/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 32 │   def create_settlement(settlement_data, options \\ []) do
    │                                          ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promotion_settlement_repository.ex:32:42: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionSettlementRepository.create_settlement/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 32 │   def create_channel(channel_data, options \\ []) do
    │                                    ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promotion_channel_repository.ex:32:36: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionChannelRepository.create_channel/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 32 │   def create_settlement(settlement_data, options \\ []) do
    │                                          ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/share_settlement_repository.ex:32:42: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.ShareSettlementRepository.create_settlement/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 32 │   def create_config(config_data, options \\ []) do
    │                                  ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/share_config_repository.ex:32:34: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.ShareConfigRepository.create_config/2

     warning: variable "promoter" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 242 │          {:ok, promoter} <- PromoterRepository.get_promoter(channel.promoter_id),
     │                ~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/promotion_system_service.ex:242:16: RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService.handle_channel_conversion/3

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 58 │   def get_promoter(promoter_id, options \\ []) do
    │                                 ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promoter_repository.ex:58:33: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromoterRepository.get_promoter/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 58 │   def get_settlement(settlement_id, options \\ []) do
    │                                     ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/share_settlement_repository.ex:58:37: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.ShareSettlementRepository.get_settlement/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 58 │   def get_channel(channel_id, options \\ []) do
    │                               ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promotion_channel_repository.ex:58:31: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionChannelRepository.get_channel/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 58 │   def get_config(config_id, options \\ []) do
    │                             ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/share_config_repository.ex:58:29: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.ShareConfigRepository.get_config/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 58 │   def get_settlement(settlement_id, options \\ []) do
    │                                     ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promotion_settlement_repository.ex:58:37: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionSettlementRepository.get_settlement/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 88 │   def get_promoter_by_user_id(user_id, options \\ []) do
    │                                        ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promoter_repository.ex:88:40: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromoterRepository.get_promoter_by_user_id/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 88 │   def get_config_by_key(config_key, options \\ []) do
    │                                     ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/share_config_repository.ex:88:37: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.ShareConfigRepository.get_config_by_key/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 89 │   def update_channel(channel_id, update_data, options \\ []) do
    │                                               ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promotion_channel_repository.ex:89:47: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionChannelRepository.update_channel/3

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 89 │   def update_settlement(settlement_id, update_data, options \\ []) do
    │                                                     ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/share_settlement_repository.ex:89:53: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.ShareSettlementRepository.update_settlement/3

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 89 │   def update_settlement(settlement_id, update_data, options \\ []) do
    │                                                     ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promotion_settlement_repository.ex:89:53: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionSettlementRepository.update_settlement/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 119 │   def get_promoter_by_code(promoter_code, options \\ []) do
     │                                           ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promoter_repository.ex:119:43: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromoterRepository.get_promoter_by_code/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 115 │   def delete_channel(channel_id, options \\ []) do
     │                                  ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promotion_channel_repository.ex:115:34: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionChannelRepository.delete_channel/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 120 │   def update_config(config_id, update_data, options \\ []) do
     │                                             ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/share_config_repository.ex:120:45: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.ShareConfigRepository.update_config/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 115 │   def delete_settlement(settlement_id, options \\ []) do
     │                                        ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promotion_settlement_repository.ex:115:40: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionSettlementRepository.delete_settlement/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 115 │   def delete_settlement(settlement_id, options \\ []) do
     │                                        ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/share_settlement_repository.ex:115:40: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.ShareSettlementRepository.delete_settlement/2

     warning: variable "reason" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 378 │   defp send_promoter_review_notification(promoter, action, reason) do
     │                                                            ~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/promotion_system_service.ex:378:60: RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService.send_promoter_review_notification/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 151 │   def update_promoter(promoter_id, update_data, options \\ []) do
     │                                                 ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promoter_repository.ex:151:49: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromoterRepository.update_promoter/3

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 82 │   def build_promoter_relationship_tree(promoter_id, depth \\ 3, options \\ []) do
    │                                                                 ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/query_builders/teen/promotion_system_query_builder.ex:82:65: RacingGame.Live.AdminPanel.QueryBuilders.Teen.PromotionSystemQueryBuilder.build_promoter_relationship_tree/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 146 │   def delete_config(config_id, options \\ []) do
     │                                ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/share_config_repository.ex:146:32: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.ShareConfigRepository.delete_config/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 177 │   def delete_promoter(promoter_id, options \\ []) do
     │                                    ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promoter_repository.ex:177:36: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromoterRepository.delete_promoter/2

     warning: variable "channel" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 443 │   defp get_redirect_url(channel) do
     │                         ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/promotion_system_service.ex:443:25: RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService.get_redirect_url/1

     warning: variable "channel" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 451 │   defp calculate_registration_commission(promoter, channel) do
     │                                                    ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/promotion_system_service.ex:451:52: RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService.calculate_registration_commission/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 205 │   def approve_promoter(promoter_id, options \\ []) do
     │                                     ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promoter_repository.ex:205:37: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromoterRepository.approve_promoter/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 231 │   def reject_promoter(promoter_id, options \\ []) do
     │                                    ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promoter_repository.ex:231:36: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromoterRepository.reject_promoter/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 242 │   def get_promoter_settlement_stats(promoter_id, date_range \\ nil, options \\ []) do
     │                                                                     ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promotion_settlement_repository.ex:242:69: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionSettlementRepository.get_promoter_settlement_stats/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 237 │   def list_active_configs(options \\ []) do
     │                           ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/share_config_repository.ex:237:27: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.ShareConfigRepository.list_active_configs/1

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_robot_stats(stats_data, options \\ []) do
    │                                      ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/robot_stats_repository.ex:35:38: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RobotStatsRepository.create_robot_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 244 │   def increment_channel_clicks(channel_id, click_count \\ 1, options \\ []) do
     │                                                              ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promotion_channel_repository.ex:244:62: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionChannelRepository.increment_channel_clicks/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 271 │   def get_system_settlement_stats(date_range \\ nil, options \\ []) do
     │                                                      ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promotion_settlement_repository.ex:271:54: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionSettlementRepository.get_system_settlement_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 275 │   def get_user_settlement_stats(user_id, date_range \\ nil, options \\ []) do
     │                                                             ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/share_settlement_repository.ex:275:61: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.ShareSettlementRepository.get_user_settlement_stats/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 272 │   def increment_channel_registers(channel_id, register_count \\ 1, options \\ []) do
     │                                                                    ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promotion_channel_repository.ex:272:68: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionChannelRepository.increment_channel_registers/3

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 70 │   def get_robot_stats(stats_id, options \\ []) do
    │                                 ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/robot_stats_repository.ex:70:33: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RobotStatsRepository.get_robot_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 304 │   def get_system_settlement_stats(date_range \\ nil, options \\ []) do
     │                                                      ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/share_settlement_repository.ex:304:54: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.ShareSettlementRepository.get_system_settlement_stats/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_coin_stats(stats_data, options \\ []) do
    │                                     ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/coin_stats_repository.ex:35:37: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.CoinStatsRepository.create_coin_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 113 │   def update_robot_stats(robot_stats, update_data, options \\ []) do
     │                                                    ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/robot_stats_repository.ex:113:52: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RobotStatsRepository.update_robot_stats/3

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 70 │   def get_coin_stats(stats_id, options \\ []) do
    │                                ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/coin_stats_repository.ex:70:32: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.CoinStatsRepository.get_coin_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 148 │   def delete_robot_stats(robot_stats, options \\ []) do
     │                                       ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/robot_stats_repository.ex:148:39: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RobotStatsRepository.delete_robot_stats/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_payment_stats(stats_data, options \\ []) do
    │                                        ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/payment_stats_repository.ex:35:40: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.PaymentStatsRepository.create_payment_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 694 │   def system_health_check(options \\ []) do
     │                           ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/promotion_system_service.ex:694:27: RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService.system_health_check/1

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 113 │   def update_coin_stats(coin_stats, update_data, options \\ []) do
     │                                                  ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/coin_stats_repository.ex:113:50: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.CoinStatsRepository.update_coin_stats/3

     warning: variable "share_type" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 758 │   defp validate_user_share_eligibility(user_id, share_type) do
     │                                                 ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/promotion_system_service.ex:758:49: RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService.validate_user_share_eligibility/2

     warning: variable "user_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 758 │   defp validate_user_share_eligibility(user_id, share_type) do
     │                                        ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/promotion_system_service.ex:758:40: RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService.validate_user_share_eligibility/2

     warning: variable "share_data" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 764 │   defp calculate_share_reward(user_id, share_config, share_data) do
     │                                                      ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/promotion_system_service.ex:764:54: RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService.calculate_share_reward/3

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 70 │   def get_payment_stats(stats_id, options \\ []) do
    │                                   ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/payment_stats_repository.ex:70:35: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.PaymentStatsRepository.get_payment_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 148 │   def delete_coin_stats(coin_stats, options \\ []) do
     │                                     ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/coin_stats_repository.ex:148:37: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.CoinStatsRepository.delete_coin_stats/2

     warning: variable "user_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 764 │   defp calculate_share_reward(user_id, share_config, share_data) do
     │                               ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/promotion_system_service.ex:764:31: RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService.calculate_share_reward/3

     warning: variable "settlement" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 779 │     with {:ok, settlement} <- ShareSettlementRepository.get_settlement(settlement_id),
     │                ~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/promotion_system_service.ex:779:16: RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService.process_single_share_settlement/3

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_ltv_stats(stats_data, options \\ []) do
    │                                    ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/ltv_stats_repository.ex:35:36: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.LtvStatsRepository.create_ltv_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 628 │   defp get_system_health_indicators(options) do
     │                                     ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/query_builders/teen/promotion_system_query_builder.ex:628:37: RacingGame.Live.AdminPanel.QueryBuilders.Teen.PromotionSystemQueryBuilder.get_system_health_indicators/1

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 113 │   def update_payment_stats(payment_stats, update_data, options \\ []) do
     │                                                        ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/payment_stats_repository.ex:113:56: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.PaymentStatsRepository.update_payment_stats/3

     warning: variable "promoter" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 830 │   defp calculate_promoter_performance_score(promoter, channels, settlement_stats) do
     │                                             ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/promotion_system_service.ex:830:45: RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService.calculate_promoter_performance_score/3

     warning: variable "promoter" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 843 │   defp generate_promoter_recommendations(promoter, channels, settlement_stats) do
     │                                          ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/promotion_system_service.ex:843:42: RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService.generate_promoter_recommendations/3

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 70 │   def get_ltv_stats(stats_id, options \\ []) do
    │                               ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/ltv_stats_repository.ex:70:31: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.LtvStatsRepository.get_ltv_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 148 │   def delete_payment_stats(payment_stats, options \\ []) do
     │                                           ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/payment_stats_repository.ex:148:43: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.PaymentStatsRepository.delete_payment_stats/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_retention_stats(stats_data, options \\ []) do
    │                                          ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/retention_stats_repository.ex:35:42: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RetentionStatsRepository.create_retention_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 113 │   def update_ltv_stats(ltv_stats, update_data, options \\ []) do
     │                                                ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/ltv_stats_repository.ex:113:48: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.LtvStatsRepository.update_ltv_stats/3

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_channel_stats(stats_data, options \\ []) do
    │                                        ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/channel_stats_repository.ex:35:40: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.ChannelStatsRepository.create_channel_stats/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 70 │   def get_retention_stats(stats_id, options \\ []) do
    │                                     ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/retention_stats_repository.ex:70:37: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RetentionStatsRepository.get_retention_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 148 │   def delete_ltv_stats(ltv_stats, options \\ []) do
     │                                   ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/ltv_stats_repository.ex:148:35: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.LtvStatsRepository.delete_ltv_stats/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_user_stats(stats_data, options \\ []) do
    │                                     ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/user_stats_repository.ex:35:37: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.UserStatsRepository.create_user_stats/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 70 │   def get_channel_stats(stats_id, options \\ []) do
    │                                   ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/channel_stats_repository.ex:70:35: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.ChannelStatsRepository.get_channel_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 113 │   def update_retention_stats(retention_stats, update_data, options \\ []) do
     │                                                            ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/retention_stats_repository.ex:113:60: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RetentionStatsRepository.update_retention_stats/3

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 70 │   def get_user_stats(stats_id, options \\ []) do
    │                                ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/user_stats_repository.ex:70:32: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.UserStatsRepository.get_user_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 148 │   def delete_retention_stats(retention_stats, options \\ []) do
     │                                               ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/retention_stats_repository.ex:148:47: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RetentionStatsRepository.delete_retention_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 113 │   def update_channel_stats(channel_stats, update_data, options \\ []) do
     │                                                        ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/channel_stats_repository.ex:113:56: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.ChannelStatsRepository.update_channel_stats/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 113 │   def update_user_stats(user_stats, update_data, options \\ []) do
     │                                                  ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/user_stats_repository.ex:113:50: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.UserStatsRepository.update_user_stats/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 148 │   def delete_channel_stats(channel_stats, options \\ []) do
     │                                           ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/channel_stats_repository.ex:148:43: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.ChannelStatsRepository.delete_channel_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 148 │   def delete_user_stats(user_stats, options \\ []) do
     │                                     ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/user_stats_repository.ex:148:37: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.UserStatsRepository.delete_user_stats/2

    warning: variable "yesterday" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 91 │       yesterday = Date.add(today, -1)
    │       ~
    │
    └─ lib/racing_game/live/admin_panel/query_builders/teen/data_statistics_query_builder.ex:91:7: RacingGame.Live.AdminPanel.QueryBuilders.Teen.DataStatisticsQueryBuilder.get_realtime_dashboard/1

     warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
     │
 133 │             Logger.warn("⚠️ [数据统计服务] 报告保存失败，但报告生成成功: #{inspect(save_error)}")
     │                    ~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:133:20: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.generate_comprehensive_report/2

     warning: variable "config" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 441 │   defp process_report_data(raw_data, config, _options) do
     │                                      ~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:441:38: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.process_report_data/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 711 │   defp sync_online_statistics(sync_config, options) do
     │                                            ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:711:44: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.sync_online_statistics/2

     warning: variable "sync_config" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 711 │   defp sync_online_statistics(sync_config, options) do
     │                               ~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:711:31: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.sync_online_statistics/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 726 │   defp sync_user_statistics(sync_config, options) do
     │                                          ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:726:42: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.sync_user_statistics/2

     warning: variable "sync_config" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 726 │   defp sync_user_statistics(sync_config, options) do
     │                             ~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:726:29: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.sync_user_statistics/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 739 │   defp sync_payment_statistics(sync_config, options) do
     │                                             ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:739:45: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.sync_payment_statistics/2

     warning: variable "sync_config" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 739 │   defp sync_payment_statistics(sync_config, options) do
     │                                ~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:739:32: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.sync_payment_statistics/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 752 │   defp sync_channel_statistics(sync_config, options) do
     │                                             ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:752:45: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.sync_channel_statistics/2

     warning: variable "sync_config" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 752 │   defp sync_channel_statistics(sync_config, options) do
     │                                ~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:752:32: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.sync_channel_statistics/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 765 │   defp sync_retention_statistics(sync_config, options) do
     │                                               ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:765:47: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.sync_retention_statistics/2

     warning: variable "sync_config" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 765 │   defp sync_retention_statistics(sync_config, options) do
     │                                  ~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:765:34: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.sync_retention_statistics/2

     warning: variable "validation_config" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 790 │   defp check_data_completeness(validation_config, options) do
     │                                ~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:790:32: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.check_data_completeness/2

     warning: variable "validation_config" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 826 │   defp check_data_consistency(validation_config, options) do
     │                               ~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:826:31: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.check_data_consistency/2

     warning: variable "validation_config" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 857 │   defp check_data_accuracy(validation_config, options) do
     │                            ~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:857:28: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.check_data_accuracy/2

     warning: variable "validation_config" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 888 │   defp check_data_timeliness(validation_config, options) do
     │                              ~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:888:30: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.check_data_timeliness/2

     warning: do not use "length(stats) > 0" to check if a list is not empty since length always traverses the whole list. Prefer to pattern match on a non-empty list, such as [_ | _], or use "stats != []" as a guard
     │
 966 │       {:ok, stats} when length(stats) > 0 -> :ok
     │                                       ~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:966:39

     warning: do not use "length(stats) > 0" to check if a list is not empty since length always traverses the whole list. Prefer to pattern match on a non-empty list, such as [_ | _], or use "stats != []" as a guard
     │
 973 │       {:ok, stats} when length(stats) > 0 -> :ok
     │                                       ~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:973:39

     warning: do not use "length(stats) > 0" to check if a list is not empty since length always traverses the whole list. Prefer to pattern match on a non-empty list, such as [_ | _], or use "stats != []" as a guard
     │
 980 │       {:ok, stats} when length(stats) > 0 -> :ok
     │                                       ~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:980:39

     warning: do not use "length(stats) > 0" to check if a list is not empty since length always traverses the whole list. Prefer to pattern match on a non-empty list, such as [_ | _], or use "stats != []" as a guard
     │
 987 │       {:ok, stats} when length(stats) > 0 -> :ok
     │                                       ~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:987:39

     warning: do not use "length(stats) > 0" to check if a list is not empty since length always traverses the whole list. Prefer to pattern match on a non-empty list, such as [_ | _], or use "stats != []" as a guard
     │
 994 │       {:ok, stats} when length(stats) > 0 -> :ok
     │                                       ~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:994:39

      warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
      │
 1090 │   defp create_repair_plan(issue_analysis, config, options) do
      │                                                   ~~~~~~~
      │
      └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:1090:51: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.create_repair_plan/3

      warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
      │
 1381 │   defp create_data_backup(repair_plan, options) do
      │                                        ~~~~~~~
      │
      └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:1381:40: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.create_data_backup/2

      warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
      │
 1451 │   defp validate_repair_results(repair_results, repair_plan, options) do
      │                                                             ~~~~~~~
      │
      └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:1451:61: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.validate_repair_results/3

      warning: variable "repair_plan" is unused (if the variable is not meant to be used, prefix it with an underscore)
      │
 1451 │   defp validate_repair_results(repair_results, repair_plan, options) do
      │                                                ~~~~~~~~~~~
      │
      └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:1451:48: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.validate_repair_results/3

    warning: module attribute @cache_ttl was set but never used
    │
 19 │   @cache_ttl 600_000  # 10分钟缓存
    │   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/payment_stats_repository.ex:19: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.PaymentStatsRepository (module)

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_online_stats(stats_data, options \\ []) do
    │                                       ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/online_stats_repository.ex:35:39: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.OnlineStatsRepository.create_online_stats/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 70 │   def get_online_stats(stats_id, options \\ []) do
    │                                  ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/online_stats_repository.ex:70:34: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.OnlineStatsRepository.get_online_stats/2

    warning: module attribute @cache_ttl was set but never used
    │
 19 │   @cache_ttl 600_000  # 10分钟缓存
    │   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/channel_stats_repository.ex:19: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.ChannelStatsRepository (module)

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_system_report(report_data, options \\ []) do
    │                                         ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/system_report_repository.ex:35:41: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.SystemReportRepository.create_system_report/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 113 │   def update_online_stats(online_stats, update_data, options \\ []) do
     │                                                      ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/online_stats_repository.ex:113:54: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.OnlineStatsRepository.update_online_stats/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 148 │   def delete_online_stats(online_stats, options \\ []) do
     │                                         ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/online_stats_repository.ex:148:41: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.OnlineStatsRepository.delete_online_stats/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 70 │   def get_system_report(report_id, options \\ []) do
    │                                    ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/system_report_repository.ex:70:36: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.SystemReportRepository.get_system_report/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 114 │   def update_system_report(system_report, update_data, options \\ []) do
     │                                                        ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/system_report_repository.ex:114:56: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.SystemReportRepository.update_system_report/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 149 │   def delete_system_report(system_report, options \\ []) do
     │                                           ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/system_report_repository.ex:149:43: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.SystemReportRepository.delete_system_report/2

    warning: module attribute @cache_ttl was set but never used
    │
 19 │   @cache_ttl 600_000  # 10分钟缓存
    │   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/ltv_stats_repository.ex:19: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.LtvStatsRepository (module)

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 264 │   def analyze_user_activity_participation(user_id, options \\ []) do
     │                                                    ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/query_builders/teen/activity_system_query_builder.ex:264:52: RacingGame.Live.AdminPanel.QueryBuilders.Teen.ActivitySystemQueryBuilder.analyze_user_activity_participation/2

    warning: module attribute @cache_ttl was set but never used
    │
 19 │   @cache_ttl 600_000  # 10分钟缓存
    │   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/robot_stats_repository.ex:19: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RobotStatsRepository (module)

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 601 │   defp do_generate_cdkey_batch(batch_data, count, options) do
     │                                                   ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/activity_system_service.ex:601:51: RacingGame.Live.AdminPanel.Services.Teen.ActivitySystemService.do_generate_cdkey_batch/3

    warning: module attribute @cache_ttl was set but never used
    │
 19 │   @cache_ttl 600_000  # 10分钟缓存
    │   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/coin_stats_repository.ex:19: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.CoinStatsRepository (module)

     warning: variable "error" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 106 │   defp handle_save_exception(socket, error) do
     │                                      ~~~~~
     │
     └─ lib/racing_game/live/admin_panel/handlers/data_event_handler.ex:106:38: RacingGame.Live.AdminPanel.Handlers.DataEventHandler.handle_save_exception/2

    warning: module attribute @cache_ttl was set but never used
    │
 19 │   @cache_ttl 600_000  # 10分钟缓存
    │   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/retention_stats_repository.ex:19: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RetentionStatsRepository (module)

    warning: module attribute @cache_ttl was set but never used
    │
 19 │   @cache_ttl 600_000  # 10分钟缓存
    │   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/user_stats_repository.ex:19: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.UserStatsRepository (module)

    warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
    │
 61 │           Logger.warn("⚠️ [限时礼包仓储] 限时礼包不存在: #{gift_id}")
    │                  ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/limited_gift_repository.ex:61:18: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.LimitedGiftRepository.get_limited_gift/2

    warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
    │
 61 │           Logger.warn("⚠️ [Free Bonus仓储] Free Bonus不存在: #{bonus_id}")
    │                  ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/free_bonus_repository.ex:61:18: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.FreeBonusRepository.get_free_bonus/2

     warning: default values for the optional arguments in truncate_text/2 are never used
     │
 503 │   defp truncate_text(text, max_length \\ @max_content_preview_length) do
     │        ~
     │
     └─ lib/racing_game/live/admin_panel/handlers/data_event_handler.ex:503:8: RacingGame.Live.AdminPanel.Handlers.DataEventHandler (module)

     error: misplaced operator ^vip_level

     The pin operator ^ is supported only inside matches or inside custom macros. Make sure you are inside a match or all necessary macros have been required
     │
 311 │     Ash.Query.filter(query, is_nil(vip_level) or vip_level <= ^vip_level)
     │                                                               ^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/free_bonus_repository.ex:311:63: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.FreeBonusRepository.maybe_filter_by_vip_level/2

     error: misplaced operator ^level

     The pin operator ^ is supported only inside matches or inside custom macros. Make sure you are inside a match or all necessary macros have been required
     │
 305 │     Ash.Query.filter(query, min_level <= ^level)
     │                                          ^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/free_bonus_repository.ex:305:42: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.FreeBonusRepository.maybe_filter_by_level/2

     error: undefined variable "min_level"
     │
 305 │     Ash.Query.filter(query, min_level <= ^level)
     │                             ^^^^^^^^^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/free_bonus_repository.ex:305:29: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.FreeBonusRepository.maybe_filter_by_level/2

     error: misplaced operator ^status

     The pin operator ^ is supported only inside matches or inside custom macros. Make sure you are inside a match or all necessary macros have been required
     │
 299 │     Ash.Query.filter(query, status == ^status)
     │                                       ^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/free_bonus_repository.ex:299:39: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.FreeBonusRepository.maybe_filter_by_status/2

     error: misplaced operator ^game_type

     The pin operator ^ is supported only inside matches or inside custom macros. Make sure you are inside a match or all necessary macros have been required
     │
 293 │     Ash.Query.filter(query, game_type == ^game_type)
     │                                          ^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/free_bonus_repository.ex:293:42: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.FreeBonusRepository.maybe_filter_by_game_type/2

     error: misplaced operator ^name

     The pin operator ^ is supported only inside matches or inside custom macros. Make sure you are inside a match or all necessary macros have been required
     │
 287 │     Ash.Query.filter(query, contains(bonus_name, ^name))
     │                                                  ^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/free_bonus_repository.ex:287:50: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.FreeBonusRepository.maybe_filter_by_name/2

     error: undefined variable "bonus_name"
     │
 287 │     Ash.Query.filter(query, contains(bonus_name, ^name))
     │                                      ^^^^^^^^^^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/free_bonus_repository.ex:287:38: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.FreeBonusRepository.maybe_filter_by_name/2

     error: misplaced operator ^level

     The pin operator ^ is supported only inside matches or inside custom macros. Make sure you are inside a match or all necessary macros have been required
     │
 340 │     Ash.Query.filter(query, is_nil(required_level) or required_level <= ^level)
     │                                                                         ^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/limited_gift_repository.ex:340:73: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.LimitedGiftRepository.maybe_filter_by_level/2

     error: undefined variable "required_level"
     │
 340 │     Ash.Query.filter(query, is_nil(required_level) or required_level <= ^level)
     │                                                       ^^^^^^^^^^^^^^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/limited_gift_repository.ex:340:55: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.LimitedGiftRepository.maybe_filter_by_level/2

     error: undefined variable "required_level"
     │
 340 │     Ash.Query.filter(query, is_nil(required_level) or required_level <= ^level)
     │                                    ^^^^^^^^^^^^^^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/limited_gift_repository.ex:340:36: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.LimitedGiftRepository.maybe_filter_by_level/2

     error: misplaced operator ^end_time

     The pin operator ^ is supported only inside matches or inside custom macros. Make sure you are inside a match or all necessary macros have been required
     │
 334 │     Ash.Query.filter(query, start_time >= ^start_time and end_time <= ^end_time)
     │                                                                       ^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/limited_gift_repository.ex:334:71: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.LimitedGiftRepository.maybe_filter_by_time_range/2

     error: misplaced operator ^start_time

     The pin operator ^ is supported only inside matches or inside custom macros. Make sure you are inside a match or all necessary macros have been required
     │
 334 │     Ash.Query.filter(query, start_time >= ^start_time and end_time <= ^end_time)
     │                                           ^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/limited_gift_repository.ex:334:43: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.LimitedGiftRepository.maybe_filter_by_time_range/2

     error: misplaced operator ^status

     The pin operator ^ is supported only inside matches or inside custom macros. Make sure you are inside a match or all necessary macros have been required
     │
 328 │     Ash.Query.filter(query, status == ^status)
     │                                       ^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/limited_gift_repository.ex:328:39: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.LimitedGiftRepository.maybe_filter_by_status/2

     error: misplaced operator ^gift_type

     The pin operator ^ is supported only inside matches or inside custom macros. Make sure you are inside a match or all necessary macros have been required
     │
 322 │     Ash.Query.filter(query, gift_type == ^gift_type)
     │                                          ^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/limited_gift_repository.ex:322:42: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.LimitedGiftRepository.maybe_filter_by_type/2

     error: misplaced operator ^name

     The pin operator ^ is supported only inside matches or inside custom macros. Make sure you are inside a match or all necessary macros have been required
     │
 316 │     Ash.Query.filter(query, contains(gift_name, ^name))
     │                                                 ^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/limited_gift_repository.ex:316:49: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.LimitedGiftRepository.maybe_filter_by_name/2

     error: undefined variable "gift_name"
     │
 316 │     Ash.Query.filter(query, contains(gift_name, ^name))
     │                                      ^^^^^^^^^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/limited_gift_repository.ex:316:38: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.LimitedGiftRepository.maybe_filter_by_name/2

     error: misplaced operator ^assist_id

     The pin operator ^ is supported only inside matches or inside custom macros. Make sure you are inside a match or all necessary macros have been required
     │
 138 │       |> Ash.Query.filter(id == ^assist_id)
     │                                 ^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/activity_system_repositories.ex:138:33: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.ActivitySystemRepositories.get_bankruptcy_assist/2

     error: undefined variable "id"
     │
 138 │       |> Ash.Query.filter(id == ^assist_id)
     │                           ^^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/activity_system_repositories.ex:138:27: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.ActivitySystemRepositories.get_bankruptcy_assist/2

    error: misplaced operator ^bonus_id

    The pin operator ^ is supported only inside matches or inside custom macros. Make sure you are inside a match or all necessary macros have been required
    │
 55 │       |> Ash.Query.filter(id == ^bonus_id)
    │                                 ^
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/free_bonus_repository.ex:55:33: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.FreeBonusRepository.get_free_bonus/2

    error: undefined variable "id"
    │
 55 │       |> Ash.Query.filter(id == ^bonus_id)
    │                           ^^
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/free_bonus_repository.ex:55:27: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.FreeBonusRepository.get_free_bonus/2

    error: misplaced operator ^cash_id

    The pin operator ^ is supported only inside matches or inside custom macros. Make sure you are inside a match or all necessary macros have been required
    │
 55 │       |> Ash.Query.filter(id == ^cash_id)
    │                                 ^
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/activity_system_repositories.ex:55:33: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.ActivitySystemRepositories.get_free_cash/2

    error: undefined variable "id"
    │
 55 │       |> Ash.Query.filter(id == ^cash_id)
    │                           ^^
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/activity_system_repositories.ex:55:27: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.ActivitySystemRepositories.get_free_cash/2

    error: misplaced operator ^gift_id

    The pin operator ^ is supported only inside matches or inside custom macros. Make sure you are inside a match or all necessary macros have been required
    │
 55 │       |> Ash.Query.filter(id == ^gift_id)
    │                                 ^
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/limited_gift_repository.ex:55:33: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.LimitedGiftRepository.get_limited_gift/2

    error: undefined variable "id"
    │
 55 │       |> Ash.Query.filter(id == ^gift_id)
    │                           ^^
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/limited_gift_repository.ex:55:27: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.LimitedGiftRepository.get_limited_gift/2


== Compilation error in file lib/racing_game/live/admin_panel/repositories/teen/activity_system/free_bonus_repository.ex ==
** (CompileError) lib/racing_game/live/admin_panel/repositories/teen/activity_system/free_bonus_repository.ex: cannot compile module RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.FreeBonusRepository (errors have been logged)

