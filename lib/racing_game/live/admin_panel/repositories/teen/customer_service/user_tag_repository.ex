defmodule RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.UserTagRepository do
  @moduledoc """
  用户标签数据访问仓储

  提供用户标签相关的数据访问抽象：
  - 标签管理
  - 用户标记
  - 标签统计
  - 标签分析
  """

  require Logger
  alias Ash.Query
  import Ash.Query
  alias Teen.CustomerService.UserTag
  require Ash.Query

  # 常量定义
  @default_limit 20
  @max_limit 100
  @cache_ttl 600  # 10分钟缓存

  # ============================================================================
  # 标签管理
  # ============================================================================

  @doc """
  根据ID获取用户标签

  ## 参数
  - `tag_id` - 标签ID
  - `options` - 选项

  ## 返回
  - `{:ok, tag}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_tag_by_id(tag_id, options \\ []) do
    Logger.debug("🏷️ [用户标签仓储] 获取标签: #{tag_id}")

    cache_key = build_cache_key("tag", tag_id, options[:preload])

    case get_from_cache(cache_key) do
      :miss ->
        fetch_and_cache_tag_by_id(tag_id, options[:preload] || [], cache_key)
      {:hit, tag} ->
        Logger.debug("💾 [用户标签仓储] 缓存命中: #{tag_id}")
        {:ok, tag}
    end
  end

  @doc """
  创建用户标签

  ## 参数
  - `tag_data` - 标签数据

  ## 返回
  - `{:ok, tag}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_tag(tag_data) do
    Logger.info("📝 [用户标签仓储] 创建标签: 用户#{tag_data[:user_id]}")

    try do
      case UserTag.create(tag_data) do
        {:ok, tag} ->
          clear_cache(:tag, nil)
          Logger.info("✅ [用户标签仓储] 标签创建成功: #{tag.id}")
          {:ok, tag}
        {:error, error} ->
          Logger.error("❌ [用户标签仓储] 创建标签失败: #{inspect(error)}")
          {:error, :create_failed}
      end
    rescue
      error ->
        Logger.error("❌ [用户标签仓储] 创建标签异常: #{inspect(error)}")
        {:error, :create_error}
    end
  end

  @doc """
  更新用户标签

  ## 参数
  - `tag_id` - 标签ID
  - `update_data` - 更新数据

  ## 返回
  - `{:ok, tag}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_tag(tag_id, update_data) do
    Logger.info("📝 [用户标签仓储] 更新标签: #{tag_id}")

    with {:ok, tag} <- get_tag_by_id(tag_id),
         {:ok, updated_tag} <- do_update_tag(tag, update_data) do

      clear_cache(:tag, tag_id)
      Logger.info("✅ [用户标签仓储] 标签更新成功: #{tag_id}")
      {:ok, updated_tag}
    else
      error -> error
    end
  end

  @doc """
  删除用户标签

  ## 参数
  - `tag_id` - 标签ID

  ## 返回
  - `{:ok, tag}` - 成功
  - `{:error, reason}` - 失败
  """
  def delete_tag(tag_id) do
    Logger.info("🗑️ [用户标签仓储] 删除标签: #{tag_id}")

    with {:ok, tag} <- get_tag_by_id(tag_id),
         {:ok, deleted_tag} <- do_delete_tag(tag) do

      clear_cache(:tag, tag_id)
      Logger.info("✅ [用户标签仓储] 标签删除成功: #{tag_id}")
      {:ok, deleted_tag}
    else
      error -> error
    end
  end

  # ============================================================================
  # 标签查询
  # ============================================================================

  @doc """
  分页获取用户标签列表

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, %{items: tags, page_info: page_info}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_tags_paginated(params \\ %{}) do
    Logger.debug("📋 [用户标签仓储] 分页获取标签列表")

    normalized_params = normalize_list_params(params)
    cache_key = build_cache_key("tags_list", :paginated, normalized_params)

    case get_from_cache(cache_key) do
      :miss ->
        fetch_and_cache_tags_list(normalized_params, cache_key)
      {:hit, result} ->
        Logger.debug("💾 [用户标签仓储] 缓存命中: 标签列表")
        {:ok, result}
    end
  end

  @doc """
  获取用户的所有标签

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - `{:ok, tags}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_tags(user_id, options \\ []) do
    Logger.debug("👤 [用户标签仓储] 获取用户标签: #{user_id}")

    cache_key = build_cache_key("user_tags", user_id, options)

    case get_from_cache(cache_key) do
      :miss ->
        fetch_and_cache_user_tags(user_id, options, cache_key)
      {:hit, tags} ->
        Logger.debug("💾 [用户标签仓储] 缓存命中: 用户标签")
        {:ok, tags}
    end
  end

  @doc """
  根据标签类型获取标签

  ## 参数
  - `tag_type` - 标签类型
  - `options` - 选项

  ## 返回
  - `{:ok, tags}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_tags_by_type(tag_type, options \\ []) do
    Logger.debug("🏷️ [用户标签仓储] 根据类型获取标签: #{tag_type}")

    try do
      query = UserTag
      |> Ash.Query.filter(tag_type == tag_type)
      |> maybe_apply_user_filter(options[:user_id])
      |> apply_sort(options[:sort] || [inserted_at: :desc])
      |> maybe_limit(options[:limit])

      case Ash.read(query) do
        {:ok, tags} -> {:ok, tags}
        {:error, error} ->
          Logger.error("❌ [用户标签仓储] 获取类型标签失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [用户标签仓储] 获取类型标签异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  根据标签名称获取标签

  ## 参数
  - `tag_name` - 标签名称
  - `options` - 选项

  ## 返回
  - `{:ok, tags}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_tags_by_name(tag_name, options \\ []) do
    Logger.debug("🔍 [用户标签仓储] 根据名称获取标签: #{tag_name}")

    try do
      query = UserTag
      |> Ash.Query.filter(tag_name == tag_name)
      |> maybe_apply_user_filter(options[:user_id])
      |> maybe_apply_tag_type_filter(options[:tag_type])
      |> apply_sort(options[:sort] || [inserted_at: :desc])
      |> maybe_limit(options[:limit])

      case Ash.read(query) do
        {:ok, tags} -> {:ok, tags}
        {:error, error} ->
          Logger.error("❌ [用户标签仓储] 获取名称标签失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [用户标签仓储] 获取名称标签异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  检查用户是否有特定标签

  ## 参数
  - `user_id` - 用户ID
  - `tag_name` - 标签名称
  - `tag_type` - 标签类型（可选）

  ## 返回
  - `{:ok, boolean}` - 成功
  - `{:error, reason}` - 失败
  """
  def user_has_tag?(user_id, tag_name, tag_type \\ nil) do
    Logger.debug("❓ [用户标签仓储] 检查用户标签: #{user_id} - #{tag_name}")

    try do
      query = UserTag
      |> Ash.Query.filter(user_id == user_id and tag_name == tag_name)
      |> maybe_apply_tag_type_filter(tag_type)

      case Ash.read_one(query) do
        {:ok, tag} when not is_nil(tag) -> {:ok, true}
        {:ok, nil} -> {:ok, false}
        {:error, error} ->
          Logger.error("❌ [用户标签仓储] 检查标签失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [用户标签仓储] 检查标签异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  为用户添加标签

  ## 参数
  - `user_id` - 用户ID
  - `tag_name` - 标签名称
  - `tag_type` - 标签类型
  - `options` - 选项

  ## 返回
  - `{:ok, tag}` - 成功
  - `{:error, reason}` - 失败
  """
  def add_user_tag(user_id, tag_name, tag_type, options \\ []) do
    Logger.info("➕ [用户标签仓储] 添加用户标签: #{user_id} - #{tag_name}")

    # 检查是否已存在
    case user_has_tag?(user_id, tag_name, tag_type) do
      {:ok, true} ->
        Logger.warn("⚠️ [用户标签仓储] 标签已存在: #{user_id} - #{tag_name}")
        {:error, :tag_already_exists}
      {:ok, false} ->
        tag_data = %{
          user_id: user_id,
          tag_name: tag_name,
          tag_type: tag_type,
          tag_value: options[:tag_value],
          notes: options[:notes],
          created_by: options[:created_by]
        }
        create_tag(tag_data)
      error -> error
    end
  end

  @doc """
  移除用户标签

  ## 参数
  - `user_id` - 用户ID
  - `tag_name` - 标签名称
  - `tag_type` - 标签类型（可选）

  ## 返回
  - `{:ok, tag}` - 成功
  - `{:error, reason}` - 失败
  """
  def remove_user_tag(user_id, tag_name, tag_type \\ nil) do
    Logger.info("➖ [用户标签仓储] 移除用户标签: #{user_id} - #{tag_name}")

    try do
      query = UserTag
      |> Ash.Query.filter(user_id == user_id and tag_name == tag_name)
      |> maybe_apply_tag_type_filter(tag_type)

      case Ash.read_one(query) do
        {:ok, tag} when not is_nil(tag) ->
          delete_tag(tag.id)
        {:ok, nil} ->
          Logger.warn("⚠️ [用户标签仓储] 标签不存在: #{user_id} - #{tag_name}")
          {:error, :tag_not_found}
        {:error, error} ->
          Logger.error("❌ [用户标签仓储] 查找标签失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [用户标签仓储] 移除标签异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  批量添加用户标签

  ## 参数
  - `user_id` - 用户ID
  - `tags_data` - 标签数据列表

  ## 返回
  - `{:ok, results}` - 成功
  - `{:error, reason}` - 失败
  """
  def batch_add_user_tags(user_id, tags_data) do
    Logger.info("📝 [用户标签仓储] 批量添加用户标签: #{user_id} - #{length(tags_data)}个")

    results = Enum.map(tags_data, fn tag_data ->
      full_tag_data = Map.put(tag_data, :user_id, user_id)
      case create_tag(full_tag_data) do
        {:ok, tag} -> {:ok, tag}
        {:error, reason} -> {:error, {tag_data[:tag_name], reason}}
      end
    end)

    success_count = Enum.count(results, fn {status, _} -> status == :ok end)
    Logger.info("✅ [用户标签仓储] 批量添加完成: #{success_count}/#{length(tags_data)}")

    {:ok, results}
  end

  # ============================================================================
  # 统计查询
  # ============================================================================

  @doc """
  获取标签统计

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, statistics}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_tag_statistics(options \\ []) do
    Logger.debug("📊 [用户标签仓储] 获取标签统计")

    try do
      stats = %{
        total_tags: get_total_tags(),
        unique_users: get_unique_users_count(),
        tags_by_type: get_tags_by_type_count(),
        tags_by_name: get_tags_by_name_count(),
        recent_tags: get_recent_tags_count(options[:days] || 7)
      }

      {:ok, stats}
    rescue
      error ->
        Logger.error("❌ [用户标签仓储] 获取统计异常: #{inspect(error)}")
        {:error, :statistics_error}
    end
  end

  # ============================================================================
  # 缓存管理
  # ============================================================================

  @doc """
  清除标签相关缓存

  ## 参数
  - `cache_type` - 缓存类型
  - `id` - ID（可选）
  """
  def clear_cache(cache_type, id \\ nil) do
    cache_patterns = case {cache_type, id} do
      {:tag, nil} -> ["tag:*"]
      {:tag, tag_id} -> ["tag:#{tag_id}:*"]
      {:user_tags, user_id} -> ["user_tags:#{user_id}:*"]
      {:all, _} -> ["tag:*", "user_tags:*", "tags_list:*"]
    end

    Logger.debug("🧹 [用户标签仓储] 清除缓存: #{inspect(cache_patterns)}")
    Enum.each(cache_patterns, &clear_cache_pattern/1)
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 标准化列表参数
  defp normalize_list_params(params) do
    %{
      page: max(1, params[:page] || 1),
      limit: min(params[:limit] || @default_limit, @max_limit),
      search: String.trim(params[:search] || ""),
      filters: params[:filters] || %{},
      sort: params[:sort] || [inserted_at: :desc],
      preload: params[:preload] || []
    }
  end

  # 获取并缓存标签
  defp fetch_and_cache_tag_by_id(tag_id, preload, cache_key) do
    case fetch_tag_by_id(tag_id, preload) do
      {:ok, tag} ->
        cache_put(cache_key, tag, @cache_ttl)
        {:ok, tag}
      error -> error
    end
  end

  # 获取标签
  defp fetch_tag_by_id(tag_id, preload) do
    try do
      case UserTag
           |> maybe_preload(preload)
           |> Ash.get(tag_id) do
        {:ok, tag} -> {:ok, tag}
        {:error, %Ash.Error.Query.NotFound{}} -> {:error, :not_found}
        {:error, error} ->
          Logger.error("❌ [用户标签仓储] 获取标签失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [用户标签仓储] 获取标签异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # 执行更新标签
  defp do_update_tag(tag, update_data) do
    try do
      case tag
           |> Ash.Changeset.for_update(:update, update_data)
           |> Ash.update() do
        {:ok, updated_tag} -> {:ok, updated_tag}
        {:error, error} ->
          Logger.error("❌ [用户标签仓储] 更新标签失败: #{inspect(error)}")
          {:error, :update_failed}
      end
    rescue
      error ->
        Logger.error("❌ [用户标签仓储] 更新标签异常: #{inspect(error)}")
        {:error, :update_error}
    end
  end

  # 执行删除标签
  defp do_delete_tag(tag) do
    try do
      case Ash.destroy(tag) do
        :ok -> {:ok, tag}
        {:error, error} ->
          Logger.error("❌ [用户标签仓储] 删除标签失败: #{inspect(error)}")
          {:error, :delete_failed}
      end
    rescue
      error ->
        Logger.error("❌ [用户标签仓储] 删除标签异常: #{inspect(error)}")
        {:error, :delete_error}
    end
  end

  # 获取并缓存标签列表
  defp fetch_and_cache_tags_list(params, cache_key) do
    case fetch_tags_list(params) do
      {:ok, result} ->
        cache_put(cache_key, result, @cache_ttl)
        {:ok, result}
      error -> error
    end
  end

  # 获取标签列表
  defp fetch_tags_list(params) do
    try do
      query = UserTag
      |> apply_tag_filters(params.filters)
      |> apply_tag_search(params.search)
      |> apply_sort(params.sort)

      execute_paginated_query(query, params)
    rescue
      error ->
        Logger.error("❌ [用户标签仓储] 获取标签列表异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # 获取并缓存用户标签
  defp fetch_and_cache_user_tags(user_id, options, cache_key) do
    case fetch_user_tags(user_id, options) do
      {:ok, tags} ->
        cache_put(cache_key, tags, @cache_ttl)
        {:ok, tags}
      error -> error
    end
  end

  # 获取用户标签
  defp fetch_user_tags(user_id, options) do
    try do
      query = UserTag
      |> Ash.Query.filter(user_id == user_id)
      |> maybe_apply_tag_type_filter(options[:tag_type])
      |> maybe_apply_tag_name_filter(options[:tag_name])
      |> apply_sort(options[:sort] || [inserted_at: :desc])
      |> maybe_limit(options[:limit])

      case Ash.read(query) do
        {:ok, tags} -> {:ok, tags}
        {:error, error} ->
          Logger.error("❌ [用户标签仓储] 获取用户标签失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [用户标签仓储] 获取用户标签异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # 应用标签过滤器
  defp apply_tag_filters(query, filters) when map_size(filters) == 0, do: query
  defp apply_tag_filters(query, filters) do
    Enum.reduce(filters, query, fn {key, value}, acc_query ->
      apply_tag_filter(acc_query, key, value)
    end)
  end

  # 应用单个标签过滤器
  defp apply_tag_filter(query, :user_id, user_id) when not is_nil(user_id) do
    Ash.Query.filter(query, user_id  == user_id)
  end
  defp apply_tag_filter(query, :tag_type, tag_type) when not is_nil(tag_type) do
    Ash.Query.filter(query, tag_type  == tag_type)
  end
  defp apply_tag_filter(query, :tag_name, tag_name) when not is_nil(tag_name) do
    Ash.Query.filter(query, tag_name  == tag_name)
  end
  defp apply_tag_filter(query, :created_by, created_by) when not is_nil(created_by) do
    Ash.Query.filter(query, created_by  == created_by)
  end
  defp apply_tag_filter(query, _key, _value), do: query

  # 应用标签搜索
  defp apply_tag_search(query, search) when search == "" or is_nil(search), do: query
  defp apply_tag_search(query, search) do
    search_term = "%#{search}%"
    Ash.Query.filter(query, ilike(tag_name, search_term) or ilike(notes, search_term))
  end

  # 执行分页查询
  defp execute_paginated_query(query, params) do
    offset = (params.page - 1) * params.limit

    case query
         |> maybe_preload(params.preload)
         |> Ash.Query.page(count: true, limit: params.limit, offset: offset)
         |> Ash.read() do
      {:ok, %Ash.Page.Offset{results: items, count: total_count}} ->
        page_info = %{
          total_count: total_count,
          page: params.page,
          limit: params.limit,
          total_pages: ceil(total_count / params.limit)
        }
        {:ok, %{items: items, page_info: page_info}}
      {:error, error} ->
        Logger.error("❌ [用户标签仓储] 分页查询失败: #{inspect(error)}")
        {:error, :query_failed}
    end
  end

  # 统计函数
  defp get_total_tags do
    case UserTag |> Ash.Query.aggregate(:count, :id) |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_unique_users_count do
    case UserTag
         |> Ash.Query.aggregate(:count, :user_id, distinct: true)
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_tags_by_type_count do
    # 这里需要根据实际需求实现类型分组统计
    %{}
  end

  defp get_tags_by_name_count do
    # 这里需要根据实际需求实现名称分组统计
    %{}
  end

  defp get_recent_tags_count(days) do
    date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)

    case UserTag
         |> Ash.Query.filter(inserted_at >= date_threshold)
         |> Ash.Query.aggregate(:count, :id)
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  # 可能预加载关联数据
  defp maybe_preload(query, []), do: query
  defp maybe_preload(query, preload) when is_list(preload) do
    Ash.Query.load(query, preload)
  end

  # 可能限制数量
  defp maybe_limit(query, nil), do: query
  defp maybe_limit(query, limit) when is_integer(limit) and limit > 0 do
    Ash.Query.limit(query, limit)
  end
  defp maybe_limit(query, _), do: query

  # 应用排序
  defp apply_sort(query, sort) when is_list(sort) do
    Ash.Query.sort(query, sort)
  end
  defp apply_sort(query, _), do: query

  # 可能应用用户过滤
  defp maybe_apply_user_filter(query, nil), do: query
  defp maybe_apply_user_filter(query, user_id) do
    Ash.Query.filter(query, user_id  == user_id)
  end

  # 可能应用标签类型过滤
  defp maybe_apply_tag_type_filter(query, nil), do: query
  defp maybe_apply_tag_type_filter(query, tag_type) do
    Ash.Query.filter(query, tag_type  == tag_type)
  end

  # 可能应用标签名称过滤
  defp maybe_apply_tag_name_filter(query, nil), do: query
  defp maybe_apply_tag_name_filter(query, tag_name) do
    Ash.Query.filter(query, tag_name  == tag_name)
  end

  # 构建缓存键
  defp build_cache_key(prefix, id, extra \\ nil) do
    case extra do
      nil -> "#{prefix}:#{id}"
      extra -> "#{prefix}:#{id}:#{:erlang.phash2(extra)}"
    end
  end

  # 缓存操作（需要实现具体的缓存后端）
  defp get_from_cache(_key), do: :miss
  defp cache_put(_key, _value, _ttl), do: :ok
  defp clear_cache_pattern(_pattern), do: :ok
end
