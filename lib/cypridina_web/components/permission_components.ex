defmodule CypridinaWeb.PermissionComponents do
  @moduledoc """
  权限控制组件

  提供基于用户权限的条件渲染组件，用于在模板中根据用户权限显示不同内容。
  """

  use Phoenix.Component
  alias CypridinaWeb.AuthHelper

  @doc """
  根据权限条件渲染内容

  ## 属性
  - `user`: 当前用户
  - `permission`: 需要的权限（原子或字符串）
  - `inner_block`: 要渲染的内容

  ## 示例
  ```heex
  <.permission_required user={@current_user} permission={:admin}>
    <button>管理员专用按钮</button>
  </.permission_required>
  ```
  """
  attr :user, :any, required: true, doc: "当前用户"
  attr :permission, :any, required: true, doc: "需要的权限"
  slot :inner_block, required: true

  def permission_required(assigns) do
    if AuthHelper.has_permission?(assigns.user, assigns.permission) do
      ~H"""
      {render_slot(@inner_block)}
      """
    else
      ~H""
    end
  end

  @doc """
  根据用户角色渲染内容

  ## 属性
  - `user`: 当前用户
  - `roles`: 允许的角色列表
  - `inner_block`: 要渲染的内容

  ## 示例
  ```heex
  <.role_required user={@current_user} roles={[:admin, :super_admin]}>
    <button>管理员和超级管理员可见</button>
  </.role_required>
  ```
  """
  attr :user, :any, required: true, doc: "当前用户"
  attr :roles, :list, required: true, doc: "允许的角色列表"
  slot :inner_block, required: true

  def role_required(assigns) do
    user_role = AuthHelper.get_user_role(assigns.user)

    if user_role in assigns.roles do
      ~H"""
      {render_slot(@inner_block)}
      """
    else
      ~H""
    end
  end

  @doc """
  根据用户是否可以管理目标用户来渲染内容

  ## 属性
  - `user`: 当前用户
  - `target_user_id`: 目标用户ID
  - `inner_block`: 要渲染的内容

  ## 示例
  ```heex
  <.can_manage user={@current_user} target_user_id={@target_user.id}>
    <button>编辑用户</button>
  </.can_manage>
  ```
  """
  attr :user, :any, required: true, doc: "当前用户"
  attr :target_user_id, :string, required: true, doc: "目标用户ID"
  slot :inner_block, required: true

  def can_manage(assigns) do
    if AuthHelper.can_manage_user?(assigns.user, assigns.target_user_id) do
      ~H"""
      {render_slot(@inner_block)}
      """
    else
      ~H""
    end
  end

  @doc """
  权限级别徽章组件（支持权限覆盖显示）

  ## 属性
  - `user`: 用户
  - `class`: 额外的CSS类
  - `show_conflicts`: 是否显示权限冲突信息

  ## 示例
  ```heex
  <.permission_badge user={@current_user} show_conflicts={true} />
  ```
  """
  attr :user, :any, required: true, doc: "用户"
  attr :class, :string, default: "", doc: "额外的CSS类"
  attr :show_conflicts, :boolean, default: false, doc: "是否显示权限冲突信息"

  def permission_badge(assigns) do
    role = AuthHelper.get_user_role(assigns.user)
    level_name = AuthHelper.get_permission_level_name(assigns.user)
    conflict_info = AuthHelper.check_permission_conflicts(assigns.user)

    {badge_class, icon} =
      case role do
        :super_admin -> {"bg-red-100 text-red-800", "👑"}
        :admin -> {"bg-blue-100 text-blue-800", "🛡️"}
        :root_agent -> {"bg-green-100 text-green-800", "🌟"}
        :agent -> {"bg-yellow-100 text-yellow-800", "⭐"}
        :user -> {"bg-gray-100 text-gray-800", "👤"}
      end

    # 检查是否有权限冲突
    has_conflict = match?({:conflict, _, _}, conflict_info)

    # 如果有权限冲突，调整显示
    {final_badge_class, final_level_name} =
      case conflict_info do
        {:conflict, :admin_overrides_agent, _details} when assigns.show_conflicts ->
          {"bg-purple-100 text-purple-800 border border-purple-300", "#{level_name}*"}

        _ ->
          {badge_class, level_name}
      end

    assigns =
      assign(assigns,
        badge_class: final_badge_class,
        icon: icon,
        level_name: final_level_name,
        has_conflict: has_conflict,
        conflict_info: conflict_info
      )

    ~H"""
    <div class="relative inline-block">
      <span class={"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium #{@badge_class} #{@class}"}>
        <span class="mr-1">{@icon}</span>
        {@level_name}
      </span>

    <!-- 权限冲突提示 -->
      <div
        :if={@has_conflict and @show_conflicts}
        class="absolute z-10 w-64 p-2 mt-1 text-xs bg-yellow-50 border border-yellow-200 rounded-md shadow-lg"
      >
        <div class="font-medium text-yellow-800">权限覆盖提示</div>
        <div class="mt-1 text-yellow-700">
          <%= case @conflict_info do %>
            <% {:conflict, :admin_overrides_agent, details} -> %>
              {details.message}
            <% _ -> %>
              存在权限冲突
          <% end %>
        </div>
      </div>
    </div>
    """
  end

  @doc """
  权限说明组件（支持权限覆盖显示）

  ## 属性
  - `user`: 当前用户
  - `class`: 额外的CSS类
  - `show_details`: 是否显示详细信息

  ## 示例
  ```heex
  <.permission_info user={@current_user} show_details={true} />
  ```
  """
  attr :user, :any, required: true, doc: "当前用户"
  attr :class, :string, default: "", doc: "额外的CSS类"
  attr :show_details, :boolean, default: true, doc: "是否显示详细信息"

  def permission_info(assigns) do
    role = AuthHelper.get_user_role(assigns.user)
    level_name = AuthHelper.get_permission_level_name(assigns.user)
    full_identity = AuthHelper.get_full_identity_description(assigns.user)
    effective_scope = AuthHelper.get_effective_permission_scope(assigns.user)
    conflict_info = AuthHelper.check_permission_conflicts(assigns.user)

    permissions = get_permission_list(assigns.user)

    assigns =
      assign(assigns,
        role: role,
        level_name: level_name,
        full_identity: full_identity,
        effective_scope: effective_scope,
        conflict_info: conflict_info,
        permissions: permissions
      )

    ~H"""
    <div class={"bg-gray-50 p-4 rounded-lg #{@class}"}>
      <h3 class="text-sm font-medium text-gray-900 mb-2">权限信息</h3>
      <div class="space-y-3">
        <div class="flex items-center">
          <span class="text-sm text-gray-600">当前角色：</span>
          <.permission_badge user={@user} show_conflicts={true} class="ml-2" />
        </div>

    <!-- 完整身份描述 -->
        <div :if={@show_details} class="text-sm text-gray-600">
          <span class="font-medium">完整身份：</span>
          <span>{@full_identity}</span>
        </div>

    <!-- 有效权限范围 -->
        <div :if={@show_details} class="text-sm text-gray-600">
          <span class="font-medium">有效权限：</span>
          <span>{@effective_scope}</span>
        </div>

    <!-- 权限冲突警告 -->
        <div
          :if={match?({:conflict, _, _}, @conflict_info)}
          class="p-2 bg-yellow-50 border border-yellow-200 rounded-md"
        >
          <div class="flex items-start">
            <svg class="w-4 h-4 text-yellow-400 mt-0.5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path
                fill-rule="evenodd"
                d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                clip-rule="evenodd"
              />
            </svg>
            <div class="text-xs">
              <div class="font-medium text-yellow-800">权限覆盖提醒</div>
              <div class="text-yellow-700 mt-1">
                <%= case @conflict_info do %>
                  <% {:conflict, :admin_overrides_agent, details} -> %>
                    {details.message}
                    <div class="mt-1 text-yellow-600">
                      <strong>建议：</strong>{details.recommendation}
                    </div>
                  <% _ -> %>
                    存在权限冲突，请联系系统管理员
                <% end %>
              </div>
            </div>
          </div>
        </div>

    <!-- 可用权限列表 -->
        <div :if={@show_details} class="text-sm text-gray-600">
          <span class="font-medium">可用功能：</span>
          <div class="flex flex-wrap gap-1 mt-1">
            <span
              :for={permission <- @permissions}
              class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
            >
              {permission}
            </span>
          </div>
        </div>
      </div>
    </div>
    """
  end

  @doc """
  条件渲染组件 - 根据条件显示不同内容

  ## 属性
  - `condition`: 条件表达式
  - `if_true`: 条件为真时的内容
  - `if_false`: 条件为假时的内容（可选）

  ## 示例
  ```heex
  <.conditional condition={AuthHelper.has_permission?(@user, :admin)}>
    <:if_true>
      <button>管理员按钮</button>
    </:if_true>
    <:if_false>
      <p>权限不足</p>
    </:if_false>
  </.conditional>
  ```
  """
  attr :condition, :boolean, required: true, doc: "条件表达式"
  slot :if_true, doc: "条件为真时的内容"
  slot :if_false, doc: "条件为假时的内容"

  def conditional(assigns) do
    if assigns.condition do
      ~H"""
      {render_slot(@if_true)}
      """
    else
      ~H"""
      {render_slot(@if_false)}
      """
    end
  end

  @doc """
  权限警告组件

  ## 属性
  - `message`: 警告消息
  - `class`: 额外的CSS类

  ## 示例
  ```heex
  <.permission_warning message="您没有权限执行此操作" />
  ```
  """
  attr :message, :string, default: "权限不足", doc: "警告消息"
  attr :class, :string, default: "", doc: "额外的CSS类"

  def permission_warning(assigns) do
    ~H"""
    <div class={"bg-yellow-50 border border-yellow-200 rounded-md p-4 #{@class}"}>
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
            <path
              fill-rule="evenodd"
              d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
              clip-rule="evenodd"
            />
          </svg>
        </div>
        <div class="ml-3">
          <p class="text-sm text-yellow-700">
            {@message}
          </p>
        </div>
      </div>
    </div>
    """
  end

  # 私有函数

  defp get_permission_list(user) do
    permissions = []

    permissions =
      if AuthHelper.has_permission?(user, :super_admin),
        do: ["超级管理员" | permissions],
        else: permissions

    permissions =
      if AuthHelper.has_permission?(user, :admin), do: ["管理员" | permissions], else: permissions

    permissions =
      if AuthHelper.has_permission?(user, :root_agent),
        do: ["根代理" | permissions],
        else: permissions

    permissions =
      if AuthHelper.has_permission?(user, :agent), do: ["代理" | permissions], else: permissions

    if permissions == [], do: ["普通用户"], else: permissions
  end
end
