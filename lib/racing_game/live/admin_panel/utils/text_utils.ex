defmodule RacingGame.Utils.TextUtils do
  @moduledoc """
  文本处理工具模块

  提供文本截断、清理、格式化等通用功能。

  ## 功能特性

  ### 文本截断
  - 智能截断，支持单词边界
  - 多种预设配置
  - 自定义截断选项
  - 上下文感知截断

  ### 文本清理
  - 移除多余空格
  - 清理特殊字符
  - HTML标签处理
  - 安全字符串处理

  ### 文本格式化
  - 首字母大写
  - 标题格式化
  - 数字格式化
  - 列表格式化

  ### 文本分析
  - 字数统计
  - 阅读时间估算
  - 文本复杂度分析
  """

  require Logger

  # 常量定义
  @reading_speed_wpm 200  # 每分钟阅读字数
  @html_tag_regex ~r/<[^>]*>/
  @whitespace_regex ~r/\s+/
  @special_chars_regex ~r/[^\p{L}\p{N}\p{P}\p{Z}]/u

  # 文本截断配置
  @truncate_defaults %{
    suffix: "...",
    word_boundary: false,
    preserve_html: false,
    min_length: 10,
    encoding: :utf8
  }

  # 预定义的截断配置
  @truncate_presets %{
    title: %{max_length: 50, suffix: "...", word_boundary: true},
    content: %{max_length: 100, suffix: "...", word_boundary: true},
    description: %{max_length: 80, suffix: "...", word_boundary: true},
    short: %{max_length: 30, suffix: "...", word_boundary: false},
    medium: %{max_length: 60, suffix: "...", word_boundary: true},
    long: %{max_length: 120, suffix: "...", word_boundary: true},
    preview: %{max_length: 200, suffix: "... 查看更多", word_boundary: true}
  }

  @doc """
  截断文本到指定长度

  ## 参数
  - `text` - 要截断的文本
  - `options` - 截断选项，可以是预设名称（原子）或选项列表

  ## 选项
  - `:max_length` - 最大长度
  - `:suffix` - 后缀（默认 "..."）
  - `:word_boundary` - 是否在单词边界截断（默认 false）
  - `:min_length` - 最小长度（默认 10）
  - `:encoding` - 编码方式（默认 :utf8）

  ## 预设
  - `:title` - 标题截断（50字符）
  - `:content` - 内容截断（100字符）
  - `:description` - 描述截断（80字符）
  - `:short` - 短文本（30字符）
  - `:medium` - 中等长度（60字符）
  - `:long` - 长文本（120字符）
  - `:preview` - 预览模式（200字符）

  ## 示例
      iex> TextUtils.truncate_text("这是一个很长的文本", :title)
      "这是一个很长的文本"

      iex> TextUtils.truncate_text("这是一个非常非常长的文本内容", max_length: 10)
      "这是一个非常非..."
  """
  def truncate_text(text, options \\ [])

  # 使用预设配置
  def truncate_text(text, preset) when is_atom(preset) do
    case Map.get(@truncate_presets, preset) do
      nil ->
        Logger.warn("未知的截断预设: #{preset}，使用默认配置")
        truncate_text(text, [])
      preset_config ->
        truncate_text(text, Map.to_list(preset_config))
    end
  end

  # 使用自定义配置
  def truncate_text(text, options) when is_list(options) do
    config = parse_truncate_config(options)
    perform_truncate(text, config)
  end

  # 兼容旧版本的三参数调用
  def truncate_text(text, max_length, opts) when is_integer(max_length) and is_list(opts) do
    options = Keyword.put(opts, :max_length, max_length)
    truncate_text(text, options)
  end

  @doc """
  智能截断文本，根据上下文自动选择最佳配置

  ## 参数
  - `text` - 要截断的文本
  - `context` - 使用上下文（默认 :general）

  ## 上下文
  - `:title` - 标题
  - `:content` - 内容
  - `:description` - 描述
  - `:list_item` - 列表项
  - `:tooltip` - 工具提示
  - `:preview` - 预览

  ## 示例
      iex> TextUtils.smart_truncate("长标题文本", :title)
      "长标题文本"
  """
  def smart_truncate(text, context \\ :general) do
    preset = case context do
      :title -> :title
      :content -> :content
      :description -> :description
      :list_item -> :medium
      :tooltip -> :short
      :preview -> :preview
      _ -> :medium
    end

    truncate_text(text, preset)
  end

  @doc """
  清理和标准化字符串

  移除多余空格，清理特殊字符

  ## 示例
      iex> TextUtils.sanitize_string("  hello   world  ")
      "hello world"
  """
  def sanitize_string(nil), do: ""
  def sanitize_string(value) when is_binary(value) do
    value
    |> String.trim()
    |> String.replace(@whitespace_regex, " ")  # 将多个空白字符替换为单个空格
    |> String.replace(@special_chars_regex, "")  # 移除特殊字符，保留字母、数字、标点和空格
  end
  def sanitize_string(value), do: value |> to_string() |> sanitize_string()

  @doc """
  移除HTML标签

  ## 参数
  - `html_string` - 包含HTML的字符串

  ## 示例
      iex> TextUtils.strip_html("<p>Hello <strong>World</strong></p>")
      "Hello World"
  """
  def strip_html(nil), do: ""
  def strip_html(html_string) when is_binary(html_string) do
    html_string
    |> String.replace(@html_tag_regex, "")
    |> String.replace("&nbsp;", " ")
    |> String.replace("&amp;", "&")
    |> String.replace("&lt;", "<")
    |> String.replace("&gt;", ">")
    |> String.replace("&quot;", "\"")
    |> sanitize_string()
  end
  def strip_html(value), do: value |> to_string() |> strip_html()

  @doc """
  首字母大写

  ## 参数
  - `string` - 要处理的字符串

  ## 示例
      iex> TextUtils.capitalize_first("hello world")
      "Hello world"
  """
  def capitalize_first(nil), do: ""
  def capitalize_first(""), do: ""
  def capitalize_first(string) when is_binary(string) do
    {first, rest} = String.split_at(string, 1)
    String.upcase(first) <> rest
  end
  def capitalize_first(value), do: value |> to_string() |> capitalize_first()

  @doc """
  标题格式化（每个单词首字母大写）

  ## 参数
  - `string` - 要处理的字符串

  ## 示例
      iex> TextUtils.title_case("hello world")
      "Hello World"
  """
  def title_case(nil), do: ""
  def title_case(string) when is_binary(string) do
    string
    |> String.split()
    |> Enum.map(&capitalize_first/1)
    |> Enum.join(" ")
  end
  def title_case(value), do: value |> to_string() |> title_case()

  @doc """
  统计字数

  ## 参数
  - `text` - 要统计的文本

  ## 示例
      iex> TextUtils.word_count("Hello world")
      2
  """
  def word_count(nil), do: 0
  def word_count(text) when is_binary(text) do
    text
    |> strip_html()
    |> String.split(@whitespace_regex, trim: true)
    |> length()
  end
  def word_count(value), do: value |> to_string() |> word_count()

  @doc """
  估算阅读时间（分钟）

  ## 参数
  - `text` - 要分析的文本
  - `wpm` - 每分钟阅读字数（默认200）

  ## 示例
      iex> TextUtils.reading_time("很长的文本内容...")
      "约2分钟"
  """
  def reading_time(text, wpm \\ @reading_speed_wpm) do
    words = word_count(text)
    minutes = max(1, round(words / wpm))

    case minutes do
      1 -> "约1分钟"
      n when n < 60 -> "约#{n}分钟"
      n ->
        hours = div(n, 60)
        remaining_minutes = rem(n, 60)
        if remaining_minutes == 0 do
          "约#{hours}小时"
        else
          "约#{hours}小时#{remaining_minutes}分钟"
        end
    end
  end

  @doc """
  获取可用的截断预设列表

  ## 示例
      iex> TextUtils.get_available_presets()
      [:title, :content, :description, :short, :medium, :long, :preview]
  """
  def get_available_presets do
    Map.keys(@truncate_presets)
  end

  @doc """
  获取预设配置详情

  ## 示例
      iex> TextUtils.get_preset_info(:title)
      %{max_length: 50, suffix: "...", word_boundary: true}
  """
  def get_preset_info(preset) do
    Map.get(@truncate_presets, preset)
  end

  @doc """
  格式化数字为可读字符串

  ## 参数
  - `number` - 要格式化的数字

  ## 示例
      iex> TextUtils.format_number(1234567)
      "1,234,567"
  """
  def format_number(number) when is_integer(number) do
    number
    |> Integer.to_string()
    |> String.reverse()
    |> String.replace(~r/(\d{3})(?=\d)/, "\\1,")
    |> String.reverse()
  end
  def format_number(number) when is_float(number) do
    :erlang.float_to_binary(number, decimals: 2)
    |> format_number_string()
  end
  def format_number(value), do: to_string(value)

  @doc """
  将列表格式化为可读字符串

  ## 参数
  - `list` - 要格式化的列表
  - `opts` - 选项
    - `:separator` - 分隔符（默认"、"）
    - `:last_separator` - 最后一个分隔符（默认"和"）

  ## 示例
      iex> TextUtils.format_list(["苹果", "香蕉", "橙子"])
      "苹果、香蕉和橙子"
  """
  def format_list([], _opts), do: ""
  def format_list([item], _opts), do: to_string(item)
  def format_list([item1, item2], opts) do
    last_sep = Keyword.get(opts, :last_separator, "和")
    "#{item1}#{last_sep}#{item2}"
  end
  def format_list(list, opts) when is_list(list) do
    separator = Keyword.get(opts, :separator, "、")
    last_separator = Keyword.get(opts, :last_separator, "和")

    {last_item, rest_items} = List.pop_at(list, -1)
    rest_string = Enum.join(rest_items, separator)
    "#{rest_string}#{last_separator}#{last_item}"
  end
  def format_list(value, _opts), do: to_string(value)

  @doc """
  文本复杂度分析

  ## 参数
  - `text` - 要分析的文本

  ## 返回
  - 包含复杂度指标的映射

  ## 示例
      iex> TextUtils.analyze_complexity("这是一个简单的句子。")
      %{
        word_count: 7,
        sentence_count: 1,
        avg_word_length: 2.14,
        complexity: :simple
      }
  """
  def analyze_complexity(text) when is_binary(text) do
    clean_text = strip_html(text)
    words = String.split(clean_text, @whitespace_regex, trim: true)
    sentences = String.split(clean_text, ~r/[。！？.!?]+/, trim: true)

    word_count = length(words)
    sentence_count = max(1, length(sentences))

    avg_word_length = if word_count > 0 do
      total_chars = words |> Enum.map(&String.length/1) |> Enum.sum()
      total_chars / word_count
    else
      0
    end

    avg_sentence_length = word_count / sentence_count

    complexity = determine_complexity(avg_word_length, avg_sentence_length)

    %{
      word_count: word_count,
      sentence_count: sentence_count,
      avg_word_length: Float.round(avg_word_length, 2),
      avg_sentence_length: Float.round(avg_sentence_length, 2),
      complexity: complexity,
      reading_time: reading_time(text)
    }
  end
  def analyze_complexity(_), do: %{complexity: :unknown}

  @doc """
  生成文本摘要

  ## 参数
  - `text` - 要摘要的文本
  - `sentence_count` - 摘要句子数（默认2）

  ## 示例
      iex> TextUtils.summarize("很长的文本...", 2)
      "第一句话。第二句话。"
  """
  def summarize(text, sentence_count \\ 2) when is_binary(text) do
    text
    |> strip_html()
    |> String.split(~r/[。！？.!?]+/, trim: true)
    |> Enum.take(sentence_count)
    |> Enum.map(&String.trim/1)
    |> Enum.join("。")
    |> case do
      "" -> ""
      summary -> summary <> "。"
    end
  end
  def summarize(_, _), do: ""

  # ============================================================================
  # 私有辅助函数
  # ============================================================================

  # 私有函数 - 解析截断配置
  defp parse_truncate_config(options) do
    @truncate_defaults
    |> Map.merge(Enum.into(options, %{}))
    |> validate_truncate_config()
  end

  # 私有函数 - 格式化数字字符串
  defp format_number_string(number_string) do
    [integer_part, decimal_part] = String.split(number_string, ".")
    formatted_integer = format_integer_part(integer_part)
    "#{formatted_integer}.#{decimal_part}"
  end

  # 私有函数 - 格式化整数部分
  defp format_integer_part(integer_string) do
    integer_string
    |> String.reverse()
    |> String.replace(~r/(\d{3})(?=\d)/, "\\1,")
    |> String.reverse()
  end

  # 私有函数 - 确定文本复杂度
  defp determine_complexity(avg_word_length, avg_sentence_length) do
    cond do
      avg_word_length <= 3 and avg_sentence_length <= 10 -> :simple
      avg_word_length <= 5 and avg_sentence_length <= 15 -> :moderate
      avg_word_length <= 7 and avg_sentence_length <= 20 -> :complex
      true -> :very_complex
    end
  end

  # 私有函数 - 验证截断配置
  defp validate_truncate_config(config) do
    config
    |> ensure_max_length()
    |> ensure_valid_suffix()
    |> ensure_valid_min_length()
  end

  # 私有函数 - 确保有最大长度配置
  defp ensure_max_length(%{max_length: max_length} = config) when is_integer(max_length) and max_length > 0 do
    config
  end
  defp ensure_max_length(config) do
    Logger.warn("截断配置缺少有效的 max_length，使用默认值 100")
    Map.put(config, :max_length, 100)
  end

  # 私有函数 - 确保有效的后缀
  defp ensure_valid_suffix(%{suffix: suffix} = config) when is_binary(suffix) do
    config
  end
  defp ensure_valid_suffix(config) do
    Map.put(config, :suffix, "...")
  end

  # 私有函数 - 确保有效的最小长度
  defp ensure_valid_min_length(%{min_length: min_length} = config) when is_integer(min_length) and min_length >= 0 do
    config
  end
  defp ensure_valid_min_length(config) do
    Map.put(config, :min_length, 10)
  end

  # 私有函数 - 执行截断操作
  defp perform_truncate(text, config) do
    case normalize_text_input(text) do
      "" -> ""
      normalized_text -> do_truncate(normalized_text, config)
    end
  end

  # 私有函数 - 标准化文本输入
  defp normalize_text_input(nil), do: ""
  defp normalize_text_input(text) when is_binary(text), do: String.trim(text)
  defp normalize_text_input(text), do: text |> to_string() |> String.trim()

  # 私有函数 - 执行实际截断
  defp do_truncate(text, %{max_length: max_length} = config) do
    text_length = get_text_length(text, config.encoding)

    if text_length <= max_length do
      text
    else
      perform_text_truncation(text, config)
    end
  end

  # 私有函数 - 获取文本长度
  defp get_text_length(text, :utf8), do: String.length(text)
  defp get_text_length(text, :bytes), do: byte_size(text)
  defp get_text_length(text, :graphemes), do: String.length(text)  # 默认使用字符长度
  defp get_text_length(text, _), do: String.length(text)

  # 私有函数 - 执行文本截断
  defp perform_text_truncation(text, config) do
    %{max_length: max_length, suffix: suffix, word_boundary: word_boundary, min_length: min_length} = config

    # 计算可用的文本长度（减去后缀长度）
    available_length = max(max_length - String.length(suffix), min_length)

    # 基础截断
    truncated = String.slice(text, 0, available_length)

    # 根据配置进行边界处理
    final_text = if word_boundary do
      truncate_at_word_boundary(truncated, text, available_length, min_length)
    else
      truncated
    end

    # 添加后缀
    final_text <> suffix
  end

  # 私有函数 - 在单词边界截断
  defp truncate_at_word_boundary(truncated, original_text, available_length, min_length) do
    # 检查截断点是否在单词中间
    if available_length < String.length(original_text) do
      next_char = String.at(original_text, available_length)

      if next_char && !String.match?(next_char, ~r/\s/) do
        # 在单词中间，需要找到最近的单词边界
        find_word_boundary(truncated, min_length)
      else
        # 已经在单词边界，直接使用
        truncated
      end
    else
      truncated
    end
  end

  # 私有函数 - 查找单词边界
  defp find_word_boundary(text, min_length) do
    # 清理输入文本，移除多余空格
    cleaned_text = String.trim(text)

    # 如果文本为空或太短，直接返回
    if cleaned_text == "" or String.length(cleaned_text) < min_length do
      cleaned_text
    else
      find_optimal_word_boundary(cleaned_text, min_length)
    end
  end

  # 私有函数 - 查找最优单词边界
  defp find_optimal_word_boundary(text, min_length) do
    words = String.split(text, ~r/\s+/, trim: true)  # 使用正则表达式处理多种空白字符

    case words do
      [] ->
        ""

      [single_word] ->
        # 只有一个单词的情况
        handle_single_word(single_word, min_length)

      multiple_words ->
        # 多个单词的情况
        handle_multiple_words(multiple_words, min_length, text)
    end
  end

  # 私有函数 - 处理单个单词的情况
  defp handle_single_word(word, min_length) do
    word_length = String.length(word)

    cond do
      word_length >= min_length ->
        word

      word_length >= div(min_length, 2) ->
        # 如果单词长度至少是最小长度的一半，保留它
        word

      true ->
        # 单词太短，但仍然返回以避免空字符串
        word
    end
  end

  # 私有函数 - 处理多个单词的情况
  defp handle_multiple_words(words, min_length, original_text) do
    # 尝试不同的截断策略
    strategies = [
      fn -> try_drop_last_word(words, min_length) end,
      fn -> try_progressive_truncation(words, min_length) end,
      fn -> try_character_based_fallback(original_text, min_length) end
    ]

    # 按优先级尝试各种策略
    Enum.reduce_while(strategies, nil, fn strategy, _acc ->
      case strategy.() do
        {:ok, result} -> {:halt, result}
        :error -> {:cont, nil}
      end
    end) || String.slice(original_text, 0, min_length)  # 最后的备用方案
  end

  # 私有函数 - 尝试移除最后一个单词
  defp try_drop_last_word(words, min_length) do
    if length(words) <= 1 do
      :error
    else
      result = words
      |> Enum.drop(-1)
      |> Enum.join(" ")

      if String.length(result) >= min_length do
        {:ok, result}
      else
        :error
      end
    end
  end

  # 私有函数 - 尝试渐进式截断
  defp try_progressive_truncation(words, min_length) do
    # 从后往前逐个移除单词，直到找到合适的长度
    try_truncate_words(words, min_length, length(words) - 1)
  end

  # 私有函数 - 递归尝试截断单词
  defp try_truncate_words(_words, _min_length, 0), do: :error

  defp try_truncate_words(words, min_length, words_to_keep) when words_to_keep > 0 do
    result = words
    |> Enum.take(words_to_keep)
    |> Enum.join(" ")

    result_length = String.length(result)

    cond do
      result_length >= min_length ->
        {:ok, result}

      words_to_keep == 1 and result_length >= div(min_length, 2) ->
        # 如果只剩一个单词且长度至少是最小长度的一半，接受它
        {:ok, result}

      true ->
        try_truncate_words(words, min_length, words_to_keep - 1)
    end
  end

  # 私有函数 - 基于字符的备用方案
  defp try_character_based_fallback(text, min_length) do
    # 在最小长度附近查找空格
    search_start = max(0, min_length - 10)
    search_end = min(String.length(text), min_length + 10)

    search_text = String.slice(text, search_start, search_end - search_start)

    # 查找最后一个空格的位置
    case String.last_index_of(search_text, " ") do
      nil ->
        :error

      relative_index ->
        absolute_index = search_start + relative_index

        if absolute_index >= min_length do
          result = String.slice(text, 0, absolute_index)
          {:ok, result}
        else
          :error
        end
    end
  end
end
