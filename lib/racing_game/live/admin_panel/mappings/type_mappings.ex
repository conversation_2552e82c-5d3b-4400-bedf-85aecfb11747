defmodule RacingGame.Live.AdminPanel.Mappings.TypeMappings do
  @moduledoc """
  类型映射模块

  定义系统通信管理组件中的各种类型映射和配置。
  """

  # 通信类型映射
  @communication_type_mappings %{
    message: %{
      display_name: "系统消息",
      icon: "fas fa-envelope",
      description: "系统内部消息通知",
      color_class: "text-blue-600"
    },
    announcement: %{
      display_name: "系统公告",
      icon: "fas fa-bullhorn",
      description: "重要的系统公告信息",
      color_class: "text-orange-600"
    },
    notification: %{
      display_name: "系统通知",
      icon: "fas fa-bell",
      description: "系统状态和事件通知",
      color_class: "text-green-600"
    }
  }

  # 优先级映射
  @priority_mappings %{
    low: %{
      display_name: "低",
      badge_class: "bg-gray-100 text-gray-800 border-gray-200",
      icon: "fas fa-arrow-down",
      sort_order: 1,
      description: "低优先级"
    },
    medium: %{
      display_name: "中",
      badge_class: "bg-blue-100 text-blue-800 border-blue-200",
      icon: "fas fa-minus",
      sort_order: 2,
      description: "中等优先级"
    },
    high: %{
      display_name: "高",
      badge_class: "bg-orange-100 text-orange-800 border-orange-200",
      icon: "fas fa-arrow-up",
      sort_order: 3,
      description: "高优先级"
    },
    urgent: %{
      display_name: "紧急",
      badge_class: "bg-red-100 text-red-800 border-red-200",
      icon: "fas fa-exclamation-triangle",
      sort_order: 4,
      description: "紧急优先级"
    }
  }

  # 状态映射
  @status_mappings %{
    true => %{
      display_name: "启用",
      badge_class: "bg-green-100 text-green-800 border-green-200",
      icon: "fas fa-check-circle",
      description: "已启用状态"
    },
    false => %{
      display_name: "禁用",
      badge_class: "bg-red-100 text-red-800 border-red-200",
      icon: "fas fa-times-circle",
      description: "已禁用状态"
    }
  }

  @doc """
  获取通信类型信息

  ## 参数
  - `type` - 通信类型
  - `field` - 要获取的字段（默认 :display_name）

  ## 返回
  - 对应字段的值，如果不存在则返回默认值
  """
  def get_type_info(type, field \\ :display_name) do
    normalized_type = normalize_type(type)

    case Map.get(@communication_type_mappings, normalized_type) do
      nil -> get_default_type_info(field)
      type_info -> Map.get(type_info, field, get_default_type_info(field))
    end
  end

  @doc """
  获取优先级信息

  ## 参数
  - `priority` - 优先级
  - `field` - 要获取的字段（默认 :display_name）

  ## 返回
  - 对应字段的值，如果不存在则返回默认值
  """
  def get_priority_info(priority, field \\ :display_name) do
    normalized_priority = normalize_priority(priority)

    case Map.get(@priority_mappings, normalized_priority) do
      nil -> get_default_priority_info(field)
      priority_info -> Map.get(priority_info, field, get_default_priority_info(field))
    end
  end

  @doc """
  获取状态信息

  ## 参数
  - `status` - 状态值
  - `field` - 要获取的字段（默认 :display_name）

  ## 返回
  - 对应字段的值，如果不存在则返回默认值
  """
  def get_status_info(status, field \\ :display_name) do
    case Map.get(@status_mappings, status) do
      nil -> get_default_status_info(field)
      status_info -> Map.get(status_info, field, get_default_status_info(field))
    end
  end

  # 便捷函数 - 类型相关
  def get_type_display_name(type), do: get_type_info(type, :display_name)
  def get_type_icon(type), do: get_type_info(type, :icon)
  def get_type_description(type), do: get_type_info(type, :description)
  def get_type_color_class(type), do: get_type_info(type, :color_class)

  # 便捷函数 - 优先级相关
  def get_priority_display_name(priority), do: get_priority_info(priority, :display_name)
  def get_priority_badge_class(priority), do: get_priority_info(priority, :badge_class)
  def get_priority_icon(priority), do: get_priority_info(priority, :icon)
  def get_priority_sort_order(priority), do: get_priority_info(priority, :sort_order)

  # 便捷函数 - 状态相关
  def get_status_display_name(status), do: get_status_info(status, :display_name)
  def get_status_badge_class(status), do: get_status_info(status, :badge_class)
  def get_status_icon(status), do: get_status_info(status, :icon)

  @doc """
  获取所有支持的通信类型

  ## 返回
  - 通信类型列表
  """
  def get_supported_types, do: Map.keys(@communication_type_mappings)

  @doc """
  获取所有支持的优先级

  ## 返回
  - 优先级列表
  """
  def get_supported_priorities, do: Map.keys(@priority_mappings)

  @doc """
  检查类型是否有效

  ## 参数
  - `type` - 要检查的类型

  ## 返回
  - `true` 如果类型有效，否则 `false`
  """
  def valid_type?(type) do
    normalized_type = normalize_type(type)
    Map.has_key?(@communication_type_mappings, normalized_type)
  end

  @doc """
  检查优先级是否有效

  ## 参数
  - `priority` - 要检查的优先级

  ## 返回
  - `true` 如果优先级有效，否则 `false`
  """
  def valid_priority?(priority) do
    normalized_priority = normalize_priority(priority)
    Map.has_key?(@priority_mappings, normalized_priority)
  end

  # 私有函数 - 标准化类型
  defp normalize_type(type) when is_atom(type), do: type
  defp normalize_type(type) when is_binary(type) do
    case String.downcase(type) do
      "message" -> :message
      "announcement" -> :announcement
      "notification" -> :notification
      _ -> :unknown
    end
  end
  defp normalize_type(_), do: :unknown

  # 私有函数 - 标准化优先级
  defp normalize_priority(priority) when is_atom(priority), do: priority
  defp normalize_priority(priority) when is_binary(priority) do
    case String.downcase(priority) do
      "low" -> :low
      "medium" -> :medium
      "high" -> :high
      "urgent" -> :urgent
      _ -> :medium
    end
  end
  defp normalize_priority(_), do: :medium

  # 私有函数 - 获取默认类型信息
  defp get_default_type_info(:display_name), do: "未知类型"
  defp get_default_type_info(:icon), do: "fas fa-question-circle"
  defp get_default_type_info(:description), do: "未知的通信类型"
  defp get_default_type_info(:color_class), do: "text-gray-600"
  defp get_default_type_info(_), do: nil

  # 私有函数 - 获取默认优先级信息
  defp get_default_priority_info(:display_name), do: "未知"
  defp get_default_priority_info(:badge_class), do: "bg-gray-100 text-gray-800 border-gray-200"
  defp get_default_priority_info(:icon), do: "fas fa-question-circle"
  defp get_default_priority_info(:sort_order), do: 0
  defp get_default_priority_info(:description), do: "未知优先级"
  defp get_default_priority_info(_), do: nil

  # 私有函数 - 获取默认状态信息
  defp get_default_status_info(:display_name), do: "未知"
  defp get_default_status_info(:badge_class), do: "bg-gray-100 text-gray-800 border-gray-200"
  defp get_default_status_info(:icon), do: "fas fa-question-circle"
  defp get_default_status_info(:description), do: "未知状态"
  defp get_default_status_info(_), do: nil
end
