defmodule RacingGame.Live.AdminPanel.Repositories.Teen.GameManagement.VipLevelRepository do
  @moduledoc """
  👑 VIP等级仓储层

  负责VIP等级配置的数据访问操作，包括：
  - VIP等级的CRUD操作
  - VIP等级查询和过滤
  - VIP等级状态管理
  - VIP等级缓存
  - VIP等级统计信息
  """

  require Logger
  import Ash.Query

  alias Teen.GameManagement.VipLevel

  # 缓存配置
  @cache_ttl 600_000  # 10分钟缓存（VIP等级变化较少）
  @cache_prefix "vip_level_repo"

  # ==================== 基础 CRUD 操作 ====================

  @doc """
  创建VIP等级

  ## 参数
  - `vip_data` - VIP等级数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, vip_level}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_vip_level(vip_data, options \\ []) do
    Logger.info("👑 [VIP等级仓储] 创建VIP等级: #{inspect(vip_data)}")

    try do
      case VipLevel.create(vip_data) do
        {:ok, vip_level} ->
          Logger.info("✅ [VIP等级仓储] VIP等级创建成功: #{vip_level.id}")
          clear_vip_level_cache()
          {:ok, vip_level}

        {:error, error} ->
          Logger.error("❌ [VIP等级仓储] VIP等级创建失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [VIP等级仓储] 创建VIP等级异常: #{inspect(exception)}")
        {:error, :create_vip_level_exception}
    end
  end

  @doc """
  根据ID获取VIP等级

  ## 参数
  - `vip_level_id` - VIP等级ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, vip_level}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_vip_level_by_id(vip_level_id, options \\ []) do
    Logger.info("🔍 [VIP等级仓储] 获取VIP等级: #{vip_level_id}")

    cache_key = "#{@cache_prefix}:vip_level:#{vip_level_id}"
    use_cache = Keyword.get(options, :use_cache, true)

    if use_cache do
      case get_from_cache(cache_key) do
        {:ok, vip_level} ->
          Logger.info("📦 [VIP等级仓储] 从缓存获取VIP等级: #{vip_level_id}")
          {:ok, vip_level}

        {:error, :not_found} ->
          fetch_and_cache_vip_level(vip_level_id, cache_key)
      end
    else
      fetch_vip_level_by_id(vip_level_id)
    end
  end

  @doc """
  根据等级获取VIP等级配置

  ## 参数
  - `level` - VIP等级
  - `options` - 选项参数

  ## 返回
  - `{:ok, vip_level}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_vip_level_by_level(level, options \\ []) do
    Logger.info("🔍 [VIP等级仓储] 根据等级获取VIP配置: #{level}")

    try do
      case VipLevel.get_by_level(level) do
        {:ok, [vip_level]} ->
          Logger.info("✅ [VIP等级仓储] 找到VIP等级: #{vip_level.id}")
          {:ok, vip_level}

        {:ok, []} ->
          Logger.info("📭 [VIP等级仓储] 未找到VIP等级: #{level}")
          {:error, :vip_level_not_found}

        {:error, error} ->
          Logger.error("❌ [VIP等级仓储] 查询VIP等级失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [VIP等级仓储] 查询VIP等级异常: #{inspect(exception)}")
        {:error, :query_vip_level_exception}
    end
  end

  @doc """
  更新VIP等级

  ## 参数
  - `vip_level_id` - VIP等级ID
  - `update_data` - 更新数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, vip_level}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_vip_level(vip_level_id, update_data, options \\ []) do
    Logger.info("📝 [VIP等级仓储] 更新VIP等级: #{vip_level_id}")

    with {:ok, vip_level} <- get_vip_level_by_id(vip_level_id, use_cache: false),
         {:ok, updated_vip_level} <- VipLevel.update(vip_level, update_data) do
      Logger.info("✅ [VIP等级仓储] VIP等级更新成功: #{vip_level_id}")
      clear_vip_level_cache()
      {:ok, updated_vip_level}
    else
      {:error, error} ->
        Logger.error("❌ [VIP等级仓储] VIP等级更新失败: #{inspect(error)}")
        {:error, error}
    end
  end

  @doc """
  删除VIP等级

  ## 参数
  - `vip_level_id` - VIP等级ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, vip_level}` - 成功
  - `{:error, reason}` - 失败
  """
  def delete_vip_level(vip_level_id, options \\ []) do
    Logger.info("🗑️ [VIP等级仓储] 删除VIP等级: #{vip_level_id}")

    with {:ok, vip_level} <- get_vip_level_by_id(vip_level_id, use_cache: false),
         {:ok, deleted_vip_level} <- VipLevel.destroy(vip_level) do
      Logger.info("✅ [VIP等级仓储] VIP等级删除成功: #{vip_level_id}")
      clear_vip_level_cache()
      {:ok, deleted_vip_level}
    else
      {:error, error} ->
        Logger.error("❌ [VIP等级仓储] VIP等级删除失败: #{inspect(error)}")
        {:error, error}
    end
  end

  # ==================== 查询操作 ====================

  @doc """
  获取所有活跃VIP等级

  ## 参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, vip_levels}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_active_vip_levels(options \\ []) do
    Logger.info("📋 [VIP等级仓储] 获取活跃VIP等级列表")

    cache_key = "#{@cache_prefix}:active_vip_levels"
    use_cache = Keyword.get(options, :use_cache, true)

    if use_cache do
      case get_from_cache(cache_key) do
        {:ok, vip_levels} ->
          Logger.info("📦 [VIP等级仓储] 从缓存获取活跃VIP等级")
          {:ok, vip_levels}

        {:error, :not_found} ->
          fetch_and_cache_active_vip_levels(cache_key)
      end
    else
      fetch_active_vip_levels()
    end
  end

  @doc """
  获取VIP等级列表（带分页和过滤）

  ## 参数
  - `params` - 查询参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, %{vip_levels: vip_levels, pagination: pagination}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_vip_levels(params \\ %{}, options \\ []) do
    Logger.info("📋 [VIP等级仓储] 获取VIP等级列表: #{inspect(params)}")

    try do
      query = VipLevel
      |> apply_vip_level_filters(params)
      |> apply_vip_level_sorting(params)
      |> apply_vip_level_pagination(params)

      case Ash.read(query) do
        {:ok, vip_levels} ->
          pagination = build_pagination_info(vip_levels, params)
          Logger.info("✅ [VIP等级仓储] 获取到 #{length(vip_levels)} 个VIP等级")
          {:ok, %{vip_levels: vip_levels, pagination: pagination}}

        {:error, error} ->
          Logger.error("❌ [VIP等级仓储] 获取VIP等级列表失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [VIP等级仓储] 获取VIP等级列表异常: #{inspect(exception)}")
        {:error, :list_vip_levels_exception}
    end
  end

  # ==================== 状态管理操作 ====================

  @doc """
  启用VIP等级

  ## 参数
  - `vip_level_id` - VIP等级ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, vip_level}` - 成功
  - `{:error, reason}` - 失败
  """
  def enable_vip_level(vip_level_id, options \\ []) do
    Logger.info("🟢 [VIP等级仓储] 启用VIP等级: #{vip_level_id}")
    update_vip_level_status(vip_level_id, 1, options)
  end

  @doc """
  禁用VIP等级

  ## 参数
  - `vip_level_id` - VIP等级ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, vip_level}` - 成功
  - `{:error, reason}` - 失败
  """
  def disable_vip_level(vip_level_id, options \\ []) do
    Logger.info("🔴 [VIP等级仓储] 禁用VIP等级: #{vip_level_id}")
    update_vip_level_status(vip_level_id, 0, options)
  end

  # ==================== 业务查询操作 ====================

  @doc """
  根据充值金额计算VIP等级

  ## 参数
  - `total_recharge` - 总充值金额
  - `options` - 选项参数

  ## 返回
  - `{:ok, vip_level}` - 成功
  - `{:error, reason}` - 失败
  """
  def calculate_vip_level_by_recharge(total_recharge, options \\ []) do
    Logger.info("🧮 [VIP等级仓储] 计算VIP等级，充值金额: #{total_recharge}")

    with {:ok, active_levels} <- list_active_vip_levels(options) do
      # 按充值要求降序排列，找到符合条件的最高等级
      sorted_levels = Enum.sort_by(active_levels, & &1.recharge_requirement, {:desc, Decimal})

      matching_level = Enum.find(sorted_levels, fn level ->
        Decimal.compare(total_recharge, level.recharge_requirement) != :lt
      end)

      case matching_level do
        nil ->
          Logger.info("📭 [VIP等级仓储] 未达到任何VIP等级要求")
          {:ok, 0}

        level ->
          Logger.info("✅ [VIP等级仓储] 计算得到VIP等级: #{level.level}")
          {:ok, level.level}
      end
    end
  end

  @doc """
  获取VIP等级特权

  ## 参数
  - `level` - VIP等级
  - `options` - 选项参数

  ## 返回
  - `{:ok, privileges}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_vip_privileges(level, options \\ []) do
    Logger.info("🎁 [VIP等级仓储] 获取VIP特权: #{level}")

    case get_vip_level_by_level(level, options) do
      {:ok, vip_level} ->
        privileges = vip_level.privileges || []
        Logger.info("✅ [VIP等级仓储] 获取到 #{length(privileges)} 个特权")
        {:ok, privileges}

      {:error, :vip_level_not_found} ->
        Logger.info("📭 [VIP等级仓储] VIP等级不存在，返回空特权")
        {:ok, []}

      error ->
        error
    end
  end

  # ==================== 私有辅助函数 ====================

  # 从数据库获取VIP等级并缓存
  defp fetch_and_cache_vip_level(vip_level_id, cache_key) do
    case fetch_vip_level_by_id(vip_level_id) do
      {:ok, vip_level} ->
        cache_vip_level(cache_key, vip_level)
        {:ok, vip_level}

      error ->
        error
    end
  end

  # 从数据库获取VIP等级
  defp fetch_vip_level_by_id(vip_level_id) do
    try do
      case VipLevel.read(vip_level_id) do
        {:ok, vip_level} ->
          Logger.info("✅ [VIP等级仓储] 从数据库获取VIP等级: #{vip_level_id}")
          {:ok, vip_level}

        {:error, error} ->
          Logger.error("❌ [VIP等级仓储] 获取VIP等级失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [VIP等级仓储] 获取VIP等级异常: #{inspect(exception)}")
        {:error, :fetch_vip_level_exception}
    end
  end

  # 获取活跃VIP等级并缓存
  defp fetch_and_cache_active_vip_levels(cache_key) do
    case fetch_active_vip_levels() do
      {:ok, vip_levels} ->
        cache_vip_levels(cache_key, vip_levels)
        {:ok, vip_levels}

      error ->
        error
    end
  end

  # 从数据库获取活跃VIP等级
  defp fetch_active_vip_levels do
    try do
      case VipLevel.list_active_levels() do
        {:ok, vip_levels} ->
          Logger.info("✅ [VIP等级仓储] 获取到 #{length(vip_levels)} 个活跃VIP等级")
          {:ok, vip_levels}

        {:error, error} ->
          Logger.error("❌ [VIP等级仓储] 获取活跃VIP等级失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [VIP等级仓储] 获取活跃VIP等级异常: #{inspect(exception)}")
        {:error, :fetch_active_vip_levels_exception}
    end
  end

  # 更新VIP等级状态
  defp update_vip_level_status(vip_level_id, status, options) do
    update_vip_level(vip_level_id, %{status: status}, options)
  end

  # 应用VIP等级过滤条件
  defp apply_vip_level_filters(query, params) do
    Enum.reduce(params, query, fn
      {"status", status}, acc when not is_nil(status) ->
        filter(acc, status == ^status)

      {"level", level}, acc when not is_nil(level) ->
        filter(acc, level == ^level)

      {"level_name", name}, acc when not is_nil(name) and name != "" ->
        filter(acc, contains(level_name, ^name))

      {"min_recharge", min_amount}, acc when not is_nil(min_amount) ->
        filter(acc, recharge_requirement >= ^min_amount)

      {"max_recharge", max_amount}, acc when not is_nil(max_amount) ->
        filter(acc, recharge_requirement <= ^max_amount)

      _other, acc ->
        acc
    end)
  end

  # 应用排序
  defp apply_vip_level_sorting(query, params) do
    sort_by = Map.get(params, "sort_by", "level")
    sort_order = Map.get(params, "sort_order", "asc")

    case {sort_by, sort_order} do
      {"level", "asc"} -> sort(query, asc: :level)
      {"level", "desc"} -> sort(query, desc: :level)
      {"recharge_requirement", "asc"} -> sort(query, asc: :recharge_requirement)
      {"recharge_requirement", "desc"} -> sort(query, desc: :recharge_requirement)
      {"inserted_at", "desc"} -> sort(query, desc: :inserted_at)
      {"inserted_at", "asc"} -> sort(query, asc: :inserted_at)
      _ -> sort(query, asc: :level)
    end
  end

  # 应用分页
  defp apply_vip_level_pagination(query, params) do
    page = Map.get(params, "page", 1) |> ensure_integer()
    page_size = Map.get(params, "page_size", 20) |> ensure_integer()

    query
    |> limit(^page_size)
    |> offset(^((page - 1) * page_size))
  end

  # 构建分页信息
  defp build_pagination_info(vip_levels, params) do
    page = Map.get(params, "page", 1) |> ensure_integer()
    page_size = Map.get(params, "page_size", 20) |> ensure_integer()

    %{
      current_page: page,
      page_size: page_size,
      total_count: length(vip_levels),
      has_next: length(vip_levels) == page_size,
      has_prev: page > 1
    }
  end

  # 确保整数类型
  defp ensure_integer(value) when is_integer(value), do: value
  defp ensure_integer(value) when is_binary(value) do
    case Integer.parse(value) do
      {int, _} -> int
      :error -> 1
    end
  end
  defp ensure_integer(_), do: 1

  # 缓存操作
  defp get_from_cache(cache_key) do
    # 这里可以集成实际的缓存系统，如 Cachex 或 ETS
    {:error, :not_found}
  end

  defp cache_vip_level(cache_key, vip_level) do
    # 这里可以集成实际的缓存系统
    :ok
  end

  defp cache_vip_levels(cache_key, vip_levels) do
    # 这里可以集成实际的缓存系统
    :ok
  end

  defp clear_vip_level_cache do
    # 这里可以清理相关缓存
    :ok
  end
end
