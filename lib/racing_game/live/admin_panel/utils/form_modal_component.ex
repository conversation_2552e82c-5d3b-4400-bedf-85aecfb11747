defmodule Cypridina.Utils.FormModalComponent do
  @moduledoc """
  通用表单模态框组件

  提供统一的模态框表单界面，支持：
  - 动态表单字段配置
  - 多种表单类型（创建/编辑）
  - 自定义样式主题
  - 表单验证
  - 操作选项配置
  """
  use CypridinaWeb, :live_component

  # 默认配置
  @default_config %{
    title: "表单",
    subtitle: "请填写表单信息",
    icon: "fas fa-plus",
    theme: "blue",
    max_width: "2xl",
    close_after_save: true,
    show_close_after_save_option: true
  }

  # 主题配置
  @themes %{
    "blue" => %{
      gradient: "from-blue-500 to-blue-600",
      hover_gradient: "hover:from-blue-600 hover:to-blue-700",
      text_color: "text-blue-100",
      icon_color: "text-blue-500"
    },
    "green" => %{
      gradient: "from-green-500 to-green-600",
      hover_gradient: "hover:from-green-600 hover:to-green-700",
      text_color: "text-green-100",
      icon_color: "text-green-500"
    },
    "purple" => %{
      gradient: "from-purple-500 to-purple-600",
      hover_gradient: "hover:from-purple-600 hover:to-purple-700",
      text_color: "text-purple-100",
      icon_color: "text-purple-500"
    },
    "red" => %{
      gradient: "from-red-500 to-red-600",
      hover_gradient: "hover:from-red-600 hover:to-red-700",
      text_color: "text-red-100",
      icon_color: "text-red-500"
    },
    "gray" => %{
      gradient: "from-gray-500 to-gray-600",
      hover_gradient: "hover:from-gray-600 hover:to-gray-700",
      text_color: "text-gray-100",
      icon_color: "text-gray-500"
    }
  }

  def update(assigns, socket) do
    config = Map.merge(@default_config, assigns[:config] || %{})
    theme = Map.get(@themes, config.theme, @themes["blue"])

    socket =
      socket
      |> assign(assigns)
      |> assign(:config, config)
      |> assign(:theme, theme)

    {:ok, socket}
  end

  def handle_event("hide_modal", _params, socket) do
    send(self(), {:form_modal_event, :hide_modal, %{}})
    {:noreply, socket}
  end

  def handle_event("handle_keydown", %{"key" => "Escape"}, socket) do
    send(self(), {:form_modal_event, :hide_modal, %{}})
    {:noreply, socket}
  end

  def handle_event("handle_keydown", _params, socket) do
    {:noreply, socket}
  end

  def handle_event("submit_form", params, socket) do
    send(self(), {:form_modal_event, :submit_form, params})
    {:noreply, socket}
  end

  def render(assigns) do
    ~H"""
    <div class="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4"
         phx-click="hide_modal"
         phx-target={@myself}
         phx-window-keydown="handle_keydown"
         phx-key="Escape">
      <div class={[
        "bg-white rounded-2xl shadow-2xl w-full max-h-[90vh] overflow-hidden transform transition-all duration-300 scale-100",
        "max-w-#{@config.max_width}"
      ]}
      onclick="event.stopPropagation(); return false;"
      tabindex="-1">

        <!-- 模态框头部 -->
        <div class={[
          "flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r text-white rounded-t-2xl",
          @theme.gradient
        ]}>
          <div class="flex items-center space-x-3">
            <div class="p-2 bg-white bg-opacity-20 rounded-lg">
              <i class={[@config.icon, "text-xl"]}></i>
            </div>
            <div>
              <h3 class="text-xl font-bold"><%= @config.title %></h3>
              <p class={["text-sm opacity-90", @theme.text_color]}>
                <%= @config.subtitle %>
              </p>
            </div>
          </div>
          <button
            phx-click="hide_modal"
            phx-target={@myself}
            class="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors duration-200"
          >
            <i class="fas fa-times text-xl"></i>
          </button>
        </div>

        <!-- 表单内容 -->
        <div class="overflow-y-auto max-h-[calc(90vh-200px)]">
          <form phx-submit="submit_form" phx-target={@myself} class="p-6 space-y-6">
            <!-- 动态表单字段 -->
            <%= for field_group <- @form_fields do %>
              <div class="bg-gray-50 p-4 rounded-lg">
                <%= if field_group[:title] do %>
                  <h4 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <%= if field_group[:icon] do %>
                      <i class={[field_group.icon, "mr-2", @theme.icon_color]}></i>
                    <% end %>
                    <%= field_group.title %>
                  </h4>
                <% end %>

                <div class={[
                  (if field_group[:grid], do: "grid gap-4", else: "space-y-4"),
                  field_group[:grid] || ""
                ]}>
                  <%= for field <- field_group.fields do %>
                    <%= render_field(assigns, field) %>
                  <% end %>
                </div>
              </div>
            <% end %>

            <!-- 操作选项 -->
            <%= if @config.show_close_after_save_option do %>
              <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                  <i class="fas fa-tools mr-2 text-orange-500"></i>
                  操作选项
                </h4>

                <label class="flex items-center space-x-3 cursor-pointer">
                  <input
                    type="checkbox"
                    name="close_after_save"
                    value="true"
                    {if @config.close_after_save, do: [checked: "checked"], else: []}
                    class="w-5 h-5 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <div>
                    <span class="text-sm font-medium text-gray-700">保存后关闭窗口</span>
                    <p class="text-xs text-gray-500">取消勾选此项，保存后将保持窗口打开状态，方便连续创建</p>
                  </div>
                </label>
              </div>
            <% end %>

            <!-- 按钮区域 -->
            <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200 mt-6">
              <button
                type="button"
                phx-click="hide_modal"
                phx-target={@myself}
                class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 font-medium shadow-sm"
              >
                <i class="fas fa-times mr-2"></i>取消
              </button>
              <button
                type="submit"
                class={[
                  "px-6 py-3 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105",
                  "bg-gradient-to-r",
                  @theme.gradient,
                  @theme.hover_gradient
                ]}
              >
                <i class="fas fa-save mr-2"></i>
                <%= @config.submit_text || "保存" %>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
    """
  end

  # 渲染单个字段
  defp render_field(assigns, field) do
    case field.type do
      :text -> render_text_field(assigns, field)
      :textarea -> render_textarea_field(assigns, field)
      :select -> render_select_field(assigns, field)
      :checkbox -> render_checkbox_field(assigns, field)
      :hidden -> render_hidden_field(assigns, field)
      _ -> render_text_field(assigns, field)
    end
  end

  # 文本输入框
  defp render_text_field(assigns, field) do
    assigns = assign(assigns, :field, field)
    ~H"""
    <div class={@field[:wrapper_class] || ""}>
      <%= if @field[:label] do %>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          <%= if @field[:icon] do %>
            <i class={[@field.icon, "mr-1"]}></i>
          <% end %>
          <%= @field.label %>
          <%= if @field[:required] do %>
            <span class="text-red-500">*</span>
          <% end %>
        </label>
      <% end %>
      <input
        type={@field[:input_type] || "text"}
        name={@field.name}
        value={@field[:value] || ""}
        placeholder={@field[:placeholder] || ""}
        maxlength={@field[:maxlength]}
        class={@field[:class] || "w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"}
        required={@field[:required] || false}
      />
      <%= if @field[:help_text] do %>
        <p class="mt-1 text-xs text-gray-500"><%= @field.help_text %></p>
      <% end %>
    </div>
    """
  end

  # 文本域
  defp render_textarea_field(assigns, field) do
    assigns = assign(assigns, :field, field)
    ~H"""
    <div class={@field[:wrapper_class] || ""}>
      <%= if @field[:label] do %>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          <%= if @field[:icon] do %>
            <i class={[@field.icon, "mr-1"]}></i>
          <% end %>
          <%= @field.label %>
          <%= if @field[:required] do %>
            <span class="text-red-500">*</span>
          <% end %>
        </label>
      <% end %>
      <textarea
        name={@field.name}
        rows={@field[:rows] || 4}
        maxlength={@field[:maxlength]}
        placeholder={@field[:placeholder] || ""}
        class={@field[:class] || "w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none"}
        required={@field[:required] || false}
      ><%= @field[:value] || "" %></textarea>
      <%= if @field[:help_text] do %>
        <p class="mt-1 text-xs text-gray-500"><%= @field.help_text %></p>
      <% end %>
    </div>
    """
  end

  # 下拉选择框
  defp render_select_field(assigns, field) do
    assigns = assign(assigns, :field, field)
    ~H"""
    <div class={@field[:wrapper_class] || ""}>
      <%= if @field[:label] do %>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          <%= if @field[:icon] do %>
            <i class={[@field.icon, "mr-1"]}></i>
          <% end %>
          <%= @field.label %>
          <%= if @field[:required] do %>
            <span class="text-red-500">*</span>
          <% end %>
        </label>
      <% end %>
      <select
        name={@field.name}
        class={@field[:class] || "w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"}
        required={@field[:required] || false}
      >
        <%= for option <- @field[:options] || [] do %>
          <option value={option[:value]} {if @field[:value] == option[:value], do: [selected: "selected"], else: []}>
            <%= option[:label] %>
          </option>
        <% end %>
      </select>
      <%= if @field[:help_text] do %>
        <p class="mt-1 text-xs text-gray-500"><%= @field.help_text %></p>
      <% end %>
    </div>
    """
  end

  # 复选框
  defp render_checkbox_field(assigns, field) do
    assigns = assign(assigns, :field, field)
    ~H"""
    <div class={@field[:wrapper_class] || "flex items-center"}>
      <label class="flex items-center space-x-3 cursor-pointer">
        <input
          type="checkbox"
          name={@field.name}
          value={@field[:checkbox_value] || "true"}
          {if @field[:value] || @field[:checked] || false, do: [checked: "checked"], else: []}
          class="w-5 h-5 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
        />
        <div>
          <%= if @field[:label] do %>
            <span class="text-sm font-medium text-gray-700"><%= @field.label %></span>
          <% end %>
          <%= if @field[:help_text] do %>
            <p class="text-xs text-gray-500"><%= @field.help_text %></p>
          <% end %>
        </div>
      </label>
    </div>
    """
  end

  # 隐藏字段
  defp render_hidden_field(assigns, field) do
    assigns = assign(assigns, :field, field)
    ~H"""
    <input type="hidden" name={@field.name} value={@field[:value] || ""} />
    """
  end
end
