# 系统通信管理数据库设计文档

## 概述

系统通信管理模块用于管理系统内的消息、公告和通知。该模块支持多种通信类型、优先级设置、过期时间管理和用户阅读状态跟踪。

## 数据库表结构

### 1. system_communications (系统通信主表)

存储所有系统消息、公告和通知的主要信息。

#### 字段说明

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | uuid | PRIMARY KEY, NOT NULL | gen_random_uuid() | 主键，自动生成UUID |
| type | text | NOT NULL | - | 通信类型：message/announcement/notification |
| title | text | NOT NULL | - | 标题，最大长度200字符 |
| content | text | NOT NULL | - | 内容，最大长度2000字符 |
| recipient_id | uuid | FOREIGN KEY | NULL | 特定接收者的用户ID |
| recipient_type | text | NOT NULL | "all" | 接收者类型：all/user/role |
| priority | text | NOT NULL | "medium" | 优先级：low/medium/high |
| active | boolean | NOT NULL | true | 激活状态 |
| expires_at | utc_datetime | NULL | NULL | 过期时间 |
| created_by | uuid | FOREIGN KEY | NULL | 创建者用户ID |
| inserted_at | utc_datetime_usec | NOT NULL | now() | 创建时间 |
| updated_at | utc_datetime_usec | NOT NULL | now() | 更新时间 |

#### 外键关系

- `recipient_id` → `users.id` (特定接收者)
- `created_by` → `users.id` (创建者)

#### 索引

- `system_communications_type_active_index` - 按类型和状态查询优化
- `system_communications_inserted_at_index` - 按创建时间排序优化
- `system_communications_expires_at_index` - 过期时间查询优化
- `system_communications_recipient_index` - 接收者查询优化

### 2. system_communication_reads (阅读记录表)

跟踪用户对系统通信的阅读状态，用于实现已读/未读功能。

#### 字段说明

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | uuid | PRIMARY KEY, NOT NULL | gen_random_uuid() | 主键，自动生成UUID |
| communication_id | uuid | FOREIGN KEY, NOT NULL | - | 关联的通信ID |
| user_id | uuid | FOREIGN KEY, NOT NULL | - | 阅读用户ID |
| read_at | utc_datetime | NOT NULL | now() | 阅读时间 |
| ip_address | text | NULL | NULL | 阅读时的IP地址 |
| user_agent | text | NULL | NULL | 阅读时的浏览器信息 |
| inserted_at | utc_datetime_usec | NOT NULL | now() | 创建时间 |
| updated_at | utc_datetime_usec | NOT NULL | now() | 更新时间 |

#### 外键关系

- `communication_id` → `system_communications.id` (关联通信)
- `user_id` → `users.id` (阅读用户)

#### 唯一约束

- `system_communication_reads_unique_user_communication_index` - 确保每个用户对每个通信只能有一条阅读记录

## 通信类型说明

### 1. message (消息)
- 用于一般性的系统消息
- 通常用于通知用户操作结果、系统状态等
- 优先级通常为 medium

### 2. announcement (公告)
- 用于重要的系统公告
- 通常用于系统维护通知、新功能发布等
- 优先级通常为 high

### 3. notification (通知)
- 用于实时通知
- 通常用于即时提醒、警告等
- 优先级可以是 low、medium 或 high

## 接收者类型说明

### 1. all (所有用户)
- 发送给系统中的所有用户
- `recipient_id` 字段为 NULL

### 2. user (特定用户)
- 发送给特定的单个用户
- `recipient_id` 字段包含目标用户的UUID

### 3. role (特定角色)
- 发送给特定角色的所有用户
- `recipient_id` 字段包含目标角色的UUID

## 优先级说明

### 1. low (低优先级)
- 一般性信息，不紧急
- 在UI中可能显示为灰色或普通样式

### 2. medium (中等优先级)
- 重要但不紧急的信息
- 在UI中可能显示为蓝色或标准样式

### 3. high (高优先级)
- 紧急或非常重要的信息
- 在UI中可能显示为红色或醒目样式

## 数据库操作示例

### 创建系统公告
```sql
INSERT INTO system_communications (
  type, title, content, recipient_type, priority, active, created_by
) VALUES (
  'announcement',
  '系统维护通知',
  '系统将于今晚22:00-24:00进行维护，期间可能无法正常使用。',
  'all',
  'high',
  true,
  '创建者用户UUID'
);
```

### 查询用户未读通信
```sql
SELECT sc.* 
FROM system_communications sc
LEFT JOIN system_communication_reads scr 
  ON sc.id = scr.communication_id 
  AND scr.user_id = '用户UUID'
WHERE sc.active = true
  AND (sc.expires_at IS NULL OR sc.expires_at > NOW())
  AND (sc.recipient_type = 'all' OR 
       (sc.recipient_type = 'user' AND sc.recipient_id = '用户UUID'))
  AND scr.id IS NULL
ORDER BY sc.priority DESC, sc.inserted_at DESC;
```

### 标记通信为已读
```sql
INSERT INTO system_communication_reads (
  communication_id, user_id, ip_address, user_agent
) VALUES (
  '通信UUID',
  '用户UUID',
  '用户IP地址',
  '用户浏览器信息'
) ON CONFLICT (user_id, communication_id) DO NOTHING;
```

## 性能优化建议

1. **定期清理过期数据**：定期删除过期的通信记录和相关的阅读记录
2. **分页查询**：对于大量数据的查询，使用分页避免性能问题
3. **索引维护**：定期检查和维护索引的性能
4. **缓存策略**：对于频繁查询的数据，考虑使用缓存

## 安全考虑

1. **权限控制**：确保只有授权用户可以创建和管理系统通信
2. **内容过滤**：对通信内容进行敏感词过滤
3. **审计日志**：记录所有创建、修改、删除操作的审计日志
4. **数据验证**：严格验证输入数据的格式和长度

## 扩展性考虑

1. **多语言支持**：可以扩展表结构支持多语言内容
2. **富文本支持**：可以扩展内容字段支持HTML或Markdown格式
3. **附件支持**：可以添加附件表支持文件附件
4. **模板支持**：可以添加模板功能支持通信模板
