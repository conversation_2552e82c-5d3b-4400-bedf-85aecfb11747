defmodule RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.ActivitySystemRepositories do
  @moduledoc """
  🎯 活动系统仓储集合

  统一管理所有活动系统相关的仓储操作，包括：
  - FreeCash仓储操作
  - BankruptcyAssist仓储操作
  - TaskLevel仓储操作
  - LevelReward仓储操作
  - RewardMultiplier仓储操作
  """

  require Logger
  alias Teen.ActivitySystem.{FreeCash, BankruptcyAssist, TaskLevel, LevelReward, RewardMultiplier}
  alias Ash.Query
  import Ash.Query

  # ==================== FreeCash 仓储操作 ====================

  @doc """
  创建Free Cash
  """
  def create_free_cash(cash_data, options \\ []) do
    Logger.info("💰 [Free Cash仓储] 创建Free Cash: #{inspect(cash_data[:cash_name])}")

    try do
      result = FreeCash
      |> Ash.Changeset.for_create(:create, cash_data)
      |> Ash.create()

      case result do
        {:ok, cash} ->
          Logger.info("✅ [Free Cash仓储] Free Cash创建成功: #{cash.cash_name}")
          maybe_load_associations({:ok, cash}, options)

        {:error, reason} ->
          Logger.error("❌ [Free Cash仓储] Free Cash创建失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [Free Cash仓储] 创建Free Cash异常: #{inspect(exception)}")
        {:error, :create_failed}
    end
  end

  @doc """
  获取Free Cash
  """
  def get_free_cash(cash_id, options \\ []) do
    Logger.info("💰 [Free Cash仓储] 获取Free Cash: #{cash_id}")

    try do
      result = FreeCash
      |> Ash.Query.for_read(:read)
      |> Ash.Query.filter(id == ^cash_id)
      |> maybe_load_associations(options)
      |> Ash.read_one()

      case result do
        {:ok, nil} -> {:error, :not_found}
        {:ok, cash} -> {:ok, cash}
        {:error, reason} -> {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [Free Cash仓储] 获取Free Cash异常: #{inspect(exception)}")
        {:error, :query_failed}
    end
  end

  @doc """
  获取启用的Free Cash列表
  """
  def list_active_free_cashes(options \\ []) do
    Logger.info("💰 [Free Cash仓储] 获取启用的Free Cash列表")

    try do
      result = FreeCash
      |> Ash.Query.for_read(:list_active_cashes)
      |> apply_sorting(options)
      |> maybe_load_associations(options)
      |> Ash.read()

      case result do
        {:ok, cashes} ->
          Logger.info("✅ [Free Cash仓储] 获取启用Free Cash成功: #{length(cashes)}条")
          {:ok, cashes}

        {:error, reason} ->
          Logger.error("❌ [Free Cash仓储] 获取启用Free Cash失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [Free Cash仓储] 获取启用Free Cash异常: #{inspect(exception)}")
        {:error, :query_failed}
    end
  end

  # ==================== BankruptcyAssist 仓储操作 ====================

  @doc """
  创建破产补助
  """
  def create_bankruptcy_assist(assist_data, options \\ []) do
    Logger.info("🆘 [破产补助仓储] 创建破产补助: #{inspect(assist_data[:assist_name])}")

    try do
      result = BankruptcyAssist
      |> Ash.Changeset.for_create(:create, assist_data)
      |> Ash.create()

      case result do
        {:ok, assist} ->
          Logger.info("✅ [破产补助仓储] 破产补助创建成功: #{assist.assist_name}")
          maybe_load_associations({:ok, assist}, options)

        {:error, reason} ->
          Logger.error("❌ [破产补助仓储] 破产补助创建失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [破产补助仓储] 创建破产补助异常: #{inspect(exception)}")
        {:error, :create_failed}
    end
  end

  @doc """
  获取破产补助
  """
  def get_bankruptcy_assist(assist_id, options \\ []) do
    Logger.info("🆘 [破产补助仓储] 获取破产补助: #{assist_id}")

    try do
      result = BankruptcyAssist
      |> Ash.Query.for_read(:read)
      |> Ash.Query.filter(id == ^assist_id)
      |> maybe_load_associations(options)
      |> Ash.read_one()

      case result do
        {:ok, nil} -> {:error, :not_found}
        {:ok, assist} -> {:ok, assist}
        {:error, reason} -> {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [破产补助仓储] 获取破产补助异常: #{inspect(exception)}")
        {:error, :query_failed}
    end
  end

  @doc """
  获取启用的破产补助列表
  """
  def list_active_bankruptcy_assists(options \\ []) do
    Logger.info("🆘 [破产补助仓储] 获取启用的破产补助列表")

    try do
      result = BankruptcyAssist
      |> Ash.Query.for_read(:list_active_assists)
      |> apply_sorting(options)
      |> maybe_load_associations(options)
      |> Ash.read()

      case result do
        {:ok, assists} ->
          Logger.info("✅ [破产补助仓储] 获取启用破产补助成功: #{length(assists)}条")
          {:ok, assists}

        {:error, reason} ->
          Logger.error("❌ [破产补助仓储] 获取启用破产补助失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [破产补助仓储] 获取启用破产补助异常: #{inspect(exception)}")
        {:error, :query_failed}
    end
  end

  # ==================== TaskLevel 仓储操作 ====================

  @doc """
  创建任务等级
  """
  def create_task_level(level_data, options \\ []) do
    Logger.info("📊 [任务等级仓储] 创建任务等级: #{inspect(level_data[:level_name])}")

    try do
      result = TaskLevel
      |> Ash.Changeset.for_create(:create, level_data)
      |> Ash.create()

      case result do
        {:ok, level} ->
          Logger.info("✅ [任务等级仓储] 任务等级创建成功: #{level.level_name}")
          maybe_load_associations({:ok, level}, options)

        {:error, reason} ->
          Logger.error("❌ [任务等级仓储] 任务等级创建失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [任务等级仓储] 创建任务等级异常: #{inspect(exception)}")
        {:error, :create_failed}
    end
  end

  @doc """
  根据等级范围获取任务等级列表
  """
  def list_task_levels_by_range(min_level, max_level, options \\ []) do
    Logger.info("📊 [任务等级仓储] 根据等级范围获取任务等级列表: #{min_level}-#{max_level}")

    try do
      result = TaskLevel
      |> Ash.Query.for_read(:list_by_level_range, %{min_level: min_level, max_level: max_level})
      |> apply_sorting(options)
      |> maybe_load_associations(options)
      |> Ash.read()

      case result do
        {:ok, levels} ->
          Logger.info("✅ [任务等级仓储] 获取等级范围任务等级成功: #{length(levels)}条")
          {:ok, levels}

        {:error, reason} ->
          Logger.error("❌ [任务等级仓储] 获取等级范围任务等级失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [任务等级仓储] 获取等级范围任务等级异常: #{inspect(exception)}")
        {:error, :query_failed}
    end
  end

  # ==================== LevelReward 仓储操作 ====================

  @doc """
  创建等级奖励
  """
  def create_level_reward(reward_data, options \\ []) do
    Logger.info("🏆 [等级奖励仓储] 创建等级奖励: #{inspect(reward_data[:reward_name])}")

    try do
      result = LevelReward
      |> Ash.Changeset.for_create(:create, reward_data)
      |> Ash.create()

      case result do
        {:ok, reward} ->
          Logger.info("✅ [等级奖励仓储] 等级奖励创建成功: #{reward.reward_name}")
          maybe_load_associations({:ok, reward}, options)

        {:error, reason} ->
          Logger.error("❌ [等级奖励仓储] 等级奖励创建失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [等级奖励仓储] 创建等级奖励异常: #{inspect(exception)}")
        {:error, :create_failed}
    end
  end

  @doc """
  根据等级获取等级奖励列表
  """
  def list_level_rewards_by_level(target_level, options \\ []) do
    Logger.info("🏆 [等级奖励仓储] 根据等级获取等级奖励列表: #{target_level}")

    try do
      result = LevelReward
      |> Ash.Query.for_read(:list_by_level, %{target_level: target_level})
      |> apply_sorting(options)
      |> maybe_load_associations(options)
      |> Ash.read()

      case result do
        {:ok, rewards} ->
          Logger.info("✅ [等级奖励仓储] 获取等级奖励成功: #{length(rewards)}条")
          {:ok, rewards}

        {:error, reason} ->
          Logger.error("❌ [等级奖励仓储] 获取等级奖励失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [等级奖励仓储] 获取等级奖励异常: #{inspect(exception)}")
        {:error, :query_failed}
    end
  end

  # ==================== RewardMultiplier 仓储操作 ====================

  @doc """
  创建奖励倍率
  """
  def create_reward_multiplier(multiplier_data, options \\ []) do
    Logger.info("⚡ [奖励倍率仓储] 创建奖励倍率: #{inspect(multiplier_data[:multiplier_name])}")

    try do
      result = RewardMultiplier
      |> Ash.Changeset.for_create(:create, multiplier_data)
      |> Ash.create()

      case result do
        {:ok, multiplier} ->
          Logger.info("✅ [奖励倍率仓储] 奖励倍率创建成功: #{multiplier.multiplier_name}")
          maybe_load_associations({:ok, multiplier}, options)

        {:error, reason} ->
          Logger.error("❌ [奖励倍率仓储] 奖励倍率创建失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [奖励倍率仓储] 创建奖励倍率异常: #{inspect(exception)}")
        {:error, :create_failed}
    end
  end

  @doc """
  获取当前有效的奖励倍率列表
  """
  def list_current_reward_multipliers(options \\ []) do
    Logger.info("⚡ [奖励倍率仓储] 获取当前有效的奖励倍率列表")

    try do
      result = RewardMultiplier
      |> Ash.Query.for_read(:list_current_multipliers)
      |> apply_sorting(options)
      |> maybe_load_associations(options)
      |> Ash.read()

      case result do
        {:ok, multipliers} ->
          Logger.info("✅ [奖励倍率仓储] 获取当前奖励倍率成功: #{length(multipliers)}条")
          {:ok, multipliers}

        {:error, reason} ->
          Logger.error("❌ [奖励倍率仓储] 获取当前奖励倍率失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [奖励倍率仓储] 获取当前奖励倍率异常: #{inspect(exception)}")
        {:error, :query_failed}
    end
  end

  # ==================== 私有辅助函数 ====================

  # 应用排序
  defp apply_sorting(query, options) do
    case options[:sort] do
      nil -> Ash.Query.sort(query, inserted_at: :desc)
      sort_params -> Ash.Query.sort(query, sort_params)
    end
  end

  # 可能加载关联数据
  defp maybe_load_associations({:ok, data}, options) do
    case options[:load] do
      nil -> {:ok, data}
      associations ->
        case Ash.load(data, associations) do
          {:ok, loaded_data} -> {:ok, loaded_data}
          {:error, reason} -> {:error, reason}
        end
    end
  end

  defp maybe_load_associations(query, options) do
    case options[:load] do
      nil -> query
      associations -> Ash.Query.load(query, associations)
    end
  end
end
