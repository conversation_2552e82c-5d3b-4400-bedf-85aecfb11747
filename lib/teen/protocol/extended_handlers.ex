defmodule Cypridina.Protocol.ExtendedHandlers do
  @moduledoc """
  cypridina项目的扩展协议处理函数模块
  处理QMAgent、Task、HallActivity、LongHu等协议
  参考IndiaGameServer的处理逻辑
  """

  require Logger

  alias Cypridina.GameSystem.Games.LongHu.LongHuProtocol

  # ==================== QMAgent 协议处理 ====================

  @doc """
  处理获得推广佣金信息 (QMAgent.CS_AGENT_PROMOTIONDATA)
  """
  def handle_agent_promotion_data(%{main_id: 41, sub_id: 0} = message, state) do
    Logger.info("获得推广佣金信息: #{inspect(message.data)}")

    # 模拟代理推广数据
    promotion_data = %{
      "total_commission" => 15000.50,
      "this_month_commission" => 3200.00,
      "today_commission" => 150.00,
      "total_invites" => 45,
      "active_invites" => 32,
      "commission_rate" => 0.15,
      "next_level_requirement" => 100,
      "current_level" => 2
    }

    response_data = %{
      "status" => 0,
      "promotion_data" => promotion_data,
      "message" => "获取推广数据成功"
    }

    response_map = %{
      "mainId" => 41,
      "subId" => 1,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理领取佣金 (QMAgent.CS_AGENT_GETMONEY)
  """
  def handle_agent_get_money(%{main_id: 41, sub_id: 2} = message, state) do
    Logger.info("领取佣金: #{inspect(message.data)}")

    data = message.data || %{}
    amount = Map.get(data, "amount", 0)

    response_data =
      cond do
        amount <= 0 ->
          %{
            "status" => 1,
            "message" => "领取金额必须大于0"
          }

        amount > 10000 ->
          %{
            "status" => 2,
            "message" => "单次领取金额不能超过10000"
          }

        true ->
          %{
            "status" => 0,
            "amount" => amount,
            "remaining_commission" => 15000.50 - amount,
            "message" => "佣金领取成功"
          }
      end

    response_map = %{
      "mainId" => 41,
      "subId" => 3,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理佣金明细 (QMAgent.CS_AGENT_MONEYDETAIL)
  """
  def handle_agent_money_detail(%{main_id: 41, sub_id: 4} = message, state) do
    Logger.info("佣金明细: #{inspect(message.data)}")

    data = message.data || %{}
    page = Map.get(data, "page", 1)
    page_size = Map.get(data, "page_size", 10)

    # 模拟佣金明细
    money_details = [
      %{
        "id" => 1,
        # 1-推广佣金, 2-活跃佣金
        "type" => 1,
        "amount" => 150.00,
        "from_user" => "user_123",
        "from_nickname" => "玩家123",
        "time" => System.system_time(:millisecond) - 3_600_000,
        "description" => "推广佣金"
      },
      %{
        "id" => 2,
        "type" => 2,
        "amount" => 80.00,
        "from_user" => "user_456",
        "from_nickname" => "玩家456",
        "time" => System.system_time(:millisecond) - 7_200_000,
        "description" => "活跃佣金"
      }
    ]

    response_data = %{
      "status" => 0,
      "money_details" => money_details,
      "page" => page,
      "page_size" => page_size,
      "total_count" => length(money_details)
    }

    response_map = %{
      "mainId" => 41,
      "subId" => 5,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理我的团队 (QMAgent.CS_AGENT_MYTEAM)
  """
  def handle_agent_my_team(%{main_id: 41, sub_id: 6} = message, state) do
    Logger.info("我的团队: #{inspect(message.data)}")

    # 模拟团队数据
    team_members = [
      %{
        "user_id" => "user_001",
        "nickname" => "团队成员1",
        "level" => 15,
        "total_recharge" => 5000.00,
        "commission_generated" => 750.00,
        "join_time" => System.system_time(:millisecond) - 86_400_000 * 30,
        "last_active" => System.system_time(:millisecond) - 3_600_000,
        # 1-活跃, 0-不活跃
        "status" => 1
      },
      %{
        "user_id" => "user_002",
        "nickname" => "团队成员2",
        "level" => 8,
        "total_recharge" => 2000.00,
        "commission_generated" => 300.00,
        "join_time" => System.system_time(:millisecond) - 86_400_000 * 15,
        "last_active" => System.system_time(:millisecond) - 86_400_000,
        "status" => 0
      }
    ]

    response_data = %{
      "status" => 0,
      "team_members" => team_members,
      "total_members" => length(team_members),
      "active_members" => Enum.count(team_members, fn member -> member["status"] == 1 end),
      "total_commission" =>
        Enum.reduce(team_members, 0, fn member, acc -> acc + member["commission_generated"] end)
    }

    response_map = %{
      "mainId" => 41,
      "subId" => 7,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # ==================== Task 协议处理 ====================

  @doc """
  处理更新任务列表信息 (Task.CS_UPDATE_TASK_LIST)
  """
  def handle_update_task_list(%{main_id: 42, sub_id: 0} = message, state) do
    Logger.info("更新任务列表信息: #{inspect(message.data)}")

    # 模拟任务列表
    task_list = [
      %{
        "id" => 1,
        "name" => "每日登录",
        "description" => "每日登录游戏获得奖励",
        # 1-每日任务, 2-周任务, 3-成就任务
        "type" => 1,
        "progress" => 1,
        "target" => 1,
        # 0-未完成, 1-已完成, 2-已领取
        "status" => 2,
        "rewards" => [
          %{"type" => "money", "amount" => 1000},
          %{"type" => "exp", "amount" => 100}
        ]
      },
      %{
        "id" => 2,
        "name" => "游戏5局",
        "description" => "完成5局游戏",
        "type" => 1,
        "progress" => 3,
        "target" => 5,
        "status" => 0,
        "rewards" => [
          %{"type" => "money", "amount" => 2000}
        ]
      },
      %{
        "id" => 3,
        "name" => "胜利3局",
        "description" => "获得3局胜利",
        "type" => 1,
        "progress" => 1,
        "target" => 3,
        "status" => 0,
        "rewards" => [
          %{"type" => "money", "amount" => 3000},
          %{"type" => "item", "item_id" => 1, "count" => 1}
        ]
      }
    ]

    response_data = %{
      "status" => 0,
      "task_list" => task_list,
      "total_tasks" => length(task_list),
      "completed_tasks" => Enum.count(task_list, fn task -> task["status"] >= 1 end)
    }

    response_map = %{
      "mainId" => 42,
      "subId" => 1,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理领取任务奖励 (Task.CS_GET_TASK_REWARD)
  """
  def handle_get_task_reward(%{main_id: 42, sub_id: 2} = message, state) do
    Logger.info("领取任务奖励: #{inspect(message.data)}")

    data = message.data || %{}
    task_id = Map.get(data, "task_id", 0)

    response_data =
      cond do
        task_id <= 0 ->
          %{
            "status" => 1,
            "message" => "无效的任务ID"
          }

        true ->
          # 模拟任务奖励
          rewards = [
            %{"type" => "money", "amount" => 1000},
            %{"type" => "exp", "amount" => 100}
          ]

          %{
            "status" => 0,
            "task_id" => task_id,
            "rewards" => rewards,
            "message" => "任务奖励领取成功"
          }
      end

    response_map = %{
      "mainId" => 42,
      "subId" => 3,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理获取比赛列表信息 (Task.CS_GET_MATCH_LIST)
  """
  def handle_get_match_list(%{main_id: 42, sub_id: 11} = message, state) do
    Logger.info("获取比赛列表信息: #{inspect(message.data)}")

    # 模拟比赛列表
    match_list = [
      %{
        "id" => 1,
        "name" => "每日金币赛",
        "description" => "每日金币排行赛",
        # 1-金币赛, 2-积分赛
        "type" => 1,
        "start_time" => System.system_time(:millisecond),
        "end_time" => System.system_time(:millisecond) + 86_400_000,
        "entry_fee" => 1000,
        "max_players" => 100,
        "current_players" => 45,
        # 0-未开始, 1-进行中, 2-已结束
        "status" => 1,
        "rewards" => [
          %{"rank" => 1, "reward" => [%{"type" => "money", "amount" => 50000}]},
          %{"rank" => 2, "reward" => [%{"type" => "money", "amount" => 30000}]},
          %{"rank" => 3, "reward" => [%{"type" => "money", "amount" => 20000}]}
        ]
      },
      %{
        "id" => 2,
        "name" => "周末大奖赛",
        "description" => "周末特别大奖赛",
        "type" => 2,
        "start_time" => System.system_time(:millisecond) + 86_400_000,
        "end_time" => System.system_time(:millisecond) + 86_400_000 * 3,
        "entry_fee" => 5000,
        "max_players" => 500,
        "current_players" => 0,
        "status" => 0,
        "rewards" => [
          %{"rank" => 1, "reward" => [%{"type" => "money", "amount" => 500_000}]},
          %{"rank" => 2, "reward" => [%{"type" => "money", "amount" => 300_000}]},
          %{"rank" => 3, "reward" => [%{"type" => "money", "amount" => 200_000}]}
        ]
      }
    ]

    response_data = %{
      "status" => 0,
      "match_list" => match_list,
      "total_matches" => length(match_list)
    }

    response_map = %{
      "mainId" => 42,
      "subId" => 12,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理获取比赛排名和奖励 (Task.CS_GET_MATCH_RANK_REWARD)
  """
  def handle_get_match_rank_reward(%{main_id: 42, sub_id: 13} = message, state) do
    Logger.info("获取比赛排名和奖励: #{inspect(message.data)}")

    data = message.data || %{}
    match_id = Map.get(data, "match_id", 0)

    # 模拟比赛排名
    rank_list = [
      %{
        "rank" => 1,
        "user_id" => "user_001",
        "nickname" => "比赛冠军",
        "score" => 100_000,
        "reward_claimed" => true
      },
      %{
        "rank" => 2,
        "user_id" => "user_002",
        "nickname" => "比赛亚军",
        "score" => 80000,
        "reward_claimed" => false
      },
      %{
        "rank" => 3,
        "user_id" => state.user_id,
        "nickname" => "我的昵称",
        "score" => 60000,
        "reward_claimed" => false
      }
    ]

    my_rank = Enum.find_index(rank_list, fn player -> player["user_id"] == state.user_id end)
    my_rank = if my_rank, do: my_rank + 1, else: nil

    response_data = %{
      "status" => 0,
      "match_id" => match_id,
      "rank_list" => rank_list,
      "my_rank" => my_rank,
      "my_score" => if(my_rank, do: 60000, else: 0),
      "can_claim_reward" => my_rank != nil and my_rank <= 10
    }

    response_map = %{
      "mainId" => 42,
      "subId" => 14,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # ==================== HallActivity 协议处理 ====================

  @doc """
  处理请求登录活动信息 (HallActivity.CS_LOGINCASH_INFO_P)
  """
  def handle_login_cash_info(%{main_id: 101, sub_id: 0} = message, state) do
    Logger.info("请求登录活动信息: #{inspect(message.data)}")

    # 模拟登录活动信息
    activity_info = %{
      "activity_id" => 1,
      "activity_name" => "七日登录活动",
      "description" => "连续登录7天获得丰厚奖励",
      "start_time" => System.system_time(:millisecond) - 86_400_000,
      "end_time" => System.system_time(:millisecond) + 86_400_000 * 6,
      "current_day" => 2,
      "max_days" => 7,
      "daily_rewards" => [
        %{"day" => 1, "reward" => [%{"type" => "money", "amount" => 1000}], "claimed" => true},
        %{"day" => 2, "reward" => [%{"type" => "money", "amount" => 2000}], "claimed" => false},
        %{"day" => 3, "reward" => [%{"type" => "money", "amount" => 3000}], "claimed" => false},
        %{"day" => 4, "reward" => [%{"type" => "money", "amount" => 5000}], "claimed" => false},
        %{"day" => 5, "reward" => [%{"type" => "money", "amount" => 8000}], "claimed" => false},
        %{"day" => 6, "reward" => [%{"type" => "money", "amount" => 10000}], "claimed" => false},
        %{"day" => 7, "reward" => [%{"type" => "money", "amount" => 20000}], "claimed" => false}
      ],
      "can_claim_today" => true
    }

    response_data = %{
      "status" => 0,
      "activity_info" => activity_info
    }

    response_map = %{
      "mainId" => 101,
      "subId" => 1,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理领取登录活动奖励 (HallActivity.CS_FETCH_LOGINCASH_AWARD_P)
  """
  def handle_fetch_login_cash_award(%{main_id: 101, sub_id: 2} = message, state) do
    Logger.info("领取登录活动奖励: #{inspect(message.data)}")

    data = message.data || %{}
    # 0-登录, 1-充值, 2-游戏局数, 3-转盘, 4-游戏赢分, 10-完成
    fetch_type = Map.get(data, "fetchtype", 0)
    ip = Map.get(data, "ip", "")

    response_data =
      case fetch_type do
        # 登录奖励
        0 ->
          %{
            "status" => 0,
            "fetchaward" => 2000,
            "message" => "登录奖励领取成功",
            "next_day" => 3
          }

        # 充值奖励
        1 ->
          %{
            "status" => 0,
            "fetchaward" => 5000,
            "message" => "充值奖励领取成功"
          }

        # 游戏局数奖励
        2 ->
          %{
            "status" => 0,
            "fetchaward" => 1000,
            "message" => "游戏局数奖励领取成功"
          }

        _ ->
          %{
            "status" => 1,
            "message" => "无效的奖励类型"
          }
      end

    response_map = %{
      "mainId" => 101,
      "subId" => 3,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理获取用户金币信息 (HallActivity.CS_GET_USER_MONEY_P)
  """
  def handle_get_user_money(%{main_id: 101, sub_id: 4} = message, state) do
    Logger.info("🎮 [GET_USER_MONEY] 获取用户金币信息: #{inspect(message.data)}")

    # 获取用户真实积分
    real_points = Cypridina.Accounts.get_user_points(state.user_id)

    # 构建符合客户端期望的用户金币信息
    # 客户端期望的字段名称（小写）
    user_info = %{
      # 奖金金币 (暂时使用真实积分的一部分)
      "bonusmoney" => div(real_points, 10),
      # 奖金现金 (暂时使用真实积分的一部分)
      "bonuscashmoney" => div(real_points, 5),
      # 当前金币 (使用真实积分)
      "money" => real_points,
      # 今日赢取金币 (暂时设为0，后续可从游戏记录计算)
      "winningmoney" => 0,
      # VIP等级 (暂时根据积分计算)
      "vip" => calculate_vip_level(real_points)
    }

    # 按照客户端期望的格式返回，包含code和msg字段
    response_data = %{
      # 成功状态码（客户端期望）
      "code" => 0,
      # 成功消息（客户端期望）
      "msg" => "获取成功",
      # 客户端期望的user字段
      "user" => user_info
    }

    response_map = %{
      "mainId" => 101,
      "subId" => 5,
      "data" => response_data
    }

    Logger.info("🎮 [GET_USER_MONEY] 返回用户金币信息: #{inspect(response_data)}")

    {:reply, response_map, state}
  end

  @doc """
  处理领取用户积分信息 (HallActivity.CS_FETCH_USER_BONUS_P)
  """
  def handle_fetch_user_bonus(%{main_id: 101, sub_id: 6} = message, state) do
    Logger.info("领取用户积分信息: #{inspect(message.data)}")

    data = message.data || %{}
    fetch_amount = Map.get(data, "fetchamount", 0)
    ip = Map.get(data, "ip", "")

    response_data =
      cond do
        fetch_amount <= 0 ->
          %{
            "status" => 1,
            "message" => "领取积分必须大于0"
          }

        fetch_amount > 10000 ->
          %{
            "status" => 2,
            "message" => "单次领取积分不能超过10000"
          }

        true ->
          %{
            "status" => 0,
            "fetch_amount" => fetch_amount,
            "remaining_bonus" => 50000 - fetch_amount,
            "message" => "积分领取成功"
          }
      end

    response_map = %{
      "mainId" => 101,
      "subId" => 7,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  @doc """
  处理请求7日签到活动信息 (HallActivity.CS_GET_SEVEN_DAYS_P)
  """
  def handle_get_seven_days(%{main_id: 101, sub_id: 8} = message, state) do
    Logger.info("请求7日签到活动信息: #{inspect(message.data)}")

    # 模拟7日签到活动信息
    seven_days_info = %{
      "activity_id" => 2,
      "activity_name" => "七日签到豪礼",
      "description" => "连续签到7天，奖励递增",
      "current_day" => 3,
      "consecutive_days" => 3,
      "sign_today" => false,
      "sign_records" => [
        %{"day" => 1, "signed" => true, "reward" => [%{"type" => "money", "amount" => 1000}]},
        %{"day" => 2, "signed" => true, "reward" => [%{"type" => "money", "amount" => 2000}]},
        %{"day" => 3, "signed" => false, "reward" => [%{"type" => "money", "amount" => 3000}]},
        %{"day" => 4, "signed" => false, "reward" => [%{"type" => "money", "amount" => 5000}]},
        %{"day" => 5, "signed" => false, "reward" => [%{"type" => "money", "amount" => 8000}]},
        %{"day" => 6, "signed" => false, "reward" => [%{"type" => "money", "amount" => 12000}]},
        %{"day" => 7, "signed" => false, "reward" => [%{"type" => "money", "amount" => 20000}]}
      ],
      "can_sign_today" => true,
      "next_reset_time" => System.system_time(:millisecond) + 86_400_000
    }

    response_data = %{
      "status" => 0,
      "seven_days_info" => seven_days_info
    }

    response_map = %{
      "mainId" => 101,
      "subId" => 9,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # ==================== LongHu 龙虎斗游戏协议处理 ====================

  @doc """
  处理加入龙虎斗房间 (LongHu.CS_JOIN_ROOM)
  """
  def handle_longhu_join_room(%{main_id: 201, sub_id: 1} = message, state) do
    Logger.info("🐉 [LONGHU] 处理加入房间请求: #{inspect(message.data)}")
    Cypridina.Teen.GameSystem.Games.LongHu.LongHuProtocol.handle_join_room(message, state)
  end

  @doc """
  处理离开龙虎斗房间 (LongHu.CS_LEAVE_ROOM)
  """
  def handle_longhu_leave_room(%{main_id: 201, sub_id: 2} = message, state) do
    Logger.info("🐉 [LONGHU] 处理离开房间请求: #{inspect(message.data)}")
    Cypridina.Teen.GameSystem.Games.LongHu.LongHuProtocol.handle_leave_room(message, state)
  end

  @doc """
  检查是否为龙虎斗协议
  """
  def is_longhu_protocol?(main_id, sub_id) do
    Cypridina.Teen.GameSystem.Games.LongHu.LongHuProtocol.is_longhu_protocol?(main_id, sub_id)
  end

  # 私有辅助函数

  # 根据积分计算VIP等级
  defp calculate_vip_level(points) do
    cond do
      points >= 1_000_000 -> 5
      points >= 500_000 -> 4
      points >= 100_000 -> 3
      points >= 50_000 -> 2
      points >= 10_000 -> 1
      true -> 0
    end
  end
end
