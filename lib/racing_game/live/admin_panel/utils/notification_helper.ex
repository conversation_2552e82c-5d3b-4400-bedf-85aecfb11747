defmodule RacingGame.Live.AdminPanel.Utils.NotificationHelper do
  @moduledoc """
  通知助手工具
  
  提供通知发送功能：
  - 系统通知
  - 用户通知
  - 管理员通知
  - 实时推送
  """

  require Logger
  alias Phoenix.PubSub

  # 常量定义
  @pubsub_name Cypridina.PubSub
  @notification_topics %{
    all_users: "notifications:all_users",
    admins: "notifications:admins",
    user: "notifications:user:"
  }

  # ============================================================================
  # 广播通知
  # ============================================================================

  @doc """
  向所有用户广播通知

  ## 参数
  - `notification` - 通知内容

  ## 返回
  - `:ok` - 成功
  - `{:error, reason}` - 失败
  """
  def broadcast_to_all(notification) do
    Logger.info("📢 [通知助手] 向所有用户广播通知: #{notification.title}")
    
    try do
      message = format_notification_message(notification, :broadcast)
      
      PubSub.broadcast(@pubsub_name, @notification_topics.all_users, {
        :new_notification,
        message
      })
      
      Logger.debug("✅ [通知助手] 广播通知发送成功")
      :ok
    rescue
      error ->
        Logger.error("❌ [通知助手] 广播通知发送失败: #{inspect(error)}")
        {:error, :broadcast_failed}
    end
  end

  @doc """
  向管理员广播通知

  ## 参数
  - `notification` - 通知内容

  ## 返回
  - `:ok` - 成功
  - `{:error, reason}` - 失败
  """
  def broadcast_to_admins(notification) do
    Logger.info("👨‍💼 [通知助手] 向管理员广播通知: #{notification.title}")
    
    try do
      message = format_notification_message(notification, :admin_broadcast)
      
      PubSub.broadcast(@pubsub_name, @notification_topics.admins, {
        :admin_notification,
        message
      })
      
      Logger.debug("✅ [通知助手] 管理员通知发送成功")
      :ok
    rescue
      error ->
        Logger.error("❌ [通知助手] 管理员通知发送失败: #{inspect(error)}")
        {:error, :admin_broadcast_failed}
    end
  end

  @doc """
  向特定用户发送通知

  ## 参数
  - `user_id` - 用户ID
  - `notification` - 通知内容

  ## 返回
  - `:ok` - 成功
  - `{:error, reason}` - 失败
  """
  def send_to_user(user_id, notification) do
    Logger.info("👤 [通知助手] 向用户发送通知: #{user_id} - #{notification.title}")
    
    try do
      message = format_notification_message(notification, :user)
      topic = @notification_topics.user <> user_id
      
      PubSub.broadcast(@pubsub_name, topic, {
        :user_notification,
        message
      })
      
      Logger.debug("✅ [通知助手] 用户通知发送成功")
      :ok
    rescue
      error ->
        Logger.error("❌ [通知助手] 用户通知发送失败: #{inspect(error)}")
        {:error, :user_notification_failed}
    end
  end

  @doc """
  批量向用户发送通知

  ## 参数
  - `user_ids` - 用户ID列表
  - `notification` - 通知内容

  ## 返回
  - `{:ok, results}` - 成功，返回发送结果
  - `{:error, reason}` - 失败
  """
  def send_to_users(user_ids, notification) when is_list(user_ids) do
    Logger.info("👥 [通知助手] 批量发送用户通知: #{length(user_ids)}个用户")
    
    results = Enum.map(user_ids, fn user_id ->
      case send_to_user(user_id, notification) do
        :ok -> {:ok, user_id}
        {:error, reason} -> {:error, {user_id, reason}}
      end
    end)
    
    success_count = Enum.count(results, &match?({:ok, _}, &1))
    failure_count = length(user_ids) - success_count
    
    Logger.info("📊 [通知助手] 批量发送完成: 成功#{success_count}个，失败#{failure_count}个")
    
    {:ok, %{
      total: length(user_ids),
      success: success_count,
      failure: failure_count,
      results: results
    }}
  end

  # ============================================================================
  # 实时推送
  # ============================================================================

  @doc """
  推送系统状态更新

  ## 参数
  - `status_type` - 状态类型
  - `status_data` - 状态数据

  ## 返回
  - `:ok` - 成功
  - `{:error, reason}` - 失败
  """
  def push_system_status(status_type, status_data) do
    Logger.debug("🔄 [通知助手] 推送系统状态更新: #{status_type}")
    
    try do
      message = %{
        type: :system_status,
        status_type: status_type,
        data: status_data,
        timestamp: DateTime.utc_now()
      }
      
      PubSub.broadcast(@pubsub_name, @notification_topics.admins, {
        :system_status_update,
        message
      })
      
      :ok
    rescue
      error ->
        Logger.error("❌ [通知助手] 系统状态推送失败: #{inspect(error)}")
        {:error, :status_push_failed}
    end
  end

  @doc """
  推送数据更新通知

  ## 参数
  - `data_type` - 数据类型
  - `action` - 操作类型 (:create, :update, :delete)
  - `data` - 数据内容

  ## 返回
  - `:ok` - 成功
  - `{:error, reason}` - 失败
  """
  def push_data_update(data_type, action, data) do
    Logger.debug("📊 [通知助手] 推送数据更新: #{data_type} - #{action}")
    
    try do
      message = %{
        type: :data_update,
        data_type: data_type,
        action: action,
        data: sanitize_data_for_broadcast(data),
        timestamp: DateTime.utc_now()
      }
      
      # 根据数据类型选择推送范围
      topic = case data_type do
        :communication -> @notification_topics.all_users
        :bet -> @notification_topics.admins
        :stock -> @notification_topics.admins
        :user -> @notification_topics.admins
        _ -> @notification_topics.admins
      end
      
      PubSub.broadcast(@pubsub_name, topic, {
        :data_update,
        message
      })
      
      :ok
    rescue
      error ->
        Logger.error("❌ [通知助手] 数据更新推送失败: #{inspect(error)}")
        {:error, :data_update_push_failed}
    end
  end

  # ============================================================================
  # 订阅管理
  # ============================================================================

  @doc """
  订阅所有用户通知

  ## 参数
  - `pid` - 进程PID

  ## 返回
  - `:ok` - 成功
  """
  def subscribe_all_notifications(pid \\ self()) do
    Logger.debug("🔔 [通知助手] 订阅所有用户通知")
    PubSub.subscribe(@pubsub_name, @notification_topics.all_users)
  end

  @doc """
  订阅管理员通知

  ## 参数
  - `pid` - 进程PID

  ## 返回
  - `:ok` - 成功
  """
  def subscribe_admin_notifications(pid \\ self()) do
    Logger.debug("👨‍💼 [通知助手] 订阅管理员通知")
    PubSub.subscribe(@pubsub_name, @notification_topics.admins)
  end

  @doc """
  订阅用户通知

  ## 参数
  - `user_id` - 用户ID
  - `pid` - 进程PID

  ## 返回
  - `:ok` - 成功
  """
  def subscribe_user_notifications(user_id, pid \\ self()) do
    Logger.debug("👤 [通知助手] 订阅用户通知: #{user_id}")
    topic = @notification_topics.user <> user_id
    PubSub.subscribe(@pubsub_name, topic)
  end

  @doc """
  取消订阅通知

  ## 参数
  - `topic_type` - 主题类型 (:all, :admin, {:user, user_id})
  - `pid` - 进程PID

  ## 返回
  - `:ok` - 成功
  """
  def unsubscribe_notifications(topic_type, pid \\ self()) do
    topic = case topic_type do
      :all -> @notification_topics.all_users
      :admin -> @notification_topics.admins
      {:user, user_id} -> @notification_topics.user <> user_id
    end
    
    Logger.debug("🔕 [通知助手] 取消订阅通知: #{topic}")
    PubSub.unsubscribe(@pubsub_name, topic)
  end

  # ============================================================================
  # 通知格式化
  # ============================================================================

  @doc """
  格式化通知消息

  ## 参数
  - `notification` - 通知内容
  - `type` - 通知类型

  ## 返回
  - 格式化后的消息
  """
  def format_notification_message(notification, type) do
    base_message = %{
      id: notification.id,
      type: notification.type,
      title: notification.title,
      content: notification.content,
      priority: notification.priority,
      timestamp: notification.inserted_at || DateTime.utc_now()
    }
    
    case type do
      :broadcast ->
        Map.merge(base_message, %{
          broadcast_type: :all_users,
          recipient_type: notification.recipient_type
        })
      
      :admin_broadcast ->
        Map.merge(base_message, %{
          broadcast_type: :admins,
          recipient_type: :admin
        })
      
      :user ->
        Map.merge(base_message, %{
          broadcast_type: :user,
          recipient_id: notification.recipient_id
        })
      
      _ -> base_message
    end
  end

  @doc """
  创建系统通知消息

  ## 参数
  - `title` - 标题
  - `content` - 内容
  - `options` - 选项

  ## 返回
  - 通知消息
  """
  def create_system_notification(title, content, options \\ []) do
    %{
      id: Ecto.UUID.generate(),
      type: Keyword.get(options, :type, :notification),
      title: title,
      content: content,
      priority: Keyword.get(options, :priority, :medium),
      recipient_type: Keyword.get(options, :recipient_type, :all),
      recipient_id: Keyword.get(options, :recipient_id),
      inserted_at: DateTime.utc_now()
    }
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 清理数据用于广播
  defp sanitize_data_for_broadcast(data) when is_map(data) do
    # 移除敏感字段
    sensitive_fields = [:hashed_password, :password, :token, :secret]
    
    Enum.reduce(sensitive_fields, data, fn field, acc ->
      Map.delete(acc, field)
    end)
  end
  defp sanitize_data_for_broadcast(data), do: data

  # ============================================================================
  # 通知模板
  # ============================================================================

  @doc """
  创建用户操作通知

  ## 参数
  - `action` - 操作类型
  - `user_name` - 用户名
  - `details` - 详细信息

  ## 返回
  - 通知消息
  """
  def create_user_action_notification(action, user_name, details \\ "") do
    {title, content} = case action do
      :login ->
        {"用户登录", "用户 #{user_name} 已登录系统"}
      
      :logout ->
        {"用户登出", "用户 #{user_name} 已登出系统"}
      
      :register ->
        {"新用户注册", "新用户 #{user_name} 已注册"}
      
      :bet_placed ->
        {"新下注", "用户 #{user_name} 进行了下注#{details}"}
      
      :stock_traded ->
        {"股票交易", "用户 #{user_name} 进行了股票交易#{details}"}
      
      _ ->
        {"用户操作", "用户 #{user_name} 进行了操作#{details}"}
    end
    
    create_system_notification(title, content, [
      type: :notification,
      priority: :low,
      recipient_type: :admin
    ])
  end

  @doc """
  创建系统警告通知

  ## 参数
  - `warning_type` - 警告类型
  - `message` - 警告消息

  ## 返回
  - 通知消息
  """
  def create_system_warning(warning_type, message) do
    title = case warning_type do
      :high_load -> "系统负载过高"
      :database_error -> "数据库错误"
      :security_alert -> "安全警告"
      _ -> "系统警告"
    end
    
    create_system_notification(title, message, [
      type: :notification,
      priority: :high,
      recipient_type: :admin
    ])
  end
end
