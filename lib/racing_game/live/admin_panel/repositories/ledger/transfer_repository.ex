defmodule RacingGame.Live.AdminPanel.Repositories.Ledger.TransferRepository do
  @moduledoc """
  转账交易数据访问仓储
  
  提供转账交易相关的数据访问抽象：
  - 转账记录管理
  - 交易查询和统计
  - 转账验证和处理
  - 交易历史追踪
  """

  require Logger
  alias Cypridina.Ledger.{Transfer, Account}
  alias RacingGame.Live.AdminPanel.Repositories.QueryBuilders.LedgerQueryBuilder
  require Ash.Query

  # 常量定义
  @cache_ttl 300  # 5分钟缓存
  @default_limit 20
  @max_limit 100

  # ============================================================================
  # 转账基础查询
  # ============================================================================

  @doc """
  根据ID获取转账记录

  ## 参数
  - `transfer_id` - 转账ID
  - `options` - 选项

  ## 返回
  - `{:ok, transfer}` - 成功
  - `{:error, :not_found}` - 转账不存在
  - `{:error, reason}` - 其他错误
  """
  def get_transfer_by_id(transfer_id, options \\ []) do
    Logger.debug("🔍 [转账仓储] 根据ID获取转账: #{transfer_id}")
    
    preload = Keyword.get(options, :preload, [])
    use_cache = Keyword.get(options, :use_cache, true)
    
    cache_key = build_cache_key("transfer", transfer_id, preload)
    
    if use_cache do
      case get_from_cache(cache_key) do
        {:ok, transfer} -> {:ok, transfer}
        :miss -> fetch_and_cache_transfer_by_id(transfer_id, preload, cache_key)
      end
    else
      fetch_transfer_by_id(transfer_id, preload)
    end
  end

  @doc """
  分页查询转账列表

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, %{transfers: transfers, page_info: page_info}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_transfers_paginated(params \\ %{}) do
    Logger.debug("📋 [转账仓储] 分页查询转账列表")
    
    try do
      normalized_params = normalize_list_params(params)
      
      with {:ok, query} <- LedgerQueryBuilder.build_transfer_list_query(normalized_params),
           {:ok, results} <- execute_paginated_query(query, normalized_params) do
        {:ok, %{transfers: results.items, page_info: results.page_info}}
      else
        {:error, reason} -> {:error, reason}
      end
    rescue
      error ->
        Logger.error("❌ [转账仓储] 分页查询异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  获取账户转账历史

  ## 参数
  - `account_id` - 账户ID
  - `options` - 选项

  ## 返回
  - `{:ok, transfers}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_account_transfers(account_id, options \\ []) do
    Logger.debug("📊 [转账仓储] 获取账户转账历史: #{account_id}")
    
    try do
      query = Transfer
      |> Ash.Query.filter(from_account_id == ^account_id or to_account_id == ^account_id)
      |> maybe_preload(options[:preload])
      |> apply_sort(options[:sort] || [inserted_at: :desc])
      |> maybe_limit(options[:limit])
      
      case Ash.read(query) do
        {:ok, transfers} -> {:ok, transfers}
        {:error, error} ->
          Logger.error("❌ [转账仓储] 获取转账历史失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [转账仓储] 获取转账历史异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  获取用户转账历史

  ## 参数
  - `user_id` - 用户ID
  - `currency` - 货币类型
  - `options` - 选项

  ## 返回
  - `{:ok, transfers}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_transfers(user_id, currency \\ :XAA, options \\ []) do
    Logger.debug("👤 [转账仓储] 获取用户转账历史: #{user_id} - #{currency}")
    
    # 首先获取用户账户
    case get_user_account_id(user_id, currency) do
      {:ok, account_id} -> get_account_transfers(account_id, options)
      error -> error
    end
  end

  @doc """
  搜索转账记录

  ## 参数
  - `search_term` - 搜索词
  - `options` - 选项

  ## 返回
  - `{:ok, transfers}` - 成功
  - `{:error, reason}` - 失败
  """
  def search_transfers(search_term, options \\ []) do
    Logger.debug("🔍 [转账仓储] 搜索转账: #{search_term}")
    
    limit = Keyword.get(options, :limit, @default_limit)
    fields = Keyword.get(options, :fields, [:description, :reference])
    
    try do
      query = LedgerQueryBuilder.build_transfer_search_query(search_term, fields)
      
      case query
           |> Ash.Query.limit(limit)
           |> Ash.read() do
        {:ok, transfers} -> {:ok, transfers}
        {:error, error} ->
          Logger.error("❌ [转账仓储] 搜索失败: #{inspect(error)}")
          {:error, :search_failed}
      end
    rescue
      error ->
        Logger.error("❌ [转账仓储] 搜索异常: #{inspect(error)}")
        {:error, :search_error}
    end
  end

  # ============================================================================
  # 转账创建和管理
  # ============================================================================

  @doc """
  创建转账

  ## 参数
  - `transfer_data` - 转账数据

  ## 返回
  - `{:ok, transfer}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_transfer(transfer_data) do
    Logger.info("➕ [转账仓储] 创建转账: #{transfer_data[:amount]} #{transfer_data[:currency]}")
    
    try do
      case Transfer.transfer(transfer_data) do
        {:ok, transfer} ->
          clear_cache(:transfer)
          Logger.info("✅ [转账仓储] 转账创建成功: #{transfer.id}")
          {:ok, transfer}
        {:error, error} ->
          Logger.error("❌ [转账仓储] 创建转账失败: #{inspect(error)}")
          {:error, :create_failed}
      end
    rescue
      error ->
        Logger.error("❌ [转账仓储] 创建转账异常: #{inspect(error)}")
        {:error, :create_error}
    end
  end

  @doc """
  批量创建转账

  ## 参数
  - `transfers_data` - 转账数据列表

  ## 返回
  - `{:ok, transfers}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_transfers_batch(transfers_data) do
    Logger.info("📦 [转账仓储] 批量创建转账: #{length(transfers_data)} 笔")
    
    try do
      results = Enum.map(transfers_data, &create_transfer/1)
      
      case Enum.split_with(results, &match?({:ok, _}, &1)) do
        {successes, []} ->
          transfers = Enum.map(successes, fn {:ok, transfer} -> transfer end)
          Logger.info("✅ [转账仓储] 批量转账全部成功: #{length(transfers)} 笔")
          {:ok, transfers}
        {successes, failures} ->
          Logger.warn("⚠️ [转账仓储] 批量转账部分失败: 成功 #{length(successes)}, 失败 #{length(failures)}")
          {:error, {:partial_failure, successes, failures}}
      end
    rescue
      error ->
        Logger.error("❌ [转账仓储] 批量转账异常: #{inspect(error)}")
        {:error, :batch_error}
    end
  end

  @doc """
  更新转账状态

  ## 参数
  - `transfer_id` - 转账ID
  - `status` - 新状态
  - `options` - 选项

  ## 返回
  - `{:ok, transfer}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_transfer_status(transfer_id, status, options \\ []) do
    Logger.info("✏️ [转账仓储] 更新转账状态: #{transfer_id} -> #{status}")
    
    with {:ok, transfer} <- get_transfer_by_id(transfer_id, use_cache: false),
         {:ok, updated_transfer} <- do_update_transfer_status(transfer, status, options) do
      
      clear_cache(:transfer, transfer_id)
      Logger.info("✅ [转账仓储] 转账状态更新成功: #{transfer_id}")
      {:ok, updated_transfer}
    else
      error -> error
    end
  end

  @doc """
  取消转账

  ## 参数
  - `transfer_id` - 转账ID
  - `reason` - 取消原因

  ## 返回
  - `{:ok, transfer}` - 成功
  - `{:error, reason}` - 失败
  """
  def cancel_transfer(transfer_id, reason \\ "用户取消") do
    Logger.info("❌ [转账仓储] 取消转账: #{transfer_id} - #{reason}")
    
    with {:ok, transfer} <- get_transfer_by_id(transfer_id),
         :ok <- validate_transfer_cancellation(transfer),
         {:ok, cancelled_transfer} <- do_cancel_transfer(transfer, reason) do
      
      clear_cache(:transfer, transfer_id)
      Logger.info("✅ [转账仓储] 转账取消成功: #{transfer_id}")
      {:ok, cancelled_transfer}
    else
      error -> error
    end
  end

  # ============================================================================
  # 转账统计
  # ============================================================================

  @doc """
  获取转账统计信息

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, statistics}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_transfer_statistics(options \\ []) do
    Logger.debug("📊 [转账仓储] 获取转账统计")
    
    try do
      stats = %{
        total_transfers: get_total_transfers(),
        transfers_by_status: get_transfers_by_status(),
        transfers_by_currency: get_transfers_by_currency(),
        total_volume: get_total_volume(),
        recent_transfers: get_recent_transfers_count(options[:days] || 7),
        average_amount: get_average_transfer_amount(),
        failed_transfers: get_failed_transfers_count()
      }
      
      {:ok, stats}
    rescue
      error ->
        Logger.error("❌ [转账仓储] 获取统计异常: #{inspect(error)}")
        {:error, :statistics_error}
    end
  end

  @doc """
  获取账户转账统计

  ## 参数
  - `account_id` - 账户ID
  - `options` - 选项

  ## 返回
  - `{:ok, statistics}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_account_transfer_statistics(account_id, options \\ []) do
    Logger.debug("📊 [转账仓储] 获取账户转账统计: #{account_id}")
    
    try do
      stats = %{
        total_sent: get_account_sent_count(account_id),
        total_received: get_account_received_count(account_id),
        sent_volume: get_account_sent_volume(account_id),
        received_volume: get_account_received_volume(account_id),
        recent_activity: get_account_recent_activity(account_id, options[:days] || 30)
      }
      
      {:ok, stats}
    rescue
      error ->
        Logger.error("❌ [转账仓储] 获取账户统计异常: #{inspect(error)}")
        {:error, :statistics_error}
    end
  end

  # ============================================================================
  # 缓存管理
  # ============================================================================

  @doc """
  清除转账相关缓存

  ## 参数
  - `cache_type` - 缓存类型
  - `id` - ID（可选）
  """
  def clear_cache(cache_type, id \\ nil) do
    cache_patterns = case {cache_type, id} do
      {:transfer, nil} -> ["transfer:*"]
      {:transfer, transfer_id} -> ["transfer:#{transfer_id}:*"]
      {:account_transfers, account_id} -> ["account_transfers:#{account_id}:*"]
      {:all, _} -> ["transfer:*", "account_transfers:*"]
    end
    
    Logger.debug("🧹 [转账仓储] 清除缓存: #{inspect(cache_patterns)}")
    Enum.each(cache_patterns, &clear_cache_pattern/1)
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 标准化列表参数
  defp normalize_list_params(params) do
    %{
      page: max(1, params[:page] || 1),
      limit: min(params[:limit] || @default_limit, @max_limit),
      search: String.trim(params[:search] || ""),
      filters: params[:filters] || %{},
      sort: params[:sort] || [inserted_at: :desc],
      preload: params[:preload] || []
    }
  end

  # 获取并缓存转账
  defp fetch_and_cache_transfer_by_id(transfer_id, preload, cache_key) do
    case fetch_transfer_by_id(transfer_id, preload) do
      {:ok, transfer} ->
        cache_put(cache_key, transfer, @cache_ttl)
        {:ok, transfer}
      error -> error
    end
  end

  # 获取转账
  defp fetch_transfer_by_id(transfer_id, preload) do
    try do
      case Transfer
           |> maybe_preload(preload)
           |> Ash.get(transfer_id) do
        {:ok, transfer} -> {:ok, transfer}
        {:error, %Ash.Error.Query.NotFound{}} -> {:error, :not_found}
        {:error, error} ->
          Logger.error("❌ [转账仓储] 获取转账失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [转账仓储] 获取转账异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # 获取用户账户ID
  defp get_user_account_id(user_id, currency) do
    identifier = "user:#{currency}:#{user_id}"
    
    case Account
         |> Ash.Query.filter(identifier == ^identifier)
         |> Ash.Query.select([:id])
         |> Ash.read_one() do
      {:ok, %{id: account_id}} -> {:ok, account_id}
      {:ok, nil} -> {:error, :account_not_found}
      {:error, error} ->
        Logger.error("❌ [转账仓储] 获取用户账户失败: #{inspect(error)}")
        {:error, :account_query_failed}
    end
  end

  # 执行更新转账状态
  defp do_update_transfer_status(transfer, status, options) do
    try do
      update_data = %{status: status}
      update_data = if options[:reason], do: Map.put(update_data, :status_reason, options[:reason]), else: update_data
      
      case transfer
           |> Ash.Changeset.for_update(:update, update_data)
           |> Ash.update() do
        {:ok, updated_transfer} -> {:ok, updated_transfer}
        {:error, error} ->
          Logger.error("❌ [转账仓储] 更新状态失败: #{inspect(error)}")
          {:error, :update_failed}
      end
    rescue
      error ->
        Logger.error("❌ [转账仓储] 更新状态异常: #{inspect(error)}")
        {:error, :update_error}
    end
  end

  # 验证转账取消
  defp validate_transfer_cancellation(transfer) do
    case transfer.status do
      :pending -> :ok
      :processing -> :ok
      :completed -> {:error, :transfer_completed}
      :cancelled -> {:error, :already_cancelled}
      :failed -> {:error, :transfer_failed}
      _ -> {:error, :invalid_status}
    end
  end

  # 执行取消转账
  defp do_cancel_transfer(transfer, reason) do
    try do
      case transfer
           |> Ash.Changeset.for_update(:cancel, %{cancellation_reason: reason})
           |> Ash.update() do
        {:ok, cancelled_transfer} -> {:ok, cancelled_transfer}
        {:error, error} ->
          Logger.error("❌ [转账仓储] 取消转账失败: #{inspect(error)}")
          {:error, :cancel_failed}
      end
    rescue
      error ->
        Logger.error("❌ [转账仓储] 取消转账异常: #{inspect(error)}")
        {:error, :cancel_error}
    end
  end

  # 执行分页查询
  defp execute_paginated_query(query, params) do
    offset = (params.page - 1) * params.limit
    
    case query
         |> maybe_preload(params.preload)
         |> Ash.Query.page(count: true, limit: params.limit, offset: offset)
         |> Ash.read() do
      {:ok, %Ash.Page.Offset{results: items, count: total_count}} ->
        page_info = %{
          total_count: total_count,
          page: params.page,
          limit: params.limit,
          total_pages: ceil(total_count / params.limit)
        }
        {:ok, %{items: items, page_info: page_info}}
      {:error, error} ->
        Logger.error("❌ [转账仓储] 分页查询失败: #{inspect(error)}")
        {:error, :query_failed}
    end
  end

  # 可能预加载关联数据
  defp maybe_preload(query, []), do: query
  defp maybe_preload(query, preload) when is_list(preload) do
    Ash.Query.load(query, preload)
  end

  # 可能限制数量
  defp maybe_limit(query, nil), do: query
  defp maybe_limit(query, limit) when is_integer(limit) and limit > 0 do
    Ash.Query.limit(query, limit)
  end
  defp maybe_limit(query, _), do: query

  # 应用排序
  defp apply_sort(query, sort) when is_list(sort) do
    Ash.Query.sort(query, sort)
  end
  defp apply_sort(query, _), do: query

  # 统计函数（简化实现）
  defp get_total_transfers do
    case Transfer |> Ash.Query.aggregate(:count, :id) |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_transfers_by_status do
    case Transfer 
         |> Ash.Query.aggregate(:count, :id, group: [:status]) 
         |> Ash.read() do
      {:ok, results} -> 
        Enum.into(results, %{}, fn %{status: status, count: count} -> 
          {status, count} 
        end)
      _ -> %{}
    end
  end

  defp get_transfers_by_currency do
    case Transfer 
         |> Ash.Query.aggregate(:count, :id, group: [:currency]) 
         |> Ash.read() do
      {:ok, results} -> 
        Enum.into(results, %{}, fn %{currency: currency, count: count} -> 
          {currency, count} 
        end)
      _ -> %{}
    end
  end

  defp get_total_volume do
    # 这里需要根据实际的金额聚合逻辑实现
    Money.new(0, :XAA)
  end

  defp get_recent_transfers_count(days) do
    date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
    
    case Transfer 
         |> Ash.Query.filter(inserted_at >= ^date_threshold)
         |> Ash.Query.aggregate(:count, :id) 
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_average_transfer_amount do
    # 这里需要根据实际的金额平均值逻辑实现
    Money.new(0, :XAA)
  end

  defp get_failed_transfers_count do
    case Transfer 
         |> Ash.Query.filter(status == :failed)
         |> Ash.Query.aggregate(:count, :id) 
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  # 账户统计函数
  defp get_account_sent_count(account_id) do
    case Transfer 
         |> Ash.Query.filter(from_account_id == ^account_id)
         |> Ash.Query.aggregate(:count, :id) 
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_account_received_count(account_id) do
    case Transfer 
         |> Ash.Query.filter(to_account_id == ^account_id)
         |> Ash.Query.aggregate(:count, :id) 
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_account_sent_volume(account_id) do
    # 这里需要根据实际的金额聚合逻辑实现
    Money.new(0, :XAA)
  end

  defp get_account_received_volume(account_id) do
    # 这里需要根据实际的金额聚合逻辑实现
    Money.new(0, :XAA)
  end

  defp get_account_recent_activity(account_id, days) do
    date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
    
    case Transfer 
         |> Ash.Query.filter((from_account_id == ^account_id or to_account_id == ^account_id) and inserted_at >= ^date_threshold)
         |> Ash.Query.aggregate(:count, :id) 
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  # 构建缓存键
  defp build_cache_key(prefix, id, extra \\ nil) do
    case extra do
      nil -> "#{prefix}:#{id}"
      extra -> "#{prefix}:#{id}:#{:erlang.phash2(extra)}"
    end
  end

  # 缓存操作（需要实现具体的缓存后端）
  defp get_from_cache(_key), do: :miss
  defp cache_put(_key, _value, _ttl), do: :ok
  defp clear_cache_pattern(_pattern), do: :ok
end
