defmodule CypridinaWeb.Router do
  use CypridinaWeb, :router

  use AshAuthentication.Phoenix.Router

  import AshAuthentication.Plug.Helpers
  import Oban.Web.Router
  import Backpex.Router
  use ErrorTracker.Integrations.Plug
  use ErrorTracker.Web, :router

  # 仅在开发环境导入AshAdmin.Router
  # if Application.compile_env(:cypridina, :dev_routes) do
  import AshAdmin.Router
  # end

  # pipeline :mcp do
  #   plug AshAuthentication.Strategy.ApiKey.Plug,
  #     resource: Cypridina.Accounts.User,
  #     # Use `required?: false` to allow unauthenticated
  #     # users to connect, for example if some tools
  #     # are publicly accessible.
  #     required?: true,
  #     on_error: &__MODULE__.api_key_error/2
  # end

  # def api_key_error(conn, _opts) do
  #   conn
  #   |> Plug.Conn.put_status(401)
  #   |> Phoenix.Controller.json(%{error: "Unauthorized"})
  #   |> Plug.Conn.halt()
  # end

  pipeline :browser do
    plug(:accepts, ["html"])
    plug(:fetch_session)
    plug(:fetch_live_flash)
    plug(:put_root_layout, html: {CypridinaWeb.Layouts, :root})
    plug(:protect_from_forgery)
    plug(:put_secure_browser_headers)
    plug(:load_from_session)
    plug(CypridinaWeb.Plugs.ReturnToPlug)
  end

  pipeline :api do
    plug(:accepts, ["json"])
    plug(:load_from_bearer)
    plug(:set_actor, :user)
    # plug AshAuthentication.Strategy.ApiKey.Plug, resource: Cypridina.Accounts.User
  end

  scope "/" do
    pipe_through(:browser)
    get("/", CypridinaWeb.PageController, :redirect_to_racing_game)

    # 用户应用路由
    scope "/" do
      # 需要用户登录的路由
      ash_authentication_live_session :user_required,
        on_mount: {CypridinaWeb.LiveUserAuth, :user_required} do
        live("/app/racing_game", CypridinaWeb.RacingLive.Index, :index)
        # 管理面板路由 - 支持URL导航
        live("/admin_panel", RacingGame.Live.AdminPanelLive, :index)
        live("/admin_panel/profile", RacingGame.Live.AdminPanelLive, :profile)
        live("/admin_panel/users", RacingGame.Live.AdminPanelLive, :users)
        live("/admin_panel/subordinates", RacingGame.Live.AdminPanelLive, :subordinates)
        live("/admin_panel/stocks", RacingGame.Live.AdminPanelLive, :stocks)
        live("/admin_panel/bet_records", RacingGame.Live.AdminPanelLive, :bet_records)
        live("/admin_panel/system_communications", RacingGame.Live.AdminPanelLive, :system_communications)
        live("/admin_panel/system_settings", RacingGame.Live.AdminPanelLive, :system_settings)
        live("/admin_panel/system_logs", RacingGame.Live.AdminPanelLive, :system_logs)
        live("/admin_panel/system_monitoring", RacingGame.Live.AdminPanelLive, :system_monitoring)
        live("/admin_panel/system_statistics", RacingGame.Live.AdminPanelLive, :system_statistics)
        live("/admin_panel/system_upgrade", RacingGame.Live.AdminPanelLive, :system_upgrade)
        live("/admin/games", CypridinaWeb.Admin.GameManagementLive, :index)
        # 对话框演示页面
        live("/dialog_demo", RacingGame.Live.DialogDemoLive, :index)
      end

      ash_authentication_live_session :race_control,
        on_mount: [{CypridinaWeb.LiveUserAuth, :admin_required}] do
        live("/race_control", RacingGame.Live.RaceControlLive, :index)
      end

      # 可选用户登录的路由
      ash_authentication_live_session :user_optional,
        on_mount: {CypridinaWeb.LiveUserAuth, :user_optional} do
        # 可选登录的页面
      end
    end
  end

  # ash管理后台 - 需要管理员权限
  scope "/" do
    pipe_through([:browser, CypridinaWeb.Plugs.AdminAuth])

    ash_admin("/admin")
  end

  scope "/" do
    pipe_through(:api)

    scope "/racing_game", CypridinaWeb do
      get("/getsettle", RacingGameController, :getsettle)
      get("/getinfo", RacingGameController, :getinfo)
      get("/getlatestdata", RacingGameController, :getlatestdata)
      post("/rank", RacingGameController, :rank)
    end

    # Teen
    scope "/serverlist_v2", CypridinaWeb do
      # 基本信息端点
      get("/", TeenApiController, :default)
    end
  end

  # 认证相关
  scope "/", CypridinaWeb do
    pipe_through(:browser)

    auth_routes(AuthController, Cypridina.Accounts.User, path: "/auth")
    sign_out_route(AuthController)

    # 登录相关路由
    sign_in_route(
      register_path: "/register",
      reset_path: "/reset",
      auth_routes_prefix: "/auth",
      on_mount: [{CypridinaWeb.LiveUserAuth, :live_no_user}],
      overrides: [
        CypridinaWeb.AuthOverrides,
        AshAuthentication.Phoenix.Overrides.Default
      ]
    )

    # 重置密码路由
    reset_route(
      auth_routes_prefix: "/auth",
      overrides: [
        CypridinaWeb.AuthOverrides,
        AshAuthentication.Phoenix.Overrides.Default
      ]
    )

    # 用户确认路由
    confirm_route(Cypridina.Accounts.User, :confirm_new_user,
      auth_routes_prefix: "/auth",
      overrides: [CypridinaWeb.AuthOverrides, AshAuthentication.Phoenix.Overrides.Default]
    )
  end

  # 统一的管理后台 - 管理员和代理都可以访问，通过权限控制显示内容
  # scope "/", CypridinaWeb do
  #   pipe_through([:browser])
  #   # Backpex 管理界面
  #   backpex_routes()
  #   get("/", PageController, :redirect_to_admin_users)
  #   # 统一的管理后台LiveView
  #   ash_authentication_live_session :admin_backend,
  #     on_mount: [{CypridinaWeb.LiveUserAuth, :admin_required}, Backpex.InitAssigns] do
  #     # 用户管理 - 根据权限显示不同内容
  #     live_resources("/users", Live.UserLive)
  #     live_resources("/user_assets", Live.UserAssetLive)
  #     # 代理关系管理 - 管理员看全部，代理只看自己的下级
  #     live_resources("/agent_relationships", Live.AgentLive)
  #     # 佣金记录 - 管理员看全部，代理只看自己相关的
  #     live_resources("/commission_records", Live.CommissionRecordLive)
  #   end
  # end

  # Enable LiveDashboard and Swoosh mailbox preview in development
  if Application.compile_env(:cypridina, :dev_routes) do
    # If you want to use the LiveDashboard in production, you should put
    # it behind authentication and allow only admins to access it.
    # If your application does not have an admins-only section yet,
    # you can use Plug.BasicAuth to set up some basic authentication
    # as long as you are also using SSL (which you should anyway).
    import Phoenix.LiveDashboard.Router

    scope "/dev" do
      pipe_through(:browser)

      oban_dashboard("/oban")
      error_tracker_dashboard("/errors")
      live_dashboard("/dashboard", metrics: CypridinaWeb.Telemetry)
      forward("/mailbox", Plug.Swoosh.MailboxPreview)
    end
  end
end
