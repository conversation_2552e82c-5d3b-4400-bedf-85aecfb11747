defmodule Teen.PaymentSystem.PaymentGateway do
  @moduledoc """
  支付网关资源
  
  管理各种支付网关的配置和状态
  """
  
  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.PaymentSystem,
    extensions: [AshAdmin.Resource]

  postgres do
    table "payment_gateways"
    repo Cypridina.Repo
  end

  admin do
    table_columns [:id, :name, :gateway_type, :status, :inserted_at]
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_gateways
    define :list_by_type
  end

  actions do
    defaults [:create, :read, :update, :destroy]

    read :list_active_gateways do
      filter expr(status == 1)
    end

    read :list_by_type do
      argument :gateway_type, :string, allow_nil?: false
      filter expr(gateway_type == ^arg(:gateway_type))
    end

    update :enable do
      change set_attribute(:status, 1)
    end

    update :disable do
      change set_attribute(:status, 0)
    end

    update :test_connection do
      # 这里可以添加测试连接的逻辑
      change set_attribute(:last_test_at, &DateTime.utc_now/0)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :name, :string do
      allow_nil? false
      public? true
      description "网关名称"
      constraints max_length: 100
    end

    attribute :gateway_type, :string do
      allow_nil? false
      public? true
      description "网关类型"
      constraints max_length: 50
    end

    attribute :api_url, :string do
      allow_nil? false
      public? true
      description "API地址"
      constraints max_length: 500
    end

    attribute :merchant_id, :string do
      allow_nil? false
      public? true
      description "商户ID"
      constraints max_length: 100
    end

    attribute :api_key, :string do
      allow_nil? false
      public? true
      description "API密钥"
      constraints max_length: 500
    end

    attribute :secret_key, :string do
      allow_nil? true
      public? true
      description "密钥"
      constraints max_length: 500
    end

    attribute :status, :integer do
      allow_nil? false
      public? true
      description "状态：0-禁用，1-启用"
      default 1
      constraints min: 0, max: 1
    end

    attribute :sort_order, :integer do
      allow_nil? false
      public? true
      description "排序"
      default 0
    end

    attribute :config_data, :map do
      allow_nil? true
      public? true
      description "网关配置数据（JSON）"
    end

    attribute :last_test_at, :utc_datetime do
      allow_nil? true
      public? true
      description "最后测试时间"
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "描述"
      constraints max_length: 500
    end

    timestamps()
  end

  relationships do
    has_many :payment_configs, Teen.PaymentSystem.PaymentConfig do
      public? true
      source_attribute :id
      destination_attribute :gateway_id
    end
  end

  identities do
    identity :unique_gateway_name, [:name]
    identity :unique_merchant_id, [:merchant_id]
  end
end
