Compiling 76 files (.ex)
     warning: variable "future_time" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 136 │       future_time = DateTime.add(now, minutes * 60, :second)
     │       ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/racing_game/race_repository.ex:136:7: RacingGame.Live.AdminPanel.Repositories.RacingGame.RaceRepository.get_upcoming_races/2

     warning: variable "read_communication_ids" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 200 │       read_communication_ids = case get_user_read_communication_ids(user_id) do
     │       ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/racing_game/system_communication_repository.ex:200:7: RacingGame.Live.AdminPanel.Repositories.RacingGame.SystemCommunicationRepository.get_user_unread_communications/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 145 │   def get_user_agent(user_id, options \\ []) do
     │                               ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/agent_relationship_repository.ex:145:31: RacingGame.Live.AdminPanel.Repositories.Accounts.AgentRelationshipRepository.get_user_agent/2

     warning: variable "cache_patterns" is unused (there is a variable with the same name in the context, use the pin operator (^) to match on it or prefix this variable with underscore if it is not meant to be used)
     │
 264 │       cache_patterns = ["bet:#{bet_id}:*" | cache_patterns]
     │       ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/bet_repository.ex:264:7: RacingGame.Live.AdminPanel.Repositories.BetRepository.clear_cache/2

     warning: variable "cache_patterns" is unused (there is a variable with the same name in the context, use the pin operator (^) to match on it or prefix this variable with underscore if it is not meant to be used)
     │
 268 │       cache_patterns = ["user_bets:#{user_id}:*" | cache_patterns]
     │       ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/bet_repository.ex:268:7: RacingGame.Live.AdminPanel.Repositories.BetRepository.clear_cache/2

     warning: variable "cache_patterns" is unused (there is a variable with the same name in the context, use the pin operator (^) to match on it or prefix this variable with underscore if it is not meant to be used)
     │
 272 │       cache_patterns = ["bet:*", "user_bets:*"]
     │       ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/bet_repository.ex:272:7: RacingGame.Live.AdminPanel.Repositories.BetRepository.clear_cache/2

     warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
     │
 234 │           Logger.warn("⚠️ [转账仓储] 批量转账部分失败: 成功 #{length(successes)}, 失败 #{length(failures)}")
     │                  ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/ledger/transfer_repository.ex:234:18: RacingGame.Live.AdminPanel.Repositories.Ledger.TransferRepository.create_transfers_batch/1

     warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
     │
 245 │           Logger.warn("⚠️ [投注仓储] 批量投注部分失败: 成功 #{length(successes)}, 失败 #{length(failures)}")
     │                  ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/racing_game/bet_repository.ex:245:18: RacingGame.Live.AdminPanel.Repositories.RacingGame.BetRepository.create_bets_batch/1

     warning: variable "cache_patterns" is unused (there is a variable with the same name in the context, use the pin operator (^) to match on it or prefix this variable with underscore if it is not meant to be used)
     │
 297 │       cache_patterns = ["stock:#{stock_id}:*" | cache_patterns]
     │       ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/stock_repository.ex:297:7: RacingGame.Live.AdminPanel.Repositories.StockRepository.clear_cache/3

     warning: variable "token_value" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 177 │   def get_token_by_value(token_value, options \\ []) do
     │                          ~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/user_identity_repository.ex:177:26: RacingGame.Live.AdminPanel.Repositories.Accounts.UserIdentityRepository.get_token_by_value/2

     warning: variable "cache_patterns" is unused (there is a variable with the same name in the context, use the pin operator (^) to match on it or prefix this variable with underscore if it is not meant to be used)
     │
 301 │       cache_patterns = ["user_stocks:#{user_id}:*" | cache_patterns]
     │       ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/stock_repository.ex:301:7: RacingGame.Live.AdminPanel.Repositories.StockRepository.clear_cache/3

     warning: variable "cache_patterns" is unused (there is a variable with the same name in the context, use the pin operator (^) to match on it or prefix this variable with underscore if it is not meant to be used)
     │
 305 │       cache_patterns = ["racer_holdings:#{racer_id}:*" | cache_patterns]
     │       ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/stock_repository.ex:305:7: RacingGame.Live.AdminPanel.Repositories.StockRepository.clear_cache/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 275 │   def get_account_balance(account_id, options \\ []) do
     │                                       ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/ledger/account_repository.ex:275:39: RacingGame.Live.AdminPanel.Repositories.Ledger.AccountRepository.get_account_balance/2

     warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
     │
 271 │           Logger.warn("⚠️ [余额仓储] 批量创建部分失败: 成功 #{length(successes)}, 失败 #{length(failures)}")
     │                  ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/ledger/balance_repository.ex:271:18: RacingGame.Live.AdminPanel.Repositories.Ledger.BalanceRepository.create_balances_batch/1

     warning: variable "cache_patterns" is unused (there is a variable with the same name in the context, use the pin operator (^) to match on it or prefix this variable with underscore if it is not meant to be used)
     │
 309 │       cache_patterns = ["stock:*", "user_stocks:*", "racer_holdings:*"]
     │       ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/stock_repository.ex:309:7: RacingGame.Live.AdminPanel.Repositories.StockRepository.clear_cache/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 304 │   def get_user_balance(user_id, currency \\ :XAA, options \\ []) do
     │                                                   ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/ledger/account_repository.ex:304:51: RacingGame.Live.AdminPanel.Repositories.Ledger.AccountRepository.get_user_balance/3

     warning: variable "agent_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 429 │   defp check_existing_relationship(user_id, agent_id) do
     │                                             ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/agent_relationship_repository.ex:429:45: RacingGame.Live.AdminPanel.Repositories.Accounts.AgentRelationshipRepository.check_existing_relationship/2

     warning: variable "search_term" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 523 │     search_term = "%#{search}%"
     │     ~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/customer_chat_repository.ex:523:5: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.CustomerChatRepository.apply_chat_search/2

     warning: variable "user_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 429 │   defp check_existing_relationship(user_id, agent_id) do
     │                                    ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/agent_relationship_repository.ex:429:36: RacingGame.Live.AdminPanel.Repositories.Accounts.AgentRelationshipRepository.check_existing_relationship/2

     warning: variable "identifier" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 433 │     identifier = "user:#{currency}:#{user_id}"
     │     ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/ledger/transfer_repository.ex:433:5: RacingGame.Live.AdminPanel.Repositories.Ledger.TransferRepository.get_user_account_id/2

     warning: variable "identifier" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 467 │     identifier = "user:#{currency}:#{user_id}"
     │     ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/ledger/balance_repository.ex:467:5: RacingGame.Live.AdminPanel.Repositories.Ledger.BalanceRepository.get_user_account_id/2

     warning: variable "user_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 530 │   defp get_user_read_communication_ids(user_id) do
     │                                        ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/racing_game/system_communication_repository.ex:530:40: RacingGame.Live.AdminPanel.Repositories.RacingGame.SystemCommunicationRepository.get_user_read_communication_ids/1

     warning: variable "resource" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 479 │   defp execute_paginated_query(query, params, resource) do
     │                                               ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/user_repository.ex:479:47: RacingGame.Live.AdminPanel.Repositories.Accounts.UserRepository.execute_paginated_query/3

     warning: variable "type" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 338 │   defp apply_type_filter(query, type) do
     │                                 ~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/communication_repository.ex:338:33: RacingGame.Live.AdminPanel.Repositories.CommunicationRepository.apply_type_filter/2

     warning: variable "level" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 583 │   defp maybe_filter_by_level(query, level) do
     │                                     ~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/agent_relationship_repository.ex:583:37: RacingGame.Live.AdminPanel.Repositories.Accounts.AgentRelationshipRepository.maybe_filter_by_level/2

     warning: variable "end_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 564 │   defp maybe_date_range(query, {start_date, end_date}) do
     │                                             ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/ledger/balance_repository.ex:564:45: RacingGame.Live.AdminPanel.Repositories.Ledger.BalanceRepository.maybe_date_range/2

     warning: variable "search_term" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 570 │     search_term = "%#{search}%"
     │     ~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/racing_game/system_communication_repository.ex:570:5: RacingGame.Live.AdminPanel.Repositories.RacingGame.SystemCommunicationRepository.apply_communication_search/2

     warning: variable "race_issue" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 363 │   defp apply_race_issue_filter(query, race_issue) do
     │                                       ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/bet_repository.ex:363:39: RacingGame.Live.AdminPanel.Repositories.BetRepository.apply_race_issue_filter/2

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 569 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/user_repository.ex:569:5: RacingGame.Live.AdminPanel.Repositories.Accounts.UserRepository.get_recent_registrations_count/1

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 393 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/communication_repository.ex:393:5: RacingGame.Live.AdminPanel.Repositories.CommunicationRepository.get_recent_count/1

     warning: variable "start_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 564 │   defp maybe_date_range(query, {start_date, end_date}) do
     │                                 ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/ledger/balance_repository.ex:564:33: RacingGame.Live.AdminPanel.Repositories.Ledger.BalanceRepository.maybe_date_range/2

     warning: variable "end_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 621 │   defp maybe_date_range(query, {start_date, end_date}) do
     │                                             ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/racing_game/bet_repository.ex:621:45: RacingGame.Live.AdminPanel.Repositories.RacingGame.BetRepository.maybe_date_range/2

     warning: variable "racer_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 388 │   defp apply_racer_filter(query, racer_id) do
     │                                  ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/stock_repository.ex:388:34: RacingGame.Live.AdminPanel.Repositories.StockRepository.apply_racer_filter/2

     warning: variable "status" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 589 │   defp maybe_filter_by_status(query, status) do
     │                                      ~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/agent_relationship_repository.ex:589:38: RacingGame.Live.AdminPanel.Repositories.Accounts.AgentRelationshipRepository.maybe_filter_by_status/2

     warning: variable "purpose" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 478 │   defp maybe_filter_token_purpose(query, purpose) do
     │                                          ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/user_identity_repository.ex:478:42: RacingGame.Live.AdminPanel.Repositories.Accounts.UserIdentityRepository.maybe_filter_token_purpose/2

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 577 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/ledger/transfer_repository.ex:577:5: RacingGame.Live.AdminPanel.Repositories.Ledger.TransferRepository.get_recent_transfers_count/1

     warning: variable "start_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 621 │   defp maybe_date_range(query, {start_date, end_date}) do
     │                                 ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/racing_game/bet_repository.ex:621:33: RacingGame.Live.AdminPanel.Repositories.RacingGame.BetRepository.maybe_date_range/2

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 578 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/customer_chat_repository.ex:578:5: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.CustomerChatRepository.get_recent_chats_count/1

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 604 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/ledger/balance_repository.ex:604:5: RacingGame.Live.AdminPanel.Repositories.Ledger.BalanceRepository.get_recent_balance_changes/1

     warning: variable "agent_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 594 │   defp get_agent_sub_users_count(agent_id) do
     │                                  ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/agent_relationship_repository.ex:594:34: RacingGame.Live.AdminPanel.Repositories.Accounts.AgentRelationshipRepository.get_agent_sub_users_count/1

     warning: variable "now" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 484 │     now = DateTime.utc_now()
     │     ~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/user_identity_repository.ex:484:5: RacingGame.Live.AdminPanel.Repositories.Accounts.UserIdentityRepository.maybe_filter_active_tokens/2

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 615 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/racing_game/system_communication_repository.ex:615:5: RacingGame.Live.AdminPanel.Repositories.RacingGame.SystemCommunicationRepository.get_recent_communications_count/1

     warning: variable "account_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 604 │   defp get_account_sent_count(account_id) do
     │                               ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/ledger/transfer_repository.ex:604:31: RacingGame.Live.AdminPanel.Repositories.Ledger.TransferRepository.get_account_sent_count/1

     warning: variable "balances" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 616 │   defp calculate_balance_trend(balances, interval) do
     │                                ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/ledger/balance_repository.ex:616:32: RacingGame.Live.AdminPanel.Repositories.Ledger.BalanceRepository.calculate_balance_trend/2

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 670 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/racing_game/bet_repository.ex:670:5: RacingGame.Live.AdminPanel.Repositories.RacingGame.BetRepository.get_recent_bets_count/1

     warning: variable "user_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 633 │   defp get_user_unread_count(user_id) do
     │                              ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/racing_game/system_communication_repository.ex:633:30: RacingGame.Live.AdminPanel.Repositories.RacingGame.SystemCommunicationRepository.get_user_unread_count/1

     warning: variable "customer_service_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 599 │   defp get_customer_service_handled_count(customer_service_id) do
     │                                           ~~~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/customer_chat_repository.ex:599:43: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.CustomerChatRepository.get_customer_service_handled_count/1

     warning: variable "interval" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 616 │   defp calculate_balance_trend(balances, interval) do
     │                                          ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/ledger/balance_repository.ex:616:42: RacingGame.Live.AdminPanel.Repositories.Ledger.BalanceRepository.calculate_balance_trend/2

     warning: variable "now" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 529 │     now = DateTime.utc_now()
     │     ~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/user_identity_repository.ex:529:5: RacingGame.Live.AdminPanel.Repositories.Accounts.UserIdentityRepository.get_active_tokens_count/0

     warning: variable "agent_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 604 │   defp get_active_sub_users_count(agent_id) do
     │                                   ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/agent_relationship_repository.ex:604:35: RacingGame.Live.AdminPanel.Repositories.Accounts.AgentRelationshipRepository.get_active_sub_users_count/1

     warning: variable "end_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 619 │   defp maybe_date_range(query, {start_date, end_date}) do
     │                                             ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/racing_game/stock_repository.ex:619:45: RacingGame.Live.AdminPanel.Repositories.RacingGame.StockRepository.maybe_date_range/2

     warning: variable "account_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 614 │   defp get_account_received_count(account_id) do
     │                                   ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/ledger/transfer_repository.ex:614:35: RacingGame.Live.AdminPanel.Repositories.Ledger.TransferRepository.get_account_received_count/1

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 589 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/ledger/account_repository.ex:589:5: RacingGame.Live.AdminPanel.Repositories.Ledger.AccountRepository.get_recent_accounts_count/1

     warning: variable "user_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 639 │   defp get_user_read_count(user_id) do
     │                            ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/racing_game/system_communication_repository.ex:639:28: RacingGame.Live.AdminPanel.Repositories.RacingGame.SystemCommunicationRepository.get_user_read_count/1

     warning: variable "now" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 540 │     now = DateTime.utc_now()
     │     ~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/user_identity_repository.ex:540:5: RacingGame.Live.AdminPanel.Repositories.Accounts.UserIdentityRepository.get_expired_tokens_count/0

     warning: variable "customer_service_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 609 │   defp get_customer_service_recent_handled_count(customer_service_id, days) do
     │                                                  ~~~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/customer_chat_repository.ex:609:50: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.CustomerChatRepository.get_customer_service_recent_handled_count/2

     warning: variable "start_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 619 │   defp maybe_date_range(query, {start_date, end_date}) do
     │                                 ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/racing_game/stock_repository.ex:619:33: RacingGame.Live.AdminPanel.Repositories.RacingGame.StockRepository.maybe_date_range/2

     warning: variable "account_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 624 │   defp get_account_sent_volume(account_id) do
     │                                ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/ledger/transfer_repository.ex:624:32: RacingGame.Live.AdminPanel.Repositories.Ledger.TransferRepository.get_account_sent_volume/1

     warning: variable "agent_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 615 │   defp get_direct_sub_users_count(agent_id) do
     │                                   ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/agent_relationship_repository.ex:615:35: RacingGame.Live.AdminPanel.Repositories.Accounts.AgentRelationshipRepository.get_direct_sub_users_count/1

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 551 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/user_identity_repository.ex:551:5: RacingGame.Live.AdminPanel.Repositories.Accounts.UserIdentityRepository.get_recent_logins_count/1

     warning: variable "user_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 692 │   defp get_user_total_bets(user_id) do
     │                            ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/racing_game/bet_repository.ex:692:28: RacingGame.Live.AdminPanel.Repositories.RacingGame.BetRepository.get_user_total_bets/1

     warning: variable "account_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 629 │   defp get_account_received_volume(account_id) do
     │                                    ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/ledger/transfer_repository.ex:629:36: RacingGame.Live.AdminPanel.Repositories.Ledger.TransferRepository.get_account_received_volume/1

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 610 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/customer_chat_repository.ex:610:5: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.CustomerChatRepository.get_customer_service_recent_handled_count/2

     warning: variable "user_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 702 │   defp get_user_total_bet_amount(user_id) do
     │                                  ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/racing_game/bet_repository.ex:702:34: RacingGame.Live.AdminPanel.Repositories.RacingGame.BetRepository.get_user_total_bet_amount/1

     warning: variable "agent_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 625 │   defp get_indirect_sub_users_count(agent_id) do
     │                                     ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/agent_relationship_repository.ex:625:37: RacingGame.Live.AdminPanel.Repositories.Accounts.AgentRelationshipRepository.get_indirect_sub_users_count/1

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 650 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/racing_game/system_communication_repository.ex:650:5: RacingGame.Live.AdminPanel.Repositories.RacingGame.SystemCommunicationRepository.get_user_recent_reads_count/2

     warning: variable "user_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 707 │   defp get_user_total_winnings(user_id) do
     │                                ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/racing_game/bet_repository.ex:707:32: RacingGame.Live.AdminPanel.Repositories.RacingGame.BetRepository.get_user_total_winnings/1

     warning: variable "user_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 649 │   defp get_user_recent_reads_count(user_id, days) do
     │                                    ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/racing_game/system_communication_repository.ex:649:36: RacingGame.Live.AdminPanel.Repositories.RacingGame.SystemCommunicationRepository.get_user_recent_reads_count/2

     warning: variable "status" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 652 │   defp maybe_apply_status_filter(query, status) do
     │                                         ~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/customer_chat_repository.ex:652:41: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.CustomerChatRepository.maybe_apply_status_filter/2

     warning: variable "user_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 712 │   defp get_user_win_rate(user_id) do
     │                          ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/racing_game/bet_repository.ex:712:26: RacingGame.Live.AdminPanel.Repositories.RacingGame.BetRepository.get_user_win_rate/1

     warning: variable "transaction_type" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 627 │   defp maybe_filter_by_type(query, transaction_type) do
     │                                    ~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/racing_game/stock_repository.ex:627:36: RacingGame.Live.AdminPanel.Repositories.RacingGame.StockRepository.maybe_filter_by_type/2

     warning: variable "agent_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 651 │   defp get_recent_additions_count(agent_id, days) do
     │                                   ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/agent_relationship_repository.ex:651:35: RacingGame.Live.AdminPanel.Repositories.Accounts.AgentRelationshipRepository.get_recent_additions_count/2

     warning: variable "platform" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 658 │   defp maybe_apply_platform_filter(query, platform) do
     │                                           ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/customer_chat_repository.ex:658:43: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.CustomerChatRepository.maybe_apply_platform_filter/2

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 718 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/racing_game/bet_repository.ex:718:5: RacingGame.Live.AdminPanel.Repositories.RacingGame.BetRepository.get_user_recent_activity/2

     warning: variable "search_term" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 600 │     search_term = "%#{search}%"
     │     ~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/exchange_order_repository.ex:600:5: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.ExchangeOrderRepository.apply_order_search/2

     warning: variable "user_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 717 │   defp get_user_recent_activity(user_id, days) do
     │                                 ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/racing_game/bet_repository.ex:717:33: RacingGame.Live.AdminPanel.Repositories.RacingGame.BetRepository.get_user_recent_activity/2

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 638 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/racing_game/race_repository.ex:638:5: RacingGame.Live.AdminPanel.Repositories.RacingGame.RaceRepository.get_recent_races_count/1

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 652 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/agent_relationship_repository.ex:652:5: RacingGame.Live.AdminPanel.Repositories.Accounts.AgentRelationshipRepository.get_recent_additions_count/2

     warning: variable "end_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 664 │   defp maybe_apply_date_range(query, {start_date, end_date}) do
     │                                                   ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/customer_chat_repository.ex:664:51: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.CustomerChatRepository.maybe_apply_date_range/2

     warning: variable "account_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 634 │   defp get_account_recent_activity(account_id, days) do
     │                                    ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/ledger/transfer_repository.ex:634:36: RacingGame.Live.AdminPanel.Repositories.Ledger.TransferRepository.get_account_recent_activity/2

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 635 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/ledger/transfer_repository.ex:635:5: RacingGame.Live.AdminPanel.Repositories.Ledger.TransferRepository.get_account_recent_activity/2

     warning: variable "start_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 664 │   defp maybe_apply_date_range(query, {start_date, end_date}) do
     │                                       ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/customer_chat_repository.ex:664:39: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.CustomerChatRepository.maybe_apply_date_range/2

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 676 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/racing_game/stock_repository.ex:676:5: RacingGame.Live.AdminPanel.Repositories.RacingGame.StockRepository.get_recent_transactions_count/1

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 704 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/agent_relationship_repository.ex:704:5: RacingGame.Live.AdminPanel.Repositories.Accounts.AgentRelationshipRepository.get_recent_relationships_count/1

     warning: variable "user_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 688 │   defp get_user_total_holdings(user_id) do
     │                                ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/racing_game/stock_repository.ex:688:32: RacingGame.Live.AdminPanel.Repositories.RacingGame.StockRepository.get_user_total_holdings/1

     warning: variable "user_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 698 │   defp get_user_total_market_value(user_id) do
     │                                    ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/racing_game/stock_repository.ex:698:36: RacingGame.Live.AdminPanel.Repositories.RacingGame.StockRepository.get_user_total_market_value/1

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 685 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/exchange_order_repository.ex:685:5: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.ExchangeOrderRepository.get_recent_orders_count/1

     warning: variable "user_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 703 │   defp get_user_total_transactions(user_id) do
     │                                    ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/racing_game/stock_repository.ex:703:36: RacingGame.Live.AdminPanel.Repositories.RacingGame.StockRepository.get_user_total_transactions/1

     warning: variable "audit_status" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 727 │   defp maybe_apply_audit_status_filter(query, audit_status) do
     │                                               ~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/exchange_order_repository.ex:727:47: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.ExchangeOrderRepository.maybe_apply_audit_status_filter/2

     warning: variable "user_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 713 │   defp get_user_portfolio_diversity(user_id) do
     │                                     ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/racing_game/stock_repository.ex:713:37: RacingGame.Live.AdminPanel.Repositories.RacingGame.StockRepository.get_user_portfolio_diversity/1

     warning: variable "progress_status" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 733 │   defp maybe_apply_progress_status_filter(query, progress_status) do
     │                                                  ~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/exchange_order_repository.ex:733:50: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.ExchangeOrderRepository.maybe_apply_progress_status_filter/2

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 724 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/racing_game/stock_repository.ex:724:5: RacingGame.Live.AdminPanel.Repositories.RacingGame.StockRepository.get_user_recent_activity/2

     warning: variable "user_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 723 │   defp get_user_recent_activity(user_id, days) do
     │                                 ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/racing_game/stock_repository.ex:723:33: RacingGame.Live.AdminPanel.Repositories.RacingGame.StockRepository.get_user_recent_activity/2

     warning: variable "exchange_type" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 739 │   defp maybe_apply_exchange_type_filter(query, exchange_type) do
     │                                                ~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/exchange_order_repository.ex:739:48: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.ExchangeOrderRepository.maybe_apply_exchange_type_filter/2

     warning: variable "end_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 745 │   defp maybe_apply_date_range(query, {start_date, end_date}) do
     │                                                   ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/exchange_order_repository.ex:745:51: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.ExchangeOrderRepository.maybe_apply_date_range/2

     warning: variable "start_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 745 │   defp maybe_apply_date_range(query, {start_date, end_date}) do
     │                                       ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/exchange_order_repository.ex:745:39: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.ExchangeOrderRepository.maybe_apply_date_range/2

     warning: default values for the optional arguments in build_cache_key/3 are never used
     │
 405 │   defp build_cache_key(prefix, id, extra \\ nil) do
     │        ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/communication_repository.ex:405:8: RacingGame.Live.AdminPanel.Repositories.CommunicationRepository (module)

     warning: default values for the optional arguments in build_cache_key/3 are never used
     │
 601 │   defp build_cache_key(prefix, id, extra \\ nil) do
     │        ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/ledger/account_repository.ex:601:8: RacingGame.Live.AdminPanel.Repositories.Ledger.AccountRepository (module)

     warning: default values for the optional arguments in build_cache_key/3 are never used
     │
 669 │   defp build_cache_key(prefix, id, extra \\ nil) do
     │        ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/customer_chat_repository.ex:669:8: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.CustomerChatRepository (module)

     warning: default values for the optional arguments in build_cache_key/3 are never used
     │
 581 │   defp build_cache_key(prefix, id, extra \\ nil) do
     │        ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/user_repository.ex:581:8: RacingGame.Live.AdminPanel.Repositories.Accounts.UserRepository (module)

     warning: default values for the optional arguments in build_cache_key/3 are never used
     │
 681 │   defp build_cache_key(prefix, id, extra \\ nil) do
     │        ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/racing_game/system_communication_repository.ex:681:8: RacingGame.Live.AdminPanel.Repositories.RacingGame.SystemCommunicationRepository (module)

     warning: default values for the optional arguments in build_cache_key/3 are never used
     │
 647 │   defp build_cache_key(prefix, id, extra \\ nil) do
     │        ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/ledger/transfer_repository.ex:647:8: RacingGame.Live.AdminPanel.Repositories.Ledger.TransferRepository (module)

     warning: default values for the optional arguments in build_cache_key/3 are never used
     │
 736 │   defp build_cache_key(prefix, id, extra \\ nil) do
     │        ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/racing_game/stock_repository.ex:736:8: RacingGame.Live.AdminPanel.Repositories.RacingGame.StockRepository (module)

     warning: default values for the optional arguments in build_cache_key/3 are never used
     │
 623 │   defp build_cache_key(prefix, id, extra \\ nil) do
     │        ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/ledger/balance_repository.ex:623:8: RacingGame.Live.AdminPanel.Repositories.Ledger.BalanceRepository (module)

     warning: default values for the optional arguments in build_cache_key/3 are never used
     │
 470 │   defp build_cache_key(prefix, id, extra \\ nil) do
     │        ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/bet_repository.ex:470:8: RacingGame.Live.AdminPanel.Repositories.BetRepository (module)

     warning: default values for the optional arguments in build_cache_key/3 are never used
     │
 730 │   defp build_cache_key(prefix, id, extra \\ nil) do
     │        ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/racing_game/bet_repository.ex:730:8: RacingGame.Live.AdminPanel.Repositories.RacingGame.BetRepository (module)

     warning: default values for the optional arguments in build_cache_key/3 are never used
     │
 481 │   defp build_cache_key(prefix, id, extra \\ nil) do
     │        ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/stock_repository.ex:481:8: RacingGame.Live.AdminPanel.Repositories.StockRepository (module)

     warning: default values for the optional arguments in build_cache_key/3 are never used
     │
 750 │   defp build_cache_key(prefix, id, extra \\ nil) do
     │        ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/exchange_order_repository.ex:750:8: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.ExchangeOrderRepository (module)

     warning: default values for the optional arguments in build_cache_key/3 are never used
     │
 650 │   defp build_cache_key(prefix, id, extra \\ nil) do
     │        ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/racing_game/race_repository.ex:650:8: RacingGame.Live.AdminPanel.Repositories.RacingGame.RaceRepository (module)

    warning: module attribute @cache_ttl was set but never used
    │
 20 │   @cache_ttl 300  # 5分钟缓存
    │   ~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/accounts/user_identity_repository.ex:20: RacingGame.Live.AdminPanel.Repositories.Accounts.UserIdentityRepository (module)

    warning: unused alias Query
    │
 13 │   alias Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/bet_repository.ex:13:3

    warning: unused alias User
    │
 17 │   alias Cypridina.Accounts.User
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/bet_repository.ex:17:3

    warning: unused import Ash.Query
    │
 14 │   import Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/bet_repository.ex:14:3

    warning: unused alias Query
    │
 13 │   alias Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/communication_repository.ex:13:3

    warning: unused import Ash.Query
    │
 14 │   import Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/communication_repository.ex:14:3

    warning: unused alias Query
    │
 13 │   alias Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/stock_repository.ex:13:3

    warning: unused alias User
    │
 17 │   alias Cypridina.Accounts.User
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/stock_repository.ex:17:3

    warning: unused import Ash.Query
    │
 14 │   import Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/stock_repository.ex:14:3

    warning: unused alias Query
    │
 13 │   alias Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/ledger/account_repository.ex:13:3

    warning: unused alias Transfer
    │
 15 │   alias Cypridina.Ledger.{Account, Transfer, Balance}
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/ledger/account_repository.ex:15:3

    warning: unused import Ash.Query
    │
 14 │   import Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/ledger/account_repository.ex:14:3

    warning: unused alias Query
    │
 14 │   alias Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/accounts/user_repository.ex:14:3

    warning: unused alias UserIdentity
    │
 16 │   alias Cypridina.Accounts.{User, UserIdentity, AgentRelationship}
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/accounts/user_repository.ex:16:3

    warning: unused import Ash.Query
    │
 15 │   import Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/accounts/user_repository.ex:15:3

    warning: unused alias Query
    │
 13 │   alias Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/accounts/user_identity_repository.ex:13:3

    warning: unused import Ash.Query
    │
 14 │   import Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/accounts/user_identity_repository.ex:14:3

    warning: unused alias Query
    │
 15 │   alias Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/activity_system_repositories.ex:15:3

    warning: unused import Ash.Query
    │
 16 │   import Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/activity_system_repositories.ex:16:3

    warning: unused alias Query
    │
 13 │   alias Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/racing_game/system_communication_repository.ex:13:3

    warning: unused alias Query
    │
 13 │   alias Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/racing_game/bet_repository.ex:13:3

    warning: unused alias Race
    │
 15 │   alias RacingGame.{Bet, Race}
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/racing_game/bet_repository.ex:15:3

    warning: unused alias Query
    │
 13 │   alias Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/ledger/transfer_repository.ex:13:3

    warning: unused import Ash.Query
    │
 14 │   import Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/racing_game/system_communication_repository.ex:14:3

    warning: unused alias Query
    │
 13 │   alias Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/ledger/balance_repository.ex:13:3

    warning: unused import Ash.Query
    │
 14 │   import Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/ledger/transfer_repository.ex:14:3

    warning: unused import Ash.Query
    │
 14 │   import Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/ledger/balance_repository.ex:14:3

    warning: unused import Ash.Query
    │
 14 │   import Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/racing_game/bet_repository.ex:14:3

    warning: unused alias Query
    │
 13 │   alias Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/racing_game/stock_repository.ex:13:3

    warning: unused alias Query
    │
 13 │   alias Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/customer_chat_repository.ex:13:3

    warning: unused import Ash.Query
    │
 14 │   import Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/racing_game/stock_repository.ex:14:3

    warning: unused import Ash.Query
    │
 14 │   import Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/customer_chat_repository.ex:14:3

    warning: unused alias Query
    │
 13 │   alias Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/exchange_order_repository.ex:13:3

    warning: unused import Ash.Query
    │
 14 │   import Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/exchange_order_repository.ex:14:3

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_exchange_config(config_data, options \\ []) do
    │                                           ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/exchange_config_repository.ex:35:43: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.ExchangeConfigRepository.create_exchange_config/2

    warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
    │
 87 │           Logger.warn("⚠️ [兑换配置仓储] 兑换配置不存在: #{config_id}")
    │                  ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/exchange_config_repository.ex:87:18: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.ExchangeConfigRepository.get_exchange_config/2

     warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
     │
 295 │         Logger.warn("⚠️ [敏感词仓储] 发现敏感词: #{inspect(Enum.map(matched_words, & &1.word))}")
     │                ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/sensitive_word_repository.ex:295:16: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.SensitiveWordRepository.check_text_for_sensitive_words/2

     warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
     │
 127 │           Logger.warn("⚠️ [兑换配置仓储] 兑换配置不存在: #{config_name}")
     │                  ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/exchange_config_repository.ex:127:18: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.ExchangeConfigRepository.get_exchange_config_by_name/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 153 │   def update_exchange_config(config_id, update_data, options \\ []) do
     │                                                      ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/exchange_config_repository.ex:153:54: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.ExchangeConfigRepository.update_exchange_config/3

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_payment_gateway(gateway_data, options \\ []) do
    │                                            ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/payment_gateway_repository.ex:35:44: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.PaymentGatewayRepository.create_payment_gateway/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 190 │   def delete_exchange_config(config_id, options \\ []) do
     │                                         ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/exchange_config_repository.ex:190:41: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.ExchangeConfigRepository.delete_exchange_config/2

    warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
    │
 87 │           Logger.warn("⚠️ [支付网关仓储] 支付网关不存在: #{gateway_id}")
    │                  ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/payment_gateway_repository.ex:87:18: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.PaymentGatewayRepository.get_payment_gateway/2

     warning: variable "current_time" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 305 │       current_time = DateTime.utc_now()
     │       ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/verification_code_repository.ex:305:7: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.VerificationCodeRepository.get_expired_codes/1

     warning: variable "search_term" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 612 │     search_term = "%#{search}%"
     │     ~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/sensitive_word_repository.ex:612:5: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.SensitiveWordRepository.apply_word_search/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 113 │   def update_payment_gateway(gateway_id, update_data, options \\ []) do
     │                                                       ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/payment_gateway_repository.ex:113:55: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.PaymentGatewayRepository.update_payment_gateway/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 150 │   def delete_payment_gateway(gateway_id, options \\ []) do
     │                                          ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/payment_gateway_repository.ex:150:42: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.PaymentGatewayRepository.delete_payment_gateway/2

    warning: unused alias Query
    │
 13 │   alias Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/racing_game/race_repository.ex:13:3

    warning: unused import Ash.Query
    │
 14 │   import Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/racing_game/race_repository.ex:14:3

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 672 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/sensitive_word_repository.ex:672:5: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.SensitiveWordRepository.get_recent_words_count/1

     warning: variable "enabled" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 704 │   defp maybe_apply_enabled_filter(query, enabled) do
     │                                          ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/sensitive_word_repository.ex:704:42: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.SensitiveWordRepository.maybe_apply_enabled_filter/2

     warning: variable "word_type" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 710 │   defp maybe_apply_word_type_filter(query, word_type) do
     │                                            ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/sensitive_word_repository.ex:710:44: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.SensitiveWordRepository.maybe_apply_word_type_filter/2

     warning: variable "search_term" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 559 │     search_term = "%#{search}%"
     │     ~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_question_repository.ex:559:5: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.UserQuestionRepository.apply_question_search/2

     warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
     │
 517 │       Logger.warn("⚠️ [验证码仓储] 验证码已使用: #{verification_code.id}")
     │              ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/verification_code_repository.ex:517:14: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.VerificationCodeRepository.do_verify_code/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 389 │   def activate_exchange_config(config_id, options \\ []) do
     │                                           ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/exchange_config_repository.ex:389:43: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.ExchangeConfigRepository.activate_exchange_config/2

     warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
     │
 523 │       Logger.warn("⚠️ [验证码仓储] 验证码已过期: #{verification_code.id}")
     │              ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/verification_code_repository.ex:523:14: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.VerificationCodeRepository.do_verify_code/3

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 624 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_question_repository.ex:624:5: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.UserQuestionRepository.get_recent_questions_count/1

     warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
     │
 530 │       Logger.warn("⚠️ [验证码仓储] 验证次数超限: #{verification_code.id}")
     │              ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/verification_code_repository.ex:530:14: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.VerificationCodeRepository.do_verify_code/3

     warning: variable "staff_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 650 │   defp get_staff_assigned_count(staff_id) do
     │                                 ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_question_repository.ex:650:33: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.UserQuestionRepository.get_staff_assigned_count/1

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 426 │   def deactivate_exchange_config(config_id, options \\ []) do
     │                                             ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/exchange_config_repository.ex:426:45: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.ExchangeConfigRepository.deactivate_exchange_config/2

     warning: variable "staff_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 660 │   defp get_staff_completed_count(staff_id) do
     │                                  ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_question_repository.ex:660:34: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.UserQuestionRepository.get_staff_completed_count/1

     warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
     │
 539 │       Logger.warn("⚠️ [验证码仓储] 验证码不匹配: #{verification_code.id}")
     │              ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/verification_code_repository.ex:539:14: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.VerificationCodeRepository.do_verify_code/3

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 671 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_question_repository.ex:671:5: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.UserQuestionRepository.get_staff_recent_completed_count/2

     warning: variable "rate" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 471 │           {rate, ""} -> Ash.Query.filter(query, exchange_rate  >= rate)
     │            ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/exchange_config_repository.ex:471:12: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.ExchangeConfigRepository.apply_filters/2

     warning: variable "staff_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 670 │   defp get_staff_recent_completed_count(staff_id, days) do
     │                                         ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_question_repository.ex:670:41: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.UserQuestionRepository.get_staff_recent_completed_count/2

     warning: variable "rate" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 477 │           {rate, ""} -> Ash.Query.filter(query, exchange_rate  <= rate)
     │            ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/exchange_config_repository.ex:477:12: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.ExchangeConfigRepository.apply_filters/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 209 │   def get_subordinates(user_id, options \\ []) do
     │                                 ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/user_repository.ex:209:33: RacingGame.Live.AdminPanel.Repositories.UserRepository.get_subordinates/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 311 │   def activate_payment_gateway(gateway_id, options \\ []) do
     │                                            ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/payment_gateway_repository.ex:311:44: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.PaymentGatewayRepository.activate_payment_gateway/2

     warning: variable "status" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 719 │   defp maybe_apply_status_filter(query, status) do
     │                                         ~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_question_repository.ex:719:41: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.UserQuestionRepository.maybe_apply_status_filter/2

     warning: variable "question_type" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 725 │   defp maybe_apply_type_filter(query, question_type) do
     │                                       ~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_question_repository.ex:725:39: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.UserQuestionRepository.maybe_apply_type_filter/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 348 │   def deactivate_payment_gateway(gateway_id, options \\ []) do
     │                                              ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/payment_gateway_repository.ex:348:46: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.PaymentGatewayRepository.deactivate_payment_gateway/2

     warning: variable "code_type" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 604 │   defp get_earliest_code_in_window(phone, code_type, time_threshold) do
     │                                           ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/verification_code_repository.ex:604:43: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.VerificationCodeRepository.get_earliest_code_in_window/3

     warning: variable "priority" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 731 │   defp maybe_apply_priority_filter(query, priority) do
     │                                           ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_question_repository.ex:731:43: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.UserQuestionRepository.maybe_apply_priority_filter/2

     warning: default values for the optional arguments in build_cache_key/3 are never used
     │
 715 │   defp build_cache_key(prefix, id, extra \\ nil) do
     │        ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/sensitive_word_repository.ex:715:8: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.SensitiveWordRepository (module)

     warning: variable "phone" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 604 │   defp get_earliest_code_in_window(phone, code_type, time_threshold) do
     │                                    ~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/verification_code_repository.ex:604:36: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.VerificationCodeRepository.get_earliest_code_in_window/3

     warning: variable "time_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 604 │   defp get_earliest_code_in_window(phone, code_type, time_threshold) do
     │                                                      ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/verification_code_repository.ex:604:54: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.VerificationCodeRepository.get_earliest_code_in_window/3

     warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
     │
 307 │         Logger.warn("⚠️ [用户标签仓储] 标签已存在: #{user_id} - #{tag_name}")
     │                ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_tag_repository.ex:307:16: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.UserTagRepository.add_user_tag/4

     warning: variable "current_time" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 644 │     current_time = DateTime.utc_now()
     │     ~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/verification_code_repository.ex:644:5: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.VerificationCodeRepository.apply_code_filter/3

     warning: variable "current_time" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 648 │     current_time = DateTime.utc_now()
     │     ~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/verification_code_repository.ex:648:5: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.VerificationCodeRepository.apply_code_filter/3

     warning: variable "user_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 364 │   defp fetch_agent_relationship(user_id) do
     │                                 ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/user_repository.ex:364:33: RacingGame.Live.AdminPanel.Repositories.UserRepository.fetch_agent_relationship/1

     warning: variable "search_term" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 656 │     search_term = "%#{search}%"
     │     ~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/verification_code_repository.ex:656:5: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.VerificationCodeRepository.apply_code_search/2

     warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
     │
 347 │           Logger.warn("⚠️ [用户标签仓储] 标签不存在: #{user_id} - #{tag_name}")
     │                  ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_tag_repository.ex:347:18: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.UserTagRepository.remove_user_tag/3

     warning: variable "current_time" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 711 │     current_time = DateTime.utc_now()
     │     ~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/verification_code_repository.ex:711:5: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.VerificationCodeRepository.get_expired_codes_count/0

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 722 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/verification_code_repository.ex:722:5: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.VerificationCodeRepository.get_recent_codes_count/1

     warning: variable "code_type" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 770 │   defp maybe_apply_code_type_filter(query, code_type) do
     │                                            ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/verification_code_repository.ex:770:44: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.VerificationCodeRepository.maybe_apply_code_type_filter/2

     warning: variable "end_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 776 │   defp maybe_apply_date_range(query, {start_date, end_date}) do
     │                                                   ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/verification_code_repository.ex:776:51: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.VerificationCodeRepository.maybe_apply_date_range/2

     warning: variable "start_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 776 │   defp maybe_apply_date_range(query, {start_date, end_date}) do
     │                                       ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/verification_code_repository.ex:776:39: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.VerificationCodeRepository.maybe_apply_date_range/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_payment_config(config_data, options \\ []) do
    │                                          ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/payment_config_repository.ex:35:42: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.PaymentConfigRepository.create_payment_config/2

    warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
    │
 87 │           Logger.warn("⚠️ [支付配置仓储] 支付配置不存在: #{config_id}")
    │                  ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/payment_config_repository.ex:87:18: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.PaymentConfigRepository.get_payment_config/2

     warning: variable "user_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 561 │   defp fetch_user_tags(user_id, options) do
     │                        ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_tag_repository.ex:561:24: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.UserTagRepository.fetch_user_tags/2

     warning: variable "search_term" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 609 │     search_term = "%#{search}%"
     │     ~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_tag_repository.ex:609:5: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.UserTagRepository.apply_tag_search/2

    warning: module attribute @cache_ttl was set but never used
    │
 20 │   @cache_ttl 600  # 10分钟缓存
    │   ~~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/accounts/agent_relationship_repository.ex:20: RacingGame.Live.AdminPanel.Repositories.Accounts.AgentRelationshipRepository (module)

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 113 │   def update_payment_config(config_id, update_data, options \\ []) do
     │                                                     ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/payment_config_repository.ex:113:53: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.PaymentConfigRepository.update_payment_config/3

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 663 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_tag_repository.ex:663:5: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.UserTagRepository.get_recent_tags_count/1

     warning: variable "user_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 695 │   defp maybe_apply_user_filter(query, user_id) do
     │                                       ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_tag_repository.ex:695:39: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.UserTagRepository.maybe_apply_user_filter/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 150 │   def delete_payment_config(config_id, options \\ []) do
     │                                        ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/payment_config_repository.ex:150:40: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.PaymentConfigRepository.delete_payment_config/2

     warning: variable "tag_type" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 701 │   defp maybe_apply_tag_type_filter(query, tag_type) do
     │                                           ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_tag_repository.ex:701:43: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.UserTagRepository.maybe_apply_tag_type_filter/2

     warning: variable "tag_name" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 707 │   defp maybe_apply_tag_name_filter(query, tag_name) do
     │                                           ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_tag_repository.ex:707:43: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.UserTagRepository.maybe_apply_tag_name_filter/2

    warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
    │
 62 │           Logger.warn("⚠️ [Free Bonus仓储] Free Bonus不存在: #{bonus_id}")
    │                  ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/free_bonus_repository.ex:62:18: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.FreeBonusRepository.get_free_bonus/2

    warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
    │
 98 │           Logger.warn("⚠️ [CDKEY活动仓储] CDKEY不存在: #{cdkey_code}")
    │                  ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex:98:18: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.CdkeyActivityRepository.get_cdkey_by_code/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 347 │   def activate_payment_config(config_id, options \\ []) do
     │                                          ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/payment_config_repository.ex:347:42: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.PaymentConfigRepository.activate_payment_config/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 384 │   def deactivate_payment_config(config_id, options \\ []) do
     │                                            ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/payment_config_repository.ex:384:44: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.PaymentConfigRepository.deactivate_payment_config/2

     warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
     │
 131 │           Logger.warn("⚠️ [CDKEY活动仓储] CDKEY不存在: #{cdkey_id}")
     │                  ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex:131:18: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.CdkeyActivityRepository.get_cdkey/2

    warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
    │
 71 │           Logger.warn("⚠️ [签到活动仓储] 签到活动不存在: #{activity_id}")
    │                  ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/sign_in_activity_repository.ex:71:18: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.SignInActivityRepository.get_sign_in_activity/2

     warning: variable "amount" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 432 │           {amount, ""} -> Ash.Query.filter(query, min_amount  >= amount)
     │            ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/payment_config_repository.ex:432:12: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.PaymentConfigRepository.apply_filters/2

     warning: variable "amount" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 438 │           {amount, ""} -> Ash.Query.filter(query, max_amount  <= amount)
     │            ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/payment_config_repository.ex:438:12: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.PaymentConfigRepository.apply_filters/2

     warning: variable "max_level" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 146 │   defp apply_user_filter(query, :agent_level_range, {min_level, max_level}) do
     │                                                                 ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:146:65: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_user_filter/3

     warning: variable "min_level" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 146 │   defp apply_user_filter(query, :agent_level_range, {min_level, max_level}) do
     │                                                      ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:146:54: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_user_filter/3

     warning: variable "date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 166 │   defp apply_user_filter(query, :created_after, date) do
     │                                                 ~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:166:49: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_user_filter/3

     warning: variable "date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 170 │   defp apply_user_filter(query, :created_before, date) do
     │                                                  ~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:170:50: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_user_filter/3

     warning: variable "end_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 174 │   defp apply_user_filter(query, :created_date_range, {start_date, end_date}) do
     │                                                                   ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:174:67: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_user_filter/3

     warning: default values for the optional arguments in build_cache_key/3 are never used
     │
 736 │   defp build_cache_key(prefix, id, extra \\ nil) do
     │        ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_question_repository.ex:736:8: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.UserQuestionRepository (module)

     warning: variable "start_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 174 │   defp apply_user_filter(query, :created_date_range, {start_date, end_date}) do
     │                                                       ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:174:55: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_user_filter/3

     warning: variable "max_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 178 │   defp apply_user_filter(query, :numeric_id_range, {min_id, max_id}) do
     │                                                             ~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:178:61: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_user_filter/3

     warning: variable "min_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 178 │   defp apply_user_filter(query, :numeric_id_range, {min_id, max_id}) do
     │                                                     ~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:178:53: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_user_filter/3

     warning: variable "now" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 361 │     now = DateTime.utc_now()
     │     ~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex:361:5: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.CdkeyActivityRepository.maybe_filter_by_expiry/2

     warning: variable "end_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 306 │   defp maybe_filter_by_date_range(query, {start_date, end_date}) do
     │                                                       ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/sign_in_activity_repository.ex:306:55: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.SignInActivityRepository.maybe_filter_by_date_range/2

     warning: variable "now" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 365 │     now = DateTime.utc_now()
     │     ~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex:365:5: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.CdkeyActivityRepository.maybe_filter_by_expiry/2

     warning: variable "start_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 306 │   defp maybe_filter_by_date_range(query, {start_date, end_date}) do
     │                                           ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/sign_in_activity_repository.ex:306:43: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.SignInActivityRepository.maybe_filter_by_date_range/2

     warning: variable "date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 228 │   defp apply_identity_filter(query, :created_after, date) do
     │                                                     ~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:228:53: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_identity_filter/3

     warning: variable "date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 232 │   defp apply_identity_filter(query, :created_before, date) do
     │                                                      ~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:232:54: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_identity_filter/3

     warning: variable "max_level" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 277 │   defp apply_agent_relationship_filter(query, :level_range, {min_level, max_level}) do
     │                                                                         ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:277:73: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_agent_relationship_filter/3

     warning: variable "min_level" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 277 │   defp apply_agent_relationship_filter(query, :level_range, {min_level, max_level}) do
     │                                                              ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:277:62: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_agent_relationship_filter/3

     warning: variable "date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 285 │   defp apply_agent_relationship_filter(query, :created_after, date) do
     │                                                               ~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:285:63: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_agent_relationship_filter/3

     warning: variable "date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 289 │   defp apply_agent_relationship_filter(query, :created_before, date) do
     │                                                                ~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:289:64: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_agent_relationship_filter/3

     warning: variable "end_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 293 │   defp apply_agent_relationship_filter(query, :created_date_range, {start_date, end_date}) do
     │                                                                                 ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:293:81: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_agent_relationship_filter/3

     warning: variable "start_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 293 │   defp apply_agent_relationship_filter(query, :created_date_range, {start_date, end_date}) do
     │                                                                     ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:293:69: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_agent_relationship_filter/3

     warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
     │
 456 │     Logger.warn("⚠️ [查询构建器] 未知条件类型: #{inspect(condition)}")
     │            ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:456:12: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.build_condition_expr/1

     warning: variable "status_code" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 220 │       status_code ->
     │       ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/bet_query_builder.ex:220:7: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.BetQueryBuilder.apply_status_filter/2

    warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
    │
 62 │           Logger.warn("⚠️ [限时礼包仓储] 限时礼包不存在: #{gift_id}")
    │                  ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/limited_gift_repository.ex:62:18: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.LimitedGiftRepository.get_limited_gift/2

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 275 │   defp apply_date_range_filter(query, from_date, nil) do
     │                                       ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/bet_query_builder.ex:275:39: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.BetQueryBuilder.apply_date_range_filter/3

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 278 │   defp apply_date_range_filter(query, nil, to_date) do
     │                                            ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/bet_query_builder.ex:278:44: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.BetQueryBuilder.apply_date_range_filter/3

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 281 │   defp apply_date_range_filter(query, from_date, to_date) do
     │                                       ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/bet_query_builder.ex:281:39: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.BetQueryBuilder.apply_date_range_filter/3

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 281 │   defp apply_date_range_filter(query, from_date, to_date) do
     │                                                  ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/bet_query_builder.ex:281:50: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.BetQueryBuilder.apply_date_range_filter/3

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 311 │       {:updated_at, %{from: from_date, to: to_date}} ->
     │                             ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/bet_query_builder.ex:311:29: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.BetQueryBuilder.apply_single_filter/3

     warning: default values for the optional arguments in build_cache_key/3 are never used
     │
 782 │   defp build_cache_key(prefix, id, extra \\ nil) do
     │        ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/verification_code_repository.ex:782:8: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.VerificationCodeRepository (module)

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 311 │       {:updated_at, %{from: from_date, to: to_date}} ->
     │                                            ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/bet_query_builder.ex:311:44: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.BetQueryBuilder.apply_single_filter/3

     warning: variable "search_term" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 374 │   defp get_matching_user_ids(search_term) do
     │                              ~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/bet_query_builder.ex:374:30: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.BetQueryBuilder.get_matching_user_ids/1

     warning: default values for the optional arguments in build_cache_key/3 are never used
     │
 712 │   defp build_cache_key(prefix, id, extra \\ nil) do
     │        ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_tag_repository.ex:712:8: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.UserTagRepository (module)

     warning: variable "end_time" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 334 │   defp maybe_filter_by_time_range(query, {start_time, end_time}) do
     │                                                       ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/limited_gift_repository.ex:334:55: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.LimitedGiftRepository.maybe_filter_by_time_range/2

     warning: variable "start_time" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 334 │   defp maybe_filter_by_time_range(query, {start_time, end_time}) do
     │                                           ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/limited_gift_repository.ex:334:43: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.LimitedGiftRepository.maybe_filter_by_time_range/2

     warning: variable "user_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 264 │   defp apply_user_recipient_filter(query, user_id) do
     │                                           ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/communication_query_builder.ex:264:43: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CommunicationQueryBuilder.apply_user_recipient_filter/2

     warning: variable "now" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 278 │     now = DateTime.utc_now()
     │     ~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/communication_query_builder.ex:278:5: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CommunicationQueryBuilder.apply_expiry_filter/1

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 297 │       {:updated_at, %{from: from_date, to: to_date}} ->
     │                             ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/communication_query_builder.ex:297:29: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CommunicationQueryBuilder.apply_single_filter/3

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 297 │       {:updated_at, %{from: from_date, to: to_date}} ->
     │                                            ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/communication_query_builder.ex:297:44: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CommunicationQueryBuilder.apply_single_filter/3

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 302 │       {:expires_at, %{from: from_date, to: to_date}} ->
     │                             ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/communication_query_builder.ex:302:29: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CommunicationQueryBuilder.apply_single_filter/3

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 302 │       {:expires_at, %{from: from_date, to: to_date}} ->
     │                                            ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/communication_query_builder.ex:302:44: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CommunicationQueryBuilder.apply_single_filter/3

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 319 │   defp apply_date_range_filter(query, from_date, nil) do
     │                                       ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/communication_query_builder.ex:319:39: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CommunicationQueryBuilder.apply_date_range_filter/3

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 322 │   defp apply_date_range_filter(query, nil, to_date) do
     │                                            ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/communication_query_builder.ex:322:44: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CommunicationQueryBuilder.apply_date_range_filter/3

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 325 │   defp apply_date_range_filter(query, from_date, to_date) do
     │                                       ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/communication_query_builder.ex:325:39: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CommunicationQueryBuilder.apply_date_range_filter/3

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 325 │   defp apply_date_range_filter(query, from_date, to_date) do
     │                                                  ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/communication_query_builder.ex:325:50: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CommunicationQueryBuilder.apply_date_range_filter/3

    warning: unused alias Query
    │
 13 │   alias Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/accounts/agent_relationship_repository.ex:13:3

    warning: unused alias User
    │
 15 │   alias Cypridina.Accounts.{AgentRelationship, User}
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/accounts/agent_relationship_repository.ex:15:3

    warning: unused import Ash.Query
    │
 14 │   import Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/accounts/agent_relationship_repository.ex:14:3

    warning: module attribute @bet_statuses was set but never used
    │
 20 │   @bet_statuses %{0 => :pending, 1 => :won, 2 => :lost}
    │   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/query_builders/bet_query_builder.ex:20: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.BetQueryBuilder (module)

    warning: module attribute @cache_ttl was set but never used
    │
 20 │   @cache_ttl 300  # 5分钟缓存
    │   ~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/payment_gateway_repository.ex:20: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.PaymentGatewayRepository (module)

    warning: unused alias Query
    │
 13 │   alias Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/user_repository.ex:13:3

    warning: unused import Ash.Query
    │
 14 │   import Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/user_repository.ex:14:3

    warning: unused alias Query
    │
 16 │   alias Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/sign_in_activity_repository.ex:16:3

    warning: unused import Ash.Query
    │
 17 │   import Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/sign_in_activity_repository.ex:17:3

    warning: unused alias Query
    │
 15 │   alias Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/payment_gateway_repository.ex:15:3

    warning: unused import Ash.Query
    │
 16 │   import Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/payment_gateway_repository.ex:16:3

    warning: module attribute @cache_ttl was set but never used
    │
 20 │   @cache_ttl 300  # 5分钟缓存
    │   ~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/payment_config_repository.ex:20: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.PaymentConfigRepository (module)

    warning: unused alias Query
    │
 15 │   alias Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/free_bonus_repository.ex:15:3

    warning: unused import Ash.Query
    │
 16 │   import Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/free_bonus_repository.ex:16:3

    warning: unused alias Query
    │
 16 │   alias Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/payment_config_repository.ex:16:3

    warning: unused import Ash.Query
    │
 17 │   import Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/payment_config_repository.ex:17:3

     warning: variable "account_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 254 │         {:ok, account_id} ->
     │               ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/ledger_query_builder.ex:254:15: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.LedgerQueryBuilder.build_user_financial_query/3

     warning: variable "account_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 263 │         {:ok, account_id} ->
     │               ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/ledger_query_builder.ex:263:15: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.LedgerQueryBuilder.build_user_financial_query/3

     warning: variable "pattern" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 405 │   defp apply_single_search_condition(query, {:ilike, field, pattern}) do
     │                                                             ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/ledger_query_builder.ex:405:61: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.LedgerQueryBuilder.apply_single_search_condition/2

     warning: variable "pattern" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 414 │       {:ilike, field, pattern} -> Ash.Query.filter(query, ilike(^field, pattern))
     │                       ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/ledger_query_builder.ex:414:23: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.LedgerQueryBuilder.apply_multiple_search_conditions/2

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 429 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/ledger_query_builder.ex:429:5: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.LedgerQueryBuilder.maybe_limit_by_days/2

     warning: variable "identifier" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 435 │   defp get_account_id_by_identifier(identifier) do
     │                                     ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/ledger_query_builder.ex:435:37: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.LedgerQueryBuilder.get_account_id_by_identifier/1

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 282 │   defp apply_date_range_filter(query, from_date, nil) do
     │                                       ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/stock_query_builder.ex:282:39: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.StockQueryBuilder.apply_date_range_filter/3

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 285 │   defp apply_date_range_filter(query, nil, to_date) do
     │                                            ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/stock_query_builder.ex:285:44: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.StockQueryBuilder.apply_date_range_filter/3

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 201 │       {:updated_at, %{from: from_date, to: to_date}} ->
     │                             ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/user_query_builder.ex:201:29: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.UserQueryBuilder.apply_single_filter/3

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 201 │       {:updated_at, %{from: from_date, to: to_date}} ->
     │                                            ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/user_query_builder.ex:201:44: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.UserQueryBuilder.apply_single_filter/3

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 288 │   defp apply_date_range_filter(query, from_date, to_date) do
     │                                       ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/stock_query_builder.ex:288:39: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.StockQueryBuilder.apply_date_range_filter/3

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 288 │   defp apply_date_range_filter(query, from_date, to_date) do
     │                                                  ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/stock_query_builder.ex:288:50: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.StockQueryBuilder.apply_date_range_filter/3

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_channel_stats(stats_data, options \\ []) do
    │                                        ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/channel_stats_repository.ex:35:40: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.ChannelStatsRepository.create_channel_stats/2

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 315 │       {:inserted_at, %{from: from_date, to: to_date}} ->
     │                              ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/stock_query_builder.ex:315:30: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.StockQueryBuilder.apply_single_filter/3

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 315 │       {:inserted_at, %{from: from_date, to: to_date}} ->
     │                                             ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/stock_query_builder.ex:315:45: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.StockQueryBuilder.apply_single_filter/3

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 218 │   defp apply_date_range_filter(query, from_date, nil) do
     │                                       ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/user_query_builder.ex:218:39: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.UserQueryBuilder.apply_date_range_filter/3

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 221 │   defp apply_date_range_filter(query, nil, to_date) do
     │                                            ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/user_query_builder.ex:221:44: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.UserQueryBuilder.apply_date_range_filter/3

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 224 │   defp apply_date_range_filter(query, from_date, to_date) do
     │                                       ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/user_query_builder.ex:224:39: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.UserQueryBuilder.apply_date_range_filter/3

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 224 │   defp apply_date_range_filter(query, from_date, to_date) do
     │                                                  ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/user_query_builder.ex:224:50: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.UserQueryBuilder.apply_date_range_filter/3

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 70 │   def get_channel_stats(stats_id, options \\ []) do
    │                                   ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/channel_stats_repository.ex:70:35: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.ChannelStatsRepository.get_channel_stats/2

     warning: variable "pattern" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 510 │     Enum.reduce(conditions, query, fn {op, field, pattern}, acc_query ->
     │                                                   ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:510:51: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_search_across_fields/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 318 │   defp maybe_add_index_hints(query, options) do
     │                                     ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/user_query_builder.ex:318:37: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.UserQueryBuilder.maybe_add_index_hints/2

     warning: variable "search_term" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 378 │   defp get_matching_user_ids(search_term) do
     │                              ~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/stock_query_builder.ex:378:30: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.StockQueryBuilder.get_matching_user_ids/1

     warning: variable "user_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 532 │   defp apply_single_filter(query, :user_id, user_id) do
     │                                             ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:532:45: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_single_filter/3

     warning: variable "query" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 356 │   def analyze_query_complexity(query) do
     │                                ~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/user_query_builder.ex:356:32: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.UserQueryBuilder.analyze_query_complexity/1

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 113 │   def update_channel_stats(channel_stats, update_data, options \\ []) do
     │                                                        ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/channel_stats_repository.ex:113:56: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.ChannelStatsRepository.update_channel_stats/3

     warning: variable "customer_service_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 535 │   defp apply_single_filter(query, :customer_service_id, customer_service_id) do
     │                                                         ~~~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:535:57: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_single_filter/3

     warning: variable "query" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 376 │   def get_execution_plan(query) do
     │                          ~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/user_query_builder.ex:376:26: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.UserQueryBuilder.get_execution_plan/1

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 148 │   def delete_channel_stats(channel_stats, options \\ []) do
     │                                           ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/channel_stats_repository.ex:148:43: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.ChannelStatsRepository.delete_channel_stats/2

     warning: variable "assigned_to" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 538 │   defp apply_single_filter(query, :assigned_to, assigned_to) do
     │                                                 ~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:538:49: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_single_filter/3

     warning: variable "auditor_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 541 │   defp apply_single_filter(query, :auditor_id, auditor_id) do
     │                                                ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:541:48: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_single_filter/3

     warning: variable "created_by" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 544 │   defp apply_single_filter(query, :created_by, created_by) do
     │                                                ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:544:48: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_single_filter/3

     warning: variable "status" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 547 │   defp apply_single_filter(query, :status, status) do
     │                                            ~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:547:44: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_single_filter/3

     warning: variable "priority" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 550 │   defp apply_single_filter(query, :priority, priority) do
     │                                              ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:550:46: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_single_filter/3

     warning: variable "progress_status" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 553 │   defp apply_single_filter(query, :progress_status, progress_status) do
     │                                                     ~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:553:53: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_single_filter/3

     warning: variable "word_type" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 556 │   defp apply_single_filter(query, :word_type, word_type) do
     │                                               ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:556:47: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_single_filter/3

     warning: variable "tag_type" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 559 │   defp apply_single_filter(query, :tag_type, tag_type) do
     │                                              ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:559:46: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_single_filter/3

     warning: variable "code_type" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 562 │   defp apply_single_filter(query, :code_type, code_type) do
     │                                               ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:562:47: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_single_filter/3

     warning: variable "read_ids_query" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 320 │       read_ids_query = SystemCommunicationRead
     │       ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:320:7: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.build_user_unread_communications_query/2

     warning: variable "enabled" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 565 │   defp apply_single_filter(query, :enabled, enabled) do
     │                                             ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:565:45: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_single_filter/3

     warning: variable "is_used" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 568 │   defp apply_single_filter(query, :is_used, is_used) do
     │                                             ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:568:45: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_single_filter/3

     warning: variable "start_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 330 │       start_date = Date.utc_today() |> Date.add(-days)
     │       ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/channel_stats_repository.ex:330:7: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.ChannelStatsRepository.get_channel_performance/3

     warning: variable "end_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 572 │     {start_date, end_date} = date_range
     │                  ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:572:18: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_single_filter/3

     warning: variable "start_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 572 │     {start_date, end_date} = date_range
     │      ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:572:6: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_single_filter/3

     warning: variable "end_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 576 │     {start_date, end_date} = date_range
     │                  ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:576:18: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_single_filter/3

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_coin_stats(stats_data, options \\ []) do
    │                                     ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/coin_stats_repository.ex:35:37: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.CoinStatsRepository.create_coin_stats/2

     warning: variable "start_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 576 │     {start_date, end_date} = date_range
     │      ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:576:6: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_single_filter/3

     warning: variable "end_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 580 │     {start_date, end_date} = date_range
     │                  ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:580:18: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_single_filter/3

     warning: variable "start_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 580 │     {start_date, end_date} = date_range
     │      ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:580:6: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_single_filter/3

     warning: variable "status" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 599 │   defp apply_status_filter(query, status) do
     │                                   ~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:599:35: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_status_filter/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 70 │   def get_coin_stats(stats_id, options \\ []) do
    │                                ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/coin_stats_repository.ex:70:32: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.CoinStatsRepository.get_coin_stats/2

     warning: variable "priority" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 605 │   defp apply_priority_filter(query, priority) do
     │                                     ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:605:37: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_priority_filter/2

     warning: variable "end_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 456 │   defp apply_race_filter(query, :date_range, {start_date, end_date}) do
     │                                                           ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:456:59: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.apply_race_filter/3

     warning: variable "start_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 456 │   defp apply_race_filter(query, :date_range, {start_date, end_date}) do
     │                                               ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:456:47: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.apply_race_filter/3

     warning: variable "date_from" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 618 │   defp maybe_apply_date_from(query, date_from) do
     │                                     ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:618:37: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.maybe_apply_date_from/2

     warning: variable "search_term" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 464 │     search_term = "%#{search}%"
     │     ~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:464:5: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.apply_race_search/2

     warning: variable "date_to" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 623 │   defp maybe_apply_date_to(query, date_to) do
     │                                   ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:623:35: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.maybe_apply_date_to/2

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 470 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:470:5: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.build_recent_races_query/1

     warning: variable "word_type" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 629 │   defp apply_word_type_filter(query, word_type) do
     │                                      ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:629:38: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_word_type_filter/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 113 │   def update_coin_stats(coin_stats, update_data, options \\ []) do
     │                                                  ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/coin_stats_repository.ex:113:50: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.CoinStatsRepository.update_coin_stats/3

     warning: variable "tag_type" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 635 │   defp apply_tag_type_filter(query, tag_type) do
     │                                     ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:635:37: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_tag_type_filter/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_ltv_stats(stats_data, options \\ []) do
    │                                    ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/ltv_stats_repository.ex:35:36: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.LtvStatsRepository.create_ltv_stats/2

     warning: variable "code_type" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 641 │   defp apply_code_type_filter(query, code_type) do
     │                                      ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:641:38: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_code_type_filter/2

     warning: variable "search" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 506 │   defp apply_bet_search(query, search) do
     │                                ~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:506:32: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.apply_bet_search/2

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 513 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:513:5: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.build_recent_bets_query/1

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 148 │   def delete_coin_stats(coin_stats, options \\ []) do
     │                                     ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/coin_stats_repository.ex:148:37: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.CoinStatsRepository.delete_coin_stats/2

     warning: variable "search_term" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 544 │     search_term = "%#{search}%"
     │     ~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:544:5: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.apply_stock_search/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 70 │   def get_ltv_stats(stats_id, options \\ []) do
    │                               ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/ltv_stats_repository.ex:70:31: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.LtvStatsRepository.get_ltv_stats/2

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 550 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:550:5: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.build_recent_stock_transactions_query/1

     warning: variable "one_hour_ago" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 744 │       one_hour_ago = DateTime.add(current_time, -3600, :second)
     │       ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:744:7: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.build_system_health_check_query/1

     warning: variable "user_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 558 │   defp build_user_stock_transactions_query(user_id, options) do
     │                                            ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:558:44: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.build_user_stock_transactions_query/2

     warning: variable "params" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 739 │   defp build_system_health_check_query(params) do
     │                                        ~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:739:40: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.build_system_health_check_query/1

     warning: variable "end_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 601 │   defp maybe_apply_date_range(query, {start_date, end_date}) do
     │                                                   ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:601:51: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.maybe_apply_date_range/2

     warning: variable "user_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 763 │   defp build_user_questions_query(user_id, options \\ []) do
     │                                   ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:763:35: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.build_user_questions_query/2

     warning: variable "start_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 601 │   defp maybe_apply_date_range(query, {start_date, end_date}) do
     │                                       ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:601:39: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.maybe_apply_date_range/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_online_stats(stats_data, options \\ []) do
    │                                       ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/online_stats_repository.ex:35:39: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.OnlineStatsRepository.create_online_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 113 │   def update_ltv_stats(ltv_stats, update_data, options \\ []) do
     │                                                ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/ltv_stats_repository.ex:113:48: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.LtvStatsRepository.update_ltv_stats/3

     warning: variable "status" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 607 │   defp maybe_apply_status_filter(query, status) do
     │                                         ~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:607:41: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.maybe_apply_status_filter/2

     warning: variable "bet_type" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 613 │   defp maybe_apply_bet_type_filter(query, bet_type) do
     │                                           ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:613:43: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.maybe_apply_bet_type_filter/2

     warning: variable "user_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 771 │   defp build_user_codes_query(user_id, options \\ []) do
     │                               ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:771:31: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.build_user_codes_query/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 70 │   def get_online_stats(stats_id, options \\ []) do
    │                                  ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/online_stats_repository.ex:70:34: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.OnlineStatsRepository.get_online_stats/2

     warning: variable "stock_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 626 │   defp maybe_apply_stock_filter(query, stock_id) do
     │                                        ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:626:40: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.maybe_apply_stock_filter/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 148 │   def delete_ltv_stats(ltv_stats, options \\ []) do
     │                                   ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/ltv_stats_repository.ex:148:35: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.LtvStatsRepository.delete_ltv_stats/2

     warning: variable "transaction_type" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 632 │   defp maybe_apply_transaction_type_filter(query, transaction_type) do
     │                                                   ~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:632:51: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.maybe_apply_transaction_type_filter/2

     warning: variable "end_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 655 │   defp apply_communication_filter(query, :date_range, {start_date, end_date}) do
     │                                                                    ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:655:68: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.apply_communication_filter/3

     warning: variable "start_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 292 │       start_date = Date.utc_today() |> Date.add(-days)
     │       ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/coin_stats_repository.ex:292:7: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.CoinStatsRepository.get_economy_health/2

     warning: variable "start_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 655 │   defp apply_communication_filter(query, :date_range, {start_date, end_date}) do
     │                                                        ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:655:56: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.apply_communication_filter/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 113 │   def update_online_stats(online_stats, update_data, options \\ []) do
     │                                                      ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/online_stats_repository.ex:113:54: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.OnlineStatsRepository.update_online_stats/3

     warning: variable "search_term" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 663 │     search_term = "%#{search}%"
     │     ~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:663:5: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.apply_communication_search/2

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 669 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:669:5: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.build_recent_communications_query/1

     warning: variable "communication_type" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 678 │   defp maybe_apply_communication_type_filter(query, communication_type) do
     │                                                     ~~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:678:53: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.maybe_apply_communication_type_filter/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 148 │   def delete_online_stats(online_stats, options \\ []) do
     │                                         ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/online_stats_repository.ex:148:41: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.OnlineStatsRepository.delete_online_stats/2

     warning: default values for the optional arguments in build_user_questions_query/2 are never used
     │
 763 │   defp build_user_questions_query(user_id, options \\ []) do
     │        ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:763:8: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder (module)

     warning: default values for the optional arguments in build_user_codes_query/2 are never used
     │
 771 │   defp build_user_codes_query(user_id, options \\ []) do
     │        ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:771:8: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder (module)

     warning: variable "start_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 297 │       start_date = Date.utc_today() |> Date.add(-days)
     │       ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/ltv_stats_repository.ex:297:7: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.LtvStatsRepository.get_ltv_trend/3

     warning: variable "start_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 296 │       start_date = Date.utc_today() |> Date.add(-days)
     │       ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/online_stats_repository.ex:296:7: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.OnlineStatsRepository.get_peak_stats/2

    warning: unused alias Query
    │
 13 │   alias Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/sensitive_word_repository.ex:13:3

    warning: unused import Ash.Query
    │
 14 │   import Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/sensitive_word_repository.ex:14:3

    warning: module attribute @cache_ttl was set but never used
    │
 20 │   @cache_ttl 300  # 5分钟缓存
    │   ~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/exchange_config_repository.ex:20: RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.ExchangeConfigRepository (module)

    warning: unused alias Query
    │
 16 │   alias Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex:16:3

    warning: unused import Ash.Query
    │
 17 │   import Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex:17:3

    warning: unused alias Query
    │
 13 │   alias Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_question_repository.ex:13:3

    warning: unused import Ash.Query
    │
 14 │   import Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_question_repository.ex:14:3

    warning: unused alias Query
    │
 15 │   alias Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/exchange_config_repository.ex:15:3

    warning: unused import Ash.Query
    │
 16 │   import Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/payment_system/exchange_config_repository.ex:16:3

    warning: unused alias Query
    │
 15 │   alias Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/limited_gift_repository.ex:15:3

    warning: unused alias Query
    │
 13 │   alias Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/verification_code_repository.ex:13:3

    warning: unused alias Query
    │
 13 │   alias Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_tag_repository.ex:13:3

    warning: unused import Ash.Query
    │
 16 │   import Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/limited_gift_repository.ex:16:3

    warning: unused import Ash.Query
    │
 14 │   import Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/verification_code_repository.ex:14:3

    warning: unused import Ash.Query
    │
 14 │   import Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_tag_repository.ex:14:3

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_payment_stats(stats_data, options \\ []) do
    │                                        ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/payment_stats_repository.ex:35:40: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.PaymentStatsRepository.create_payment_stats/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 70 │   def get_payment_stats(stats_id, options \\ []) do
    │                                   ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/payment_stats_repository.ex:70:35: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.PaymentStatsRepository.get_payment_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 113 │   def update_payment_stats(payment_stats, update_data, options \\ []) do
     │                                                        ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/payment_stats_repository.ex:113:56: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.PaymentStatsRepository.update_payment_stats/3

    warning: module attribute @cache_ttl was set but never used
    │
 19 │   @cache_ttl 600_000  # 10分钟缓存
    │   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/channel_stats_repository.ex:19: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.ChannelStatsRepository (module)

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 148 │   def delete_payment_stats(payment_stats, options \\ []) do
     │                                           ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/payment_stats_repository.ex:148:43: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.PaymentStatsRepository.delete_payment_stats/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_retention_stats(stats_data, options \\ []) do
    │                                          ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/retention_stats_repository.ex:35:42: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RetentionStatsRepository.create_retention_stats/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 70 │   def get_retention_stats(stats_id, options \\ []) do
    │                                     ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/retention_stats_repository.ex:70:37: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RetentionStatsRepository.get_retention_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 113 │   def update_retention_stats(retention_stats, update_data, options \\ []) do
     │                                                            ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/retention_stats_repository.ex:113:60: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RetentionStatsRepository.update_retention_stats/3

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_robot_stats(stats_data, options \\ []) do
    │                                      ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/robot_stats_repository.ex:35:38: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RobotStatsRepository.create_robot_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 148 │   def delete_retention_stats(retention_stats, options \\ []) do
     │                                               ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/retention_stats_repository.ex:148:47: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RetentionStatsRepository.delete_retention_stats/2

     warning: variable "start_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 292 │       start_date = Date.utc_today() |> Date.add(-days)
     │       ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/payment_stats_repository.ex:292:7: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.PaymentStatsRepository.get_conversion_stats/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 70 │   def get_robot_stats(stats_id, options \\ []) do
    │                                 ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/robot_stats_repository.ex:70:33: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RobotStatsRepository.get_robot_stats/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_user_stats(stats_data, options \\ []) do
    │                                     ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/user_stats_repository.ex:35:37: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.UserStatsRepository.create_user_stats/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_system_report(report_data, options \\ []) do
    │                                         ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/system_report_repository.ex:35:41: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.SystemReportRepository.create_system_report/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 70 │   def get_user_stats(stats_id, options \\ []) do
    │                                ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/user_stats_repository.ex:70:32: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.UserStatsRepository.get_user_stats/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 70 │   def get_system_report(report_id, options \\ []) do
    │                                    ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/system_report_repository.ex:70:36: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.SystemReportRepository.get_system_report/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 113 │   def update_robot_stats(robot_stats, update_data, options \\ []) do
     │                                                    ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/robot_stats_repository.ex:113:52: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RobotStatsRepository.update_robot_stats/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 113 │   def update_user_stats(user_stats, update_data, options \\ []) do
     │                                                  ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/user_stats_repository.ex:113:50: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.UserStatsRepository.update_user_stats/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 148 │   def delete_robot_stats(robot_stats, options \\ []) do
     │                                       ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/robot_stats_repository.ex:148:39: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RobotStatsRepository.delete_robot_stats/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_admin_user(user_data, options \\ []) do
    │                                    ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/admin_user_repository.ex:35:36: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.AdminUserRepository.create_admin_user/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 114 │   def update_system_report(system_report, update_data, options \\ []) do
     │                                                        ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/system_report_repository.ex:114:56: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.SystemReportRepository.update_system_report/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 148 │   def delete_user_stats(user_stats, options \\ []) do
     │                                     ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/user_stats_repository.ex:148:37: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.UserStatsRepository.delete_user_stats/2

     warning: variable "start_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 297 │       start_date = Date.utc_today() |> Date.add(-days)
     │       ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/retention_stats_repository.ex:297:7: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RetentionStatsRepository.get_retention_trend/3

    warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
    │
 81 │           Logger.warn("⚠️ [管理员用户仓储] 管理员用户不存在: #{user_id}")
    │                  ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/admin_user_repository.ex:81:18: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.AdminUserRepository.get_admin_user/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 69 │   def get_admin_user(user_id, options \\ []) do
    │                               ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/admin_user_repository.ex:69:31: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.AdminUserRepository.get_admin_user/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 149 │   def delete_system_report(system_report, options \\ []) do
     │                                           ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/system_report_repository.ex:149:43: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.SystemReportRepository.delete_system_report/2

    warning: module attribute @cache_ttl was set but never used
    │
 19 │   @cache_ttl 600_000  # 10分钟缓存
    │   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/ltv_stats_repository.ex:19: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.LtvStatsRepository (module)

     warning: variable "start_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 256 │       start_date = Date.utc_today() |> Date.add(-days)
     │       ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/user_stats_repository.ex:256:7: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.UserStatsRepository.get_growth_stats/2

     warning: variable "start_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 292 │       start_date = Date.utc_today() |> Date.add(-days)
     │       ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/robot_stats_repository.ex:292:7: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RobotStatsRepository.get_robot_effect_stats/2

     warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
     │
 308 │           Logger.warn("⚠️ [管理员用户仓储] 用户认证失败: 用户名或密码错误")
     │                  ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/admin_user_repository.ex:308:18: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.AdminUserRepository.authenticate_admin_user/3

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_ip_whitelist(ip_data, options \\ []) do
    │                                    ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/ip_whitelist_repository.ex:35:36: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.IpWhitelistRepository.create_ip_whitelist/2

    warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
    │
 81 │           Logger.warn("⚠️ [IP白名单仓储] IP白名单不存在: #{ip_id}")
    │                  ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/ip_whitelist_repository.ex:81:18: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.IpWhitelistRepository.get_ip_whitelist/2

    warning: module attribute @cache_ttl was set but never used
    │
 19 │   @cache_ttl 600_000  # 10分钟缓存
    │   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/coin_stats_repository.ex:19: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.CoinStatsRepository (module)

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 69 │   def get_ip_whitelist(ip_id, options \\ []) do
    │                               ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/ip_whitelist_repository.ex:69:31: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.IpWhitelistRepository.get_ip_whitelist/2

     warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
     │
 306 │           Logger.warn("⚠️ [IP白名单仓储] IP地址不在白名单中: #{ip_address}")
     │                  ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/ip_whitelist_repository.ex:306:18: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.IpWhitelistRepository.check_ip_allowed/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 293 │   def check_ip_allowed(ip_address, options \\ []) do
     │                                    ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/ip_whitelist_repository.ex:293:36: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.IpWhitelistRepository.check_ip_allowed/2

    warning: module attribute @cache_ttl was set but never used
    │
 19 │   @cache_ttl 300_000  # 5分钟缓存
    │   ~~~~~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/online_stats_repository.ex:19: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.OnlineStatsRepository (module)

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_operation_log(log_data, options \\ []) do
    │                                      ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/operation_log_repository.ex:35:38: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.OperationLogRepository.create_operation_log/2

    warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
    │
 81 │           Logger.warn("⚠️ [操作日志仓储] 操作日志不存在: #{log_id}")
    │                  ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/operation_log_repository.ex:81:18: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.OperationLogRepository.get_operation_log/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 69 │   def get_operation_log(log_id, options \\ []) do
    │                                 ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/operation_log_repository.ex:69:33: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.OperationLogRepository.get_operation_log/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_permission(permission_data, options \\ []) do
    │                                          ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/permission_repository.ex:35:42: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.PermissionRepository.create_permission/2

    warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
    │
 81 │           Logger.warn("⚠️ [权限仓储] 权限不存在: #{permission_id}")
    │                  ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/permission_repository.ex:81:18: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.PermissionRepository.get_permission/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 69 │   def get_permission(permission_id, options \\ []) do
    │                                     ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/permission_repository.ex:69:37: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.PermissionRepository.get_permission/2

     warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
     │
 119 │           Logger.warn("⚠️ [权限仓储] 权限不存在: #{permission_code}")
     │                  ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/permission_repository.ex:119:18: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.PermissionRepository.get_permission_by_code/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_role(role_data, options \\ []) do
    │                              ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/role_repository.ex:35:30: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.RoleRepository.create_role/2

    warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
    │
 81 │           Logger.warn("⚠️ [角色仓储] 角色不存在: #{role_id}")
    │                  ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/role_repository.ex:81:18: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.RoleRepository.get_role/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 69 │   def get_role(role_id, options \\ []) do
    │                         ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/role_repository.ex:69:25: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.RoleRepository.get_role/2

     warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
     │
 119 │           Logger.warn("⚠️ [角色仓储] 角色不存在: #{role_code}")
     │                  ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/role_repository.ex:119:18: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.RoleRepository.get_role_by_code/2

     warning: variable "today_end" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 336 │       today_end = DateTime.new!(today, ~T[23:59:59])
     │       ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/operation_log_repository.ex:336:7: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.OperationLogRepository.get_operation_log_stats/1

     warning: variable "today_start" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 335 │       today_start = DateTime.new!(today, ~T[00:00:00])
     │       ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/operation_log_repository.ex:335:7: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.OperationLogRepository.get_operation_log_stats/1

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 326 │   def get_operation_log_stats(options \\ []) do
     │                               ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/operation_log_repository.ex:326:31: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.OperationLogRepository.get_operation_log_stats/1

     warning: variable "end_datetime" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 408 │          {:ok, end_datetime} <- parse_date(end_date) do
     │                ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/operation_log_repository.ex:408:16: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.OperationLogRepository.maybe_filter_by_date_range/2

     warning: variable "start_datetime" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 407 │     with {:ok, start_datetime} <- parse_date(start_date),
     │                ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/operation_log_repository.ex:407:16: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.OperationLogRepository.maybe_filter_by_date_range/2

    warning: variable "yesterday" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 91 │       yesterday = Date.add(today, -1)
    │       ~
    │
    └─ lib/racing_game/live/admin_panel/query_builders/teen/data_statistics_query_builder.ex:91:7: RacingGame.Live.AdminPanel.QueryBuilders.Teen.DataStatisticsQueryBuilder.get_realtime_dashboard/1

    warning: unused alias RaceResult
    │
 13 │   alias RacingGame.{Race, Bet, Stock, StockHolding, StockTransaction, RaceResult, SystemCommunication, SystemCommunicationRead}
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:13:3

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 32 │   def create_settlement(settlement_data, options \\ []) do
    │                                          ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/share_settlement_repository.ex:32:42: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.ShareSettlementRepository.create_settlement/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 58 │   def get_settlement(settlement_id, options \\ []) do
    │                                     ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/share_settlement_repository.ex:58:37: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.ShareSettlementRepository.get_settlement/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 89 │   def update_settlement(settlement_id, update_data, options \\ []) do
    │                                                     ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/share_settlement_repository.ex:89:53: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.ShareSettlementRepository.update_settlement/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 115 │   def delete_settlement(settlement_id, options \\ []) do
     │                                        ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/share_settlement_repository.ex:115:40: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.ShareSettlementRepository.delete_settlement/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 275 │   def get_user_settlement_stats(user_id, date_range \\ nil, options \\ []) do
     │                                                             ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/share_settlement_repository.ex:275:61: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.ShareSettlementRepository.get_user_settlement_stats/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 304 │   def get_system_settlement_stats(date_range \\ nil, options \\ []) do
     │                                                      ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/share_settlement_repository.ex:304:54: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.ShareSettlementRepository.get_system_settlement_stats/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 32 │   def create_config(config_data, options \\ []) do
    │                                  ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/share_config_repository.ex:32:34: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.ShareConfigRepository.create_config/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 58 │   def get_config(config_id, options \\ []) do
    │                             ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/share_config_repository.ex:58:29: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.ShareConfigRepository.get_config/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 88 │   def get_config_by_key(config_key, options \\ []) do
    │                                     ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/share_config_repository.ex:88:37: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.ShareConfigRepository.get_config_by_key/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 120 │   def update_config(config_id, update_data, options \\ []) do
     │                                             ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/share_config_repository.ex:120:45: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.ShareConfigRepository.update_config/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 146 │   def delete_config(config_id, options \\ []) do
     │                                ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/share_config_repository.ex:146:32: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.ShareConfigRepository.delete_config/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 237 │   def list_active_configs(options \\ []) do
     │                           ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/share_config_repository.ex:237:27: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.ShareConfigRepository.list_active_configs/1

     warning: variable "promoter" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 242 │          {:ok, promoter} <- PromoterRepository.get_promoter(channel.promoter_id),
     │                ~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/promotion_system_service.ex:242:16: RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService.handle_channel_conversion/3

     warning: variable "reason" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 378 │   defp send_promoter_review_notification(promoter, action, reason) do
     │                                                            ~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/promotion_system_service.ex:378:60: RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService.send_promoter_review_notification/3

     warning: variable "channel" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 443 │   defp get_redirect_url(channel) do
     │                         ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/promotion_system_service.ex:443:25: RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService.get_redirect_url/1

     warning: variable "channel" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 451 │   defp calculate_registration_commission(promoter, channel) do
     │                                                    ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/promotion_system_service.ex:451:52: RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService.calculate_registration_commission/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 82 │   def build_promoter_relationship_tree(promoter_id, depth \\ 3, options \\ []) do
    │                                                                 ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/query_builders/teen/promotion_system_query_builder.ex:82:65: RacingGame.Live.AdminPanel.QueryBuilders.Teen.PromotionSystemQueryBuilder.build_promoter_relationship_tree/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 694 │   def system_health_check(options \\ []) do
     │                           ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/promotion_system_service.ex:694:27: RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService.system_health_check/1

     warning: variable "share_type" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 758 │   defp validate_user_share_eligibility(user_id, share_type) do
     │                                                 ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/promotion_system_service.ex:758:49: RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService.validate_user_share_eligibility/2

     warning: variable "user_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 758 │   defp validate_user_share_eligibility(user_id, share_type) do
     │                                        ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/promotion_system_service.ex:758:40: RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService.validate_user_share_eligibility/2

     warning: variable "share_data" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 764 │   defp calculate_share_reward(user_id, share_config, share_data) do
     │                                                      ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/promotion_system_service.ex:764:54: RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService.calculate_share_reward/3

     warning: variable "user_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 764 │   defp calculate_share_reward(user_id, share_config, share_data) do
     │                               ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/promotion_system_service.ex:764:31: RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService.calculate_share_reward/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 628 │   defp get_system_health_indicators(options) do
     │                                     ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/query_builders/teen/promotion_system_query_builder.ex:628:37: RacingGame.Live.AdminPanel.QueryBuilders.Teen.PromotionSystemQueryBuilder.get_system_health_indicators/1

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 32 │   def create_settlement(settlement_data, options \\ []) do
    │                                          ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promotion_settlement_repository.ex:32:42: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionSettlementRepository.create_settlement/2

     warning: variable "settlement" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 779 │     with {:ok, settlement} <- ShareSettlementRepository.get_settlement(settlement_id),
     │                ~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/promotion_system_service.ex:779:16: RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService.process_single_share_settlement/3

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 58 │   def get_settlement(settlement_id, options \\ []) do
    │                                     ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promotion_settlement_repository.ex:58:37: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionSettlementRepository.get_settlement/2

     warning: variable "promoter" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 830 │   defp calculate_promoter_performance_score(promoter, channels, settlement_stats) do
     │                                             ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/promotion_system_service.ex:830:45: RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService.calculate_promoter_performance_score/3

     warning: variable "promoter" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 843 │   defp generate_promoter_recommendations(promoter, channels, settlement_stats) do
     │                                          ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/promotion_system_service.ex:843:42: RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService.generate_promoter_recommendations/3

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 89 │   def update_settlement(settlement_id, update_data, options \\ []) do
    │                                                     ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promotion_settlement_repository.ex:89:53: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionSettlementRepository.update_settlement/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 115 │   def delete_settlement(settlement_id, options \\ []) do
     │                                        ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promotion_settlement_repository.ex:115:40: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionSettlementRepository.delete_settlement/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 242 │   def get_promoter_settlement_stats(promoter_id, date_range \\ nil, options \\ []) do
     │                                                                     ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promotion_settlement_repository.ex:242:69: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionSettlementRepository.get_promoter_settlement_stats/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 271 │   def get_system_settlement_stats(date_range \\ nil, options \\ []) do
     │                                                      ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promotion_settlement_repository.ex:271:54: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionSettlementRepository.get_system_settlement_stats/2

    warning: module attribute @cache_ttl was set but never used
    │
 19 │   @cache_ttl 600_000  # 10分钟缓存
    │   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/payment_stats_repository.ex:19: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.PaymentStatsRepository (module)

    warning: module attribute @cache_ttl was set but never used
    │
 19 │   @cache_ttl 600_000  # 10分钟缓存
    │   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/robot_stats_repository.ex:19: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RobotStatsRepository (module)

    warning: module attribute @cache_prefix was set but never used
    │
 20 │   @cache_prefix "admin_user_repo"
    │   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/admin_user_repository.ex:20: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.AdminUserRepository (module)

    warning: module attribute @cache_ttl was set but never used
    │
 19 │   @cache_ttl 300_000  # 5分钟缓存
    │   ~~~~~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/admin_user_repository.ex:19: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.AdminUserRepository (module)

    warning: unused import Ash.Query
    │
 14 │   import Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/admin_user_repository.ex:14:3

    warning: module attribute @cache_ttl was set but never used
    │
 19 │   @cache_ttl 600_000  # 10分钟缓存
    │   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/retention_stats_repository.ex:19: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RetentionStatsRepository (module)

    warning: module attribute @cache_ttl was set but never used
    │
 19 │   @cache_ttl 600_000  # 10分钟缓存
    │   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/user_stats_repository.ex:19: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.UserStatsRepository (module)

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 32 │   def create_promoter(promoter_data, options \\ []) do
    │                                      ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promoter_repository.ex:32:38: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromoterRepository.create_promoter/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 58 │   def get_promoter(promoter_id, options \\ []) do
    │                                 ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promoter_repository.ex:58:33: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromoterRepository.get_promoter/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 32 │   def create_channel(channel_data, options \\ []) do
    │                                    ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promotion_channel_repository.ex:32:36: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionChannelRepository.create_channel/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 88 │   def get_promoter_by_user_id(user_id, options \\ []) do
    │                                        ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promoter_repository.ex:88:40: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromoterRepository.get_promoter_by_user_id/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 119 │   def get_promoter_by_code(promoter_code, options \\ []) do
     │                                           ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promoter_repository.ex:119:43: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromoterRepository.get_promoter_by_code/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 58 │   def get_channel(channel_id, options \\ []) do
    │                               ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promotion_channel_repository.ex:58:31: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionChannelRepository.get_channel/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 151 │   def update_promoter(promoter_id, update_data, options \\ []) do
     │                                                 ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promoter_repository.ex:151:49: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromoterRepository.update_promoter/3

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 89 │   def update_channel(channel_id, update_data, options \\ []) do
    │                                               ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promotion_channel_repository.ex:89:47: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionChannelRepository.update_channel/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 177 │   def delete_promoter(promoter_id, options \\ []) do
     │                                    ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promoter_repository.ex:177:36: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromoterRepository.delete_promoter/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 115 │   def delete_channel(channel_id, options \\ []) do
     │                                  ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promotion_channel_repository.ex:115:34: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionChannelRepository.delete_channel/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 205 │   def approve_promoter(promoter_id, options \\ []) do
     │                                     ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promoter_repository.ex:205:37: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromoterRepository.approve_promoter/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 231 │   def reject_promoter(promoter_id, options \\ []) do
     │                                    ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promoter_repository.ex:231:36: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromoterRepository.reject_promoter/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 244 │   def increment_channel_clicks(channel_id, click_count \\ 1, options \\ []) do
     │                                                              ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promotion_channel_repository.ex:244:62: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionChannelRepository.increment_channel_clicks/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 272 │   def increment_channel_registers(channel_id, register_count \\ 1, options \\ []) do
     │                                                                    ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promotion_channel_repository.ex:272:68: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionChannelRepository.increment_channel_registers/3

    warning: module attribute @cache_ttl was set but never used
    │
 19 │   @cache_ttl 600_000  # 10分钟缓存
    │   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/system_report_repository.ex:19: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.SystemReportRepository (module)

     warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
     │
 133 │             Logger.warn("⚠️ [数据统计服务] 报告保存失败，但报告生成成功: #{inspect(save_error)}")
     │                    ~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:133:20: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.generate_comprehensive_report/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 264 │   def analyze_user_activity_participation(user_id, options \\ []) do
     │                                                    ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/query_builders/teen/activity_system_query_builder.ex:264:52: RacingGame.Live.AdminPanel.QueryBuilders.Teen.ActivitySystemQueryBuilder.analyze_user_activity_participation/2

     warning: variable "config" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 441 │   defp process_report_data(raw_data, config, _options) do
     │                                      ~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:441:38: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.process_report_data/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 711 │   defp sync_online_statistics(sync_config, options) do
     │                                            ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:711:44: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.sync_online_statistics/2

     warning: variable "sync_config" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 711 │   defp sync_online_statistics(sync_config, options) do
     │                               ~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:711:31: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.sync_online_statistics/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 726 │   defp sync_user_statistics(sync_config, options) do
     │                                          ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:726:42: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.sync_user_statistics/2

     warning: variable "sync_config" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 726 │   defp sync_user_statistics(sync_config, options) do
     │                             ~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:726:29: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.sync_user_statistics/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 739 │   defp sync_payment_statistics(sync_config, options) do
     │                                             ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:739:45: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.sync_payment_statistics/2

     warning: variable "sync_config" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 739 │   defp sync_payment_statistics(sync_config, options) do
     │                                ~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:739:32: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.sync_payment_statistics/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 752 │   defp sync_channel_statistics(sync_config, options) do
     │                                             ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:752:45: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.sync_channel_statistics/2

     warning: variable "sync_config" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 752 │   defp sync_channel_statistics(sync_config, options) do
     │                                ~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:752:32: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.sync_channel_statistics/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 765 │   defp sync_retention_statistics(sync_config, options) do
     │                                               ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:765:47: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.sync_retention_statistics/2

     warning: variable "sync_config" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 765 │   defp sync_retention_statistics(sync_config, options) do
     │                                  ~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:765:34: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.sync_retention_statistics/2

    warning: module attribute @cache_prefix was set but never used
    │
 20 │   @cache_prefix "ip_whitelist_repo"
    │   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/ip_whitelist_repository.ex:20: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.IpWhitelistRepository (module)

    warning: module attribute @cache_ttl was set but never used
    │
 19 │   @cache_ttl 600_000  # 10分钟缓存（IP白名单变化较少）
    │   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/ip_whitelist_repository.ex:19: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.IpWhitelistRepository (module)

     warning: variable "validation_config" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 790 │   defp check_data_completeness(validation_config, options) do
     │                                ~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:790:32: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.check_data_completeness/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 601 │   defp do_generate_cdkey_batch(batch_data, count, options) do
     │                                                   ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/activity_system_service.ex:601:51: RacingGame.Live.AdminPanel.Services.Teen.ActivitySystemService.do_generate_cdkey_batch/3

     warning: variable "validation_config" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 826 │   defp check_data_consistency(validation_config, options) do
     │                               ~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:826:31: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.check_data_consistency/2

     warning: variable "validation_config" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 857 │   defp check_data_accuracy(validation_config, options) do
     │                            ~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:857:28: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.check_data_accuracy/2

     warning: variable "validation_config" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 888 │   defp check_data_timeliness(validation_config, options) do
     │                              ~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:888:30: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.check_data_timeliness/2

     warning: do not use "length(stats) > 0" to check if a list is not empty since length always traverses the whole list. Prefer to pattern match on a non-empty list, such as [_ | _], or use "stats != []" as a guard
     │
 966 │       {:ok, stats} when length(stats) > 0 -> :ok
     │                                       ~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:966:39

     warning: do not use "length(stats) > 0" to check if a list is not empty since length always traverses the whole list. Prefer to pattern match on a non-empty list, such as [_ | _], or use "stats != []" as a guard
     │
 973 │       {:ok, stats} when length(stats) > 0 -> :ok
     │                                       ~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:973:39

     warning: do not use "length(stats) > 0" to check if a list is not empty since length always traverses the whole list. Prefer to pattern match on a non-empty list, such as [_ | _], or use "stats != []" as a guard
     │
 980 │       {:ok, stats} when length(stats) > 0 -> :ok
     │                                       ~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:980:39

     warning: do not use "length(stats) > 0" to check if a list is not empty since length always traverses the whole list. Prefer to pattern match on a non-empty list, such as [_ | _], or use "stats != []" as a guard
     │
 987 │       {:ok, stats} when length(stats) > 0 -> :ok
     │                                       ~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:987:39

     warning: do not use "length(stats) > 0" to check if a list is not empty since length always traverses the whole list. Prefer to pattern match on a non-empty list, such as [_ | _], or use "stats != []" as a guard
     │
 994 │       {:ok, stats} when length(stats) > 0 -> :ok
     │                                       ~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:994:39

      warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
      │
 1090 │   defp create_repair_plan(issue_analysis, config, options) do
      │                                                   ~~~~~~~
      │
      └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:1090:51: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.create_repair_plan/3

      warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
      │
 1381 │   defp create_data_backup(repair_plan, options) do
      │                                        ~~~~~~~
      │
      └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:1381:40: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.create_data_backup/2

      warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
      │
 1451 │   defp validate_repair_results(repair_results, repair_plan, options) do
      │                                                             ~~~~~~~
      │
      └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:1451:61: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.validate_repair_results/3

      warning: variable "repair_plan" is unused (if the variable is not meant to be used, prefix it with an underscore)
      │
 1451 │   defp validate_repair_results(repair_results, repair_plan, options) do
      │                                                ~~~~~~~~~~~
      │
      └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:1451:48: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.validate_repair_results/3

     warning: variable "error" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 106 │   defp handle_save_exception(socket, error) do
     │                                      ~~~~~
     │
     └─ lib/racing_game/live/admin_panel/handlers/data_event_handler.ex:106:38: RacingGame.Live.AdminPanel.Handlers.DataEventHandler.handle_save_exception/2

     warning: default values for the optional arguments in truncate_text/2 are never used
     │
 503 │   defp truncate_text(text, max_length \\ @max_content_preview_length) do
     │        ~
     │
     └─ lib/racing_game/live/admin_panel/handlers/data_event_handler.ex:503:8: RacingGame.Live.AdminPanel.Handlers.DataEventHandler (module)

    warning: module attribute @cache_prefix was set but never used
    │
 20 │   @cache_prefix "operation_log_repo"
    │   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/operation_log_repository.ex:20: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.OperationLogRepository (module)

    warning: module attribute @cache_ttl was set but never used
    │
 19 │   @cache_ttl 180_000  # 3分钟缓存（日志数据实时性要求高）
    │   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/operation_log_repository.ex:19: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.OperationLogRepository (module)

    warning: unused import Ash.Query
    │
 14 │   import Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/ip_whitelist_repository.ex:14:3

    warning: module attribute @cache_prefix was set but never used
    │
 20 │   @cache_prefix "permission_repo"
    │   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/permission_repository.ex:20: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.PermissionRepository (module)

    warning: module attribute @cache_ttl was set but never used
    │
 19 │   @cache_ttl 900_000  # 15分钟缓存（权限变化较少）
    │   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/permission_repository.ex:19: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.PermissionRepository (module)

    warning: module attribute @cache_prefix was set but never used
    │
 20 │   @cache_prefix "role_repo"
    │   ~~~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/role_repository.ex:20: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.RoleRepository (module)

    warning: module attribute @cache_ttl was set but never used
    │
 19 │   @cache_ttl 600_000  # 10分钟缓存（角色变化较少）
    │   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/role_repository.ex:19: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.RoleRepository (module)

    warning: unused import Ash.Query
    │
 14 │   import Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/operation_log_repository.ex:14:3

    warning: unused import Ash.Query
    │
 14 │   import Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/permission_repository.ex:14:3

    warning: unused import Ash.Query
    │
 14 │   import Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/system_settings/role_repository.ex:14:3

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def get_available_payment_methods(user_info, amount, options \\ []) do
    │                                                        ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/query_builders/teen/payment_system_query_builder.ex:35:56: RacingGame.Live.AdminPanel.QueryBuilders.Teen.PaymentSystemQueryBuilder.get_available_payment_methods/3

    warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
    │
 99 │           Logger.warn("⚠️ [支付系统查询构建器] 没有找到适合的网关")
    │                  ~
    │
    └─ lib/racing_game/live/admin_panel/query_builders/teen/payment_system_query_builder.ex:99:18: RacingGame.Live.AdminPanel.QueryBuilders.Teen.PaymentSystemQueryBuilder.select_optimal_gateway/3

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 82 │   def select_optimal_gateway(payment_type, amount, options \\ []) do
    │                                                    ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/query_builders/teen/payment_system_query_builder.ex:82:52: RacingGame.Live.AdminPanel.QueryBuilders.Teen.PaymentSystemQueryBuilder.select_optimal_gateway/3

     warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
     │
 148 │           Logger.warn("⚠️ [支付系统查询构建器] 没有找到适合的兑换配置")
     │                  ~
     │
     └─ lib/racing_game/live/admin_panel/query_builders/teen/payment_system_query_builder.ex:148:18: RacingGame.Live.AdminPanel.QueryBuilders.Teen.PaymentSystemQueryBuilder.get_user_exchange_config/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 128 │   def get_user_exchange_config(user_info, exchange_type, options \\ []) do
     │                                                          ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/query_builders/teen/payment_system_query_builder.ex:128:58: RacingGame.Live.AdminPanel.QueryBuilders.Teen.PaymentSystemQueryBuilder.get_user_exchange_config/3

     warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
     │
 198 │             Logger.warn("⚠️ [支付系统查询构建器] 没有找到支付配置")
     │                    ~
     │
     └─ lib/racing_game/live/admin_panel/query_builders/teen/payment_system_query_builder.ex:198:20: RacingGame.Live.AdminPanel.QueryBuilders.Teen.PaymentSystemQueryBuilder.calculate_payment_fees/4

     warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
     │
 202 │             Logger.warn("⚠️ [支付系统查询构建器] 找到多个支付配置: #{length(multiple_configs)}")
     │                    ~
     │
     └─ lib/racing_game/live/admin_panel/query_builders/teen/payment_system_query_builder.ex:202:20: RacingGame.Live.AdminPanel.QueryBuilders.Teen.PaymentSystemQueryBuilder.calculate_payment_fees/4

    warning: module attribute @operation_timeout was set but never used
    │
 32 │   @operation_timeout 5000
    │   ~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/handlers/data_event_handler.ex:32: RacingGame.Live.AdminPanel.Handlers.DataEventHandler (module)

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 178 │   def calculate_payment_fees(gateway_id, payment_type, amount, options \\ []) do
     │                                                                ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/query_builders/teen/payment_system_query_builder.ex:178:64: RacingGame.Live.AdminPanel.QueryBuilders.Teen.PaymentSystemQueryBuilder.calculate_payment_fees/4

     warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
     │
 243 │           Logger.warn("⚠️ [支付系统查询构建器] 兑换配置不存在")
     │                  ~
     │
     └─ lib/racing_game/live/admin_panel/query_builders/teen/payment_system_query_builder.ex:243:18: RacingGame.Live.AdminPanel.QueryBuilders.Teen.PaymentSystemQueryBuilder.calculate_exchange_amount/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 231 │   def calculate_exchange_amount(config_id, amount, options \\ []) do
     │                                                    ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/query_builders/teen/payment_system_query_builder.ex:231:52: RacingGame.Live.AdminPanel.QueryBuilders.Teen.PaymentSystemQueryBuilder.calculate_exchange_amount/3

     warning: variable "date_range" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 271 │   def get_payment_system_stats(date_range \\ nil, options \\ []) do
     │                                ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/query_builders/teen/payment_system_query_builder.ex:271:32: RacingGame.Live.AdminPanel.QueryBuilders.Teen.PaymentSystemQueryBuilder.get_payment_system_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 271 │   def get_payment_system_stats(date_range \\ nil, options \\ []) do
     │                                                   ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/query_builders/teen/payment_system_query_builder.ex:271:51: RacingGame.Live.AdminPanel.QueryBuilders.Teen.PaymentSystemQueryBuilder.get_payment_system_stats/2

     warning: variable "target_gateway" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 498 │   defp validate_gateway_update_data(update_data, target_gateway) do
     │                                                  ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/payment_system_service.ex:498:50: RacingGame.Live.AdminPanel.Services.Teen.PaymentSystemService.validate_gateway_update_data/2

     warning: variable "details" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 796 │   defp log_config_operation(operator_id, operation, config_id, details) do
     │                                                                ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/payment_system_service.ex:796:64: RacingGame.Live.AdminPanel.Services.Teen.PaymentSystemService.log_config_operation/4

     warning: variable "operator_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 796 │   defp log_config_operation(operator_id, operation, config_id, details) do
     │                             ~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/payment_system_service.ex:796:29: RacingGame.Live.AdminPanel.Services.Teen.PaymentSystemService.log_config_operation/4

      warning: variable "details" is unused (if the variable is not meant to be used, prefix it with an underscore)
      │
 1153 │   defp log_exchange_config_operation(operator_id, operation, config_id, details) do
      │                                                                         ~~~~~~~
      │
      └─ lib/racing_game/live/admin_panel/services/teen/payment_system_service.ex:1153:73: RacingGame.Live.AdminPanel.Services.Teen.PaymentSystemService.log_exchange_config_operation/4

      warning: variable "operator_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
      │
 1153 │   defp log_exchange_config_operation(operator_id, operation, config_id, details) do
      │                                      ~~~~~~~~~~~
      │
      └─ lib/racing_game/live/admin_panel/services/teen/payment_system_service.ex:1153:38: RacingGame.Live.AdminPanel.Services.Teen.PaymentSystemService.log_exchange_config_operation/4

      warning: variable "user_info" is unused (if the variable is not meant to be used, prefix it with an underscore)
      │
 1187 │   defp check_daily_exchange_limit(user_info, config) do
      │                                   ~~~~~~~~~
      │
      └─ lib/racing_game/live/admin_panel/services/teen/payment_system_service.ex:1187:35: RacingGame.Live.AdminPanel.Services.Teen.PaymentSystemService.check_daily_exchange_limit/2

      warning: variable "user_info" is unused (if the variable is not meant to be used, prefix it with an underscore)
      │
 1248 │   defp filter_eligible_payment_methods(available_methods, user_info) do
      │                                                           ~~~~~~~~~
      │
      └─ lib/racing_game/live/admin_panel/services/teen/payment_system_service.ex:1248:59: RacingGame.Live.AdminPanel.Services.Teen.PaymentSystemService.filter_eligible_payment_methods/2

      warning: variable "payment_request" is unused (if the variable is not meant to be used, prefix it with an underscore)
      │
 1265 │   defp select_best_payment_method(eligible_methods, payment_request) do
      │                                                     ~~~~~~~~~~~~~~~
      │
      └─ lib/racing_game/live/admin_panel/services/teen/payment_system_service.ex:1265:53: RacingGame.Live.AdminPanel.Services.Teen.PaymentSystemService.select_best_payment_method/2

      warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
      │
 1358 │   def test_payment_gateway_connection(gateway_id, tester_id, options \\ []) do
      │                                                              ~~~~~~~
      │
      └─ lib/racing_game/live/admin_panel/services/teen/payment_system_service.ex:1358:62: RacingGame.Live.AdminPanel.Services.Teen.PaymentSystemService.test_payment_gateway_connection/3

      warning: variable "test_result" is unused (if the variable is not meant to be used, prefix it with an underscore)
      │
 1484 │   defp log_gateway_test_operation(tester_id, gateway_id, test_result) do
      │                                                          ~~~~~~~~~~~
      │
      └─ lib/racing_game/live/admin_panel/services/teen/payment_system_service.ex:1484:58: RacingGame.Live.AdminPanel.Services.Teen.PaymentSystemService.log_gateway_test_operation/3

      warning: variable "tester_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
      │
 1484 │   defp log_gateway_test_operation(tester_id, gateway_id, test_result) do
      │                                   ~~~~~~~~~
      │
      └─ lib/racing_game/live/admin_panel/services/teen/payment_system_service.ex:1484:35: RacingGame.Live.AdminPanel.Services.Teen.PaymentSystemService.log_gateway_test_operation/3

     warning: variable "params" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 158 │   def build_permission_query(:tree, params, options) do
     │                                     ~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/query_builders/teen/system_settings_query_builder.ex:158:37: RacingGame.Live.AdminPanel.QueryBuilders.Teen.SystemSettingsQueryBuilder.build_permission_query/3

     warning: variable "params" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 220 │   def build_operation_log_query(:stats, params, options) do
     │                                         ~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/query_builders/teen/system_settings_query_builder.ex:220:41: RacingGame.Live.AdminPanel.QueryBuilders.Teen.SystemSettingsQueryBuilder.build_operation_log_query/3

     warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
     │
 374 │           Logger.warn("⚠️ [系统设置查询] 用户无角色分配: #{admin_user_id}")
     │                  ~
     │
     └─ lib/racing_game/live/admin_panel/query_builders/teen/system_settings_query_builder.ex:374:18: RacingGame.Live.AdminPanel.QueryBuilders.Teen.SystemSettingsQueryBuilder.build_user_permissions_query/2

     warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
     │
 130 │           Logger.warn("⚠️ [系统设置服务] IP地址不在白名单中: #{ip_address}")
     │                  ~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/system_settings_service.ex:130:18: RacingGame.Live.AdminPanel.Services.Teen.SystemSettingsService.authenticate_admin_user/4

     warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
     │
 135 │           Logger.warn("⚠️ [系统设置服务] 登录尝试次数过多: #{username}")
     │                  ~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/system_settings_service.ex:135:18: RacingGame.Live.AdminPanel.Services.Teen.SystemSettingsService.authenticate_admin_user/4

     warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
     │
 140 │           Logger.warn("⚠️ [系统设置服务] 认证失败: #{username}")
     │                  ~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/system_settings_service.ex:140:18: RacingGame.Live.AdminPanel.Services.Teen.SystemSettingsService.authenticate_admin_user/4

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 217 │   def delete_admin_user(admin_user_id, deleter_id, options \\ []) do
     │                                                    ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/system_settings_service.ex:217:52: RacingGame.Live.AdminPanel.Services.Teen.SystemSettingsService.delete_admin_user/3

     warning: variable "username" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 419 │   defp check_login_attempts(username) do
     │                             ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/system_settings_service.ex:419:29: RacingGame.Live.AdminPanel.Services.Teen.SystemSettingsService.check_login_attempts/1

     warning: variable "username" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 459 │   defp reset_login_attempts(username) do
     │                             ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/system_settings_service.ex:459:29: RacingGame.Live.AdminPanel.Services.Teen.SystemSettingsService.reset_login_attempts/1

     warning: variable "username" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 465 │   defp increment_login_attempts(username) do
     │                                 ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/system_settings_service.ex:465:33: RacingGame.Live.AdminPanel.Services.Teen.SystemSettingsService.increment_login_attempts/1

     warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
     │
 472 │     Logger.warn("🚨 [安全事件] #{event_type}: #{inspect(details)}")
     │            ~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/system_settings_service.ex:472:12: RacingGame.Live.AdminPanel.Services.Teen.SystemSettingsService.log_security_event/2

     warning: do not use "length(users) > 0" to check if a list is not empty since length always traverses the whole list. Prefer to pattern match on a non-empty list, such as [_ | _], or use "users != []" as a guard
     │
 811 │       {:ok, users} when length(users) > 0 -> {:error, :role_in_use}
     │                                       ~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/system_settings_service.ex:811:39

      warning: do not use "length(children) > 0" to check if a list is not empty since length always traverses the whole list. Prefer to pattern match on a non-empty list, such as [_ | _], or use "children != []" as a guard
      │
 1217 │       {:ok, children} when length(children) > 0 -> {:error, :has_child_permissions}
      │                                             ~
      │
      └─ lib/racing_game/live/admin_panel/services/teen/system_settings_service.ex:1217:45

      warning: variable "permission_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
      │
 1223 │   defp check_permission_usage(permission_id) do
      │                               ~~~~~~~~~~~~~
      │
      └─ lib/racing_game/live/admin_panel/services/teen/system_settings_service.ex:1223:31: RacingGame.Live.AdminPanel.Services.Teen.SystemSettingsService.check_permission_usage/1

      warning: variable "target_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
      │
 1343 │   defp verify_updater_permissions(updater_id, target_id, operation) do
      │                                               ~~~~~~~~~
      │
      └─ lib/racing_game/live/admin_panel/services/teen/system_settings_service.ex:1343:47: RacingGame.Live.AdminPanel.Services.Teen.SystemSettingsService.verify_updater_permissions/3

      warning: variable "target_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
      │
 1357 │   defp verify_deleter_permissions(deleter_id, target_id, operation) do
      │                                               ~~~~~~~~~
      │
      └─ lib/racing_game/live/admin_panel/services/teen/system_settings_service.ex:1357:47: RacingGame.Live.AdminPanel.Services.Teen.SystemSettingsService.verify_deleter_permissions/3

      warning: variable "target_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
      │
 1385 │   defp verify_deactivator_permissions(deactivator_id, target_id, operation) do
      │                                                       ~~~~~~~~~
      │
      └─ lib/racing_game/live/admin_panel/services/teen/system_settings_service.ex:1385:55: RacingGame.Live.AdminPanel.Services.Teen.SystemSettingsService.verify_deactivator_permissions/3

      warning: variable "role" is unused (if the variable is not meant to be used, prefix it with an underscore)
      │
 1426 │   defp check_admin_permission(role, operation) do
      │                               ~~~~
      │
      └─ lib/racing_game/live/admin_panel/services/teen/system_settings_service.ex:1426:31: RacingGame.Live.AdminPanel.Services.Teen.SystemSettingsService.check_admin_permission/2

      warning: variable "field" is unused (if the variable is not meant to be used, prefix it with an underscore)
      │
 1510 │   defp validate_update_field(field, value, _target_user) do
      │                              ~~~~~
      │
      └─ lib/racing_game/live/admin_panel/services/teen/system_settings_service.ex:1510:30: RacingGame.Live.AdminPanel.Services.Teen.SystemSettingsService.validate_update_field/3

      warning: variable "update_data" is unused (if the variable is not meant to be used, prefix it with an underscore)
      │
 1515 │   defp verify_update_permissions(updater, target_user, update_data) do
      │                                                        ~~~~~~~~~~~
      │
      └─ lib/racing_game/live/admin_panel/services/teen/system_settings_service.ex:1515:56: RacingGame.Live.AdminPanel.Services.Teen.SystemSettingsService.verify_update_permissions/3

      warning: variable "target_role" is unused (if the variable is not meant to be used, prefix it with an underscore)
      │
 1574 │   defp validate_role_update_data(update_data, target_role) do
      │                                               ~~~~~~~~~~~
      │
      └─ lib/racing_game/live/admin_panel/services/teen/system_settings_service.ex:1574:47: RacingGame.Live.AdminPanel.Services.Teen.SystemSettingsService.validate_role_update_data/2

      warning: variable "target_permission" is unused (if the variable is not meant to be used, prefix it with an underscore)
      │
 1580 │   defp validate_permission_update_data(update_data, target_permission) do
      │                                                     ~~~~~~~~~~~~~~~~~
      │
      └─ lib/racing_game/live/admin_panel/services/teen/system_settings_service.ex:1580:53: RacingGame.Live.AdminPanel.Services.Teen.SystemSettingsService.validate_permission_update_data/2

      warning: variable "target_role" is unused (if the variable is not meant to be used, prefix it with an underscore)
      │
 1586 │   defp verify_role_update_permissions(updater, target_role, update_data) do
      │                                                ~~~~~~~~~~~
      │
      └─ lib/racing_game/live/admin_panel/services/teen/system_settings_service.ex:1586:48: RacingGame.Live.AdminPanel.Services.Teen.SystemSettingsService.verify_role_update_permissions/3

      warning: variable "update_data" is unused (if the variable is not meant to be used, prefix it with an underscore)
      │
 1586 │   defp verify_role_update_permissions(updater, target_role, update_data) do
      │                                                             ~~~~~~~~~~~
      │
      └─ lib/racing_game/live/admin_panel/services/teen/system_settings_service.ex:1586:61: RacingGame.Live.AdminPanel.Services.Teen.SystemSettingsService.verify_role_update_permissions/3

      warning: variable "updater" is unused (if the variable is not meant to be used, prefix it with an underscore)
      │
 1586 │   defp verify_role_update_permissions(updater, target_role, update_data) do
      │                                       ~~~~~~~
      │
      └─ lib/racing_game/live/admin_panel/services/teen/system_settings_service.ex:1586:39: RacingGame.Live.AdminPanel.Services.Teen.SystemSettingsService.verify_role_update_permissions/3

      warning: variable "deleter" is unused (if the variable is not meant to be used, prefix it with an underscore)
      │
 1592 │   defp verify_role_delete_permissions(deleter, target_role) do
      │                                       ~~~~~~~
      │
      └─ lib/racing_game/live/admin_panel/services/teen/system_settings_service.ex:1592:39: RacingGame.Live.AdminPanel.Services.Teen.SystemSettingsService.verify_role_delete_permissions/2

      warning: variable "target_role" is unused (if the variable is not meant to be used, prefix it with an underscore)
      │
 1592 │   defp verify_role_delete_permissions(deleter, target_role) do
      │                                                ~~~~~~~~~~~
      │
      └─ lib/racing_game/live/admin_panel/services/teen/system_settings_service.ex:1592:48: RacingGame.Live.AdminPanel.Services.Teen.SystemSettingsService.verify_role_delete_permissions/2

      warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
      │
 1615 │         Logger.warn("⚠️ [系统设置服务] 角色禁用将影响 #{length(users)} 个用户")
      │                ~
      │
      └─ lib/racing_game/live/admin_panel/services/teen/system_settings_service.ex:1615:16: RacingGame.Live.AdminPanel.Services.Teen.SystemSettingsService.check_role_deactivation_impact/1

    warning: module attribute @max_page_size was set but never used
    │
 28 │   @max_page_size 100
    │   ~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/query_builders/teen/system_settings_query_builder.ex:28: RacingGame.Live.AdminPanel.QueryBuilders.Teen.SystemSettingsQueryBuilder (module)

    warning: module attribute @default_page_size was set but never used
    │
 27 │   @default_page_size 20
    │   ~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/query_builders/teen/system_settings_query_builder.ex:27: RacingGame.Live.AdminPanel.QueryBuilders.Teen.SystemSettingsQueryBuilder (module)

    warning: unused import Ash.Query
    │
 16 │   import Ash.Query
    │   ~
    │
    └─ lib/racing_game/live/admin_panel/query_builders/teen/system_settings_query_builder.ex:16:3

     warning: variable "liquidated_stock_details" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 447 │         liquidated_stock_details = []
     │         ~
     │
     └─ lib/racing_game/racing_game.ex:447:9: RacingGame.liquidate_user_low_price_stocks/4

    warning: unused import Backpex.Router
    │
  8 │   import Backpex.Router
    │   ~
    │
    └─ lib/cypridina_web/router.ex:8:3

    warning: module attribute @exchange_types was set but never used
    │
 39 │   @exchange_types %{
    │   ~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/services/teen/payment_system_service.ex:39: RacingGame.Live.AdminPanel.Services.Teen.PaymentSystemService (module)

    warning: module attribute @cache_ttl was set but never used
    │
 27 │   @cache_ttl 300  # 5分钟缓存
    │   ~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/services/teen/payment_system_service.ex:27: RacingGame.Live.AdminPanel.Services.Teen.PaymentSystemService (module)

    warning: unused alias UserCache
    │
 26 │   alias Cypridina.UserCache
    │   ~
    │
    └─ lib/cypridina/accounts.ex:26:3

    warning: module attribute @login_lockout_duration was set but never used
    │
 32 │   @login_lockout_duration 3600  # 1小时
    │   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/services/teen/system_settings_service.ex:32: RacingGame.Live.AdminPanel.Services.Teen.SystemSettingsService (module)

    warning: module attribute @max_login_attempts was set but never used
    │
 31 │   @max_login_attempts 5
    │   ~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/services/teen/system_settings_service.ex:31: RacingGame.Live.AdminPanel.Services.Teen.SystemSettingsService (module)

Compiling lib/teen/resources/activity_system/limited_gift.ex (it's taking more than 10s)
Compiling lib/teen/resources/activity_system/reward_multiplier.ex (it's taking more than 10s)
Compiling lib/teen/resources/activity_system/level_reward.ex (it's taking more than 10s)
     warning: Money.multiply/2 is undefined or private. Did you mean:

           * mult/2

     │
 260 │       total_amount: Money.multiply(price, quantity)
     │                           ~
     │
     └─ lib/racing_game/live/admin_panel/services/racing_game/racing_game_service.ex:260:27: RacingGame.Live.AdminPanel.Services.RacingGame.RacingGameService.execute_stock_transaction/6
     └─ lib/racing_game/live/admin_panel/services/racing_game/racing_game_service.ex:591:28: RacingGame.Live.AdminPanel.Services.RacingGame.RacingGameService.determine_bet_outcome/2

    warning: comparison with structs found:

        start_time > DateTime.utc_now()

    given types:

        dynamic() > dynamic(%DateTime{})

    where "start_time" was given the type:

        # type: dynamic()
        # from: lib/racing_game/live/admin_panel/components/system_management/system_maintenance_component.ex:72:28
        {:ok, start_time} <- parse_datetime_local(start)

    Comparison operators (>, <, >=, <=, min, and max) perform structural and not semantic comparison. Comparing with a struct won't give meaningful results. Structs that can be compared typically define a compare/2 function within their modules that can be used for semantic comparison.

    typing violation found at:
    │
 75 │          true <- start_time > DateTime.utc_now() do
    │                             ~
    │
    └─ lib/racing_game/live/admin_panel/components/system_management/system_maintenance_component.ex:75:29: RacingGame.Live.AdminPanel.SystemMaintenanceComponent.handle_event/3

     warning: RacingGame.Live.AdminPanel.Utils.NotificationHelper.send_welcome_email/1 is undefined or private
     │
 487 │       NotificationHelper.send_welcome_email(user)
     │                          ~
     │
     └─ lib/racing_game/live/admin_panel/services/accounts/user_service.ex:487:26: RacingGame.Live.AdminPanel.Services.Accounts.UserService.send_registration_notifications/2

     warning: RacingGame.Bet.update/2 is undefined or private
     │
 362 │     case Bet.update(bet, params) do
     │              ~
     │
     └─ lib/racing_game/live/admin_panel/services/bet_service.ex:362:14: RacingGame.Live.AdminPanel.Services.BetService.update_bet_record/2

     warning: RacingGame.Live.AdminPanel.Repositories.Accounts.UserRepository.get_user_by_client_uniq_id/1 is undefined or private. Did you mean:

           * get_user_by_id/1
           * get_user_by_id/2
           * get_user_by_numeric_id/1
           * get_user_by_numeric_id/2

     │
 511 │     case UserRepository.get_user_by_client_uniq_id(client_uniq_id) do
     │                         ~
     │
     └─ lib/racing_game/live/admin_panel/services/accounts/user_service.ex:511:25: RacingGame.Live.AdminPanel.Services.Accounts.UserService.find_user_by_credentials/1

    warning: the following clause will never match:

        {:ok, result}

    because it attempts to match on the result of:

        Cypridina.Accounts.add_points(agent_id, amount,
          transaction_type: :refund_income,
          description:
            <<"收到下线退费: ",
              String.Chars.to_string(
                case extra_data["refund_reason"] do
                  x when x === false or x === nil -> "无原因"
                  x -> x
                end
              )::binary>>,
          metadata: %{
            "refund_reason" => extra_data["refund_reason"],
            "subordinate_id" => user_id,
            "subordinate_username" => get_username(user_id),
            "original_request_type" => "refund",
            "income_source" => "subordinate_refund"
          }
        )

    which has type:

        dynamic(%{points: term()} or {:error, term()})

    typing violation found at:
    │
 80 │           {:ok, result} ->
    │           ~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/changes/process_approved_refund.ex:80: RacingGame.Changes.ProcessApprovedRefund.add_points_to_agent/3

    warning: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.CustomerChatRepository.assign_to_customer_service/3 is undefined or private
    │
 79 │          {:ok, updated_chat} <- CustomerChatRepository.assign_to_customer_service(chat_id, customer_service_id, options[:notes]) do
    │                                                        ~
    │
    └─ lib/racing_game/live/admin_panel/services/customer_service_service.ex:79:56: RacingGame.Live.AdminPanel.Services.CustomerServiceService.assign_chat_to_customer_service/3

     warning: Ash.DataLayer.transaction/1 is undefined or private. Did you mean:

           * transaction/2
           * transaction/3
           * transaction/4

     │
 231 │     Ash.DataLayer.transaction(fn ->
     │                   ~
     │
     └─ lib/racing_game/live/admin_panel/services/customer_service_service.ex:231:19: RacingGame.Live.AdminPanel.Services.CustomerServiceService.batch_audit_orders/4

     warning: the following clause will never match:

         {:error, _reason}

     because it attempts to match on the result of:

         find_best_staff_for_question(question, [])

     which has type:

         dynamic({:ok, %{id: binary(), name: binary(), workload: integer()}})

     typing violation found at:
     │
 637 │       {:error, _reason} ->
     │       ~~~~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/customer_service_service.ex:637: RacingGame.Live.AdminPanel.Services.CustomerServiceService.auto_assign_question/1

     warning: the following clause will never match:

         {:ok, user}

     because it attempts to match on the result of:

         get_user_by_id(user_id)

     which has type:

         dynamic({:error, :not_implemented})

     typing violation found at:
     │
 168 │       {:ok, user} ->
     │       ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/components/user_management/user_management_component.ex:168: RacingGame.Live.AdminPanel.UserManagementComponent.show_edit_modal/2

     warning: Logger.error/1 is undefined or private. However, there is a macro with the same name and arity. Be sure to require Logger if you intend to invoke this macro
     │
 344 │               Logger.error("拒绝退费请求失败: #{inspect(error)}")
     │                      ~
     │
     └─ lib/racing_game/live/admin_panel/components/user_management/subordinate_management_component.ex:344:22: RacingGame.Live.AdminPanel.SubordinateManagementComponent.handle_event/3

     warning: the following clause will never match:

         {:error, reason}

     because it attempts to match on the result of:

         force_end_race_with_ranking(race, final_ranking)

     which has type:

         dynamic({:ok, term()})

     typing violation found at:
     │
 593 │           {:error, reason} ->
     │           ~~~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/race_control_live.ex:593: RacingGame.Live.RaceControlLive.handle_event/3

     warning: Ash.Query.filter/2 is undefined or private. However, there is a macro with the same name and arity. Be sure to require Ash.Query if you intend to invoke this macro
     │
 429 │          |> Ash.Query.filter(expr(id in ^user_ids))
     │                       ~
     │
     └─ lib/cypridina/accounts/user_cache.ex:429:23: Cypridina.UserCache.load_and_cache_users/1

     warning: the following clause will never match:

         {:error, reason}

     because it attempts to match on the result of:

         reset_animal_prices_to_default(state)

     which has type:

         dynamic({:ok, %{..., reset_bet_amount_map: true}})

     typing violation found at:
     │
 278 │       {:error, reason} ->
     │       ~~~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/race_controller.ex:278: RacingGame.RaceController.handle_call/3

     warning: Cypridina.Accounts.read/1 is undefined or private. Did you mean:

           * create_admin/1

     │
 431 │          |> Cypridina.Accounts.read() do
     │                                ~
     │
     └─ lib/cypridina/accounts/user_cache.ex:431:32: Cypridina.UserCache.load_and_cache_users/1

     warning: undefined attribute "bg_color" for component CypridinaWeb.Components.AdminButtonGroup.admin_button/1
     │
 321 │                   bg_color="#dc2626"
     │                   ~~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/components/system_management/system_maintenance_component.ex:321: (file)

     warning: undefined attribute "bg_color" for component CypridinaWeb.Components.AdminButtonGroup.admin_button/1
     │
 328 │                   bg_color="#16a34a"
     │                   ~~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/components/system_management/system_maintenance_component.ex:328: (file)

     warning: missing required attribute "confirm_message" for component CypridinaWeb.Components.AdminButtonGroup.danger_button/1
     │
 425 │                   <AdminButtonGroup.danger_button
     │                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/components/system_management/system_maintenance_component.ex:425: (file)

     warning: undefined attribute "bg_color" for component CypridinaWeb.Components.AdminButtonGroup.admin_button/1
     │
 294 │                   bg_color="#2563eb"
     │                   ~~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/components/system_management/system_maintenance_component.ex:294: (file)

     warning: Phoenix.Component.live_flash/2 is deprecated. Use Phoenix.Flash.get/2 in Phoenix v1.7+
     │
 100 │     <%= if live_flash(@flash, :info) do %>
     │            ~
     │
     └─ lib/racing_game/live/admin_panel_live.html.heex:100:12: RacingGame.Live.AdminPanelLive.render/1
     └─ lib/racing_game/live/admin_panel_live.html.heex:103:13: RacingGame.Live.AdminPanelLive.render/1
     └─ lib/racing_game/live/admin_panel_live.html.heex:110:12: RacingGame.Live.AdminPanelLive.render/1
     └─ lib/racing_game/live/admin_panel_live.html.heex:113:13: RacingGame.Live.AdminPanelLive.render/1

     warning: undefined attribute "bg_color" for component CypridinaWeb.Components.AdminButtonGroup.admin_button/1
     │
 356 │                 bg_color="#16a34a"
     │                 ~~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/components/system_management/system_maintenance_component.ex:356: (file)

     warning: undefined attribute "bg_color" for component CypridinaWeb.Components.AdminButtonGroup.admin_button/1
     │
 401 │                 bg_color="#ea580c"
     │                 ~~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/components/system_management/system_maintenance_component.ex:401: (file)

     warning: undefined attribute "bg_color" for component CypridinaWeb.Components.AdminButtonGroup.admin_button/1
     │
 595 │             bg_color="#3b82f6"
     │             ~~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/components/user_management/user_management_component.ex:595: (file)

     warning: Cypridina.Accounts.game_bet/3 is undefined or private
     │
 134 │       case Cypridina.Accounts.game_bet(player.user_id, amount, opts) do
     │                               ~
     │
     └─ lib/teen/game_system/player_data_builder.ex:134:31: Cypridina.Teen.GameSystem.PlayerData.game_bet/3

     warning: Cypridina.Accounts.game_win/3 is undefined or private
     │
 156 │       case Cypridina.Accounts.game_win(player.user_id, amount, opts) do
     │                               ~
     │
     └─ lib/teen/game_system/player_data_builder.ex:156:31: Cypridina.Teen.GameSystem.PlayerData.game_win/3

     warning: this clause in cond will never match:

         case not is_active do
           false -> false
           true -> has_pending_transfers?(account.id)
         end

     since it has type:

         dynamic(false)

     where "account" was given the type:

         # type: dynamic()
         # from: lib/racing_game/live/admin_panel/services/ledger/ledger_service.ex:372:39
         account

     where "is_active" was given the type:

         # type: dynamic()
         # from: lib/racing_game/live/admin_panel/services/ledger/ledger_service.ex:372:48
         is_active

     typing violation found at:
     │
 376 │       not is_active and has_pending_transfers?(account.id) ->
     │                                                            ~
     │
     └─ lib/racing_game/live/admin_panel/services/ledger/ledger_service.ex:376:60: RacingGame.Live.AdminPanel.Services.Ledger.LedgerService.validate_account_status_change/2

     warning: incompatible types given to Money.negative?/1:

         Money.negative?(new_balance)

     given types:

         -dynamic(
           {:error, {ArgumentError, binary()}} or
             {:ok, %Money{amount: term(), currency: term(), format_options: term()}}
         )-

     but expected one of:

         dynamic(%{..., currency: term()})

     where "new_balance" was given the type:

         # type: dynamic({:error, {ArgumentError, binary()}} or {:ok, %Money{}})
         # from: lib/racing_game/live/admin_panel/services/ledger/ledger_service.ex:451:19
         new_balance = Money.add(current_balance, adjustment_amount)

     typing violation found at:
     │
 452 │       if Money.negative?(new_balance) do
     │                ~
     │
     └─ lib/racing_game/live/admin_panel/services/ledger/ledger_service.ex:452:16: RacingGame.Live.AdminPanel.Services.Ledger.LedgerService.validate_balance_adjustment/2

    warning: no route path for CypridinaWeb.Router matches "/admin_panel/#{menu_item.page}"
    │
 39 │             navigate={~p"/admin_panel/#{menu_item.page}"}
    │             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel_live.html.heex:39: RacingGame.Live.AdminPanelLive.render/1

    warning: no route path for CypridinaWeb.Router matches "/admin_panel/#{child.page}"
    │
 64 │                     navigate={~p"/admin_panel/#{child.page}"}
    │                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel_live.html.heex:64: RacingGame.Live.AdminPanelLive.render/1

     warning: undefined attribute "bg_color" for component CypridinaWeb.Components.AdminButtonGroup.admin_button/1
     │
 186 │             bg_color="#2563eb"
     │             ~~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/utils/form_example_component.ex:186: (file)

     warning: undefined attribute "bg_color" for component CypridinaWeb.Components.AdminButtonGroup.admin_button/1
     │
 195 │             bg_color="#16a34a"
     │             ~~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/utils/form_example_component.ex:195: (file)

    warning: no route path for CypridinaWeb.Router matches "/backpex_admin/agent_relationships"
    │
 93 │         navigate={~p"/backpex_admin/agent_relationships"}
    │         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/cypridina_web/components/layouts/admin.html.heex:93: CypridinaWeb.Layouts.admin/1

    warning: no route path for CypridinaWeb.Router matches "/backpex_admin/user_assets"
    │
 84 │           navigate={~p"/backpex_admin/user_assets"}
    │           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/cypridina_web/components/layouts/admin.html.heex:84: CypridinaWeb.Layouts.admin/1

    warning: no route path for CypridinaWeb.Router matches "/backpex_admin/users"
    │
 75 │         navigate={~p"/backpex_admin/users"}
    │         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/cypridina_web/components/layouts/admin.html.heex:75: CypridinaWeb.Layouts.admin/1

     warning: undefined attribute "bg_color" for component CypridinaWeb.Components.AdminButtonGroup.admin_button/1
     │
 223 │                     bg_color="#dc2626"
     │                     ~~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/components/system_management/system_monitoring_component.ex:223: (file)

     warning: undefined attribute "text" for component CypridinaWeb.Components.AdminButtonGroup.refresh_button/1
     │
 164 │             text="立即刷新"
     │             ~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/components/system_management/system_monitoring_component.ex:164: (file)

     warning: the following clause will never match:

         {:user_ids, []}

     because it attempts to match on the result of:

         get_accessible_user_ids(user)

     which has type:

         dynamic({:all} or {:user_ids, non_empty_list(term(), term())})

     typing violation found at:
     │
 110 │         {:user_ids, []} ->
     │         ~~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/application/permission_filter.ex:110: RacingGame.Live.AdminPanel.PermissionFilter.apply_user_filter/3

     warning: RacingGame.Stock.update/2 is undefined or private
     │
 380 │     case Stock.update(stock, params) do
     │                ~
     │
     └─ lib/racing_game/live/admin_panel/services/stock_service.ex:380:16: RacingGame.Live.AdminPanel.Services.StockService.update_stock_record/2

     warning: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.AdminUserRepository.activate_admin_user/2 is undefined or private. Did you mean:

           * create_admin_user/1
           * create_admin_user/2
           * get_admin_user/1
           * get_admin_user/2

     │
 271 │            {:ok, activated_user} <- AdminUserRepository.activate_admin_user(admin_user_id, options),
     │                                                         ~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/system_settings_service.ex:271:57: RacingGame.Live.AdminPanel.Services.Teen.SystemSettingsService.activate_admin_user/3

     warning: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.AdminUserRepository.deactivate_admin_user/2 is undefined or private. Did you mean:

           * authenticate_admin_user/2
           * create_admin_user/1
           * create_admin_user/2
           * delete_admin_user/1
           * delete_admin_user/2

     │
 316 │            {:ok, deactivated_user} <- AdminUserRepository.deactivate_admin_user(admin_user_id, options),
     │                                                           ~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/system_settings_service.ex:316:59: RacingGame.Live.AdminPanel.Services.Teen.SystemSettingsService.deactivate_admin_user/4

     warning: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.AdminUserRepository.get_admin_user_by_username/1 is undefined or private. Did you mean:

           * get_admin_user/1
           * get_admin_user/2

     │
 369 │     case AdminUserRepository.get_admin_user_by_username(username) do
     │                              ~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/system_settings_service.ex:369:30: RacingGame.Live.AdminPanel.Services.Teen.SystemSettingsService.check_username_uniqueness/1

     warning: RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.AdminUserRepository.get_admin_user_by_email/1 is undefined or private. Did you mean:

           * get_admin_user/1
           * get_admin_user/2

     │
 378 │     case AdminUserRepository.get_admin_user_by_email(email) do
     │                              ~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/system_settings_service.ex:378:30: RacingGame.Live.AdminPanel.Services.Teen.SystemSettingsService.check_email_uniqueness/1

     warning: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromoterRepository.increment_promoter_invites/3 is undefined or private
     │
 244 │          {:ok, updated_promoter} <- PromoterRepository.increment_promoter_invites(channel.promoter_id, 1, options) do
     │                                                        ~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/promotion_system_service.ex:244:56: RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService.handle_channel_conversion/3

     warning: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromoterRepository.increment_promoter_commission/3 is undefined or private
     │
 484 │       PromoterRepository.increment_promoter_commission(settlement.promoter_id, settlement.commission_amount, options)
     │                          ~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/promotion_system_service.ex:484:26: RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService.process_single_settlement/3

warning: Resource Teen.ActivitySystem.RewardMultiplier is not present in any known Ash.Domain module.

Domain modules checked: [Teen.ActivitySystem, RacingGame, Cypridina.Accounts, Cypridina.Ledger, Teen.CustomerService, Teen.PaymentSystem, Teen.BanSystem, Teen.GameManagement, Teen.ActivitySystem, Teen.Statistics, Teen.SystemSettings, Teen.PromotionSystem]

We check the following configuration for domain modules:

   config :cypridina, ash_domains: [Teen.ActivitySystem, RacingGame, Cypridina.Accounts, Cypridina.Ledger, Teen.CustomerService, Teen.PaymentSystem, Teen.BanSystem, Teen.GameManagement, Teen.ActivitySystem, Teen.Statistics, Teen.SystemSettings, Teen.PromotionSystem]

To resolve this warning, do one of the following.

1. Add the resource to one of your configured domain modules.
2. Add the option `validate_domain_inclusion?: false` to `use Ash.Resource`
3. Configure all resources not to warn, with `config :ash, :validate_domain_resource_inclusion?, false`

  lib/teen/resources/activity_system/reward_multiplier.ex:1: Teen.ActivitySystem.RewardMultiplier.__verify_spark_dsl__/1
  (elixir 1.18.4) lib/enum.ex:987: Enum."-each/2-lists^foreach/1-0-"/2
  (elixir 1.18.4) lib/module/parallel_checker.ex:244: Module.ParallelChecker.check_module/3
  (elixir 1.18.4) lib/module/parallel_checker.ex:90: anonymous fn/7 in Module.ParallelChecker.inner_spawn/6

warning: Resource Teen.ActivitySystem.LimitedGift is not present in any known Ash.Domain module.

Domain modules checked: [Teen.ActivitySystem, RacingGame, Cypridina.Accounts, Cypridina.Ledger, Teen.CustomerService, Teen.PaymentSystem, Teen.BanSystem, Teen.GameManagement, Teen.ActivitySystem, Teen.Statistics, Teen.SystemSettings, Teen.PromotionSystem]

We check the following configuration for domain modules:

   config :cypridina, ash_domains: [Teen.ActivitySystem, RacingGame, Cypridina.Accounts, Cypridina.Ledger, Teen.CustomerService, Teen.PaymentSystem, Teen.BanSystem, Teen.GameManagement, Teen.ActivitySystem, Teen.Statistics, Teen.SystemSettings, Teen.PromotionSystem]

To resolve this warning, do one of the following.

1. Add the resource to one of your configured domain modules.
2. Add the option `validate_domain_inclusion?: false` to `use Ash.Resource`
3. Configure all resources not to warn, with `config :ash, :validate_domain_resource_inclusion?, false`

  lib/teen/resources/activity_system/limited_gift.ex:1: Teen.ActivitySystem.LimitedGift.__verify_spark_dsl__/1
  (elixir 1.18.4) lib/enum.ex:987: Enum."-each/2-lists^foreach/1-0-"/2
  (elixir 1.18.4) lib/module/parallel_checker.ex:244: Module.ParallelChecker.check_module/3
  (elixir 1.18.4) lib/module/parallel_checker.ex:90: anonymous fn/7 in Module.ParallelChecker.inner_spawn/6

warning: Resource Teen.ActivitySystem.BankruptcyAssist is not present in any known Ash.Domain module.

Domain modules checked: [Teen.ActivitySystem, RacingGame, Cypridina.Accounts, Cypridina.Ledger, Teen.CustomerService, Teen.PaymentSystem, Teen.BanSystem, Teen.GameManagement, Teen.ActivitySystem, Teen.Statistics, Teen.SystemSettings, Teen.PromotionSystem]

We check the following configuration for domain modules:

   config :cypridina, ash_domains: [Teen.ActivitySystem, RacingGame, Cypridina.Accounts, Cypridina.Ledger, Teen.CustomerService, Teen.PaymentSystem, Teen.BanSystem, Teen.GameManagement, Teen.ActivitySystem, Teen.Statistics, Teen.SystemSettings, Teen.PromotionSystem]

To resolve this warning, do one of the following.

1. Add the resource to one of your configured domain modules.
2. Add the option `validate_domain_inclusion?: false` to `use Ash.Resource`
3. Configure all resources not to warn, with `config :ash, :validate_domain_resource_inclusion?, false`

  lib/teen/resources/activity_system/bankruptcy_assist.ex:1: Teen.ActivitySystem.BankruptcyAssist.__verify_spark_dsl__/1
  (elixir 1.18.4) lib/enum.ex:987: Enum."-each/2-lists^foreach/1-0-"/2
  (elixir 1.18.4) lib/module/parallel_checker.ex:244: Module.ParallelChecker.check_module/3
  (elixir 1.18.4) lib/module/parallel_checker.ex:90: anonymous fn/7 in Module.ParallelChecker.inner_spawn/6

warning: Resource Teen.ActivitySystem.LevelReward is not present in any known Ash.Domain module.

Domain modules checked: [Teen.ActivitySystem, RacingGame, Cypridina.Accounts, Cypridina.Ledger, Teen.CustomerService, Teen.PaymentSystem, Teen.BanSystem, Teen.GameManagement, Teen.ActivitySystem, Teen.Statistics, Teen.SystemSettings, Teen.PromotionSystem]

We check the following configuration for domain modules:

   config :cypridina, ash_domains: [Teen.ActivitySystem, RacingGame, Cypridina.Accounts, Cypridina.Ledger, Teen.CustomerService, Teen.PaymentSystem, Teen.BanSystem, Teen.GameManagement, Teen.ActivitySystem, Teen.Statistics, Teen.SystemSettings, Teen.PromotionSystem]

To resolve this warning, do one of the following.

1. Add the resource to one of your configured domain modules.
2. Add the option `validate_domain_inclusion?: false` to `use Ash.Resource`
3. Configure all resources not to warn, with `config :ash, :validate_domain_resource_inclusion?, false`

  lib/teen/resources/activity_system/level_reward.ex:1: Teen.ActivitySystem.LevelReward.__verify_spark_dsl__/1
  (elixir 1.18.4) lib/enum.ex:987: Enum."-each/2-lists^foreach/1-0-"/2
  (elixir 1.18.4) lib/module/parallel_checker.ex:244: Module.ParallelChecker.check_module/3
  (elixir 1.18.4) lib/module/parallel_checker.ex:90: anonymous fn/7 in Module.ParallelChecker.inner_spawn/6

warning: Resource Teen.ActivitySystem.FreeCash is not present in any known Ash.Domain module.

Domain modules checked: [Teen.ActivitySystem, RacingGame, Cypridina.Accounts, Cypridina.Ledger, Teen.CustomerService, Teen.PaymentSystem, Teen.BanSystem, Teen.GameManagement, Teen.ActivitySystem, Teen.Statistics, Teen.SystemSettings, Teen.PromotionSystem]

We check the following configuration for domain modules:

   config :cypridina, ash_domains: [Teen.ActivitySystem, RacingGame, Cypridina.Accounts, Cypridina.Ledger, Teen.CustomerService, Teen.PaymentSystem, Teen.BanSystem, Teen.GameManagement, Teen.ActivitySystem, Teen.Statistics, Teen.SystemSettings, Teen.PromotionSystem]

To resolve this warning, do one of the following.

1. Add the resource to one of your configured domain modules.
2. Add the option `validate_domain_inclusion?: false` to `use Ash.Resource`
3. Configure all resources not to warn, with `config :ash, :validate_domain_resource_inclusion?, false`

  lib/teen/resources/activity_system/free_cash.ex:1: Teen.ActivitySystem.FreeCash.__verify_spark_dsl__/1
  (elixir 1.18.4) lib/enum.ex:987: Enum."-each/2-lists^foreach/1-0-"/2
  (elixir 1.18.4) lib/module/parallel_checker.ex:244: Module.ParallelChecker.check_module/3
  (elixir 1.18.4) lib/module/parallel_checker.ex:90: anonymous fn/7 in Module.ParallelChecker.inner_spawn/6

warning: Resource Teen.ActivitySystem.TaskLevel is not present in any known Ash.Domain module.

Domain modules checked: [Teen.ActivitySystem, RacingGame, Cypridina.Accounts, Cypridina.Ledger, Teen.CustomerService, Teen.PaymentSystem, Teen.BanSystem, Teen.GameManagement, Teen.ActivitySystem, Teen.Statistics, Teen.SystemSettings, Teen.PromotionSystem]

We check the following configuration for domain modules:

   config :cypridina, ash_domains: [Teen.ActivitySystem, RacingGame, Cypridina.Accounts, Cypridina.Ledger, Teen.CustomerService, Teen.PaymentSystem, Teen.BanSystem, Teen.GameManagement, Teen.ActivitySystem, Teen.Statistics, Teen.SystemSettings, Teen.PromotionSystem]

To resolve this warning, do one of the following.

1. Add the resource to one of your configured domain modules.
2. Add the option `validate_domain_inclusion?: false` to `use Ash.Resource`
3. Configure all resources not to warn, with `config :ash, :validate_domain_resource_inclusion?, false`

  lib/teen/resources/activity_system/task_level.ex:1: Teen.ActivitySystem.TaskLevel.__verify_spark_dsl__/1
  (elixir 1.18.4) lib/enum.ex:987: Enum."-each/2-lists^foreach/1-0-"/2
  (elixir 1.18.4) lib/module/parallel_checker.ex:244: Module.ParallelChecker.check_module/3
  (elixir 1.18.4) lib/module/parallel_checker.ex:90: anonymous fn/7 in Module.ParallelChecker.inner_spawn/6

warning: Resource Teen.ActivitySystem.FreeBonus is not present in any known Ash.Domain module.

Domain modules checked: [Teen.ActivitySystem, RacingGame, Cypridina.Accounts, Cypridina.Ledger, Teen.CustomerService, Teen.PaymentSystem, Teen.BanSystem, Teen.GameManagement, Teen.ActivitySystem, Teen.Statistics, Teen.SystemSettings, Teen.PromotionSystem]

We check the following configuration for domain modules:

   config :cypridina, ash_domains: [Teen.ActivitySystem, RacingGame, Cypridina.Accounts, Cypridina.Ledger, Teen.CustomerService, Teen.PaymentSystem, Teen.BanSystem, Teen.GameManagement, Teen.ActivitySystem, Teen.Statistics, Teen.SystemSettings, Teen.PromotionSystem]

To resolve this warning, do one of the following.

1. Add the resource to one of your configured domain modules.
2. Add the option `validate_domain_inclusion?: false` to `use Ash.Resource`
3. Configure all resources not to warn, with `config :ash, :validate_domain_resource_inclusion?, false`

  lib/teen/resources/activity_system/free_bonus.ex:1: Teen.ActivitySystem.FreeBonus.__verify_spark_dsl__/1
  (elixir 1.18.4) lib/enum.ex:987: Enum."-each/2-lists^foreach/1-0-"/2
  (elixir 1.18.4) lib/module/parallel_checker.ex:244: Module.ParallelChecker.check_module/3
  (elixir 1.18.4) lib/module/parallel_checker.ex:90: anonymous fn/7 in Module.ParallelChecker.inner_spawn/6

     warning: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionChannelRepository.list_channels/2 is undefined or private. Did you mean:

           * get_channel/1
           * list_channels_by_promoter/1
           * list_channels_by_promoter/2
           * list_channels_by_type/1
           * list_channels_by_type/2

     │
 406 │     with {:ok, channels} <- PromotionChannelRepository.list_channels(filters, options) do
     │                                                        ~
     │
     └─ lib/racing_game/live/admin_panel/query_builders/teen/promotion_system_query_builder.ex:406:56: RacingGame.Live.AdminPanel.QueryBuilders.Teen.PromotionSystemQueryBuilder.get_channel_type_performance/2
     └─ lib/racing_game/live/admin_panel/query_builders/teen/promotion_system_query_builder.ex:428:56: RacingGame.Live.AdminPanel.QueryBuilders.Teen.PromotionSystemQueryBuilder.get_channel_conversion_analysis/2
     └─ lib/racing_game/live/admin_panel/query_builders/teen/promotion_system_query_builder.ex:471:37: RacingGame.Live.AdminPanel.QueryBuilders.Teen.PromotionSystemQueryBuilder.get_top_performing_channels/2
     └─ lib/racing_game/live/admin_panel/query_builders/teen/promotion_system_query_builder.ex:485:56: RacingGame.Live.AdminPanel.QueryBuilders.Teen.PromotionSystemQueryBuilder.get_channel_trend_analysis/2

     warning: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionSettlementRepository.list_settlements/2 is undefined or private. Did you mean:

           * list_pending_settlements/0
           * list_settlements_by_promoter/1
           * list_settlements_by_promoter/2
           * list_settlements_by_status/1
           * list_settlements_by_status/2

     │
 521 │     with {:ok, settlements} <- PromotionSettlementRepository.list_settlements(filters, options) do
     │                                                              ~
     │
     └─ lib/racing_game/live/admin_panel/query_builders/teen/promotion_system_query_builder.ex:521:62: RacingGame.Live.AdminPanel.QueryBuilders.Teen.PromotionSystemQueryBuilder.get_commission_trend_analysis/2
     └─ lib/racing_game/live/admin_panel/query_builders/teen/promotion_system_query_builder.ex:548:62: RacingGame.Live.AdminPanel.QueryBuilders.Teen.PromotionSystemQueryBuilder.get_top_commission_earners/2

     warning: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.ShareSettlementRepository.list_settlements/2 is undefined or private. Did you mean:

           * list_settlements_by_status/1
           * list_settlements_by_type/1
           * list_settlements_by_type/2
           * list_settlements_by_user/1
           * list_settlements_by_user/2

     │
 599 │     with {:ok, settlements} <- ShareSettlementRepository.list_settlements(filters, options) do
     │                                                          ~
     │
     └─ lib/racing_game/live/admin_panel/query_builders/teen/promotion_system_query_builder.ex:599:58: RacingGame.Live.AdminPanel.QueryBuilders.Teen.PromotionSystemQueryBuilder.get_share_settlement_analysis/2

     warning: UserManagementService.create_admin_by_super_admin/2 is undefined (module UserManagementService is not available or is yet to be defined). Make sure the module name is correct and has been specified in full (or that an alias has been defined)
     │
 114 │            UserManagementService.create_admin_by_super_admin(super_admin.id, %{
     │                                  ~
     │
     └─ lib/mix/tasks/cypridina.users.ex:114:34: Mix.Tasks.Cypridina.Users.create_admin/1

     warning: UserManagementService.create_user_by_admin/2 is undefined (module UserManagementService is not available or is yet to be defined). Make sure the module name is correct and has been specified in full (or that an alias has been defined)
     │
 169 │            UserManagementService.create_user_by_admin(admin.id, %{
     │                                  ~
     │
     └─ lib/mix/tasks/cypridina.users.ex:169:34: Mix.Tasks.Cypridina.Users.create_agent/1

    warning: the following clause will never match:

        {:ok, cached_result}

    because it attempts to match on the result of:

        get_from_cache(cache_key)

    which has type:

        dynamic({:error, :not_found})

    typing violation found at:
    │
 76 │       {:ok, cached_result} ->
    │       ~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/system_report_repository.ex:76: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.SystemReportRepository.get_system_report/2

     warning: the following clause will never match:

         {:ok, cached_result}

     because it attempts to match on the result of:

         get_from_cache(cache_key)

     which has type:

         dynamic({:error, :not_found})

     typing violation found at:
     │
 228 │       {:ok, cached_result} ->
     │       ~~~~~~~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/system_report_repository.ex:228: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.SystemReportRepository.get_reports_by_type/2

     warning: Cypridina.Teen.GameSystem.Games.LongHu.LongHuProtocol.handle_join_room/2 is undefined (module Cypridina.Teen.GameSystem.Games.LongHu.LongHuProtocol is not available or is yet to be defined)
     │
 644 │     Cypridina.Teen.GameSystem.Games.LongHu.LongHuProtocol.handle_join_room(message, state)
     │                                                           ~
     │
     └─ lib/teen/protocol/extended_handlers.ex:644:59: Cypridina.Protocol.ExtendedHandlers.handle_longhu_join_room/2

     warning: Cypridina.Teen.GameSystem.Games.LongHu.LongHuProtocol.handle_leave_room/2 is undefined (module Cypridina.Teen.GameSystem.Games.LongHu.LongHuProtocol is not available or is yet to be defined)
     │
 652 │     Cypridina.Teen.GameSystem.Games.LongHu.LongHuProtocol.handle_leave_room(message, state)
     │                                                           ~
     │
     └─ lib/teen/protocol/extended_handlers.ex:652:59: Cypridina.Protocol.ExtendedHandlers.handle_longhu_leave_room/2

     warning: Cypridina.Teen.GameSystem.Games.LongHu.LongHuProtocol.is_longhu_protocol?/2 is undefined (module Cypridina.Teen.GameSystem.Games.LongHu.LongHuProtocol is not available or is yet to be defined)
     │
 659 │     Cypridina.Teen.GameSystem.Games.LongHu.LongHuProtocol.is_longhu_protocol?(main_id, sub_id)
     │                                                           ~
     │
     └─ lib/teen/protocol/extended_handlers.ex:659:59: Cypridina.Protocol.ExtendedHandlers.is_longhu_protocol?/2

    warning: the following clause will never match:

        {:ok, cached_result}

    because it attempts to match on the result of:

        get_from_cache(cache_key)

    which has type:

        dynamic({:error, :not_found})

    typing violation found at:
    │
 76 │       {:ok, cached_result} ->
    │       ~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/user_stats_repository.ex:76: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.UserStatsRepository.get_user_stats/2

    warning: the following clause will never match:

        {:ok, cached_result}

    because it attempts to match on the result of:

        get_from_cache(cache_key)

    which has type:

        dynamic({:error, :not_found})

    typing violation found at:
    │
 76 │       {:ok, cached_result} ->
    │       ~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/retention_stats_repository.ex:76: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RetentionStatsRepository.get_retention_stats/2

    warning: Cypridina.Utils.OperationLogger.extract_client_info/1 is undefined (module Cypridina.Utils.OperationLogger is not available or is yet to be defined)
    │
 61 │     client_info = OperationLogger.extract_client_info(socket)
    │                                   ~
    │
    └─ lib/racing_game/live/admin_panel/components/system_management/system_logs_component.ex:61:35: RacingGame.Live.AdminPanel.SystemLogsComponent.handle_event/3
    └─ lib/racing_game/live/admin_panel/components/system_management/system_logs_component.ex:83:35: RacingGame.Live.AdminPanel.SystemLogsComponent.handle_event/3

     warning: the following clause will never match:

         {:ok, cached_result}

     because it attempts to match on the result of:

         get_from_cache(cache_key)

     which has type:

         dynamic({:error, :not_found})

     typing violation found at:
     │
 227 │       {:ok, cached_result} ->
     │       ~~~~~~~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/retention_stats_repository.ex:227: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RetentionStatsRepository.get_stats_by_cohort/2

    warning: Cypridina.Utils.OperationLogger.log_view/4 is undefined (module Cypridina.Utils.OperationLogger is not available or is yet to be defined)
    │
 62 │     OperationLogger.log_view(
    │                     ~
    │
    └─ lib/racing_game/live/admin_panel/components/system_management/system_logs_component.ex:62:21: RacingGame.Live.AdminPanel.SystemLogsComponent.handle_event/3
    └─ lib/racing_game/live/admin_panel/components/system_management/system_logs_component.ex:84:21: RacingGame.Live.AdminPanel.SystemLogsComponent.handle_event/3

    warning: the following clause will never match:

        {:ok, cached_result}

    because it attempts to match on the result of:

        get_from_cache(cache_key)

    which has type:

        dynamic({:error, :not_found})

    typing violation found at:
    │
 76 │       {:ok, cached_result} ->
    │       ~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/robot_stats_repository.ex:76: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RobotStatsRepository.get_robot_stats/2

    warning: the following clause will never match:

        {:ok, cached_result}

    because it attempts to match on the result of:

        get_from_cache(cache_key)

    which has type:

        dynamic({:error, :not_found})

    typing violation found at:
    │
 76 │       {:ok, cached_result} ->
    │       ~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/payment_stats_repository.ex:76: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.PaymentStatsRepository.get_payment_stats/2

     warning: Ash.Query.aggregate/3 is undefined or private. Did you mean:

           * aggregate/4
           * aggregate/5

     │
 653 │     |> Ash.Query.aggregate(:count, :id)
     │                  ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:653:18: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.build_chat_statistics_query/2
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:660:18: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.build_question_statistics_query/2
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:667:18: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.build_order_statistics_query/2
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:673:18: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.build_word_statistics_query/0
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:679:18: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.build_tag_statistics_query/0
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:686:18: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.build_code_statistics_query/2

    warning: Ash.Query.aggregate/3 is undefined or private. Did you mean:

          * aggregate/4
          * aggregate/5

    │
 81 │         total_races: Race |> Ash.Query.aggregate(:count, :id),
    │                                        ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:81:40: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.build_race_statistics_query/1
    └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:170:38: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.build_bet_statistics_query/1
    └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:258:42: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.build_stock_statistics_query/1
    └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:259:82: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.build_stock_statistics_query/1
    └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:260:51: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.build_stock_statistics_query/1
    └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:261:59: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.build_stock_statistics_query/1
    └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:353:64: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.build_communication_statistics_query/1
    └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:354:104: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.build_communication_statistics_query/1
    └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:357:59: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.build_communication_statistics_query/1
    └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:474:18: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.build_recent_races_query/1
    └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:517:18: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.build_recent_bets_query/1
    └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:554:18: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.build_recent_stock_transactions_query/1
    └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:673:18: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.build_recent_communications_query/1

    warning: the following clause will never match:

        {:ok, cached_result}

    because it attempts to match on the result of:

        get_from_cache(cache_key)

    which has type:

        dynamic({:error, :not_found})

    typing violation found at:
    │
 76 │       {:ok, cached_result} ->
    │       ~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/online_stats_repository.ex:76: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.OnlineStatsRepository.get_online_stats/2

    warning: the following clause will never match:

        {:ok, cached_result}

    because it attempts to match on the result of:

        get_from_cache(cache_key)

    which has type:

        dynamic({:error, :not_found})

    typing violation found at:
    │
 76 │       {:ok, cached_result} ->
    │       ~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/coin_stats_repository.ex:76: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.CoinStatsRepository.get_coin_stats/2

     warning: the following clause will never match:

         {:ok, cached_result}

     because it attempts to match on the result of:

         get_from_cache(cache_key)

     which has type:

         dynamic({:error, :not_found})

     typing violation found at:
     │
 227 │       {:ok, cached_result} ->
     │       ~~~~~~~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/online_stats_repository.ex:227: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.OnlineStatsRepository.get_stats_by_date/2

    warning: the following clause will never match:

        {:ok, cached_result}

    because it attempts to match on the result of:

        get_from_cache(cache_key)

    which has type:

        dynamic({:error, :not_found})

    typing violation found at:
    │
 76 │       {:ok, cached_result} ->
    │       ~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/ltv_stats_repository.ex:76: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.LtvStatsRepository.get_ltv_stats/2

    warning: the following clause will never match:

        {:ok, cached_result}

    because it attempts to match on the result of:

        get_from_cache(cache_key)

    which has type:

        dynamic({:error, :not_found})

    typing violation found at:
    │
 76 │       {:ok, cached_result} ->
    │       ~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/channel_stats_repository.ex:76: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.ChannelStatsRepository.get_channel_stats/2

     warning: the following clause will never match:

         {:ok, cached_result}

     because it attempts to match on the result of:

         get_from_cache(cache_key)

     which has type:

         dynamic({:error, :not_found})

     typing violation found at:
     │
 227 │       {:ok, cached_result} ->
     │       ~~~~~~~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/channel_stats_repository.ex:227: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.ChannelStatsRepository.get_stats_by_channel/2

     warning: the following clause will never match:

         {:ok, cached_result}

     because it attempts to match on the result of:

         get_from_cache(cache_key)

     which has type:

         dynamic({:error, :not_found})

     typing violation found at:
     │
 227 │       {:ok, cached_result} ->
     │       ~~~~~~~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/ltv_stats_repository.ex:227: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.LtvStatsRepository.get_stats_by_cohort/2

    warning: the following clause will never match:

        {:hit, tag}

    because it attempts to match on the result of:

        get_from_cache(cache_key)

    which has type:

        dynamic(:miss)

    typing violation found at:
    │
 46 │       {:hit, tag} ->
    │       ~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_tag_repository.ex:46: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.UserTagRepository.get_tag_by_id/2

    warning: the following clause will never match:

        {:hit, code}

    because it attempts to match on the result of:

        get_from_cache(cache_key)

    which has type:

        dynamic(:miss)

    typing violation found at:
    │
 46 │       {:hit, code} ->
    │       ~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/verification_code_repository.ex:46: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.VerificationCodeRepository.get_code_by_id/2

     warning: the following clause will never match:

         {:hit, result}

     because it attempts to match on the result of:

         get_from_cache(cache_key)

     which has type:

         dynamic(:miss)

     typing violation found at:
     │
 154 │       {:hit, result} ->
     │       ~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_tag_repository.ex:154: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.UserTagRepository.list_tags_paginated/1

     warning: the following clause will never match:

         {:hit, result}

     because it attempts to match on the result of:

         get_from_cache(cache_key)

     which has type:

         dynamic(:miss)

     typing violation found at:
     │
 226 │       {:hit, result} ->
     │       ~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/verification_code_repository.ex:226: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.VerificationCodeRepository.list_codes_paginated/1

     warning: the following clause will never match:

         {:hit, tags}

     because it attempts to match on the result of:

         get_from_cache(cache_key)

     which has type:

         dynamic(:miss)

     typing violation found at:
     │
 179 │       {:hit, tags} ->
     │       ~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_tag_repository.ex:179: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.UserTagRepository.get_user_tags/2

     warning: the following clause will never match:

         {:hit, codes}

     because it attempts to match on the result of:

         get_from_cache(cache_key)

     which has type:

         dynamic(:miss)

     typing violation found at:
     │
 285 │       {:hit, codes} ->
     │       ~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/verification_code_repository.ex:285: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.VerificationCodeRepository.get_unused_codes/1

     warning: Ash.Query.aggregate/3 is undefined or private. Did you mean:

           * aggregate/4
           * aggregate/5

     │
 637 │     case UserTag |> Ash.Query.aggregate(:count, :id) |> Ash.read() do
     │                               ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_tag_repository.ex:637:31: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.UserTagRepository.get_total_tags/0
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_tag_repository.ex:667:23: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.UserTagRepository.get_recent_tags_count/1

     warning: Ash.Query.aggregate/3 is undefined or private. Did you mean:

           * aggregate/4
           * aggregate/5

     │
 347 │       |> Ash.Query.aggregate(:count, :id)
     │                    ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/verification_code_repository.ex:347:20: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.VerificationCodeRepository.check_send_rate_limit/4
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/verification_code_repository.ex:684:40: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.VerificationCodeRepository.get_total_codes/0
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/verification_code_repository.ex:693:23: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.VerificationCodeRepository.get_used_codes_count/0
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/verification_code_repository.ex:703:23: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.VerificationCodeRepository.get_unused_codes_count/0
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/verification_code_repository.ex:714:23: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.VerificationCodeRepository.get_expired_codes_count/0
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/verification_code_repository.ex:726:23: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.VerificationCodeRepository.get_recent_codes_count/1

     warning: Teen.ActivitySystem.LimitedGift.enable/1 is undefined or private
     │
 264 │            {:ok, updated_gift} <- LimitedGift.enable(gift) do
     │                                               ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/limited_gift_repository.ex:264:47: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.LimitedGiftRepository.enable_limited_gift/1

     warning: the following clause will never match:

         [condition]

     because it attempts to match on the result of:

         search_conditions

     which has type:

         empty_list()

     typing violation found at:
     │
 164 │       [condition] -> Ash.Query.filter(query, ^condition)
     │       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/user_query_builder.ex:164: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.UserQueryBuilder.apply_search_across_fields/3

     warning: Teen.ActivitySystem.LimitedGift.disable/1 is undefined or private
     │
 287 │            {:ok, updated_gift} <- LimitedGift.disable(gift) do
     │                                               ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/limited_gift_repository.ex:287:47: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.LimitedGiftRepository.disable_limited_gift/1

    warning: the following clause will never match:

        {:hit, question}

    because it attempts to match on the result of:

        get_from_cache(cache_key)

    which has type:

        dynamic(:miss)

    typing violation found at:
    │
 46 │       {:hit, question} ->
    │       ~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_question_repository.ex:46: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.UserQuestionRepository.get_question_by_id/2

     warning: the following clause will never match:

         {:hit, result}

     because it attempts to match on the result of:

         get_from_cache(cache_key)

     which has type:

         dynamic(:miss)

     typing violation found at:
     │
 179 │       {:hit, result} ->
     │       ~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_question_repository.ex:179: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.UserQuestionRepository.list_questions_paginated/1

     warning: the following clause will never match:

         {:hit, questions}

     because it attempts to match on the result of:

         get_from_cache(cache_key)

     which has type:

         dynamic(:miss)

     typing violation found at:
     │
 239 │       {:hit, questions} ->
     │       ~~~~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_question_repository.ex:239: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.UserQuestionRepository.get_pending_questions/1

     warning: Ash.Query.aggregate/3 is undefined or private. Did you mean:

           * aggregate/4
           * aggregate/5

     │
 587 │     case UserQuestion |> Ash.Query.aggregate(:count, :id) |> Ash.read() do
     │                                    ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_question_repository.ex:587:36: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.UserQuestionRepository.get_total_questions/0
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_question_repository.ex:596:23: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.UserQuestionRepository.get_pending_questions_count/0
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_question_repository.ex:606:23: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.UserQuestionRepository.get_processing_questions_count/0
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_question_repository.ex:616:23: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.UserQuestionRepository.get_completed_questions_count/0
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_question_repository.ex:628:23: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.UserQuestionRepository.get_recent_questions_count/1
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_question_repository.ex:653:23: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.UserQuestionRepository.get_staff_assigned_count/1
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_question_repository.ex:663:23: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.UserQuestionRepository.get_staff_completed_count/1
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/user_question_repository.ex:675:23: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.UserQuestionRepository.get_staff_recent_completed_count/2

    warning: the following clause will never match:

        {:hit, word}

    because it attempts to match on the result of:

        get_from_cache(cache_key)

    which has type:

        dynamic(:miss)

    typing violation found at:
    │
 46 │       {:hit, word} ->
    │       ~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/sensitive_word_repository.ex:46: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.SensitiveWordRepository.get_word_by_id/2

     warning: the following clause will never match:

         {:hit, result}

     because it attempts to match on the result of:

         get_from_cache(cache_key)

     which has type:

         dynamic(:miss)

     typing violation found at:
     │
 204 │       {:hit, result} ->
     │       ~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/sensitive_word_repository.ex:204: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.SensitiveWordRepository.list_words_paginated/1

     warning: the following clause will never match:

         {:hit, words}

     because it attempts to match on the result of:

         get_from_cache(cache_key)

     which has type:

         dynamic(:miss)

     typing violation found at:
     │
 228 │       {:hit, words} ->
     │       ~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/sensitive_word_repository.ex:228: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.SensitiveWordRepository.get_enabled_words/1

     warning: Teen.ActivitySystem.FreeBonus.enable/1 is undefined or private
     │
 235 │            {:ok, updated_bonus} <- FreeBonus.enable(bonus) do
     │                                              ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/free_bonus_repository.ex:235:46: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.FreeBonusRepository.enable_free_bonus/1

     warning: Teen.ActivitySystem.FreeBonus.disable/1 is undefined or private
     │
 258 │            {:ok, updated_bonus} <- FreeBonus.disable(bonus) do
     │                                              ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/free_bonus_repository.ex:258:46: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.FreeBonusRepository.disable_free_bonus/1

     warning: Ash.Query.aggregate/3 is undefined or private. Did you mean:

           * aggregate/4
           * aggregate/5

     │
 640 │     case SensitiveWord |> Ash.Query.aggregate(:count, :id) |> Ash.read() do
     │                                     ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/sensitive_word_repository.ex:640:37: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.SensitiveWordRepository.get_total_words/0
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/sensitive_word_repository.ex:649:23: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.SensitiveWordRepository.get_enabled_words_count/0
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/sensitive_word_repository.ex:659:23: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.SensitiveWordRepository.get_disabled_words_count/0
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/sensitive_word_repository.ex:676:23: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.SensitiveWordRepository.get_recent_words_count/1

     warning: the following clause will never match:

         [condition]

     because it attempts to match on the result of:

         search_conditions

     which has type:

         empty_list()

     typing violation found at:
     │
 176 │       [condition] -> Ash.Query.filter(query, ^condition)
     │       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/communication_query_builder.ex:176: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CommunicationQueryBuilder.apply_search_across_fields/3

     warning: Ash.Query.aggregate/3 is undefined or private. Did you mean:

           * aggregate/4
           * aggregate/5

     │
 505 │     Ash.Query.aggregate(query, aggregate_type, field)
     │               ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:505:15: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_aggregate/4

    warning: the following clause will never match:

        {:ok, user}

    because it attempts to match on the result of:

        get_from_cache(cache_key)

    which has type:

        dynamic(:miss)

    typing violation found at:
    │
 52 │         {:ok, user} -> {:ok, user}
    │         ~~~~~~~~~~~~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/user_repository.ex:52: RacingGame.Live.AdminPanel.Repositories.UserRepository.get_by_id/2

     warning: the following clause will never match:

         {:ok, agent_info}

     because it attempts to match on the result of:

         get_from_cache(cache_key)

     which has type:

         dynamic(:miss)

     typing violation found at:
     │
 190 │         {:ok, agent_info} -> {:ok, agent_info}
     │         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/user_repository.ex:190: RacingGame.Live.AdminPanel.Repositories.UserRepository.get_agent_relationship/2

     warning: Ash.Query.aggregate/3 is undefined or private. Did you mean:

           * aggregate/4
           * aggregate/5

     │
 597 │          |> Ash.Query.aggregate(:count, :id) 
     │                       ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/agent_relationship_repository.ex:597:23: RacingGame.Live.AdminPanel.Repositories.Accounts.AgentRelationshipRepository.get_agent_sub_users_count/1
     └─ lib/racing_game/live/admin_panel/repositories/accounts/agent_relationship_repository.ex:608:23: RacingGame.Live.AdminPanel.Repositories.Accounts.AgentRelationshipRepository.get_active_sub_users_count/1
     └─ lib/racing_game/live/admin_panel/repositories/accounts/agent_relationship_repository.ex:618:23: RacingGame.Live.AdminPanel.Repositories.Accounts.AgentRelationshipRepository.get_direct_sub_users_count/1
     └─ lib/racing_game/live/admin_panel/repositories/accounts/agent_relationship_repository.ex:628:23: RacingGame.Live.AdminPanel.Repositories.Accounts.AgentRelationshipRepository.get_indirect_sub_users_count/1
     └─ lib/racing_game/live/admin_panel/repositories/accounts/agent_relationship_repository.ex:656:23: RacingGame.Live.AdminPanel.Repositories.Accounts.AgentRelationshipRepository.get_recent_additions_count/2
     └─ lib/racing_game/live/admin_panel/repositories/accounts/agent_relationship_repository.ex:664:41: RacingGame.Live.AdminPanel.Repositories.Accounts.AgentRelationshipRepository.get_total_relationships/0
     └─ lib/racing_game/live/admin_panel/repositories/accounts/agent_relationship_repository.ex:673:23: RacingGame.Live.AdminPanel.Repositories.Accounts.AgentRelationshipRepository.get_active_relationships_count/0
     └─ lib/racing_game/live/admin_panel/repositories/accounts/agent_relationship_repository.ex:708:23: RacingGame.Live.AdminPanel.Repositories.Accounts.AgentRelationshipRepository.get_recent_relationships_count/1

     warning: Teen.ActivitySystem.SignInActivity.enable/1 is undefined or private
     │
 244 │            {:ok, updated_activity} <- SignInActivity.enable(activity) do
     │                                                      ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/sign_in_activity_repository.ex:244:54: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.SignInActivityRepository.enable_sign_in_activity/1

     warning: Teen.ActivitySystem.SignInActivity.disable/1 is undefined or private
     │
 267 │            {:ok, updated_activity} <- SignInActivity.disable(activity) do
     │                                                      ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/sign_in_activity_repository.ex:267:54: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.SignInActivityRepository.disable_sign_in_activity/1

    warning: the following clause will never match:

        {:hit, chat}

    because it attempts to match on the result of:

        get_from_cache(cache_key)

    which has type:

        dynamic(:miss)

    typing violation found at:
    │
 46 │       {:hit, chat} ->
    │       ~~~~~~~~~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/customer_chat_repository.ex:46: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.CustomerChatRepository.get_chat_by_id/2

     warning: the following clause will never match:

         {:hit, result}

     because it attempts to match on the result of:

         get_from_cache(cache_key)

     which has type:

         dynamic(:miss)

     typing violation found at:
     │
 156 │       {:hit, result} ->
     │       ~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/customer_chat_repository.ex:156: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.CustomerChatRepository.list_chats_paginated/1

     warning: the following clause will never match:

         {:hit, chats}

     because it attempts to match on the result of:

         get_from_cache(cache_key)

     which has type:

         dynamic(:miss)

     typing violation found at:
     │
 216 │       {:hit, chats} ->
     │       ~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/customer_chat_repository.ex:216: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.CustomerChatRepository.get_pending_chats/1

     warning: Ash.Query.aggregate/3 is undefined or private. Did you mean:

           * aggregate/4
           * aggregate/5

     │
 551 │     case CustomerChat |> Ash.Query.aggregate(:count, :id) |> Ash.read() do
     │                                    ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/customer_chat_repository.ex:551:36: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.CustomerChatRepository.get_total_chats/0
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/customer_chat_repository.ex:560:23: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.CustomerChatRepository.get_pending_chats_count/0
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/customer_chat_repository.ex:570:23: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.CustomerChatRepository.get_processed_chats_count/0
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/customer_chat_repository.ex:582:23: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.CustomerChatRepository.get_recent_chats_count/1
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/customer_chat_repository.ex:602:23: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.CustomerChatRepository.get_customer_service_handled_count/1
     └─ lib/racing_game/live/admin_panel/repositories/teen/customer_service/customer_chat_repository.ex:614:23: RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.CustomerChatRepository.get_customer_service_recent_handled_count/2

** (EXIT from #PID<0.95.0>) an exception was raised:
    ** (RuntimeError) Resource Teen.ActivitySystem.FreeCash declared that its domain is Teen.ActivitySystem, but that
domain does not accept this resource.

The most likely cause for this is missing a call to `resource Teen.ActivitySystem.FreeCash`
in the `resources` block of Teen.ActivitySystem.

        lib/teen/resources/activity_system/free_cash.ex:1: Teen.ActivitySystem.FreeCash.__verify_spark_dsl__/1
        (elixir 1.18.4) lib/enum.ex:987: Enum."-each/2-lists^foreach/1-0-"/2
        (elixir 1.18.4) lib/module/parallel_checker.ex:244: Module.ParallelChecker.check_module/3
        (elixir 1.18.4) lib/module/parallel_checker.ex:90: anonymous fn/7 in Module.ParallelChecker.inner_spawn/6

