# Ash Double Entry 迁移指南

## 概述

本指南说明如何从旧的积分系统迁移到新的 ash_double_entry 复式记账系统。

## 新旧系统对比

### 旧系统
- 使用 `UserAsset.points` 字段存储积分
- 使用 `RacingGame.PointsTransaction` 记录交易
- 积分操作和记录分离

### 新系统
- 使用 ash_double_entry 复式记账系统
- 使用 `Cypridina.Accounts.Transfer` 记录所有转账
- 使用 `Cypridina.Accounts.Balance` 跟踪余额
- 积分操作和记录统一

## API 迁移

### 积分查询

```elixir
# 旧方式
user_asset = UserAsset.get_by_user_id(user_id)
points = user_asset.points

# 新方式
points = Cypridina.Accounts.get_user_points(user_id)
```

### 积分增加

```elixir
# 旧方式
UserAsset.add_points(user_asset, %{amount: amount})
PointsTransaction.create_transaction(%{...})

# 新方式
Cypridina.Accounts.add_points(user_id, amount, [
  transfer_type: :game_reward,
  reason: "游戏奖励",
  reference_type: :game
])
```

### 积分扣除

```elixir
# 旧方式
UserAsset.subtract_points(user_asset, %{amount: amount})
PointsTransaction.create_transaction(%{...})

# 新方式
Cypridina.Accounts.subtract_points(user_id, amount, [
  transfer_type: :game_cost,
  reason: "游戏消费",
  reference_type: :game
])
```

### 积分转账

```elixir
# 旧方式
# 需要手动处理两个账户的增减

# 新方式
Cypridina.Accounts.transfer_points(from_user_id, to_user_id, amount, reason)
```

### 交易历史查询

```elixir
# 旧方式
PointsTransaction
|> Ash.Query.filter(user_id == ^user_id)
|> Ash.read!()

# 新方式
Cypridina.Accounts.get_points_history(user_id, [
  limit: 20,
  offset: 0
])
```

## 转账类型映射

### 旧的 transaction_type -> 新的 transfer_type

```elixir
# 游戏相关
:buy_stock -> :buy_stock
:sell_stock -> :sell_stock
:place_bet -> :place_bet
:win_prize -> :win_prize

# 管理相关
:admin_add -> :points_add
:admin_subtract -> :points_subtract
:manual_add -> :points_add
:manual_subtract -> :points_subtract

# 转账相关
:transfer_in -> :transfer
:transfer_out -> :transfer

# 其他
:commission -> :commission
:refund -> :refund
:system_adjust -> :system_adjust
```

## PlayerData 接口更新

### 积分操作

```elixir
# 获取积分
points = PlayerData.get_points(player)

# 增加积分（会自动同步到数据库）
updated_player = PlayerData.add_points(player, amount)

# 扣除积分（会自动同步到数据库）
updated_player = PlayerData.subtract_points(player, amount)

# 同步积分（从数据库获取最新积分）
updated_player = PlayerData.sync_points(player)
```

## 已完成的迁移

### ✅ 已更新的文件

以下文件已成功迁移到新的账户系统：

#### 管理面板组件
- ✅ `lib/racing_game/live/admin_panel/user_management_component.ex` - 用户积分调整
- ✅ `lib/racing_game/live/admin_panel/profile_component.ex` - 积分查询和退费申请
- ✅ `lib/racing_game/live/admin_panel/subordinate_management_component.ex` - 下级积分查询

#### 游戏系统
- ✅ `lib/teen/game_system/player_data_builder.ex` - 玩家积分操作
- ✅ `lib/teen/game_system/games/longhu/longhu_room.ex` - 龙虎游戏积分（通过 PlayerData）
- ✅ `lib/teen/game_system/games/slot777/slot777_room.ex` - 老虎机游戏积分（通过 PlayerData）
- ✅ `lib/teen/game_system/games/teen_patti/teen_patti_room.ex` - 三张牌游戏积分（通过 PlayerData）

#### 赛车游戏
- ✅ `lib/racing_game/changes/deduct_user_points.ex` - 下注扣除积分
- ✅ `lib/racing_game/game_manager.ex` - 下注和奖励积分操作（已使用新API）

#### 测试文件
- ✅ `test/refund_functionality_test.exs` - 退费功能测试

#### 用户系统
- ✅ `lib/cypridina/accounts/resources/user.ex` - 积分计算字段更新
- ✅ `lib/cypridina/accounts/user_points_calculation.ex` - 新的积分计算模块

## 注意事项

1. **数据迁移**: 如果需要保留历史数据，需要编写数据迁移脚本
2. **测试**: 更新所有相关的测试用例
3. **向后兼容**: 考虑保留一些兼容性接口以便渐进式迁移
4. **性能**: 新系统使用复式记账，查询性能可能有所不同

## 迁移步骤

1. 更新所有积分操作调用
2. 移除旧的 PointsTransaction 引用
3. 更新测试用例
4. 运行完整测试套件
5. 部署前在测试环境验证

## 回滚计划

如果需要回滚到旧系统：

1. 恢复 `RacingGame.PointsTransaction` 资源
2. 恢复旧的积分操作调用
3. 重新部署旧版本代码

## 支持

如有问题，请参考：
- ash_double_entry 官方文档
- 本项目的测试用例
- 相关的代码注释
