defmodule Cypridina.UserCacheTest do
  use ExUnit.Case, async: false
  alias <PERSON><PERSON><PERSON><PERSON>.UserCache

  setup do
    # 确保缓存服务运行
    start_supervised!(UserCache)
    
    # 清理缓存
    UserCache.clear_all()
    
    :ok
  end

  describe "get_user/1" do
    test "handles string user_id" do
      # 这个测试需要实际的用户数据，这里只测试错误情况
      assert {:error, _} = UserCache.get_user("non_existent_user")
    end

    test "handles integer user_id" do
      assert {:error, _} = UserCache.get_user(123)
    end

    test "handles nil user_id" do
      assert {:error, :invalid_user_id} = UserCache.get_user(nil)
    end
  end

  describe "get_user_by_numeric_id/1" do
    test "handles integer numeric_id" do
      assert {:error, _} = UserCache.get_user_by_numeric_id(999999)
    end

    test "handles string numeric_id" do
      assert {:error, _} = UserCache.get_user_by_numeric_id("999999")
    end

    test "handles invalid string" do
      assert {:error, :invalid_numeric_id} = UserCache.get_user_by_numeric_id("invalid")
    end

    test "handles nil" do
      assert {:error, :invalid_numeric_id} = UserCache.get_user_by_numeric_id(nil)
    end
  end

  describe "get_users/1" do
    test "handles empty list" do
      assert [] = UserCache.get_users([])
    end

    test "handles nil" do
      assert [] = UserCache.get_users(nil)
    end

    test "filters invalid ids" do
      # 应该过滤掉无效的ID
      assert [] = UserCache.get_users([nil, "", %{}, []])
    end
  end

  describe "cached?/1" do
    test "returns false for non-existent user" do
      refute UserCache.cached?("non_existent")
    end

    test "handles different input types" do
      refute UserCache.cached?(123)
      refute UserCache.cached?(nil)
    end
  end

  describe "health_check/0" do
    test "returns healthy status when service is running" do
      assert {:ok, %{status: :healthy}} = UserCache.health_check()
    end
  end

  describe "stats/0" do
    test "returns cache statistics" do
      stats = UserCache.stats()
      assert is_map(stats)
      assert Map.has_key?(stats, :cache_size)
      assert Map.has_key?(stats, :hit_rate)
    end
  end

  describe "cache management" do
    test "invalidate_user/1 doesn't crash on non-existent user" do
      assert :ok = UserCache.invalidate_user("non_existent")
    end

    test "clear_all/0 works" do
      assert :ok = UserCache.clear_all()
    end

    test "warm_cache/1 accepts user id list" do
      assert :ok = UserCache.warm_cache(["id1", "id2"])
    end
  end
end
