defmodule RacingGame.Live.AdminPanel.Services.Teen.ActivitySystemService do
  @moduledoc """
  🎯 活动系统服务层

  负责活动系统的业务逻辑协调，包括：
  - 签到活动管理
  - CDKEY活动管理
  - 限时礼包管理
  - Free Bonus管理
  - Free Cash管理
  - 破产补助管理
  - 任务等级管理
  - 等级奖励管理
  - 奖励倍率管理
  - 跨活动业务逻辑协调
  """

  require Logger
  alias RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.{
    SignInActivityRepository,
    CdkeyActivityRepository,
    LimitedGiftRepository,
    FreeBonusRepository,
    ActivitySystemRepositories
  }
  alias RacingGame.Live.AdminPanel.QueryBuilders.Teen.ActivitySystemQueryBuilder

  # ==================== 签到活动管理 ====================

  @doc """
  创建签到活动
  """
  def create_sign_in_activity(activity_data, options \\ []) do
    Logger.info("🎯 [活动系统服务] 创建签到活动: #{inspect(activity_data[:activity_name])}")

    try do
      # 业务规则验证
      with :ok <- validate_sign_in_activity_data(activity_data),
           {:ok, activity} <- SignInActivityRepository.create_sign_in_activity(activity_data, options) do

        Logger.info("✅ [活动系统服务] 签到活动创建成功: #{activity.activity_name}")

        # 可以在这里添加创建后的业务逻辑，如缓存更新、通知发送等
        {:ok, activity}
      else
        {:error, reason} ->
          Logger.error("❌ [活动系统服务] 创建签到活动失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [活动系统服务] 创建签到活动异常: #{inspect(exception)}")
        {:error, :service_error}
    end
  end

  @doc """
  更新签到活动
  """
  def update_sign_in_activity(activity_id, update_data, options \\ []) do
    Logger.info("🎯 [活动系统服务] 更新签到活动: #{activity_id}")

    try do
      with :ok <- validate_sign_in_activity_update_data(update_data),
           {:ok, activity} <- SignInActivityRepository.update_sign_in_activity(activity_id, update_data, options) do

        Logger.info("✅ [活动系统服务] 签到活动更新成功: #{activity.activity_name}")

        # 更新后的业务逻辑处理
        handle_sign_in_activity_update(activity)

        {:ok, activity}
      else
        {:error, reason} ->
          Logger.error("❌ [活动系统服务] 更新签到活动失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [活动系统服务] 更新签到活动异常: #{inspect(exception)}")
        {:error, :service_error}
    end
  end

  @doc """
  删除签到活动
  """
  def delete_sign_in_activity(activity_id) do
    Logger.info("🎯 [活动系统服务] 删除签到活动: #{activity_id}")

    try do
      with {:ok, activity} <- SignInActivityRepository.get_sign_in_activity(activity_id),
           :ok <- validate_can_delete_sign_in_activity(activity),
           {:ok, :deleted} <- SignInActivityRepository.delete_sign_in_activity(activity_id) do

        Logger.info("✅ [活动系统服务] 签到活动删除成功: #{activity_id}")

        # 删除后的清理工作
        handle_sign_in_activity_deletion(activity)

        {:ok, :deleted}
      else
        {:error, reason} ->
          Logger.error("❌ [活动系统服务] 删除签到活动失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [活动系统服务] 删除签到活动异常: #{inspect(exception)}")
        {:error, :service_error}
    end
  end

  # ==================== CDKEY活动管理 ====================

  @doc """
  批量生成CDKEY
  """
  def generate_cdkey_batch(batch_data, count, options \\ []) do
    Logger.info("🎯 [活动系统服务] 批量生成CDKEY: #{inspect(batch_data[:batch_name])}, 数量: #{count}")

    try do
      with :ok <- validate_cdkey_batch_data(batch_data, count),
           {:ok, cdkeys} <- do_generate_cdkey_batch(batch_data, count, options) do

        Logger.info("✅ [活动系统服务] CDKEY批量生成成功: #{length(cdkeys)}个")

        # 生成后的业务逻辑处理
        handle_cdkey_batch_generation(cdkeys, batch_data)

        {:ok, cdkeys}
      else
        {:error, reason} ->
          Logger.error("❌ [活动系统服务] 批量生成CDKEY失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [活动系统服务] 批量生成CDKEY异常: #{inspect(exception)}")
        {:error, :service_error}
    end
  end

  @doc """
  使用CDKEY
  """
  def use_cdkey(cdkey_code, user_id, options \\ []) do
    Logger.info("🎯 [活动系统服务] 使用CDKEY: #{cdkey_code} by #{user_id}")

    try do
      with {:ok, cdkey} <- CdkeyActivityRepository.get_cdkey_by_code(cdkey_code),
           :ok <- validate_can_use_cdkey(cdkey, user_id),
           {:ok, updated_cdkey} <- CdkeyActivityRepository.use_cdkey(cdkey.id, user_id, options) do

        Logger.info("✅ [活动系统服务] CDKEY使用成功: #{cdkey_code}")

        # 使用后的奖励分发和业务逻辑处理
        handle_cdkey_usage(updated_cdkey, user_id)

        {:ok, updated_cdkey}
      else
        {:error, reason} ->
          Logger.error("❌ [活动系统服务] 使用CDKEY失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [活动系统服务] 使用CDKEY异常: #{inspect(exception)}")
        {:error, :service_error}
    end
  end

  # ==================== 限时礼包管理 ====================

  @doc """
  创建限时礼包
  """
  def create_limited_gift(gift_data, options \\ []) do
    Logger.info("🎯 [活动系统服务] 创建限时礼包: #{inspect(gift_data[:gift_name])}")

    try do
      with :ok <- validate_limited_gift_data(gift_data),
           {:ok, gift} <- LimitedGiftRepository.create_limited_gift(gift_data, options) do

        Logger.info("✅ [活动系统服务] 限时礼包创建成功: #{gift.gift_name}")

        # 创建后的业务逻辑处理
        handle_limited_gift_creation(gift)

        {:ok, gift}
      else
        {:error, reason} ->
          Logger.error("❌ [活动系统服务] 创建限时礼包失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [活动系统服务] 创建限时礼包异常: #{inspect(exception)}")
        {:error, :service_error}
    end
  end

  @doc """
  领取限时礼包
  """
  def claim_limited_gift(gift_id, user_id, options \\ []) do
    Logger.info("🎯 [活动系统服务] 领取限时礼包: #{gift_id} by #{user_id}")

    try do
      with {:ok, gift} <- LimitedGiftRepository.get_limited_gift(gift_id),
           :ok <- validate_can_claim_gift(gift, user_id),
           {:ok, claim_result} <- process_gift_claim(gift, user_id, options) do

        Logger.info("✅ [活动系统服务] 限时礼包领取成功: #{gift.gift_name}")

        # 领取后的奖励分发和业务逻辑处理
        handle_gift_claim(gift, user_id, claim_result)

        {:ok, claim_result}
      else
        {:error, reason} ->
          Logger.error("❌ [活动系统服务] 领取限时礼包失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [活动系统服务] 领取限时礼包异常: #{inspect(exception)}")
        {:error, :service_error}
    end
  end

  # ==================== Free Bonus管理 ====================

  @doc """
  创建Free Bonus
  """
  def create_free_bonus(bonus_data, options \\ []) do
    Logger.info("🎯 [活动系统服务] 创建Free Bonus: #{inspect(bonus_data[:bonus_name])}")

    try do
      with :ok <- validate_free_bonus_data(bonus_data),
           {:ok, bonus} <- FreeBonusRepository.create_free_bonus(bonus_data, options) do

        Logger.info("✅ [活动系统服务] Free Bonus创建成功: #{bonus.bonus_name}")

        # 创建后的业务逻辑处理
        handle_free_bonus_creation(bonus)

        {:ok, bonus}
      else
        {:error, reason} ->
          Logger.error("❌ [活动系统服务] 创建Free Bonus失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [活动系统服务] 创建Free Bonus异常: #{inspect(exception)}")
        {:error, :service_error}
    end
  end

  @doc """
  领取Free Bonus
  """
  def claim_free_bonus(bonus_id, user_id, options \\ []) do
    Logger.info("🎯 [活动系统服务] 领取Free Bonus: #{bonus_id} by #{user_id}")

    try do
      with {:ok, bonus} <- FreeBonusRepository.get_free_bonus(bonus_id),
           :ok <- validate_can_claim_bonus(bonus, user_id),
           {:ok, claim_result} <- process_bonus_claim(bonus, user_id, options) do

        Logger.info("✅ [活动系统服务] Free Bonus领取成功: #{bonus.bonus_name}")

        # 领取后的奖励分发和业务逻辑处理
        handle_bonus_claim(bonus, user_id, claim_result)

        {:ok, claim_result}
      else
        {:error, reason} ->
          Logger.error("❌ [活动系统服务] 领取Free Bonus失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [活动系统服务] 领取Free Bonus异常: #{inspect(exception)}")
        {:error, :service_error}
    end
  end

  # ==================== Free Cash管理 ====================

  @doc """
  创建Free Cash
  """
  def create_free_cash(cash_data, options \\ []) do
    Logger.info("🎯 [活动系统服务] 创建Free Cash: #{inspect(cash_data[:cash_name])}")

    try do
      with :ok <- validate_free_cash_data(cash_data),
           {:ok, cash} <- ActivitySystemRepositories.create_free_cash(cash_data, options) do

        Logger.info("✅ [活动系统服务] Free Cash创建成功: #{cash.cash_name}")

        # 创建后的业务逻辑处理
        handle_free_cash_creation(cash)

        {:ok, cash}
      else
        {:error, reason} ->
          Logger.error("❌ [活动系统服务] 创建Free Cash失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [活动系统服务] 创建Free Cash异常: #{inspect(exception)}")
        {:error, :service_error}
    end
  end

  @doc """
  分发Free Cash
  """
  def distribute_free_cash(cash_id, user_id, options \\ []) do
    Logger.info("🎯 [活动系统服务] 分发Free Cash: #{cash_id} to #{user_id}")

    try do
      with {:ok, cash} <- ActivitySystemRepositories.get_free_cash(cash_id),
           :ok <- validate_can_distribute_cash(cash, user_id),
           {:ok, distribution_result} <- process_cash_distribution(cash, user_id, options) do

        Logger.info("✅ [活动系统服务] Free Cash分发成功: #{cash.cash_name}")

        # 分发后的业务逻辑处理
        handle_cash_distribution(cash, user_id, distribution_result)

        {:ok, distribution_result}
      else
        {:error, reason} ->
          Logger.error("❌ [活动系统服务] 分发Free Cash失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [活动系统服务] 分发Free Cash异常: #{inspect(exception)}")
        {:error, :service_error}
    end
  end

  # ==================== 破产补助管理 ====================

  @doc """
  创建破产补助
  """
  def create_bankruptcy_assist(assist_data, options \\ []) do
    Logger.info("🎯 [活动系统服务] 创建破产补助: #{inspect(assist_data[:assist_name])}")

    try do
      with :ok <- validate_bankruptcy_assist_data(assist_data),
           {:ok, assist} <- ActivitySystemRepositories.create_bankruptcy_assist(assist_data, options) do

        Logger.info("✅ [活动系统服务] 破产补助创建成功: #{assist.assist_name}")

        # 创建后的业务逻辑处理
        handle_bankruptcy_assist_creation(assist)

        {:ok, assist}
      else
        {:error, reason} ->
          Logger.error("❌ [活动系统服务] 创建破产补助失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [活动系统服务] 创建破产补助异常: #{inspect(exception)}")
        {:error, :service_error}
    end
  end

  @doc """
  申请破产补助
  """
  def apply_bankruptcy_assist(assist_id, user_id, options \\ []) do
    Logger.info("🎯 [活动系统服务] 申请破产补助: #{assist_id} by #{user_id}")

    try do
      with {:ok, assist} <- ActivitySystemRepositories.get_bankruptcy_assist(assist_id),
           :ok <- validate_can_apply_bankruptcy_assist(assist, user_id),
           {:ok, application_result} <- process_bankruptcy_assist_application(assist, user_id, options) do

        Logger.info("✅ [活动系统服务] 破产补助申请成功: #{assist.assist_name}")

        # 申请后的业务逻辑处理
        handle_bankruptcy_assist_application(assist, user_id, application_result)

        {:ok, application_result}
      else
        {:error, reason} ->
          Logger.error("❌ [活动系统服务] 申请破产补助失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [活动系统服务] 申请破产补助异常: #{inspect(exception)}")
        {:error, :service_error}
    end
  end

  # ==================== 任务等级管理 ====================

  @doc """
  创建任务等级
  """
  def create_task_level(level_data, options \\ []) do
    Logger.info("🎯 [活动系统服务] 创建任务等级: #{inspect(level_data[:level_name])}")

    try do
      with :ok <- validate_task_level_data(level_data),
           {:ok, level} <- ActivitySystemRepositories.create_task_level(level_data, options) do

        Logger.info("✅ [活动系统服务] 任务等级创建成功: #{level.level_name}")

        # 创建后的业务逻辑处理
        handle_task_level_creation(level)

        {:ok, level}
      else
        {:error, reason} ->
          Logger.error("❌ [活动系统服务] 创建任务等级失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [活动系统服务] 创建任务等级异常: #{inspect(exception)}")
        {:error, :service_error}
    end
  end

  # ==================== 等级奖励管理 ====================

  @doc """
  创建等级奖励
  """
  def create_level_reward(reward_data, options \\ []) do
    Logger.info("🎯 [活动系统服务] 创建等级奖励: #{inspect(reward_data[:reward_name])}")

    try do
      with :ok <- validate_level_reward_data(reward_data),
           {:ok, reward} <- ActivitySystemRepositories.create_level_reward(reward_data, options) do

        Logger.info("✅ [活动系统服务] 等级奖励创建成功: #{reward.reward_name}")

        # 创建后的业务逻辑处理
        handle_level_reward_creation(reward)

        {:ok, reward}
      else
        {:error, reason} ->
          Logger.error("❌ [活动系统服务] 创建等级奖励失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [活动系统服务] 创建等级奖励异常: #{inspect(exception)}")
        {:error, :service_error}
    end
  end

  # ==================== 奖励倍率管理 ====================

  @doc """
  创建奖励倍率
  """
  def create_reward_multiplier(multiplier_data, options \\ []) do
    Logger.info("🎯 [活动系统服务] 创建奖励倍率: #{inspect(multiplier_data[:multiplier_name])}")

    try do
      with :ok <- validate_reward_multiplier_data(multiplier_data),
           {:ok, multiplier} <- ActivitySystemRepositories.create_reward_multiplier(multiplier_data, options) do

        Logger.info("✅ [活动系统服务] 奖励倍率创建成功: #{multiplier.multiplier_name}")

        # 创建后的业务逻辑处理
        handle_reward_multiplier_creation(multiplier)

        {:ok, multiplier}
      else
        {:error, reason} ->
          Logger.error("❌ [活动系统服务] 创建奖励倍率失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [活动系统服务] 创建奖励倍率异常: #{inspect(exception)}")
        {:error, :service_error}
    end
  end

  # ==================== 系统统计和监控 ====================

  @doc """
  获取活动系统健康状态
  """
  def get_system_health_status(options \\ []) do
    Logger.info("🎯 [活动系统服务] 获取活动系统健康状态")

    try do
      with {:ok, overview} <- ActivitySystemQueryBuilder.get_activity_system_overview(options) do

        health_status = %{
          overall_status: calculate_overall_health(overview),
          component_status: %{
            sign_in_activities: assess_component_health(overview.sign_in_activities),
            cdkey_activities: assess_component_health(overview.cdkey_activities),
            limited_gifts: assess_component_health(overview.limited_gifts),
            free_bonuses: assess_component_health(overview.free_bonuses),
            free_cashes: assess_component_health(overview.free_cashes),
            bankruptcy_assists: assess_component_health(overview.bankruptcy_assists)
          },
          last_check: DateTime.utc_now()
        }

        Logger.info("✅ [活动系统服务] 活动系统健康状态获取成功")
        {:ok, health_status}
      else
        {:error, reason} ->
          Logger.error("❌ [活动系统服务] 获取活动系统健康状态失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [活动系统服务] 获取活动系统健康状态异常: #{inspect(exception)}")
        {:error, :service_error}
    end
  end

  # ==================== 私有辅助函数 ====================

  # 验证签到活动数据
  defp validate_sign_in_activity_data(activity_data) do
    cond do
      is_nil(activity_data[:activity_name]) or activity_data[:activity_name] == "" ->
        {:error, :activity_name_required}

      is_nil(activity_data[:start_date]) ->
        {:error, :start_date_required}

      is_nil(activity_data[:end_date]) ->
        {:error, :end_date_required}

      DateTime.compare(activity_data[:start_date], activity_data[:end_date]) != :lt ->
        {:error, :invalid_date_range}

      true ->
        :ok
    end
  end

  # 验证签到活动更新数据
  defp validate_sign_in_activity_update_data(_update_data) do
    # 这里可以添加更新数据的验证逻辑
    :ok
  end

  # 验证是否可以删除签到活动
  defp validate_can_delete_sign_in_activity(activity) do
    if activity.status == 1 do
      {:error, :cannot_delete_active_activity}
    else
      :ok
    end
  end

  # 处理签到活动更新后的业务逻辑
  defp handle_sign_in_activity_update(_activity) do
    # 这里可以添加更新后的业务逻辑，如缓存刷新、通知发送等
    :ok
  end

  # 处理签到活动删除后的清理工作
  defp handle_sign_in_activity_deletion(_activity) do
    # 这里可以添加删除后的清理工作，如相关数据清理、缓存清理等
    :ok
  end

  # 验证CDKEY批次数据
  defp validate_cdkey_batch_data(batch_data, count) do
    cond do
      is_nil(batch_data[:batch_name]) or batch_data[:batch_name] == "" ->
        {:error, :batch_name_required}

      count <= 0 or count > 10000 ->
        {:error, :invalid_count}

      true ->
        :ok
    end
  end

  # 执行CDKEY批量生成
  defp do_generate_cdkey_batch(batch_data, count, options) do
    # 这里应该实现实际的批量生成逻辑
    # 由于当前资源定义的限制，这里返回模拟结果
    cdkeys = Enum.map(1..count, fn i ->
      %{
        cdkey: "BATCH#{i}#{:rand.uniform(999999)}",
        batch_name: batch_data[:batch_name],
        status: 0
      }
    end)

    {:ok, cdkeys}
  end

  # 处理CDKEY批次生成后的业务逻辑
  defp handle_cdkey_batch_generation(_cdkeys, _batch_data) do
    # 这里可以添加生成后的业务逻辑，如统计更新、通知发送等
    :ok
  end

  # 验证是否可以使用CDKEY
  defp validate_can_use_cdkey(cdkey, _user_id) do
    cond do
      cdkey.status != 0 ->
        {:error, :cdkey_already_used}

      not is_nil(cdkey.expires_at) and DateTime.compare(DateTime.utc_now(), cdkey.expires_at) == :gt ->
        {:error, :cdkey_expired}

      true ->
        :ok
    end
  end

  # 处理CDKEY使用后的业务逻辑
  defp handle_cdkey_usage(_cdkey, _user_id) do
    # 这里可以添加使用后的业务逻辑，如奖励分发、统计更新等
    :ok
  end

  # 验证限时礼包数据
  defp validate_limited_gift_data(gift_data) do
    cond do
      is_nil(gift_data[:gift_name]) or gift_data[:gift_name] == "" ->
        {:error, :gift_name_required}

      true ->
        :ok
    end
  end

  # 处理限时礼包创建后的业务逻辑
  defp handle_limited_gift_creation(_gift) do
    # 这里可以添加创建后的业务逻辑
    :ok
  end

  # 验证是否可以领取礼包
  defp validate_can_claim_gift(gift, _user_id) do
    if gift.status == 1 do
      :ok
    else
      {:error, :gift_not_active}
    end
  end

  # 处理礼包领取
  defp process_gift_claim(gift, _user_id, _options) do
    # 这里应该实现实际的礼包领取逻辑
    {:ok, %{
      gift_id: gift.id,
      rewards: %{
        coins: gift.coins_reward,
        points: gift.points_reward,
        items: gift.items_reward
      },
      claimed_at: DateTime.utc_now()
    }}
  end

  # 处理礼包领取后的业务逻辑
  defp handle_gift_claim(_gift, _user_id, _claim_result) do
    # 这里可以添加领取后的业务逻辑
    :ok
  end

  # 验证Free Bonus数据
  defp validate_free_bonus_data(bonus_data) do
    cond do
      is_nil(bonus_data[:bonus_name]) or bonus_data[:bonus_name] == "" ->
        {:error, :bonus_name_required}

      true ->
        :ok
    end
  end

  # 处理Free Bonus创建后的业务逻辑
  defp handle_free_bonus_creation(_bonus) do
    # 这里可以添加创建后的业务逻辑
    :ok
  end

  # 验证是否可以领取Bonus
  defp validate_can_claim_bonus(bonus, _user_id) do
    if bonus.status == 1 do
      :ok
    else
      {:error, :bonus_not_active}
    end
  end

  # 处理Bonus领取
  defp process_bonus_claim(bonus, _user_id, _options) do
    # 这里应该实现实际的Bonus领取逻辑
    {:ok, %{
      bonus_id: bonus.id,
      reward_points: bonus.reward_points,
      claimed_at: DateTime.utc_now()
    }}
  end

  # 处理Bonus领取后的业务逻辑
  defp handle_bonus_claim(_bonus, _user_id, _claim_result) do
    # 这里可以添加领取后的业务逻辑
    :ok
  end

  # 计算整体健康状态
  defp calculate_overall_health(overview) do
    # 这里应该实现实际的健康状态计算逻辑
    case overview do
      %{sign_in_activities: {:ok, _}, cdkey_activities: {:ok, _}} ->
        :healthy
      _ ->
        :degraded
    end
  end

  # 评估组件健康状态
  defp assess_component_health({:ok, _stats}) do
    :healthy
  end
  defp assess_component_health({:error, _reason}) do
    :unhealthy
  end

  # ==================== 新增验证函数 ====================

  # 验证Free Cash数据
  defp validate_free_cash_data(cash_data) do
    cond do
      is_nil(cash_data[:cash_name]) or cash_data[:cash_name] == "" ->
        {:error, :cash_name_required}

      is_nil(cash_data[:total_reward_amount]) or Decimal.compare(cash_data[:total_reward_amount], Decimal.new("0")) != :gt ->
        {:error, :invalid_total_reward_amount}

      true ->
        :ok
    end
  end

  # 验证是否可以分发Cash
  defp validate_can_distribute_cash(cash, _user_id) do
    if cash.status == 1 do
      :ok
    else
      {:error, :cash_not_active}
    end
  end

  # 处理Cash分发
  defp process_cash_distribution(cash, _user_id, _options) do
    # 这里应该实现实际的Cash分发逻辑
    amount = case cash.distribution_type do
      "random" ->
        min = Decimal.to_float(cash.min_amount)
        max = Decimal.to_float(cash.max_amount)
        random_amount = min + :rand.uniform() * (max - min)
        Decimal.from_float(random_amount)
      "fixed" ->
        cash.min_amount
      _ ->
        cash.min_amount
    end

    {:ok, %{
      cash_id: cash.id,
      distributed_amount: amount,
      distribution_type: cash.distribution_type,
      distributed_at: DateTime.utc_now()
    }}
  end

  # 处理Free Cash创建后的业务逻辑
  defp handle_free_cash_creation(_cash) do
    # 这里可以添加创建后的业务逻辑
    :ok
  end

  # 处理Cash分发后的业务逻辑
  defp handle_cash_distribution(_cash, _user_id, _distribution_result) do
    # 这里可以添加分发后的业务逻辑
    :ok
  end

  # 验证破产补助数据
  defp validate_bankruptcy_assist_data(assist_data) do
    cond do
      is_nil(assist_data[:assist_name]) or assist_data[:assist_name] == "" ->
        {:error, :assist_name_required}

      is_nil(assist_data[:bankruptcy_threshold]) or Decimal.compare(assist_data[:bankruptcy_threshold], Decimal.new("0")) != :gt ->
        {:error, :invalid_bankruptcy_threshold}

      true ->
        :ok
    end
  end

  # 验证是否可以申请破产补助
  defp validate_can_apply_bankruptcy_assist(assist, _user_id) do
    if assist.status == 1 do
      :ok
    else
      {:error, :assist_not_active}
    end
  end

  # 处理破产补助申请
  defp process_bankruptcy_assist_application(assist, _user_id, _options) do
    # 这里应该实现实际的破产补助申请逻辑
    {:ok, %{
      assist_id: assist.id,
      reward_amount: assist.reward_amount,
      applied_at: DateTime.utc_now()
    }}
  end

  # 处理破产补助创建后的业务逻辑
  defp handle_bankruptcy_assist_creation(_assist) do
    # 这里可以添加创建后的业务逻辑
    :ok
  end

  # 处理破产补助申请后的业务逻辑
  defp handle_bankruptcy_assist_application(_assist, _user_id, _application_result) do
    # 这里可以添加申请后的业务逻辑
    :ok
  end

  # 验证任务等级数据
  defp validate_task_level_data(level_data) do
    cond do
      is_nil(level_data[:level_name]) or level_data[:level_name] == "" ->
        {:error, :level_name_required}

      is_nil(level_data[:level]) or level_data[:level] <= 0 ->
        {:error, :invalid_level}

      true ->
        :ok
    end
  end

  # 处理任务等级创建后的业务逻辑
  defp handle_task_level_creation(_level) do
    # 这里可以添加创建后的业务逻辑
    :ok
  end

  # 验证等级奖励数据
  defp validate_level_reward_data(reward_data) do
    cond do
      is_nil(reward_data[:reward_name]) or reward_data[:reward_name] == "" ->
        {:error, :reward_name_required}

      is_nil(reward_data[:target_level]) or reward_data[:target_level] <= 0 ->
        {:error, :invalid_target_level}

      true ->
        :ok
    end
  end

  # 处理等级奖励创建后的业务逻辑
  defp handle_level_reward_creation(_reward) do
    # 这里可以添加创建后的业务逻辑
    :ok
  end

  # 验证奖励倍率数据
  defp validate_reward_multiplier_data(multiplier_data) do
    cond do
      is_nil(multiplier_data[:multiplier_name]) or multiplier_data[:multiplier_name] == "" ->
        {:error, :multiplier_name_required}

      is_nil(multiplier_data[:multiplier_value]) or Decimal.compare(multiplier_data[:multiplier_value], Decimal.new("1")) != :gt ->
        {:error, :invalid_multiplier_value}

      true ->
        :ok
    end
  end

  # 处理奖励倍率创建后的业务逻辑
  defp handle_reward_multiplier_creation(_multiplier) do
    # 这里可以添加创建后的业务逻辑
    :ok
  end
end
