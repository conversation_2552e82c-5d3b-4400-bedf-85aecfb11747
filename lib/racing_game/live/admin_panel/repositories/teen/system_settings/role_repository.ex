defmodule RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.RoleRepository do
  @moduledoc """
  🎭 角色仓储层

  负责角色的数据访问操作，包括：
  - 角色的CRUD操作
  - 角色权限管理
  - 角色状态管理
  - 角色查询和过滤
  - 角色统计信息
  """

  require Logger
  import Ash.Query

  alias Teen.SystemSettings.Role

  # 缓存配置
  @cache_ttl 600_000  # 10分钟缓存（角色变化较少）
  @cache_prefix "role_repo"

  # ==================== 基础 CRUD 操作 ====================

  @doc """
  创建角色

  ## 参数
  - `role_data` - 角色数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, role}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_role(role_data, options \\ []) do
    Logger.info("🎭 [角色仓储] 创建角色: #{inspect(role_data[:name])}")

    try do
      result = Role
      |> Ash.Changeset.for_create(:create, role_data)
      |> Ash.create()

      case result do
        {:ok, role} ->
          Logger.info("✅ [角色仓储] 角色创建成功: #{role.name}")
          {:ok, role}
        {:error, error} ->
          Logger.error("❌ [角色仓储] 角色创建失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [角色仓储] 创建角色异常: #{inspect(exception)}")
        {:error, :create_role_exception}
    end
  end

  @doc """
  根据ID获取角色

  ## 参数
  - `role_id` - 角色ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, role}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_role(role_id, options \\ []) do
    Logger.info("🎭 [角色仓储] 获取角色: #{role_id}")

    try do
      result = Role
      |> Ash.get(role_id)

      case result do
        {:ok, role} when not is_nil(role) ->
          Logger.info("✅ [角色仓储] 角色获取成功: #{role.name}")
          {:ok, role}
        {:ok, nil} ->
          Logger.warn("⚠️ [角色仓储] 角色不存在: #{role_id}")
          {:error, :role_not_found}
        {:error, error} ->
          Logger.error("❌ [角色仓储] 角色获取失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [角色仓储] 获取角色异常: #{inspect(exception)}")
        {:error, :get_role_exception}
    end
  end

  @doc """
  根据代码获取角色

  ## 参数
  - `role_code` - 角色代码
  - `options` - 选项参数

  ## 返回
  - `{:ok, role}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_role_by_code(role_code, options \\ []) do
    Logger.info("🎭 [角色仓储] 根据代码获取角色: #{role_code}")

    try do
      result = Role
      |> Ash.Query.for_read(:get_by_code, %{code: role_code})
      |> maybe_load_associations(options)
      |> Ash.read_one()

      case result do
        {:ok, role} when not is_nil(role) ->
          Logger.info("✅ [角色仓储] 角色获取成功: #{role.name}")
          {:ok, role}
        {:ok, nil} ->
          Logger.warn("⚠️ [角色仓储] 角色不存在: #{role_code}")
          {:error, :role_not_found}
        {:error, error} ->
          Logger.error("❌ [角色仓储] 角色获取失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [角色仓储] 根据代码获取角色异常: #{inspect(exception)}")
        {:error, :get_role_by_code_exception}
    end
  end

  @doc """
  更新角色

  ## 参数
  - `role_id` - 角色ID
  - `update_data` - 更新数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, role}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_role(role_id, update_data, options \\ []) do
    Logger.info("🎭 [角色仓储] 更新角色: #{role_id}")

    try do
      with {:ok, role} <- get_role(role_id, options) do
        result = role
        |> Ash.Changeset.for_update(:update, update_data)
        |> Ash.update()

        case result do
          {:ok, updated_role} ->
            Logger.info("✅ [角色仓储] 角色更新成功: #{updated_role.name}")
            {:ok, updated_role}
          {:error, error} ->
            Logger.error("❌ [角色仓储] 角色更新失败: #{inspect(error)}")
            {:error, error}
        end
      end
    rescue
      exception ->
        Logger.error("💥 [角色仓储] 更新角色异常: #{inspect(exception)}")
        {:error, :update_role_exception}
    end
  end

  @doc """
  删除角色

  ## 参数
  - `role_id` - 角色ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, role}` - 成功
  - `{:error, reason}` - 失败
  """
  def delete_role(role_id, options \\ []) do
    Logger.info("🎭 [角色仓储] 删除角色: #{role_id}")

    try do
      with {:ok, role} <- get_role(role_id, options) do
        result = role |> Ash.destroy()

        case result do
          :ok ->
            Logger.info("✅ [角色仓储] 角色删除成功: #{role.name}")
            {:ok, role}
          {:error, error} ->
            Logger.error("❌ [角色仓储] 角色删除失败: #{inspect(error)}")
            {:error, error}
        end
      end
    rescue
      exception ->
        Logger.error("💥 [角色仓储] 删除角色异常: #{inspect(exception)}")
        {:error, :delete_role_exception}
    end
  end

  # ==================== 查询操作 ====================

  @doc """
  获取角色列表

  ## 参数
  - `params` - 查询参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, {roles, total_count}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_roles(params \\ %{}, options \\ []) do
    Logger.info("🎭 [角色仓储] 获取角色列表: #{inspect(params)}")

    try do
      query = Role
      |> build_list_query(params)
      |> maybe_load_associations(options)

      # 获取总数
      count_result = query |> Ash.count()
      
      # 获取数据
      list_result = query |> Ash.read()

      case {count_result, list_result} do
        {{:ok, total_count}, {:ok, roles}} ->
          Logger.info("✅ [角色仓储] 角色列表获取成功: #{length(roles)}条记录")
          {:ok, {roles, total_count}}
        {{:error, error}, _} ->
          Logger.error("❌ [角色仓储] 获取角色总数失败: #{inspect(error)}")
          {:error, error}
        {_, {:error, error}} ->
          Logger.error("❌ [角色仓储] 获取角色列表失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [角色仓储] 获取角色列表异常: #{inspect(exception)}")
        {:error, :list_roles_exception}
    end
  end

  @doc """
  根据状态获取角色

  ## 参数
  - `status` - 状态
  - `options` - 选项参数

  ## 返回
  - `{:ok, roles}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_roles_by_status(status, options \\ []) do
    Logger.info("🎭 [角色仓储] 根据状态获取角色: #{status}")

    try do
      result = Role
      |> Ash.Query.for_read(:list_by_status, %{status: status})
      |> maybe_load_associations(options)
      |> Ash.read()

      case result do
        {:ok, roles} ->
          Logger.info("✅ [角色仓储] 状态角色获取成功: #{length(roles)}条记录")
          {:ok, roles}
        {:error, error} ->
          Logger.error("❌ [角色仓储] 状态角色获取失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [角色仓储] 根据状态获取角色异常: #{inspect(exception)}")
        {:error, :list_by_status_exception}
    end
  end

  @doc """
  根据级别获取角色

  ## 参数
  - `level` - 角色级别
  - `options` - 选项参数

  ## 返回
  - `{:ok, roles}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_roles_by_level(level, options \\ []) do
    Logger.info("🎭 [角色仓储] 根据级别获取角色: #{level}")

    try do
      result = Role
      |> Ash.Query.for_read(:list_by_level, %{level: level})
      |> maybe_load_associations(options)
      |> Ash.read()

      case result do
        {:ok, roles} ->
          Logger.info("✅ [角色仓储] 级别角色获取成功: #{length(roles)}条记录")
          {:ok, roles}
        {:error, error} ->
          Logger.error("❌ [角色仓储] 级别角色获取失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [角色仓储] 根据级别获取角色异常: #{inspect(exception)}")
        {:error, :list_by_level_exception}
    end
  end

  # ==================== 状态管理操作 ====================

  @doc """
  激活角色

  ## 参数
  - `role_id` - 角色ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, role}` - 成功
  - `{:error, reason}` - 失败
  """
  def activate_role(role_id, options \\ []) do
    Logger.info("🎭 [角色仓储] 激活角色: #{role_id}")

    try do
      with {:ok, role} <- get_role(role_id, options) do
        result = role
        |> Ash.Changeset.for_update(:activate)
        |> Ash.update()

        case result do
          {:ok, updated_role} ->
            Logger.info("✅ [角色仓储] 角色激活成功: #{updated_role.name}")
            {:ok, updated_role}
          {:error, error} ->
            Logger.error("❌ [角色仓储] 角色激活失败: #{inspect(error)}")
            {:error, error}
        end
      end
    rescue
      exception ->
        Logger.error("💥 [角色仓储] 激活角色异常: #{inspect(exception)}")
        {:error, :activate_role_exception}
    end
  end

  @doc """
  禁用角色

  ## 参数
  - `role_id` - 角色ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, role}` - 成功
  - `{:error, reason}` - 失败
  """
  def deactivate_role(role_id, options \\ []) do
    Logger.info("🎭 [角色仓储] 禁用角色: #{role_id}")

    try do
      with {:ok, role} <- get_role(role_id, options) do
        result = role
        |> Ash.Changeset.for_update(:deactivate)
        |> Ash.update()

        case result do
          {:ok, updated_role} ->
            Logger.info("✅ [角色仓储] 角色禁用成功: #{updated_role.name}")
            {:ok, updated_role}
          {:error, error} ->
            Logger.error("❌ [角色仓储] 角色禁用失败: #{inspect(error)}")
            {:error, error}
        end
      end
    rescue
      exception ->
        Logger.error("💥 [角色仓储] 禁用角色异常: #{inspect(exception)}")
        {:error, :deactivate_role_exception}
    end
  end

  # ==================== 私有辅助函数 ====================

  # 构建列表查询
  defp build_list_query(query, params) do
    query
    |> maybe_filter_by_name(params)
    |> maybe_filter_by_code(params)
    |> maybe_filter_by_status(params)
    |> maybe_filter_by_level(params)
    |> apply_pagination(params)
    |> apply_sorting(params)
  end

  # 按名称过滤
  defp maybe_filter_by_name(query, %{"name" => name}) when is_binary(name) and name != "" do
    query |> Ash.Query.filter(contains(name, name))
  end
  defp maybe_filter_by_name(query, _), do: query

  # 按代码过滤
  defp maybe_filter_by_code(query, %{"code" => code}) when is_binary(code) and code != "" do
    query |> Ash.Query.filter(contains(code, code))
  end
  defp maybe_filter_by_code(query, _), do: query

  # 按状态过滤
  defp maybe_filter_by_status(query, %{"status" => status}) when is_integer(status) do
    query |> Ash.Query.filter(status == status)
  end
  defp maybe_filter_by_status(query, _), do: query

  # 按级别过滤
  defp maybe_filter_by_level(query, %{"level" => level}) when is_integer(level) do
    query |> Ash.Query.filter(level == level)
  end
  defp maybe_filter_by_level(query, _), do: query

  # 应用分页
  defp apply_pagination(query, params) do
    page = Map.get(params, "page", 1) |> ensure_integer()
    page_size = Map.get(params, "page_size", 20) |> ensure_integer() |> min(100)
    offset = (page - 1) * page_size

    query
    |> Ash.Query.limit(page_size)
    |> Ash.Query.offset(offset)
  end

  # 应用排序
  defp apply_sorting(query, params) do
    sort_by = Map.get(params, "sort_by", "inserted_at")
    sort_order = Map.get(params, "sort_order", "desc")

    case {sort_by, sort_order} do
      {"inserted_at", "desc"} -> query |> Ash.Query.sort(inserted_at: :desc)
      {"inserted_at", "asc"} -> query |> Ash.Query.sort(inserted_at: :asc)
      {"name", "asc"} -> query |> Ash.Query.sort(name: :asc)
      {"name", "desc"} -> query |> Ash.Query.sort(name: :desc)
      {"level", "desc"} -> query |> Ash.Query.sort(level: :desc)
      {"level", "asc"} -> query |> Ash.Query.sort(level: :asc)
      _ -> query |> Ash.Query.sort(inserted_at: :desc)
    end
  end

  # 加载关联数据
  defp maybe_load_associations(query, options) do
    load_associations = Keyword.get(options, :load, [])
    if Enum.empty?(load_associations) do
      query
    else
      query |> Ash.Query.load(load_associations)
    end
  end

  # 确保整数类型
  defp ensure_integer(value) when is_integer(value), do: value
  defp ensure_integer(value) when is_binary(value) do
    case Integer.parse(value) do
      {int, _} -> int
      :error -> 1
    end
  end
  defp ensure_integer(_), do: 1
end
