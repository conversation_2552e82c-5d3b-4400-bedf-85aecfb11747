defmodule RacingGame.Live.AdminPanel.InputValidator do
  @moduledoc """
  管理面板输入验证模块

  提供统一的输入验证功能，确保数据安全和一致性。

  ## 功能特性
  - 文本输入验证（长度、内容、HTML过滤）
  - 数字和ID验证
  - 邮箱和URL格式验证
  - 搜索查询安全验证
  - XSS防护和HTML清理
  - 统一的错误消息格式

  ## 使用示例

      iex> InputValidator.validate_text("hello", required: true, max_length: 10)
      {:ok, "hello"}

      iex> InputValidator.validate_email("<EMAIL>")
      {:ok, "<EMAIL>"}

      iex> InputValidator.validate_number("123", min: 1, max: 1000)
      {:ok, 123}
  """

  require Logger

  # 常量定义
  @default_max_length 255
  @default_min_length 0
  @max_search_length 100
  @email_regex ~r/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
  @url_regex ~r/^https?:\/\/[^\s]+$/
  @dangerous_patterns ["<script", "javascript:", "data:", "vbscript:", "onload=", "onerror="]

  @doc """
  验证文本输入

  ## 选项
  - `:required` - 是否必填，默认 false
  - `:max_length` - 最大长度，默认 255
  - `:min_length` - 最小长度，默认 0
  - `:field_name` - 字段名称，用于错误消息
  - `:allow_html` - 是否允许HTML，默认 false
  """
  def validate_text(value, opts \\ []) do
    Logger.debug("🔍 [输入验证] 验证文本输入: #{inspect(String.slice(to_string(value), 0, 50))}")

    config = extract_text_validation_config(opts)

    try do
      with {:ok, cleaned_value} <- clean_and_validate_presence(value, config),
           {:ok, length_validated} <- validate_length(cleaned_value, config),
           {:ok, safe_value} <- validate_content(length_validated, config) do
        Logger.debug("✅ [输入验证] 文本验证成功")
        {:ok, safe_value}
      else
        {:error, message} = error ->
          Logger.debug("❌ [输入验证] 文本验证失败: #{message}")
          error
      end
    rescue
      error ->
        Logger.error("❌ [输入验证] 文本验证异常: #{inspect(error)}")
        {:error, "验证过程中发生错误"}
    end
  end

  # 提取文本验证配置
  defp extract_text_validation_config(opts) do
    %{
      required: Keyword.get(opts, :required, false),
      max_length: Keyword.get(opts, :max_length, @default_max_length),
      min_length: Keyword.get(opts, :min_length, @default_min_length),
      field_name: Keyword.get(opts, :field_name, "字段"),
      allow_html: Keyword.get(opts, :allow_html, false),
      trim: Keyword.get(opts, :trim, true)
    }
  end

  @doc """
  验证搜索查询字符串

  ## 参数
  - `query` - 搜索查询字符串

  ## 返回值
  - `{:ok, cleaned_query}` - 验证成功
  - `{:error, message}` - 验证失败
  """
  def validate_search_query(query) when is_binary(query) do
    Logger.debug("🔍 [输入验证] 验证搜索查询: #{inspect(String.slice(query, 0, 20))}")

    try do
      cleaned_query = String.trim(query)

      with :ok <- validate_search_length(cleaned_query),
           :ok <- validate_search_safety(cleaned_query) do
        Logger.debug("✅ [输入验证] 搜索查询验证成功")
        {:ok, cleaned_query}
      else
        {:error, message} = error ->
          Logger.debug("❌ [输入验证] 搜索查询验证失败: #{message}")
          error
      end
    rescue
      error ->
        Logger.error("❌ [输入验证] 搜索查询验证异常: #{inspect(error)}")
        {:error, "搜索查询验证失败"}
    end
  end

  def validate_search_query(nil), do: {:ok, ""}
  def validate_search_query(""), do: {:ok, ""}
  def validate_search_query(_), do: {:ok, ""}

  # 验证搜索长度
  defp validate_search_length(query) do
    if String.length(query) > @max_search_length do
      {:error, "搜索关键词不能超过#{@max_search_length}个字符"}
    else
      :ok
    end
  end

  # 验证搜索安全性
  defp validate_search_safety(query) do
    dangerous_found = Enum.find(@dangerous_patterns, fn pattern ->
      String.contains?(String.downcase(query), String.downcase(pattern))
    end)

    if dangerous_found do
      {:error, "搜索关键词包含不安全内容"}
    else
      :ok
    end
  end

  @doc """
  验证ID参数（整数类型）
  """
  def validate_id(id) when is_binary(id) do
    case Integer.parse(id) do
      {parsed_id, ""} when parsed_id > 0 ->
        {:ok, parsed_id}
      _ ->
        {:error, "无效的ID格式"}
    end
  end

  def validate_id(id) when is_integer(id) and id > 0 do
    {:ok, id}
  end

  def validate_id(_), do: {:error, "无效的ID"}

  @doc """
  验证UUID格式的ID参数
  """
  def validate_uuid(uuid) when is_binary(uuid) do
    uuid = String.trim(uuid)

    case Ecto.UUID.cast(uuid) do
      {:ok, valid_uuid} -> {:ok, valid_uuid}
      :error -> {:error, "无效的ID格式"}
    end
  end

  def validate_uuid(_), do: {:error, "无效的ID"}

  @doc """
  验证页码
  """
  def validate_page_number(page) when is_binary(page) do
    case Integer.parse(page) do
      {parsed_page, ""} when parsed_page > 0 ->
        {:ok, parsed_page}
      _ ->
        {:error, "无效的页码"}
    end
  end

  def validate_page_number(page) when is_integer(page) and page > 0 do
    {:ok, page}
  end

  def validate_page_number(_), do: {:error, "无效的页码"}

  @doc """
  验证布尔值
  """
  def validate_boolean(value) when value in [true, false] do
    {:ok, value}
  end

  def validate_boolean("true"), do: {:ok, true}
  def validate_boolean("false"), do: {:ok, false}
  def validate_boolean("on"), do: {:ok, true}
  def validate_boolean(nil), do: {:ok, false}
  def validate_boolean(""), do: {:ok, false}
  def validate_boolean(_), do: {:error, "无效的布尔值"}

  @doc """
  验证邮箱地址
  """
  def validate_email(email) when is_binary(email) do
    email = String.trim(email)

    if Regex.match?(~r/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, email) do
      {:ok, email}
    else
      {:error, "邮箱格式不正确"}
    end
  end

  def validate_email(_), do: {:error, "邮箱格式不正确"}

  @doc """
  验证数字范围
  """
  def validate_number(value, opts \\ []) do
    min_value = Keyword.get(opts, :min)
    max_value = Keyword.get(opts, :max)
    field_name = Keyword.get(opts, :field_name, "数值")

    with {:ok, number} <- parse_number(value),
         {:ok, validated} <- validate_number_range(number, min_value, max_value, field_name) do
      {:ok, validated}
    else
      {:error, message} -> {:error, message}
    end
  end

  @doc """
  验证URL
  """
  def validate_url(url) when is_binary(url) do
    url = String.trim(url)

    if Regex.match?(~r/^https?:\/\/[^\s]+$/, url) do
      {:ok, url}
    else
      {:error, "URL格式不正确"}
    end
  end

  def validate_url(_), do: {:error, "URL格式不正确"}

  # 私有函数

  defp clean_and_validate_presence(value, required, field_name) do
    cleaned = if is_binary(value), do: String.trim(value), else: ""

    case {cleaned, required} do
      {"", true} -> {:error, "#{field_name}不能为空"}
      {cleaned, _} -> {:ok, cleaned}
    end
  end

  defp validate_length(value, min_length, max_length, field_name) do
    length = String.length(value)

    cond do
      length < min_length ->
        {:error, "#{field_name}长度不能少于#{min_length}个字符"}

      length > max_length ->
        {:error, "#{field_name}长度不能超过#{max_length}个字符"}

      true ->
        {:ok, value}
    end
  end

  defp validate_content(value, allow_html, field_name) do
    if allow_html do
      # 如果允许HTML，进行基本的XSS防护
      sanitized = sanitize_html(value)
      {:ok, sanitized}
    else
      # 不允许HTML，检查是否包含HTML标签
      if String.contains?(value, ["<", ">"]) do
        {:error, "#{field_name}不能包含HTML标签"}
      else
        {:ok, value}
      end
    end
  end

  defp sanitize_html(html) do
    # 基本的HTML清理，移除危险标签和属性
    html
    |> String.replace(~r/<script[^>]*>.*?<\/script>/i, "")
    |> String.replace(~r/<iframe[^>]*>.*?<\/iframe>/i, "")
    |> String.replace(~r/javascript:/i, "")
    |> String.replace(~r/on\w+\s*=/i, "")
  end

  defp parse_number(value) when is_number(value), do: {:ok, value}

  defp parse_number(value) when is_binary(value) do
    case Float.parse(value) do
      {number, ""} -> {:ok, number}
      {number, _} -> {:ok, number}
      :error ->
        case Integer.parse(value) do
          {number, ""} -> {:ok, number}
          _ -> {:error, "不是有效的数字"}
        end
    end
  end

  defp parse_number(_), do: {:error, "不是有效的数字"}

  defp validate_number_range(number, nil, nil, _), do: {:ok, number}

  defp validate_number_range(number, min_value, nil, field_name) when number < min_value do
    {:error, "#{field_name}不能小于#{min_value}"}
  end

  defp validate_number_range(number, nil, max_value, field_name) when number > max_value do
    {:error, "#{field_name}不能大于#{max_value}"}
  end

  defp validate_number_range(number, min_value, max_value, field_name)
       when number < min_value or number > max_value do
    {:error, "#{field_name}必须在#{min_value}到#{max_value}之间"}
  end

  defp validate_number_range(number, _, _, _), do: {:ok, number}
end
