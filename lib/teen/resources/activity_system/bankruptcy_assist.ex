defmodule Teen.ActivitySystem.BankruptcyAssist do
  @moduledoc """
  破产补助资源

  管理破产补助活动配置和奖励
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  postgres do
    table "bankruptcy_assists"
    repo Cypridina.Repo
  end

  admin do
    table_columns [:id, :assist_name, :bankruptcy_threshold, :reward_amount, :initial_count, :status]
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_assists
  end

  actions do
    defaults [:create, :read, :update, :destroy]

    read :list_active_assists do
      filter expr(status == 1)
    end

    update :enable do
      change set_attribute(:status, 1)
    end

    update :disable do
      change set_attribute(:status, 0)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :assist_name, :string do
      allow_nil? false
      public? true
      description "破产补助名称"
      constraints max_length: 100
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "破产补助描述"
      constraints max_length: 1000
    end

    attribute :bankruptcy_threshold, :decimal do
      allow_nil? false
      public? true
      description "破产金币阈值"
      default Decimal.new("1000")
      constraints min: Decimal.new("0")
    end

    attribute :reward_amount, :decimal do
      allow_nil? false
      public? true
      description "破产奖励金额"
      default Decimal.new("5000")
      constraints min: Decimal.new("0")
    end

    attribute :initial_count, :integer do
      allow_nil? false
      public? true
      description "初始领取次数"
      default 3
      constraints min: 0
    end

    attribute :additional_count, :integer do
      allow_nil? false
      public? true
      description "增加次数（每次充值后增加）"
      default 1
      constraints min: 0
    end

    attribute :max_count, :integer do
      allow_nil? false
      public? true
      description "最大领取次数"
      default 10
      constraints min: 1
    end

    attribute :reset_period, :integer do
      allow_nil? false
      public? true
      description "重置周期（天）：0-不重置，1-每日重置，7-每周重置，30-每月重置"
      default 0
      constraints min: 0
    end

    attribute :min_level, :integer do
      allow_nil? false
      public? true
      description "最低等级要求"
      default 1
      constraints min: 1
    end

    attribute :vip_bonus_rate, :decimal do
      allow_nil? false
      public? true
      description "VIP奖励加成比例（%）"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :cooldown_hours, :integer do
      allow_nil? false
      public? true
      description "冷却时间（小时）"
      default 1
      constraints min: 0
    end

    attribute :recharge_requirement, :decimal do
      allow_nil? false
      public? true
      description "充值要求（获得额外次数的最低充值金额）"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :consecutive_limit, :integer do
      allow_nil? false
      public? true
      description "连续领取限制（防止滥用）"
      default 3
      constraints min: 1
    end

    attribute :status, :integer do
      allow_nil? false
      public? true
      description "状态：0-禁用，1-启用"
      default 1
      constraints min: 0, max: 1
    end

    attribute :priority, :integer do
      allow_nil? false
      public? true
      description "优先级（数字越大优先级越高）"
      default 0
      constraints min: 0
    end

    attribute :conditions, :map do
      allow_nil? true
      public? true
      description "额外条件配置"
    end

    timestamps()
  end

  identities do
    identity :unique_assist_name, [:assist_name]
  end
end
