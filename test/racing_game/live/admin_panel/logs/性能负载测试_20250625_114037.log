Compiling 48 files (.ex)
    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 32 │   def create_promoter(promoter_data, options \\ []) do
    │                                      ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promoter_repository.ex:32:38: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromoterRepository.create_promoter/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 32 │   def create_settlement(settlement_data, options \\ []) do
    │                                          ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promotion_settlement_repository.ex:32:42: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionSettlementRepository.create_settlement/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 32 │   def create_settlement(settlement_data, options \\ []) do
    │                                          ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/share_settlement_repository.ex:32:42: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.ShareSettlementRepository.create_settlement/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 32 │   def create_channel(channel_data, options \\ []) do
    │                                    ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promotion_channel_repository.ex:32:36: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionChannelRepository.create_channel/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 32 │   def create_config(config_data, options \\ []) do
    │                                  ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/share_config_repository.ex:32:34: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.ShareConfigRepository.create_config/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 58 │   def get_settlement(settlement_id, options \\ []) do
    │                                     ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/share_settlement_repository.ex:58:37: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.ShareSettlementRepository.get_settlement/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 58 │   def get_settlement(settlement_id, options \\ []) do
    │                                     ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promotion_settlement_repository.ex:58:37: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionSettlementRepository.get_settlement/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 58 │   def get_promoter(promoter_id, options \\ []) do
    │                                 ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promoter_repository.ex:58:33: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromoterRepository.get_promoter/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 58 │   def get_channel(channel_id, options \\ []) do
    │                               ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promotion_channel_repository.ex:58:31: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionChannelRepository.get_channel/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 58 │   def get_config(config_id, options \\ []) do
    │                             ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/share_config_repository.ex:58:29: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.ShareConfigRepository.get_config/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 88 │   def get_promoter_by_user_id(user_id, options \\ []) do
    │                                        ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promoter_repository.ex:88:40: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromoterRepository.get_promoter_by_user_id/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 82 │   def build_promoter_relationship_tree(promoter_id, depth \\ 3, options \\ []) do
    │                                                                 ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/query_builders/teen/promotion_system_query_builder.ex:82:65: RacingGame.Live.AdminPanel.QueryBuilders.Teen.PromotionSystemQueryBuilder.build_promoter_relationship_tree/3

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 89 │   def update_settlement(settlement_id, update_data, options \\ []) do
    │                                                     ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/share_settlement_repository.ex:89:53: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.ShareSettlementRepository.update_settlement/3

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 89 │   def update_channel(channel_id, update_data, options \\ []) do
    │                                               ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promotion_channel_repository.ex:89:47: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionChannelRepository.update_channel/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 119 │   def get_promoter_by_code(promoter_code, options \\ []) do
     │                                           ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promoter_repository.ex:119:43: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromoterRepository.get_promoter_by_code/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 89 │   def update_settlement(settlement_id, update_data, options \\ []) do
    │                                                     ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promotion_settlement_repository.ex:89:53: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionSettlementRepository.update_settlement/3

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 88 │   def get_config_by_key(config_key, options \\ []) do
    │                                     ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/share_config_repository.ex:88:37: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.ShareConfigRepository.get_config_by_key/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 115 │   def delete_settlement(settlement_id, options \\ []) do
     │                                        ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/share_settlement_repository.ex:115:40: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.ShareSettlementRepository.delete_settlement/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 115 │   def delete_channel(channel_id, options \\ []) do
     │                                  ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promotion_channel_repository.ex:115:34: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionChannelRepository.delete_channel/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 151 │   def update_promoter(promoter_id, update_data, options \\ []) do
     │                                                 ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promoter_repository.ex:151:49: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromoterRepository.update_promoter/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 120 │   def update_config(config_id, update_data, options \\ []) do
     │                                             ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/share_config_repository.ex:120:45: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.ShareConfigRepository.update_config/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 115 │   def delete_settlement(settlement_id, options \\ []) do
     │                                        ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promotion_settlement_repository.ex:115:40: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionSettlementRepository.delete_settlement/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 146 │   def delete_config(config_id, options \\ []) do
     │                                ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/share_config_repository.ex:146:32: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.ShareConfigRepository.delete_config/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 177 │   def delete_promoter(promoter_id, options \\ []) do
     │                                    ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promoter_repository.ex:177:36: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromoterRepository.delete_promoter/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 205 │   def approve_promoter(promoter_id, options \\ []) do
     │                                     ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promoter_repository.ex:205:37: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromoterRepository.approve_promoter/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 231 │   def reject_promoter(promoter_id, options \\ []) do
     │                                    ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promoter_repository.ex:231:36: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromoterRepository.reject_promoter/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 237 │   def list_active_configs(options \\ []) do
     │                           ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/share_config_repository.ex:237:27: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.ShareConfigRepository.list_active_configs/1

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 242 │   def get_promoter_settlement_stats(promoter_id, date_range \\ nil, options \\ []) do
     │                                                                     ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promotion_settlement_repository.ex:242:69: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionSettlementRepository.get_promoter_settlement_stats/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 244 │   def increment_channel_clicks(channel_id, click_count \\ 1, options \\ []) do
     │                                                              ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promotion_channel_repository.ex:244:62: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionChannelRepository.increment_channel_clicks/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 275 │   def get_user_settlement_stats(user_id, date_range \\ nil, options \\ []) do
     │                                                             ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/share_settlement_repository.ex:275:61: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.ShareSettlementRepository.get_user_settlement_stats/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 271 │   def get_system_settlement_stats(date_range \\ nil, options \\ []) do
     │                                                      ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promotion_settlement_repository.ex:271:54: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionSettlementRepository.get_system_settlement_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 304 │   def get_system_settlement_stats(date_range \\ nil, options \\ []) do
     │                                                      ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/share_settlement_repository.ex:304:54: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.ShareSettlementRepository.get_system_settlement_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 272 │   def increment_channel_registers(channel_id, register_count \\ 1, options \\ []) do
     │                                                                    ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/promotion_system/promotion_channel_repository.ex:272:68: RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionChannelRepository.increment_channel_registers/3

     warning: variable "promoter" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 242 │          {:ok, promoter} <- PromoterRepository.get_promoter(channel.promoter_id),
     │                ~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/promotion_system_service.ex:242:16: RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService.handle_channel_conversion/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 628 │   defp get_system_health_indicators(options) do
     │                                     ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/query_builders/teen/promotion_system_query_builder.ex:628:37: RacingGame.Live.AdminPanel.QueryBuilders.Teen.PromotionSystemQueryBuilder.get_system_health_indicators/1

     warning: variable "reason" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 378 │   defp send_promoter_review_notification(promoter, action, reason) do
     │                                                            ~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/promotion_system_service.ex:378:60: RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService.send_promoter_review_notification/3

     warning: variable "channel" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 443 │   defp get_redirect_url(channel) do
     │                         ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/promotion_system_service.ex:443:25: RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService.get_redirect_url/1

     warning: variable "channel" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 451 │   defp calculate_registration_commission(promoter, channel) do
     │                                                    ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/promotion_system_service.ex:451:52: RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService.calculate_registration_commission/2

    warning: variable "yesterday" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 91 │       yesterday = Date.add(today, -1)
    │       ~
    │
    └─ lib/racing_game/live/admin_panel/query_builders/teen/data_statistics_query_builder.ex:91:7: RacingGame.Live.AdminPanel.QueryBuilders.Teen.DataStatisticsQueryBuilder.get_realtime_dashboard/1

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 694 │   def system_health_check(options \\ []) do
     │                           ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/promotion_system_service.ex:694:27: RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService.system_health_check/1

     warning: variable "share_type" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 758 │   defp validate_user_share_eligibility(user_id, share_type) do
     │                                                 ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/promotion_system_service.ex:758:49: RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService.validate_user_share_eligibility/2

     warning: variable "user_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 758 │   defp validate_user_share_eligibility(user_id, share_type) do
     │                                        ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/promotion_system_service.ex:758:40: RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService.validate_user_share_eligibility/2

     warning: variable "share_data" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 764 │   defp calculate_share_reward(user_id, share_config, share_data) do
     │                                                      ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/promotion_system_service.ex:764:54: RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService.calculate_share_reward/3

     warning: variable "user_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 764 │   defp calculate_share_reward(user_id, share_config, share_data) do
     │                               ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/promotion_system_service.ex:764:31: RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService.calculate_share_reward/3

     warning: variable "settlement" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 779 │     with {:ok, settlement} <- ShareSettlementRepository.get_settlement(settlement_id),
     │                ~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/promotion_system_service.ex:779:16: RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService.process_single_share_settlement/3

     warning: variable "promoter" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 830 │   defp calculate_promoter_performance_score(promoter, channels, settlement_stats) do
     │                                             ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/promotion_system_service.ex:830:45: RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService.calculate_promoter_performance_score/3

     warning: variable "promoter" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 843 │   defp generate_promoter_recommendations(promoter, channels, settlement_stats) do
     │                                          ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/promotion_system_service.ex:843:42: RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService.generate_promoter_recommendations/3

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_payment_stats(stats_data, options \\ []) do
    │                                        ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/payment_stats_repository.ex:35:40: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.PaymentStatsRepository.create_payment_stats/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 70 │   def get_payment_stats(stats_id, options \\ []) do
    │                                   ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/payment_stats_repository.ex:70:35: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.PaymentStatsRepository.get_payment_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 113 │   def update_payment_stats(payment_stats, update_data, options \\ []) do
     │                                                        ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/payment_stats_repository.ex:113:56: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.PaymentStatsRepository.update_payment_stats/3

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_retention_stats(stats_data, options \\ []) do
    │                                          ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/retention_stats_repository.ex:35:42: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RetentionStatsRepository.create_retention_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 148 │   def delete_payment_stats(payment_stats, options \\ []) do
     │                                           ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/payment_stats_repository.ex:148:43: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.PaymentStatsRepository.delete_payment_stats/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 70 │   def get_retention_stats(stats_id, options \\ []) do
    │                                     ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/retention_stats_repository.ex:70:37: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RetentionStatsRepository.get_retention_stats/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_ltv_stats(stats_data, options \\ []) do
    │                                    ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/ltv_stats_repository.ex:35:36: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.LtvStatsRepository.create_ltv_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 113 │   def update_retention_stats(retention_stats, update_data, options \\ []) do
     │                                                            ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/retention_stats_repository.ex:113:60: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RetentionStatsRepository.update_retention_stats/3

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_coin_stats(stats_data, options \\ []) do
    │                                     ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/coin_stats_repository.ex:35:37: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.CoinStatsRepository.create_coin_stats/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 70 │   def get_ltv_stats(stats_id, options \\ []) do
    │                               ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/ltv_stats_repository.ex:70:31: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.LtvStatsRepository.get_ltv_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 148 │   def delete_retention_stats(retention_stats, options \\ []) do
     │                                               ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/retention_stats_repository.ex:148:47: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RetentionStatsRepository.delete_retention_stats/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 70 │   def get_coin_stats(stats_id, options \\ []) do
    │                                ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/coin_stats_repository.ex:70:32: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.CoinStatsRepository.get_coin_stats/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_user_stats(stats_data, options \\ []) do
    │                                     ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/user_stats_repository.ex:35:37: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.UserStatsRepository.create_user_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 113 │   def update_ltv_stats(ltv_stats, update_data, options \\ []) do
     │                                                ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/ltv_stats_repository.ex:113:48: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.LtvStatsRepository.update_ltv_stats/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 113 │   def update_coin_stats(coin_stats, update_data, options \\ []) do
     │                                                  ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/coin_stats_repository.ex:113:50: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.CoinStatsRepository.update_coin_stats/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 148 │   def delete_ltv_stats(ltv_stats, options \\ []) do
     │                                   ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/ltv_stats_repository.ex:148:35: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.LtvStatsRepository.delete_ltv_stats/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 70 │   def get_user_stats(stats_id, options \\ []) do
    │                                ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/user_stats_repository.ex:70:32: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.UserStatsRepository.get_user_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 148 │   def delete_coin_stats(coin_stats, options \\ []) do
     │                                     ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/coin_stats_repository.ex:148:37: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.CoinStatsRepository.delete_coin_stats/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_robot_stats(stats_data, options \\ []) do
    │                                      ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/robot_stats_repository.ex:35:38: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RobotStatsRepository.create_robot_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 113 │   def update_user_stats(user_stats, update_data, options \\ []) do
     │                                                  ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/user_stats_repository.ex:113:50: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.UserStatsRepository.update_user_stats/3

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 70 │   def get_robot_stats(stats_id, options \\ []) do
    │                                 ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/robot_stats_repository.ex:70:33: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RobotStatsRepository.get_robot_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 148 │   def delete_user_stats(user_stats, options \\ []) do
     │                                     ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/user_stats_repository.ex:148:37: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.UserStatsRepository.delete_user_stats/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 35 │   def create_channel_stats(stats_data, options \\ []) do
    │                                        ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/channel_stats_repository.ex:35:40: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.ChannelStatsRepository.create_channel_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 113 │   def update_robot_stats(robot_stats, update_data, options \\ []) do
     │                                                    ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/robot_stats_repository.ex:113:52: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RobotStatsRepository.update_robot_stats/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 148 │   def delete_robot_stats(robot_stats, options \\ []) do
     │                                       ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/robot_stats_repository.ex:148:39: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.RobotStatsRepository.delete_robot_stats/2

    warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
    │
 70 │   def get_channel_stats(stats_id, options \\ []) do
    │                                   ~~~~~~~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/channel_stats_repository.ex:70:35: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.ChannelStatsRepository.get_channel_stats/2

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 113 │   def update_channel_stats(channel_stats, update_data, options \\ []) do
     │                                                        ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/channel_stats_repository.ex:113:56: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.ChannelStatsRepository.update_channel_stats/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 148 │   def delete_channel_stats(channel_stats, options \\ []) do
     │                                           ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/data_statistics/channel_stats_repository.ex:148:43: RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.ChannelStatsRepository.delete_channel_stats/2

     warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
     │
 133 │             Logger.warn("⚠️ [数据统计服务] 报告保存失败，但报告生成成功: #{inspect(save_error)}")
     │                    ~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:133:20: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.generate_comprehensive_report/2

     warning: variable "config" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 441 │   defp process_report_data(raw_data, config, _options) do
     │                                      ~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/services/teen/data_statistics_service.ex:441:38: RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService.process_report_data/3


== Compilation error in file lib/racing_game/live/admin_panel/query_builders/teen/data_statistics_query_builder.ex ==
** (ArgumentError) cannot pipe stats |> Enum.map(& &1.ltv_value) into Enum.sum() / length(stats), the :/ operator can only take two arguments
    (elixir 1.18.4) lib/macro.ex:329: Macro.pipe/3
    (stdlib 6.2.2) lists.erl:2146: :lists.foldl/3
    (elixir 1.18.4) expanding macro: Kernel.|>/2
    lib/racing_game/live/admin_panel/query_builders/teen/data_statistics_query_builder.ex:806: RacingGame.Live.AdminPanel.QueryBuilders.Teen.DataStatisticsQueryBuilder.build_ltv_segments/1
    (elixir 1.18.4) expanding macro: Kernel.|>/2
    lib/racing_game/live/admin_panel/query_builders/teen/data_statistics_query_builder.ex:803: RacingGame.Live.AdminPanel.QueryBuilders.Teen.DataStatisticsQueryBuilder.build_ltv_segments/1
    (elixir 1.18.4) expanding macro: Kernel.|>/2
    lib/racing_game/live/admin_panel/query_builders/teen/data_statistics_query_builder.ex:810: RacingGame.Live.AdminPanel.QueryBuilders.Teen.DataStatisticsQueryBuilder.build_ltv_segments/1
