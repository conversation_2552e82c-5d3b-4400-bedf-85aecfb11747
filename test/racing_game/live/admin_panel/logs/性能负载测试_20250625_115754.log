Compiling 76 files (.ex)
    warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
    │
 97 │           Logger.warn("⚠️ [CDKEY活动仓储] CDKEY不存在: #{cdkey_code}")
    │                  ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex:97:18: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.CdkeyActivityRepository.get_cdkey_by_code/2

     warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
     │
 130 │           Logger.warn("⚠️ [CDKEY活动仓储] CDKEY不存在: #{cdkey_id}")
     │                  ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex:130:18: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.CdkeyActivityRepository.get_cdkey/2

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 201 │       {:updated_at, %{from: from_date, to: to_date}} ->
     │                             ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/user_query_builder.ex:201:29: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.UserQueryBuilder.apply_single_filter/3

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 201 │       {:updated_at, %{from: from_date, to: to_date}} ->
     │                                            ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/user_query_builder.ex:201:44: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.UserQueryBuilder.apply_single_filter/3

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 218 │   defp apply_date_range_filter(query, from_date, nil) do
     │                                       ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/user_query_builder.ex:218:39: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.UserQueryBuilder.apply_date_range_filter/3

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 221 │   defp apply_date_range_filter(query, nil, to_date) do
     │                                            ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/user_query_builder.ex:221:44: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.UserQueryBuilder.apply_date_range_filter/3

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 224 │   defp apply_date_range_filter(query, from_date, to_date) do
     │                                       ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/user_query_builder.ex:224:39: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.UserQueryBuilder.apply_date_range_filter/3

     warning: variable "max_level" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 146 │   defp apply_user_filter(query, :agent_level_range, {min_level, max_level}) do
     │                                                                 ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:146:65: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_user_filter/3

     warning: variable "status_code" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 220 │       status_code ->
     │       ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/bet_query_builder.ex:220:7: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.BetQueryBuilder.apply_status_filter/2

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 224 │   defp apply_date_range_filter(query, from_date, to_date) do
     │                                                  ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/user_query_builder.ex:224:50: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.UserQueryBuilder.apply_date_range_filter/3

     warning: variable "min_level" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 146 │   defp apply_user_filter(query, :agent_level_range, {min_level, max_level}) do
     │                                                      ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:146:54: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_user_filter/3

     warning: variable "user_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 264 │   defp apply_user_recipient_filter(query, user_id) do
     │                                           ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/communication_query_builder.ex:264:43: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CommunicationQueryBuilder.apply_user_recipient_filter/2

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 282 │   defp apply_date_range_filter(query, from_date, nil) do
     │                                       ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/stock_query_builder.ex:282:39: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.StockQueryBuilder.apply_date_range_filter/3

     warning: variable "date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 166 │   defp apply_user_filter(query, :created_after, date) do
     │                                                 ~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:166:49: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_user_filter/3

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 275 │   defp apply_date_range_filter(query, from_date, nil) do
     │                                       ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/bet_query_builder.ex:275:39: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.BetQueryBuilder.apply_date_range_filter/3

     warning: variable "cache_patterns" is unused (there is a variable with the same name in the context, use the pin operator (^) to match on it or prefix this variable with underscore if it is not meant to be used)
     │
 262 │       cache_patterns = ["bet:#{bet_id}:*" | cache_patterns]
     │       ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/bet_repository.ex:262:7: RacingGame.Live.AdminPanel.Repositories.BetRepository.clear_cache/2

     warning: variable "account_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 254 │         {:ok, account_id} ->
     │               ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/ledger_query_builder.ex:254:15: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.LedgerQueryBuilder.build_user_financial_query/3

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 285 │   defp apply_date_range_filter(query, nil, to_date) do
     │                                            ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/stock_query_builder.ex:285:44: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.StockQueryBuilder.apply_date_range_filter/3

     warning: variable "now" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 278 │     now = DateTime.utc_now()
     │     ~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/communication_query_builder.ex:278:5: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CommunicationQueryBuilder.apply_expiry_filter/1

     warning: variable "date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 170 │   defp apply_user_filter(query, :created_before, date) do
     │                                                  ~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:170:50: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_user_filter/3

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 278 │   defp apply_date_range_filter(query, nil, to_date) do
     │                                            ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/bet_query_builder.ex:278:44: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.BetQueryBuilder.apply_date_range_filter/3

     warning: variable "cache_patterns" is unused (there is a variable with the same name in the context, use the pin operator (^) to match on it or prefix this variable with underscore if it is not meant to be used)
     │
 266 │       cache_patterns = ["user_bets:#{user_id}:*" | cache_patterns]
     │       ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/bet_repository.ex:266:7: RacingGame.Live.AdminPanel.Repositories.BetRepository.clear_cache/2

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 288 │   defp apply_date_range_filter(query, from_date, to_date) do
     │                                       ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/stock_query_builder.ex:288:39: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.StockQueryBuilder.apply_date_range_filter/3

     warning: variable "account_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 263 │         {:ok, account_id} ->
     │               ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/ledger_query_builder.ex:263:15: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.LedgerQueryBuilder.build_user_financial_query/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 318 │   defp maybe_add_index_hints(query, options) do
     │                                     ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/user_query_builder.ex:318:37: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.UserQueryBuilder.maybe_add_index_hints/2

     warning: variable "end_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 174 │   defp apply_user_filter(query, :created_date_range, {start_date, end_date}) do
     │                                                                   ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:174:67: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_user_filter/3

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 297 │       {:updated_at, %{from: from_date, to: to_date}} ->
     │                             ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/communication_query_builder.ex:297:29: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CommunicationQueryBuilder.apply_single_filter/3

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 281 │   defp apply_date_range_filter(query, from_date, to_date) do
     │                                       ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/bet_query_builder.ex:281:39: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.BetQueryBuilder.apply_date_range_filter/3

     warning: variable "cache_patterns" is unused (there is a variable with the same name in the context, use the pin operator (^) to match on it or prefix this variable with underscore if it is not meant to be used)
     │
 270 │       cache_patterns = ["bet:*", "user_bets:*"]
     │       ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/bet_repository.ex:270:7: RacingGame.Live.AdminPanel.Repositories.BetRepository.clear_cache/2

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 288 │   defp apply_date_range_filter(query, from_date, to_date) do
     │                                                  ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/stock_query_builder.ex:288:50: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.StockQueryBuilder.apply_date_range_filter/3

     warning: variable "start_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 174 │   defp apply_user_filter(query, :created_date_range, {start_date, end_date}) do
     │                                                       ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:174:55: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_user_filter/3

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 297 │       {:updated_at, %{from: from_date, to: to_date}} ->
     │                                            ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/communication_query_builder.ex:297:44: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CommunicationQueryBuilder.apply_single_filter/3

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 281 │   defp apply_date_range_filter(query, from_date, to_date) do
     │                                                  ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/bet_query_builder.ex:281:50: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.BetQueryBuilder.apply_date_range_filter/3

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 302 │       {:expires_at, %{from: from_date, to: to_date}} ->
     │                             ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/communication_query_builder.ex:302:29: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CommunicationQueryBuilder.apply_single_filter/3

     warning: variable "query" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 356 │   def analyze_query_complexity(query) do
     │                                ~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/user_query_builder.ex:356:32: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.UserQueryBuilder.analyze_query_complexity/1

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 311 │       {:updated_at, %{from: from_date, to: to_date}} ->
     │                             ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/bet_query_builder.ex:311:29: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.BetQueryBuilder.apply_single_filter/3

     warning: variable "max_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 178 │   defp apply_user_filter(query, :numeric_id_range, {min_id, max_id}) do
     │                                                             ~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:178:61: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_user_filter/3

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 315 │       {:inserted_at, %{from: from_date, to: to_date}} ->
     │                              ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/stock_query_builder.ex:315:30: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.StockQueryBuilder.apply_single_filter/3

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 302 │       {:expires_at, %{from: from_date, to: to_date}} ->
     │                                            ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/communication_query_builder.ex:302:44: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CommunicationQueryBuilder.apply_single_filter/3

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 311 │       {:updated_at, %{from: from_date, to: to_date}} ->
     │                                            ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/bet_query_builder.ex:311:44: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.BetQueryBuilder.apply_single_filter/3

     warning: variable "min_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 178 │   defp apply_user_filter(query, :numeric_id_range, {min_id, max_id}) do
     │                                                     ~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:178:53: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_user_filter/3

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 315 │       {:inserted_at, %{from: from_date, to: to_date}} ->
     │                                             ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/stock_query_builder.ex:315:45: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.StockQueryBuilder.apply_single_filter/3

     warning: variable "pattern" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 405 │   defp apply_single_search_condition(query, {:ilike, field, pattern}) do
     │                                                             ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/ledger_query_builder.ex:405:61: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.LedgerQueryBuilder.apply_single_search_condition/2

     warning: variable "query" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 376 │   def get_execution_plan(query) do
     │                          ~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/user_query_builder.ex:376:26: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.UserQueryBuilder.get_execution_plan/1

     warning: variable "pattern" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 414 │       {:ilike, field, pattern} -> Ash.Query.filter(query, ilike(^field, pattern))
     │                       ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/ledger_query_builder.ex:414:23: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.LedgerQueryBuilder.apply_multiple_search_conditions/2

     warning: variable "date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 228 │   defp apply_identity_filter(query, :created_after, date) do
     │                                                     ~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:228:53: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_identity_filter/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 143 │   def get_user_agent(user_id, options \\ []) do
     │                               ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/agent_relationship_repository.ex:143:31: RacingGame.Live.AdminPanel.Repositories.Accounts.AgentRelationshipRepository.get_user_agent/2

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 429 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/ledger_query_builder.ex:429:5: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.LedgerQueryBuilder.maybe_limit_by_days/2

     warning: variable "date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 232 │   defp apply_identity_filter(query, :created_before, date) do
     │                                                      ~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:232:54: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_identity_filter/3

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 319 │   defp apply_date_range_filter(query, from_date, nil) do
     │                                       ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/communication_query_builder.ex:319:39: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CommunicationQueryBuilder.apply_date_range_filter/3

     warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
     │
 269 │           Logger.warn("⚠️ [余额仓储] 批量创建部分失败: 成功 #{length(successes)}, 失败 #{length(failures)}")
     │                  ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/ledger/balance_repository.ex:269:18: RacingGame.Live.AdminPanel.Repositories.Ledger.BalanceRepository.create_balances_batch/1

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 273 │   def get_account_balance(account_id, options \\ []) do
     │                                       ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/ledger/account_repository.ex:273:39: RacingGame.Live.AdminPanel.Repositories.Ledger.AccountRepository.get_account_balance/2

     warning: variable "identifier" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 435 │   defp get_account_id_by_identifier(identifier) do
     │                                     ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/ledger_query_builder.ex:435:37: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.LedgerQueryBuilder.get_account_id_by_identifier/1

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 322 │   defp apply_date_range_filter(query, nil, to_date) do
     │                                            ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/communication_query_builder.ex:322:44: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CommunicationQueryBuilder.apply_date_range_filter/3

     warning: variable "token_value" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 175 │   def get_token_by_value(token_value, options \\ []) do
     │                          ~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/user_identity_repository.ex:175:26: RacingGame.Live.AdminPanel.Repositories.Accounts.UserIdentityRepository.get_token_by_value/2

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 325 │   defp apply_date_range_filter(query, from_date, to_date) do
     │                                       ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/communication_query_builder.ex:325:39: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CommunicationQueryBuilder.apply_date_range_filter/3

     warning: variable "max_level" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 277 │   defp apply_agent_relationship_filter(query, :level_range, {min_level, max_level}) do
     │                                                                         ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:277:73: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_agent_relationship_filter/3

     warning: variable "search_term" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 374 │   defp get_matching_user_ids(search_term) do
     │                              ~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/bet_query_builder.ex:374:30: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.BetQueryBuilder.get_matching_user_ids/1

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 302 │   def get_user_balance(user_id, currency \\ :XAA, options \\ []) do
     │                                                   ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/ledger/account_repository.ex:302:51: RacingGame.Live.AdminPanel.Repositories.Ledger.AccountRepository.get_user_balance/3

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 325 │   defp apply_date_range_filter(query, from_date, to_date) do
     │                                                  ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/communication_query_builder.ex:325:50: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CommunicationQueryBuilder.apply_date_range_filter/3

     warning: variable "min_level" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 277 │   defp apply_agent_relationship_filter(query, :level_range, {min_level, max_level}) do
     │                                                              ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:277:62: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_agent_relationship_filter/3

     warning: variable "search_term" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 378 │   defp get_matching_user_ids(search_term) do
     │                              ~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/stock_query_builder.ex:378:30: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.StockQueryBuilder.get_matching_user_ids/1

     warning: variable "date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 285 │   defp apply_agent_relationship_filter(query, :created_after, date) do
     │                                                               ~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:285:63: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_agent_relationship_filter/3

     warning: variable "read_ids_query" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 320 │       read_ids_query = SystemCommunicationRead
     │       ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:320:7: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.build_user_unread_communications_query/2

     warning: variable "date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 289 │   defp apply_agent_relationship_filter(query, :created_before, date) do
     │                                                                ~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:289:64: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_agent_relationship_filter/3

     error: undefined variable "expires_at"
     │
 365 │     Ash.Query.filter(query, is_nil(expires_at) or expires_at  >= now)
     │                                                   ^^^^^^^^^^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex:365:51: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.CdkeyActivityRepository.maybe_filter_by_expiry/2

     error: undefined variable "expires_at"
     │
 365 │     Ash.Query.filter(query, is_nil(expires_at) or expires_at  >= now)
     │                                    ^^^^^^^^^^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex:365:36: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.CdkeyActivityRepository.maybe_filter_by_expiry/2

     error: undefined variable "expires_at"
     │
 361 │     Ash.Query.filter(query, not is_nil(expires_at) and expires_at  < now)
     │                                                        ^^^^^^^^^^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex:361:56: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.CdkeyActivityRepository.maybe_filter_by_expiry/2

     error: undefined variable "expires_at"
     │
 361 │     Ash.Query.filter(query, not is_nil(expires_at) and expires_at  < now)
     │                                        ^^^^^^^^^^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex:361:40: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.CdkeyActivityRepository.maybe_filter_by_expiry/2

     error: undefined variable "used_by_user_id"
     │
 354 │     Ash.Query.filter(query, used_by_user_id  == user_id)
     │                             ^^^^^^^^^^^^^^^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex:354:29: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.CdkeyActivityRepository.maybe_filter_by_user/2

     error: undefined variable "id"
     │
 124 │       |> Ash.Query.filter(id == cdkey_id)
     │                           ^^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex:124:27: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.CdkeyActivityRepository.get_cdkey/2

    error: misplaced operator ^cdkey_code

    The pin operator ^ is supported only inside matches or inside custom macros. Make sure you are inside a match or all necessary macros have been required
    │
 91 │       |> Ash.Query.filter(expr(cdkey == ^cdkey_code))
    │                                         ^
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex:91:41: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.CdkeyActivityRepository.get_cdkey_by_code/2

    error: undefined variable "cdkey"
    │
 91 │       |> Ash.Query.filter(expr(cdkey == ^cdkey_code))
    │                                ^^^^^
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex:91:32: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.CdkeyActivityRepository.get_cdkey_by_code/2


== Compilation error in file lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex ==
** (CompileError) lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex: cannot compile module RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.CdkeyActivityRepository (errors have been logged)

