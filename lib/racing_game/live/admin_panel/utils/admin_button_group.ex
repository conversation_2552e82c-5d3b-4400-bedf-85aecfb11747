defmodule CypridinaWeb.Components.AdminButtonGroup do
  @moduledoc """
  管理后台按钮组组件

  提供统一的按钮样式和功能，包括各种类型的按钮：
  - 基础按钮（主要、次要、危险等）
  - 功能按钮（保存、删除、编辑等）
  - 状态按钮（成功、警告、信息等）

  所有按钮都支持图标、禁用状态、自定义样式等功能。
  """
  use Phoenix.Component

  # 常量定义
  @button_base_classes "px-4 py-2 rounded-md font-medium transition-colors duration-200"
  @button_sizes %{
    xs: "px-2 py-1 text-xs",
    sm: "px-3 py-1.5 text-sm",
    md: "px-4 py-2 text-base",
    lg: "px-6 py-3 text-lg",
    xl: "px-8 py-4 text-xl"
  }
  @button_variants %{
    primary: %{class: "bg-indigo-600 text-white hover:bg-indigo-700", color: "#4f46e5"},
    secondary: %{class: "bg-gray-200 text-gray-800 border border-gray-300 hover:bg-gray-300", color: "#e5e7eb"},
    success: %{class: "bg-green-600 text-white hover:bg-green-700", color: "#16a34a"},
    danger: %{class: "bg-red-600 text-white hover:bg-red-700", color: "#dc2626"},
    warning: %{class: "bg-yellow-500 text-white hover:bg-yellow-600", color: "#eab308"},
    info: %{class: "bg-blue-500 text-white hover:bg-blue-600", color: "#3b82f6"},
    cancel: %{class: "bg-gray-500 text-white hover:bg-gray-600", color: "#6b7280"},
    edit: %{class: "bg-orange-500 text-white hover:bg-orange-600", color: "#f97316"}
  }

  @doc """
  渲染管理后台按钮组

  ## 属性
  - `buttons`: 按钮列表，每个按钮包含 text, action, class 等属性
  - `id`: 按钮组ID，用于标识按钮组
  - `class`: 额外的CSS类
  - `hidden`: 是否隐藏，默认为false（显示）
  """
  attr :buttons, :list, required: true
  attr :id, :string, default: nil
  attr :class, :string, default: ""
  attr :hidden, :boolean, default: false

  def admin_button_group(assigns) do
    assigns = assign_new(assigns, :button_group_id, fn -> "admin-button-group-#{:rand.uniform(10000)}" end)

    ~H"""
    <div
      class={[
        "flex gap-2 transition-opacity duration-200",
        @class,
        if(@hidden, do: "opacity-0 pointer-events-none", else: "opacity-100")
      ]}
      id={@button_group_id}
    >
      <%= for button <- @buttons do %>
        <button
          type={Map.get(button, :type, "button")}
          phx-click={Map.get(button, :action)}
          phx-target={Map.get(button, :target)}
          phx-value-id={Map.get(button, :id)}
          class={[
            "px-4 py-2 rounded-md font-medium transition-colors duration-200",
            Map.get(button, :class, "bg-blue-500 text-white hover:bg-blue-600")
          ]}
          style="background-color: var(--bg-color, #3b82f6) !important;"
          disabled={Map.get(button, :disabled, false)}
        >
          <%= if Map.get(button, :icon) do %>
            <i class={[Map.get(button, :icon), "mr-2"]}></i>
          <% end %>
          <%= Map.get(button, :text, "按钮") %>
        </button>
      <% end %>
    </div>
    """
  end

  @doc """
  渲染单个管理按钮

  ## 属性
  - `text`: 按钮文本
  - `action`: 点击事件
  - `icon`: 图标类名
  - `variant`: 按钮变体 (primary, secondary, success, danger, warning, info)
  - `size`: 按钮尺寸 (xs, sm, md, lg, xl)
  - `class`: 自定义样式类
  - `type`: 按钮类型
  - `disabled`: 是否禁用
  - `hidden`: 是否隐藏
  - `loading`: 是否显示加载状态
  """
  attr :text, :string, required: true
  attr :action, :string, default: nil
  attr :target, :string, default: nil
  attr :icon, :string, default: nil
  attr :variant, :atom, default: :primary
  attr :size, :atom, default: :md
  attr :class, :string, default: ""
  attr :type, :string, default: "button"
  attr :disabled, :boolean, default: false
  attr :id, :string, default: nil
  attr :hidden, :boolean, default: false
  attr :loading, :boolean, default: false
  attr :form, :string, default: nil

  def admin_button(assigns) do
    assigns =
      assigns
      |> assign_new(:button_id, fn -> "admin-button-#{:rand.uniform(10000)}" end)
      |> assign(:computed_class, compute_button_class(assigns))
      |> assign(:computed_style, compute_button_style(assigns))

    ~H"""
    <button
      type={@type}
      form={@form}
      phx-click={if @type == "submit", do: nil, else: @action}
      phx-target={if @type == "submit", do: nil, else: @target}
      phx-value-id={@id}
      id={@button_id}
      class={@computed_class}
      style={@computed_style}
      disabled={@disabled or @loading}
    >
      <%= if @loading do %>
        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
      <% else %>
        <%= if @icon do %>
          <i class={[@icon, "mr-2"]}></i>
        <% end %>
      <% end %>
      <%= @text %>
    </button>
    """
  end

  # 计算按钮样式类
  defp compute_button_class(assigns) do
    variant_class = get_variant_class(assigns.variant)
    size_class = get_size_class(assigns.size)

    [
      @button_base_classes,
      variant_class,
      size_class,
      assigns.class,
      if(assigns.hidden, do: "opacity-0 pointer-events-none", else: "opacity-100"),
      if(assigns.disabled, do: "opacity-50 cursor-not-allowed", else: "")
    ]
    |> Enum.filter(&(&1 != ""))
    |> Enum.join(" ")
  end

  # 计算按钮内联样式
  defp compute_button_style(assigns) do
    variant_info = Map.get(@button_variants, assigns.variant, @button_variants.primary)
    "background-color: #{variant_info.color} !important;"
  end

  # 获取变体样式类
  defp get_variant_class(variant) do
    variant_info = Map.get(@button_variants, variant, @button_variants.primary)
    variant_info.class
  end

  # 获取尺寸样式类
  defp get_size_class(size) do
    Map.get(@button_sizes, size, @button_sizes.md)
  end

  @doc """
  渲染危险操作按钮（带确认）

  ## 属性
  - `text`: 按钮文本
  - `action`: 点击事件
  - `confirm_message`: 确认消息
  - `icon`: 图标类名
  """
  attr :text, :string, required: true
  attr :action, :string, required: true
  attr :target, :string, default: nil
  attr :confirm_message, :string, required: true
  attr :class, :string, default: "bg-red-500 text-white hover:bg-red-600"
  attr :icon, :string, default: nil
  attr :id, :string, default: nil
  attr :bg_color, :string, default: "#ef4444"

  def danger_button(assigns) do
    ~H"""
    <button
      type="button"
      phx-click={@action}
      phx-target={@target}
      phx-value-id={@id}
      data-confirm={@confirm_message}
      class={[
        "px-4 py-2 rounded-md font-medium transition-colors duration-200",
        @class
      ]}
      style={"background-color: #{@bg_color} !important;"}
    >
      <%= if @icon do %>
        <i class={[@icon, "mr-2"]}></i>
      <% end %>
      <%= @text %>
    </button>
    """
  end

  @doc """
  渲染成功操作按钮

  ## 属性
  - `text`: 按钮文本
  - `action`: 点击事件
  - `icon`: 图标类名
  """
  attr :text, :string, required: true
  attr :action, :string, required: true
  attr :target, :string, default: nil
  attr :icon, :string, default: nil
  attr :id, :string, default: nil
  attr :bg_color, :string, default: "#22c55e"

  def success_button(assigns) do
    assigns = assign(assigns, :variant, :success)
    admin_button(assigns)
  end

  @doc """
  渲染警告操作按钮

  ## 属性
  - `text`: 按钮文本
  - `action`: 点击事件
  - `icon`: 图标类名
  """
  attr :text, :string, required: true
  attr :action, :string, required: true
  attr :target, :string, default: nil
  attr :icon, :string, default: nil
  attr :id, :string, default: nil

  def warning_button(assigns) do
    assigns = assign(assigns, :variant, :warning)
    admin_button(assigns)
  end

  @doc """
  渲染刷新按钮

  ## 属性
  - `action`: 点击事件，默认为 "refresh"
  - `target`: 目标组件
  """
  attr :action, :string, default: "refresh"
  attr :target, :string, default: nil

  def refresh_button(assigns) do
    ~H"""
    <button
      type="button"
      phx-click={@action}
      phx-target={@target}
      class="px-4 py-2 bg-white text-black border border-black rounded-md hover:bg-gray-300 transition-colors duration-200 font-medium"
      style="background-color: #ffffff !important; border-color: #000000 !important;"
    >
      <i class="fas fa-sync-alt mr-2"></i>刷新
    </button>
    """
  end

  @doc """
  渲染取消按钮

  ## 属性
  - `text`: 按钮文本，默认为 "取消"
  - `action`: 点击事件，默认为 "cancel"
  - `target`: 目标组件
  - `icon`: 图标类名，默认为 "fas fa-times"
  - `id`: 按钮ID
  """
  attr :text, :string, default: "取消"
  attr :action, :string, default: "cancel"
  attr :target, :string, default: nil
  attr :icon, :string, default: "fas fa-times"
  attr :id, :string, default: nil

  def cancel_button(assigns) do
    assigns = assign(assigns, :variant, :cancel)
    admin_button(assigns)
  end

  @doc """
  渲染信息按钮

  ## 属性
  - `text`: 按钮文本，默认为 "信息"
  - `action`: 点击事件，默认为 "info"
  - `target`: 目标组件
  - `icon`: 图标类名，默认为 "fas fa-info-circle"
  - `id`: 按钮ID
  """
  attr :text, :string, default: "信息"
  attr :action, :string, default: "info"
  attr :target, :string, default: nil
  attr :icon, :string, default: "fas fa-info-circle"
  attr :id, :string, default: nil

  def info_button(assigns) do
    assigns = assign(assigns, :variant, :info)
    admin_button(assigns)
  end

  @doc """
  渲染主要操作按钮

  ## 属性
  - `text`: 按钮文本，默认为 "确认"
  - `action`: 点击事件，默认为 "confirm"
  - `target`: 目标组件
  - `icon`: 图标类名，默认为 "fas fa-check"
  - `id`: 按钮ID
  """
  attr :text, :string, default: "确认"
  attr :action, :string, default: "confirm"
  attr :target, :string, default: nil
  attr :icon, :string, default: "fas fa-check"
  attr :id, :string, default: nil

  def primary_button(assigns) do
    assigns = assign(assigns, :variant, :primary)
    admin_button(assigns)
  end

  @doc """
  渲染次要操作按钮

  ## 属性
  - `text`: 按钮文本，默认为 "次要"
  - `action`: 点击事件，默认为 "secondary"
  - `target`: 目标组件
  - `icon`: 图标类名
  - `id`: 按钮ID
  """
  attr :text, :string, default: "次要"
  attr :action, :string, default: "secondary"
  attr :target, :string, default: nil
  attr :icon, :string, default: nil
  attr :id, :string, default: nil

  def secondary_button(assigns) do
    assigns = assign(assigns, :variant, :secondary)
    admin_button(assigns)
  end

  @doc """
  渲染编辑按钮

  ## 属性
  - `text`: 按钮文本，默认为 "编辑"
  - `action`: 点击事件，默认为 "edit"
  - `target`: 目标组件
  - `icon`: 图标类名，默认为 "fas fa-edit"
  - `id`: 按钮ID
  - `class`: 自定义CSS类
  """
  attr :text, :string, default: "编辑"
  attr :action, :string, default: "edit"
  attr :target, :string, default: nil
  attr :icon, :string, default: "fas fa-edit"
  attr :id, :string, default: nil
  attr :class, :string, default: nil

  def edit_button(assigns) do
    assigns = assign(assigns, :variant, :edit)
    admin_button(assigns)
  end

  @doc """
  渲染删除按钮

  ## 属性
  - `text`: 按钮文本，默认为 "删除"
  - `action`: 点击事件，默认为 "delete"
  - `target`: 目标组件
  - `icon`: 图标类名，默认为 "fas fa-trash"
  - `id`: 按钮ID
  - `confirm_message`: 确认消息，默认为 "确定要删除吗？"
  - `use_native_confirm`: 是否使用原生确认对话框，默认为 true
  - `class`: 自定义CSS类
  """
  attr :text, :string, default: "删除"
  attr :action, :string, default: "delete"
  attr :target, :string, default: nil
  attr :icon, :string, default: "fas fa-trash"
  attr :id, :string, default: nil
  attr :confirm_message, :string, default: "确定要删除吗？"
  attr :use_native_confirm, :boolean, default: true
  attr :class, :string, default: nil

  def delete_button(assigns) do
    assigns =
      assigns
      |> assign(:variant, :danger)
      |> assign_new(:data_confirm, fn ->
        if assigns[:use_native_confirm] != false, do: assigns[:confirm_message] || "确定要删除吗？", else: nil
      end)

    ~H"""
    <.admin_button
      text={@text}
      action={@action}
      target={@target}
      icon={@icon}
      id={@id}
      variant={@variant}
      class={@class}
      data-confirm={@data_confirm}
    />
    """
  end

  @doc """
  渲染保存按钮

  ## 属性
  - `text`: 按钮文本，默认为 "保存"
  - `action`: 点击事件，默认为 "save"
  - `target`: 目标组件
  - `icon`: 图标类名，默认为 "fas fa-save"
  - `id`: 按钮ID
  - `type`: 按钮类型，默认为 "button"，可设置为 "submit"
  """
  attr :text, :string, default: "保存"
  attr :action, :string, default: "save"
  attr :target, :string, default: nil
  attr :icon, :string, default: "fas fa-save"
  attr :id, :string, default: nil
  attr :type, :string, default: "button"
  attr :form, :string, default: nil

  def save_button(assigns) do
    assigns = assign(assigns, :variant, :success)
    admin_button(assigns)
  end

  @doc """
  渲染添加按钮

  ## 属性
  - `text`: 按钮文本，默认为 "添加"
  - `action`: 点击事件，默认为 "add"
  - `target`: 目标组件
  - `icon`: 图标类名，默认为 "fas fa-plus"
  - `id`: 按钮ID
  """
  attr :text, :string, default: "添加"
  attr :action, :string, default: "add"
  attr :target, :string, default: nil
  attr :icon, :string, default: "fas fa-plus"
  attr :id, :string, default: nil

  def add_button(assigns) do
    ~H"""
    <button
      type="button"
      phx-click={@action}
      phx-target={@target}
      phx-value-id={@id}
      class="px-4 py-2 bg-emerald-500 text-white rounded-md hover:bg-emerald-600 transition-colors duration-200 font-medium"
      style="background-color: #10b981 !important;"
    >
      <%= if @icon do %>
        <i class={[@icon, "mr-2"]}></i>
      <% end %>
      <%= @text %>
    </button>
    """
  end

  @doc """
  渲染查看按钮

  ## 属性
  - `text`: 按钮文本，默认为 "查看"
  - `action`: 点击事件，默认为 "view"
  - `target`: 目标组件
  - `icon`: 图标类名，默认为 "fas fa-eye"
  - `id`: 按钮ID
  """
  attr :text, :string, default: "查看"
  attr :action, :string, default: "view"
  attr :target, :string, default: nil
  attr :icon, :string, default: "fas fa-eye"
  attr :id, :string, default: nil

  def view_button(assigns) do
    ~H"""
    <button
      type="button"
      phx-click={@action}
      phx-target={@target}
      phx-value-id={@id}
      class="px-4 py-2 bg-cyan-500 text-white rounded-md hover:bg-cyan-600 transition-colors duration-200 font-medium"
      style="background-color: #06b6d4 !important;"
    >
      <%= if @icon do %>
        <i class={[@icon, "mr-2"]}></i>
      <% end %>
      <%= @text %>
    </button>
    """
  end

  @doc """
  渲染下载按钮

  ## 属性
  - `text`: 按钮文本，默认为 "下载"
  - `action`: 点击事件，默认为 "download"
  - `target`: 目标组件
  - `icon`: 图标类名，默认为 "fas fa-download"
  - `id`: 按钮ID
  """
  attr :text, :string, default: "下载"
  attr :action, :string, default: "download"
  attr :target, :string, default: nil
  attr :icon, :string, default: "fas fa-download"
  attr :id, :string, default: nil

  def download_button(assigns) do
    ~H"""
    <button
      type="button"
      phx-click={@action}
      phx-target={@target}
      phx-value-id={@id}
      class="px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600 transition-colors duration-200 font-medium"
      style="background-color: #8b5cf6 !important;"
    >
      <%= if @icon do %>
        <i class={[@icon, "mr-2"]}></i>
      <% end %>
      <%= @text %>
    </button>
    """
  end

  @doc """
  渲染分页按钮

  ## 属性
  - `text`: 按钮文本
  - `action`: 点击事件，默认为 "page_change"
  - `target`: 目标组件
  - `icon`: 图标类名
  - `page`: 页码
  """
  attr :text, :string, required: true
  attr :action, :string, default: "page_change"
  attr :target, :string, default: nil
  attr :icon, :string, default: nil
  attr :page, :integer, required: true

  def pagination_button(assigns) do
    ~H"""
    <button
      type="button"
      phx-click={@action}
      phx-target={@target}
      phx-value-page={@page}
      class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 text-sm font-medium"
      style="background-color: #ffffff !important; border-color: #d1d5db !important;"
    >
      <%= if @icon do %>
        <i class={[@icon, "mr-2"]}></i>
      <% end %>
      <%= @text %>
    </button>
    """
  end

  @doc """
  渲染上传按钮

  ## 属性
  - `text`: 按钮文本，默认为 "上传"
  - `action`: 点击事件，默认为 "upload"
  - `target`: 目标组件
  - `icon`: 图标类名，默认为 "fas fa-upload"
  - `id`: 按钮ID
  """
  attr :text, :string, default: "上传"
  attr :action, :string, default: "upload"
  attr :target, :string, default: nil
  attr :icon, :string, default: "fas fa-upload"
  attr :id, :string, default: nil

  def upload_button(assigns) do
    ~H"""
    <button
      type="button"
      phx-click={@action}
      phx-target={@target}
      phx-value-id={@id}
      class="px-4 py-2 bg-teal-500 text-white rounded-md hover:bg-teal-600 transition-colors duration-200 font-medium"
      style="background-color: #14b8a6 !important;"
    >
      <%= if @icon do %>
        <i class={[@icon, "mr-2"]}></i>
      <% end %>
      <%= @text %>
    </button>
    """
  end

  @doc """
  渲染搜索按钮

  ## 属性
  - `text`: 按钮文本，默认为 "搜索"
  - `action`: 点击事件，默认为 "search"
  - `target`: 目标组件
  - `icon`: 图标类名，默认为 "fas fa-search"
  - `id`: 按钮ID
  - `query`: 搜索查询参数
  """
  attr :text, :string, default: "搜索"
  attr :action, :string, default: "search"
  attr :target, :string, default: nil
  attr :icon, :string, default: "fas fa-search"
  attr :id, :string, default: nil
  attr :query, :string, default: ""

  def search_button(assigns) do
    ~H"""
    <button
      type="button"
      phx-click={@action}
      phx-target={@target}
      phx-value-id={@id}
      phx-value-query={@query}
      class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors duration-200 font-medium"
      style="background-color: #3b82f6 !important;"
    >
      <%= if @icon do %>
        <i class={[@icon, if(@text != "", do: " mr-2", else: "")]}></i>
      <% end %>
      <%= @text %>
    </button>
    """
  end
end
