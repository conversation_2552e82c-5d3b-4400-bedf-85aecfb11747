defmodule Cypridina.Ledger.Repositories.LedgerRepository do
  @moduledoc """
  账本系统数据访问仓储
  
  提供账本系统相关的数据访问抽象：
  - 账户管理
  - 转账记录查询
  - 余额查询和统计
  - 数据缓存管理
  """

  require Logger
  alias Cypridina.Ledger.{Account, Transfer, Balance}
  alias Cypridina.Ledger.Repositories.QueryBuilders.LedgerQueryBuilder
  require Ash.Query

  # 常量定义
  @cache_ttl 300  # 5分钟缓存
  @default_limit 20
  @max_limit 100

  # ============================================================================
  # 账户管理
  # ============================================================================

  @doc """
  根据ID获取账户

  ## 参数
  - `account_id` - 账户ID
  - `options` - 选项

  ## 返回
  - `{:ok, account}` - 成功
  - `{:error, :not_found}` - 账户不存在
  - `{:error, reason}` - 其他错误
  """
  def get_account_by_id(account_id, options \\ []) do
    Logger.debug("🔍 [账本仓储] 根据ID获取账户: #{account_id}")
    
    preload = Keyword.get(options, :preload, [])
    use_cache = Keyword.get(options, :use_cache, true)
    
    cache_key = build_cache_key("account", account_id, preload)
    
    if use_cache do
      case get_from_cache(cache_key) do
        {:ok, account} -> {:ok, account}
        :miss -> fetch_and_cache_account_by_id(account_id, preload, cache_key)
      end
    else
      fetch_account_by_id(account_id, preload)
    end
  end

  @doc """
  根据标识符获取账户

  ## 参数
  - `identifier` - 账户标识符
  - `options` - 选项

  ## 返回
  - `{:ok, account}` - 成功
  - `{:error, :not_found}` - 账户不存在
  - `{:error, reason}` - 其他错误
  """
  def get_account_by_identifier(identifier, options \\ []) do
    Logger.debug("🔍 [账本仓储] 根据标识符获取账户: #{identifier}")
    
    try do
      case Account.get_by_identifier(identifier) do
        {:ok, account} ->
          if options[:preload] do
            {:ok, Ash.load!(account, options[:preload])}
          else
            {:ok, account}
          end
        {:error, %Ash.Error.Query.NotFound{}} -> {:error, :not_found}
        {:error, error} ->
          Logger.error("❌ [账本仓储] 获取账户失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [账本仓储] 获取账户异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  获取用户账户

  ## 参数
  - `user_id` - 用户ID
  - `currency` - 货币类型
  - `options` - 选项

  ## 返回
  - `{:ok, account}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_account(user_id, currency \\ :XAA, options \\ []) do
    Logger.debug("👤 [账本仓储] 获取用户账户: #{user_id} - #{currency}")
    
    identifier = "user:#{currency}:#{user_id}"
    get_account_by_identifier(identifier, options)
  end

  @doc """
  获取系统账户

  ## 参数
  - `account_type` - 账户类型
  - `options` - 选项

  ## 返回
  - `{:ok, account}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_system_account(account_type \\ :main, options \\ []) do
    Logger.debug("🏛️ [账本仓储] 获取系统账户: #{account_type}")
    
    identifier = "system:#{account_type}"
    get_account_by_identifier(identifier, options)
  end

  @doc """
  分页查询账户

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, %{accounts: accounts, page_info: page_info}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_accounts_paginated(params \\ %{}) do
    Logger.debug("📋 [账本仓储] 分页查询账户")
    
    try do
      normalized_params = normalize_list_params(params)
      
      with {:ok, query} <- LedgerQueryBuilder.build_account_list_query(normalized_params),
           {:ok, results} <- execute_paginated_query(query, normalized_params, Account) do
        {:ok, %{accounts: results.items, page_info: results.page_info}}
      else
        {:error, reason} -> {:error, reason}
      end
    rescue
      error ->
        Logger.error("❌ [账本仓储] 分页查询异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # ============================================================================
  # 转账记录管理
  # ============================================================================

  @doc """
  根据ID获取转账记录

  ## 参数
  - `transfer_id` - 转账ID
  - `options` - 选项

  ## 返回
  - `{:ok, transfer}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_transfer_by_id(transfer_id, options \\ []) do
    Logger.debug("🔍 [账本仓储] 根据ID获取转账记录: #{transfer_id}")
    
    preload = Keyword.get(options, :preload, [:from_account, :to_account])
    
    try do
      case Transfer
           |> maybe_preload(preload)
           |> Ash.read(transfer_id) do
        {:ok, transfer} -> {:ok, transfer}
        {:error, %Ash.Error.Query.NotFound{}} -> {:error, :not_found}
        {:error, error} ->
          Logger.error("❌ [账本仓储] 获取转账记录失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [账本仓储] 获取转账记录异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  分页查询转账记录

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, %{transfers: transfers, page_info: page_info}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_transfers_paginated(params \\ %{}) do
    Logger.debug("📋 [账本仓储] 分页查询转账记录")
    
    try do
      normalized_params = normalize_list_params(params)
      
      with {:ok, query} <- LedgerQueryBuilder.build_transfer_list_query(normalized_params),
           {:ok, results} <- execute_paginated_query(query, normalized_params, Transfer) do
        {:ok, %{transfers: results.items, page_info: results.page_info}}
      else
        {:error, reason} -> {:error, reason}
      end
    rescue
      error ->
        Logger.error("❌ [账本仓储] 分页查询异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  获取账户转账记录

  ## 参数
  - `account_id` - 账户ID
  - `options` - 选项

  ## 返回
  - `{:ok, transfers}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_account_transfers(account_id, options \\ []) do
    Logger.debug("💸 [账本仓储] 获取账户转账记录: #{account_id}")
    
    try do
      query = Transfer
      |> Ash.Query.filter(from_account_id == ^account_id or to_account_id == ^account_id)
      |> Ash.Query.load([:from_account, :to_account])
      |> Ash.Query.sort(inserted_at: :desc)
      |> maybe_limit(options[:limit])
      
      case Ash.read(query) do
        {:ok, transfers} -> {:ok, transfers}
        {:error, error} ->
          Logger.error("❌ [账本仓储] 获取账户转账记录失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [账本仓储] 获取账户转账记录异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # ============================================================================
  # 余额管理
  # ============================================================================

  @doc """
  获取账户余额

  ## 参数
  - `account_id` - 账户ID
  - `options` - 选项

  ## 返回
  - `{:ok, balance}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_account_balance(account_id, options \\ []) do
    Logger.debug("💰 [账本仓储] 获取账户余额: #{account_id}")
    
    try do
      case get_account_by_id(account_id, preload: [:balance_as_of]) do
        {:ok, account} ->
          balance = Map.get(account, :balance_as_of, Money.new(0, :XAA))
          {:ok, balance}
        error -> error
      end
    rescue
      error ->
        Logger.error("❌ [账本仓储] 获取账户余额异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  获取用户余额

  ## 参数
  - `user_id` - 用户ID
  - `currency` - 货币类型
  - `options` - 选项

  ## 返回
  - `{:ok, balance}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_balance(user_id, currency \\ :XAA, options \\ []) do
    Logger.debug("👤 [账本仓储] 获取用户余额: #{user_id} - #{currency}")
    
    case get_user_account(user_id, currency, preload: [:balance_as_of]) do
      {:ok, account} ->
        balance = Map.get(account, :balance_as_of, Money.new(0, currency))
        {:ok, balance}
      error -> error
    end
  end

  # ============================================================================
  # 统计查询
  # ============================================================================

  @doc """
  获取账本统计信息

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, statistics}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_statistics(options \\ []) do
    Logger.debug("📊 [账本仓储] 获取账本统计")
    
    try do
      stats = %{
        total_accounts: get_total_accounts(),
        user_accounts: get_user_accounts_count(),
        system_accounts: get_system_accounts_count(),
        total_transfers: get_total_transfers(),
        total_volume: get_total_transfer_volume(),
        recent_transfers: get_recent_transfers_count(options[:days] || 7)
      }
      
      {:ok, stats}
    rescue
      error ->
        Logger.error("❌ [账本仓储] 获取统计异常: #{inspect(error)}")
        {:error, :statistics_error}
    end
  end

  # ============================================================================
  # 缓存管理
  # ============================================================================

  @doc """
  清除账本相关缓存

  ## 参数
  - `cache_type` - 缓存类型
  - `id` - ID（可选）
  """
  def clear_cache(cache_type, id \\ nil) do
    cache_patterns = case {cache_type, id} do
      {:account, nil} -> ["account:*"]
      {:account, account_id} -> ["account:#{account_id}:*"]
      {:transfer, nil} -> ["transfer:*"]
      {:transfer, transfer_id} -> ["transfer:#{transfer_id}:*"]
      {:all, _} -> ["account:*", "transfer:*", "balance:*"]
    end
    
    Logger.debug("🧹 [账本仓储] 清除缓存: #{inspect(cache_patterns)}")
    Enum.each(cache_patterns, &clear_cache_pattern/1)
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 标准化列表参数
  defp normalize_list_params(params) do
    %{
      page: max(1, params[:page] || 1),
      limit: min(params[:limit] || @default_limit, @max_limit),
      search: String.trim(params[:search] || ""),
      filters: params[:filters] || %{},
      sort: params[:sort] || [inserted_at: :desc],
      preload: params[:preload] || []
    }
  end

  # 获取并缓存账户
  defp fetch_and_cache_account_by_id(account_id, preload, cache_key) do
    case fetch_account_by_id(account_id, preload) do
      {:ok, account} ->
        cache_put(cache_key, account, @cache_ttl)
        {:ok, account}
      error -> error
    end
  end

  # 获取账户
  defp fetch_account_by_id(account_id, preload) do
    try do
      case Account
           |> maybe_preload(preload)
           |> Ash.read(account_id) do
        {:ok, account} -> {:ok, account}
        {:error, %Ash.Error.Query.NotFound{}} -> {:error, :not_found}
        {:error, error} ->
          Logger.error("❌ [账本仓储] 获取账户失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [账本仓储] 获取账户异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # 执行分页查询
  defp execute_paginated_query(query, params, resource) do
    offset = (params.page - 1) * params.limit
    
    case query
         |> maybe_preload(params.preload)
         |> Ash.Query.page(count: true, limit: params.limit, offset: offset)
         |> Ash.read() do
      {:ok, %Ash.Page.Offset{results: items, count: total_count}} ->
        page_info = %{
          total_count: total_count,
          page: params.page,
          limit: params.limit,
          total_pages: ceil(total_count / params.limit)
        }
        {:ok, %{items: items, page_info: page_info}}
      {:error, error} ->
        Logger.error("❌ [账本仓储] 分页查询失败: #{inspect(error)}")
        {:error, :query_failed}
    end
  end

  # 可能限制数量
  defp maybe_limit(query, nil), do: query
  defp maybe_limit(query, limit) when is_integer(limit) and limit > 0 do
    Ash.Query.limit(query, limit)
  end
  defp maybe_limit(query, _), do: query

  # 可能预加载关联数据
  defp maybe_preload(query, []), do: query
  defp maybe_preload(query, preload) when is_list(preload) do
    Ash.Query.load(query, preload)
  end

  # 统计函数（简化实现）
  defp get_total_accounts do
    case Account |> Ash.Query.aggregate(:count, :id) |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_user_accounts_count do
    case Account 
         |> Ash.Query.filter(account_type == :user)
         |> Ash.Query.aggregate(:count, :id) 
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_system_accounts_count do
    case Account 
         |> Ash.Query.filter(account_type == :system)
         |> Ash.Query.aggregate(:count, :id) 
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_total_transfers do
    case Transfer |> Ash.Query.aggregate(:count, :id) |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_total_transfer_volume do
    # TODO: 实现转账总量统计
    Money.new(0, :XAA)
  end

  defp get_recent_transfers_count(days) do
    date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
    
    case Transfer 
         |> Ash.Query.filter(inserted_at >= ^date_threshold)
         |> Ash.Query.aggregate(:count, :id) 
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  # 构建缓存键
  defp build_cache_key(prefix, id, extra \\ nil) do
    case extra do
      nil -> "#{prefix}:#{id}"
      extra -> "#{prefix}:#{id}:#{:erlang.phash2(extra)}"
    end
  end

  # 缓存操作（需要实现具体的缓存后端）
  defp get_from_cache(_key), do: :miss
  defp cache_put(_key, _value, _ttl), do: :ok
  defp clear_cache_pattern(_pattern), do: :ok
end
