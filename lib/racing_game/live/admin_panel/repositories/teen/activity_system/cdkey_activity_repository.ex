defmodule RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.CdkeyActivityRepository do
  @moduledoc """
  🔑 CDKEY活动仓储层
  
  负责CDKEY活动相关的数据访问操作，包括：
  - CDKEY的CRUD操作
  - 批次管理和查询
  - 使用状态管理
  - 过期处理
  - 批量生成CDKEY
  - 使用统计查询
  """

  require Logger
  alias Teen.ActivitySystem.CdkeyActivity
  alias Ash.Query

  # ==================== 基础CRUD操作 ====================

  @doc """
  创建CDKEY
  
  ## 参数
  - `cdkey_data` - CDKEY数据
  - `options` - 选项参数
  
  ## 返回
  - `{:ok, cdkey}` - 创建成功
  - `{:error, reason}` - 创建失败
  """
  def create_cdkey(cdkey_data, options \\ []) do
    Logger.info("🔑 [CDKEY活动仓储] 创建CDKEY: #{inspect(cdkey_data[:batch_name])}")
    
    try do
      result = CdkeyActivity
      |> Ash.Changeset.for_create(:create, cdkey_data)
      |> Ash.create()
      
      case result do
        {:ok, cdkey} ->
          Logger.info("✅ [CDKEY活动仓储] CDKEY创建成功: #{cdkey.cdkey}")
          maybe_load_associations({:ok, cdkey}, options)
          
        {:error, reason} ->
          Logger.error("❌ [CDKEY活动仓储] CDKEY创建失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [CDKEY活动仓储] 创建CDKEY异常: #{inspect(exception)}")
        {:error, :create_failed}
    end
  end

  @doc """
  批量生成CDKEY
  """
  def generate_cdkey_batch(batch_data, options \\ []) do
    Logger.info("🔑 [CDKEY活动仓储] 批量生成CDKEY: #{inspect(batch_data[:batch_name])}")
    
    try do
      result = CdkeyActivity
      |> Ash.Changeset.for_create(:generate_batch, batch_data)
      |> Ash.create()
      
      case result do
        {:ok, cdkey} ->
          Logger.info("✅ [CDKEY活动仓储] CDKEY批量生成成功: #{cdkey.cdkey}")
          maybe_load_associations({:ok, cdkey}, options)
          
        {:error, reason} ->
          Logger.error("❌ [CDKEY活动仓储] CDKEY批量生成失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [CDKEY活动仓储] 批量生成CDKEY异常: #{inspect(exception)}")
        {:error, :generate_failed}
    end
  end

  @doc """
  根据CDKEY字符串获取CDKEY
  """
  def get_cdkey_by_code(cdkey_code, options \\ []) do
    Logger.info("🔑 [CDKEY活动仓储] 根据代码获取CDKEY: #{cdkey_code}")
    
    try do
      result = CdkeyActivity
      |> Ash.Query.for_read(:read)
      |> Ash.Query.filter(cdkey == ^cdkey_code)
      |> maybe_load_associations(options)
      |> Ash.read_one()
      
      case result do
        {:ok, nil} ->
          Logger.warn("⚠️ [CDKEY活动仓储] CDKEY不存在: #{cdkey_code}")
          {:error, :not_found}
          
        {:ok, cdkey} ->
          Logger.info("✅ [CDKEY活动仓储] 获取CDKEY成功: #{cdkey.batch_name}")
          {:ok, cdkey}
          
        {:error, reason} ->
          Logger.error("❌ [CDKEY活动仓储] 获取CDKEY失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [CDKEY活动仓储] 获取CDKEY异常: #{inspect(exception)}")
        {:error, :query_failed}
    end
  end

  @doc """
  根据ID获取CDKEY
  """
  def get_cdkey(cdkey_id, options \\ []) do
    Logger.info("🔑 [CDKEY活动仓储] 获取CDKEY: #{cdkey_id}")
    
    try do
      result = CdkeyActivity
      |> Ash.Query.for_read(:read)
      |> Ash.Query.filter(id == ^cdkey_id)
      |> maybe_load_associations(options)
      |> Ash.read_one()
      
      case result do
        {:ok, nil} ->
          Logger.warn("⚠️ [CDKEY活动仓储] CDKEY不存在: #{cdkey_id}")
          {:error, :not_found}
          
        {:ok, cdkey} ->
          Logger.info("✅ [CDKEY活动仓储] 获取CDKEY成功: #{cdkey.cdkey}")
          {:ok, cdkey}
          
        {:error, reason} ->
          Logger.error("❌ [CDKEY活动仓储] 获取CDKEY失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [CDKEY活动仓储] 获取CDKEY异常: #{inspect(exception)}")
        {:error, :query_failed}
    end
  end

  @doc """
  使用CDKEY
  """
  def use_cdkey(cdkey_id, user_id, options \\ []) do
    Logger.info("🔑 [CDKEY活动仓储] 使用CDKEY: #{cdkey_id} by #{user_id}")
    
    try do
      with {:ok, cdkey} <- get_cdkey(cdkey_id),
           {:ok, updated_cdkey} <- CdkeyActivity.use_cdkey(cdkey, %{user_id: user_id}) do
        Logger.info("✅ [CDKEY活动仓储] CDKEY使用成功: #{updated_cdkey.cdkey}")
        maybe_load_associations({:ok, updated_cdkey}, options)
      else
        {:error, reason} ->
          Logger.error("❌ [CDKEY活动仓储] 使用CDKEY失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [CDKEY活动仓储] 使用CDKEY异常: #{inspect(exception)}")
        {:error, :use_failed}
    end
  end

  # ==================== 批次查询操作 ====================

  @doc """
  根据批次名称获取CDKEY列表
  """
  def list_cdkeys_by_batch(batch_name, options \\ []) do
    Logger.info("🔑 [CDKEY活动仓储] 根据批次获取CDKEY列表: #{batch_name}")
    
    try do
      query = CdkeyActivity
      |> Ash.Query.for_read(:list_by_batch, %{batch_name: batch_name})
      |> apply_sorting(options)
      |> maybe_load_associations(options)
      
      result = if options[:paginate] do
        Ash.read(query, page: build_page_params(options))
      else
        Ash.read(query)
      end
      
      case result do
        {:ok, cdkeys} when is_list(cdkeys) ->
          Logger.info("✅ [CDKEY活动仓储] 获取批次CDKEY成功: #{length(cdkeys)}条")
          {:ok, cdkeys}
          
        {:ok, %{results: cdkeys, more?: more?, count: count}} ->
          Logger.info("✅ [CDKEY活动仓储] 分页获取批次CDKEY成功: #{length(cdkeys)}条，总数: #{count}")
          {:ok, %{cdkeys: cdkeys, more?: more?, count: count}}
          
        {:error, reason} ->
          Logger.error("❌ [CDKEY活动仓储] 获取批次CDKEY失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [CDKEY活动仓储] 获取批次CDKEY异常: #{inspect(exception)}")
        {:error, :query_failed}
    end
  end

  @doc """
  获取未使用的CDKEY列表
  """
  def list_unused_cdkeys(options \\ []) do
    Logger.info("🔑 [CDKEY活动仓储] 获取未使用的CDKEY列表")
    
    try do
      query = CdkeyActivity
      |> Ash.Query.for_read(:list_unused_keys)
      |> apply_list_filters(options)
      |> apply_sorting(options)
      |> maybe_load_associations(options)
      
      result = if options[:paginate] do
        Ash.read(query, page: build_page_params(options))
      else
        Ash.read(query)
      end
      
      case result do
        {:ok, cdkeys} when is_list(cdkeys) ->
          Logger.info("✅ [CDKEY活动仓储] 获取未使用CDKEY成功: #{length(cdkeys)}条")
          {:ok, cdkeys}
          
        {:ok, %{results: cdkeys, more?: more?, count: count}} ->
          Logger.info("✅ [CDKEY活动仓储] 分页获取未使用CDKEY成功: #{length(cdkeys)}条，总数: #{count}")
          {:ok, %{cdkeys: cdkeys, more?: more?, count: count}}
          
        {:error, reason} ->
          Logger.error("❌ [CDKEY活动仓储] 获取未使用CDKEY失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [CDKEY活动仓储] 获取未使用CDKEY异常: #{inspect(exception)}")
        {:error, :query_failed}
    end
  end

  @doc """
  获取已使用的CDKEY列表
  """
  def list_used_cdkeys(options \\ []) do
    Logger.info("🔑 [CDKEY活动仓储] 获取已使用的CDKEY列表")
    
    try do
      query = CdkeyActivity
      |> Ash.Query.for_read(:list_used_keys)
      |> apply_list_filters(options)
      |> apply_sorting(options)
      |> maybe_load_associations(options)
      
      result = if options[:paginate] do
        Ash.read(query, page: build_page_params(options))
      else
        Ash.read(query)
      end
      
      case result do
        {:ok, cdkeys} when is_list(cdkeys) ->
          Logger.info("✅ [CDKEY活动仓储] 获取已使用CDKEY成功: #{length(cdkeys)}条")
          {:ok, cdkeys}
          
        {:ok, %{results: cdkeys, more?: more?, count: count}} ->
          Logger.info("✅ [CDKEY活动仓储] 分页获取已使用CDKEY成功: #{length(cdkeys)}条，总数: #{count}")
          {:ok, %{cdkeys: cdkeys, more?: more?, count: count}}
          
        {:error, reason} ->
          Logger.error("❌ [CDKEY活动仓储] 获取已使用CDKEY失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [CDKEY活动仓储] 获取已使用CDKEY异常: #{inspect(exception)}")
        {:error, :query_failed}
    end
  end

  @doc """
  获取所有CDKEY列表
  """
  def list_cdkeys(options \\ []) do
    Logger.info("🔑 [CDKEY活动仓储] 获取CDKEY列表")
    
    try do
      query = CdkeyActivity
      |> Ash.Query.for_read(:read)
      |> apply_list_filters(options)
      |> apply_sorting(options)
      |> maybe_load_associations(options)
      
      result = if options[:paginate] do
        Ash.read(query, page: build_page_params(options))
      else
        Ash.read(query)
      end
      
      case result do
        {:ok, cdkeys} when is_list(cdkeys) ->
          Logger.info("✅ [CDKEY活动仓储] 获取CDKEY列表成功: #{length(cdkeys)}条")
          {:ok, cdkeys}
          
        {:ok, %{results: cdkeys, more?: more?, count: count}} ->
          Logger.info("✅ [CDKEY活动仓储] 分页获取CDKEY成功: #{length(cdkeys)}条，总数: #{count}")
          {:ok, %{cdkeys: cdkeys, more?: more?, count: count}}
          
        {:error, reason} ->
          Logger.error("❌ [CDKEY活动仓储] 获取CDKEY列表失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [CDKEY活动仓储] 获取CDKEY列表异常: #{inspect(exception)}")
        {:error, :query_failed}
    end
  end

  # ==================== 私有辅助函数 ====================

  # 应用列表过滤条件
  defp apply_list_filters(query, options) do
    query
    |> maybe_filter_by_batch(options[:batch_name])
    |> maybe_filter_by_status(options[:status])
    |> maybe_filter_by_user(options[:user_id])
    |> maybe_filter_by_expiry(options[:expired])
  end

  # 按批次名称过滤
  defp maybe_filter_by_batch(query, nil), do: query
  defp maybe_filter_by_batch(query, batch_name) when is_binary(batch_name) do
    Ash.Query.filter(query, contains(batch_name, ^batch_name))
  end

  # 按状态过滤
  defp maybe_filter_by_status(query, nil), do: query
  defp maybe_filter_by_status(query, status) when is_integer(status) do
    Ash.Query.filter(query, status == ^status)
  end

  # 按用户过滤
  defp maybe_filter_by_user(query, nil), do: query
  defp maybe_filter_by_user(query, user_id) when is_binary(user_id) do
    Ash.Query.filter(query, used_by_user_id == ^user_id)
  end

  # 按过期状态过滤
  defp maybe_filter_by_expiry(query, nil), do: query
  defp maybe_filter_by_expiry(query, true) do
    now = DateTime.utc_now()
    Ash.Query.filter(query, not is_nil(expires_at) and expires_at < ^now)
  end
  defp maybe_filter_by_expiry(query, false) do
    now = DateTime.utc_now()
    Ash.Query.filter(query, is_nil(expires_at) or expires_at >= ^now)
  end

  # 应用排序
  defp apply_sorting(query, options) do
    case options[:sort] do
      nil -> Ash.Query.sort(query, inserted_at: :desc)
      sort_params -> Ash.Query.sort(query, sort_params)
    end
  end

  # 构建分页参数
  defp build_page_params(options) do
    %{
      limit: options[:limit] || 20,
      offset: options[:offset] || 0,
      count: true
    }
  end

  # 可能加载关联数据
  defp maybe_load_associations({:ok, data}, options) do
    case options[:load] do
      nil -> {:ok, data}
      associations -> 
        case Ash.load(data, associations) do
          {:ok, loaded_data} -> {:ok, loaded_data}
          {:error, reason} -> {:error, reason}
        end
    end
  end

  defp maybe_load_associations(query, options) do
    case options[:load] do
      nil -> query
      associations -> Ash.Query.load(query, associations)
    end
  end
end
