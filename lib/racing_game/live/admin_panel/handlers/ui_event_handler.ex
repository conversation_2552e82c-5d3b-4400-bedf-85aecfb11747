defmodule RacingGame.Live.AdminPanel.Handlers.UiEventHandler do
  @moduledoc """
  UI交互事件处理器

  处理系统通信管理组件中的分页、界面交互等操作。
  """

  require Logger
  alias RacingGame.Utils.PaginationHandler
  alias RacingGame.Live.AdminPanel.Operations.SearchOperations

  @doc """
  处理页面切换事件

  ## 参数
  - `params` - 分页参数
  - `socket` - Phoenix LiveView socket

  ## 返回
  - `{:noreply, socket}` - 更新后的socket
  """
  def handle_page_change(params, socket) do
    Logger.info("📄 [界面交互] 页面切换事件")
    PaginationHandler.handle_page_change(params, socket, &SearchOperations.load_communications/1)
  end

  @doc """
  处理每页数量变化事件

  ## 参数
  - `params` - 分页参数
  - `socket` - Phoenix LiveView socket

  ## 返回
  - `{:noreply, socket}` - 更新后的socket
  """
  def handle_per_page_change(params, socket) do
    Logger.info("📄 [界面交互] 每页数量变化事件")
    PaginationHandler.handle_per_page_change(params, socket, &SearchOperations.load_communications/1)
  end

  @doc """
  处理页面跳转事件

  ## 参数
  - `params` - 分页参数
  - `socket` - Phoenix LiveView socket

  ## 返回
  - `{:noreply, socket}` - 更新后的socket
  """
  def handle_page_jump(params, socket) do
    Logger.info("📄 [界面交互] 页面跳转事件")
    PaginationHandler.handle_page_jump(params, socket, &SearchOperations.load_communications/1)
  end
end
