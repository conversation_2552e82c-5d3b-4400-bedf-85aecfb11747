defmodule RacingGame.Live.AdminPanel.Operations.CrudOperations do
  @moduledoc """
  CRUD操作模块

  处理系统通信管理组件中的创建、更新、删除操作。

  ## 功能特性
  - 安全的数据创建和更新
  - 完整的删除约束检查
  - 状态切换操作
  - 详细的错误处理和日志记录
  - 用户友好的成功和错误提示
  - 异常安全处理

  ## 支持的操作
  - 创建通信记录
  - 更新通信记录
  - 删除通信记录（带约束检查）
  - 状态切换（启用/禁用）
  """

  require Logger
  alias RacingGame.SystemCommunication
  alias RacingGame.Live.AdminPanel.Operations.SearchOperations
  alias RacingGame.Live.AdminPanel.Validators.DeleteConstraintValidator
  import RacingGame.Utils.DialogHelper
  import Phoenix.Component, only: [assign: 3]
  import Phoenix.LiveView, only: [put_flash: 3]

  # 常量定义
  @operation_timeout 10_000
  @type_display_names %{
    :message => "系统消息",
    :announcement => "系统公告",
    :notification => "系统通知",
    "message" => "系统消息",
    "announcement" => "系统公告",
    "notification" => "系统通知"
  }
  @field_display_names %{
    :title => "标题",
    :content => "内容",
    :priority => "优先级",
    :recipient_type => "接收者类型",
    :recipient_id => "接收者ID",
    :active => "状态",
    :type => "类型",
    :expires_at => "过期时间"
  }

  @doc """
  处理创建通信操作

  ## 参数
  - `socket` - Phoenix LiveView socket
  - `validated_params` - 已验证的参数

  ## 返回
  - `{:noreply, socket}` - 更新后的socket
  """
  def handle_create_communication(socket, validated_params) do
    Logger.info("📝 [CRUD操作] 开始创建通信")
    Logger.debug("📝 [CRUD操作] 验证参数: #{inspect(validated_params)}")

    try do
      case prepare_and_create_communication(socket, validated_params) do
        {:ok, communication} ->
          Logger.info("✅ [CRUD操作] 通信创建成功: #{communication.title}")
          handle_create_success(socket, communication)
        {:error, error} ->
          Logger.error("❌ [CRUD操作] 通信创建失败: #{inspect(error)}")
          handle_create_error(socket, error)
      end
    rescue
      error ->
        Logger.error("❌ [CRUD操作] 创建通信异常: #{inspect(error)}")
        handle_create_exception(socket, error)
    end
  end

  # 准备和创建通信
  defp prepare_and_create_communication(socket, validated_params) do
    current_user_id = get_current_user_id(socket)

    if current_user_id do
      create_params = build_create_params(validated_params, current_user_id)
      SystemCommunication.create(create_params)
    else
      {:error, "无法获取当前用户信息"}
    end
  end

  # 处理创建异常
  defp handle_create_exception(socket, error) do
    error_message = "创建过程中发生异常，请稍后重试"

    socket =
      socket
      |> put_flash(:error, error_message)
      |> show_error_dialog(:operation_error,
          title: "系统错误",
          message: "❌ #{error_message}",
          confirm_action: "dialog:hide",
          confirm_data: %{"dialog" => "operation_error"}
        )

    {:noreply, socket}
  end

  @doc """
  处理更新通信操作

  ## 参数
  - `socket` - Phoenix LiveView socket
  - `validated_params` - 已验证的参数
  - `original_params` - 原始参数

  ## 返回
  - `{:noreply, socket}` - 更新后的socket
  """
  def handle_update_communication(socket, validated_params, original_params) do
    Logger.info("📝 [CRUD操作] 开始更新通信")
    Logger.debug("📝 [CRUD操作] 验证参数: #{inspect(validated_params)}")

    try do
      case prepare_and_update_communication(socket, validated_params, original_params) do
        {:ok, updated_communication} ->
          Logger.info("✅ [CRUD操作] 通信更新成功: #{updated_communication.title}")
          handle_update_success(socket, updated_communication)
        {:error, error_message} ->
          Logger.error("❌ [CRUD操作] 通信更新失败: #{error_message}")
          handle_update_error(socket, "更新失败", error_message)
      end
    rescue
      error ->
        Logger.error("❌ [CRUD操作] 更新通信异常: #{inspect(error)}")
        handle_update_exception(socket, error)
    end
  end

  # 准备和更新通信
  defp prepare_and_update_communication(socket, validated_params, original_params) do
    case get_communication_id_for_update(original_params, socket) do
      {:ok, communication_id} ->
        execute_database_update(communication_id, validated_params)
      {:error, error_message} ->
        {:error, error_message}
    end
  end

  # 处理更新异常
  defp handle_update_exception(socket, error) do
    error_message = "更新过程中发生异常，请稍后重试"

    socket =
      socket
      |> put_flash(:error, error_message)
      |> show_error_dialog(:operation_error,
          title: "系统错误",
          message: "❌ #{error_message}",
          confirm_action: "dialog:hide",
          confirm_data: %{"dialog" => "operation_error"}
        )

    {:noreply, socket}
  end

  @doc """
  执行删除操作

  ## 参数
  - `id` - 记录ID
  - `title` - 记录标题
  - `type_name` - 类型名称

  ## 返回
  - `{:ok, success_message}` - 成功消息
  - `{:error, error_message}` - 错误消息
  """
  def perform_delete_operation(id, title, type_name) do
    Logger.info("🗑️ [CRUD操作] 执行删除操作，ID: #{id}")

    try do
      case load_and_validate_for_delete(id) do
        {:ok, communication} ->
          execute_delete_with_constraints(communication, title, type_name)
        {:error, error_message} ->
          Logger.warning("⚠️ [CRUD操作] 删除前验证失败: #{error_message}")
          {:error, error_message}
      end
    rescue
      error ->
        Logger.error("❌ [CRUD操作] 删除操作异常: #{inspect(error)}")
        {:error, "删除过程中发生异常，请稍后重试"}
    end
  end

  # 加载和验证删除条件
  defp load_and_validate_for_delete(id) do
    case SystemCommunication.read(id) do
      {:ok, communication} ->
        case DeleteConstraintValidator.check_delete_constraints(communication) do
          :ok ->
            {:ok, communication}
          {:error, constraint_error} ->
            {:error, constraint_error}
        end
      {:error, %Ash.Error.Query.NotFound{}} ->
        {:error, "记录不存在，可能已被删除"}
      {:error, error} ->
        error_message = format_delete_error(error)
        {:error, error_message}
    end
  end

  # 执行带约束检查的删除
  defp execute_delete_with_constraints(communication, title, type_name) do
    case SystemCommunication.destroy(communication) do
      :ok ->
        success_message = build_delete_success_message(title, type_name)
        Logger.info("✅ [CRUD操作] 删除成功: #{success_message}")
        {:ok, success_message}
      {:error, error} ->
        error_message = format_delete_error(error)
        Logger.error("❌ [CRUD操作] 删除失败: #{error_message}")
        {:error, error_message}
    end
  end

  @doc """
  执行状态切换操作

  ## 参数
  - `id` - 记录ID

  ## 返回
  - `{:ok, communication, action_name}` - 成功结果
  - `{:error, error_message}` - 错误消息
  """
  def perform_status_toggle_operation(id) do
    Logger.info("🔄 [CRUD操作] 执行状态切换操作，ID: #{id}")

    try do
      case load_and_toggle_status(id) do
        {:ok, updated_communication, action_name} ->
          Logger.info("✅ [CRUD操作] 状态切换成功: #{action_name}")
          {:ok, updated_communication, action_name}
        {:error, error_message} ->
          Logger.error("❌ [CRUD操作] 状态切换失败: #{error_message}")
          {:error, error_message}
      end
    rescue
      error ->
        Logger.error("❌ [CRUD操作] 状态切换异常: #{inspect(error)}")
        {:error, "状态切换过程中发生异常，请稍后重试"}
    end
  end

  # 加载和切换状态
  defp load_and_toggle_status(id) do
    case SystemCommunication.read(id) do
      {:ok, communication} ->
        execute_status_toggle(communication)
      {:error, %Ash.Error.Query.NotFound{}} ->
        {:error, "记录不存在，可能已被删除"}
      {:error, error} ->
        error_message = format_status_toggle_error(error)
        {:error, error_message}
    end
  end

  # 执行状态切换
  defp execute_status_toggle(communication) do
    new_status = !communication.active
    action_name = if new_status, do: "启用", else: "禁用"

    case SystemCommunication.update(communication, %{active: new_status}) do
      {:ok, updated_communication} ->
        {:ok, updated_communication, action_name}
      {:error, error} ->
        error_message = format_status_toggle_error(error)
        {:error, error_message}
    end
  end

  # 私有函数 - 构建创建参数
  defp build_create_params(validated_params, current_user_id) do
    validated_params
    |> Map.put(:creator_id, current_user_id)
    |> Map.put(:inserted_at, DateTime.utc_now())
    |> Map.put(:updated_at, DateTime.utc_now())
  end

  # 私有函数 - 处理创建成功
  defp handle_create_success(socket, communication) do
    type_name = get_type_display_name(communication.type)

    socket =
      socket
      |> assign(:show_create_modal, false)
      |> assign(:modal_data, nil)
      |> SearchOperations.load_communications()
      |> show_success_dialog(:save_success,
          title: "创建成功",
          message: "✅ #{type_name}「#{communication.title}」创建成功！",
          confirm_action: "dialog:hide",
          confirm_data: %{"dialog" => "save_success"}
        )

    {:noreply, socket}
  end

  # 私有函数 - 处理创建错误
  defp handle_create_error(socket, error) do
    error_message = format_create_error(error)
    Logger.error("❌ [CRUD操作] 创建失败: #{error_message}")

    socket =
      socket
      |> put_flash(:error, error_message)
      |> show_error_dialog(:operation_error,
          title: "创建失败",
          message: "❌ #{error_message}",
          confirm_action: "dialog:hide",
          confirm_data: %{"dialog" => "operation_error"}
        )

    {:noreply, socket}
  end

  # 私有函数 - 获取更新用的通信ID
  defp get_communication_id_for_update(original_params, socket) do
    case Map.get(original_params, "id") || Map.get(socket.assigns.modal_data || %{}, :id) do
      nil ->
        {:error, "缺少记录ID，无法执行更新操作"}
      id when is_binary(id) ->
        {:ok, id}
      id ->
        {:ok, to_string(id)}
    end
  end

  # 私有函数 - 执行数据库更新
  defp execute_database_update(communication_id, validated_params) do
    case SystemCommunication.read(communication_id) do
      {:ok, existing_communication} ->
        perform_changeset_update(existing_communication, validated_params)
      {:error, %Ash.Error.Query.NotFound{}} ->
        {:error, "记录不存在，可能已被删除"}
      {:error, error} ->
        error_message = format_update_error(error)
        {:error, error_message}
    end
  end

  # 私有函数 - 执行变更集更新
  defp perform_changeset_update(existing_communication, validated_params) do
    update_attributes = build_update_attributes(validated_params)

    case SystemCommunication.update(existing_communication, update_attributes) do
      {:ok, updated_communication} ->
        {:ok, updated_communication}
      {:error, error} ->
        error_message = format_validation_errors(error)
        {:error, error_message}
    end
  end

  # 私有函数 - 构建更新属性
  defp build_update_attributes(validated_params) do
    validated_params
    |> Map.put(:updated_at, DateTime.utc_now())
    |> Map.drop([:id, :creator_id, :inserted_at])
  end

  # 私有函数 - 处理更新成功
  defp handle_update_success(socket, updated_communication) do
    type_name = get_type_display_name(updated_communication.type)

    socket =
      socket
      |> assign(:show_edit_modal, false)
      |> assign(:current_communication, nil)
      |> assign(:modal_data, nil)
      |> SearchOperations.load_communications()
      |> show_success_dialog(:save_success,
          title: "更新成功",
          message: "✅ #{type_name}「#{updated_communication.title}」更新成功！",
          confirm_action: "dialog:hide",
          confirm_data: %{"dialog" => "save_success"}
        )

    {:noreply, socket}
  end

  # 私有函数 - 处理更新错误
  defp handle_update_error(socket, error_title, error_message) do
    socket =
      socket
      |> put_flash(:error, error_message)
      |> show_error_dialog(:operation_error,
          title: error_title,
          message: "❌ #{error_message}",
          confirm_action: "dialog:hide",
          confirm_data: %{"dialog" => "operation_error"}
        )

    {:noreply, socket}
  end

  # 私有函数 - 获取当前用户ID
  defp get_current_user_id(socket) do
    case socket.assigns.current_user do
      %{id: id} -> id
      _ -> nil
    end
  end

  # 私有函数 - 构建删除成功消息
  defp build_delete_success_message(title, type_name) do
    "✅ #{type_name}「#{title}」已成功删除！"
  end

  # 私有函数 - 格式化删除错误
  defp format_delete_error(error) when is_binary(error), do: error
  defp format_delete_error(%{message: message}) when is_binary(message), do: message
  defp format_delete_error(_error), do: "删除操作失败，请稍后重试"

  # 私有函数 - 格式化状态切换错误
  defp format_status_toggle_error(error) when is_binary(error), do: error
  defp format_status_toggle_error(%{message: message}) when is_binary(message), do: message
  defp format_status_toggle_error(_error), do: "状态切换失败，请稍后重试"

  # 私有函数 - 格式化创建错误
  defp format_create_error(error) when is_binary(error), do: error
  defp format_create_error(%Ash.Error.Invalid{errors: errors}) do
    errors
    |> Enum.map(&format_single_create_error/1)
    |> Enum.filter(& &1)
    |> case do
      [] -> "创建失败，请检查输入数据"
      error_messages -> Enum.join(error_messages, "；")
    end
  end
  defp format_create_error(%{message: message}) when is_binary(message), do: message
  defp format_create_error(_error), do: "创建操作失败，请稍后重试"

  # 私有函数 - 格式化单个创建错误
  defp format_single_create_error(%{field: field, message: message}) when is_binary(message) do
    field_name = get_field_display_name(field)
    "#{field_name}: #{message}"
  end
  defp format_single_create_error(%{message: message}) when is_binary(message), do: message
  defp format_single_create_error(_), do: nil

  # 私有函数 - 格式化验证错误
  defp format_validation_errors(%Ash.Error.Invalid{errors: errors}) do
    errors
    |> Enum.map(&format_single_validation_error/1)
    |> Enum.filter(& &1)
    |> case do
      [] -> "验证失败，请检查输入数据"
      error_messages -> Enum.join(error_messages, "；")
    end
  end
  defp format_validation_errors(error), do: format_update_error(error)

  # 私有函数 - 格式化单个验证错误
  defp format_single_validation_error(%{field: field, message: message}) when is_binary(message) do
    field_name = get_field_display_name(field)
    "#{field_name}: #{message}"
  end
  defp format_single_validation_error(%{message: message}) when is_binary(message), do: message
  defp format_single_validation_error(_), do: nil

  # 私有函数 - 获取字段显示名称
  defp get_field_display_name(field) do
    Map.get(@field_display_names, field, to_string(field))
  end

  # 私有函数 - 格式化更新错误
  defp format_update_error(error) when is_binary(error), do: error
  defp format_update_error(%{message: message}) when is_binary(message), do: message
  defp format_update_error(error), do: "更新操作失败: #{inspect(error)}"

  # 私有函数 - 获取类型显示名称
  defp get_type_display_name(type) do
    Map.get(@type_display_names, type, "通信记录")
  end
end
