defmodule RacingGameWeb.BusinessComponents do
  @moduledoc """
  业务组件库 - Racing Game Admin Panel

  提供专门针对Racing Game业务场景的组件，包括用户卡片、支付状态、
  数据统计、筛选面板等业务相关的复合组件。
  """

  use Phoenix.Component
  alias Phoenix.LiveView.JS
  import RacingGameWeb.UIComponents
  import RacingGameWeb.Gettext

  @doc """
  用户信息卡片组件

  ## 示例

      <.user_card
        user={@user}
        show_balance={true}
        show_actions={true}
        class="shadow-lg"
      />
  """
  attr :user, :map, required: true
  attr :show_balance, :boolean, default: false
  attr :show_actions, :boolean, default: false
  attr :compact, :boolean, default: false
  attr :class, :string, default: ""

  def user_card(assigns) do
    ~H"""
    <.card class={["hover:shadow-lg transition-shadow", @class]}>
      <div class={[
        "flex items-center",
        @compact && "space-x-3" || "space-x-4"
      ]}>
        <.avatar
          src={@user[:avatar]}
          name={@user.username || @user.name || "用户"}
          size={@compact && "sm" || "md"}
          online={@user[:is_online]}
        />

        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-2">
            <h3 class={[
              "font-semibold truncate",
              @compact && "text-sm" || "text-base"
            ]}>
              <%= @user.username || @user.name || "未知用户" %>
            </h3>
            <%= if @user[:vip_level] && @user.vip_level > 0 do %>
              <.badge variant="warning" size="xs">VIP<%= @user.vip_level %></.badge>
            <% end %>
          </div>

          <p class="text-xs text-gray-500 truncate">
            ID: <%= @user.id %>
            <%= if @user[:phone] do %>
              • <%= mask_phone(@user.phone) %>
            <% end %>
          </p>

          <div class="flex items-center space-x-2 mt-1">
            <.user_status_badge status={@user[:status] || "active"} />
            <%= if @user[:last_login_at] do %>
              <span class="text-xs text-gray-400">
                <%= format_relative_time(@user.last_login_at) %>
              </span>
            <% end %>
          </div>
        </div>

        <%= if @show_balance && @user[:balance] do %>
          <div class="text-right">
            <p class="text-xs text-gray-500">余额</p>
            <p class="text-lg font-bold text-primary">
              ¥<%= format_currency(@user.balance) %>
            </p>
          </div>
        <% end %>
      </div>

      <%= if @show_actions do %>
        <div class="flex items-center justify-end space-x-2 mt-4 pt-4 border-t border-gray-100">
          <.button variant="ghost" size="sm" phx-click="view_user" phx-value-id={@user.id}>
            <.icon name="hero-eye" class="w-4 h-4 mr-1" />
            查看
          </.button>
          <.button variant="ghost" size="sm" phx-click="edit_user" phx-value-id={@user.id}>
            <.icon name="hero-pencil" class="w-4 h-4 mr-1" />
            编辑
          </.button>
        </div>
      <% end %>
    </.card>
    """
  end

  @doc """
  用户状态徽章组件
  """
  attr :status, :string, required: true
  attr :size, :string, default: "xs"

  def user_status_badge(assigns) do
    ~H"""
    <.badge
      variant={status_variant(@status)}
      size={@size}
    >
      <%= status_text(@status) %>
    </.badge>
    """
  end

  @doc """
  支付状态组件

  ## 示例

      <.payment_status
        status="completed"
        amount={1000}
        created_at={~N[2024-01-01 12:00:00]}
        show_amount={true}
      />
  """
  attr :status, :string, required: true
  attr :amount, :integer, default: nil
  attr :created_at, :any, default: nil
  attr :show_amount, :boolean, default: false
  attr :show_time, :boolean, default: true
  attr :size, :string, default: "md"

  def payment_status(assigns) do
    ~H"""
    <div class="flex items-center space-x-2">
      <div class={[
        "w-3 h-3 rounded-full",
        payment_status_color(@status),
        payment_status_animation(@status)
      ]}>
      </div>

      <div class="flex-1">
        <div class="flex items-center space-x-2">
          <span class={[
            "font-medium",
            payment_status_text_color(@status),
            @size == "sm" && "text-sm" || "text-base"
          ]}>
            <%= payment_status_text(@status) %>
          </span>

          <%= if @show_amount && @amount do %>
            <span class={[
              "font-bold text-gray-900",
              @size == "sm" && "text-sm" || "text-base"
            ]}>
              ¥<%= format_currency(@amount) %>
            </span>
          <% end %>
        </div>

        <%= if @show_time && @created_at do %>
          <p class="text-xs text-gray-500">
            <%= format_relative_time(@created_at) %>
          </p>
        <% end %>
      </div>
    </div>
    """
  end

  @doc """
  数据统计卡片组件

  ## 示例

      <.stat_card
        title="在线用户"
        value="1,234"
        change="+12%"
        trend="up"
        icon="hero-users"
        color="primary"
      />
  """
  attr :title, :string, required: true
  attr :value, :string, required: true
  attr :change, :string, default: nil
  attr :trend, :string, default: nil, values: ~w(up down flat)
  attr :icon, :string, default: nil
  attr :color, :string, default: "primary"
  attr :description, :string, default: nil
  attr :class, :string, default: ""

  def stat_card(assigns) do
    ~H"""
    <div class={[
      "stats shadow bg-base-100",
      @class
    ]}>
      <div class="stat">
        <%= if @icon do %>
          <div class={[
            "stat-figure",
            stat_color_class(@color)
          ]}>
            <.icon name={@icon} class="w-8 h-8" />
          </div>
        <% end %>

        <div class="stat-title text-sm font-medium text-gray-600">
          <%= @title %>
        </div>

        <div class={[
          "stat-value text-2xl font-bold",
          stat_color_class(@color)
        ]}>
          <%= @value %>
        </div>

        <%= if @change || @description do %>
          <div class="stat-desc flex items-center space-x-2">
            <%= if @change do %>
              <span class={[
                "flex items-center text-sm font-medium",
                trend_color_class(@trend)
              ]}>
                <%= if @trend == "up" do %>
                  <.icon name="hero-arrow-trending-up" class="w-4 h-4 mr-1" />
                <% end %>
                <%= if @trend == "down" do %>
                  <.icon name="hero-arrow-trending-down" class="w-4 h-4 mr-1" />
                <% end %>
                <%= @change %>
              </span>
            <% end %>

            <%= if @description do %>
              <span class="text-sm text-gray-500">
                <%= @description %>
              </span>
            <% end %>
          </div>
        <% end %>
      </div>
    </div>
    """
  end

  @doc """
  筛选面板组件

  ## 示例

      <.filter_panel title="用户筛选" collapsible={true}>
        <.input type="text" name="search" label="搜索" placeholder="用户名或邮箱" />
        <.input type="select" name="status" label="状态" options={[{"全部", ""}, {"活跃", "active"}]} />
        
        <:actions>
          <.button variant="ghost" size="sm">重置</.button>
          <.button variant="primary" size="sm">应用筛选</.button>
        </:actions>
      </.filter_panel>
  """
  attr :title, :string, default: "筛选条件"
  attr :collapsible, :boolean, default: false
  attr :collapsed, :boolean, default: false
  attr :class, :string, default: ""

  slot :actions
  slot :inner_block, required: true

  def filter_panel(assigns) do
    ~H"""
    <.card
      title={@title}
      class={["border border-gray-200", @class]}
      compact={@collapsible && @collapsed}
    >
      <:actions>
        <%= if @collapsible do %>
          <.button
            variant="ghost"
            size="xs"
            phx-click={JS.toggle(to: "#filter-content")}
          >
            <.icon name="hero-chevron-down" class="w-4 h-4" />
          </.button>
        <% end %>
      </:actions>

      <div id="filter-content" class={@collapsed && "hidden" || ""}>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <%= render_slot(@inner_block) %>
        </div>

        <%= if @actions != [] do %>
          <div class="flex items-center justify-between mt-6 pt-4 border-t border-gray-100">
            <div class="text-sm text-gray-500">
              <%= gettext("筛选结果将实时更新") %>
            </div>
            <div class="flex items-center space-x-2">
              <%= for action <- @actions do %>
                <%= render_slot(action) %>
              <% end %>
            </div>
          </div>
        <% end %>
      </div>
    </.card>
    """
  end

  @doc """
  操作栏组件

  ## 示例

      <.action_bar>
        <:left>
          <.button variant="primary">
            <.icon name="hero-plus" class="w-4 h-4 mr-2" />
            新建用户
          </.button>
        </:left>
        
        <:right>
          <.button variant="ghost" size="sm">导出</.button>
          <.button variant="ghost" size="sm">刷新</.button>
        </:right>
      </.action_bar>
  """
  attr :class, :string, default: ""

  slot :left
  slot :right

  def action_bar(assigns) do
    ~H"""
    <div class={[
      "flex items-center justify-between p-4 bg-base-100 border-b border-gray-200",
      @class
    ]}>
      <div class="flex items-center space-x-2">
        <%= for left <- @left do %>
          <%= render_slot(left) %>
        <% end %>
      </div>

      <div class="flex items-center space-x-2">
        <%= for right <- @right do %>
          <%= render_slot(right) %>
        <% end %>
      </div>
    </div>
    """
  end

  @doc """
  空状态组件

  ## 示例

      <.empty_state
        icon="hero-users"
        title="暂无用户"
        description="还没有用户注册，点击下方按钮创建第一个用户"
      >
        <.button variant="primary">创建用户</.button>
      </.empty_state>
  """
  attr :icon, :string, required: true
  attr :title, :string, required: true
  attr :description, :string, default: nil
  attr :class, :string, default: ""

  slot :inner_block

  def empty_state(assigns) do
    ~H"""
    <div class={[
      "flex flex-col items-center justify-center py-12 text-center",
      @class
    ]}>
      <div class="w-16 h-16 text-gray-400 mb-4">
        <.icon name={@icon} class="w-full h-full" />
      </div>

      <h3 class="text-lg font-semibold text-gray-900 mb-2">
        <%= @title %>
      </h3>

      <%= if @description do %>
        <p class="text-gray-500 mb-6 max-w-sm">
          <%= @description %>
        </p>
      <% end %>

      <%= if @inner_block do %>
        <div class="flex items-center space-x-2">
          <%= render_slot(@inner_block) %>
        </div>
      <% end %>
    </div>
    """
  end

  # 私有辅助函数

  defp mask_phone(phone) when is_binary(phone) do
    case String.length(phone) do
      11 -> String.slice(phone, 0, 3) <> "****" <> String.slice(phone, 7, 4)
      _ -> phone
    end
  end

  defp mask_phone(_), do: ""

  defp format_currency(amount) when is_number(amount) do
    amount
    |> :erlang.float_to_binary(decimals: 2)
    |> String.replace(~r/(\d)(?=(\d{3})+(?!\d))/, "\\1,")
  end

  defp format_currency(_), do: "0.00"

  defp format_relative_time(datetime) do
    # 简化的相对时间格式化，实际项目中可以使用更完善的库
    case DateTime.diff(DateTime.utc_now(), datetime, :second) do
      diff when diff < 60 -> "刚刚"
      diff when diff < 3600 -> "#{div(diff, 60)}分钟前"
      diff when diff < 86400 -> "#{div(diff, 3600)}小时前"
      diff -> "#{div(diff, 86400)}天前"
    end
  rescue
    _ -> "未知"
  end

  defp status_variant("active"), do: "success"
  defp status_variant("inactive"), do: "warning"
  defp status_variant("banned"), do: "error"
  defp status_variant("pending"), do: "info"
  defp status_variant(_), do: "neutral"

  defp status_text("active"), do: "活跃"
  defp status_text("inactive"), do: "非活跃"
  defp status_text("banned"), do: "已封禁"
  defp status_text("pending"), do: "待审核"
  defp status_text(_), do: "未知"

  defp payment_status_color("completed"), do: "bg-success"
  defp payment_status_color("pending"), do: "bg-warning"
  defp payment_status_color("failed"), do: "bg-error"
  defp payment_status_color("refunding"), do: "bg-info"
  defp payment_status_color(_), do: "bg-gray-400"

  defp payment_status_animation("pending"), do: "animate-pulse"
  defp payment_status_animation("refunding"), do: "animate-spin"
  defp payment_status_animation(_), do: ""

  defp payment_status_text_color("completed"), do: "text-success"
  defp payment_status_text_color("pending"), do: "text-warning"
  defp payment_status_text_color("failed"), do: "text-error"
  defp payment_status_text_color("refunding"), do: "text-info"
  defp payment_status_text_color(_), do: "text-gray-600"

  defp payment_status_text("completed"), do: "支付成功"
  defp payment_status_text("pending"), do: "待支付"
  defp payment_status_text("failed"), do: "支付失败"
  defp payment_status_text("refunding"), do: "退款中"
  defp payment_status_text("refunded"), do: "已退款"
  defp payment_status_text(_), do: "未知状态"

  defp stat_color_class("primary"), do: "text-primary"
  defp stat_color_class("success"), do: "text-success"
  defp stat_color_class("warning"), do: "text-warning"
  defp stat_color_class("error"), do: "text-error"
  defp stat_color_class("info"), do: "text-info"
  defp stat_color_class(_), do: "text-primary"

  defp trend_color_class("up"), do: "text-success"
  defp trend_color_class("down"), do: "text-error"
  defp trend_color_class(_), do: "text-gray-500"
end
