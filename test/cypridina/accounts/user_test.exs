defmodule <PERSON><PERSON><PERSON><PERSON>.Accounts.UserTest do
  use <PERSON><PERSON><PERSON>ina.DataCase, async: true

  alias <PERSON><PERSON><PERSON><PERSON>.Accounts.User
  alias <PERSON><PERSON><PERSON>ina.Ledger.Account

  describe "user creation with automatic account setup" do
    test "create_guest_user/0 automatically creates ledger account and asset" do
      # Create guest user
      assert {:ok, user} = User.create_guest_user()

      # Verify user attributes
      assert user.username =~ ~r/^Guest\d+$/
      assert user.email =~ ~r/^guest\d+@guest\.local$/
      assert is_integer(user.numeric_id)
      assert user.numeric_id > 10_000_000

      # Load relationships
      user_with_relations = User.read!(user.id, load: [:ledger_account, :asset])

      # Verify ledger account was created
      assert user_with_relations.ledger_account != nil
      assert user_with_relations.ledger_account.account_type == :user
      assert user_with_relations.ledger_account.currency == :XAA
      assert user_with_relations.ledger_account.user_id == user.id

      # Verify asset was created
      assert user_with_relations.asset != nil
      assert user_with_relations.asset.user_id == user.id

      # Verify points using the new system
      assert Cypridina.Accounts.get_user_points(user.id) == 0
    end

    test "register_with_username/1 automatically creates ledger account and asset" do
      username = "testuser_#{:rand.uniform(10000)}"

      # Create registered user
      assert {:ok, user} = User.register_with_username(%{
        username: username,
        password: "password123",
        password_confirmation: "password123"
      })

      # Verify user attributes
      assert user.username == username
      assert is_integer(user.numeric_id)

      # Load relationships
      user_with_relations = User.read!(user.id, load: [:point_account])

      # Verify point account was created
      assert user_with_relations.point_account != nil
      assert user_with_relations.point_account.account_type == :user
      assert user_with_relations.point_account.currency == :XAA

      # Verify points using the new system
      assert Cypridina.Accounts.get_user_points(user.id) == 0
    end

    test "register_with_username/1 with custom ledger_account params" do
      username = "vipuser_#{:rand.uniform(10000)}"

      # Create user with custom ledger account
      assert {:ok, user} = User.register_with_username(%{
        username: username,
        password: "password123",
        password_confirmation: "password123",
        ledger_account: %{
          account_type: :user,
          currency: :XAA,
          description: "VIP用户账户"
        }
      })

      # Load relationships
      user_with_relations = User.read!(user.id, load: [:ledger_account])

      # Verify custom description was set
      assert user_with_relations.ledger_account.description == "VIP用户账户"
    end
  end

  describe "user relationships" do
    test "user has_one ledger_account relationship" do
      {:ok, user} = User.create_guest_user()

      # Test direct account lookup
      account = Account.by_user_id!(user.id)
      assert account.user_id == user.id
      assert account.account_type == :user

      # Test relationship loading
      user_with_account = User.read!(user.id, load: [:ledger_account])
      assert user_with_account.ledger_account.id == account.id
    end

    test "account belongs_to user relationship" do
      {:ok, user} = User.create_guest_user()
      account = Account.by_user_id!(user.id)

      # Test reverse relationship
      account_with_user = Account.read!(account.id, load: [:user])
      assert account_with_user.user.id == user.id
      assert account_with_user.user.username == user.username
    end
  end

  describe "user calculations" do
    test "points from account system" do
      {:ok, user} = User.create_guest_user()

      # Get points from account system
      points = Cypridina.Accounts.get_user_points(user.id)
      assert points == 0
    end

    test "role_name calculation" do
      {:ok, user} = User.create_guest_user()

      user_with_role = User.read!(user.id, load: [:role_name])
      assert user_with_role.role_name == "普通玩家"

      # Update permission level
      {:ok, admin_user} = User.update_permission_level(user, %{permission_level: 1})
      admin_with_role = User.read!(admin_user.id, load: [:role_name])
      assert admin_with_role.role_name == "管理员"
    end
  end
end
