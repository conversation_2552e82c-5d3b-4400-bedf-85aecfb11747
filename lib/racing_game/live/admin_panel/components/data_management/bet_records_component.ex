defmodule RacingGame.Live.AdminPanel.BetRecordsComponent do
  @moduledoc """
  下注记录组件

  提供下注记录的查看、搜索和分页功能。
  支持按用户名搜索和权限过滤。
  """
  use CypridinaWeb, :live_component

  alias CypridinaWeb.AuthHelper
  alias RacingGame.Bet
  alias RacingGame.Live.AdminPanel.PermissionFilter
  alias Cypridina.Utils.TimeHelper
  require Ash.Query
  require Logger

  # 常量定义
  @default_per_page 20
  @max_per_page 100
  @search_placeholder "按用户名搜索..."

  def update(assigns, socket) do
    socket =
      socket
      |> assign(assigns)
      |> assign_defaults()
      |> load_bet_records_data()

    {:ok, socket}
  end

  # ============================================================================
  # 事件处理函数
  # ============================================================================

  def handle_event("refresh", _params, socket) do
    Logger.info("🔄 [下注记录] 刷新数据")
    socket = load_bet_records_data(socket)
    {:noreply, socket}
  end

  def handle_event("search", %{"search" => %{"query" => query}}, socket) do
    Logger.info("🔍 [下注记录] 搜索: #{query}")
    socket = perform_search(socket, String.trim(query))
    {:noreply, socket}
  end

  def handle_event("clear_search", _params, socket) do
    Logger.info("🧹 [下注记录] 清除搜索")
    socket = clear_search(socket)
    {:noreply, socket}
  end

  def handle_event("page_change", %{"page" => page_str}, socket) do
    case Integer.parse(page_str) do
      {page, ""} when page > 0 ->
        Logger.info("📄 [下注记录] 切换到第 #{page} 页")
        socket = change_page(socket, page)
        {:noreply, socket}
      _ ->
        Logger.warning("⚠️ [下注记录] 无效的页码: #{page_str}")
        {:noreply, socket}
    end
  end

  # ============================================================================
  # 私有辅助函数
  # ============================================================================

  defp assign_defaults(socket) do
    socket
    |> assign(:search_query, "")
    |> assign(:page, 1)
    |> assign(:per_page, @default_per_page)
    |> assign(:page_info, nil)
    |> assign(:loading, false)
    |> assign(:search_placeholder, @search_placeholder)
  end

  defp perform_search(socket, query) do
    socket
    |> assign(:search_query, query)
    |> assign(:page, 1)
    |> load_bet_records_data()
  end

  defp clear_search(socket) do
    socket
    |> assign(:search_query, "")
    |> assign(:page, 1)
    |> load_bet_records_data()
  end

  defp change_page(socket, page) do
    socket
    |> assign(:page, page)
    |> load_bet_records_data()
  end

  defp load_bet_records_data(socket) do
    Logger.info("📊 [下注记录] 开始加载数据")

    socket = assign(socket, :loading, true)

    try do
      query_params = extract_query_params(socket)
      query = build_bet_query(query_params)

      case execute_query(query, query_params) do
        {:ok, results} ->
          Logger.info("✅ [下注记录] 成功加载 #{length(results.bets)} 条记录")
          assign_query_results(socket, results, query_params)
        {:error, error} ->
          Logger.error("❌ [下注记录] 加载失败: #{inspect(error)}")
          assign_error_state(socket, query_params)
      end
    rescue
      error ->
        Logger.error("❌ [下注记录] 加载异常: #{inspect(error)}")
        assign_error_state(socket, %{page: 1, per_page: @default_per_page})
    end
  end

  defp extract_query_params(socket) do
    %{
      user: socket.assigns.current_user,
      search_query: String.trim(socket.assigns.search_query || ""),
      page: socket.assigns.page,
      per_page: min(socket.assigns.per_page, @max_per_page)
    }
  end

  defp build_bet_query(params) do
    Bet
    |> Ash.Query.load([:user])
    |> apply_permission_filter(params.user)
    |> apply_search_filter(params.search_query)
    |> Ash.Query.sort(updated_at: :desc)
  end

  defp apply_permission_filter(query, user) do
    PermissionFilter.apply_user_filter(query, user, :user_id)
  end

  defp apply_search_filter(query, ""), do: query
  defp apply_search_filter(query, search_query) do
    case find_matching_user_ids(search_query) do
      [] ->
        # 没有匹配的用户，返回空结果
        import Ash.Query
        filter(query, user_id == "no-match")
      user_ids ->
        import Ash.Query
        filter(query, user_id in ^user_ids)
    end
  end

  defp find_matching_user_ids(search_query) do
    import Ash.Query

    case Cypridina.Accounts.User
         |> filter(contains(username, ^search_query))
         |> select([:id])
         |> Ash.read() do
      {:ok, users} -> Enum.map(users, & &1.id)
      {:error, _} -> []
    end
  end

  defp execute_query(query, params) do
    offset = (params.page - 1) * params.per_page

    case query
         |> Ash.Query.page(count: true, limit: params.per_page, offset: offset)
         |> Ash.read() do
      {:ok, %Ash.Page.Offset{results: bets, count: total_count}} ->
        {:ok, %{bets: bets, total_count: total_count}}
      {:error, error} ->
        {:error, error}
    end
  end

  defp assign_query_results(socket, results, params) do
    page_info = %{
      total_count: results.total_count,
      page: params.page,
      per_page: params.per_page
    }

    socket
    |> assign(:bet_records_data, results.bets)
    |> assign(:page_info, page_info)
    |> assign(:loading, false)
  end

  defp assign_error_state(socket, params) do
    page_info = %{
      total_count: 0,
      page: params.page,
      per_page: params.per_page
    }

    socket
    |> assign(:bet_records_data, [])
    |> assign(:page_info, page_info)
    |> assign(:loading, false)
  end

  # ============================================================================
  # 状态映射和辅助函数
  # ============================================================================

  defp get_bet_status_info(status) do
    case status do
      0 -> %{text: "待开奖", class: "badge-warning"}
      1 -> %{text: "中奖", class: "badge-success"}
      2 -> %{text: "未中奖", class: "badge-error"}
      _ -> %{text: "未知", class: "badge-ghost"}
    end
  end

  defp format_user_display(bet) do
    if bet.user do
      to_string(bet.user.username)
    else
      "未知用户"
    end
  end

  defp format_user_id(user_id) when is_binary(user_id) do
    String.slice(user_id, 0, 8) <> "..."
  end
  defp format_user_id(_), do: "N/A"

  defp format_payout(payout) when payout > 0, do: payout
  defp format_payout(_), do: "-"

  defp get_payout_class(payout) when payout > 0, do: "text-success"
  defp get_payout_class(_), do: "text-base-content/40"

  # ============================================================================
  # 渲染函数
  # ============================================================================

  def render(assigns) do
    ~H"""
    <div class="p-6">
      <!-- 页面头部 -->
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-xl font-semibold">下注记录</h2>
        <div class="flex items-center space-x-4">
          <%= if @loading do %>
            <div class="loading loading-spinner loading-sm"></div>
            <span class="text-sm text-base-content/60">加载中...</span>
          <% else %>
            <div class="text-sm text-base-content/60">
              <%= if @page_info do %>
                共 <%= @page_info.total_count %> 条记录，第 <%= @page_info.page %> 页
              <% else %>
                暂无数据
              <% end %>
            </div>
          <% end %>
          <button
            phx-click="refresh"
            phx-target={@myself}
            class="btn btn-ghost btn-sm"
            disabled={@loading}
          >
            <.icon name="hero-arrow-path" class="w-4 h-4" />
            刷新
          </button>
        </div>
      </div>

      <!-- 搜索框 -->
      <div class="flex justify-between items-center mb-4">
        <form phx-submit="search" phx-target={@myself} class="flex space-x-2">
          <input
            type="text"
            name="search[query]"
            value={@search_query}
            placeholder={@search_placeholder}
            class="input input-bordered input-sm w-64"
            disabled={@loading}
          />
          <button
            type="submit"
            class="btn btn-outline btn-sm"
            disabled={@loading}
          >
            <.icon name="hero-magnifying-glass" class="w-4 h-4" /> 搜索
          </button>
        </form>
        <%= if @search_query != "" do %>
          <button
            phx-click="clear_search"
            phx-target={@myself}
            class="btn btn-ghost btn-sm"
            disabled={@loading}
          >
            <.icon name="hero-x-mark" class="w-4 h-4" /> 清除搜索
          </button>
        <% end %>
      </div>

      <%= if @bet_records_data && length(@bet_records_data) > 0 do %>
        <div class="overflow-x-auto">
          <table class="table table-zebra w-full">
            <thead>
              <tr>
                <th>用户名</th>
                <th>用户ID</th>
                <th>期号</th>
                <th>选择</th>
                <th>下注金额</th>
                <th>状态</th>
                <th>奖金</th>
                <th>下注时间</th>
              </tr>
            </thead>
            <tbody>
              <%= for bet <- @bet_records_data do %>
                <% status_info = get_bet_status_info(bet.status) %>
                <tr>
                  <td class="font-medium">
                    <%= format_user_display(bet) %>
                  </td>
                  <td class="font-mono text-sm">
                    <%= format_user_id(bet.user_id) %>
                  </td>
                  <td class="font-mono"><%= bet.race_issue %></td>
                  <td>
                    <span class="badge badge-primary"><%= bet.selection %></span>
                  </td>
                  <td class="font-medium"><%= bet.amount %></td>
                  <td>
                    <span class={"badge #{status_info.class}"}>
                      <%= status_info.text %>
                    </span>
                  </td>
                  <td class="font-medium">
                    <span class={get_payout_class(bet.payout)}>
                      <%= format_payout(bet.payout) %>
                    </span>
                  </td>
                  <td><%= TimeHelper.format_local_short_time(bet.inserted_at) %></td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>

        <!-- 分页组件 -->
        <%= if @page_info && @page_info.total_count > @page_info.per_page do %>
          <div class="flex justify-center mt-6">
            <div class="join">
              <%= if @page_info.page > 1 do %>
                <button
                  phx-click="page_change"
                  phx-value-page={@page_info.page - 1}
                  phx-target={@myself}
                  class="join-item btn btn-sm"
                >
                  «
                </button>
              <% end %>

              <%= for page_num <- max(1, @page_info.page - 2)..min(ceil(@page_info.total_count / @page_info.per_page), @page_info.page + 2) do %>
                <button
                  phx-click="page_change"
                  phx-value-page={page_num}
                  phx-target={@myself}
                  class={[
                    "join-item btn btn-sm",
                    if(page_num == @page_info.page, do: "btn-active", else: "")
                  ]}
                >
                  <%= page_num %>
                </button>
              <% end %>

              <%= if @page_info.page < ceil(@page_info.total_count / @page_info.per_page) do %>
                <button
                  phx-click="page_change"
                  phx-value-page={@page_info.page + 1}
                  phx-target={@myself}
                  class="join-item btn btn-sm"
                >
                  »
                </button>
              <% end %>
            </div>
          </div>
        <% end %>
      <% else %>
        <div class="text-center py-12">
          <div class="text-base-content/40 mb-4">
            <.icon name="hero-ticket" class="w-16 h-16 mx-auto" />
          </div>
          <p class="text-base-content/60">暂无下注记录</p>
        </div>
      <% end %>
    </div>
    """
  end
end
