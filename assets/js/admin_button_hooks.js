/**
 * 管理后台按钮组件的JavaScript Hooks
 * 处理按钮的显示/隐藏逻辑
 */

// 单个按钮切换Hook
export const ButtonToggle = {
  mounted() {
    this.isHidden = false; // 默认显示
    
    // 点击事件：切换显示状态
    this.el.addEventListener('click', (e) => {
      // 如果按钮已经显示，正常执行点击事件
    });

    // 鼠标悬浮事件：不改变状态，只是视觉效果
    this.el.addEventListener('mouseenter', () => {
      // 悬浮时不改变显示状态，保持当前状态
    });

    this.el.addEventListener('mouseleave', () => {
      // 离开时不改变显示状态，保持当前状态
    });

    // 初始化状态
    this.updateButtonState();
  },

  showButton() {
    this.isHidden = false;
    this.updateButtonState();
  },

  hideButton() {
    this.isHidden = true;
    this.updateButtonState();
  },

  updateButtonState() {
    if (this.isHidden) {
      this.el.classList.add('opacity-0', 'pointer-events-none');
      this.el.classList.remove('opacity-100');
    } else {
      this.el.classList.remove('opacity-0', 'pointer-events-none');
      this.el.classList.add('opacity-100');
    }
  }
};

// 按钮组切换Hook
export const ButtonGroupToggle = {
  mounted() {
    this.isHidden = true; // 默认隐藏
    this.buttons = this.el.querySelectorAll('button');
    
    // 为整个按钮组添加点击事件
    this.el.addEventListener('click', (e) => {
      // 如果点击的是按钮组容器（不是具体按钮），切换显示状态
      if (e.target === this.el) {
        this.toggleVisibility();
        e.preventDefault();
        e.stopPropagation();
      }
    });

    // 为每个按钮添加点击事件
    this.buttons.forEach(button => {
      button.addEventListener('click', (e) => {
        // 如果按钮组当前是隐藏的，点击任意按钮后显示整个组
        if (this.isHidden) {
          this.showButtonGroup();
          e.preventDefault(); // 阻止默认的phx-click事件
          e.stopPropagation();
        }
        // 如果按钮组已经显示，正常执行按钮的点击事件
      });
    });

    // 初始化状态
    this.updateGroupState();
  },

  toggleVisibility() {
    this.isHidden = !this.isHidden;
    this.updateGroupState();
  },

  showButtonGroup() {
    this.isHidden = false;
    this.updateGroupState();
  },

  hideButtonGroup() {
    this.isHidden = true;
    this.updateGroupState();
  },

  updateGroupState() {
    if (this.isHidden) {
      this.el.classList.add('opacity-0', 'pointer-events-none');
      this.el.classList.remove('opacity-100');
    } else {
      this.el.classList.remove('opacity-0', 'pointer-events-none');
      this.el.classList.add('opacity-100');
    }
  }
};

// 导出所有Hooks
export default {
  ButtonToggle,
  ButtonGroupToggle
};
