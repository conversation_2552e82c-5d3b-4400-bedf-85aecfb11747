defmodule RacingGame.Live.AdminPanel.Validators.CommunicationValidator do
  @moduledoc """
  通信数据验证器

  处理系统通信管理组件中的数据验证逻辑。
  提供全面的数据验证功能，确保数据的完整性和有效性。
  """

  require Logger

  # 常量定义
  @valid_types ~w[message announcement notification]
  @valid_recipient_types ~w[all user admin]
  @valid_priorities ~w[low medium high urgent]
  @max_title_length 200
  @max_content_length 5000

  @doc """
  验证通信参数

  ## 参数
  - `params` - 要验证的参数映射

  ## 返回
  - `{:ok, validated_params}` - 验证成功，返回验证后的参数
  - `{:error, error_message}` - 验证失败，返回错误消息
  """
  def validate_communication_params(params) when is_map(params) do
    Logger.debug("🔍 [验证器] 开始验证通信参数: #{inspect(Map.keys(params))}")

    with {:ok, type} <- validate_communication_type(params["type"]),
         {:ok, title} <- validate_communication_title(params["title"]),
         {:ok, content} <- validate_communication_content(params["content"]),
         {:ok, recipient_type} <- validate_communication_recipient_type(params["recipient_type"]),
         {:ok, recipient_id} <- validate_communication_recipient_id(recipient_type, params["recipient_id"]),
         {:ok, priority} <- validate_communication_priority(params["priority"]),
         {:ok, active} <- validate_communication_active(params["active"]),
         {:ok, expires_at} <- validate_communication_expires_at(params["expires_at"]) do

      validated_params = %{
        type: type,
        title: title,
        content: content,
        recipient_type: recipient_type,
        recipient_id: recipient_id,
        priority: priority,
        active: active,
        expires_at: expires_at
      }

      Logger.info("✅ [验证器] 通信参数验证成功")
      {:ok, validated_params}
    else
      {:error, message} ->
        Logger.warning("❌ [验证器] 通信参数验证失败: #{message}")
        {:error, message}
    end
  end

  def validate_communication_params(_), do: {:error, "无效的参数格式"}

  # ============================================================================
  # 私有验证函数
  # ============================================================================

  # 验证通信类型
  defp validate_communication_type(type) when type in @valid_types do
    {:ok, String.to_atom(type)}
  end
  defp validate_communication_type(_), do: {:error, "无效的通信类型，支持的类型：#{Enum.join(@valid_types, ", ")}"}

  # 验证标题
  defp validate_communication_title(title) when is_binary(title) do
    title
    |> String.trim()
    |> validate_title_content()
  end
  defp validate_communication_title(_), do: {:error, "标题必须是文本"}

  defp validate_title_content(""), do: {:error, "标题不能为空"}
  defp validate_title_content(title) when byte_size(title) > @max_title_length do
    {:error, "标题长度不能超过#{@max_title_length}个字符"}
  end
  defp validate_title_content(title), do: {:ok, title}

  # 验证内容
  defp validate_communication_content(content) when is_binary(content) do
    content
    |> String.trim()
    |> validate_content_body()
  end
  defp validate_communication_content(_), do: {:error, "内容必须是文本"}

  defp validate_content_body(""), do: {:error, "内容不能为空"}
  defp validate_content_body(content) when byte_size(content) > @max_content_length do
    {:error, "内容长度不能超过#{@max_content_length}个字符"}
  end
  defp validate_content_body(content), do: {:ok, content}

  # 验证接收者类型
  defp validate_communication_recipient_type(type) when type in @valid_recipient_types do
    {:ok, String.to_atom(type)}
  end
  defp validate_communication_recipient_type(_) do
    {:error, "无效的接收者类型，支持的类型：#{Enum.join(@valid_recipient_types, ", ")}"}
  end

  # 验证接收者ID
  defp validate_communication_recipient_id(recipient_type, recipient_id) do
    case {recipient_type, recipient_id} do
      {:user, id} when is_binary(id) ->
        validate_user_recipient_id(String.trim(id))
      {:user, _} ->
        {:error, "选择特定用户时必须提供用户ID"}
      {_, id} when is_binary(id) ->
        validate_optional_recipient_id(String.trim(id))
      {_, _} ->
        {:ok, nil}
    end
  end

  defp validate_user_recipient_id(""), do: {:error, "选择特定用户时必须提供用户ID"}
  defp validate_user_recipient_id(id) do
    # TODO: 添加用户ID存在性验证
    {:ok, id}
  end

  defp validate_optional_recipient_id(""), do: {:ok, nil}
  defp validate_optional_recipient_id(id), do: {:ok, id}

  # 验证优先级
  defp validate_communication_priority(priority) when priority in @valid_priorities do
    {:ok, String.to_atom(priority)}
  end
  defp validate_communication_priority(_) do
    {:error, "无效的优先级，支持的优先级：#{Enum.join(@valid_priorities, ", ")}"}
  end

  # 验证激活状态
  defp validate_communication_active(value) do
    case value do
      val when val in ["true", true] -> {:ok, true}
      val when val in ["false", false] -> {:ok, false}
      _ -> {:ok, true}  # 默认为激活状态
    end
  end

  # 验证过期时间
  defp validate_communication_expires_at(expires_at) do
    case expires_at do
      val when val in ["", nil] -> {:ok, nil}
      val when is_binary(val) -> validate_datetime_format(val)
      _ -> {:ok, nil}
    end
  end

  # 验证日期时间格式（占位符实现）
  defp validate_datetime_format(datetime_string) do
    # TODO: 实现具体的日期时间格式验证
    # 可以使用 DateTime.from_iso8601/1 或其他日期时间解析函数
    {:ok, datetime_string}
  end
end
