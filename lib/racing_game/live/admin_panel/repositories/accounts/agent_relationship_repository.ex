defmodule RacingGame.Live.AdminPanel.Repositories.Accounts.AgentRelationshipRepository do
  @moduledoc """
  代理关系数据访问仓储
  
  提供代理关系相关的数据访问抽象：
  - 代理关系管理
  - 代理层级查询
  - 代理业绩统计
  - 代理佣金计算
  """

  require Logger
  alias Ash.Query
  import Ash.Query
  alias Cypridina.Accounts.{AgentRelationship, User}
  alias RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder
  require Ash.Query

  # 常量定义
  @cache_ttl 600  # 10分钟缓存
  @default_limit 20
  @max_limit 100
  @max_agent_level 10

  # ============================================================================
  # 代理关系查询
  # ============================================================================

  @doc """
  根据ID获取代理关系

  ## 参数
  - `relationship_id` - 关系ID
  - `options` - 选项

  ## 返回
  - `{:ok, relationship}` - 成功
  - `{:error, :not_found}` - 未找到
  - `{:error, reason}` - 其他错误
  """
  def get_relationship_by_id(relationship_id, options \\ []) do
    Logger.debug("🔍 [代理仓储] 根据ID获取代理关系: #{relationship_id}")
    
    try do
      case AgentRelationship
           |> maybe_preload(options[:preload])
           |> Ash.get(relationship_id) do
        {:ok, relationship} -> {:ok, relationship}
        {:error, %Ash.Error.Query.NotFound{}} -> {:error, :not_found}
        {:error, error} ->
          Logger.error("❌ [代理仓储] 获取关系失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [代理仓储] 获取关系异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  获取用户的代理关系

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - `{:ok, relationships}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_relationships(user_id, options \\ []) do
    Logger.debug("🔗 [代理仓储] 获取用户代理关系: #{user_id}")
    
    try do
      query = AgentRelationship
      |> Ash.Query.filter(user_id == user_id or agent_id == user_id)
      |> maybe_preload(options[:preload])
      |> apply_sort([inserted_at: :desc])
      |> maybe_limit(options[:limit])
      
      case Ash.read(query) do
        {:ok, relationships} -> {:ok, relationships}
        {:error, error} ->
          Logger.error("❌ [代理仓储] 获取关系失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [代理仓储] 获取关系异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  获取代理的下级用户

  ## 参数
  - `agent_id` - 代理ID
  - `options` - 选项

  ## 返回
  - `{:ok, sub_users}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_agent_sub_users(agent_id, options \\ []) do
    Logger.debug("👥 [代理仓储] 获取代理下级用户: #{agent_id}")
    
    try do
      query = AgentRelationship
      |> Ash.Query.filter(agent_id == agent_id)
      |> Ash.Query.load(:user)
      |> maybe_filter_by_level(options[:level])
      |> maybe_filter_by_status(options[:status])
      |> apply_sort([inserted_at: :desc])
      |> maybe_limit(options[:limit])
      
      case Ash.read(query) do
        {:ok, relationships} -> 
          users = Enum.map(relationships, & &1.user)
          {:ok, users}
        {:error, error} ->
          Logger.error("❌ [代理仓储] 获取下级用户失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [代理仓储] 获取下级用户异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  获取用户的上级代理

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - `{:ok, agent}` - 成功
  - `{:error, :not_found}` - 未找到
  - `{:error, reason}` - 其他错误
  """
  def get_user_agent(user_id, options \\ []) do
    Logger.debug("👤 [代理仓储] 获取用户上级代理: #{user_id}")
    
    try do
      query = AgentRelationship
      |> Ash.Query.filter(user_id == user_id)
      |> Ash.Query.load(:agent)
      |> apply_sort([inserted_at: :desc])
      
      case Ash.read_one(query) do
        {:ok, relationship} when not is_nil(relationship) -> 
          {:ok, relationship.agent}
        {:ok, nil} -> 
          {:error, :not_found}
        {:error, error} ->
          Logger.error("❌ [代理仓储] 获取上级代理失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [代理仓储] 获取上级代理异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  获取代理层级结构

  ## 参数
  - `agent_id` - 代理ID
  - `max_depth` - 最大深度（默认3）

  ## 返回
  - `{:ok, hierarchy}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_agent_hierarchy(agent_id, max_depth \\ 3) do
    Logger.debug("🌳 [代理仓储] 获取代理层级结构: #{agent_id}")
    
    try do
      hierarchy = build_agent_hierarchy(agent_id, max_depth, 0, %{})
      {:ok, hierarchy}
    rescue
      error ->
        Logger.error("❌ [代理仓储] 获取层级结构异常: #{inspect(error)}")
        {:error, :hierarchy_error}
    end
  end

  @doc """
  分页查询代理关系列表

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, %{relationships: relationships, page_info: page_info}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_relationships_paginated(params \\ %{}) do
    Logger.debug("📋 [代理仓储] 分页查询代理关系列表")
    
    try do
      normalized_params = normalize_list_params(params)
      
      with {:ok, query} <- AccountsQueryBuilder.build_agent_relationship_list_query(normalized_params),
           {:ok, results} <- execute_paginated_query(query, normalized_params) do
        {:ok, %{relationships: results.items, page_info: results.page_info}}
      else
        {:error, reason} -> {:error, reason}
      end
    rescue
      error ->
        Logger.error("❌ [代理仓储] 分页查询异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # ============================================================================
  # 代理关系创建和管理
  # ============================================================================

  @doc """
  创建代理关系

  ## 参数
  - `relationship_data` - 关系数据

  ## 返回
  - `{:ok, relationship}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_relationship(relationship_data) do
    Logger.info("➕ [代理仓储] 创建代理关系")
    
    with :ok <- validate_relationship_data(relationship_data),
         :ok <- check_relationship_constraints(relationship_data),
         {:ok, relationship} <- do_create_relationship(relationship_data) do
      
      clear_cache(:relationship)
      Logger.info("✅ [代理仓储] 代理关系创建成功: #{relationship.id}")
      {:ok, relationship}
    else
      error -> error
    end
  end

  @doc """
  更新代理关系

  ## 参数
  - `relationship_id` - 关系ID
  - `update_data` - 更新数据

  ## 返回
  - `{:ok, relationship}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_relationship(relationship_id, update_data) do
    Logger.info("✏️ [代理仓储] 更新代理关系: #{relationship_id}")
    
    with {:ok, relationship} <- get_relationship_by_id(relationship_id),
         {:ok, updated_relationship} <- do_update_relationship(relationship, update_data) do
      
      clear_cache(:relationship, relationship_id)
      Logger.info("✅ [代理仓储] 代理关系更新成功: #{relationship_id}")
      {:ok, updated_relationship}
    else
      error -> error
    end
  end

  @doc """
  删除代理关系

  ## 参数
  - `relationship_id` - 关系ID

  ## 返回
  - `:ok` - 成功
  - `{:error, reason}` - 失败
  """
  def delete_relationship(relationship_id) do
    Logger.info("🗑️ [代理仓储] 删除代理关系: #{relationship_id}")
    
    with {:ok, relationship} <- get_relationship_by_id(relationship_id),
         :ok <- do_delete_relationship(relationship) do
      
      clear_cache(:relationship, relationship_id)
      Logger.info("✅ [代理仓储] 代理关系删除成功: #{relationship_id}")
      :ok
    else
      error -> error
    end
  end

  # ============================================================================
  # 代理统计查询
  # ============================================================================

  @doc """
  获取代理统计信息

  ## 参数
  - `agent_id` - 代理ID
  - `options` - 选项

  ## 返回
  - `{:ok, statistics}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_agent_statistics(agent_id, options \\ []) do
    Logger.debug("📊 [代理仓储] 获取代理统计: #{agent_id}")
    
    try do
      stats = %{
        total_sub_users: get_agent_sub_users_count(agent_id),
        active_sub_users: get_active_sub_users_count(agent_id),
        direct_sub_users: get_direct_sub_users_count(agent_id),
        indirect_sub_users: get_indirect_sub_users_count(agent_id),
        hierarchy_depth: get_agent_hierarchy_depth(agent_id),
        recent_additions: get_recent_additions_count(agent_id, options[:days] || 7)
      }
      
      {:ok, stats}
    rescue
      error ->
        Logger.error("❌ [代理仓储] 获取统计异常: #{inspect(error)}")
        {:error, :statistics_error}
    end
  end

  @doc """
  获取系统代理统计

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, statistics}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_system_agent_statistics(options \\ []) do
    Logger.debug("📊 [代理仓储] 获取系统代理统计")
    
    try do
      stats = %{
        total_relationships: get_total_relationships(),
        active_relationships: get_active_relationships_count(),
        agents_by_level: get_agents_by_level(),
        top_agents: get_top_agents(options[:limit] || 10),
        recent_relationships: get_recent_relationships_count(options[:days] || 7)
      }
      
      {:ok, stats}
    rescue
      error ->
        Logger.error("❌ [代理仓储] 获取系统统计异常: #{inspect(error)}")
        {:error, :statistics_error}
    end
  end

  # ============================================================================
  # 缓存管理
  # ============================================================================

  @doc """
  清除代理关系相关缓存

  ## 参数
  - `cache_type` - 缓存类型
  - `id` - ID（可选）
  """
  def clear_cache(cache_type, id \\ nil) do
    cache_patterns = case {cache_type, id} do
      {:relationship, nil} -> ["agent_relationship:*"]
      {:relationship, relationship_id} -> ["agent_relationship:#{relationship_id}:*"]
      {:agent, agent_id} -> ["agent:#{agent_id}:*"]
      {:user, user_id} -> ["user_agent:#{user_id}:*"]
      {:all, _} -> ["agent_relationship:*", "agent:*", "user_agent:*"]
    end
    
    Logger.debug("🧹 [代理仓储] 清除缓存: #{inspect(cache_patterns)}")
    Enum.each(cache_patterns, &clear_cache_pattern/1)
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 标准化列表参数
  defp normalize_list_params(params) do
    %{
      page: max(1, params[:page] || 1),
      limit: min(params[:limit] || @default_limit, @max_limit),
      search: String.trim(params[:search] || ""),
      filters: params[:filters] || %{},
      sort: params[:sort] || [inserted_at: :desc],
      preload: params[:preload] || []
    }
  end

  # 验证关系数据
  defp validate_relationship_data(data) do
    cond do
      is_nil(data[:user_id]) -> {:error, :missing_user_id}
      is_nil(data[:agent_id]) -> {:error, :missing_agent_id}
      data[:user_id] == data[:agent_id] -> {:error, :self_reference}
      true -> :ok
    end
  end

  # 检查关系约束
  defp check_relationship_constraints(data) do
    with :ok <- check_existing_relationship(data[:user_id], data[:agent_id]),
         :ok <- check_circular_reference(data[:user_id], data[:agent_id]),
         :ok <- check_agent_level_limit(data[:agent_id]) do
      :ok
    else
      error -> error
    end
  end

  # 检查是否已存在关系
  defp check_existing_relationship(user_id, agent_id) do
    query = AgentRelationship
    |> Ash.Query.filter(user_id == user_id and agent_id == agent_id)
    
    case Ash.read_one(query) do
      {:ok, nil} -> :ok
      {:ok, _relationship} -> {:error, :relationship_exists}
      {:error, _} -> {:error, :check_failed}
    end
  end

  # 检查循环引用
  defp check_circular_reference(user_id, agent_id) do
    # 简化实现：检查agent_id是否在user_id的下级中
    case get_agent_hierarchy(user_id, @max_agent_level) do
      {:ok, hierarchy} ->
        if hierarchy_contains_user?(hierarchy, agent_id) do
          {:error, :circular_reference}
        else
          :ok
        end
      {:error, _} -> :ok  # 如果获取失败，允许创建
    end
  end

  # 检查代理层级限制
  defp check_agent_level_limit(agent_id) do
    case get_agent_hierarchy_depth(agent_id) do
      depth when depth >= @max_agent_level -> {:error, :max_level_exceeded}
      _ -> :ok
    end
  end

  # 执行创建关系
  defp do_create_relationship(relationship_data) do
    try do
      case AgentRelationship
           |> Ash.Changeset.for_create(:create, relationship_data)
           |> Ash.create() do
        {:ok, relationship} -> {:ok, relationship}
        {:error, error} ->
          Logger.error("❌ [代理仓储] 创建关系失败: #{inspect(error)}")
          {:error, :create_failed}
      end
    rescue
      error ->
        Logger.error("❌ [代理仓储] 创建关系异常: #{inspect(error)}")
        {:error, :create_error}
    end
  end

  # 执行更新关系
  defp do_update_relationship(relationship, update_data) do
    try do
      case relationship
           |> Ash.Changeset.for_update(:update, update_data)
           |> Ash.update() do
        {:ok, updated_relationship} -> {:ok, updated_relationship}
        {:error, error} ->
          Logger.error("❌ [代理仓储] 更新关系失败: #{inspect(error)}")
          {:error, :update_failed}
      end
    rescue
      error ->
        Logger.error("❌ [代理仓储] 更新关系异常: #{inspect(error)}")
        {:error, :update_error}
    end
  end

  # 执行删除关系
  defp do_delete_relationship(relationship) do
    try do
      case Ash.destroy(relationship) do
        :ok -> :ok
        {:error, error} ->
          Logger.error("❌ [代理仓储] 删除关系失败: #{inspect(error)}")
          {:error, :delete_failed}
      end
    rescue
      error ->
        Logger.error("❌ [代理仓储] 删除关系异常: #{inspect(error)}")
        {:error, :delete_error}
    end
  end

  # 构建代理层级结构
  defp build_agent_hierarchy(agent_id, max_depth, current_depth, visited) do
    if current_depth >= max_depth or Map.has_key?(visited, agent_id) do
      %{agent_id: agent_id, sub_agents: [], depth: current_depth}
    else
      new_visited = Map.put(visited, agent_id, true)
      
      case get_agent_sub_users(agent_id, limit: 100) do
        {:ok, sub_users} ->
          sub_agents = Enum.map(sub_users, fn user ->
            build_agent_hierarchy(user.id, max_depth, current_depth + 1, new_visited)
          end)
          
          %{agent_id: agent_id, sub_agents: sub_agents, depth: current_depth}
        {:error, _} ->
          %{agent_id: agent_id, sub_agents: [], depth: current_depth}
      end
    end
  end

  # 检查层级结构是否包含用户
  defp hierarchy_contains_user?(%{agent_id: agent_id, sub_agents: sub_agents}, target_user_id) do
    agent_id == target_user_id or 
    Enum.any?(sub_agents, &hierarchy_contains_user?(&1, target_user_id))
  end

  # 执行分页查询
  defp execute_paginated_query(query, params) do
    offset = (params.page - 1) * params.limit
    
    case query
         |> maybe_preload(params.preload)
         |> Ash.Query.page(count: true, limit: params.limit, offset: offset)
         |> Ash.read() do
      {:ok, %Ash.Page.Offset{results: items, count: total_count}} ->
        page_info = %{
          total_count: total_count,
          page: params.page,
          limit: params.limit,
          total_pages: ceil(total_count / params.limit)
        }
        {:ok, %{items: items, page_info: page_info}}
      {:error, error} ->
        Logger.error("❌ [代理仓储] 分页查询失败: #{inspect(error)}")
        {:error, :query_failed}
    end
  end

  # 可能预加载关联数据
  defp maybe_preload(query, []), do: query
  defp maybe_preload(query, preload) when is_list(preload) do
    Ash.Query.load(query, preload)
  end

  # 可能限制数量
  defp maybe_limit(query, nil), do: query
  defp maybe_limit(query, limit) when is_integer(limit) and limit > 0 do
    Ash.Query.limit(query, limit)
  end
  defp maybe_limit(query, _), do: query

  # 应用排序
  defp apply_sort(query, sort) when is_list(sort) do
    Ash.Query.sort(query, sort)
  end
  defp apply_sort(query, _), do: query

  # 可能按层级过滤
  defp maybe_filter_by_level(query, nil), do: query
  defp maybe_filter_by_level(query, level) do
    Ash.Query.filter(query, level  == level)
  end

  # 可能按状态过滤
  defp maybe_filter_by_status(query, nil), do: query
  defp maybe_filter_by_status(query, status) do
    Ash.Query.filter(query, status  == status)
  end

  # 统计函数（简化实现）
  defp get_agent_sub_users_count(agent_id) do
    case AgentRelationship 
         |> Ash.Query.filter(agent_id == agent_id)
         |> Ash.Query.aggregate(:count, :id) 
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_active_sub_users_count(agent_id) do
    # 假设有 is_active 字段
    case AgentRelationship 
         |> Ash.Query.filter(agent_id  == agent_id and is_active == true)
         |> Ash.Query.aggregate(:count, :id) 
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_direct_sub_users_count(agent_id) do
    case AgentRelationship 
         |> Ash.Query.filter(agent_id == agent_id and level == 1)
         |> Ash.Query.aggregate(:count, :id) 
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_indirect_sub_users_count(agent_id) do
    case AgentRelationship 
         |> Ash.Query.filter(agent_id  == agent_id and level > 1)
         |> Ash.Query.aggregate(:count, :id) 
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_agent_hierarchy_depth(agent_id) do
    case get_agent_hierarchy(agent_id, @max_agent_level) do
      {:ok, hierarchy} -> calculate_hierarchy_depth(hierarchy)
      {:error, _} -> 0
    end
  end

  defp calculate_hierarchy_depth(%{sub_agents: []}), do: 1
  defp calculate_hierarchy_depth(%{sub_agents: sub_agents}) do
    max_sub_depth = sub_agents
    |> Enum.map(&calculate_hierarchy_depth/1)
    |> Enum.max(fn -> 0 end)
    
    1 + max_sub_depth
  end

  defp get_recent_additions_count(agent_id, days) do
    date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
    
    case AgentRelationship 
         |> Ash.Query.filter(agent_id == agent_id and inserted_at >= date_threshold)
         |> Ash.Query.aggregate(:count, :id) 
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_total_relationships do
    case AgentRelationship |> Ash.Query.aggregate(:count, :id) |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_active_relationships_count do
    case AgentRelationship 
         |> Ash.Query.filter(is_active == true)
         |> Ash.Query.aggregate(:count, :id) 
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_agents_by_level do
    case AgentRelationship 
         |> Ash.Query.aggregate(:count, :id, group: [:level]) 
         |> Ash.read() do
      {:ok, results} -> 
        Enum.into(results, %{}, fn %{level: level, count: count} -> 
          {level, count} 
        end)
      _ -> %{}
    end
  end

  defp get_top_agents(limit) do
    case AgentRelationship 
         |> Ash.Query.aggregate(:count, :id, group: [:agent_id]) 
         |> Ash.Query.sort(count: :desc)
         |> Ash.Query.limit(limit)
         |> Ash.read() do
      {:ok, results} -> results
      _ -> []
    end
  end

  defp get_recent_relationships_count(days) do
    date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
    
    case AgentRelationship 
         |> Ash.Query.filter(inserted_at >= date_threshold)
         |> Ash.Query.aggregate(:count, :id) 
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  # 缓存操作（需要实现具体的缓存后端）
  defp clear_cache_pattern(_pattern), do: :ok
end
