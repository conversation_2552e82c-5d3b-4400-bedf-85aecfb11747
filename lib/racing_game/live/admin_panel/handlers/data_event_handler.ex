defmodule RacingGame.Live.AdminPanel.Handlers.DataEventHandler do
  @moduledoc """
  数据操作事件处理器

  处理系统通信管理组件中的数据保存、删除和状态切换操作。

  ## 功能特性
  - 安全的数据验证和处理
  - 详细的操作日志记录
  - 用户友好的错误提示
  - 完整的删除确认流程
  - 状态切换确认机制
  - 异常安全处理

  ## 支持的操作
  - 创建和更新通信记录
  - 删除通信记录（带确认）
  - 状态切换（启用/禁用）
  - 数据验证和清理
  """

  require Logger
  alias RacingGame.SystemCommunication
  alias RacingGame.Live.AdminPanel.InputValidator
  alias RacingGame.Live.AdminPanel.Operations.{CrudOperations, SearchOperations}
  alias RacingGame.Live.AdminPanel.Validators.CommunicationValidator
  import RacingGame.Utils.DialogHelper
  import Phoenix.LiveView, only: [put_flash: 3]

  # 常量定义
  @max_content_preview_length 50
  @operation_timeout 5000
  @type_display_names %{
    :message => "系统消息",
    :announcement => "系统公告",
    :notification => "系统通知",
    "message" => "系统消息",
    "announcement" => "系统公告",
    "notification" => "系统通知"
  }
  @priority_display_names %{
    :low => "低",
    :medium => "中",
    :high => "高",
    :urgent => "紧急"
  }

  @doc """
  处理保存通信事件

  ## 参数
  - `params` - 表单参数
  - `socket` - Phoenix LiveView socket

  ## 返回
  - `{:noreply, socket}` - 更新后的socket
  """
  def handle_save_communication(params, socket) do
    Logger.info("💾 [数据操作] 保存通信事件触发")
    Logger.debug("💾 [数据操作] 参数: #{inspect(params)}")

    try do
      final_params = prepare_final_params(socket, params)

      case validate_and_process_communication(socket, final_params) do
        {:ok, result_socket} ->
          Logger.info("✅ [数据操作] 通信保存成功")
          result_socket
        {:error, error_socket} ->
          Logger.error("❌ [数据操作] 通信保存失败")
          error_socket
      end
    rescue
      error ->
        Logger.error("❌ [数据操作] 保存通信异常: #{inspect(error)}")
        handle_save_exception(socket, error)
    end
  end

  # 验证和处理通信数据
  defp validate_and_process_communication(socket, final_params) do
    case CommunicationValidator.validate_communication_params(final_params) do
      {:ok, validated_params} ->
        process_communication_by_mode(socket, validated_params, final_params)
      {:error, message} ->
        {:error, handle_validation_error(socket, message)}
    end
  end

  # 根据模式处理通信
  defp process_communication_by_mode(socket, validated_params, final_params) do
    case socket.assigns.modal_mode do
      :create ->
        Logger.debug("📝 [数据操作] 执行创建操作")
        {:ok, CrudOperations.handle_create_communication(socket, validated_params)}
      :edit ->
        Logger.debug("✏️ [数据操作] 执行更新操作")
        {:ok, CrudOperations.handle_update_communication(socket, validated_params, final_params)}
      mode ->
        Logger.warning("⚠️ [数据操作] 未知模式: #{inspect(mode)}")
        {:error, handle_unknown_mode_error(socket)}
    end
  end

  # 处理保存异常
  defp handle_save_exception(socket, error) do
    error_message = "保存过程中发生异常，请稍后重试"

    socket =
      socket
      |> put_flash(:error, error_message)
      |> show_error_dialog(:operation_error,
          title: "系统错误",
          message: "❌ #{error_message}",
          confirm_action: "dialog:hide",
          confirm_data: %{"dialog" => "operation_error"}
        )
    {:noreply, socket}
  end

  @doc """
  处理显示删除确认对话框事件

  ## 参数
  - `id` - 记录ID
  - `socket` - Phoenix LiveView socket

  ## 返回
  - `{:noreply, socket}` - 更新后的socket
  """
  def handle_show_delete_dialog(id, socket) do
    Logger.info("🗑️ [数据操作] 显示删除确认对话框，ID: #{id}")

    try do
      case validate_and_load_communication(id) do
        {:ok, communication} ->
          Logger.debug("📋 [数据操作] 成功加载通信记录: #{communication.title}")
          show_delete_confirmation_dialog(socket, communication, id)
          {:noreply, socket}
        {:error, error_type, message} ->
          Logger.warning("⚠️ [数据操作] 加载通信记录失败: #{error_type} - #{message}")
          show_delete_error(socket, error_type, message)
          {:noreply, socket}
      end
    rescue
      error ->
        Logger.error("❌ [数据操作] 显示删除对话框异常: #{inspect(error)}")
        show_delete_error(socket, "系统错误", "显示删除对话框时发生异常")
        {:noreply, socket}
    end
  end

  # 验证和加载通信记录
  defp validate_and_load_communication(id) do
    case InputValidator.validate_uuid(id) do
      {:ok, valid_id} ->
        load_communication_record(valid_id)
      {:error, message} ->
        {:error, "参数错误", message}
    end
  end

  # 加载通信记录
  defp load_communication_record(valid_id) do
    case SystemCommunication.read(valid_id) do
      {:ok, communication} ->
        {:ok, communication}
      {:error, %Ash.Error.Query.NotFound{}} ->
        {:error, "记录不存在", "指定的记录可能已被删除，请刷新页面后重试"}
      {:error, error} ->
        Logger.error("❌ [数据操作] 读取通信记录失败: #{inspect(error)}")
        {:error, "读取失败", "无法读取记录信息，请稍后重试"}
    end
  end

  @doc """
  处理确认删除事件

  ## 参数
  - `id` - 记录ID
  - `params` - 额外参数（标题、类型等）
  - `socket` - Phoenix LiveView socket

  ## 返回
  - `{:noreply, socket}` - 更新后的socket
  """
  def handle_confirm_delete(id, params, socket) do
    Logger.info("✅ [数据操作] 确认删除通信，ID: #{id}")

    title = Map.get(params, "title", "未知")
    type_name = Map.get(params, "type", "记录")

    case CrudOperations.perform_delete_operation(id, title, type_name) do
      {:ok, success_message} ->
        socket =
          socket
          |> hide_dialog(:delete_confirm)
          |> SearchOperations.load_communications()
          |> show_success_dialog(:save_success,
              title: "删除成功",
              message: success_message,
              confirm_action: "dialog:hide",
              confirm_data: %{"dialog" => "save_success"}
            )
        {:noreply, socket}

      {:error, error_message} ->
        socket =
          socket
          |> hide_dialog(:delete_confirm)
          |> show_error_dialog(:operation_error,
              title: "删除失败",
              message: "❌ #{error_message}",
              confirm_action: "dialog:hide",
              confirm_data: %{"dialog" => "operation_error"}
            )
        {:noreply, socket}
    end
  end

  @doc """
  处理显示状态切换确认对话框事件

  ## 参数
  - `id` - 记录ID
  - `socket` - Phoenix LiveView socket

  ## 返回
  - `{:noreply, socket}` - 更新后的socket
  """
  def handle_show_status_dialog(id, socket) do
    Logger.info("🔄 [数据操作] 显示状态切换确认对话框，ID: #{id}")

    case load_communication_for_status_toggle(id, socket) do
      {:ok, communication} ->
        show_status_toggle_dialog(socket, communication, id)
        {:noreply, socket}

      {:error, error_socket} ->
        {:noreply, error_socket}
    end
  end

  @doc """
  处理确认状态切换事件

  ## 参数
  - `id` - 记录ID
  - `socket` - Phoenix LiveView socket

  ## 返回
  - `{:noreply, socket}` - 更新后的socket
  """
  def handle_confirm_status_toggle(id, socket) do
    Logger.info("✅ [数据操作] 确认状态切换，ID: #{id}")

    case CrudOperations.perform_status_toggle_operation(id) do
      {:ok, communication, action_name} ->
        socket =
          socket
          |> hide_dialog(:status_confirm)
          |> SearchOperations.load_communications()
          |> show_success_dialog(:save_success,
              title: "状态切换成功",
              message: "✅ 通信「#{communication.title}」已#{action_name}！",
              confirm_action: "dialog:hide",
              confirm_data: %{"dialog" => "save_success"}
            )
        {:noreply, socket}

      {:error, error_message} ->
        socket =
          socket
          |> hide_dialog(:status_confirm)
          |> show_error_dialog(:operation_error,
              title: "状态切换失败",
              message: "❌ #{error_message}",
              confirm_action: "dialog:hide",
              confirm_data: %{"dialog" => "operation_error"}
            )
        {:noreply, socket}
    end
  end

  # 私有函数 - 准备最终参数
  defp prepare_final_params(socket, params) do
    modal_type = socket.assigns.modal_type
    modal_data_to_params(socket.assigns.modal_data, modal_type)
    |> Map.merge(params)
    |> Map.put("type", modal_type)
  end

  # 私有函数 - 模态框数据转换为参数
  defp modal_data_to_params(modal_data, modal_type) do
    %{
      "type" => modal_type,
      "title" => modal_data.title || "",
      "content" => modal_data.content || "",
      "recipient_type" => modal_data.recipient_type || "all",
      "recipient_id" => modal_data.recipient_id || "",
      "priority" => modal_data.priority || "medium",
      "active" => to_string(modal_data.active || true),
      "expires_at" => modal_data.expires_at
    }
  end

  # 私有函数 - 处理未知模式错误
  defp handle_unknown_mode_error(socket) do
    error_message = "未知的操作模式，请刷新页面后重试"

    socket =
      socket
      |> put_flash(:error, error_message)
      |> show_error_dialog(:operation_error,
          title: "操作失败",
          message: "❌ #{error_message}",
          confirm_action: "dialog:hide",
          confirm_data: %{"dialog" => "operation_error"}
        )
    {:noreply, socket}
  end

  # 私有函数 - 处理验证错误
  defp handle_validation_error(socket, message) do
    socket =
      socket
      |> put_flash(:error, message)
      |> show_error_dialog(:operation_error,
          title: "验证失败",
          message: "❌ #{message}",
          confirm_action: "dialog:hide",
          confirm_data: %{"dialog" => "operation_error"}
        )
    {:noreply, socket}
  end

  # 私有函数 - 加载通信记录用于状态切换
  defp load_communication_for_status_toggle(id, socket) do
    case InputValidator.validate_uuid(id) do
      {:ok, valid_id} ->
        case SystemCommunication.read(valid_id) do
          {:ok, communication} ->
            {:ok, communication}
          {:error, %Ash.Error.Query.NotFound{}} ->
            error_socket = show_delete_error(socket, "记录不存在", "指定的记录可能已被删除，请刷新页面后重试")
            {:error, error_socket}
          {:error, _error} ->
            error_socket = show_delete_error(socket, "读取失败", "无法读取记录信息，请稍后重试")
            {:error, error_socket}
        end
      {:error, message} ->
        error_socket = show_delete_error(socket, "参数错误", message)
        {:error, error_socket}
    end
  end

  # 私有函数 - 显示状态切换对话框
  defp show_status_toggle_dialog(socket, communication, id) do
    current_status = if communication.active, do: "启用", else: "禁用"
    new_status = if communication.active, do: "禁用", else: "启用"
    action = if communication.active, do: "禁用", else: "启用"

    show_confirm_dialog(socket, :status_confirm,
      title: "确认#{action}通信",
      message: """
      您确定要#{action}以下通信吗？

      📋 **通信信息**
      • 标题：#{communication.title}
      • 类型：#{get_type_display_name(communication.type)}
      • 当前状态：#{current_status}

      ⚠️ **操作说明**
      #{action}后，该通信的状态将变为「#{new_status}」。
      """,
      confirm_text: "确认#{action}",
      confirm_action: "data:confirm_status",
      confirm_data: %{"id" => id},
      cancel_text: "取消"
    )
  end

  # 私有函数 - 显示删除确认对话框
  defp show_delete_confirmation_dialog(socket, communication, id) do
    type_name = get_type_display_name(communication.type)
    enhanced_message = build_enhanced_delete_message(communication, type_name)

    show_confirm_dialog(socket, :delete_confirm,
      title: "确认删除#{type_name}",
      message: enhanced_message,
      confirm_text: "确认删除",
      confirm_action: "data:confirm_delete",
      confirm_data: %{
        "id" => id,
        "title" => communication.title,
        "type" => type_name
      },
      cancel_text: "取消"
    )
  end

  # 私有函数 - 显示删除错误
  defp show_delete_error(socket, title, message) do
    show_error_dialog(socket, :operation_error,
      title: title,
      message: "❌ #{message}",
      confirm_action: "dialog:hide",
      confirm_data: %{"dialog" => "operation_error"}
    )
  end

  # 私有函数 - 构建增强的删除消息
  defp build_enhanced_delete_message(communication, type_name) do
    record_info = build_record_info_section(communication)
    impact_info = build_impact_info_section(communication)
    warning_section = build_warning_section()

    """
    您确定要删除以下#{type_name}吗？

    #{record_info}

    #{impact_info}

    #{warning_section}
    """
  end

  # 私有函数 - 构建记录信息部分
  defp build_record_info_section(communication) do
    created_time = case communication.inserted_at do
      nil -> "未知"
      datetime -> format_time(datetime)
    end

    updated_time = case communication.updated_at do
      nil -> "未知"
      datetime -> format_time(datetime)
    end

    status = if communication.active, do: "启用", else: "禁用"
    priority_display = get_priority_display_name(communication.priority)

    """
    📋 **#{get_type_display_name(communication.type)}信息**
    • 标题：#{communication.title}
    • 内容：#{truncate_text(communication.content || "", 50)}
    • 优先级：#{priority_display}
    • 状态：#{status}
    • 创建时间：#{created_time}
    • 更新时间：#{updated_time}
    """
  end

  # 私有函数 - 构建影响信息部分
  defp build_impact_info_section(communication) do
    recipient_info = case communication.recipient_type do
      "all" -> "所有用户"
      "user" -> "特定用户"
      "admin" -> "管理员"
      _ -> "未知"
    end

    """
    🎯 **影响范围**
    • 接收者：#{recipient_info}
    • 通信类型：#{get_type_display_name(communication.type)}
    """
  end

  # 私有函数 - 构建警告部分
  defp build_warning_section do
    """
    ⚠️ **重要提醒**
    • 删除操作无法撤销
    • 相关的阅读记录也将被删除
    • 请确认您有权限执行此操作
    """
  end

  # 私有函数 - 获取类型显示名称
  defp get_type_display_name(type) do
    Map.get(@type_display_names, type, "通信记录")
  end

  # 私有函数 - 获取优先级显示名称
  defp get_priority_display_name(priority) do
    Map.get(@priority_display_names, priority, "未知")
  end

  # 私有函数 - 格式化时间
  defp format_time(nil), do: "-"
  defp format_time(datetime) do
    case Cypridina.Utils.TimeHelper.format_local_datetime(datetime) do
      formatted when is_binary(formatted) -> formatted
      _ -> "无效时间"
    end
  rescue
    _ -> "无效时间"
  end

  # 私有函数 - 截断文本
  defp truncate_text(text, max_length \\ @max_content_preview_length) do
    if String.length(text) > max_length do
      String.slice(text, 0, max_length) <> "..."
    else
      text
    end
  end
end
