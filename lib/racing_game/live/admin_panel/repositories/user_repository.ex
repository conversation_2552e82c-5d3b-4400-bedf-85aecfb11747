defmodule RacingGame.Live.AdminPanel.Repositories.UserRepository do
  @moduledoc """
  用户数据访问仓储
  
  提供用户相关的数据访问抽象，隔离数据访问细节：
  - 用户查询和过滤
  - 复杂查询构建
  - 数据缓存管理
  - 查询优化
  """

  require Logger
  alias Ash.Query
  import Ash.Query
  alias Cypridina.Accounts.{User, AgentRelationship}
  alias RacingGame.Live.AdminPanel.Repositories.QueryBuilders.UserQueryBuilder
  require Ash.Query

  # 常量定义
  @cache_ttl 300  # 5分钟缓存
  @default_limit 20
  @max_limit 100

  # ============================================================================
  # 基础查询方法
  # ============================================================================

  @doc """
  根据ID获取用户

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项
    - `:preload` - 预加载关联数据
    - `:use_cache` - 是否使用缓存

  ## 返回
  - `{:ok, user}` - 成功
  - `{:error, :not_found}` - 用户不存在
  - `{:error, reason}` - 其他错误
  """
  def get_by_id(user_id, options \\ []) do
    Logger.debug("🔍 [用户仓储] 根据ID获取用户: #{user_id}")
    
    preload = Keyword.get(options, :preload, [])
    use_cache = Keyword.get(options, :use_cache, true)
    
    cache_key = build_cache_key("user", user_id, preload)
    
    if use_cache do
      case get_from_cache(cache_key) do
        {:ok, user} -> {:ok, user}
        :miss -> fetch_and_cache_user_by_id(user_id, preload, cache_key)
      end
    else
      fetch_user_by_id(user_id, preload)
    end
  end

  @doc """
  根据用户名获取用户

  ## 参数
  - `username` - 用户名
  - `options` - 选项

  ## 返回
  - `{:ok, user}` - 成功
  - `{:error, :not_found}` - 用户不存在
  - `{:error, reason}` - 其他错误
  """
  def get_by_username(username, options \\ []) do
    Logger.debug("🔍 [用户仓储] 根据用户名获取用户: #{username}")
    
    preload = Keyword.get(options, :preload, [])
    
    try do
      case User
           |> Ash.Query.filter(username == username)
           |> maybe_preload(preload)
           |> Ash.read() do
        {:ok, [user]} -> {:ok, user}
        {:ok, []} -> {:error, :not_found}
        {:error, error} -> 
          Logger.error("❌ [用户仓储] 查询用户失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [用户仓储] 查询用户异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  分页查询用户

  ## 参数
  - `params` - 查询参数
    - `:page` - 页码
    - `:limit` - 每页数量
    - `:search` - 搜索关键词
    - `:filters` - 过滤条件
    - `:sort` - 排序条件
    - `:preload` - 预加载关联

  ## 返回
  - `{:ok, %{users: users, page_info: page_info}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_paginated(params \\ %{}) do
    Logger.debug("📋 [用户仓储] 分页查询用户")
    
    try do
      normalized_params = normalize_list_params(params)
      
      with {:ok, query} <- UserQueryBuilder.build_list_query(normalized_params),
           {:ok, results} <- execute_paginated_query(query, normalized_params) do
        {:ok, results}
      else
        {:error, reason} -> {:error, reason}
      end
    rescue
      error ->
        Logger.error("❌ [用户仓储] 分页查询异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  搜索用户

  ## 参数
  - `search_term` - 搜索词
  - `options` - 选项
    - `:limit` - 结果数量限制
    - `:fields` - 搜索字段

  ## 返回
  - `{:ok, users}` - 成功
  - `{:error, reason}` - 失败
  """
  def search(search_term, options \\ []) do
    Logger.debug("🔍 [用户仓储] 搜索用户: #{search_term}")
    
    limit = Keyword.get(options, :limit, @default_limit)
    fields = Keyword.get(options, :fields, [:username, :email])
    
    try do
      query = UserQueryBuilder.build_search_query(search_term, fields)
      
      case query
           |> Ash.Query.limit(limit)
           |> Ash.read() do
        {:ok, users} -> {:ok, users}
        {:error, error} ->
          Logger.error("❌ [用户仓储] 搜索失败: #{inspect(error)}")
          {:error, :search_failed}
      end
    rescue
      error ->
        Logger.error("❌ [用户仓储] 搜索异常: #{inspect(error)}")
        {:error, :search_error}
    end
  end

  # ============================================================================
  # 关联数据查询
  # ============================================================================

  @doc """
  获取用户的代理关系

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - `{:ok, agent_info}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_agent_relationship(user_id, options \\ []) do
    Logger.debug("🔗 [用户仓储] 获取用户代理关系: #{user_id}")
    
    use_cache = Keyword.get(options, :use_cache, true)
    cache_key = build_cache_key("agent_relationship", user_id)
    
    if use_cache do
      case get_from_cache(cache_key) do
        {:ok, agent_info} -> {:ok, agent_info}
        :miss -> fetch_and_cache_agent_relationship(user_id, cache_key)
      end
    else
      fetch_agent_relationship(user_id)
    end
  end

  @doc """
  获取用户的下级列表

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - `{:ok, subordinates}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_subordinates(user_id, options \\ []) do
    Logger.debug("👥 [用户仓储] 获取用户下级: #{user_id}")
    
    try do
      case AgentRelationship
           |> Ash.Query.filter(agent_id == user_id and status == 1)
           |> Ash.Query.load([:subordinate])
           |> Ash.read() do
        {:ok, relationships} ->
          subordinates = Enum.map(relationships, & &1.subordinate)
          {:ok, subordinates}
        {:error, error} ->
          Logger.error("❌ [用户仓储] 获取下级失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [用户仓储] 获取下级异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # ============================================================================
  # 统计查询
  # ============================================================================

  @doc """
  获取用户统计信息

  ## 参数
  - `user_id` - 用户ID（可选，为空时获取全局统计）
  - `options` - 选项

  ## 返回
  - `{:ok, statistics}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_statistics(user_id \\ nil, options \\ []) do
    Logger.debug("📊 [用户仓储] 获取用户统计: #{user_id || "全局"}")
    
    try do
      stats = if user_id do
        get_user_specific_statistics(user_id, options)
      else
        get_global_user_statistics(options)
      end
      
      {:ok, stats}
    rescue
      error ->
        Logger.error("❌ [用户仓储] 获取统计异常: #{inspect(error)}")
        {:error, :statistics_error}
    end
  end

  # ============================================================================
  # 缓存管理
  # ============================================================================

  @doc """
  清除用户相关缓存

  ## 参数
  - `user_id` - 用户ID
  """
  def clear_cache(user_id) do
    Logger.debug("🧹 [用户仓储] 清除用户缓存: #{user_id}")
    
    cache_patterns = [
      "user:#{user_id}:*",
      "agent_relationship:#{user_id}",
      "subordinates:#{user_id}"
    ]
    
    Enum.each(cache_patterns, &clear_cache_pattern/1)
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 标准化列表参数
  defp normalize_list_params(params) do
    %{
      page: max(1, params[:page] || 1),
      limit: min(params[:limit] || @default_limit, @max_limit),
      search: String.trim(params[:search] || ""),
      filters: params[:filters] || %{},
      sort: params[:sort] || [inserted_at: :desc],
      preload: params[:preload] || []
    }
  end

  # 获取并缓存用户
  defp fetch_and_cache_user_by_id(user_id, preload, cache_key) do
    case fetch_user_by_id(user_id, preload) do
      {:ok, user} ->
        cache_put(cache_key, user, @cache_ttl)
        {:ok, user}
      error -> error
    end
  end

  # 获取用户
  defp fetch_user_by_id(user_id, preload) do
    try do
      case User
           |> maybe_preload(preload)
           |> Ash.read(user_id) do
        {:ok, user} -> {:ok, user}
        {:error, %Ash.Error.Query.NotFound{}} -> {:error, :not_found}
        {:error, error} ->
          Logger.error("❌ [用户仓储] 获取用户失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [用户仓储] 获取用户异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # 执行分页查询
  defp execute_paginated_query(query, params) do
    offset = (params.page - 1) * params.limit
    
    case query
         |> maybe_preload(params.preload)
         |> Ash.Query.page(count: true, limit: params.limit, offset: offset)
         |> Ash.read() do
      {:ok, %Ash.Page.Offset{results: users, count: total_count}} ->
        page_info = %{
          total_count: total_count,
          page: params.page,
          limit: params.limit,
          total_pages: ceil(total_count / params.limit)
        }
        {:ok, %{users: users, page_info: page_info}}
      {:error, error} ->
        Logger.error("❌ [用户仓储] 分页查询失败: #{inspect(error)}")
        {:error, :query_failed}
    end
  end

  # 获取并缓存代理关系
  defp fetch_and_cache_agent_relationship(user_id, cache_key) do
    case fetch_agent_relationship(user_id) do
      {:ok, agent_info} ->
        cache_put(cache_key, agent_info, @cache_ttl)
        {:ok, agent_info}
      error -> error
    end
  end

  # 获取代理关系
  defp fetch_agent_relationship(user_id) do
    try do
      case AgentRelationship
           |> Ash.Query.filter(subordinate_id == user_id and status == 1)
           |> Ash.Query.load([:agent])
           |> Ash.read() do
        {:ok, [relationship]} ->
          agent_info = %{
            has_agent: true,
            agent: relationship.agent,
            commission_rate: relationship.commission_rate,
            level: relationship.level,
            created_at: relationship.inserted_at
          }
          {:ok, agent_info}
        {:ok, []} ->
          agent_info = %{has_agent: false}
          {:ok, agent_info}
        {:error, error} ->
          Logger.error("❌ [用户仓储] 获取代理关系失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [用户仓储] 获取代理关系异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # 获取用户特定统计
  defp get_user_specific_statistics(user_id, _options) do
    # TODO: 实现用户特定统计
    %{
      user_id: user_id,
      total_bets: 0,
      total_winnings: 0,
      subordinate_count: 0
    }
  end

  # 获取全局用户统计
  defp get_global_user_statistics(_options) do
    # TODO: 实现全局用户统计
    %{
      total_users: 0,
      active_users: 0,
      new_users_today: 0,
      agents_count: 0
    }
  end

  # 可能预加载关联数据
  defp maybe_preload(query, []), do: query
  defp maybe_preload(query, preload) when is_list(preload) do
    Ash.Query.load(query, preload)
  end

  # 构建缓存键
  defp build_cache_key(prefix, id, extra \\ nil) do
    case extra do
      nil -> "#{prefix}:#{id}"
      extra -> "#{prefix}:#{id}:#{:erlang.phash2(extra)}"
    end
  end

  # 缓存操作（需要实现具体的缓存后端）
  defp get_from_cache(_key), do: :miss
  defp cache_put(_key, _value, _ttl), do: :ok
  defp clear_cache_pattern(_pattern), do: :ok
end
