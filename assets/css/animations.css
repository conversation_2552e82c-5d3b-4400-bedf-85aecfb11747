/**
 * Racing Game Admin Panel - 动画样式库
 * 
 * 高性能CSS动画和过渡效果
 */

/* ===== 基础动画配置 ===== */
:root {
  /* 动画时长 */
  --anim-duration-fast: 150ms;
  --anim-duration-normal: 300ms;
  --anim-duration-slow: 500ms;
  --anim-duration-slower: 800ms;

  /* 缓动函数 */
  --anim-ease-out: cubic-bezier(0.4, 0, 0.2, 1);
  --anim-ease-in: cubic-bezier(0.4, 0, 1, 1);
  --anim-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --anim-ease-bounce: cubic-bezier(0.34, 1.56, 0.64, 1);
  --anim-ease-elastic: cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* 动画延迟 */
  --anim-delay-1: 100ms;
  --anim-delay-2: 200ms;
  --anim-delay-3: 300ms;
  --anim-delay-4: 400ms;
  --anim-delay-5: 500ms;
}

/* ===== GPU加速基础类 ===== */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform, opacity;
  backface-visibility: hidden;
  perspective: 1000px;
}

.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.will-change-auto {
  will-change: auto;
}

/* ===== 淡入淡出动画 ===== */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-10px);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== 滑动动画 ===== */
@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideOutLeft {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(-100%);
    opacity: 0;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* ===== 缩放动画 ===== */
@keyframes scaleIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes scaleOut {
  from {
    transform: scale(1);
    opacity: 1;
  }
  to {
    transform: scale(0.8);
    opacity: 0;
  }
}

@keyframes scaleInBounce {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  70% {
    transform: scale(0.9);
    opacity: 0.9;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* ===== 旋转动画 ===== */
@keyframes rotateIn {
  from {
    transform: rotate(-180deg) scale(0.8);
    opacity: 0;
  }
  to {
    transform: rotate(0deg) scale(1);
    opacity: 1;
  }
}

@keyframes rotateOut {
  from {
    transform: rotate(0deg) scale(1);
    opacity: 1;
  }
  to {
    transform: rotate(180deg) scale(0.8);
    opacity: 0;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* ===== 弹跳和摇摆动画 ===== */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translateY(0);
  }
  40%, 43% {
    transform: translateY(-10px);
  }
  70% {
    transform: translateY(-5px);
  }
  90% {
    transform: translateY(-2px);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-10px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(10px);
  }
}

@keyframes wobble {
  0% {
    transform: translateX(0%);
  }
  15% {
    transform: translateX(-25%) rotate(-5deg);
  }
  30% {
    transform: translateX(20%) rotate(3deg);
  }
  45% {
    transform: translateX(-15%) rotate(-3deg);
  }
  60% {
    transform: translateX(10%) rotate(2deg);
  }
  75% {
    transform: translateX(-5%) rotate(-1deg);
  }
  100% {
    transform: translateX(0%);
  }
}

/* ===== 脉冲和闪烁动画 ===== */
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes heartbeat {
  0% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.3);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.3);
  }
  70% {
    transform: scale(1);
  }
}

@keyframes flash {
  0%, 50%, 100% {
    opacity: 1;
  }
  25%, 75% {
    opacity: 0;
  }
}

/* ===== 加载动画 ===== */
@keyframes loading-dots {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes loading-spinner {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes loading-wave {
  0%, 60%, 100% {
    transform: initial;
  }
  30% {
    transform: translateY(-15px);
  }
}

/* ===== 动画工具类 ===== */
.animate-fade-in {
  animation: fadeIn var(--anim-duration-normal) var(--anim-ease-out) forwards;
}

.animate-fade-out {
  animation: fadeOut var(--anim-duration-normal) var(--anim-ease-in) forwards;
}

.animate-slide-in-left {
  animation: slideInLeft var(--anim-duration-normal) var(--anim-ease-out) forwards;
}

.animate-slide-in-right {
  animation: slideInRight var(--anim-duration-normal) var(--anim-ease-out) forwards;
}

.animate-slide-in-up {
  animation: slideInUp var(--anim-duration-normal) var(--anim-ease-out) forwards;
}

.animate-slide-in-down {
  animation: slideInDown var(--anim-duration-normal) var(--anim-ease-out) forwards;
}

.animate-scale-in {
  animation: scaleIn var(--anim-duration-normal) var(--anim-ease-bounce) forwards;
}

.animate-scale-out {
  animation: scaleOut var(--anim-duration-normal) var(--anim-ease-in) forwards;
}

.animate-rotate-in {
  animation: rotateIn var(--anim-duration-slow) var(--anim-ease-out) forwards;
}

.animate-bounce {
  animation: bounce var(--anim-duration-slower) var(--anim-ease-out);
}

.animate-shake {
  animation: shake var(--anim-duration-slow) var(--anim-ease-in-out);
}

.animate-pulse {
  animation: pulse 2s var(--anim-ease-in-out) infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* ===== 过渡效果类 ===== */
.transition-all {
  transition: all var(--anim-duration-normal) var(--anim-ease-out);
}

.transition-transform {
  transition: transform var(--anim-duration-normal) var(--anim-ease-out);
}

.transition-opacity {
  transition: opacity var(--anim-duration-normal) var(--anim-ease-out);
}

.transition-colors {
  transition: color var(--anim-duration-normal) var(--anim-ease-out),
              background-color var(--anim-duration-normal) var(--anim-ease-out),
              border-color var(--anim-duration-normal) var(--anim-ease-out);
}

/* ===== 悬停效果 ===== */
.hover-lift {
  transition: transform var(--anim-duration-fast) var(--anim-ease-out);
}

.hover-lift:hover {
  transform: translateY(-2px);
}

.hover-scale {
  transition: transform var(--anim-duration-fast) var(--anim-ease-out);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-glow {
  transition: box-shadow var(--anim-duration-fast) var(--anim-ease-out);
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
}

/* ===== 焦点效果 ===== */
.focus-ring {
  transition: box-shadow var(--anim-duration-fast) var(--anim-ease-out);
}

.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
}

/* ===== 加载状态动画 ===== */
.loading-dots {
  display: inline-flex;
  gap: 4px;
}

.loading-dots span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: currentColor;
  animation: loading-dots 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }
.loading-dots span:nth-child(3) { animation-delay: 0s; }

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-left-color: currentColor;
  border-radius: 50%;
  animation: loading-spinner 1s linear infinite;
}

.loading-wave {
  display: inline-flex;
  gap: 2px;
}

.loading-wave span {
  width: 4px;
  height: 20px;
  background-color: currentColor;
  animation: loading-wave 1.2s ease-in-out infinite;
}

.loading-wave span:nth-child(1) { animation-delay: -1.1s; }
.loading-wave span:nth-child(2) { animation-delay: -1.0s; }
.loading-wave span:nth-child(3) { animation-delay: -0.9s; }
.loading-wave span:nth-child(4) { animation-delay: -0.8s; }
.loading-wave span:nth-child(5) { animation-delay: -0.7s; }

/* ===== 响应式动画控制 ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* ===== 动画延迟工具类 ===== */
.delay-100 { animation-delay: var(--anim-delay-1); }
.delay-200 { animation-delay: var(--anim-delay-2); }
.delay-300 { animation-delay: var(--anim-delay-3); }
.delay-400 { animation-delay: var(--anim-delay-4); }
.delay-500 { animation-delay: var(--anim-delay-5); }

/* ===== 动画持续时间工具类 ===== */
.duration-fast { animation-duration: var(--anim-duration-fast); }
.duration-normal { animation-duration: var(--anim-duration-normal); }
.duration-slow { animation-duration: var(--anim-duration-slow); }
.duration-slower { animation-duration: var(--anim-duration-slower); }

/* ===== 页面过渡效果 ===== */
.page-transition-loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: opacity var(--anim-duration-normal) var(--anim-ease-out),
              visibility var(--anim-duration-normal) var(--anim-ease-out);
}

.page-transition-loader.active {
  opacity: 1;
  visibility: visible;
}

.page-transition-loader .loader-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(4px);
}

.page-transition-loader .loader-content {
  position: relative;
  z-index: 1;
  text-align: center;
  color: white;
  background: rgba(0, 0, 0, 0.7);
  padding: 2rem;
  border-radius: 1rem;
  backdrop-filter: blur(8px);
}

.page-transition-loader .loading-spinner {
  margin: 0 auto 1rem;
}

/* ===== 页面容器过渡状态 ===== */
[data-page-container] {
  transition: opacity var(--anim-duration-normal) var(--anim-ease-out),
              transform var(--anim-duration-normal) var(--anim-ease-out);
}

.page-transitioning-out {
  opacity: 0;
  transform: translateY(-10px);
}

.page-transitioning-in {
  opacity: 0;
  transform: translateY(20px);
}

/* ===== 模态框过渡效果 ===== */
.modal-backdrop {
  transition: opacity var(--anim-duration-normal) var(--anim-ease-out);
}

.modal-content {
  transition: opacity var(--anim-duration-normal) var(--anim-ease-out),
              transform var(--anim-duration-normal) var(--anim-ease-out);
}

.modal-active .modal-backdrop {
  opacity: 1;
}

.modal-active .modal-content {
  opacity: 1;
  transform: scale(1) translateY(0);
}

/* ===== 手风琴过渡效果 ===== */
.accordion-content {
  overflow: hidden;
  transition: height var(--anim-duration-normal) var(--anim-ease-out),
              opacity var(--anim-duration-normal) var(--anim-ease-out);
}

.accordion-content:not(.expanded) {
  height: 0;
  opacity: 0.7;
}

/* ===== 标签页过渡效果 ===== */
.tab-content {
  transition: opacity var(--anim-duration-fast) var(--anim-ease-out),
              transform var(--anim-duration-fast) var(--anim-ease-out);
}

.tab-content.tab-entering {
  opacity: 0;
  transform: translateX(20px);
}

.tab-content.tab-leaving {
  opacity: 0;
  transform: translateX(-20px);
}
