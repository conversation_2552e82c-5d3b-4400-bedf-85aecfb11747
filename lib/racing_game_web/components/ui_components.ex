defmodule RacingGameWeb.UIComponents do
  @moduledoc """
  现代化UI组件库 - Racing Game Admin Panel

  提供一套完整的、可复用的UI组件，基于DaisyUI和TailwindCSS构建，
  遵循统一的设计系统规范，支持主题切换和响应式设计。
  """

  use Phoenix.Component
  alias Phoenix.LiveView.JS
  import RacingGameWeb.Gettext

  @doc """
  现代化按钮组件

  ## 示例

      <.button variant="primary" size="md" class="w-full">
        <.icon name="hero-plus" class="w-4 h-4 mr-2" />
        创建用户
      </.button>

      <.button variant="danger" size="sm" loading={@loading}>
        删除
      </.button>
  """
  attr :variant, :string, default: "primary", values: ~w(primary secondary success warning danger ghost outline)
  attr :size, :string, default: "md", values: ~w(xs sm md lg xl)
  attr :loading, :boolean, default: false
  attr :disabled, :boolean, default: false
  attr :full_width, :boolean, default: false
  attr :class, :string, default: ""
  attr :rest, :global, include: ~w(type form name value disabled)

  slot :inner_block, required: true

  def button(assigns) do
    ~H"""
    <button
      class={[
        "btn",
        variant_class(@variant),
        size_class(@size),
        @full_width && "btn-block",
        @loading && "loading",
        @class
      ]}
      disabled={@disabled or @loading}
      {@rest}
    >
      <%= if @loading do %>
        <span class="loading loading-spinner loading-sm mr-2"></span>
      <% end %>
      <%= render_slot(@inner_block) %>
    </button>
    """
  end

  @doc """
  现代化输入框组件

  ## 示例

      <.input
        type="text"
        name="username"
        label="用户名"
        placeholder="请输入用户名"
        required
        error={@errors[:username]}
      />

      <.input
        type="email"
        name="email"
        label="邮箱地址"
        helper_text="我们不会分享您的邮箱"
        icon="hero-envelope"
      />
  """
  attr :type, :string, default: "text"
  attr :name, :string, required: true
  attr :label, :string, default: nil
  attr :placeholder, :string, default: nil
  attr :value, :any, default: nil
  attr :error, :string, default: nil
  attr :helper_text, :string, default: nil
  attr :required, :boolean, default: false
  attr :disabled, :boolean, default: false
  attr :icon, :string, default: nil
  attr :size, :string, default: "md", values: ~w(xs sm md lg)
  attr :class, :string, default: ""
  attr :rest, :global

  def input(assigns) do
    ~H"""
    <div class="form-control w-full">
      <%= if @label do %>
        <label class="label">
          <span class="label-text font-medium">
            <%= @label %>
            <%= if @required do %>
              <span class="text-error">*</span>
            <% end %>
          </span>
        </label>
      <% end %>

      <div class="relative">
        <%= if @icon do %>
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <.icon name={@icon} class="w-5 h-5 text-gray-400" />
          </div>
        <% end %>

        <input
          type={@type}
          name={@name}
          value={@value}
          placeholder={@placeholder}
          disabled={@disabled}
          class={[
            "input input-bordered w-full",
            input_size_class(@size),
            @icon && "pl-10",
            @error && "input-error",
            @class
          ]}
          {@rest}
        />
      </div>

      <%= if @error do %>
        <label class="label">
          <span class="label-text-alt text-error">
            <.icon name="hero-exclamation-triangle" class="w-4 h-4 inline mr-1" />
            <%= @error %>
          </span>
        </label>
      <% end %>

      <%= if @helper_text && !@error do %>
        <label class="label">
          <span class="label-text-alt text-gray-500">
            <%= @helper_text %>
          </span>
        </label>
      <% end %>
    </div>
    """
  end

  @doc """
  现代化卡片组件

  ## 示例

      <.card title="用户统计" class="shadow-lg">
        <:actions>
          <.button variant="ghost" size="sm">查看详情</.button>
        </:actions>

        <div class="stats">
          <div class="stat">
            <div class="stat-value">1,234</div>
            <div class="stat-desc">总用户数</div>
          </div>
        </div>
      </.card>
  """
  attr :title, :string, default: nil
  attr :subtitle, :string, default: nil
  attr :compact, :boolean, default: false
  attr :bordered, :boolean, default: false
  attr :class, :string, default: ""

  slot :actions
  slot :inner_block, required: true

  def card(assigns) do
    ~H"""
    <div class={[
      "card bg-base-100",
      @bordered && "card-bordered",
      @compact && "card-compact",
      @class
    ]}>
      <div class="card-body">
        <%= if @title || @subtitle || @actions != [] do %>
          <div class="flex items-start justify-between">
            <div>
              <%= if @title do %>
                <h2 class="card-title text-lg font-semibold"><%= @title %></h2>
              <% end %>
              <%= if @subtitle do %>
                <p class="text-sm text-gray-500 mt-1"><%= @subtitle %></p>
              <% end %>
            </div>
            <%= if @actions != [] do %>
              <div class="card-actions">
                <%= for action <- @actions do %>
                  <%= render_slot(action) %>
                <% end %>
              </div>
            <% end %>
          </div>
        <% end %>

        <div class="mt-4">
          <%= render_slot(@inner_block) %>
        </div>
      </div>
    </div>
    """
  end

  @doc """
  现代化模态框组件

  ## 示例

      <.modal id="user-modal" title="编辑用户" size="lg">
        <:actions>
          <.button variant="primary">保存</.button>
          <.button variant="ghost" phx-click={JS.hide(to: "#user-modal")}>取消</.button>
        </:actions>

        <.input name="username" label="用户名" />
        <.input name="email" label="邮箱" />
      </.modal>
  """
  attr :id, :string, required: true
  attr :title, :string, default: nil
  attr :size, :string, default: "md", values: ~w(sm md lg xl)
  attr :closable, :boolean, default: true
  attr :class, :string, default: ""

  slot :actions
  slot :inner_block, required: true

  def modal(assigns) do
    ~H"""
    <dialog id={@id} class="modal">
      <div class={[
        "modal-box",
        modal_size_class(@size),
        @class
      ]}>
        <%= if @title do %>
          <div class="flex items-center justify-between mb-4">
            <h3 class="font-bold text-lg"><%= @title %></h3>
            <%= if @closable do %>
              <button
                class="btn btn-sm btn-circle btn-ghost"
                phx-click={JS.hide(to: "##{@id}")}
              >
                <.icon name="hero-x-mark" class="w-5 h-5" />
              </button>
            <% end %>
          </div>
        <% end %>

        <div class="py-4">
          <%= render_slot(@inner_block) %>
        </div>

        <%= if @actions != [] do %>
          <div class="modal-action">
            <%= for action <- @actions do %>
              <%= render_slot(action) %>
            <% end %>
          </div>
        <% end %>
      </div>

      <%= if @closable do %>
        <form method="dialog" class="modal-backdrop">
          <button>close</button>
        </form>
      <% end %>
    </dialog>
    """
  end

  @doc """
  现代化表格组件

  ## 示例

      <.table rows={@users} row_id={&"user-#{&1.id}"}>
        <:col :let={user} label="用户名">
          <div class="flex items-center space-x-3">
            <.avatar src={user.avatar} name={user.username} size="sm" />
            <div>
              <div class="font-bold"><%= user.username %></div>
              <div class="text-sm opacity-50">ID: <%= user.id %></div>
            </div>
          </div>
        </:col>

        <:col :let={user} label="状态">
          <.badge variant={user.active && "success" || "warning"}>
            <%= user.active && "活跃" || "非活跃" %>
          </.badge>
        </:col>

        <:action :let={user}>
          <.button variant="ghost" size="xs">编辑</.button>
          <.button variant="ghost" size="xs" class="text-error">删除</.button>
        </:action>
      </.table>
  """
  attr :rows, :list, required: true
  attr :row_id, :any, default: nil, doc: "function to generate row IDs"
  attr :zebra, :boolean, default: true
  attr :compact, :boolean, default: false
  attr :class, :string, default: ""

  slot :col, required: true do
    attr :label, :string
    attr :sortable, :boolean
  end

  slot :action, doc: "action buttons for each row"

  def table(assigns) do
    assigns =
      with %{rows: %Phoenix.LiveView.LiveStream{}} <- assigns do
        assign(assigns, row_id: assigns.row_id || fn {id, _item} -> id end)
      end

    ~H"""
    <div class="overflow-x-auto">
      <table class={[
        "table w-full",
        @zebra && "table-zebra",
        @compact && "table-compact",
        @class
      ]}>
        <thead>
          <tr>
            <%= for col <- @col do %>
              <th class="font-semibold">
                <%= if col[:sortable] do %>
                  <button class="flex items-center space-x-1 hover:text-primary">
                    <span><%= col[:label] %></span>
                    <.icon name="hero-chevron-up-down" class="w-4 h-4" />
                  </button>
                <% else %>
                  <%= col[:label] %>
                <% end %>
              </th>
            <% end %>
            <%= if @action != [] do %>
              <th class="text-right">操作</th>
            <% end %>
          </tr>
        </thead>
        <tbody id={@row_id && "#{@row_id}-tbody"} phx-update={match?(%Phoenix.LiveView.LiveStream{}, @rows) && "stream"}>
          <%= for row <- @rows do %>
            <tr id={@row_id && @row_id.(row)} class="hover">
              <%= for col <- @col do %>
                <td>
                  <%= render_slot(col, row) %>
                </td>
              <% end %>
              <%= if @action != [] do %>
                <td class="text-right">
                  <div class="flex items-center justify-end space-x-2">
                    <%= for action <- @action do %>
                      <%= render_slot(action, row) %>
                    <% end %>
                  </div>
                </td>
              <% end %>
            </tr>
          <% end %>
        </tbody>
      </table>
    </div>
    """
  end

  @doc """
  现代化头像组件

  ## 示例

      <.avatar src={@user.avatar} name={@user.username} size="lg" />
      <.avatar name="张三" size="md" online />
  """
  attr :src, :string, default: nil
  attr :name, :string, required: true
  attr :size, :string, default: "md", values: ~w(xs sm md lg xl)
  attr :online, :boolean, default: false
  attr :class, :string, default: ""

  def avatar(assigns) do
    ~H"""
    <div class={[
      "avatar",
      @online && "online",
      @class
    ]}>
      <div class={[
        "rounded-full",
        avatar_size_class(@size)
      ]}>
        <%= if @src do %>
          <img src={@src} alt={@name} />
        <% else %>
          <div class="bg-primary text-primary-content flex items-center justify-center font-semibold">
            <%= String.first(@name) %>
          </div>
        <% end %>
      </div>
    </div>
    """
  end

  @doc """
  现代化徽章组件

  ## 示例

      <.badge variant="success">活跃</.badge>
      <.badge variant="warning" size="sm">待审核</.badge>
  """
  attr :variant, :string, default: "neutral", values: ~w(neutral primary secondary success warning error info)
  attr :size, :string, default: "md", values: ~w(xs sm md lg)
  attr :outline, :boolean, default: false
  attr :class, :string, default: ""

  slot :inner_block, required: true

  def badge(assigns) do
    ~H"""
    <span class={[
      "badge",
      badge_variant_class(@variant),
      badge_size_class(@size),
      @outline && "badge-outline",
      @class
    ]}>
      <%= render_slot(@inner_block) %>
    </span>
    """
  end

  @doc """
  图标组件 - 使用Heroicons
  """
  attr :name, :string, required: true
  attr :class, :string, default: "w-5 h-5"

  def icon(assigns) do
    ~H"""
    <span class={[@name, @class]} />
    """
  end

  # 私有辅助函数

  defp variant_class("primary"), do: "btn-primary"
  defp variant_class("secondary"), do: "btn-secondary"
  defp variant_class("success"), do: "btn-success"
  defp variant_class("warning"), do: "btn-warning"
  defp variant_class("danger"), do: "btn-error"
  defp variant_class("ghost"), do: "btn-ghost"
  defp variant_class("outline"), do: "btn-outline"
  defp variant_class(_), do: "btn-primary"

  defp size_class("xs"), do: "btn-xs"
  defp size_class("sm"), do: "btn-sm"
  defp size_class("md"), do: "btn-md"
  defp size_class("lg"), do: "btn-lg"
  defp size_class("xl"), do: "btn-xl"
  defp size_class(_), do: "btn-md"

  defp input_size_class("xs"), do: "input-xs"
  defp input_size_class("sm"), do: "input-sm"
  defp input_size_class("md"), do: "input-md"
  defp input_size_class("lg"), do: "input-lg"
  defp input_size_class(_), do: "input-md"

  defp modal_size_class("sm"), do: "max-w-sm"
  defp modal_size_class("md"), do: "max-w-2xl"
  defp modal_size_class("lg"), do: "max-w-4xl"
  defp modal_size_class("xl"), do: "max-w-6xl"
  defp modal_size_class(_), do: "max-w-2xl"

  defp avatar_size_class("xs"), do: "w-6 h-6"
  defp avatar_size_class("sm"), do: "w-8 h-8"
  defp avatar_size_class("md"), do: "w-12 h-12"
  defp avatar_size_class("lg"), do: "w-16 h-16"
  defp avatar_size_class("xl"), do: "w-20 h-20"
  defp avatar_size_class(_), do: "w-12 h-12"

  defp badge_variant_class("neutral"), do: "badge-neutral"
  defp badge_variant_class("primary"), do: "badge-primary"
  defp badge_variant_class("secondary"), do: "badge-secondary"
  defp badge_variant_class("success"), do: "badge-success"
  defp badge_variant_class("warning"), do: "badge-warning"
  defp badge_variant_class("error"), do: "badge-error"
  defp badge_variant_class("info"), do: "badge-info"
  defp badge_variant_class(_), do: "badge-neutral"

  defp badge_size_class("xs"), do: "badge-xs"
  defp badge_size_class("sm"), do: "badge-sm"
  defp badge_size_class("md"), do: "badge-md"
  defp badge_size_class("lg"), do: "badge-lg"
  defp badge_size_class(_), do: "badge-md"
end
