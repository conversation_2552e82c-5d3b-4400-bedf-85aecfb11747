defmodule Teen.GameManagement.VipLevel do
  @moduledoc """
  VIP等级资源
  
  管理VIP等级配置，包括等级要求、特权等
  """
  
  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.GameManagement,
    extensions: [AshAdmin.Resource]

  postgres do
    table "vip_levels"
    repo Cypridina.Repo
  end

  admin do
    table_columns [:id, :level, :level_name, :recharge_requirement, :status]
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_levels
    define :get_by_level
  end

  actions do
    defaults [:create, :read, :update, :destroy]

    read :list_active_levels do
      filter expr(status == 1)
    end

    read :get_by_level do
      argument :level, :integer, allow_nil?: false
      filter expr(level == ^arg(:level))
    end

    update :enable do
      change set_attribute(:status, 1)
    end

    update :disable do
      change set_attribute(:status, 0)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :level, :integer do
      allow_nil? false
      public? true
      description "VIP等级"
      constraints min: 0
    end

    attribute :level_name, :string do
      allow_nil? false
      public? true
      description "等级名称"
      constraints max_length: 50
    end

    attribute :recharge_requirement, :decimal do
      allow_nil? false
      public? true
      description "充值要求（分）"
      constraints min: Decimal.new("0")
    end

    attribute :daily_bonus, :decimal do
      allow_nil? false
      public? true
      description "每日奖励金币"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :exchange_rate_bonus, :decimal do
      allow_nil? false
      public? true
      description "兑换比例加成（%）"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :recharge_bonus, :decimal do
      allow_nil? false
      public? true
      description "充值奖励比例（%）"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :status, :integer do
      allow_nil? false
      public? true
      description "状态：0-禁用，1-启用"
      default 1
      constraints min: 0, max: 1
    end

    attribute :icon_url, :string do
      allow_nil? true
      public? true
      description "等级图标URL"
      constraints max_length: 500
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "等级描述"
      constraints max_length: 500
    end

    attribute :privileges, {:array, :string} do
      allow_nil? true
      public? true
      description "特权列表"
      default []
    end

    timestamps()
  end

  identities do
    identity :unique_vip_level, [:level]
    identity :unique_level_name, [:level_name]
  end
end
