defmodule Teen.PaymentSystem.PaymentConfig do
  @moduledoc """
  支付配置资源
  
  管理各种支付方式的配置，包括费率、限额、状态等
  """
  
  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.PaymentSystem,
    extensions: [AshAdmin.Resource]

  postgres do
    table "payment_configs"
    repo Cy<PERSON><PERSON><PERSON>.Repo
  end

  admin do
    table_columns [:id, :gateway_name, :payment_type_name, :min_amount, :max_amount, :fee_rate, :status]
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_configs
    define :list_by_gateway
    define :list_by_payment_type
  end

  actions do
    defaults [:create, :read, :update, :destroy]

    read :list_active_configs do
      filter expr(status == 1)
    end

    read :list_by_gateway do
      argument :gateway_id, :uuid, allow_nil?: false
      filter expr(gateway_id == ^arg(:gateway_id))
    end

    read :list_by_payment_type do
      argument :payment_type, :string, allow_nil?: false
      filter expr(payment_type == ^arg(:payment_type))
    end

    update :enable do
      change set_attribute(:status, 1)
    end

    update :disable do
      change set_attribute(:status, 0)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :gateway_id, :uuid do
      allow_nil? false
      public? true
      description "支付网关ID"
    end

    attribute :gateway_name, :string do
      allow_nil? false
      public? true
      description "支付网关名称"
      constraints max_length: 100
    end

    attribute :payment_type, :string do
      allow_nil? false
      public? true
      description "支付类型编码"
      constraints max_length: 50
    end

    attribute :payment_type_name, :string do
      allow_nil? false
      public? true
      description "支付类型名称"
      constraints max_length: 100
    end

    attribute :min_amount, :decimal do
      allow_nil? false
      public? true
      description "最小充值金额（分）"
      constraints min: Decimal.new("0")
    end

    attribute :max_amount, :decimal do
      allow_nil? false
      public? true
      description "最大充值金额（分）"
      constraints min: Decimal.new("0")
    end

    attribute :recharge_range, :string do
      allow_nil? true
      public? true
      description "充值区间配置"
      constraints max_length: 500
    end

    attribute :bonus_range, :string do
      allow_nil? true
      public? true
      description "赠送区间配置"
      constraints max_length: 500
    end

    attribute :fee_rate, :decimal do
      allow_nil? false
      public? true
      description "费率（%）"
      default Decimal.new("0")
      constraints min: Decimal.new("0"), max: Decimal.new("100")
    end

    attribute :deduction_rate, :decimal do
      allow_nil? false
      public? true
      description "扣除费率（%）"
      default Decimal.new("0")
      constraints min: Decimal.new("0"), max: Decimal.new("100")
    end

    attribute :status, :integer do
      allow_nil? false
      public? true
      description "状态：0-关闭，1-开启"
      default 1
      constraints min: 0, max: 1
    end

    attribute :sort_order, :integer do
      allow_nil? false
      public? true
      description "排序"
      default 0
    end

    attribute :config_data, :map do
      allow_nil? true
      public? true
      description "配置数据（JSON）"
    end

    timestamps()
  end

  relationships do
    belongs_to :gateway, Teen.PaymentSystem.PaymentGateway do
      public? true
      source_attribute :gateway_id
      destination_attribute :id
    end
  end

  identities do
    identity :unique_gateway_payment_type, [:gateway_id, :payment_type]
  end
end
