// 图表工具函数
import ApexCharts from 'apexcharts'

// 创建排名走势图
export function createRankingChart(element, historyData, animals) {
    // 如果没有数据，直接返回
    if (!historyData || historyData.length === 0 || !animals || animals.length === 0) {
        console.error("没有足够的数据来绘制图表");
        return null;
    }

    // 处理数据为 ApexCharts 需要的格式
    const series = prepareSeriesData(historyData, animals);
    const categories = historyData.map(race => race.issue.slice(-4)); // 仅显示期号后四位

    // 配置图表选项
    const options = {
        series: series,
        chart: {
            height: 350,
            type: 'line',
            zoom: {
                enabled: false
            },
            toolbar: {
                show: false
            },
            fontFamily: 'inherit'
        },
        id: "realtime",
        animations: {
            enabled: true,
            easing: 'linear',
            dynamicAnimation: {
                speed: 1000
            }
        },
        colors: [
            '#FF4560', // 红色
            '#008FFB', // 蓝色
            '#FEB019', // 黄色
            '#00E396', // 绿色
            '#775DD0', // 紫色
            '#F86624'  // 橙色
        ],
        dataLabels: {
            enabled: false
        },
        stroke: {
            curve: 'straight',
            width: 2
        },
        grid: {
            row: {
                colors: ['#f3f3f3', 'transparent'],
                opacity: 0.5
            }
        },
        xaxis: {
            categories: categories,
            title: {
                text: '期号'
            },
        },
        yaxis: {
            title: {
                text: '身价'
            },
            // min: 1,
            // max: 1000,
            // reversed: true, // 反转Y轴，使得第一名在最上方
            // tickAmount: 5,
            labels: {
                formatter: function (val) {
                    return val.toFixed(0); // 显示整数
                }
            }
        },
        tooltip: {
            y: {
                formatter: function (val) {
                    return ` ${val} `;
                }
            }
        },
        legend: {
            position: 'top'
        }
    };

    // 创建和返回图表实例
    const chart = new ApexCharts(element, options);
    return chart;
}

// 准备排名数据
function prepareSeriesData(historyData, animals) {

    return animals.map(animal => {
        // 获取每场比赛中该动物的排名
        const rankingData = historyData.map(race => {
            const bet_amount_map = race.bet_amount_map;

            // 检查动物在各名次中的位置
            // if (race.champion === getAnimalIndex(animal)) return 
            // if (race.second === getAnimalIndex(animal)) return 2;
            // if (race.third === getAnimalIndex(animal)) return 3;
            // if (race.fourth === getAnimalIndex(animal)) return 4;
            // if (race.fifth === getAnimalIndex(animal)) return 5;
            // if (race.sixth === getAnimalIndex(animal)) return 6;
            // return null; // 如果没有找到，返回null
            return bet_amount_map[animal.id];
        });

        console.log("排名数据", rankingData);

        // 返回该动物的系列数据
        return {
            name: animal.name,
            data: rankingData
        };
    });
}
