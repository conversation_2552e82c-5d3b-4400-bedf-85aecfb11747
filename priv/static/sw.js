/**
 * Racing Game Admin Panel - Service Worker
 * 
 * 实现高级缓存策略和离线支持
 */

const CACHE_VERSION = 'racing-admin-v1.0.0';
const STATIC_CACHE = `${CACHE_VERSION}-static`;
const DYNAMIC_CACHE = `${CACHE_VERSION}-dynamic`;
const API_CACHE = `${CACHE_VERSION}-api`;

// 静态资源列表
const STATIC_ASSETS = [
  '/',
  '/css/app.css',
  '/js/app.js',
  '/images/logo.png',
  '/fonts/inter-var.woff2'
];

// API缓存策略配置
const API_CACHE_CONFIG = {
  maxAge: 5 * 60 * 1000, // 5分钟
  maxEntries: 100
};

// 动态缓存配置
const DYNAMIC_CACHE_CONFIG = {
  maxAge: 24 * 60 * 60 * 1000, // 24小时
  maxEntries: 50
};

/**
 * Service Worker安装事件
 */
self.addEventListener('install', (event) => {
  console.log('🔧 Service Worker installing...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then((cache) => {
        console.log('📦 Caching static assets...');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        console.log('✅ Static assets cached');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('❌ Failed to cache static assets:', error);
      })
  );
});

/**
 * Service Worker激活事件
 */
self.addEventListener('activate', (event) => {
  console.log('🚀 Service Worker activating...');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName.startsWith('racing-admin-') && cacheName !== STATIC_CACHE && 
                cacheName !== DYNAMIC_CACHE && cacheName !== API_CACHE) {
              console.log('🗑️ Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('✅ Service Worker activated');
        return self.clients.claim();
      })
  );
});

/**
 * 网络请求拦截
 */
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // 跳过非GET请求
  if (request.method !== 'GET') {
    return;
  }
  
  // 跳过Chrome扩展请求
  if (url.protocol === 'chrome-extension:') {
    return;
  }
  
  // 根据请求类型选择缓存策略
  if (isStaticAsset(url)) {
    event.respondWith(handleStaticAsset(request));
  } else if (isAPIRequest(url)) {
    event.respondWith(handleAPIRequest(request));
  } else if (isPageRequest(url)) {
    event.respondWith(handlePageRequest(request));
  } else {
    event.respondWith(handleDynamicRequest(request));
  }
});

/**
 * 判断是否为静态资源
 */
function isStaticAsset(url) {
  return url.pathname.match(/\.(css|js|png|jpg|jpeg|gif|svg|woff|woff2|ttf|eot|ico)$/);
}

/**
 * 判断是否为API请求
 */
function isAPIRequest(url) {
  return url.pathname.startsWith('/api/') || url.pathname.startsWith('/live/');
}

/**
 * 判断是否为页面请求
 */
function isPageRequest(url) {
  return url.pathname.startsWith('/admin_panel') || url.pathname === '/';
}

/**
 * 处理静态资源请求 - Cache First策略
 */
async function handleStaticAsset(request) {
  try {
    const cache = await caches.open(STATIC_CACHE);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      // 后台更新缓存
      updateCacheInBackground(request, cache);
      return cachedResponse;
    }
    
    // 缓存未命中，从网络获取
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.error('Static asset fetch failed:', error);
    return new Response('Asset not available', { status: 404 });
  }
}

/**
 * 处理API请求 - Network First策略
 */
async function handleAPIRequest(request) {
  try {
    // 尝试网络请求
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      // 缓存成功的API响应
      const cache = await caches.open(API_CACHE);
      const responseClone = networkResponse.clone();
      
      // 添加时间戳
      const responseWithTimestamp = new Response(responseClone.body, {
        status: responseClone.status,
        statusText: responseClone.statusText,
        headers: {
          ...Object.fromEntries(responseClone.headers.entries()),
          'sw-cached-at': Date.now().toString()
        }
      });
      
      cache.put(request, responseWithTimestamp);
      
      // 清理过期的API缓存
      cleanupAPICache(cache);
    }
    
    return networkResponse;
  } catch (error) {
    console.log('Network failed, trying cache for API request');
    
    // 网络失败，尝试缓存
    const cache = await caches.open(API_CACHE);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      const cachedAt = cachedResponse.headers.get('sw-cached-at');
      const age = Date.now() - parseInt(cachedAt || '0');
      
      if (age < API_CACHE_CONFIG.maxAge) {
        return cachedResponse;
      }
    }
    
    return new Response(JSON.stringify({ error: 'Network unavailable' }), {
      status: 503,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 处理页面请求 - Network First with Cache Fallback
 */
async function handlePageRequest(request) {
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.log('Network failed, trying cache for page request');
    
    const cache = await caches.open(DYNAMIC_CACHE);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // 返回离线页面
    return new Response(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Racing Game Admin - Offline</title>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            body { font-family: system-ui, sans-serif; text-align: center; padding: 2rem; }
            .offline-message { max-width: 400px; margin: 0 auto; }
            .icon { font-size: 4rem; margin-bottom: 1rem; }
            .retry-btn { 
              background: #3b82f6; color: white; border: none; 
              padding: 0.75rem 1.5rem; border-radius: 0.5rem; 
              cursor: pointer; margin-top: 1rem;
            }
          </style>
        </head>
        <body>
          <div class="offline-message">
            <div class="icon">🏎️</div>
            <h1>Racing Game Admin</h1>
            <p>您当前处于离线状态</p>
            <p>请检查网络连接后重试</p>
            <button class="retry-btn" onclick="window.location.reload()">重试</button>
          </div>
        </body>
      </html>
    `, {
      status: 200,
      headers: { 'Content-Type': 'text/html' }
    });
  }
}

/**
 * 处理动态请求 - Cache First with Network Fallback
 */
async function handleDynamicRequest(request) {
  try {
    const cache = await caches.open(DYNAMIC_CACHE);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      // 后台更新
      updateCacheInBackground(request, cache);
      return cachedResponse;
    }
    
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    return new Response('Resource not available', { status: 404 });
  }
}

/**
 * 后台更新缓存
 */
function updateCacheInBackground(request, cache) {
  fetch(request)
    .then((response) => {
      if (response.ok) {
        cache.put(request, response);
      }
    })
    .catch(() => {
      // 静默失败
    });
}

/**
 * 清理过期的API缓存
 */
async function cleanupAPICache(cache) {
  const requests = await cache.keys();
  const now = Date.now();
  
  for (const request of requests) {
    const response = await cache.match(request);
    const cachedAt = response.headers.get('sw-cached-at');
    const age = now - parseInt(cachedAt || '0');
    
    if (age > API_CACHE_CONFIG.maxAge) {
      await cache.delete(request);
    }
  }
  
  // 限制缓存条目数量
  if (requests.length > API_CACHE_CONFIG.maxEntries) {
    const excessRequests = requests.slice(API_CACHE_CONFIG.maxEntries);
    for (const request of excessRequests) {
      await cache.delete(request);
    }
  }
}

/**
 * 消息处理
 */
self.addEventListener('message', (event) => {
  const { type, payload } = event.data;
  
  switch (type) {
    case 'SKIP_WAITING':
      self.skipWaiting();
      break;
      
    case 'CLEAR_CACHE':
      clearAllCaches().then(() => {
        event.ports[0].postMessage({ success: true });
      });
      break;
      
    case 'GET_CACHE_STATUS':
      getCacheStatus().then((status) => {
        event.ports[0].postMessage(status);
      });
      break;
      
    default:
      console.log('Unknown message type:', type);
  }
});

/**
 * 清理所有缓存
 */
async function clearAllCaches() {
  const cacheNames = await caches.keys();
  return Promise.all(
    cacheNames.map(cacheName => caches.delete(cacheName))
  );
}

/**
 * 获取缓存状态
 */
async function getCacheStatus() {
  const cacheNames = await caches.keys();
  const status = {};
  
  for (const cacheName of cacheNames) {
    const cache = await caches.open(cacheName);
    const keys = await cache.keys();
    status[cacheName] = keys.length;
  }
  
  return status;
}

console.log('🔧 Racing Game Admin Service Worker loaded');
