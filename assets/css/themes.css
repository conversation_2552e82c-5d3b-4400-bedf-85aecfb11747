/* Racing Game Admin Panel - 主题系统 */

/* ========================================
   基础主题变量定义
   ======================================== */

:root {
  /* 间距系统 */
  --spacing-xs: 0.25rem;    /* 4px */
  --spacing-sm: 0.5rem;     /* 8px */
  --spacing-md: 1rem;       /* 16px */
  --spacing-lg: 1.5rem;     /* 24px */
  --spacing-xl: 2rem;       /* 32px */
  --spacing-2xl: 3rem;      /* 48px */

  /* 圆角系统 */
  --radius-sm: 0.25rem;     /* 4px */
  --radius-md: 0.5rem;      /* 8px */
  --radius-lg: 0.75rem;     /* 12px */
  --radius-xl: 1rem;        /* 16px */
  --radius-full: 9999px;

  /* 阴影系统 */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* 过渡动画 */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;

  /* 字体系统 */
  --font-sans: 'Inter', 'Noto Sans SC', system-ui, sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
}

/* ========================================
   Racing Game 专业主题 (默认)
   ======================================== */

[data-theme="racing-professional"] {
  /* 主色调 - 专业蓝色 */
  --color-primary: #3b82f6;
  --color-primary-focus: #2563eb;
  --color-primary-content: #ffffff;

  /* 辅助色 */
  --color-secondary: #64748b;
  --color-secondary-focus: #475569;
  --color-secondary-content: #ffffff;

  /* 功能色 */
  --color-success: #10b981;
  --color-success-content: #ffffff;
  --color-warning: #f59e0b;
  --color-warning-content: #ffffff;
  --color-error: #ef4444;
  --color-error-content: #ffffff;
  --color-info: #06b6d4;
  --color-info-content: #ffffff;

  /* 中性色 */
  --color-base-100: #ffffff;
  --color-base-200: #f8fafc;
  --color-base-300: #e2e8f0;
  --color-base-content: #1e293b;

  /* 导航和侧边栏 */
  --color-neutral: #1e293b;
  --color-neutral-focus: #0f172a;
  --color-neutral-content: #ffffff;

  /* 特殊效果 */
  --gradient-primary: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  --gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
  --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

/* ========================================
   Racing Game 暗色主题
   ======================================== */

[data-theme="racing-dark"] {
  /* 主色调 */
  --color-primary: #60a5fa;
  --color-primary-focus: #3b82f6;
  --color-primary-content: #1e293b;

  /* 辅助色 */
  --color-secondary: #94a3b8;
  --color-secondary-focus: #64748b;
  --color-secondary-content: #1e293b;

  /* 功能色 */
  --color-success: #34d399;
  --color-success-content: #1e293b;
  --color-warning: #fbbf24;
  --color-warning-content: #1e293b;
  --color-error: #f87171;
  --color-error-content: #1e293b;
  --color-info: #22d3ee;
  --color-info-content: #1e293b;

  /* 中性色 - 暗色背景 */
  --color-base-100: #1e293b;
  --color-base-200: #334155;
  --color-base-300: #475569;
  --color-base-content: #f1f5f9;

  /* 导航和侧边栏 */
  --color-neutral: #0f172a;
  --color-neutral-focus: #020617;
  --color-neutral-content: #f1f5f9;

  /* 特殊效果 */
  --gradient-primary: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
  --gradient-success: linear-gradient(135deg, #34d399 0%, #10b981 100%);
  --gradient-warning: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
}

/* ========================================
   Racing Game 高对比度主题 (无障碍)
   ======================================== */

[data-theme="racing-high-contrast"] {
  /* 高对比度色彩 */
  --color-primary: #0066cc;
  --color-primary-focus: #0052a3;
  --color-primary-content: #ffffff;

  --color-secondary: #666666;
  --color-secondary-focus: #4d4d4d;
  --color-secondary-content: #ffffff;

  --color-success: #008000;
  --color-success-content: #ffffff;
  --color-warning: #ff8c00;
  --color-warning-content: #000000;
  --color-error: #cc0000;
  --color-error-content: #ffffff;
  --color-info: #0080ff;
  --color-info-content: #ffffff;

  /* 高对比度背景 */
  --color-base-100: #ffffff;
  --color-base-200: #f0f0f0;
  --color-base-300: #cccccc;
  --color-base-content: #000000;

  --color-neutral: #000000;
  --color-neutral-focus: #333333;
  --color-neutral-content: #ffffff;

  /* 增强边框 */
  --border-width: 2px;
  --focus-ring: 0 0 0 3px rgba(0, 102, 204, 0.5);
}

/* ========================================
   组件样式增强
   ======================================== */

/* 管理面板布局 */
.admin-panel {
  font-family: var(--font-sans);
  background: var(--color-base-200);
  min-height: 100vh;
}

/* 侧边栏样式 */
.sidebar {
  background: var(--color-neutral);
  color: var(--color-neutral-content);
  transition: all var(--transition-normal);
}

.sidebar-item {
  transition: all var(--transition-fast);
  border-radius: var(--radius-md);
}

.sidebar-item:hover {
  background: var(--color-neutral-focus);
  transform: translateX(4px);
}

.sidebar-item.active {
  background: var(--color-primary);
  color: var(--color-primary-content);
  box-shadow: var(--shadow-md);
}

/* 卡片增强 */
.card-enhanced {
  background: var(--color-base-100);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  border: 1px solid var(--color-base-300);
}

.card-enhanced:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

/* 按钮增强 */
.btn-enhanced {
  transition: all var(--transition-fast);
  border-radius: var(--radius-md);
  font-weight: 500;
}

.btn-enhanced:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-enhanced:active {
  transform: translateY(0);
}

/* 输入框增强 */
.input-enhanced {
  transition: all var(--transition-fast);
  border-radius: var(--radius-md);
}

.input-enhanced:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  border-color: var(--color-primary);
}

/* 表格增强 */
.table-enhanced {
  background: var(--color-base-100);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.table-enhanced thead {
  background: var(--color-base-200);
}

.table-enhanced tbody tr {
  transition: background-color var(--transition-fast);
}

.table-enhanced tbody tr:hover {
  background: var(--color-base-200);
}

/* 统计卡片 */
.stat-card {
  background: var(--color-base-100);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

/* 加载状态 */
.loading-overlay {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
}

[data-theme="racing-dark"] .loading-overlay {
  background: rgba(30, 41, 59, 0.9);
}

/* 动画效果 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.animate-slide-in-up {
  animation: slideInUp 0.3s ease-out;
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.animate-pulse-custom {
  animation: pulse 2s infinite;
}

/* ========================================
   响应式设计
   ======================================== */

/* 移动端适配 */
@media (max-width: 768px) {
  .admin-panel {
    font-size: 14px;
  }

  .card-enhanced {
    border-radius: var(--radius-md);
    margin: var(--spacing-sm);
  }

  .stat-card {
    padding: var(--spacing-md);
  }

  .table-enhanced {
    font-size: 12px;
  }
}

/* 平板端适配 */
@media (min-width: 769px) and (max-width: 1024px) {
  .admin-panel {
    font-size: 15px;
  }
}

/* 大屏幕适配 */
@media (min-width: 1920px) {
  .admin-panel {
    font-size: 16px;
  }

  .card-enhanced {
    border-radius: var(--radius-xl);
  }
}

/* ========================================
   无障碍访问增强
   ======================================== */

/* 高对比度主题的特殊样式 */
[data-theme="racing-high-contrast"] .btn-enhanced:focus {
  outline: var(--border-width) solid var(--color-primary);
  outline-offset: 2px;
}

[data-theme="racing-high-contrast"] .input-enhanced:focus {
  outline: var(--border-width) solid var(--color-primary);
  outline-offset: 2px;
}

/* 减少动画 (用户偏好) */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 强制颜色模式 (用户偏好) */
@media (prefers-contrast: high) {
  :root {
    --color-base-100: #ffffff;
    --color-base-content: #000000;
    --border-width: 2px;
  }
}

/* ========================================
   打印样式
   ======================================== */

@media print {
  .admin-panel {
    background: white !important;
    color: black !important;
  }

  .sidebar,
  .btn-enhanced,
  .loading-overlay {
    display: none !important;
  }

  .card-enhanced {
    box-shadow: none !important;
    border: 1px solid #ccc !important;
  }

  .table-enhanced {
    box-shadow: none !important;
  }
}
