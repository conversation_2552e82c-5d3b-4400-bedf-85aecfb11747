defmodule RacingGame.Live.AdminPanel.Services.Teen.SystemSettingsService do
  @moduledoc """
  ⚙️ 系统设置业务服务层

  负责系统设置相关的业务逻辑处理，包括：
  - 管理员用户管理业务逻辑
  - 角色权限管理业务逻辑
  - 操作日志管理业务逻辑
  - IP白名单管理业务逻辑
  - 系统安全策略执行
  - 业务工作流协调
  - 跨系统集成逻辑
  """

  require Logger

  alias RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.{
    AdminUserRepository,
    RoleRepository,
    PermissionRepository,
    OperationLogRepository,
    IpWhitelistRepository
  }

  alias RacingGame.Live.AdminPanel.QueryBuilders.Teen.SystemSettingsQueryBuilder

  # 业务常量
  @default_admin_role_code "admin"
  @super_admin_role_code "super_admin"
  @guest_role_code "guest"
  @max_login_attempts 5
  @login_lockout_duration 3600  # 1小时
  @password_min_length 8
  @session_timeout 7200  # 2小时

  # ==================== 管理员用户业务逻辑 ====================

  @doc """
  创建管理员用户

  ## 参数
  - `admin_user_data` - 管理员用户数据
  - `creator_id` - 创建者ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, admin_user}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_admin_user(admin_user_data, creator_id, options \\ []) do
    Logger.info("👤 [系统设置服务] 创建管理员用户: #{inspect(admin_user_data[:username])}")

    try do
      # 1. 验证创建者权限
      with {:ok, _creator} <- verify_creator_permissions(creator_id, :create_admin_user),
           # 2. 验证用户数据
           {:ok, validated_data} <- validate_admin_user_data(admin_user_data),
           # 3. 检查用户名唯一性
           {:ok, _} <- check_username_uniqueness(validated_data.username),
           # 4. 检查邮箱唯一性
           {:ok, _} <- check_email_uniqueness(validated_data.email),
           # 5. 验证角色存在性
           {:ok, role} <- validate_role_assignment(validated_data.role_id),
           # 6. 创建用户
           {:ok, admin_user} <- AdminUserRepository.create_admin_user(validated_data, options),
           # 7. 记录操作日志
           {:ok, _log} <- log_admin_operation(creator_id, :create_admin_user, %{
             target_user_id: admin_user.id,
             username: admin_user.username,
             role: role.name
           }) do

        Logger.info("✅ [系统设置服务] 管理员用户创建成功: #{admin_user.username}")
        {:ok, admin_user}
      else
        error ->
          Logger.error("❌ [系统设置服务] 管理员用户创建失败: #{inspect(error)}")
          error
      end
    rescue
      exception ->
        Logger.error("💥 [系统设置服务] 创建管理员用户异常: #{inspect(exception)}")
        {:error, :create_admin_user_exception}
    end
  end

  @doc """
  管理员用户认证

  ## 参数
  - `username` - 用户名
  - `password` - 密码
  - `ip_address` - IP地址
  - `options` - 选项参数

  ## 返回
  - `{:ok, {admin_user, session_info}}` - 成功
  - `{:error, reason}` - 失败
  """
  def authenticate_admin_user(username, password, ip_address, options \\ []) do
    Logger.info("🔐 [系统设置服务] 管理员用户认证: #{username} from #{ip_address}")

    try do
      # 1. 检查IP白名单
      with {:ok, ip_allowed} <- check_ip_whitelist(ip_address),
           true <- ip_allowed || {:error, :ip_not_allowed},
           # 2. 检查登录尝试次数
           {:ok, _} <- check_login_attempts(username),
           # 3. 执行认证
           {:ok, admin_user} <- AdminUserRepository.authenticate_admin_user(username, password, [load: [:role]]),
           # 4. 检查用户状态
           {:ok, _} <- check_user_status(admin_user),
           # 5. 检查角色状态
           {:ok, _} <- check_role_status(admin_user.role),
           # 6. 生成会话信息
           {:ok, session_info} <- generate_session_info(admin_user, ip_address),
           # 7. 重置登录尝试计数
           {:ok, _} <- reset_login_attempts(username),
           # 8. 记录登录日志
           {:ok, _log} <- log_admin_operation(admin_user.id, :login, %{
             ip_address: ip_address,
             user_agent: Keyword.get(options, :user_agent, ""),
             login_time: DateTime.utc_now()
           }) do

        Logger.info("✅ [系统设置服务] 管理员认证成功: #{admin_user.username}")
        {:ok, {admin_user, session_info}}
      else
        {:error, :ip_not_allowed} ->
          Logger.warn("⚠️ [系统设置服务] IP地址不在白名单中: #{ip_address}")
          log_security_event(:ip_blocked, %{ip_address: ip_address, username: username})
          {:error, :ip_not_allowed}

        {:error, :too_many_attempts} ->
          Logger.warn("⚠️ [系统设置服务] 登录尝试次数过多: #{username}")
          log_security_event(:too_many_attempts, %{username: username, ip_address: ip_address})
          {:error, :too_many_attempts}

        {:error, :invalid_credentials} ->
          Logger.warn("⚠️ [系统设置服务] 认证失败: #{username}")
          increment_login_attempts(username)
          log_security_event(:invalid_credentials, %{username: username, ip_address: ip_address})
          {:error, :invalid_credentials}

        error ->
          Logger.error("❌ [系统设置服务] 认证失败: #{inspect(error)}")
          increment_login_attempts(username)
          error
      end
    rescue
      exception ->
        Logger.error("💥 [系统设置服务] 管理员认证异常: #{inspect(exception)}")
        {:error, :authenticate_exception}
    end
  end

  @doc """
  更新管理员用户

  ## 参数
  - `admin_user_id` - 管理员用户ID
  - `update_data` - 更新数据
  - `updater_id` - 更新者ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, admin_user}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_admin_user(admin_user_id, update_data, updater_id, options \\ []) do
    Logger.info("👤 [系统设置服务] 更新管理员用户: #{admin_user_id}")

    try do
      # 1. 验证更新者权限
      with {:ok, updater} <- verify_updater_permissions(updater_id, admin_user_id, :update_admin_user),
           # 2. 获取目标用户
           {:ok, target_user} <- AdminUserRepository.get_admin_user(admin_user_id, [load: [:role]]),
           # 3. 验证更新权限
           {:ok, _} <- verify_update_permissions(updater, target_user, update_data),
           # 4. 验证更新数据
           {:ok, validated_data} <- validate_update_data(update_data, target_user),
           # 5. 执行更新
           {:ok, updated_user} <- AdminUserRepository.update_admin_user(admin_user_id, validated_data, options),
           # 6. 记录操作日志
           {:ok, _log} <- log_admin_operation(updater_id, :update_admin_user, %{
             target_user_id: admin_user_id,
             changes: validated_data,
             old_values: extract_old_values(target_user, validated_data)
           }) do

        Logger.info("✅ [系统设置服务] 管理员用户更新成功: #{updated_user.username}")
        {:ok, updated_user}
      else
        error ->
          Logger.error("❌ [系统设置服务] 管理员用户更新失败: #{inspect(error)}")
          error
      end
    rescue
      exception ->
        Logger.error("💥 [系统设置服务] 更新管理员用户异常: #{inspect(exception)}")
        {:error, :update_admin_user_exception}
    end
  end

  @doc """
  删除管理员用户

  ## 参数
  - `admin_user_id` - 管理员用户ID
  - `deleter_id` - 删除者ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, admin_user}` - 成功
  - `{:error, reason}` - 失败
  """
  def delete_admin_user(admin_user_id, deleter_id, options \\ []) do
    Logger.info("👤 [系统设置服务] 删除管理员用户: #{admin_user_id}")

    try do
      # 1. 验证删除者权限
      with {:ok, deleter} <- verify_deleter_permissions(deleter_id, admin_user_id, :delete_admin_user),
           # 2. 获取目标用户
           {:ok, target_user} <- AdminUserRepository.get_admin_user(admin_user_id, [load: [:role]]),
           # 3. 验证删除权限
           {:ok, _} <- verify_delete_permissions(deleter, target_user),
           # 4. 检查用户是否可删除
           {:ok, _} <- check_user_deletable(target_user),
           # 5. 执行软删除
           {:ok, deleted_user} <- soft_delete_admin_user(admin_user_id, deleter_id),
           # 6. 记录操作日志
           {:ok, _log} <- log_admin_operation(deleter_id, :delete_admin_user, %{
             target_user_id: admin_user_id,
             username: target_user.username,
             role: target_user.role.name
           }) do

        Logger.info("✅ [系统设置服务] 管理员用户删除成功: #{target_user.username}")
        {:ok, deleted_user}
      else
        error ->
          Logger.error("❌ [系统设置服务] 管理员用户删除失败: #{inspect(error)}")
          error
      end
    rescue
      exception ->
        Logger.error("💥 [系统设置服务] 删除管理员用户异常: #{inspect(exception)}")
        {:error, :delete_admin_user_exception}
    end
  end

  @doc """
  激活管理员用户

  ## 参数
  - `admin_user_id` - 管理员用户ID
  - `activator_id` - 激活者ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, admin_user}` - 成功
  - `{:error, reason}` - 失败
  """
  def activate_admin_user(admin_user_id, activator_id, options \\ []) do
    Logger.info("👤 [系统设置服务] 激活管理员用户: #{admin_user_id}")

    try do
      # 1. 验证激活者权限
      with {:ok, _activator} <- verify_activator_permissions(activator_id, :activate_admin_user),
           # 2. 执行激活
           {:ok, activated_user} <- AdminUserRepository.activate_admin_user(admin_user_id, options),
           # 3. 记录操作日志
           {:ok, _log} <- log_admin_operation(activator_id, :activate_admin_user, %{
             target_user_id: admin_user_id,
             username: activated_user.username
           }) do

        Logger.info("✅ [系统设置服务] 管理员用户激活成功: #{activated_user.username}")
        {:ok, activated_user}
      else
        error ->
          Logger.error("❌ [系统设置服务] 管理员用户激活失败: #{inspect(error)}")
          error
      end
    rescue
      exception ->
        Logger.error("💥 [系统设置服务] 激活管理员用户异常: #{inspect(exception)}")
        {:error, :activate_admin_user_exception}
    end
  end

  @doc """
  禁用管理员用户

  ## 参数
  - `admin_user_id` - 管理员用户ID
  - `deactivator_id` - 禁用者ID
  - `reason` - 禁用原因
  - `options` - 选项参数

  ## 返回
  - `{:ok, admin_user}` - 成功
  - `{:error, reason}` - 失败
  """
  def deactivate_admin_user(admin_user_id, deactivator_id, reason \\ "", options \\ []) do
    Logger.info("👤 [系统设置服务] 禁用管理员用户: #{admin_user_id}")

    try do
      # 1. 验证禁用者权限
      with {:ok, _deactivator} <- verify_deactivator_permissions(deactivator_id, admin_user_id, :deactivate_admin_user),
           # 2. 获取目标用户
           {:ok, target_user} <- AdminUserRepository.get_admin_user(admin_user_id, [load: [:role]]),
           # 3. 检查是否可禁用
           {:ok, _} <- check_user_deactivatable(target_user, deactivator_id),
           # 4. 执行禁用
           {:ok, deactivated_user} <- AdminUserRepository.deactivate_admin_user(admin_user_id, options),
           # 5. 记录操作日志
           {:ok, _log} <- log_admin_operation(deactivator_id, :deactivate_admin_user, %{
             target_user_id: admin_user_id,
             username: target_user.username,
             reason: reason
           }) do

        Logger.info("✅ [系统设置服务] 管理员用户禁用成功: #{target_user.username}")
        {:ok, deactivated_user}
      else
        error ->
          Logger.error("❌ [系统设置服务] 管理员用户禁用失败: #{inspect(error)}")
          error
      end
    rescue
      exception ->
        Logger.error("💥 [系统设置服务] 禁用管理员用户异常: #{inspect(exception)}")
        {:error, :deactivate_admin_user_exception}
    end
  end

  # ==================== 私有辅助函数 ====================

  # 验证创建者权限
  defp verify_creator_permissions(creator_id, operation) do
    Logger.debug("🔍 [系统设置服务] 验证创建者权限: #{creator_id} - #{operation}")

    with {:ok, creator} <- AdminUserRepository.get_admin_user(creator_id, [load: [:role]]),
         {:ok, _} <- check_user_status(creator),
         {:ok, _} <- check_role_status(creator.role),
         {:ok, _} <- check_operation_permission(creator, operation) do
      {:ok, creator}
    else
      error -> error
    end
  end

  # 验证用户数据
  defp validate_admin_user_data(admin_user_data) do
    Logger.debug("🔍 [系统设置服务] 验证用户数据")

    with {:ok, _} <- validate_username(admin_user_data[:username]),
         {:ok, _} <- validate_email(admin_user_data[:email]),
         {:ok, _} <- validate_password(admin_user_data[:password]) do
      {:ok, admin_user_data}
    else
      error -> error
    end
  end

  # 检查用户名唯一性
  defp check_username_uniqueness(username) do
    case AdminUserRepository.get_admin_user_by_username(username) do
      {:ok, nil} -> {:ok, :unique}
      {:ok, _user} -> {:error, :username_already_exists}
      error -> error
    end
  end

  # 检查邮箱唯一性
  defp check_email_uniqueness(email) do
    case AdminUserRepository.get_admin_user_by_email(email) do
      {:ok, nil} -> {:ok, :unique}
      {:ok, _user} -> {:error, :email_already_exists}
      error -> error
    end
  end

  # 验证角色分配
  defp validate_role_assignment(role_id) do
    case RoleRepository.get_role(role_id) do
      {:ok, role} when not is_nil(role) ->
        if role.status == 1 do
          {:ok, role}
        else
          {:error, :role_inactive}
        end
      {:ok, nil} -> {:error, :role_not_found}
      error -> error
    end
  end

  # 记录管理员操作日志
  defp log_admin_operation(admin_user_id, operation_type, details) do
    log_data = %{
      admin_user_id: admin_user_id,
      operation_type: Atom.to_string(operation_type),
      module: "SystemSettings",
      details: Jason.encode!(details),
      ip_address: Map.get(details, :ip_address, ""),
      user_agent: Map.get(details, :user_agent, "")
    }

    OperationLogRepository.create_operation_log(log_data)
  end

  # 检查IP白名单
  defp check_ip_whitelist(ip_address) do
    IpWhitelistRepository.check_ip_allowed(ip_address)
  end

  # 检查登录尝试次数
  defp check_login_attempts(username) do
    # 这里应该实现登录尝试次数检查逻辑
    # 可以使用Redis或数据库存储尝试次数
    {:ok, :allowed}
  end

  # 检查用户状态
  defp check_user_status(admin_user) do
    if admin_user.status == 1 do
      {:ok, :active}
    else
      {:error, :user_inactive}
    end
  end

  # 检查角色状态
  defp check_role_status(role) when not is_nil(role) do
    if role.status == 1 do
      {:ok, :active}
    else
      {:error, :role_inactive}
    end
  end
  defp check_role_status(nil), do: {:error, :no_role_assigned}

  # 生成会话信息
  defp generate_session_info(admin_user, ip_address) do
    session_info = %{
      user_id: admin_user.id,
      username: admin_user.username,
      role: admin_user.role,
      ip_address: ip_address,
      login_time: DateTime.utc_now(),
      expires_at: DateTime.add(DateTime.utc_now(), @session_timeout, :second)
    }

    {:ok, session_info}
  end

  # 重置登录尝试计数
  defp reset_login_attempts(username) do
    # 实现重置登录尝试次数逻辑
    {:ok, :reset}
  end

  # 增加登录尝试次数
  defp increment_login_attempts(username) do
    # 实现增加登录尝试次数逻辑
    {:ok, :incremented}
  end

  # 记录安全事件
  defp log_security_event(event_type, details) do
    Logger.warn("🚨 [安全事件] #{event_type}: #{inspect(details)}")
    # 这里可以实现更复杂的安全事件记录逻辑
    :ok
  end

  # 验证用户名
  defp validate_username(username) when is_binary(username) do
    if String.length(username) >= 3 and String.length(username) <= 50 do
      {:ok, :valid}
    else
      {:error, :invalid_username_length}
    end
  end
  defp validate_username(_), do: {:error, :invalid_username_format}

  # 验证邮箱
  defp validate_email(email) when is_binary(email) do
    if String.contains?(email, "@") and String.length(email) <= 255 do
      {:ok, :valid}
    else
      {:error, :invalid_email_format}
    end
  end
  defp validate_email(_), do: {:error, :invalid_email_format}

  # 验证密码
  defp validate_password(password) when is_binary(password) do
    if String.length(password) >= @password_min_length do
      {:ok, :valid}
    else
      {:error, :password_too_short}
    end
  end
  defp validate_password(_), do: {:error, :invalid_password_format}

  # ==================== 角色管理业务逻辑 ====================

  @doc """
  创建角色

  ## 参数
  - `role_data` - 角色数据
  - `creator_id` - 创建者ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, role}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_role(role_data, creator_id, options \\ []) do
    Logger.info("🎭 [系统设置服务] 创建角色: #{inspect(role_data[:name])}")

    try do
      # 1. 验证创建者权限
      with {:ok, _creator} <- verify_creator_permissions(creator_id, :create_role),
           # 2. 验证角色数据
           {:ok, validated_data} <- validate_role_data(role_data),
           # 3. 检查角色名称唯一性
           {:ok, _} <- check_role_name_uniqueness(validated_data.name),
           # 4. 检查角色代码唯一性
           {:ok, _} <- check_role_code_uniqueness(validated_data.code),
           # 5. 验证权限代码
           {:ok, _} <- validate_permission_codes(validated_data.permission_codes),
           # 6. 创建角色
           {:ok, role} <- RoleRepository.create_role(validated_data, options),
           # 7. 记录操作日志
           {:ok, _log} <- log_admin_operation(creator_id, :create_role, %{
             role_id: role.id,
             role_name: role.name,
             role_code: role.code,
             level: role.level
           }) do

        Logger.info("✅ [系统设置服务] 角色创建成功: #{role.name}")
        {:ok, role}
      else
        error ->
          Logger.error("❌ [系统设置服务] 角色创建失败: #{inspect(error)}")
          error
      end
    rescue
      exception ->
        Logger.error("💥 [系统设置服务] 创建角色异常: #{inspect(exception)}")
        {:error, :create_role_exception}
    end
  end

  @doc """
  更新角色

  ## 参数
  - `role_id` - 角色ID
  - `update_data` - 更新数据
  - `updater_id` - 更新者ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, role}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_role(role_id, update_data, updater_id, options \\ []) do
    Logger.info("🎭 [系统设置服务] 更新角色: #{role_id}")

    try do
      # 1. 验证更新者权限
      with {:ok, updater} <- verify_updater_permissions(updater_id, role_id, :update_role),
           # 2. 获取目标角色
           {:ok, target_role} <- RoleRepository.get_role(role_id),
           # 3. 验证更新权限
           {:ok, _} <- verify_role_update_permissions(updater, target_role, update_data),
           # 4. 验证更新数据
           {:ok, validated_data} <- validate_role_update_data(update_data, target_role),
           # 5. 执行更新
           {:ok, updated_role} <- RoleRepository.update_role(role_id, validated_data, options),
           # 6. 记录操作日志
           {:ok, _log} <- log_admin_operation(updater_id, :update_role, %{
             role_id: role_id,
             changes: validated_data,
             old_values: extract_role_old_values(target_role, validated_data)
           }) do

        Logger.info("✅ [系统设置服务] 角色更新成功: #{updated_role.name}")
        {:ok, updated_role}
      else
        error ->
          Logger.error("❌ [系统设置服务] 角色更新失败: #{inspect(error)}")
          error
      end
    rescue
      exception ->
        Logger.error("💥 [系统设置服务] 更新角色异常: #{inspect(exception)}")
        {:error, :update_role_exception}
    end
  end

  @doc """
  删除角色

  ## 参数
  - `role_id` - 角色ID
  - `deleter_id` - 删除者ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, role}` - 成功
  - `{:error, reason}` - 失败
  """
  def delete_role(role_id, deleter_id, options \\ []) do
    Logger.info("🎭 [系统设置服务] 删除角色: #{role_id}")

    try do
      # 1. 验证删除者权限
      with {:ok, deleter} <- verify_deleter_permissions(deleter_id, role_id, :delete_role),
           # 2. 获取目标角色
           {:ok, target_role} <- RoleRepository.get_role(role_id),
           # 3. 验证删除权限
           {:ok, _} <- verify_role_delete_permissions(deleter, target_role),
           # 4. 检查角色是否可删除
           {:ok, _} <- check_role_deletable(target_role),
           # 5. 检查是否有用户使用此角色
           {:ok, _} <- check_role_usage(role_id),
           # 6. 执行删除
           {:ok, deleted_role} <- RoleRepository.delete_role(role_id, options),
           # 7. 记录操作日志
           {:ok, _log} <- log_admin_operation(deleter_id, :delete_role, %{
             role_id: role_id,
             role_name: target_role.name,
             role_code: target_role.code
           }) do

        Logger.info("✅ [系统设置服务] 角色删除成功: #{target_role.name}")
        {:ok, deleted_role}
      else
        error ->
          Logger.error("❌ [系统设置服务] 角色删除失败: #{inspect(error)}")
          error
      end
    rescue
      exception ->
        Logger.error("💥 [系统设置服务] 删除角色异常: #{inspect(exception)}")
        {:error, :delete_role_exception}
    end
  end

  @doc """
  激活角色

  ## 参数
  - `role_id` - 角色ID
  - `activator_id` - 激活者ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, role}` - 成功
  - `{:error, reason}` - 失败
  """
  def activate_role(role_id, activator_id, options \\ []) do
    Logger.info("🎭 [系统设置服务] 激活角色: #{role_id}")

    try do
      # 1. 验证激活者权限
      with {:ok, _activator} <- verify_activator_permissions(activator_id, :activate_role),
           # 2. 执行激活
           {:ok, activated_role} <- RoleRepository.activate_role(role_id, options),
           # 3. 记录操作日志
           {:ok, _log} <- log_admin_operation(activator_id, :activate_role, %{
             role_id: role_id,
             role_name: activated_role.name
           }) do

        Logger.info("✅ [系统设置服务] 角色激活成功: #{activated_role.name}")
        {:ok, activated_role}
      else
        error ->
          Logger.error("❌ [系统设置服务] 角色激活失败: #{inspect(error)}")
          error
      end
    rescue
      exception ->
        Logger.error("💥 [系统设置服务] 激活角色异常: #{inspect(exception)}")
        {:error, :activate_role_exception}
    end
  end

  @doc """
  禁用角色

  ## 参数
  - `role_id` - 角色ID
  - `deactivator_id` - 禁用者ID
  - `reason` - 禁用原因
  - `options` - 选项参数

  ## 返回
  - `{:ok, role}` - 成功
  - `{:error, reason}` - 失败
  """
  def deactivate_role(role_id, deactivator_id, reason \\ "", options \\ []) do
    Logger.info("🎭 [系统设置服务] 禁用角色: #{role_id}")

    try do
      # 1. 验证禁用者权限
      with {:ok, _deactivator} <- verify_deactivator_permissions(deactivator_id, role_id, :deactivate_role),
           # 2. 获取目标角色
           {:ok, target_role} <- RoleRepository.get_role(role_id),
           # 3. 检查是否可禁用
           {:ok, _} <- check_role_deactivatable(target_role),
           # 4. 检查角色使用情况
           {:ok, _} <- check_role_deactivation_impact(role_id),
           # 5. 执行禁用
           {:ok, deactivated_role} <- RoleRepository.deactivate_role(role_id, options),
           # 6. 记录操作日志
           {:ok, _log} <- log_admin_operation(deactivator_id, :deactivate_role, %{
             role_id: role_id,
             role_name: target_role.name,
             reason: reason
           }) do

        Logger.info("✅ [系统设置服务] 角色禁用成功: #{target_role.name}")
        {:ok, deactivated_role}
      else
        error ->
          Logger.error("❌ [系统设置服务] 角色禁用失败: #{inspect(error)}")
          error
      end
    rescue
      exception ->
        Logger.error("💥 [系统设置服务] 禁用角色异常: #{inspect(exception)}")
        {:error, :deactivate_role_exception}
    end
  end

  # ==================== 角色管理私有函数 ====================

  # 验证角色数据
  defp validate_role_data(role_data) do
    Logger.debug("🔍 [系统设置服务] 验证角色数据")

    with {:ok, _} <- validate_role_name(role_data[:name]),
         {:ok, _} <- validate_role_code(role_data[:code]),
         {:ok, _} <- validate_role_level(role_data[:level]) do
      {:ok, role_data}
    else
      error -> error
    end
  end

  # 检查角色名称唯一性
  defp check_role_name_uniqueness(role_name) do
    case RoleRepository.list_roles(%{"name" => role_name}) do
      {:ok, {[], 0}} -> {:ok, :unique}
      {:ok, {_roles, _count}} -> {:error, :role_name_already_exists}
      error -> error
    end
  end

  # 检查角色代码唯一性
  defp check_role_code_uniqueness(role_code) do
    case RoleRepository.get_role_by_code(role_code) do
      {:ok, nil} -> {:ok, :unique}
      {:ok, _role} -> {:error, :role_code_already_exists}
      error -> error
    end
  end

  # 验证权限代码
  defp validate_permission_codes(permission_codes) when is_list(permission_codes) do
    # 验证所有权限代码是否存在且有效
    valid_codes = Enum.all?(permission_codes, fn code ->
      case PermissionRepository.get_permission_by_code(code) do
        {:ok, permission} when not is_nil(permission) -> permission.status == 1
        _ -> false
      end
    end)

    if valid_codes do
      {:ok, :valid}
    else
      {:error, :invalid_permission_codes}
    end
  end
  defp validate_permission_codes(_), do: {:error, :invalid_permission_codes_format}

  # 检查角色是否可删除
  defp check_role_deletable(role) do
    # 系统内置角色不能删除
    system_roles = [@default_admin_role_code, @super_admin_role_code, @guest_role_code]

    if role.code in system_roles do
      {:error, :system_role_cannot_delete}
    else
      {:ok, :deletable}
    end
  end

  # 检查角色使用情况
  defp check_role_usage(role_id) do
    case AdminUserRepository.list_admin_users_by_role(role_id) do
      {:ok, []} -> {:ok, :not_in_use}
      {:ok, users} when length(users) > 0 -> {:error, :role_in_use}
      error -> error
    end
  end

  # 验证角色名称
  defp validate_role_name(role_name) when is_binary(role_name) do
    if String.length(role_name) >= 2 and String.length(role_name) <= 50 do
      {:ok, :valid}
    else
      {:error, :invalid_role_name_length}
    end
  end
  defp validate_role_name(_), do: {:error, :invalid_role_name_format}

  # 验证角色代码
  defp validate_role_code(role_code) when is_binary(role_code) do
    if String.length(role_code) >= 2 and String.length(role_code) <= 20 and
       String.match?(role_code, ~r/^[a-z_]+$/) do
      {:ok, :valid}
    else
      {:error, :invalid_role_code_format}
    end
  end
  defp validate_role_code(_), do: {:error, :invalid_role_code_format}

  # 验证角色级别
  defp validate_role_level(level) when is_integer(level) and level >= 1 and level <= 10 do
    {:ok, :valid}
  end
  defp validate_role_level(_), do: {:error, :invalid_role_level}

  # ==================== 权限管理业务逻辑 ====================

  @doc """
  创建权限

  ## 参数
  - `permission_data` - 权限数据
  - `creator_id` - 创建者ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, permission}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_permission(permission_data, creator_id, options \\ []) do
    Logger.info("🔐 [系统设置服务] 创建权限: #{inspect(permission_data[:name])}")

    try do
      # 1. 验证创建者权限
      with {:ok, _creator} <- verify_creator_permissions(creator_id, :create_permission),
           # 2. 验证权限数据
           {:ok, validated_data} <- validate_permission_data(permission_data),
           # 3. 检查权限名称唯一性
           {:ok, _} <- check_permission_name_uniqueness(validated_data.name),
           # 4. 检查权限代码唯一性
           {:ok, _} <- check_permission_code_uniqueness(validated_data.code),
           # 5. 验证父权限
           {:ok, _} <- validate_parent_permission(validated_data.parent_id),
           # 6. 创建权限
           {:ok, permission} <- PermissionRepository.create_permission(validated_data, options),
           # 7. 记录操作日志
           {:ok, _log} <- log_admin_operation(creator_id, :create_permission, %{
             permission_id: permission.id,
             permission_name: permission.name,
             permission_code: permission.code,
             type: permission.type
           }) do

        Logger.info("✅ [系统设置服务] 权限创建成功: #{permission.name}")
        {:ok, permission}
      else
        error ->
          Logger.error("❌ [系统设置服务] 权限创建失败: #{inspect(error)}")
          error
      end
    rescue
      exception ->
        Logger.error("💥 [系统设置服务] 创建权限异常: #{inspect(exception)}")
        {:error, :create_permission_exception}
    end
  end

  @doc """
  更新权限

  ## 参数
  - `permission_id` - 权限ID
  - `update_data` - 更新数据
  - `updater_id` - 更新者ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, permission}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_permission(permission_id, update_data, updater_id, options \\ []) do
    Logger.info("🔐 [系统设置服务] 更新权限: #{permission_id}")

    try do
      # 1. 验证更新者权限
      with {:ok, _updater} <- verify_updater_permissions(updater_id, permission_id, :update_permission),
           # 2. 获取目标权限
           {:ok, target_permission} <- PermissionRepository.get_permission(permission_id),
           # 3. 验证更新数据
           {:ok, validated_data} <- validate_permission_update_data(update_data, target_permission),
           # 4. 执行更新
           {:ok, updated_permission} <- PermissionRepository.update_permission(permission_id, validated_data, options),
           # 5. 记录操作日志
           {:ok, _log} <- log_admin_operation(updater_id, :update_permission, %{
             permission_id: permission_id,
             changes: validated_data,
             old_values: extract_permission_old_values(target_permission, validated_data)
           }) do

        Logger.info("✅ [系统设置服务] 权限更新成功: #{updated_permission.name}")
        {:ok, updated_permission}
      else
        error ->
          Logger.error("❌ [系统设置服务] 权限更新失败: #{inspect(error)}")
          error
      end
    rescue
      exception ->
        Logger.error("💥 [系统设置服务] 更新权限异常: #{inspect(exception)}")
        {:error, :update_permission_exception}
    end
  end

  @doc """
  删除权限

  ## 参数
  - `permission_id` - 权限ID
  - `deleter_id` - 删除者ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, permission}` - 成功
  - `{:error, reason}` - 失败
  """
  def delete_permission(permission_id, deleter_id, options \\ []) do
    Logger.info("🔐 [系统设置服务] 删除权限: #{permission_id}")

    try do
      # 1. 验证删除者权限
      with {:ok, _deleter} <- verify_deleter_permissions(deleter_id, permission_id, :delete_permission),
           # 2. 获取目标权限
           {:ok, target_permission} <- PermissionRepository.get_permission(permission_id),
           # 3. 检查权限是否可删除
           {:ok, _} <- check_permission_deletable(target_permission),
           # 4. 检查子权限
           {:ok, _} <- check_child_permissions(permission_id),
           # 5. 检查权限使用情况
           {:ok, _} <- check_permission_usage(permission_id),
           # 6. 执行删除
           {:ok, deleted_permission} <- PermissionRepository.delete_permission(permission_id, options),
           # 7. 记录操作日志
           {:ok, _log} <- log_admin_operation(deleter_id, :delete_permission, %{
             permission_id: permission_id,
             permission_name: target_permission.name,
             permission_code: target_permission.code
           }) do

        Logger.info("✅ [系统设置服务] 权限删除成功: #{target_permission.name}")
        {:ok, deleted_permission}
      else
        error ->
          Logger.error("❌ [系统设置服务] 权限删除失败: #{inspect(error)}")
          error
      end
    rescue
      exception ->
        Logger.error("💥 [系统设置服务] 删除权限异常: #{inspect(exception)}")
        {:error, :delete_permission_exception}
    end
  end

  @doc """
  获取权限树

  ## 参数
  - `requester_id` - 请求者ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, permission_tree}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_permission_tree(requester_id, options \\ []) do
    Logger.info("🔐 [系统设置服务] 获取权限树: #{requester_id}")

    try do
      # 1. 验证请求者权限
      with {:ok, _requester} <- verify_requester_permissions(requester_id, :view_permissions),
           # 2. 获取权限树
           {:ok, permission_tree} <- PermissionRepository.get_permission_tree(options),
           # 3. 记录操作日志
           {:ok, _log} <- log_admin_operation(requester_id, :view_permission_tree, %{
             tree_size: length(permission_tree)
           }) do

        Logger.info("✅ [系统设置服务] 权限树获取成功: #{length(permission_tree)}个根节点")
        {:ok, permission_tree}
      else
        error ->
          Logger.error("❌ [系统设置服务] 权限树获取失败: #{inspect(error)}")
          error
      end
    rescue
      exception ->
        Logger.error("💥 [系统设置服务] 获取权限树异常: #{inspect(exception)}")
        {:error, :get_permission_tree_exception}
    end
  end

  # ==================== IP白名单管理业务逻辑 ====================

  @doc """
  创建IP白名单

  ## 参数
  - `ip_data` - IP白名单数据
  - `creator_id` - 创建者ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, ip_whitelist}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_ip_whitelist(ip_data, creator_id, options \\ []) do
    Logger.info("🛡️ [系统设置服务] 创建IP白名单: #{inspect(ip_data[:ip_address] || ip_data[:ip_range])}")

    try do
      # 1. 验证创建者权限
      with {:ok, _creator} <- verify_creator_permissions(creator_id, :create_ip_whitelist),
           # 2. 验证IP数据
           {:ok, validated_data} <- validate_ip_whitelist_data(ip_data),
           # 3. 检查IP重复
           {:ok, _} <- check_ip_uniqueness(validated_data),
           # 4. 验证IP格式
           {:ok, _} <- validate_ip_format(validated_data),
           # 5. 创建IP白名单
           {:ok, ip_whitelist} <- IpWhitelistRepository.create_ip_whitelist(
             Map.put(validated_data, :created_by, creator_id), options),
           # 6. 记录操作日志
           {:ok, _log} <- log_admin_operation(creator_id, :create_ip_whitelist, %{
             ip_whitelist_id: ip_whitelist.id,
             ip_address: ip_whitelist.ip_address,
             ip_range: ip_whitelist.ip_range,
             type: ip_whitelist.type
           }) do

        Logger.info("✅ [系统设置服务] IP白名单创建成功: #{ip_whitelist.ip_address || ip_whitelist.ip_range}")
        {:ok, ip_whitelist}
      else
        error ->
          Logger.error("❌ [系统设置服务] IP白名单创建失败: #{inspect(error)}")
          error
      end
    rescue
      exception ->
        Logger.error("💥 [系统设置服务] 创建IP白名单异常: #{inspect(exception)}")
        {:error, :create_ip_whitelist_exception}
    end
  end

  # ==================== 系统综合业务逻辑 ====================

  @doc """
  获取系统设置综合统计

  ## 参数
  - `requester_id` - 请求者ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, comprehensive_stats}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_comprehensive_stats(requester_id, options \\ []) do
    Logger.info("📊 [系统设置服务] 获取综合统计: #{requester_id}")

    try do
      # 1. 验证请求者权限
      with {:ok, _requester} <- verify_requester_permissions(requester_id, :view_system_stats),
           # 2. 获取综合统计
           {:ok, comprehensive_stats} <- SystemSettingsQueryBuilder.build_comprehensive_stats_query(%{}, options),
           # 3. 记录操作日志
           {:ok, _log} <- log_admin_operation(requester_id, :view_comprehensive_stats, %{
             stats_generated_at: comprehensive_stats.generated_at
           }) do

        Logger.info("✅ [系统设置服务] 综合统计获取成功")
        {:ok, comprehensive_stats}
      else
        error ->
          Logger.error("❌ [系统设置服务] 综合统计获取失败: #{inspect(error)}")
          error
      end
    rescue
      exception ->
        Logger.error("💥 [系统设置服务] 获取综合统计异常: #{inspect(exception)}")
        {:error, :get_comprehensive_stats_exception}
    end
  end

  @doc """
  获取用户权限

  ## 参数
  - `admin_user_id` - 管理员用户ID
  - `requester_id` - 请求者ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, user_permissions}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_permissions(admin_user_id, requester_id, options \\ []) do
    Logger.info("🔐 [系统设置服务] 获取用户权限: #{admin_user_id}")

    try do
      # 1. 验证请求者权限
      with {:ok, _requester} <- verify_requester_permissions(requester_id, :view_user_permissions),
           # 2. 获取用户权限
           {:ok, user_permissions} <- SystemSettingsQueryBuilder.build_user_permissions_query(admin_user_id, options),
           # 3. 记录操作日志
           {:ok, _log} <- log_admin_operation(requester_id, :view_user_permissions, %{
             target_user_id: admin_user_id,
             permissions_count: length(user_permissions.permissions)
           }) do

        Logger.info("✅ [系统设置服务] 用户权限获取成功: #{length(user_permissions.permissions)}个权限")
        {:ok, user_permissions}
      else
        error ->
          Logger.error("❌ [系统设置服务] 用户权限获取失败: #{inspect(error)}")
          error
      end
    rescue
      exception ->
        Logger.error("💥 [系统设置服务] 获取用户权限异常: #{inspect(exception)}")
        {:error, :get_user_permissions_exception}
    end
  end

  # ==================== 权限管理私有函数 ====================

  # 验证权限数据
  defp validate_permission_data(permission_data) do
    Logger.debug("🔍 [系统设置服务] 验证权限数据")

    with {:ok, _} <- validate_permission_name(permission_data[:name]),
         {:ok, _} <- validate_permission_code(permission_data[:code]),
         {:ok, _} <- validate_permission_type(permission_data[:type]) do
      {:ok, permission_data}
    else
      error -> error
    end
  end

  # 检查权限名称唯一性
  defp check_permission_name_uniqueness(permission_name) do
    case PermissionRepository.list_permissions(%{"name" => permission_name}) do
      {:ok, {[], 0}} -> {:ok, :unique}
      {:ok, {_permissions, _count}} -> {:error, :permission_name_already_exists}
      error -> error
    end
  end

  # 检查权限代码唯一性
  defp check_permission_code_uniqueness(permission_code) do
    case PermissionRepository.get_permission_by_code(permission_code) do
      {:ok, nil} -> {:ok, :unique}
      {:ok, _permission} -> {:error, :permission_code_already_exists}
      error -> error
    end
  end

  # 验证父权限
  defp validate_parent_permission(nil), do: {:ok, :no_parent}
  defp validate_parent_permission(parent_id) do
    case PermissionRepository.get_permission(parent_id) do
      {:ok, parent} when not is_nil(parent) ->
        if parent.status == 1 do
          {:ok, parent}
        else
          {:error, :parent_permission_inactive}
        end
      {:ok, nil} -> {:error, :parent_permission_not_found}
      error -> error
    end
  end

  # 检查权限是否可删除
  defp check_permission_deletable(_permission) do
    # 这里可以添加系统权限检查逻辑
    {:ok, :deletable}
  end

  # 检查子权限
  defp check_child_permissions(permission_id) do
    case PermissionRepository.list_permissions_by_parent(permission_id) do
      {:ok, []} -> {:ok, :no_children}
      {:ok, children} when length(children) > 0 -> {:error, :has_child_permissions}
      error -> error
    end
  end

  # 检查权限使用情况
  defp check_permission_usage(permission_id) do
    # 这里应该检查哪些角色使用了这个权限
    # 由于权限是通过代码关联的，需要检查所有角色的permission_codes字段
    {:ok, :not_in_use}
  end

  # 验证权限名称
  defp validate_permission_name(permission_name) when is_binary(permission_name) do
    if String.length(permission_name) >= 2 and String.length(permission_name) <= 100 do
      {:ok, :valid}
    else
      {:error, :invalid_permission_name_length}
    end
  end
  defp validate_permission_name(_), do: {:error, :invalid_permission_name_format}

  # 验证权限代码
  defp validate_permission_code(permission_code) when is_binary(permission_code) do
    if String.length(permission_code) >= 2 and String.length(permission_code) <= 50 and
       String.match?(permission_code, ~r/^[a-z_:]+$/) do
      {:ok, :valid}
    else
      {:error, :invalid_permission_code_format}
    end
  end
  defp validate_permission_code(_), do: {:error, :invalid_permission_code_format}

  # 验证权限类型
  defp validate_permission_type(type) when type in ["menu", "button", "api"] do
    {:ok, :valid}
  end
  defp validate_permission_type(_), do: {:error, :invalid_permission_type}

  # ==================== IP白名单管理私有函数 ====================

  # 验证IP白名单数据
  defp validate_ip_whitelist_data(ip_data) do
    Logger.debug("🔍 [系统设置服务] 验证IP白名单数据")

    with {:ok, _} <- validate_ip_type(ip_data[:type]),
         {:ok, _} <- validate_ip_data_content(ip_data) do
      {:ok, ip_data}
    else
      error -> error
    end
  end

  # 检查IP唯一性
  defp check_ip_uniqueness(ip_data) do
    case ip_data[:type] do
      "single" ->
        case IpWhitelistRepository.list_ip_whitelists(%{"ip_address" => ip_data[:ip_address]}) do
          {:ok, {[], 0}} -> {:ok, :unique}
          {:ok, {_ips, _count}} -> {:error, :ip_already_exists}
          error -> error
        end
      "range" ->
        case IpWhitelistRepository.list_ip_whitelists(%{"ip_range" => ip_data[:ip_range]}) do
          {:ok, {[], 0}} -> {:ok, :unique}
          {:ok, {_ips, _count}} -> {:error, :ip_range_already_exists}
          error -> error
        end
      _ -> {:error, :invalid_ip_type}
    end
  end

  # 验证IP格式
  defp validate_ip_format(ip_data) do
    case ip_data[:type] do
      "single" -> validate_single_ip(ip_data[:ip_address])
      "range" -> validate_ip_range(ip_data[:ip_range])
      _ -> {:error, :invalid_ip_type}
    end
  end

  # 验证单个IP地址
  defp validate_single_ip(ip_address) when is_binary(ip_address) do
    case :inet.parse_address(String.to_charlist(ip_address)) do
      {:ok, _} -> {:ok, :valid}
      {:error, _} -> {:error, :invalid_ip_format}
    end
  end
  defp validate_single_ip(_), do: {:error, :invalid_ip_format}

  # 验证IP范围
  defp validate_ip_range(ip_range) when is_binary(ip_range) do
    # 简单的CIDR格式验证
    if String.contains?(ip_range, "/") do
      [ip, mask] = String.split(ip_range, "/", parts: 2)
      with {:ok, _} <- validate_single_ip(ip),
           {mask_int, ""} <- Integer.parse(mask),
           true <- mask_int >= 0 and mask_int <= 32 do
        {:ok, :valid}
      else
        _ -> {:error, :invalid_ip_range_format}
      end
    else
      {:error, :invalid_ip_range_format}
    end
  end
  defp validate_ip_range(_), do: {:error, :invalid_ip_range_format}

  # 验证IP类型
  defp validate_ip_type(type) when type in ["single", "range"] do
    {:ok, :valid}
  end
  defp validate_ip_type(_), do: {:error, :invalid_ip_type}

  # 验证IP数据内容
  defp validate_ip_data_content(%{type: "single", ip_address: ip_address}) when is_binary(ip_address) do
    {:ok, :valid}
  end
  defp validate_ip_data_content(%{type: "range", ip_range: ip_range}) when is_binary(ip_range) do
    {:ok, :valid}
  end
  defp validate_ip_data_content(_), do: {:error, :invalid_ip_data_content}

  # ==================== 通用权限验证私有函数 ====================

  # 验证更新者权限
  defp verify_updater_permissions(updater_id, target_id, operation) do
    Logger.debug("🔍 [系统设置服务] 验证更新者权限: #{updater_id} - #{operation}")

    with {:ok, updater} <- AdminUserRepository.get_admin_user(updater_id, [load: [:role]]),
         {:ok, _} <- check_user_status(updater),
         {:ok, _} <- check_role_status(updater.role),
         {:ok, _} <- check_operation_permission(updater, operation) do
      {:ok, updater}
    else
      error -> error
    end
  end

  # 验证删除者权限
  defp verify_deleter_permissions(deleter_id, target_id, operation) do
    Logger.debug("🔍 [系统设置服务] 验证删除者权限: #{deleter_id} - #{operation}")

    with {:ok, deleter} <- AdminUserRepository.get_admin_user(deleter_id, [load: [:role]]),
         {:ok, _} <- check_user_status(deleter),
         {:ok, _} <- check_role_status(deleter.role),
         {:ok, _} <- check_operation_permission(deleter, operation) do
      {:ok, deleter}
    else
      error -> error
    end
  end

  # 验证激活者权限
  defp verify_activator_permissions(activator_id, operation) do
    Logger.debug("🔍 [系统设置服务] 验证激活者权限: #{activator_id} - #{operation}")

    with {:ok, activator} <- AdminUserRepository.get_admin_user(activator_id, [load: [:role]]),
         {:ok, _} <- check_user_status(activator),
         {:ok, _} <- check_role_status(activator.role),
         {:ok, _} <- check_operation_permission(activator, operation) do
      {:ok, activator}
    else
      error -> error
    end
  end

  # 验证禁用者权限
  defp verify_deactivator_permissions(deactivator_id, target_id, operation) do
    Logger.debug("🔍 [系统设置服务] 验证禁用者权限: #{deactivator_id} - #{operation}")

    with {:ok, deactivator} <- AdminUserRepository.get_admin_user(deactivator_id, [load: [:role]]),
         {:ok, _} <- check_user_status(deactivator),
         {:ok, _} <- check_role_status(deactivator.role),
         {:ok, _} <- check_operation_permission(deactivator, operation) do
      {:ok, deactivator}
    else
      error -> error
    end
  end

  # 验证请求者权限
  defp verify_requester_permissions(requester_id, operation) do
    Logger.debug("🔍 [系统设置服务] 验证请求者权限: #{requester_id} - #{operation}")

    with {:ok, requester} <- AdminUserRepository.get_admin_user(requester_id, [load: [:role]]),
         {:ok, _} <- check_user_status(requester),
         {:ok, _} <- check_role_status(requester.role),
         {:ok, _} <- check_operation_permission(requester, operation) do
      {:ok, requester}
    else
      error -> error
    end
  end

  # 检查操作权限
  defp check_operation_permission(admin_user, operation) do
    # 这里应该实现具体的权限检查逻辑
    # 根据用户角色和权限代码来判断是否有操作权限
    Logger.debug("🔍 [系统设置服务] 检查操作权限: #{admin_user.username} - #{operation}")

    case admin_user.role.code do
      @super_admin_role_code -> {:ok, :allowed}  # 超级管理员拥有所有权限
      @default_admin_role_code -> check_admin_permission(admin_user.role, operation)
      _ -> check_role_permission(admin_user.role, operation)
    end
  end

  # 检查管理员权限
  defp check_admin_permission(role, operation) do
    # 管理员角色的权限检查逻辑
    admin_permissions = [
      :create_admin_user, :update_admin_user, :activate_admin_user, :deactivate_admin_user,
      :create_role, :update_role, :activate_role, :deactivate_role,
      :create_permission, :update_permission, :view_permissions,
      :create_ip_whitelist, :view_system_stats, :view_user_permissions
    ]

    if operation in admin_permissions do
      {:ok, :allowed}
    else
      {:error, :permission_denied}
    end
  end

  # 检查角色权限
  defp check_role_permission(role, operation) do
    # 根据角色的权限代码检查具体权限
    operation_code = operation_to_permission_code(operation)

    if operation_code in (role.permission_codes || []) do
      {:ok, :allowed}
    else
      {:error, :permission_denied}
    end
  end

  # 操作转权限代码
  defp operation_to_permission_code(operation) do
    case operation do
      :create_admin_user -> "system:user:create"
      :update_admin_user -> "system:user:update"
      :delete_admin_user -> "system:user:delete"
      :activate_admin_user -> "system:user:activate"
      :deactivate_admin_user -> "system:user:deactivate"
      :create_role -> "system:role:create"
      :update_role -> "system:role:update"
      :delete_role -> "system:role:delete"
      :activate_role -> "system:role:activate"
      :deactivate_role -> "system:role:deactivate"
      :create_permission -> "system:permission:create"
      :update_permission -> "system:permission:update"
      :delete_permission -> "system:permission:delete"
      :view_permissions -> "system:permission:view"
      :create_ip_whitelist -> "system:ip:create"
      :view_system_stats -> "system:stats:view"
      :view_user_permissions -> "system:user:permissions"
      _ -> "unknown:operation"
    end
  end

  # ==================== 数据验证和处理私有函数 ====================

  # 验证更新数据
  defp validate_update_data(update_data, target_user) do
    Logger.debug("🔍 [系统设置服务] 验证更新数据")

    # 过滤掉不允许更新的字段
    allowed_fields = [:username, :email, :real_name, :phone, :role_id, :status]
    filtered_data = Map.take(update_data, allowed_fields)

    # 验证每个字段
    Enum.reduce_while(filtered_data, {:ok, %{}}, fn {field, value}, {:ok, acc} ->
      case validate_update_field(field, value, target_user) do
        {:ok, validated_value} -> {:cont, {:ok, Map.put(acc, field, validated_value)}}
        error -> {:halt, error}
      end
    end)
  end

  # 验证更新字段
  defp validate_update_field(:username, username, _target_user) do
    validate_username(username)
  end
  defp validate_update_field(:email, email, _target_user) do
    validate_email(email)
  end
  defp validate_update_field(:role_id, role_id, _target_user) when is_integer(role_id) do
    case validate_role_assignment(role_id) do
      {:ok, _role} -> {:ok, role_id}
      error -> error
    end
  end
  defp validate_update_field(field, value, _target_user) do
    {:ok, value}  # 其他字段直接通过
  end

  # 验证更新权限
  defp verify_update_permissions(updater, target_user, update_data) do
    # 检查是否试图修改自己无权修改的用户
    cond do
      updater.id == target_user.id -> {:ok, :self_update}  # 允许用户更新自己的部分信息
      updater.role.level <= target_user.role.level -> {:error, :insufficient_level}  # 级别不够
      true -> {:ok, :allowed}
    end
  end

  # 验证删除权限
  defp verify_delete_permissions(deleter, target_user) do
    cond do
      deleter.id == target_user.id -> {:error, :cannot_delete_self}  # 不能删除自己
      deleter.role.level <= target_user.role.level -> {:error, :insufficient_level}  # 级别不够
      true -> {:ok, :allowed}
    end
  end

  # 检查用户是否可删除
  defp check_user_deletable(target_user) do
    # 超级管理员不能删除
    if target_user.role.code == @super_admin_role_code do
      {:error, :super_admin_cannot_delete}
    else
      {:ok, :deletable}
    end
  end

  # 检查用户是否可禁用
  defp check_user_deactivatable(target_user, deactivator_id) do
    cond do
      target_user.id == deactivator_id -> {:error, :cannot_deactivate_self}
      target_user.role.code == @super_admin_role_code -> {:error, :super_admin_cannot_deactivate}
      true -> {:ok, :deactivatable}
    end
  end

  # 软删除管理员用户
  defp soft_delete_admin_user(admin_user_id, deleter_id) do
    # 实现软删除逻辑，将状态设置为已删除
    AdminUserRepository.update_admin_user(admin_user_id, %{status: 0, deleted_by: deleter_id, deleted_at: DateTime.utc_now()})
  end

  # 提取旧值
  defp extract_old_values(target_user, update_data) do
    Map.take(target_user, Map.keys(update_data))
  end

  # 提取角色旧值
  defp extract_role_old_values(target_role, update_data) do
    Map.take(target_role, Map.keys(update_data))
  end

  # 提取权限旧值
  defp extract_permission_old_values(target_permission, update_data) do
    Map.take(target_permission, Map.keys(update_data))
  end

  # 验证角色更新数据
  defp validate_role_update_data(update_data, target_role) do
    # 实现角色更新数据验证逻辑
    {:ok, update_data}
  end

  # 验证权限更新数据
  defp validate_permission_update_data(update_data, target_permission) do
    # 实现权限更新数据验证逻辑
    {:ok, update_data}
  end

  # 验证角色更新权限
  defp verify_role_update_permissions(updater, target_role, update_data) do
    # 实现角色更新权限验证逻辑
    {:ok, :allowed}
  end

  # 验证角色删除权限
  defp verify_role_delete_permissions(deleter, target_role) do
    # 实现角色删除权限验证逻辑
    {:ok, :allowed}
  end

  # 检查角色是否可禁用
  defp check_role_deactivatable(target_role) do
    # 系统内置角色不能禁用
    system_roles = [@default_admin_role_code, @super_admin_role_code, @guest_role_code]

    if target_role.code in system_roles do
      {:error, :system_role_cannot_deactivate}
    else
      {:ok, :deactivatable}
    end
  end

  # 检查角色禁用影响
  defp check_role_deactivation_impact(role_id) do
    # 检查禁用角色对现有用户的影响
    case AdminUserRepository.list_admin_users_by_role(role_id) do
      {:ok, []} -> {:ok, :no_impact}
      {:ok, users} ->
        Logger.warn("⚠️ [系统设置服务] 角色禁用将影响 #{length(users)} 个用户")
        {:ok, :has_impact}
      error -> error
    end
  end
end
