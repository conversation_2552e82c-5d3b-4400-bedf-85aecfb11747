defmodule RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.ExchangeConfigRepository do
  @moduledoc """
  💱 兑换配置数据访问层

  负责兑换配置相关的数据库操作，包括：
  - 兑换配置的CRUD操作
  - 配置状态管理
  - 兑换类型查询
  - 兑换比例管理
  - VIP等级配置
  - 兑换统计分析
  """

  require Logger
  alias Teen.PaymentSystem.ExchangeConfig

  # 缓存配置
  @cache_ttl 300  # 5分钟缓存

  # ==================== 基础CRUD操作 ====================

  @doc """
  创建兑换配置

  ## 参数
  - `config_data` - 配置数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, config}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_exchange_config(config_data, options \\ []) do
    Logger.info("💱 [兑换配置仓储] 创建兑换配置: #{inspect(config_data[:config_name])}")

    try do
      result = ExchangeConfig
      |> Ash.Changeset.for_create(:create, config_data)
      |> Ash.create()

      case result do
        {:ok, config} ->
          Logger.info("✅ [兑换配置仓储] 兑换配置创建成功: #{config.config_name}")
          clear_cache()
          {:ok, config}
        
        {:error, error} ->
          Logger.error("❌ [兑换配置仓储] 兑换配置创建失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [兑换配置仓储] 创建兑换配置异常: #{inspect(exception)}")
        {:error, :create_config_exception}
    end
  end

  @doc """
  获取兑换配置

  ## 参数
  - `config_id` - 配置ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, config}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_exchange_config(config_id, options \\ []) do
    Logger.info("💱 [兑换配置仓储] 获取兑换配置: #{config_id}")

    try do
      result = ExchangeConfig
      |> Ash.Query.for_read(:read)
      |> Ash.Query.filter(id == ^config_id)
      |> maybe_load_associations(options)
      |> Ash.read_one()

      case result do
        {:ok, config} when not is_nil(config) ->
          Logger.info("✅ [兑换配置仓储] 兑换配置获取成功: #{config.config_name}")
          {:ok, config}
        
        {:ok, nil} ->
          Logger.warn("⚠️ [兑换配置仓储] 兑换配置不存在: #{config_id}")
          {:ok, nil}
        
        {:error, error} ->
          Logger.error("❌ [兑换配置仓储] 兑换配置获取失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [兑换配置仓储] 获取兑换配置异常: #{inspect(exception)}")
        {:error, :get_config_exception}
    end
  end

  @doc """
  根据名称获取兑换配置

  ## 参数
  - `config_name` - 配置名称
  - `options` - 选项参数

  ## 返回
  - `{:ok, config}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_exchange_config_by_name(config_name, options \\ []) do
    Logger.info("💱 [兑换配置仓储] 根据名称获取兑换配置: #{config_name}")

    try do
      result = ExchangeConfig
      |> Ash.Query.for_read(:get_by_name, %{config_name: config_name})
      |> maybe_load_associations(options)
      |> Ash.read_one()

      case result do
        {:ok, config} when not is_nil(config) ->
          Logger.info("✅ [兑换配置仓储] 兑换配置获取成功: #{config.config_name}")
          {:ok, config}
        
        {:ok, nil} ->
          Logger.warn("⚠️ [兑换配置仓储] 兑换配置不存在: #{config_name}")
          {:ok, nil}
        
        {:error, error} ->
          Logger.error("❌ [兑换配置仓储] 兑换配置获取失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [兑换配置仓储] 根据名称获取兑换配置异常: #{inspect(exception)}")
        {:error, :get_config_by_name_exception}
    end
  end

  @doc """
  更新兑换配置

  ## 参数
  - `config_id` - 配置ID
  - `update_data` - 更新数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, config}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_exchange_config(config_id, update_data, options \\ []) do
    Logger.info("💱 [兑换配置仓储] 更新兑换配置: #{config_id}")

    try do
      result = ExchangeConfig
      |> Ash.get!(config_id)
      |> Ash.Changeset.for_update(:update, update_data)
      |> Ash.update()

      case result do
        {:ok, config} ->
          Logger.info("✅ [兑换配置仓储] 兑换配置更新成功: #{config.config_name}")
          clear_cache()
          {:ok, config}
        
        {:error, error} ->
          Logger.error("❌ [兑换配置仓储] 兑换配置更新失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [兑换配置仓储] 更新兑换配置异常: #{inspect(exception)}")
        {:error, :update_config_exception}
    end
  end

  @doc """
  删除兑换配置

  ## 参数
  - `config_id` - 配置ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, config}` - 成功
  - `{:error, reason}` - 失败
  """
  def delete_exchange_config(config_id, options \\ []) do
    Logger.info("💱 [兑换配置仓储] 删除兑换配置: #{config_id}")

    try do
      result = ExchangeConfig
      |> Ash.get!(config_id)
      |> Ash.destroy()

      case result do
        :ok ->
          Logger.info("✅ [兑换配置仓储] 兑换配置删除成功: #{config_id}")
          clear_cache()
          {:ok, :deleted}
        
        {:error, error} ->
          Logger.error("❌ [兑换配置仓储] 兑换配置删除失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [兑换配置仓储] 删除兑换配置异常: #{inspect(exception)}")
        {:error, :delete_config_exception}
    end
  end

  # ==================== 查询操作 ====================

  @doc """
  列出兑换配置

  ## 参数
  - `params` - 查询参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, {configs, total_count}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_exchange_configs(params \\ %{}, options \\ []) do
    Logger.info("💱 [兑换配置仓储] 列出兑换配置: #{inspect(params)}")

    try do
      query = ExchangeConfig
      |> Ash.Query.for_read(:read)
      |> apply_filters(params)
      |> apply_sorting(params)
      |> maybe_load_associations(options)

      # 获取总数
      total_count_result = query |> Ash.count()
      
      # 应用分页
      paginated_query = apply_pagination(query, params)
      
      # 执行查询
      configs_result = paginated_query |> Ash.read()

      case {configs_result, total_count_result} do
        {{:ok, configs}, {:ok, total_count}} ->
          Logger.info("✅ [兑换配置仓储] 兑换配置列表获取成功: #{length(configs)}/#{total_count}")
          {:ok, {configs, total_count}}
        
        {{:error, error}, _} ->
          Logger.error("❌ [兑换配置仓储] 兑换配置列表获取失败: #{inspect(error)}")
          {:error, error}
        
        {_, {:error, error}} ->
          Logger.error("❌ [兑换配置仓储] 兑换配置计数失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [兑换配置仓储] 列出兑换配置异常: #{inspect(exception)}")
        {:error, :list_configs_exception}
    end
  end

  @doc """
  获取活跃兑换配置

  ## 参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, configs}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_active_exchange_configs(options \\ []) do
    Logger.info("💱 [兑换配置仓储] 获取活跃兑换配置")

    try do
      result = ExchangeConfig
      |> Ash.Query.for_read(:list_active_configs)
      |> maybe_load_associations(options)
      |> Ash.read()

      case result do
        {:ok, configs} ->
          Logger.info("✅ [兑换配置仓储] 活跃兑换配置获取成功: #{length(configs)}个")
          {:ok, configs}
        
        {:error, error} ->
          Logger.error("❌ [兑换配置仓储] 活跃兑换配置获取失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [兑换配置仓储] 获取活跃兑换配置异常: #{inspect(exception)}")
        {:error, :list_active_configs_exception}
    end
  end

  @doc """
  根据兑换类型获取配置

  ## 参数
  - `exchange_type` - 兑换类型
  - `options` - 选项参数

  ## 返回
  - `{:ok, configs}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_exchange_configs_by_type(exchange_type, options \\ []) do
    Logger.info("💱 [兑换配置仓储] 根据兑换类型获取配置: #{exchange_type}")

    try do
      result = ExchangeConfig
      |> Ash.Query.for_read(:read)
      |> Ash.Query.filter(exchange_type == ^exchange_type and status == 1)
      |> maybe_load_associations(options)
      |> Ash.read()

      case result do
        {:ok, configs} ->
          Logger.info("✅ [兑换配置仓储] 类型配置获取成功: #{length(configs)}个")
          {:ok, configs}
        
        {:error, error} ->
          Logger.error("❌ [兑换配置仓储] 类型配置获取失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [兑换配置仓储] 根据兑换类型获取配置异常: #{inspect(exception)}")
        {:error, :list_configs_by_type_exception}
    end
  end

  @doc """
  根据VIP等级获取可用配置

  ## 参数
  - `vip_level` - VIP等级
  - `options` - 选项参数

  ## 返回
  - `{:ok, configs}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_exchange_configs_by_vip_level(vip_level, options \\ []) do
    Logger.info("💱 [兑换配置仓储] 根据VIP等级获取可用配置: #{vip_level}")

    try do
      result = ExchangeConfig
      |> Ash.Query.for_read(:read)
      |> Ash.Query.filter(status == 1 and (is_nil(vip_level_required) or vip_level_required <= ^vip_level))
      |> maybe_load_associations(options)
      |> Ash.read()

      case result do
        {:ok, configs} ->
          Logger.info("✅ [兑换配置仓储] VIP配置获取成功: #{length(configs)}个")
          {:ok, configs}
        
        {:error, error} ->
          Logger.error("❌ [兑换配置仓储] VIP配置获取失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [兑换配置仓储] 根据VIP等级获取可用配置异常: #{inspect(exception)}")
        {:error, :list_configs_by_vip_level_exception}
    end
  end

  # ==================== 状态管理操作 ====================

  @doc """
  激活兑换配置

  ## 参数
  - `config_id` - 配置ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, config}` - 成功
  - `{:error, reason}` - 失败
  """
  def activate_exchange_config(config_id, options \\ []) do
    Logger.info("💱 [兑换配置仓储] 激活兑换配置: #{config_id}")

    try do
      result = ExchangeConfig
      |> Ash.get!(config_id)
      |> Ash.Changeset.for_update(:enable)
      |> Ash.update()

      case result do
        {:ok, config} ->
          Logger.info("✅ [兑换配置仓储] 兑换配置激活成功: #{config.config_name}")
          clear_cache()
          {:ok, config}
        
        {:error, error} ->
          Logger.error("❌ [兑换配置仓储] 兑换配置激活失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [兑换配置仓储] 激活兑换配置异常: #{inspect(exception)}")
        {:error, :activate_config_exception}
    end
  end

  @doc """
  禁用兑换配置

  ## 参数
  - `config_id` - 配置ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, config}` - 成功
  - `{:error, reason}` - 失败
  """
  def deactivate_exchange_config(config_id, options \\ []) do
    Logger.info("💱 [兑换配置仓储] 禁用兑换配置: #{config_id}")

    try do
      result = ExchangeConfig
      |> Ash.get!(config_id)
      |> Ash.Changeset.for_update(:disable)
      |> Ash.update()

      case result do
        {:ok, config} ->
          Logger.info("✅ [兑换配置仓储] 兑换配置禁用成功: #{config.config_name}")
          clear_cache()
          {:ok, config}
        
        {:error, error} ->
          Logger.error("❌ [兑换配置仓储] 兑换配置禁用失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [兑换配置仓储] 禁用兑换配置异常: #{inspect(exception)}")
        {:error, :deactivate_config_exception}
    end
  end

  # ==================== 私有辅助函数 ====================

  # 应用过滤条件
  defp apply_filters(query, params) do
    Enum.reduce(params, query, fn
      {"config_name", config_name}, query when is_binary(config_name) and config_name != "" ->
        Ash.Query.filter(query, contains(config_name, ^config_name))
      
      {"exchange_type", exchange_type}, query when is_integer(exchange_type) ->
        Ash.Query.filter(query, exchange_type == ^exchange_type)
      
      {"status", status}, query when is_integer(status) ->
        Ash.Query.filter(query, status == ^status)
      
      {"vip_level_required", vip_level}, query when is_integer(vip_level) ->
        Ash.Query.filter(query, vip_level_required <= ^vip_level)
      
      {"exchange_rate_gte", exchange_rate}, query when is_binary(exchange_rate) ->
        case Decimal.parse(exchange_rate) do
          {rate, ""} -> Ash.Query.filter(query, exchange_rate >= ^rate)
          _ -> query
        end
      
      {"exchange_rate_lte", exchange_rate}, query when is_binary(exchange_rate) ->
        case Decimal.parse(exchange_rate) do
          {rate, ""} -> Ash.Query.filter(query, exchange_rate <= ^rate)
          _ -> query
        end
      
      _other, query ->
        query
    end)
  end

  # 应用排序
  defp apply_sorting(query, params) do
    case params do
      %{"sort_by" => sort_by, "sort_order" => sort_order} ->
        sort_field = String.to_existing_atom(sort_by)
        case sort_order do
          "desc" -> Ash.Query.sort(query, desc: sort_field)
          _ -> Ash.Query.sort(query, asc: sort_field)
        end
      
      %{"sort_by" => sort_by} ->
        sort_field = String.to_existing_atom(sort_by)
        Ash.Query.sort(query, asc: sort_field)
      
      _ ->
        Ash.Query.sort(query, asc: :exchange_type, desc: :inserted_at)
    end
  end

  # 应用分页
  defp apply_pagination(query, params) do
    page = Map.get(params, "page", 1) |> ensure_integer()
    page_size = Map.get(params, "page_size", 20) |> ensure_integer()
    offset = (page - 1) * page_size

    query
    |> Ash.Query.limit(page_size)
    |> Ash.Query.offset(offset)
  end

  # 加载关联数据
  defp maybe_load_associations(query, options) do
    case Keyword.get(options, :load, []) do
      [] -> query
      associations -> Ash.Query.load(query, associations)
    end
  end

  # 确保整数类型
  defp ensure_integer(value) when is_integer(value), do: value
  defp ensure_integer(value) when is_binary(value) do
    case Integer.parse(value) do
      {int, ""} -> int
      _ -> 1
    end
  end
  defp ensure_integer(_), do: 1

  # 清除缓存
  defp clear_cache do
    # 这里可以实现缓存清除逻辑
    :ok
  end
end
