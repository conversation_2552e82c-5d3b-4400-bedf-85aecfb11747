defmodule Cypridina.Teen.GameSystem.Games.Slot777.Slot777Jackpot do
  @moduledoc """
  Slot777 Jackpot管理模块 - 房间内部状态管理
  负责：
  - Jackpot池管理
  - 中奖记录
  - 状态计算（不再是独立的GenServer）

  注意：此模块现在是纯函数模块，不再作为独立进程启动
  """

  require Logger

  # Jackpot状态结构
  defstruct [
    :current_amount,      # 当前奖池金额
    :base_amount,         # 基础奖池金额
    :records,             # 中奖记录
    :last_winner,         # 最后中奖者
    :total_contributed,   # 总贡献金额
    # 🧪 临时测试功能 - 可随时删除
    :test_mode_enabled,   # 是否启用测试模式
    :player_spin_counts   # 玩家旋转次数计数器 %{player_id => count}
  ]

  # 默认配置
  @default_base_amount 100000
  @contribution_rate 0.01  # 1%的下注进入奖池
  @max_records 100        # 最多保存100条记录

  ## 状态管理 API（纯函数）

  @doc """
  初始化Jackpot状态
  """
  def init_state(opts \\ []) do
    # 创建一些初始测试记录，让前端有数据显示
    initial_records = create_initial_test_records()

    %__MODULE__{
      current_amount: @default_base_amount,
      base_amount: @default_base_amount,
      records: initial_records,
      last_winner: nil,
      total_contributed: 0,
      # 🧪 临时测试功能 - 默认启用测试模式
      test_mode_enabled: Keyword.get(opts, :test_mode_enabled, false),
      player_spin_counts: %{}
    }
  end

  @doc """
  获取当前Jackpot金额
  """
  def get_current_amount(jackpot_state) do
    jackpot_state.current_amount
  end

  @doc """
  贡献到Jackpot池，返回{贡献金额, 新状态}
  """
  def contribute(jackpot_state, bet_amount) do
    contribution = trunc(bet_amount * @contribution_rate)
    new_amount = jackpot_state.current_amount + contribution
    new_total = jackpot_state.total_contributed + contribution

    new_state = %{jackpot_state |
      current_amount: new_amount,
      total_contributed: new_total
    }

    Logger.debug("🎰 [JACKPOT] 奖池增加: #{contribution}, 当前总额: #{new_amount}")
    {contribution, new_state}
  end

  @doc """
  触发Jackpot中奖，返回{:ok, jackpot_amount, new_state}
  """
  def trigger_jackpot(jackpot_state, player_id, player_name, bet_amount, seven_count) do
    # 计算Jackpot奖金
    jackpot_amount = calculate_jackpot_amount(jackpot_state.current_amount, seven_count)

    # 创建中奖记录
    record = %{
      player_id: player_id,
      player_name: player_name,
      amount: jackpot_amount,
      seven_count: seven_count,
      bet_amount: bet_amount,
      timestamp: DateTime.utc_now(),
      type: get_jackpot_type(seven_count)
    }

    # 更新状态
    new_records = [record | Enum.take(jackpot_state.records, @max_records - 1)]
    new_amount = max(jackpot_state.base_amount, jackpot_state.current_amount - jackpot_amount)

    # 🧪 测试模式：中奖后重置该玩家的旋转计数器
    new_player_spin_counts = if jackpot_state.test_mode_enabled do
      Logger.info("🧪 [TEST_MODE] 玩家 #{player_id} 中奖后重置旋转计数器")
      Map.put(jackpot_state.player_spin_counts, player_id, 0)
    else
      jackpot_state.player_spin_counts
    end

    new_state = %{jackpot_state |
      current_amount: new_amount,
      records: new_records,
      last_winner: record,
      player_spin_counts: new_player_spin_counts
    }

    Logger.info("🎰 [JACKPOT] 玩家 #{player_name}(#{player_id}) 中奖！金额: #{jackpot_amount}, 7的数量: #{seven_count}")

    {:ok, jackpot_amount, new_state}
  end

  @doc """
  获取Jackpot中奖记录
  """
  def get_records(jackpot_state) do
    {:ok, jackpot_state.records}
  end

  @doc """
  获取最近的中奖者记录
  """
  def get_recent_winners(jackpot_state, limit \\ 20) do
    winners = Enum.take(jackpot_state.records, limit)
    winners
  end

  @doc """
  贡献到Jackpot池（别名函数）
  """
  def contribute_to_jackpot(jackpot_state, contribution) do
    new_amount = jackpot_state.current_amount + contribution
    new_total = jackpot_state.total_contributed + contribution

    new_state = %{jackpot_state |
      current_amount: new_amount,
      total_contributed: new_total
    }

    Logger.debug("🎰 [JACKPOT] 奖池增加: #{contribution}, 当前总额: #{new_amount}")
    new_state
  end

  @doc """
  中奖Jackpot（扣除奖金）
  """
  def win_jackpot(jackpot_state, jackpot_amount, player_name, seven_count) do
    # 创建中奖记录
    record = %{
      player_id: "unknown",
      player_name: player_name,
      amount: jackpot_amount,
      seven_count: seven_count,
      bet_amount: 0,
      timestamp: DateTime.utc_now(),
      type: get_jackpot_type(seven_count)
    }

    # 更新状态
    new_records = [record | Enum.take(jackpot_state.records, @max_records - 1)]
    new_amount = max(jackpot_state.base_amount, jackpot_state.current_amount - jackpot_amount)

    new_state = %{jackpot_state |
      current_amount: new_amount,
      records: new_records,
      last_winner: record
    }

    Logger.info("🎰 [JACKPOT] 玩家 #{player_name} 中奖！金额: #{jackpot_amount}, 7的数量: #{seven_count}")
    new_state
  end

  @doc """
  重置Jackpot池到基础金额
  """
  def reset_to_base(jackpot_state) do
    new_state = %{jackpot_state |
      current_amount: jackpot_state.base_amount,
      total_contributed: 0
    }

    Logger.info("🎰 [JACKPOT] 奖池重置到基础金额: #{jackpot_state.base_amount}")
    new_state
  end

  # 🧪 临时测试功能 - 可随时删除这些函数
  @doc """
  启用测试模式：玩家在第5次旋转时必中jackpot
  """
  def enable_test_mode(jackpot_state) do
    new_state = %{jackpot_state | test_mode_enabled: true}
    Logger.info("🧪 [TEST_MODE] 测试模式已启用：玩家第5次旋转必中jackpot")
    new_state
  end

  @doc """
  禁用测试模式
  """
  def disable_test_mode(jackpot_state) do
    new_state = %{jackpot_state | test_mode_enabled: false, player_spin_counts: %{}}
    Logger.info("🧪 [TEST_MODE] 测试模式已禁用")
    new_state
  end

  @doc """
  检查玩家是否应该中奖（测试模式）
  """
  def should_trigger_test_jackpot(jackpot_state, player_id) do
    if jackpot_state.test_mode_enabled do
      current_count = Map.get(jackpot_state.player_spin_counts, player_id, 0)
      should_trigger = current_count >= 5
      Logger.info("🧪 [TEST_MODE] 玩家 #{player_id} 旋转计数: #{current_count}/5, 应该中奖: #{should_trigger}")
      should_trigger
    else
      false
    end
  end

  @doc """
  记录玩家旋转次数（测试模式），返回新状态
  """
  def record_player_spin(jackpot_state, player_id) do
    if jackpot_state.test_mode_enabled do
      current_count = Map.get(jackpot_state.player_spin_counts, player_id, 0)
      new_count = current_count + 1
      new_counts = Map.put(jackpot_state.player_spin_counts, player_id, new_count)
      new_state = %{jackpot_state | player_spin_counts: new_counts}

      Logger.info("🧪 [TEST_MODE] 玩家 #{player_id} 旋转计数: #{new_count}/5")
      new_state
    else
      jackpot_state
    end
  end

  @doc """
  清理玩家数据（当玩家退出房间时调用）
  """
  def cleanup_player_data(jackpot_state, player_id) do
    Logger.info("🧹 [JACKPOT_CLEANUP] 清理玩家数据 - 玩家: #{player_id}")

    # 从旋转计数器中移除玩家
    new_player_spin_counts = Map.delete(jackpot_state.player_spin_counts, player_id)

    new_state = %{jackpot_state | player_spin_counts: new_player_spin_counts}

    Logger.info("✅ [JACKPOT_CLEANUP] 玩家数据清理完成 - 玩家: #{player_id}")
    new_state
  end

  ## 辅助函数



  ## 辅助函数

  @doc """
  计算Jackpot奖金金额
  """
  def calculate_jackpot_amount(current_amount, seven_count) do
    case seven_count do
      count when count >= 5 ->
        # 5个7 = 获得80%的奖池
        trunc(current_amount * 0.8)

      4 ->
        # 4个7 = 获得50%的奖池
        trunc(current_amount * 0.5)

      3 ->
        # 3个7 = 获得20%的奖池
        trunc(current_amount * 0.2)

      _ ->
        0
    end
  end

  @doc """
  获取Jackpot类型
  """
  def get_jackpot_type(seven_count) do
    case seven_count do
      count when count >= 5 -> "MEGA_JACKPOT"
      4 -> "MAJOR_JACKPOT"
      3 -> "MINOR_JACKPOT"
      _ -> "NO_JACKPOT"
    end
  end

  @doc """
  格式化中奖记录用于前端显示
  根据前端 onUpdateRecordInfo 方法的期望格式
  """
  def format_records_for_frontend(records) do
    Enum.map(records, fn record ->
      # 格式化时间为前端期望的格式
      formatted_time = record.timestamp
      |> DateTime.to_unix()
      |> format_timestamp_for_frontend()

      %{
        "playerid" => record.player_id,
        "name" => record.player_name,
        "headid" => 1,  # 默认头像ID
        "wxheadurl" => "",  # 自定义头像URL（暂时为空）
        "sevennum" => record.seven_count,  # 前端期望的字段名
        "bet" => record.bet_amount,  # 前端期望的字段名
        "winscore" => record.amount,  # 前端期望的字段名
        "time" => formatted_time  # 前端期望的字段名
      }
    end)
  end

  @doc """
  格式化时间戳为前端显示格式
  """
  defp format_timestamp_for_frontend(unix_timestamp) do
    case DateTime.from_unix(unix_timestamp) do
      {:ok, datetime} ->
        # 转换为本地时间并格式化为 "MM-dd HH:mm" 格式
        datetime
        |> DateTime.to_string()
        |> String.slice(5, 11)  # 提取 "MM-dd HH:mm" 部分
        |> String.replace("T", " ")

      {:error, _} ->
        "Unknown"
    end
  end

  @doc """
  创建初始测试记录，让前端有数据显示
  """
  defp create_initial_test_records() do
    now = DateTime.utc_now()

    [
      # 5个7的大奖记录
      %{
        player_id: 10001,
        player_name: "Lucky777",
        amount: 80000,  # 80% 的奖池
        seven_count: 15,  # 5个7
        bet_amount: 90000,
        timestamp: DateTime.add(now, -3600, :second),  # 1小时前
        type: :grand_jackpot
      },
      # 4个7的记录
      %{
        player_id: 10002,
        player_name: "Winner888",
        amount: 50000,  # 50% 的奖池
        seven_count: 4,
        bet_amount: 45000,
        timestamp: DateTime.add(now, -7200, :second),  # 2小时前
        type: :major_jackpot
      },
      # 3个7的记录
      %{
        player_id: 10003,
        player_name: "Player999",
        amount: 20000,  # 20% 的奖池
        seven_count: 3,
        bet_amount: 18000,
        timestamp: DateTime.add(now, -10800, :second),  # 3小时前
        type: :minor_jackpot
      },
      # 另一个5个7的记录
      %{
        player_id: 10004,
        player_name: "BigWin666",
        amount: 80000,
        seven_count: 15,
        bet_amount: 90000,
        timestamp: DateTime.add(now, -14400, :second),  # 4小时前
        type: :grand_jackpot
      },
      # 4个7的记录
      %{
        player_id: 10005,
        player_name: "Fortune555",
        amount: 50000,
        seven_count: 4,
        bet_amount: 36000,
        timestamp: DateTime.add(now, -18000, :second),  # 5小时前
        type: :major_jackpot
      }
    ]
  end
end
