defmodule RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.FreeBonusRepository do
  @moduledoc """
  🎁 Free Bonus仓储层
  
  负责Free Bonus相关的数据访问操作，包括：
  - Free Bonus的CRUD操作
  - 状态管理（启用/禁用）
  - 游戏类型查询
  - 使用统计管理
  - 冷却时间检查
  """

  require Logger
  alias Teen.ActivitySystem.FreeBonus
  alias Ash.Query

  # ==================== 基础CRUD操作 ====================

  @doc """
  创建Free Bonus
  """
  def create_free_bonus(bonus_data, options \\ []) do
    Logger.info("🎁 [Free Bonus仓储] 创建Free Bonus: #{inspect(bonus_data[:bonus_name])}")
    
    try do
      result = FreeBonus
      |> Ash.Changeset.for_create(:create, bonus_data)
      |> Ash.create()
      
      case result do
        {:ok, bonus} ->
          Logger.info("✅ [Free Bonus仓储] Free Bonus创建成功: #{bonus.bonus_name}")
          maybe_load_associations({:ok, bonus}, options)
          
        {:error, reason} ->
          Logger.error("❌ [Free Bonus仓储] Free Bonus创建失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [Free Bonus仓储] 创建Free Bonus异常: #{inspect(exception)}")
        {:error, :create_failed}
    end
  end

  @doc """
  根据ID获取Free Bonus
  """
  def get_free_bonus(bonus_id, options \\ []) do
    Logger.info("🎁 [Free Bonus仓储] 获取Free Bonus: #{bonus_id}")
    
    try do
      result = FreeBonus
      |> Ash.Query.for_read(:read)
      |> Ash.Query.filter(id == ^bonus_id)
      |> maybe_load_associations(options)
      |> Ash.read_one()
      
      case result do
        {:ok, nil} ->
          Logger.warn("⚠️ [Free Bonus仓储] Free Bonus不存在: #{bonus_id}")
          {:error, :not_found}
          
        {:ok, bonus} ->
          Logger.info("✅ [Free Bonus仓储] 获取Free Bonus成功: #{bonus.bonus_name}")
          {:ok, bonus}
          
        {:error, reason} ->
          Logger.error("❌ [Free Bonus仓储] 获取Free Bonus失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [Free Bonus仓储] 获取Free Bonus异常: #{inspect(exception)}")
        {:error, :query_failed}
    end
  end

  @doc """
  更新Free Bonus
  """
  def update_free_bonus(bonus_id, update_data, options \\ []) do
    Logger.info("🎁 [Free Bonus仓储] 更新Free Bonus: #{bonus_id}")
    
    try do
      with {:ok, bonus} <- get_free_bonus(bonus_id),
           {:ok, updated_bonus} <- FreeBonus.update(bonus, update_data) do
        Logger.info("✅ [Free Bonus仓储] Free Bonus更新成功: #{updated_bonus.bonus_name}")
        maybe_load_associations({:ok, updated_bonus}, options)
      else
        {:error, reason} ->
          Logger.error("❌ [Free Bonus仓储] 更新Free Bonus失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [Free Bonus仓储] 更新Free Bonus异常: #{inspect(exception)}")
        {:error, :update_failed}
    end
  end

  @doc """
  删除Free Bonus
  """
  def delete_free_bonus(bonus_id) do
    Logger.info("🎁 [Free Bonus仓储] 删除Free Bonus: #{bonus_id}")
    
    try do
      with {:ok, bonus} <- get_free_bonus(bonus_id),
           :ok <- FreeBonus.destroy(bonus) do
        Logger.info("✅ [Free Bonus仓储] Free Bonus删除成功: #{bonus_id}")
        {:ok, :deleted}
      else
        {:error, reason} ->
          Logger.error("❌ [Free Bonus仓储] 删除Free Bonus失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [Free Bonus仓储] 删除Free Bonus异常: #{inspect(exception)}")
        {:error, :delete_failed}
    end
  end

  # ==================== 列表查询操作 ====================

  @doc """
  获取Free Bonus列表
  """
  def list_free_bonuses(options \\ []) do
    Logger.info("🎁 [Free Bonus仓储] 获取Free Bonus列表")
    
    try do
      query = FreeBonus
      |> Ash.Query.for_read(:read)
      |> apply_list_filters(options)
      |> apply_sorting(options)
      |> maybe_load_associations(options)
      
      result = if options[:paginate] do
        Ash.read(query, page: build_page_params(options))
      else
        Ash.read(query)
      end
      
      case result do
        {:ok, bonuses} when is_list(bonuses) ->
          Logger.info("✅ [Free Bonus仓储] 获取Free Bonus列表成功: #{length(bonuses)}条")
          {:ok, bonuses}
          
        {:ok, %{results: bonuses, more?: more?, count: count}} ->
          Logger.info("✅ [Free Bonus仓储] 分页获取Free Bonus成功: #{length(bonuses)}条，总数: #{count}")
          {:ok, %{bonuses: bonuses, more?: more?, count: count}}
          
        {:error, reason} ->
          Logger.error("❌ [Free Bonus仓储] 获取Free Bonus列表失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [Free Bonus仓储] 获取Free Bonus列表异常: #{inspect(exception)}")
        {:error, :query_failed}
    end
  end

  @doc """
  获取启用的Free Bonus列表
  """
  def list_active_free_bonuses(options \\ []) do
    Logger.info("🎁 [Free Bonus仓储] 获取启用的Free Bonus列表")
    
    try do
      result = FreeBonus
      |> Ash.Query.for_read(:list_active_bonuses)
      |> apply_sorting(options)
      |> maybe_load_associations(options)
      |> Ash.read()
      
      case result do
        {:ok, bonuses} ->
          Logger.info("✅ [Free Bonus仓储] 获取启用Free Bonus成功: #{length(bonuses)}条")
          {:ok, bonuses}
          
        {:error, reason} ->
          Logger.error("❌ [Free Bonus仓储] 获取启用Free Bonus失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [Free Bonus仓储] 获取启用Free Bonus异常: #{inspect(exception)}")
        {:error, :query_failed}
    end
  end

  @doc """
  根据游戏类型获取Free Bonus列表
  """
  def list_free_bonuses_by_game(game_type, options \\ []) do
    Logger.info("🎁 [Free Bonus仓储] 根据游戏类型获取Free Bonus列表: #{game_type}")
    
    try do
      result = FreeBonus
      |> Ash.Query.for_read(:list_by_game, %{game_type: game_type})
      |> apply_sorting(options)
      |> maybe_load_associations(options)
      |> Ash.read()
      
      case result do
        {:ok, bonuses} ->
          Logger.info("✅ [Free Bonus仓储] 获取游戏类型Free Bonus成功: #{length(bonuses)}条")
          {:ok, bonuses}
          
        {:error, reason} ->
          Logger.error("❌ [Free Bonus仓储] 获取游戏类型Free Bonus失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [Free Bonus仓储] 获取游戏类型Free Bonus异常: #{inspect(exception)}")
        {:error, :query_failed}
    end
  end

  # ==================== 状态管理操作 ====================

  @doc """
  启用Free Bonus
  """
  def enable_free_bonus(bonus_id) do
    Logger.info("🎁 [Free Bonus仓储] 启用Free Bonus: #{bonus_id}")
    
    try do
      with {:ok, bonus} <- get_free_bonus(bonus_id),
           {:ok, updated_bonus} <- FreeBonus.enable(bonus) do
        Logger.info("✅ [Free Bonus仓储] Free Bonus启用成功: #{updated_bonus.bonus_name}")
        {:ok, updated_bonus}
      else
        {:error, reason} ->
          Logger.error("❌ [Free Bonus仓储] 启用Free Bonus失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [Free Bonus仓储] 启用Free Bonus异常: #{inspect(exception)}")
        {:error, :enable_failed}
    end
  end

  @doc """
  禁用Free Bonus
  """
  def disable_free_bonus(bonus_id) do
    Logger.info("🎁 [Free Bonus仓储] 禁用Free Bonus: #{bonus_id}")
    
    try do
      with {:ok, bonus} <- get_free_bonus(bonus_id),
           {:ok, updated_bonus} <- FreeBonus.disable(bonus) do
        Logger.info("✅ [Free Bonus仓储] Free Bonus禁用成功: #{updated_bonus.bonus_name}")
        {:ok, updated_bonus}
      else
        {:error, reason} ->
          Logger.error("❌ [Free Bonus仓储] 禁用Free Bonus失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [Free Bonus仓储] 禁用Free Bonus异常: #{inspect(exception)}")
        {:error, :disable_failed}
    end
  end

  # ==================== 私有辅助函数 ====================

  # 应用列表过滤条件
  defp apply_list_filters(query, options) do
    query
    |> maybe_filter_by_name(options[:name])
    |> maybe_filter_by_game_type(options[:game_type])
    |> maybe_filter_by_status(options[:status])
    |> maybe_filter_by_level(options[:level])
    |> maybe_filter_by_vip_level(options[:vip_level])
  end

  # 按Bonus名称过滤
  defp maybe_filter_by_name(query, nil), do: query
  defp maybe_filter_by_name(query, name) when is_binary(name) do
    Ash.Query.filter(query, contains(bonus_name, ^name))
  end

  # 按游戏类型过滤
  defp maybe_filter_by_game_type(query, nil), do: query
  defp maybe_filter_by_game_type(query, game_type) when is_binary(game_type) do
    Ash.Query.filter(query, game_type == ^game_type)
  end

  # 按状态过滤
  defp maybe_filter_by_status(query, nil), do: query
  defp maybe_filter_by_status(query, status) when is_integer(status) do
    Ash.Query.filter(query, status == ^status)
  end

  # 按等级过滤
  defp maybe_filter_by_level(query, nil), do: query
  defp maybe_filter_by_level(query, level) when is_integer(level) do
    Ash.Query.filter(query, min_level <= ^level)
  end

  # 按VIP等级过滤
  defp maybe_filter_by_vip_level(query, nil), do: query
  defp maybe_filter_by_vip_level(query, vip_level) when is_integer(vip_level) do
    Ash.Query.filter(query, is_nil(vip_level) or vip_level <= ^vip_level)
  end

  # 应用排序
  defp apply_sorting(query, options) do
    case options[:sort] do
      nil -> Ash.Query.sort(query, priority: :desc, inserted_at: :desc)
      sort_params -> Ash.Query.sort(query, sort_params)
    end
  end

  # 构建分页参数
  defp build_page_params(options) do
    %{
      limit: options[:limit] || 20,
      offset: options[:offset] || 0,
      count: true
    }
  end

  # 可能加载关联数据
  defp maybe_load_associations({:ok, data}, options) do
    case options[:load] do
      nil -> {:ok, data}
      associations -> 
        case Ash.load(data, associations) do
          {:ok, loaded_data} -> {:ok, loaded_data}
          {:error, reason} -> {:error, reason}
        end
    end
  end

  defp maybe_load_associations(query, options) do
    case options[:load] do
      nil -> query
      associations -> Ash.Query.load(query, associations)
    end
  end
end
