defmodule RacingGame.Live.AdminPanel.SystemNotificationsComponent do
  @moduledoc """
  系统通知管理组件 - 仅限管理员使用
  """
  use CypridinaWeb, :live_component

  alias CypridinaWeb.AuthHelper
  alias Cypridina.Accounts.User
  alias <PERSON><PERSON>ridina.Utils.TimeHelper
  require Ash.Query
  require Logger

  def update(assigns, socket) do
    socket = assign(socket, assigns)

    if AuthHelper.has_permission?(socket.assigns.current_user, :admin) do
      socket =
        socket
        |> assign_defaults()
        |> load_notifications()

      {:ok, socket}
    else
      # 非管理员用户显示无权限信息
      socket =
        socket
        |> assign_defaults()
        |> assign(:unauthorized, true)
      {:ok, socket}
    end
  end

  defp assign_defaults(socket) do
    socket
    |> assign(:notifications, [])
    |> assign(:show_create_modal, false)
    |> assign(:show_edit_modal, false)
    |> assign(:current_notification, nil)
    |> assign(:form, %{title: "", content: "", type: "info", active: true, user_id: nil})
    |> assign(:page, 1)
    |> assign(:per_page, 20)
    |> assign(:total_count, 0)
    |> assign(:search_query, "")
    |> assign(:unauthorized, false)
  end

  defp load_notifications(socket) do
    # 使用模拟数据（实际项目中应该从数据库获取）
    notifications = [
      %{
        id: 1,
        title: "系统维护通知",
        content: "系统将于今晚22:00-24:00进行维护，期间可能无法正常使用。",
        type: "warning",
        active: true,
        user_id: nil,
        created_by: "admin",
        inserted_at: ~N[2024-01-15 10:30:00],
        updated_at: ~N[2024-01-15 10:30:00]
      },
      %{
        id: 2,
        title: "新功能上线",
        content: "新增了用户管理和系统监控功能，欢迎体验！",
        type: "info",
        active: true,
        user_id: nil,
        created_by: "admin",
        inserted_at: ~N[2024-01-14 15:20:00],
        updated_at: ~N[2024-01-14 15:20:00]
      },
      %{
        id: 3,
        title: "重要安全更新",
        content: "已修复若干安全漏洞，建议所有用户及时更新密码。",
        type: "error",
        active: false,
        user_id: 123,
        created_by: "admin",
        inserted_at: ~N[2024-01-10 09:15:00],
        updated_at: ~N[2024-01-10 09:15:00]
      }
    ]

    # 如果有搜索条件，进行过滤
    filtered_notifications = if socket.assigns.search_query != "" do
      Enum.filter(notifications, fn notification ->
        String.contains?(String.downcase(notification.title), String.downcase(socket.assigns.search_query)) or
        String.contains?(String.downcase(notification.content), String.downcase(socket.assigns.search_query))
      end)
    else
      notifications
    end

    # 分页处理
    total_count = length(filtered_notifications)
    page = socket.assigns.page
    per_page = socket.assigns.per_page
    start_index = (page - 1) * per_page

    paged_notifications =
      filtered_notifications
      |> Enum.drop(start_index)
      |> Enum.take(per_page)

    socket
    |> assign(:notifications, paged_notifications)
    |> assign(:total_count, total_count)
  end

  def handle_event("show_create_modal", _params, socket) do
    socket =
      socket
      |> assign(:show_create_modal, true)
      |> assign(:form, %{title: "", content: "", type: "info", active: true, user_id: nil})
    {:noreply, socket}
  end

  def handle_event("hide_create_modal", _params, socket) do
    socket = assign(socket, :show_create_modal, false)
    {:noreply, socket}
  end

  def handle_event("search", %{"search" => %{"query" => query}}, socket) do
    socket =
      socket
      |> assign(:search_query, query)
      |> assign(:page, 1)
      |> load_notifications()
    {:noreply, socket}
  end

  def handle_event("show_edit_modal", %{"id" => id}, socket) do
    case Enum.find(socket.assigns.notifications, &(&1.id == String.to_integer(id))) do
      notification ->
        form = %{
          title: notification.title,
          content: notification.content,
          type: notification.type,
          active: notification.active,
          user_id: notification.user_id
        }
        socket =
          socket
          |> assign(:show_edit_modal, true)
          |> assign(:current_notification, notification)
          |> assign(:form, form)
        {:noreply, socket}

      nil ->
        socket = put_flash(socket, :error, "找不到指定的通知")
        {:noreply, socket}
    end
  end

  def handle_event("save_notification", %{"notification" => params}, socket) do
    # 模拟保存操作（实际项目中应该保存到数据库）
    is_update = socket.assigns.current_notification != nil

    socket =
      socket
      |> assign(:show_create_modal, false)
      |> assign(:show_edit_modal, false)
      |> assign(:current_notification, nil)
      |> load_notifications()
      |> put_flash(:info, if(is_update, do: "通知更新成功", else: "通知创建成功"))

    {:noreply, socket}
  end

  def handle_event("delete_notification", %{"id" => id}, socket) do
    # 模拟删除操作（实际项目中应该从数据库删除）
    socket =
      socket
      |> load_notifications()
      |> put_flash(:info, "通知删除成功")

    {:noreply, socket}
  end

  def handle_event("page_change", %{"page" => page}, socket) do
    socket =
      socket
      |> assign(:page, String.to_integer(page))
      |> load_notifications()
    {:noreply, socket}
  end

  def handle_event("refresh", _params, socket) do
    socket = load_notifications(socket)
    {:noreply, socket}
  end

  # 格式化时间显示
  defp format_time(nil), do: "-"
  defp format_time(datetime) do
    TimeHelper.format_local_datetime(datetime)
  end

  # 获取通知类型的显示文本
  defp notification_type_label(type) do
    case type do
      "info" -> "信息"
      "warning" -> "警告"
      "error" -> "错误"
      "success" -> "成功"
      _ -> "未知"
    end
  end

  # 获取通知类型的样式类
  defp notification_type_class(type) do
    case type do
      "info" -> "px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800"
      "warning" -> "px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800"
      "error" -> "px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800"
      "success" -> "px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800"
      _ -> "px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800"
    end
  end

  def render(assigns) do
    ~H"""
    <div class="p-6 bg-white rounded-lg shadow-md">
      <%= if @unauthorized do %>
        <h3 class="text-xl font-bold text-red-600 mb-4">权限不足</h3>
        <p class="text-gray-700">您没有访问系统通知管理的权限。只有管理员可以查看此页面。</p>
      <% else %>
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-xl font-bold text-gray-800">系统通知管理</h3>
          <div class="flex gap-3">
            <button
              phx-click="show_create_modal"
              phx-target={@myself}
              class="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors"
            >
              <i class="fas fa-plus mr-2"></i>新建通知
            </button>
            <button
              phx-click="refresh"
              phx-target={@myself}
              class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
            >
              <i class="fas fa-sync-alt mr-2"></i>刷新
            </button>
          </div>
        </div>

        <!-- 搜索栏 -->
        <div class="mb-4">
          <form phx-submit="search" phx-target={@myself} class="flex gap-2">
            <input
              type="text"
              name="search[query]"
              value={@search_query}
              placeholder="搜索通知标题或内容..."
              class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <button
              type="submit"
              class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"
            >
              <i class="fas fa-search"></i>
            </button>
          </form>
        </div>

        <!-- 通知列表 -->
        <div class="overflow-x-auto">
          <table class="min-w-full bg-white border border-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  标题
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  内容
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  类型
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  目标用户
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  状态
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  创建时间
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  操作
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <%= if Enum.empty?(@notifications) do %>
                <tr>
                  <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                    暂无系统通知
                  </td>
                </tr>
              <% else %>
                <%= for notification <- @notifications do %>
                  <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      <%= notification.title %>
                    </td>
                    <td class="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">
                      <%= notification.content %>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class={notification_type_class(notification.type)}>
                        <%= notification_type_label(notification.type) %>
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <%= if notification.user_id do %>
                        用户 #<%= notification.user_id %>
                      <% else %>
                        全体用户
                      <% end %>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <%= if notification.active do %>
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                          启用
                        </span>
                      <% else %>
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                          禁用
                        </span>
                      <% end %>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <%= format_time(notification.inserted_at) %>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div class="flex space-x-2">
                        <button
                          phx-click="show_edit_modal"
                          phx-value-id={notification.id}
                          phx-target={@myself}
                          class="text-indigo-600 hover:text-indigo-900"
                        >
                          编辑
                        </button>
                        <button
                          phx-click="delete_notification"
                          phx-value-id={notification.id}
                          phx-target={@myself}
                          data-confirm="确定要删除这条通知吗？"
                          class="text-red-600 hover:text-red-900"
                        >
                          删除
                        </button>
                      </div>
                    </td>
                  </tr>
                <% end %>
              <% end %>
            </tbody>
          </table>
        </div>

        <!-- 分页控制 -->
        <%= if @total_count > @per_page do %>
          <div class="mt-4 flex justify-between items-center">
            <div class="text-sm text-gray-700">
              显示第 <%= (@page - 1) * @per_page + 1 %> - <%= min(@page * @per_page, @total_count) %> 条，共 <%= @total_count %> 条记录
            </div>
            <div class="flex gap-2">
              <%= if @page > 1 do %>
                <button
                  phx-click="page_change"
                  phx-value-page={@page - 1}
                  phx-target={@myself}
                  class="px-3 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
                >
                  上一页
                </button>
              <% end %>
              <span class="px-3 py-1 bg-blue-500 text-white rounded">
                <%= @page %>
              </span>
              <%= if @page * @per_page < @total_count do %>
                <button
                  phx-click="page_change"
                  phx-value-page={@page + 1}
                  phx-target={@myself}
                  class="px-3 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
                >
                  下一页
                </button>
              <% end %>
            </div>
          </div>
        <% end %>

        <!-- 创建/编辑通知模态框 -->
        <%= if (@show_create_modal || false) or (@show_edit_modal || false) do %>
          <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg p-6 w-full max-w-2xl">
              <h3 class="text-lg font-bold text-gray-800 mb-4">
                <%= if @show_edit_modal, do: "编辑通知", else: "新建通知" %>
              </h3>

              <form phx-submit="save_notification" phx-target={@myself}>
                <div class="mb-4">
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    通知标题
                  </label>
                  <input
                    type="text"
                    name="notification[title]"
                    value={@form.title}
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>

                <div class="mb-4">
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    通知内容
                  </label>
                  <textarea
                    name="notification[content]"
                    rows="4"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  ><%= @form.content %></textarea>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      通知类型
                    </label>
                    <select
                      name="notification[type]"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="info" selected={@form.type == "info"}>信息</option>
                      <option value="warning" selected={@form.type == "warning"}>警告</option>
                      <option value="error" selected={@form.type == "error"}>错误</option>
                      <option value="success" selected={@form.type == "success"}>成功</option>
                    </select>
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      目标用户ID（可选）
                    </label>
                    <input
                      type="text"
                      name="notification[user_id]"
                      value={@form.user_id}
                      placeholder="留空表示全体用户"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>

                <div class="mb-4">
                  <label class="flex items-center">
                    <input
                      type="checkbox"
                      name="notification[active]"
                      value="true"
                      checked={@form.active}
                      class="mr-2"
                    />
                    <span class="text-sm text-gray-700">启用通知</span>
                  </label>
                </div>

                <div class="flex justify-end space-x-4 pt-4 border-t border-gray-200">
                  <button
                    type="button"
                    phx-click="hide_create_modal"
                    phx-target={@myself}
                    class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-400 transition-colors duration-200 font-medium"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    class="px-6 py-2 border border-blue-600 bg-blue-600 text-white rounded-md hover:bg-blue-700 hover:border-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200 font-medium shadow-sm"
                  >
                    保存
                  </button>
                </div>
              </form>
            </div>
          </div>
        <% end %>
      <% end %>
    </div>
    """
  end
end