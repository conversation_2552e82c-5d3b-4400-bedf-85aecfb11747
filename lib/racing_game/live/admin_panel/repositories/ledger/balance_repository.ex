defmodule RacingGame.Live.AdminPanel.Repositories.Ledger.BalanceRepository do
  @moduledoc """
  余额数据访问仓储
  
  提供余额相关的数据访问抽象：
  - 余额查询和管理
  - 余额历史追踪
  - 余额统计分析
  - 余额快照管理
  """

  require Logger
  alias Cypridina.Ledger.{Balance, Account}
  alias RacingGame.Live.AdminPanel.Repositories.QueryBuilders.LedgerQueryBuilder
  require Ash.Query

  # 常量定义
  @cache_ttl 300  # 5分钟缓存
  @default_limit 20
  @max_limit 100

  # ============================================================================
  # 余额基础查询
  # ============================================================================

  @doc """
  根据ID获取余额记录

  ## 参数
  - `balance_id` - 余额ID
  - `options` - 选项

  ## 返回
  - `{:ok, balance}` - 成功
  - `{:error, :not_found}` - 余额不存在
  - `{:error, reason}` - 其他错误
  """
  def get_balance_by_id(balance_id, options \\ []) do
    Logger.debug("🔍 [余额仓储] 根据ID获取余额: #{balance_id}")
    
    preload = Keyword.get(options, :preload, [])
    use_cache = Keyword.get(options, :use_cache, true)
    
    cache_key = build_cache_key("balance", balance_id, preload)
    
    if use_cache do
      case get_from_cache(cache_key) do
        {:ok, balance} -> {:ok, balance}
        :miss -> fetch_and_cache_balance_by_id(balance_id, preload, cache_key)
      end
    else
      fetch_balance_by_id(balance_id, preload)
    end
  end

  @doc """
  获取账户当前余额

  ## 参数
  - `account_id` - 账户ID
  - `options` - 选项

  ## 返回
  - `{:ok, balance}` - 成功
  - `{:error, :not_found}` - 账户不存在
  - `{:error, reason}` - 其他错误
  """
  def get_current_balance(account_id, options \\ []) do
    Logger.debug("💰 [余额仓储] 获取当前余额: #{account_id}")
    
    try do
      query = Balance
      |> Ash.Query.filter(account_id == ^account_id)
      |> Ash.Query.sort([inserted_at: :desc])
      |> Ash.Query.limit(1)
      |> maybe_preload(options[:preload])
      
      case Ash.read_one(query) do
        {:ok, balance} when not is_nil(balance) -> {:ok, balance}
        {:ok, nil} -> {:error, :not_found}
        {:error, error} ->
          Logger.error("❌ [余额仓储] 获取当前余额失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [余额仓储] 获取当前余额异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  获取用户当前余额

  ## 参数
  - `user_id` - 用户ID
  - `currency` - 货币类型
  - `options` - 选项

  ## 返回
  - `{:ok, balance}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_current_balance(user_id, currency \\ :XAA, options \\ []) do
    Logger.debug("👤 [余额仓储] 获取用户当前余额: #{user_id} - #{currency}")
    
    case get_user_account_id(user_id, currency) do
      {:ok, account_id} -> get_current_balance(account_id, options)
      error -> error
    end
  end

  @doc """
  获取账户余额历史

  ## 参数
  - `account_id` - 账户ID
  - `options` - 选项

  ## 返回
  - `{:ok, balances}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_balance_history(account_id, options \\ []) do
    Logger.debug("📊 [余额仓储] 获取余额历史: #{account_id}")
    
    try do
      query = Balance
      |> Ash.Query.filter(account_id == ^account_id)
      |> maybe_preload(options[:preload])
      |> apply_sort(options[:sort] || [inserted_at: :desc])
      |> maybe_limit(options[:limit])
      |> maybe_date_range(options[:date_range])
      
      case Ash.read(query) do
        {:ok, balances} -> {:ok, balances}
        {:error, error} ->
          Logger.error("❌ [余额仓储] 获取余额历史失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [余额仓储] 获取余额历史异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  分页查询余额列表

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, %{balances: balances, page_info: page_info}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_balances_paginated(params \\ %{}) do
    Logger.debug("📋 [余额仓储] 分页查询余额列表")
    
    try do
      normalized_params = normalize_list_params(params)
      
      with {:ok, query} <- LedgerQueryBuilder.build_balance_list_query(normalized_params),
           {:ok, results} <- execute_paginated_query(query, normalized_params) do
        {:ok, %{balances: results.items, page_info: results.page_info}}
      else
        {:error, reason} -> {:error, reason}
      end
    rescue
      error ->
        Logger.error("❌ [余额仓储] 分页查询异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  获取指定时间点的余额

  ## 参数
  - `account_id` - 账户ID
  - `timestamp` - 时间点
  - `options` - 选项

  ## 返回
  - `{:ok, balance}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_balance_at_time(account_id, timestamp, options \\ []) do
    Logger.debug("⏰ [余额仓储] 获取指定时间余额: #{account_id} @ #{timestamp}")
    
    try do
      query = Balance
      |> Ash.Query.filter(account_id == ^account_id and inserted_at <= ^timestamp)
      |> Ash.Query.sort([inserted_at: :desc])
      |> Ash.Query.limit(1)
      |> maybe_preload(options[:preload])
      
      case Ash.read_one(query) do
        {:ok, balance} when not is_nil(balance) -> {:ok, balance}
        {:ok, nil} -> {:error, :not_found}
        {:error, error} ->
          Logger.error("❌ [余额仓储] 获取指定时间余额失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [余额仓储] 获取指定时间余额异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # ============================================================================
  # 余额创建和管理
  # ============================================================================

  @doc """
  创建余额记录

  ## 参数
  - `balance_data` - 余额数据

  ## 返回
  - `{:ok, balance}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_balance(balance_data) do
    Logger.info("➕ [余额仓储] 创建余额记录: #{balance_data[:account_id]}")
    
    try do
      case Balance.create(balance_data) do
        {:ok, balance} ->
          clear_cache(:balance, balance_data[:account_id])
          Logger.info("✅ [余额仓储] 余额记录创建成功: #{balance.id}")
          {:ok, balance}
        {:error, error} ->
          Logger.error("❌ [余额仓储] 创建余额记录失败: #{inspect(error)}")
          {:error, :create_failed}
      end
    rescue
      error ->
        Logger.error("❌ [余额仓储] 创建余额记录异常: #{inspect(error)}")
        {:error, :create_error}
    end
  end

  @doc """
  批量创建余额记录

  ## 参数
  - `balances_data` - 余额数据列表

  ## 返回
  - `{:ok, balances}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_balances_batch(balances_data) do
    Logger.info("📦 [余额仓储] 批量创建余额记录: #{length(balances_data)} 条")
    
    try do
      results = Enum.map(balances_data, &create_balance/1)
      
      case Enum.split_with(results, &match?({:ok, _}, &1)) do
        {successes, []} ->
          balances = Enum.map(successes, fn {:ok, balance} -> balance end)
          Logger.info("✅ [余额仓储] 批量创建全部成功: #{length(balances)} 条")
          {:ok, balances}
        {successes, failures} ->
          Logger.warn("⚠️ [余额仓储] 批量创建部分失败: 成功 #{length(successes)}, 失败 #{length(failures)}")
          {:error, {:partial_failure, successes, failures}}
      end
    rescue
      error ->
        Logger.error("❌ [余额仓储] 批量创建异常: #{inspect(error)}")
        {:error, :batch_error}
    end
  end

  @doc """
  更新余额记录

  ## 参数
  - `balance_id` - 余额ID
  - `update_data` - 更新数据

  ## 返回
  - `{:ok, balance}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_balance(balance_id, update_data) do
    Logger.info("✏️ [余额仓储] 更新余额记录: #{balance_id}")
    
    with {:ok, balance} <- get_balance_by_id(balance_id, use_cache: false),
         {:ok, updated_balance} <- do_update_balance(balance, update_data) do
      
      clear_cache(:balance, balance.account_id)
      Logger.info("✅ [余额仓储] 余额记录更新成功: #{balance_id}")
      {:ok, updated_balance}
    else
      error -> error
    end
  end

  @doc """
  删除余额记录

  ## 参数
  - `balance_id` - 余额ID

  ## 返回
  - `:ok` - 成功
  - `{:error, reason}` - 失败
  """
  def delete_balance(balance_id) do
    Logger.info("🗑️ [余额仓储] 删除余额记录: #{balance_id}")
    
    with {:ok, balance} <- get_balance_by_id(balance_id),
         :ok <- validate_balance_deletion(balance),
         :ok <- do_delete_balance(balance) do
      
      clear_cache(:balance, balance.account_id)
      Logger.info("✅ [余额仓储] 余额记录删除成功: #{balance_id}")
      :ok
    else
      error -> error
    end
  end

  # ============================================================================
  # 余额统计
  # ============================================================================

  @doc """
  获取余额统计信息

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, statistics}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_balance_statistics(options \\ []) do
    Logger.debug("📊 [余额仓储] 获取余额统计")
    
    try do
      stats = %{
        total_balances: get_total_balances(),
        balances_by_currency: get_balances_by_currency(),
        total_value: get_total_value(),
        positive_balances: get_positive_balances_count(),
        zero_balances: get_zero_balances_count(),
        negative_balances: get_negative_balances_count(),
        recent_changes: get_recent_balance_changes(options[:days] || 7)
      }
      
      {:ok, stats}
    rescue
      error ->
        Logger.error("❌ [余额仓储] 获取统计异常: #{inspect(error)}")
        {:error, :statistics_error}
    end
  end

  @doc """
  获取账户余额趋势

  ## 参数
  - `account_id` - 账户ID
  - `options` - 选项

  ## 返回
  - `{:ok, trend_data}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_balance_trend(account_id, options \\ []) do
    Logger.debug("📈 [余额仓储] 获取余额趋势: #{account_id}")
    
    days = options[:days] || 30
    interval = options[:interval] || :daily
    
    try do
      case get_balance_history(account_id, limit: days * 24) do
        {:ok, balances} ->
          trend_data = calculate_balance_trend(balances, interval)
          {:ok, trend_data}
        error -> error
      end
    rescue
      error ->
        Logger.error("❌ [余额仓储] 获取余额趋势异常: #{inspect(error)}")
        {:error, :trend_error}
    end
  end

  # ============================================================================
  # 缓存管理
  # ============================================================================

  @doc """
  清除余额相关缓存

  ## 参数
  - `cache_type` - 缓存类型
  - `account_id` - 账户ID（可选）
  """
  def clear_cache(cache_type, account_id \\ nil) do
    cache_patterns = case {cache_type, account_id} do
      {:balance, nil} -> ["balance:*"]
      {:balance, account_id} -> ["balance:#{account_id}:*", "balance:account:#{account_id}:*"]
      {:all, _} -> ["balance:*"]
    end
    
    Logger.debug("🧹 [余额仓储] 清除缓存: #{inspect(cache_patterns)}")
    Enum.each(cache_patterns, &clear_cache_pattern/1)
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 标准化列表参数
  defp normalize_list_params(params) do
    %{
      page: max(1, params[:page] || 1),
      limit: min(params[:limit] || @default_limit, @max_limit),
      search: String.trim(params[:search] || ""),
      filters: params[:filters] || %{},
      sort: params[:sort] || [inserted_at: :desc],
      preload: params[:preload] || []
    }
  end

  # 获取并缓存余额
  defp fetch_and_cache_balance_by_id(balance_id, preload, cache_key) do
    case fetch_balance_by_id(balance_id, preload) do
      {:ok, balance} ->
        cache_put(cache_key, balance, @cache_ttl)
        {:ok, balance}
      error -> error
    end
  end

  # 获取余额
  defp fetch_balance_by_id(balance_id, preload) do
    try do
      case Balance
           |> maybe_preload(preload)
           |> Ash.get(balance_id) do
        {:ok, balance} -> {:ok, balance}
        {:error, %Ash.Error.Query.NotFound{}} -> {:error, :not_found}
        {:error, error} ->
          Logger.error("❌ [余额仓储] 获取余额失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [余额仓储] 获取余额异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # 获取用户账户ID
  defp get_user_account_id(user_id, currency) do
    identifier = "user:#{currency}:#{user_id}"
    
    case Account
         |> Ash.Query.filter(identifier == ^identifier)
         |> Ash.Query.select([:id])
         |> Ash.read_one() do
      {:ok, %{id: account_id}} -> {:ok, account_id}
      {:ok, nil} -> {:error, :account_not_found}
      {:error, error} ->
        Logger.error("❌ [余额仓储] 获取用户账户失败: #{inspect(error)}")
        {:error, :account_query_failed}
    end
  end

  # 执行更新余额
  defp do_update_balance(balance, update_data) do
    try do
      case balance
           |> Ash.Changeset.for_update(:update, update_data)
           |> Ash.update() do
        {:ok, updated_balance} -> {:ok, updated_balance}
        {:error, error} ->
          Logger.error("❌ [余额仓储] 更新余额失败: #{inspect(error)}")
          {:error, :update_failed}
      end
    rescue
      error ->
        Logger.error("❌ [余额仓储] 更新余额异常: #{inspect(error)}")
        {:error, :update_error}
    end
  end

  # 验证余额删除
  defp validate_balance_deletion(_balance) do
    # 这里可以添加删除验证逻辑
    :ok
  end

  # 执行删除余额
  defp do_delete_balance(balance) do
    try do
      case Ash.destroy(balance) do
        :ok -> :ok
        {:error, error} ->
          Logger.error("❌ [余额仓储] 删除余额失败: #{inspect(error)}")
          {:error, :delete_failed}
      end
    rescue
      error ->
        Logger.error("❌ [余额仓储] 删除余额异常: #{inspect(error)}")
        {:error, :delete_error}
    end
  end

  # 执行分页查询
  defp execute_paginated_query(query, params) do
    offset = (params.page - 1) * params.limit
    
    case query
         |> maybe_preload(params.preload)
         |> Ash.Query.page(count: true, limit: params.limit, offset: offset)
         |> Ash.read() do
      {:ok, %Ash.Page.Offset{results: items, count: total_count}} ->
        page_info = %{
          total_count: total_count,
          page: params.page,
          limit: params.limit,
          total_pages: ceil(total_count / params.limit)
        }
        {:ok, %{items: items, page_info: page_info}}
      {:error, error} ->
        Logger.error("❌ [余额仓储] 分页查询失败: #{inspect(error)}")
        {:error, :query_failed}
    end
  end

  # 可能预加载关联数据
  defp maybe_preload(query, []), do: query
  defp maybe_preload(query, preload) when is_list(preload) do
    Ash.Query.load(query, preload)
  end

  # 可能限制数量
  defp maybe_limit(query, nil), do: query
  defp maybe_limit(query, limit) when is_integer(limit) and limit > 0 do
    Ash.Query.limit(query, limit)
  end
  defp maybe_limit(query, _), do: query

  # 应用排序
  defp apply_sort(query, sort) when is_list(sort) do
    Ash.Query.sort(query, sort)
  end
  defp apply_sort(query, _), do: query

  # 可能应用日期范围
  defp maybe_date_range(query, nil), do: query
  defp maybe_date_range(query, {start_date, end_date}) do
    query
    |> Ash.Query.filter(inserted_at >= ^start_date and inserted_at <= ^end_date)
  end
  defp maybe_date_range(query, _), do: query

  # 统计函数（简化实现）
  defp get_total_balances do
    case Balance |> Ash.Query.aggregate(:count, :id) |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_balances_by_currency do
    # 这里需要根据实际的货币分组逻辑实现
    %{}
  end

  defp get_total_value do
    # 这里需要根据实际的总价值计算逻辑实现
    Money.new(0, :XAA)
  end

  defp get_positive_balances_count do
    # 这里需要根据实际的正余额统计逻辑实现
    0
  end

  defp get_zero_balances_count do
    # 这里需要根据实际的零余额统计逻辑实现
    0
  end

  defp get_negative_balances_count do
    # 这里需要根据实际的负余额统计逻辑实现
    0
  end

  defp get_recent_balance_changes(days) do
    date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
    
    case Balance 
         |> Ash.Query.filter(inserted_at >= ^date_threshold)
         |> Ash.Query.aggregate(:count, :id) 
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  # 计算余额趋势
  defp calculate_balance_trend(balances, interval) do
    # 这里需要根据实际的趋势计算逻辑实现
    # 简化实现，返回空列表
    []
  end

  # 构建缓存键
  defp build_cache_key(prefix, id, extra \\ nil) do
    case extra do
      nil -> "#{prefix}:#{id}"
      extra -> "#{prefix}:#{id}:#{:erlang.phash2(extra)}"
    end
  end

  # 缓存操作（需要实现具体的缓存后端）
  defp get_from_cache(_key), do: :miss
  defp cache_put(_key, _value, _ttl), do: :ok
  defp clear_cache_pattern(_pattern), do: :ok
end
