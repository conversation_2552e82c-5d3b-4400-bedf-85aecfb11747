/* Racing Game Admin Panel - 响应式设计系统 */

/* ========================================
   响应式断点系统
   ======================================== */

:root {
  /* 断点定义 */
  --breakpoint-xs: 475px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;

  /* 容器最大宽度 */
  --container-xs: 100%;
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;

  /* 响应式间距 */
  --spacing-responsive-xs: 0.5rem;
  --spacing-responsive-sm: 1rem;
  --spacing-responsive-md: 1.5rem;
  --spacing-responsive-lg: 2rem;
  --spacing-responsive-xl: 3rem;
}

/* ========================================
   移动端优先的基础样式
   ======================================== */

/* 基础容器 */
.container-responsive {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--spacing-responsive-xs);
  padding-right: var(--spacing-responsive-xs);
}

/* 响应式网格系统 */
.grid-responsive {
  display: grid;
  gap: var(--spacing-responsive-xs);
  grid-template-columns: 1fr;
}

/* 响应式Flex布局 */
.flex-responsive {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-responsive-xs);
}

/* ========================================
   小屏幕 (xs: 475px+)
   ======================================== */

@media (min-width: 475px) {
  .container-responsive {
    max-width: var(--container-xs);
    padding-left: var(--spacing-responsive-sm);
    padding-right: var(--spacing-responsive-sm);
  }

  .grid-responsive {
    gap: var(--spacing-responsive-sm);
  }

  .flex-responsive {
    gap: var(--spacing-responsive-sm);
  }

  /* 网格列数 */
  .grid-cols-xs-2 {
    grid-template-columns: repeat(2, 1fr);
  }

  /* 文字大小调整 */
  .text-responsive {
    font-size: 0.875rem;
  }

  /* 按钮大小调整 */
  .btn-responsive {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
}

/* ========================================
   小屏幕 (sm: 640px+)
   ======================================== */

@media (min-width: 640px) {
  .container-responsive {
    max-width: var(--container-sm);
    padding-left: var(--spacing-responsive-md);
    padding-right: var(--spacing-responsive-md);
  }

  .grid-responsive {
    gap: var(--spacing-responsive-md);
  }

  .flex-responsive {
    flex-direction: row;
    gap: var(--spacing-responsive-md);
  }

  /* 网格系统 */
  .grid-cols-sm-2 {
    grid-template-columns: repeat(2, 1fr);
  }

  .grid-cols-sm-3 {
    grid-template-columns: repeat(3, 1fr);
  }

  /* 管理面板布局 */
  .admin-layout {
    display: grid;
    grid-template-columns: 250px 1fr;
    min-height: 100vh;
  }

  .admin-sidebar {
    position: static;
    transform: translateX(0);
    width: 250px;
  }

  .admin-main {
    overflow-x: auto;
  }

  /* 隐藏移动端导航 */
  .mobile-nav {
    display: none;
  }

  /* 表格响应式 */
  .table-responsive {
    display: table;
  }

  .table-responsive tbody,
  .table-responsive tr,
  .table-responsive td {
    display: table-row;
    display: table-cell;
  }

  .table-responsive td:before {
    display: none;
  }
}

/* ========================================
   中等屏幕 (md: 768px+)
   ======================================== */

@media (min-width: 768px) {
  .container-responsive {
    max-width: var(--container-md);
  }

  /* 网格系统 */
  .grid-cols-md-2 {
    grid-template-columns: repeat(2, 1fr);
  }

  .grid-cols-md-3 {
    grid-template-columns: repeat(3, 1fr);
  }

  .grid-cols-md-4 {
    grid-template-columns: repeat(4, 1fr);
  }

  /* 卡片布局优化 */
  .card-responsive {
    padding: 1.5rem;
  }

  /* 统计卡片网格 */
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  /* 表单布局 */
  .form-responsive {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .form-responsive .form-control-full {
    grid-column: 1 / -1;
  }

  /* 模态框大小调整 */
  .modal-responsive .modal-box {
    max-width: 32rem;
  }
}

/* ========================================
   大屏幕 (lg: 1024px+)
   ======================================== */

@media (min-width: 1024px) {
  .container-responsive {
    max-width: var(--container-lg);
    padding-left: var(--spacing-responsive-lg);
    padding-right: var(--spacing-responsive-lg);
  }

  .grid-responsive {
    gap: var(--spacing-responsive-lg);
  }

  /* 网格系统 */
  .grid-cols-lg-3 {
    grid-template-columns: repeat(3, 1fr);
  }

  .grid-cols-lg-4 {
    grid-template-columns: repeat(4, 1fr);
  }

  .grid-cols-lg-5 {
    grid-template-columns: repeat(5, 1fr);
  }

  /* 管理面板布局优化 */
  .admin-layout {
    grid-template-columns: 280px 1fr;
  }

  .admin-sidebar {
    width: 280px;
  }

  /* 统计卡片网格 */
  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  /* 表单布局 */
  .form-responsive {
    grid-template-columns: repeat(3, 1fr);
  }

  /* 卡片网格 */
  .card-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }

  /* 模态框大小调整 */
  .modal-responsive .modal-box {
    max-width: 42rem;
  }
}

/* ========================================
   超大屏幕 (xl: 1280px+)
   ======================================== */

@media (min-width: 1280px) {
  .container-responsive {
    max-width: var(--container-xl);
    padding-left: var(--spacing-responsive-xl);
    padding-right: var(--spacing-responsive-xl);
  }

  /* 网格系统 */
  .grid-cols-xl-4 {
    grid-template-columns: repeat(4, 1fr);
  }

  .grid-cols-xl-5 {
    grid-template-columns: repeat(5, 1fr);
  }

  .grid-cols-xl-6 {
    grid-template-columns: repeat(6, 1fr);
  }

  /* 统计卡片网格 */
  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  /* 卡片网格 */
  .card-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  /* 表单布局 */
  .form-responsive {
    grid-template-columns: repeat(4, 1fr);
  }

  /* 模态框大小调整 */
  .modal-responsive .modal-box {
    max-width: 48rem;
  }
}

/* ========================================
   2XL屏幕 (2xl: 1536px+)
   ======================================== */

@media (min-width: 1536px) {
  .container-responsive {
    max-width: var(--container-2xl);
  }

  /* 网格系统 */
  .grid-cols-2xl-5 {
    grid-template-columns: repeat(5, 1fr);
  }

  .grid-cols-2xl-6 {
    grid-template-columns: repeat(6, 1fr);
  }

  /* 统计卡片网格 */
  .stats-grid {
    grid-template-columns: repeat(5, 1fr);
  }

  /* 卡片网格 */
  .card-grid {
    grid-template-columns: repeat(5, 1fr);
  }

  /* 模态框大小调整 */
  .modal-responsive .modal-box {
    max-width: 56rem;
  }
}

/* ========================================
   移动端专用样式
   ======================================== */

@media (max-width: 639px) {
  /* 移动端管理面板布局 */
  .admin-layout {
    display: block;
    position: relative;
  }

  .admin-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    width: 280px;
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
    z-index: 50;
    background: var(--color-neutral);
  }

  .admin-sidebar.open {
    transform: translateX(0);
  }

  .admin-main {
    padding-top: 4rem; /* 为移动端顶栏留出空间 */
  }

  /* 移动端顶栏 */
  .mobile-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 4rem;
    background: var(--color-base-100);
    border-bottom: 1px solid var(--color-base-300);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 1rem;
    z-index: 40;
  }

  .mobile-menu-btn {
    padding: 0.5rem;
    border: none;
    background: none;
    cursor: pointer;
  }

  /* 移动端导航 */
  .mobile-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 4rem;
    background: var(--color-base-100);
    border-top: 1px solid var(--color-base-300);
    display: flex;
    justify-content: space-around;
    align-items: center;
    z-index: 40;
  }

  .mobile-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0.5rem;
    text-decoration: none;
    color: var(--color-base-content);
    font-size: 0.75rem;
    transition: color 0.2s;
  }

  .mobile-nav-item:hover,
  .mobile-nav-item.active {
    color: var(--color-primary);
  }

  .mobile-nav-item svg {
    width: 1.25rem;
    height: 1.25rem;
    margin-bottom: 0.25rem;
  }

  /* 移动端表格 */
  .table-responsive {
    display: block;
  }

  .table-responsive thead {
    display: none;
  }

  .table-responsive tbody {
    display: block;
  }

  .table-responsive tr {
    display: block;
    border: 1px solid var(--color-base-300);
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    padding: 1rem;
    background: var(--color-base-100);
  }

  .table-responsive td {
    display: block;
    padding: 0.5rem 0;
    border: none;
  }

  .table-responsive td:before {
    content: attr(data-label) ": ";
    font-weight: 600;
    color: var(--color-base-content);
    display: inline-block;
    width: 6rem;
  }

  /* 移动端卡片 */
  .card-responsive {
    margin: 0.5rem;
    padding: 1rem;
  }

  /* 移动端表单 */
  .form-responsive {
    display: block;
  }

  .form-responsive .form-control {
    margin-bottom: 1rem;
  }

  /* 移动端按钮 */
  .btn-responsive {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .btn-group-responsive {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  /* 移动端模态框 */
  .modal-responsive .modal-box {
    margin: 1rem;
    max-width: calc(100vw - 2rem);
    max-height: calc(100vh - 2rem);
  }

  /* 移动端统计卡片 */
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .stat-responsive {
    padding: 1rem;
    text-align: center;
  }

  .stat-responsive .stat-value {
    font-size: 1.5rem;
  }

  /* 移动端筛选面板 */
  .filter-panel-responsive {
    position: fixed;
    bottom: 4rem;
    left: 0;
    right: 0;
    background: var(--color-base-100);
    border-top: 1px solid var(--color-base-300);
    padding: 1rem;
    transform: translateY(100%);
    transition: transform 0.3s ease-in-out;
    z-index: 30;
  }

  .filter-panel-responsive.open {
    transform: translateY(0);
  }
}

/* ========================================
   触摸设备优化
   ======================================== */

@media (hover: none) and (pointer: coarse) {
  /* 增大触摸目标 */
  .btn-touch {
    min-height: 44px;
    min-width: 44px;
  }

  .input-touch {
    min-height: 44px;
  }

  /* 移除悬停效果 */
  .hover-effect:hover {
    transform: none;
    box-shadow: none;
  }

  /* 增强点击反馈 */
  .btn-touch:active {
    transform: scale(0.95);
    opacity: 0.8;
  }
}

/* ========================================
   打印样式
   ======================================== */

@media print {
  .mobile-header,
  .mobile-nav,
  .admin-sidebar,
  .btn-responsive,
  .filter-panel-responsive {
    display: none !important;
  }

  .admin-main {
    padding: 0 !important;
  }

  .card-responsive {
    break-inside: avoid;
    margin-bottom: 1rem;
  }

  .table-responsive {
    display: table !important;
  }

  .table-responsive thead {
    display: table-header-group !important;
  }

  .table-responsive tbody {
    display: table-row-group !important;
  }

  .table-responsive tr {
    display: table-row !important;
    border: none !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .table-responsive td {
    display: table-cell !important;
    padding: 0.5rem !important;
    border: 1px solid #ccc !important;
  }

  .table-responsive td:before {
    display: none !important;
  }
}
