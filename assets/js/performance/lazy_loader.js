/**
 * Racing Game Admin Panel - 懒加载系统
 * 
 * 实现组件、图片、数据的智能懒加载，提升页面性能
 */

/**
 * 组件懒加载管理器
 */
class ComponentLazyLoader {
  constructor() {
    this.loadedComponents = new Set();
    this.loadingComponents = new Map();
    this.componentRegistry = new Map();
    this.intersectionObserver = null;
    this.init();
  }

  init() {
    this.setupIntersectionObserver();
    this.registerComponents();
  }

  /**
   * 设置交叉观察器
   */
  setupIntersectionObserver() {
    const options = {
      root: null,
      rootMargin: '50px',
      threshold: 0.1
    };

    this.intersectionObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this.loadComponent(entry.target);
        }
      });
    }, options);
  }

  /**
   * 注册可懒加载的组件
   */
  registerComponents() {
    // 注册图表组件
    this.componentRegistry.set('racing-chart', {
      loader: () => import('./components/racing_chart'),
      placeholder: this.createChartPlaceholder,
      priority: 'low'
    });

    // 注册数据表格组件
    this.componentRegistry.set('data-table', {
      loader: () => import('./components/data_table'),
      placeholder: this.createTablePlaceholder,
      priority: 'medium'
    });

    // 注册用户管理组件
    this.componentRegistry.set('user-management', {
      loader: () => import('./components/user_management'),
      placeholder: this.createUserManagementPlaceholder,
      priority: 'high'
    });

    // 注册支付管理组件
    this.componentRegistry.set('payment-management', {
      loader: () => import('./components/payment_management'),
      placeholder: this.createPaymentPlaceholder,
      priority: 'high'
    });

    // 注册统计仪表板组件
    this.componentRegistry.set('stats-dashboard', {
      loader: () => import('./components/stats_dashboard'),
      placeholder: this.createStatsDashboardPlaceholder,
      priority: 'medium'
    });
  }

  /**
   * 观察元素进行懒加载
   */
  observe(element) {
    if (this.intersectionObserver && element) {
      this.intersectionObserver.observe(element);
    }
  }

  /**
   * 停止观察元素
   */
  unobserve(element) {
    if (this.intersectionObserver && element) {
      this.intersectionObserver.unobserve(element);
    }
  }

  /**
   * 加载组件
   */
  async loadComponent(element) {
    const componentType = element.dataset.lazyComponent;
    const componentId = element.dataset.componentId || componentType;

    if (this.loadedComponents.has(componentId)) {
      return;
    }

    if (this.loadingComponents.has(componentId)) {
      return this.loadingComponents.get(componentId);
    }

    const componentConfig = this.componentRegistry.get(componentType);
    if (!componentConfig) {
      console.warn(`Unknown lazy component: ${componentType}`);
      return;
    }

    // 显示加载状态
    this.showLoadingState(element, componentConfig);

    // 创建加载Promise
    const loadingPromise = this.loadComponentAsync(element, componentConfig);
    this.loadingComponents.set(componentId, loadingPromise);

    try {
      await loadingPromise;
      this.loadedComponents.add(componentId);
      this.loadingComponents.delete(componentId);
      this.unobserve(element);
    } catch (error) {
      console.error(`Failed to load component ${componentType}:`, error);
      this.showErrorState(element, error);
      this.loadingComponents.delete(componentId);
    }
  }

  /**
   * 异步加载组件
   */
  async loadComponentAsync(element, config) {
    // 模拟网络延迟（开发环境）
    if (process.env.NODE_ENV === 'development') {
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // 动态导入组件
    const module = await config.loader();
    const Component = module.default || module;

    // 初始化组件
    if (typeof Component.init === 'function') {
      await Component.init(element);
    }

    // 移除占位符
    this.removePlaceholder(element);

    // 触发组件加载完成事件
    this.dispatchLoadedEvent(element, Component);
  }

  /**
   * 显示加载状态
   */
  showLoadingState(element, config) {
    const placeholder = config.placeholder();
    element.innerHTML = placeholder;
    element.classList.add('lazy-loading');
  }

  /**
   * 显示错误状态
   */
  showErrorState(element, error) {
    element.innerHTML = `
      <div class="lazy-error p-4 text-center">
        <div class="text-error mb-2">
          <svg class="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z">
            </path>
          </svg>
        </div>
        <p class="text-sm text-gray-600">组件加载失败</p>
        <button class="btn btn-sm btn-ghost mt-2" onclick="window.componentLazyLoader.retryLoad('${element.dataset.componentId}')">
          重试
        </button>
      </div>
    `;
    element.classList.add('lazy-error');
  }

  /**
   * 移除占位符
   */
  removePlaceholder(element) {
    element.classList.remove('lazy-loading', 'lazy-error');
  }

  /**
   * 触发组件加载完成事件
   */
  dispatchLoadedEvent(element, Component) {
    const event = new CustomEvent('componentLoaded', {
      detail: {
        element,
        component: Component,
        type: element.dataset.lazyComponent
      }
    });
    element.dispatchEvent(event);
  }

  /**
   * 重试加载
   */
  async retryLoad(componentId) {
    const element = document.querySelector(`[data-component-id="${componentId}"]`);
    if (element) {
      this.loadedComponents.delete(componentId);
      this.loadingComponents.delete(componentId);
      await this.loadComponent(element);
    }
  }

  /**
   * 预加载高优先级组件
   */
  preloadHighPriorityComponents() {
    this.componentRegistry.forEach((config, type) => {
      if (config.priority === 'high') {
        config.loader().catch(error => {
          console.warn(`Failed to preload component ${type}:`, error);
        });
      }
    });
  }

  // 占位符生成器
  createChartPlaceholder() {
    return `
      <div class="chart-placeholder animate-pulse">
        <div class="h-64 bg-gray-200 rounded-lg flex items-center justify-center">
          <div class="text-center">
            <div class="loading loading-spinner loading-lg text-primary mb-2"></div>
            <p class="text-sm text-gray-500">加载图表中...</p>
          </div>
        </div>
      </div>
    `;
  }

  createTablePlaceholder() {
    return `
      <div class="table-placeholder animate-pulse">
        <div class="space-y-3">
          <div class="h-4 bg-gray-200 rounded w-full"></div>
          <div class="h-4 bg-gray-200 rounded w-5/6"></div>
          <div class="h-4 bg-gray-200 rounded w-4/6"></div>
          <div class="h-4 bg-gray-200 rounded w-3/6"></div>
        </div>
        <div class="text-center mt-4">
          <div class="loading loading-spinner loading-md text-primary"></div>
          <p class="text-sm text-gray-500 mt-2">加载数据中...</p>
        </div>
      </div>
    `;
  }

  createUserManagementPlaceholder() {
    return `
      <div class="user-management-placeholder animate-pulse">
        <div class="flex justify-between items-center mb-4">
          <div class="h-6 bg-gray-200 rounded w-32"></div>
          <div class="h-8 bg-gray-200 rounded w-24"></div>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          ${Array(6).fill(0).map(() => `
            <div class="card bg-base-100 shadow">
              <div class="card-body">
                <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div class="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          `).join('')}
        </div>
        <div class="text-center mt-6">
          <div class="loading loading-spinner loading-lg text-primary"></div>
          <p class="text-sm text-gray-500 mt-2">加载用户管理界面...</p>
        </div>
      </div>
    `;
  }

  createPaymentPlaceholder() {
    return `
      <div class="payment-placeholder animate-pulse">
        <div class="stats shadow mb-6">
          ${Array(4).fill(0).map(() => `
            <div class="stat">
              <div class="h-4 bg-gray-200 rounded w-16 mb-2"></div>
              <div class="h-6 bg-gray-200 rounded w-20"></div>
            </div>
          `).join('')}
        </div>
        <div class="text-center">
          <div class="loading loading-spinner loading-lg text-primary"></div>
          <p class="text-sm text-gray-500 mt-2">加载支付管理界面...</p>
        </div>
      </div>
    `;
  }

  createStatsDashboardPlaceholder() {
    return `
      <div class="stats-dashboard-placeholder animate-pulse">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          ${Array(4).fill(0).map(() => `
            <div class="stat bg-base-100 shadow rounded-lg">
              <div class="stat-body p-4">
                <div class="h-3 bg-gray-200 rounded w-16 mb-2"></div>
                <div class="h-6 bg-gray-200 rounded w-20 mb-1"></div>
                <div class="h-3 bg-gray-200 rounded w-12"></div>
              </div>
            </div>
          `).join('')}
        </div>
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div class="h-64 bg-gray-200 rounded-lg"></div>
          <div class="h-64 bg-gray-200 rounded-lg"></div>
        </div>
        <div class="text-center mt-6">
          <div class="loading loading-spinner loading-lg text-primary"></div>
          <p class="text-sm text-gray-500 mt-2">加载统计仪表板...</p>
        </div>
      </div>
    `;
  }

  /**
   * 销毁懒加载器
   */
  destroy() {
    if (this.intersectionObserver) {
      this.intersectionObserver.disconnect();
    }
    this.loadedComponents.clear();
    this.loadingComponents.clear();
    this.componentRegistry.clear();
  }
}

/**
 * 图片懒加载管理器
 */
class ImageLazyLoader {
  constructor() {
    this.observer = null;
    this.init();
  }

  init() {
    const options = {
      root: null,
      rootMargin: '50px',
      threshold: 0.1
    };

    this.observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this.loadImage(entry.target);
        }
      });
    }, options);

    // 自动观察所有懒加载图片
    this.observeImages();
  }

  observeImages() {
    const lazyImages = document.querySelectorAll('img[data-lazy-src]');
    lazyImages.forEach(img => this.observer.observe(img));
  }

  loadImage(img) {
    const src = img.dataset.lazySrc;
    const placeholder = img.dataset.placeholder;

    if (src) {
      // 创建新图片对象预加载
      const imageLoader = new Image();
      
      imageLoader.onload = () => {
        img.src = src;
        img.classList.remove('lazy-loading');
        img.classList.add('lazy-loaded');
        this.observer.unobserve(img);
      };

      imageLoader.onerror = () => {
        img.src = placeholder || '/images/placeholder.svg';
        img.classList.add('lazy-error');
        this.observer.unobserve(img);
      };

      img.classList.add('lazy-loading');
      imageLoader.src = src;
    }
  }

  destroy() {
    if (this.observer) {
      this.observer.disconnect();
    }
  }
}

// 全局实例
window.componentLazyLoader = new ComponentLazyLoader();
window.imageLazyLoader = new ImageLazyLoader();

// Phoenix LiveView Hook
const LazyLoadHook = {
  mounted() {
    if (this.el.dataset.lazyComponent) {
      window.componentLazyLoader.observe(this.el);
    }
    
    // 观察图片
    const lazyImages = this.el.querySelectorAll('img[data-lazy-src]');
    lazyImages.forEach(img => window.imageLazyLoader.observer.observe(img));
  },

  destroyed() {
    if (this.el.dataset.lazyComponent) {
      window.componentLazyLoader.unobserve(this.el);
    }
  }
};

// 导出
export { ComponentLazyLoader, ImageLazyLoader, LazyLoadHook };
