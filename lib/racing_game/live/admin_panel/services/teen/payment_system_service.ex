defmodule RacingGame.Live.AdminPanel.Services.Teen.PaymentSystemService do
  @moduledoc """
  💳 支付系统业务服务层

  负责支付系统相关的业务逻辑处理，包括：
  - 支付网关管理业务逻辑
  - 支付配置管理业务逻辑
  - 兑换配置管理业务逻辑
  - 支付方式选择和优化
  - 费用计算和验证
  - 兑换资格验证
  - 支付安全检查
  - 系统监控和统计
  """

  require Logger
  alias RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.{
    PaymentGatewayRepository,
    PaymentConfigRepository,
    ExchangeConfigRepository
  }
  alias RacingGame.Live.AdminPanel.QueryBuilders.Teen.PaymentSystemQueryBuilder

  # 业务常量
  @default_page_size 20
  @max_page_size 100
  @cache_ttl 300  # 5分钟缓存

  # 支付类型常量
  @payment_types %{
    "alipay" => "支付宝",
    "wechat" => "微信支付",
    "bank_card" => "银行卡",
    "usdt" => "USDT",
    "bitcoin" => "比特币"
  }

  # 兑换类型常量
  @exchange_types %{
    1 => "游戏兑换",
    2 => "推广兑换"
  }

  # ==================== 支付网关管理业务逻辑 ====================

  @doc """
  创建支付网关

  ## 参数
  - `gateway_data` - 网关数据
  - `creator_id` - 创建者ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, gateway}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_payment_gateway(gateway_data, creator_id, options \\ []) do
    Logger.info("💳 [支付系统服务] 创建支付网关: #{inspect(gateway_data[:name])}")

    try do
      # 1. 验证创建者权限
      with {:ok, _creator} <- verify_creator_permissions(creator_id, :create_gateway),
           # 2. 验证网关数据
           {:ok, validated_data} <- validate_gateway_data(gateway_data),
           # 3. 检查网关名称唯一性
           {:ok, _} <- check_gateway_name_uniqueness(validated_data.name),
           # 4. 检查商户ID唯一性
           {:ok, _} <- check_merchant_id_uniqueness(validated_data.merchant_id),
           # 5. 验证网关连接
           {:ok, _} <- validate_gateway_connection(validated_data),
           # 6. 创建网关
           {:ok, gateway} <- PaymentGatewayRepository.create_payment_gateway(validated_data, options),
           # 7. 记录操作日志
           {:ok, _log} <- log_gateway_operation(creator_id, :create, gateway.id, %{
             gateway_name: gateway.name,
             gateway_type: gateway.gateway_type
           }) do

        Logger.info("✅ [支付系统服务] 支付网关创建成功: #{gateway.name}")
        {:ok, gateway}
      else
        {:error, :creator_not_found} ->
          Logger.error("❌ [支付系统服务] 创建者不存在: #{creator_id}")
          {:error, :creator_not_found}

        {:error, :permission_denied} ->
          Logger.error("❌ [支付系统服务] 创建网关权限不足: #{creator_id}")
          {:error, :permission_denied}

        {:error, :gateway_name_exists} ->
          Logger.error("❌ [支付系统服务] 网关名称已存在: #{gateway_data[:name]}")
          {:error, :gateway_name_exists}

        {:error, :merchant_id_exists} ->
          Logger.error("❌ [支付系统服务] 商户ID已存在: #{gateway_data[:merchant_id]}")
          {:error, :merchant_id_exists}

        {:error, :invalid_gateway_data} ->
          Logger.error("❌ [支付系统服务] 网关数据无效")
          {:error, :invalid_gateway_data}

        {:error, :connection_test_failed} ->
          Logger.error("❌ [支付系统服务] 网关连接测试失败")
          {:error, :connection_test_failed}

        error ->
          Logger.error("❌ [支付系统服务] 创建支付网关失败: #{inspect(error)}")
          {:error, :create_gateway_failed}
      end
    rescue
      exception ->
        Logger.error("💥 [支付系统服务] 创建支付网关异常: #{inspect(exception)}")
        {:error, :create_gateway_exception}
    end
  end

  @doc """
  更新支付网关

  ## 参数
  - `gateway_id` - 网关ID
  - `update_data` - 更新数据
  - `updater_id` - 更新者ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, gateway}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_payment_gateway(gateway_id, update_data, updater_id, options \\ []) do
    Logger.info("💳 [支付系统服务] 更新支付网关: #{gateway_id}")

    try do
      # 1. 验证更新者权限
      with {:ok, _updater} <- verify_updater_permissions(updater_id, :update_gateway),
           # 2. 获取目标网关
           {:ok, target_gateway} <- PaymentGatewayRepository.get_payment_gateway(gateway_id),
           {:ok, _} <- check_gateway_exists(target_gateway),
           # 3. 验证更新数据
           {:ok, validated_data} <- validate_gateway_update_data(update_data, target_gateway),
           # 4. 检查名称唯一性（如果更新了名称）
           {:ok, _} <- check_gateway_name_uniqueness_for_update(validated_data, target_gateway),
           # 5. 检查商户ID唯一性（如果更新了商户ID）
           {:ok, _} <- check_merchant_id_uniqueness_for_update(validated_data, target_gateway),
           # 6. 测试连接（如果更新了连接信息）
           {:ok, _} <- test_gateway_connection_if_needed(validated_data, target_gateway),
           # 7. 提取旧值用于日志
           old_values <- extract_gateway_old_values(target_gateway, validated_data),
           # 8. 更新网关
           {:ok, updated_gateway} <- PaymentGatewayRepository.update_payment_gateway(gateway_id, validated_data, options),
           # 9. 记录操作日志
           {:ok, _log} <- log_gateway_operation(updater_id, :update, gateway_id, %{
             old_values: old_values,
             new_values: validated_data,
             gateway_name: updated_gateway.name
           }) do

        Logger.info("✅ [支付系统服务] 支付网关更新成功: #{updated_gateway.name}")
        {:ok, updated_gateway}
      else
        {:error, :updater_not_found} ->
          Logger.error("❌ [支付系统服务] 更新者不存在: #{updater_id}")
          {:error, :updater_not_found}

        {:error, :permission_denied} ->
          Logger.error("❌ [支付系统服务] 更新网关权限不足: #{updater_id}")
          {:error, :permission_denied}

        {:error, :gateway_not_found} ->
          Logger.error("❌ [支付系统服务] 网关不存在: #{gateway_id}")
          {:error, :gateway_not_found}

        {:error, :gateway_name_exists} ->
          Logger.error("❌ [支付系统服务] 网关名称已存在")
          {:error, :gateway_name_exists}

        {:error, :merchant_id_exists} ->
          Logger.error("❌ [支付系统服务] 商户ID已存在")
          {:error, :merchant_id_exists}

        {:error, :connection_test_failed} ->
          Logger.error("❌ [支付系统服务] 网关连接测试失败")
          {:error, :connection_test_failed}

        error ->
          Logger.error("❌ [支付系统服务] 更新支付网关失败: #{inspect(error)}")
          {:error, :update_gateway_failed}
      end
    rescue
      exception ->
        Logger.error("💥 [支付系统服务] 更新支付网关异常: #{inspect(exception)}")
        {:error, :update_gateway_exception}
    end
  end

  @doc """
  删除支付网关

  ## 参数
  - `gateway_id` - 网关ID
  - `deleter_id` - 删除者ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, :deleted}` - 成功
  - `{:error, reason}` - 失败
  """
  def delete_payment_gateway(gateway_id, deleter_id, options \\ []) do
    Logger.info("💳 [支付系统服务] 删除支付网关: #{gateway_id}")

    try do
      # 1. 验证删除者权限
      with {:ok, _deleter} <- verify_deleter_permissions(deleter_id, :delete_gateway),
           # 2. 获取目标网关
           {:ok, target_gateway} <- PaymentGatewayRepository.get_payment_gateway(gateway_id, [load: [:payment_configs]]),
           {:ok, _} <- check_gateway_exists(target_gateway),
           # 3. 检查网关是否可删除
           {:ok, _} <- check_gateway_deletable(target_gateway),
           # 4. 删除网关
           {:ok, _} <- PaymentGatewayRepository.delete_payment_gateway(gateway_id, options),
           # 5. 记录操作日志
           {:ok, _log} <- log_gateway_operation(deleter_id, :delete, gateway_id, %{
             gateway_name: target_gateway.name,
             gateway_type: target_gateway.gateway_type,
             config_count: length(target_gateway.payment_configs || [])
           }) do

        Logger.info("✅ [支付系统服务] 支付网关删除成功: #{target_gateway.name}")
        {:ok, :deleted}
      else
        {:error, :deleter_not_found} ->
          Logger.error("❌ [支付系统服务] 删除者不存在: #{deleter_id}")
          {:error, :deleter_not_found}

        {:error, :permission_denied} ->
          Logger.error("❌ [支付系统服务] 删除网关权限不足: #{deleter_id}")
          {:error, :permission_denied}

        {:error, :gateway_not_found} ->
          Logger.error("❌ [支付系统服务] 网关不存在: #{gateway_id}")
          {:error, :gateway_not_found}

        {:error, :gateway_has_active_configs} ->
          Logger.error("❌ [支付系统服务] 网关有活跃配置，无法删除: #{gateway_id}")
          {:error, :gateway_has_active_configs}

        error ->
          Logger.error("❌ [支付系统服务] 删除支付网关失败: #{inspect(error)}")
          {:error, :delete_gateway_failed}
      end
    rescue
      exception ->
        Logger.error("💥 [支付系统服务] 删除支付网关异常: #{inspect(exception)}")
        {:error, :delete_gateway_exception}
    end
  end

  @doc """
  激活支付网关

  ## 参数
  - `gateway_id` - 网关ID
  - `activator_id` - 激活者ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, gateway}` - 成功
  - `{:error, reason}` - 失败
  """
  def activate_payment_gateway(gateway_id, activator_id, options \\ []) do
    Logger.info("💳 [支付系统服务] 激活支付网关: #{gateway_id}")

    try do
      # 1. 验证激活者权限
      with {:ok, _activator} <- verify_activator_permissions(activator_id, :activate_gateway),
           # 2. 获取目标网关
           {:ok, target_gateway} <- PaymentGatewayRepository.get_payment_gateway(gateway_id),
           {:ok, _} <- check_gateway_exists(target_gateway),
           # 3. 检查网关是否可激活
           {:ok, _} <- check_gateway_activatable(target_gateway),
           # 4. 测试网关连接
           {:ok, _} <- test_gateway_connection(target_gateway),
           # 5. 激活网关
           {:ok, activated_gateway} <- PaymentGatewayRepository.activate_payment_gateway(gateway_id, options),
           # 6. 记录操作日志
           {:ok, _log} <- log_gateway_operation(activator_id, :activate, gateway_id, %{
             gateway_name: activated_gateway.name,
             gateway_type: activated_gateway.gateway_type
           }) do

        Logger.info("✅ [支付系统服务] 支付网关激活成功: #{activated_gateway.name}")
        {:ok, activated_gateway}
      else
        {:error, :activator_not_found} ->
          Logger.error("❌ [支付系统服务] 激活者不存在: #{activator_id}")
          {:error, :activator_not_found}

        {:error, :permission_denied} ->
          Logger.error("❌ [支付系统服务] 激活网关权限不足: #{activator_id}")
          {:error, :permission_denied}

        {:error, :gateway_not_found} ->
          Logger.error("❌ [支付系统服务] 网关不存在: #{gateway_id}")
          {:error, :gateway_not_found}

        {:error, :gateway_already_active} ->
          Logger.error("❌ [支付系统服务] 网关已经是激活状态: #{gateway_id}")
          {:error, :gateway_already_active}

        {:error, :connection_test_failed} ->
          Logger.error("❌ [支付系统服务] 网关连接测试失败: #{gateway_id}")
          {:error, :connection_test_failed}

        error ->
          Logger.error("❌ [支付系统服务] 激活支付网关失败: #{inspect(error)}")
          {:error, :activate_gateway_failed}
      end
    rescue
      exception ->
        Logger.error("💥 [支付系统服务] 激活支付网关异常: #{inspect(exception)}")
        {:error, :activate_gateway_exception}
    end
  end

  @doc """
  禁用支付网关

  ## 参数
  - `gateway_id` - 网关ID
  - `deactivator_id` - 禁用者ID
  - `reason` - 禁用原因
  - `options` - 选项参数

  ## 返回
  - `{:ok, gateway}` - 成功
  - `{:error, reason}` - 失败
  """
  def deactivate_payment_gateway(gateway_id, deactivator_id, reason \\ "", options \\ []) do
    Logger.info("💳 [支付系统服务] 禁用支付网关: #{gateway_id}")

    try do
      # 1. 验证禁用者权限
      with {:ok, _deactivator} <- verify_deactivator_permissions(deactivator_id, :deactivate_gateway),
           # 2. 获取目标网关
           {:ok, target_gateway} <- PaymentGatewayRepository.get_payment_gateway(gateway_id, [load: [:payment_configs]]),
           {:ok, _} <- check_gateway_exists(target_gateway),
           # 3. 检查网关是否可禁用
           {:ok, _} <- check_gateway_deactivatable(target_gateway),
           # 4. 检查禁用影响
           {:ok, impact_info} <- check_gateway_deactivation_impact(target_gateway),
           # 5. 禁用网关
           {:ok, deactivated_gateway} <- PaymentGatewayRepository.deactivate_payment_gateway(gateway_id, options),
           # 6. 记录操作日志
           {:ok, _log} <- log_gateway_operation(deactivator_id, :deactivate, gateway_id, %{
             gateway_name: deactivated_gateway.name,
             gateway_type: deactivated_gateway.gateway_type,
             reason: reason,
             impact: impact_info
           }) do

        Logger.info("✅ [支付系统服务] 支付网关禁用成功: #{deactivated_gateway.name}")
        {:ok, deactivated_gateway}
      else
        {:error, :deactivator_not_found} ->
          Logger.error("❌ [支付系统服务] 禁用者不存在: #{deactivator_id}")
          {:error, :deactivator_not_found}

        {:error, :permission_denied} ->
          Logger.error("❌ [支付系统服务] 禁用网关权限不足: #{deactivator_id}")
          {:error, :permission_denied}

        {:error, :gateway_not_found} ->
          Logger.error("❌ [支付系统服务] 网关不存在: #{gateway_id}")
          {:error, :gateway_not_found}

        {:error, :gateway_already_inactive} ->
          Logger.error("❌ [支付系统服务] 网关已经是禁用状态: #{gateway_id}")
          {:error, :gateway_already_inactive}

        error ->
          Logger.error("❌ [支付系统服务] 禁用支付网关失败: #{inspect(error)}")
          {:error, :deactivate_gateway_failed}
      end
    rescue
      exception ->
        Logger.error("💥 [支付系统服务] 禁用支付网关异常: #{inspect(exception)}")
        {:error, :deactivate_gateway_exception}
    end
  end

  # ==================== 私有辅助函数 ====================

  # 验证创建者权限
  defp verify_creator_permissions(creator_id, operation) do
    # 这里应该实现权限验证逻辑
    Logger.debug("🔍 [支付系统服务] 验证创建者权限: #{creator_id} - #{operation}")
    {:ok, %{id: creator_id, permissions: [:create_gateway]}}
  end

  # 验证更新者权限
  defp verify_updater_permissions(updater_id, operation) do
    Logger.debug("🔍 [支付系统服务] 验证更新者权限: #{updater_id} - #{operation}")
    {:ok, %{id: updater_id, permissions: [:update_gateway]}}
  end

  # 验证删除者权限
  defp verify_deleter_permissions(deleter_id, operation) do
    Logger.debug("🔍 [支付系统服务] 验证删除者权限: #{deleter_id} - #{operation}")
    {:ok, %{id: deleter_id, permissions: [:delete_gateway]}}
  end

  # 验证激活者权限
  defp verify_activator_permissions(activator_id, operation) do
    Logger.debug("🔍 [支付系统服务] 验证激活者权限: #{activator_id} - #{operation}")
    {:ok, %{id: activator_id, permissions: [:activate_gateway]}}
  end

  # 验证禁用者权限
  defp verify_deactivator_permissions(deactivator_id, operation) do
    Logger.debug("🔍 [支付系统服务] 验证禁用者权限: #{deactivator_id} - #{operation}")
    {:ok, %{id: deactivator_id, permissions: [:deactivate_gateway]}}
  end

  # 验证网关数据
  defp validate_gateway_data(gateway_data) do
    Logger.debug("🔍 [支付系统服务] 验证网关数据")

    required_fields = [:name, :gateway_type, :api_url, :merchant_id, :api_key]

    case check_required_fields(gateway_data, required_fields) do
      :ok -> {:ok, gateway_data}
      {:error, missing_fields} ->
        Logger.error("❌ [支付系统服务] 缺少必需字段: #{inspect(missing_fields)}")
        {:error, :invalid_gateway_data}
    end
  end

  # 检查必需字段
  defp check_required_fields(data, required_fields) do
    missing_fields = Enum.filter(required_fields, fn field ->
      case Map.get(data, field) do
        nil -> true
        "" -> true
        _ -> false
      end
    end)

    if length(missing_fields) == 0 do
      :ok
    else
      {:error, missing_fields}
    end
  end

  # 检查网关名称唯一性
  defp check_gateway_name_uniqueness(name) do
    case PaymentGatewayRepository.list_payment_gateways(%{"name" => name}) do
      {:ok, {[], 0}} -> {:ok, :unique}
      {:ok, {_gateways, _count}} -> {:error, :gateway_name_exists}
      error -> error
    end
  end

  # 检查商户ID唯一性
  defp check_merchant_id_uniqueness(merchant_id) do
    case PaymentGatewayRepository.list_payment_gateways(%{"merchant_id" => merchant_id}) do
      {:ok, {[], 0}} -> {:ok, :unique}
      {:ok, {_gateways, _count}} -> {:error, :merchant_id_exists}
      error -> error
    end
  end

  # 验证网关连接
  defp validate_gateway_connection(_gateway_data) do
    Logger.debug("🔍 [支付系统服务] 验证网关连接")
    # 这里应该实现实际的连接测试逻辑
    {:ok, :connection_valid}
  end

  # 检查网关是否存在
  defp check_gateway_exists(gateway) do
    if gateway do
      {:ok, :exists}
    else
      {:error, :gateway_not_found}
    end
  end

  # 记录网关操作日志
  defp log_gateway_operation(_operator_id, operation, _gateway_id, _details) do
    Logger.info("📝 [支付系统服务] 记录网关操作: #{operation}")
    # 这里应该实现实际的日志记录逻辑
    {:ok, %{id: "log_#{:rand.uniform(1000)}", operation: operation}}
  end

  # 验证网关更新数据
  defp validate_gateway_update_data(update_data, target_gateway) do
    Logger.debug("🔍 [支付系统服务] 验证网关更新数据")
    # 这里可以添加更复杂的更新数据验证逻辑
    {:ok, update_data}
  end

  # 检查网关名称唯一性（更新时）
  defp check_gateway_name_uniqueness_for_update(update_data, target_gateway) do
    case Map.get(update_data, :name) do
      nil -> {:ok, :no_name_change}
      new_name when new_name == target_gateway.name -> {:ok, :same_name}
      new_name -> check_gateway_name_uniqueness(new_name)
    end
  end

  # 检查商户ID唯一性（更新时）
  defp check_merchant_id_uniqueness_for_update(update_data, target_gateway) do
    case Map.get(update_data, :merchant_id) do
      nil -> {:ok, :no_merchant_id_change}
      new_merchant_id when new_merchant_id == target_gateway.merchant_id -> {:ok, :same_merchant_id}
      new_merchant_id -> check_merchant_id_uniqueness(new_merchant_id)
    end
  end

  # 如果需要则测试网关连接
  defp test_gateway_connection_if_needed(update_data, target_gateway) do
    connection_fields = [:api_url, :api_key, :secret_key]

    has_connection_changes = Enum.any?(connection_fields, fn field ->
      Map.has_key?(update_data, field) and Map.get(update_data, field) != Map.get(target_gateway, field)
    end)

    if has_connection_changes do
      # 合并新旧数据进行连接测试
      test_data = Map.merge(Map.from_struct(target_gateway), update_data)
      validate_gateway_connection(test_data)
    else
      {:ok, :no_connection_test_needed}
    end
  end

  # 提取网关旧值
  defp extract_gateway_old_values(target_gateway, update_data) do
    Enum.reduce(update_data, %{}, fn {key, _new_value}, acc ->
      old_value = Map.get(target_gateway, key)
      Map.put(acc, key, old_value)
    end)
  end

  # 检查网关是否可删除
  defp check_gateway_deletable(gateway) do
    active_configs = Enum.filter(gateway.payment_configs || [], &(&1.status == 1))

    if length(active_configs) > 0 do
      {:error, :gateway_has_active_configs}
    else
      {:ok, :deletable}
    end
  end

  # 检查网关是否可激活
  defp check_gateway_activatable(gateway) do
    if gateway.status == 1 do
      {:error, :gateway_already_active}
    else
      {:ok, :activatable}
    end
  end

  # 测试网关连接
  defp test_gateway_connection(gateway) do
    Logger.debug("🔍 [支付系统服务] 测试网关连接: #{gateway.name}")
    # 这里应该实现实际的连接测试逻辑
    {:ok, :connection_successful}
  end

  # 检查网关是否可禁用
  defp check_gateway_deactivatable(gateway) do
    if gateway.status == 0 do
      {:error, :gateway_already_inactive}
    else
      {:ok, :deactivatable}
    end
  end

  # 检查网关禁用影响
  defp check_gateway_deactivation_impact(gateway) do
    active_configs = Enum.filter(gateway.payment_configs || [], &(&1.status == 1))

    impact_info = %{
      affected_configs: length(active_configs),
      config_names: Enum.map(active_configs, & &1.payment_type_name)
    }

    {:ok, impact_info}
  end

  # ==================== 支付配置管理业务逻辑 ====================

  @doc """
  创建支付配置

  ## 参数
  - `config_data` - 配置数据
  - `creator_id` - 创建者ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, config}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_payment_config(config_data, creator_id, options \\ []) do
    Logger.info("💳 [支付系统服务] 创建支付配置: #{inspect(config_data[:payment_type_name])}")

    try do
      # 1. 验证创建者权限
      with {:ok, _creator} <- verify_creator_permissions(creator_id, :create_config),
           # 2. 验证配置数据
           {:ok, validated_data} <- validate_config_data(config_data),
           # 3. 验证网关存在且可用
           {:ok, gateway} <- verify_gateway_available(validated_data.gateway_id),
           # 4. 检查配置唯一性
           {:ok, _} <- check_config_uniqueness(validated_data),
           # 5. 验证金额范围
           {:ok, _} <- validate_amount_ranges(validated_data),
           # 6. 验证费率设置
           {:ok, _} <- validate_fee_rates(validated_data),
           # 7. 创建配置
           {:ok, config} <- PaymentConfigRepository.create_payment_config(validated_data, options),
           # 8. 记录操作日志
           {:ok, _log} <- log_config_operation(creator_id, :create, config.id, %{
             config_name: config.payment_type_name,
             gateway_name: gateway.name,
             payment_type: config.payment_type
           }) do

        Logger.info("✅ [支付系统服务] 支付配置创建成功: #{config.payment_type_name}")
        {:ok, config}
      else
        {:error, :creator_not_found} ->
          Logger.error("❌ [支付系统服务] 创建者不存在: #{creator_id}")
          {:error, :creator_not_found}

        {:error, :permission_denied} ->
          Logger.error("❌ [支付系统服务] 创建配置权限不足: #{creator_id}")
          {:error, :permission_denied}

        {:error, :gateway_not_available} ->
          Logger.error("❌ [支付系统服务] 网关不可用: #{config_data[:gateway_id]}")
          {:error, :gateway_not_available}

        {:error, :config_already_exists} ->
          Logger.error("❌ [支付系统服务] 配置已存在")
          {:error, :config_already_exists}

        {:error, :invalid_amount_range} ->
          Logger.error("❌ [支付系统服务] 金额范围无效")
          {:error, :invalid_amount_range}

        {:error, :invalid_fee_rates} ->
          Logger.error("❌ [支付系统服务] 费率设置无效")
          {:error, :invalid_fee_rates}

        error ->
          Logger.error("❌ [支付系统服务] 创建支付配置失败: #{inspect(error)}")
          {:error, :create_config_failed}
      end
    rescue
      exception ->
        Logger.error("💥 [支付系统服务] 创建支付配置异常: #{inspect(exception)}")
        {:error, :create_config_exception}
    end
  end

  @doc """
  获取支付配置列表

  ## 参数
  - `params` - 查询参数
  - `requester_id` - 请求者ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, {configs, total_count, metadata}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_payment_configs(params \\ %{}, requester_id, options \\ []) do
    Logger.info("💳 [支付系统服务] 获取支付配置列表")

    try do
      # 1. 验证请求者权限
      with {:ok, _requester} <- verify_requester_permissions(requester_id, :list_configs),
           # 2. 标准化查询参数
           {:ok, normalized_params} <- normalize_list_params(params),
           # 3. 获取配置列表
           {:ok, {configs, total_count}} <- PaymentConfigRepository.list_payment_configs(normalized_params, options),
           # 4. 生成元数据
           metadata <- generate_list_metadata(normalized_params, total_count) do

        Logger.info("✅ [支付系统服务] 支付配置列表获取成功: #{length(configs)}/#{total_count}")
        {:ok, {configs, total_count, metadata}}
      else
        {:error, :requester_not_found} ->
          Logger.error("❌ [支付系统服务] 请求者不存在: #{requester_id}")
          {:error, :requester_not_found}

        {:error, :permission_denied} ->
          Logger.error("❌ [支付系统服务] 列表查询权限不足: #{requester_id}")
          {:error, :permission_denied}

        error ->
          Logger.error("❌ [支付系统服务] 获取支付配置列表失败: #{inspect(error)}")
          {:error, :list_configs_failed}
      end
    rescue
      exception ->
        Logger.error("💥 [支付系统服务] 获取支付配置列表异常: #{inspect(exception)}")
        {:error, :list_configs_exception}
    end
  end

  # 验证请求者权限
  defp verify_requester_permissions(requester_id, operation) do
    Logger.debug("🔍 [支付系统服务] 验证请求者权限: #{requester_id} - #{operation}")
    {:ok, %{id: requester_id, permissions: [:list_configs]}}
  end

  # 验证配置数据
  defp validate_config_data(config_data) do
    Logger.debug("🔍 [支付系统服务] 验证配置数据")

    required_fields = [:gateway_id, :payment_type, :payment_type_name, :min_amount, :max_amount, :fee_rate]

    case check_required_fields(config_data, required_fields) do
      :ok -> {:ok, config_data}
      {:error, missing_fields} ->
        Logger.error("❌ [支付系统服务] 缺少必需字段: #{inspect(missing_fields)}")
        {:error, :invalid_config_data}
    end
  end

  # 验证网关可用
  defp verify_gateway_available(gateway_id) do
    case PaymentGatewayRepository.get_payment_gateway(gateway_id) do
      {:ok, gateway} when not is_nil(gateway) and gateway.status == 1 ->
        {:ok, gateway}
      {:ok, gateway} when not is_nil(gateway) ->
        {:error, :gateway_not_active}
      {:ok, nil} ->
        {:error, :gateway_not_found}
      error ->
        error
    end
  end

  # 检查配置唯一性
  defp check_config_uniqueness(config_data) do
    case PaymentConfigRepository.list_payment_configs(%{
      "gateway_id" => config_data.gateway_id,
      "payment_type" => config_data.payment_type
    }) do
      {:ok, {[], 0}} -> {:ok, :unique}
      {:ok, {_configs, _count}} -> {:error, :config_already_exists}
      error -> error
    end
  end

  # 验证金额范围
  defp validate_amount_ranges(config_data) do
    min_amount = Decimal.new(config_data.min_amount)
    max_amount = Decimal.new(config_data.max_amount)

    if Decimal.compare(min_amount, max_amount) == :lt do
      {:ok, :valid_range}
    else
      {:error, :invalid_amount_range}
    end
  end

  # 验证费率设置
  defp validate_fee_rates(config_data) do
    fee_rate = Decimal.new(config_data.fee_rate)
    deduction_rate = Decimal.new(config_data[:deduction_rate] || "0")

    zero = Decimal.new("0")
    hundred = Decimal.new("100")

    fee_valid = Decimal.compare(fee_rate, zero) != :lt and Decimal.compare(fee_rate, hundred) != :gt
    deduction_valid = Decimal.compare(deduction_rate, zero) != :lt and Decimal.compare(deduction_rate, hundred) != :gt

    if fee_valid and deduction_valid do
      {:ok, :valid_rates}
    else
      {:error, :invalid_fee_rates}
    end
  end

  # 记录配置操作日志
  defp log_config_operation(operator_id, operation, config_id, details) do
    Logger.info("📝 [支付系统服务] 记录配置操作: #{operation} - #{config_id}")
    {:ok, %{id: "log_#{:rand.uniform(1000)}", operation: operation}}
  end

  # 标准化列表参数
  defp normalize_list_params(params) do
    normalized = %{
      "page" => get_page_number(params),
      "page_size" => get_page_size(params),
      "sort_by" => get_sort_field(params),
      "sort_order" => get_sort_order(params)
    }

    # 添加过滤参数
    filter_params = Map.take(params, ["gateway_id", "payment_type", "status", "gateway_name", "payment_type_name"])
    final_params = Map.merge(normalized, filter_params)

    {:ok, final_params}
  end

  # 获取页码
  defp get_page_number(params) do
    case Map.get(params, "page") do
      nil -> 1
      page when is_integer(page) and page > 0 -> page
      page when is_binary(page) ->
        case Integer.parse(page) do
          {num, ""} when num > 0 -> num
          _ -> 1
        end
      _ -> 1
    end
  end

  # 获取页面大小
  defp get_page_size(params) do
    case Map.get(params, "page_size") do
      nil -> @default_page_size
      size when is_integer(size) and size > 0 and size <= @max_page_size -> size
      size when is_binary(size) ->
        case Integer.parse(size) do
          {num, ""} when num > 0 and num <= @max_page_size -> num
          _ -> @default_page_size
        end
      _ -> @default_page_size
    end
  end

  # 获取排序字段
  defp get_sort_field(params) do
    case Map.get(params, "sort_by") do
      field when field in ["payment_type_name", "gateway_name", "min_amount", "max_amount", "fee_rate", "status", "inserted_at"] ->
        field
      _ -> "sort_order"
    end
  end

  # 获取排序顺序
  defp get_sort_order(params) do
    case Map.get(params, "sort_order") do
      "desc" -> "desc"
      _ -> "asc"
    end
  end

  # 生成列表元数据
  defp generate_list_metadata(params, total_count) do
    page = params["page"]
    page_size = params["page_size"]
    total_pages = ceil(total_count / page_size)

    %{
      current_page: page,
      page_size: page_size,
      total_count: total_count,
      total_pages: total_pages,
      has_prev: page > 1,
      has_next: page < total_pages,
      prev_page: if(page > 1, do: page - 1, else: nil),
      next_page: if(page < total_pages, do: page + 1, else: nil)
    }
  end

  # ==================== 兑换配置管理业务逻辑 ====================

  @doc """
  创建兑换配置

  ## 参数
  - `config_data` - 配置数据
  - `creator_id` - 创建者ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, config}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_exchange_config(config_data, creator_id, options \\ []) do
    Logger.info("💳 [支付系统服务] 创建兑换配置: #{inspect(config_data[:config_name])}")

    try do
      # 1. 验证创建者权限
      with {:ok, _creator} <- verify_creator_permissions(creator_id, :create_exchange_config),
           # 2. 验证配置数据
           {:ok, validated_data} <- validate_exchange_config_data(config_data),
           # 3. 检查配置名称唯一性
           {:ok, _} <- check_exchange_config_name_uniqueness(validated_data.config_name),
           # 4. 验证兑换比例
           {:ok, _} <- validate_exchange_rates(validated_data),
           # 5. 验证金额限制
           {:ok, _} <- validate_exchange_amount_limits(validated_data),
           # 6. 验证VIP等级要求
           {:ok, _} <- validate_vip_level_requirement(validated_data),
           # 7. 创建配置
           {:ok, config} <- ExchangeConfigRepository.create_exchange_config(validated_data, options),
           # 8. 记录操作日志
           {:ok, _log} <- log_exchange_config_operation(creator_id, :create, config.id, %{
             config_name: config.config_name,
             exchange_type: config.exchange_type,
             exchange_rate: config.exchange_rate
           }) do

        Logger.info("✅ [支付系统服务] 兑换配置创建成功: #{config.config_name}")
        {:ok, config}
      else
        {:error, :creator_not_found} ->
          Logger.error("❌ [支付系统服务] 创建者不存在: #{creator_id}")
          {:error, :creator_not_found}

        {:error, :permission_denied} ->
          Logger.error("❌ [支付系统服务] 创建兑换配置权限不足: #{creator_id}")
          {:error, :permission_denied}

        {:error, :config_name_exists} ->
          Logger.error("❌ [支付系统服务] 兑换配置名称已存在: #{config_data[:config_name]}")
          {:error, :config_name_exists}

        {:error, :invalid_exchange_rates} ->
          Logger.error("❌ [支付系统服务] 兑换比例无效")
          {:error, :invalid_exchange_rates}

        {:error, :invalid_amount_limits} ->
          Logger.error("❌ [支付系统服务] 金额限制无效")
          {:error, :invalid_amount_limits}

        {:error, :invalid_vip_requirement} ->
          Logger.error("❌ [支付系统服务] VIP等级要求无效")
          {:error, :invalid_vip_requirement}

        error ->
          Logger.error("❌ [支付系统服务] 创建兑换配置失败: #{inspect(error)}")
          {:error, :create_exchange_config_failed}
      end
    rescue
      exception ->
        Logger.error("💥 [支付系统服务] 创建兑换配置异常: #{inspect(exception)}")
        {:error, :create_exchange_config_exception}
    end
  end

  @doc """
  获取用户可用兑换配置

  ## 参数
  - `user_info` - 用户信息
  - `exchange_type` - 兑换类型
  - `options` - 选项参数

  ## 返回
  - `{:ok, configs}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_available_exchange_configs(user_info, exchange_type, options \\ []) do
    Logger.info("💳 [支付系统服务] 获取用户可用兑换配置: 用户=#{user_info[:id]}, 类型=#{exchange_type}")

    try do
      # 1. 验证用户信息
      with {:ok, validated_user} <- validate_user_info(user_info),
           # 2. 获取用户兑换配置
           {:ok, config} <- PaymentSystemQueryBuilder.get_user_exchange_config(validated_user, exchange_type, options),
           # 3. 验证用户兑换资格
           {:ok, _} <- verify_user_exchange_eligibility(validated_user, config),
           # 4. 检查每日限额
           {:ok, remaining_limit} <- check_daily_exchange_limit(validated_user, config),
           # 5. 增强配置信息
           enhanced_config <- enhance_exchange_config_info(config, validated_user, remaining_limit) do

        Logger.info("✅ [支付系统服务] 用户可用兑换配置获取成功: #{config.config_name}")
        {:ok, enhanced_config}
      else
        {:error, :invalid_user_info} ->
          Logger.error("❌ [支付系统服务] 用户信息无效")
          {:error, :invalid_user_info}

        {:error, :no_suitable_exchange_config} ->
          Logger.error("❌ [支付系统服务] 没有适合的兑换配置")
          {:error, :no_suitable_exchange_config}

        {:error, :user_not_eligible} ->
          Logger.error("❌ [支付系统服务] 用户不符合兑换条件")
          {:error, :user_not_eligible}

        {:error, :daily_limit_exceeded} ->
          Logger.error("❌ [支付系统服务] 超出每日兑换限额")
          {:error, :daily_limit_exceeded}

        error ->
          Logger.error("❌ [支付系统服务] 获取用户可用兑换配置失败: #{inspect(error)}")
          {:error, :get_user_exchange_configs_failed}
      end
    rescue
      exception ->
        Logger.error("💥 [支付系统服务] 获取用户可用兑换配置异常: #{inspect(exception)}")
        {:error, :get_user_exchange_configs_exception}
    end
  end

  # ==================== 支付业务逻辑 ====================

  @doc """
  选择最优支付方式

  ## 参数
  - `user_info` - 用户信息
  - `payment_request` - 支付请求
  - `options` - 选项参数

  ## 返回
  - `{:ok, payment_method}` - 成功
  - `{:error, reason}` - 失败
  """
  def select_optimal_payment_method(user_info, payment_request, options \\ []) do
    Logger.info("💳 [支付系统服务] 选择最优支付方式: 用户=#{user_info[:id]}, 金额=#{payment_request[:amount]}")

    try do
      # 1. 验证用户信息
      with {:ok, validated_user} <- validate_user_info(user_info),
           # 2. 验证支付请求
           {:ok, validated_request} <- validate_payment_request(payment_request),
           # 3. 获取可用支付方式
           {:ok, available_methods} <- PaymentSystemQueryBuilder.get_available_payment_methods(
             validated_user, validated_request.amount, options
           ),
           # 4. 验证支付资格
           {:ok, eligible_methods} <- filter_eligible_payment_methods(available_methods, validated_user),
           # 5. 选择最优方式
           {:ok, optimal_method} <- select_best_payment_method(eligible_methods, validated_request),
           # 6. 计算费用信息
           {:ok, fee_info} <- calculate_payment_fee_info(optimal_method, validated_request),
           # 7. 生成支付方式信息
           payment_method_info <- generate_payment_method_info(optimal_method, fee_info, validated_request) do

        Logger.info("✅ [支付系统服务] 最优支付方式选择成功: #{optimal_method.gateway.name}")
        {:ok, payment_method_info}
      else
        {:error, :invalid_user_info} ->
          Logger.error("❌ [支付系统服务] 用户信息无效")
          {:error, :invalid_user_info}

        {:error, :invalid_payment_request} ->
          Logger.error("❌ [支付系统服务] 支付请求无效")
          {:error, :invalid_payment_request}

        {:error, :no_available_methods} ->
          Logger.error("❌ [支付系统服务] 没有可用的支付方式")
          {:error, :no_available_methods}

        {:error, :no_eligible_methods} ->
          Logger.error("❌ [支付系统服务] 没有符合条件的支付方式")
          {:error, :no_eligible_methods}

        error ->
          Logger.error("❌ [支付系统服务] 选择最优支付方式失败: #{inspect(error)}")
          {:error, :select_payment_method_failed}
      end
    rescue
      exception ->
        Logger.error("💥 [支付系统服务] 选择最优支付方式异常: #{inspect(exception)}")
        {:error, :select_payment_method_exception}
    end
  end

  # ==================== 私有辅助函数 - 兑换配置 ====================

  # 验证兑换配置数据
  defp validate_exchange_config_data(config_data) do
    Logger.debug("🔍 [支付系统服务] 验证兑换配置数据")

    required_fields = [:config_name, :exchange_rate, :min_amount, :max_amount, :exchange_type]

    case check_required_fields(config_data, required_fields) do
      :ok -> {:ok, config_data}
      {:error, missing_fields} ->
        Logger.error("❌ [支付系统服务] 缺少必需字段: #{inspect(missing_fields)}")
        {:error, :invalid_exchange_config_data}
    end
  end

  # 检查兑换配置名称唯一性
  defp check_exchange_config_name_uniqueness(config_name) do
    case ExchangeConfigRepository.get_exchange_config_by_name(config_name) do
      {:ok, nil} -> {:ok, :unique}
      {:ok, _config} -> {:error, :config_name_exists}
      error -> error
    end
  end

  # 验证兑换比例
  defp validate_exchange_rates(config_data) do
    exchange_rate = Decimal.new(config_data.exchange_rate)
    fee_rate = Decimal.new(config_data[:fee_rate] || "0")
    tax_rate = Decimal.new(config_data[:tax_rate] || "0")

    zero = Decimal.new("0")

    rate_valid = Decimal.compare(exchange_rate, zero) == :gt
    fee_valid = Decimal.compare(fee_rate, zero) != :lt
    tax_valid = Decimal.compare(tax_rate, zero) != :lt

    if rate_valid and fee_valid and tax_valid do
      {:ok, :valid_rates}
    else
      {:error, :invalid_exchange_rates}
    end
  end

  # 验证兑换金额限制
  defp validate_exchange_amount_limits(config_data) do
    min_amount = Decimal.new(config_data.min_amount)
    max_amount = Decimal.new(config_data.max_amount)
    daily_limit = Decimal.new(config_data[:daily_limit] || "0")

    zero = Decimal.new("0")

    range_valid = Decimal.compare(min_amount, max_amount) == :lt
    daily_limit_valid = Decimal.compare(daily_limit, zero) != :lt

    if range_valid and daily_limit_valid do
      {:ok, :valid_limits}
    else
      {:error, :invalid_amount_limits}
    end
  end

  # 验证VIP等级要求
  defp validate_vip_level_requirement(config_data) do
    case Map.get(config_data, :vip_level_required) do
      nil -> {:ok, :no_vip_requirement}
      vip_level when is_integer(vip_level) and vip_level >= 0 and vip_level <= 10 ->
        {:ok, :valid_vip_requirement}
      _ ->
        {:error, :invalid_vip_requirement}
    end
  end

  # 记录兑换配置操作日志
  defp log_exchange_config_operation(operator_id, operation, config_id, details) do
    Logger.info("📝 [支付系统服务] 记录兑换配置操作: #{operation} - #{config_id}")
    {:ok, %{id: "log_#{:rand.uniform(1000)}", operation: operation}}
  end

  # ==================== 私有辅助函数 - 用户验证 ====================

  # 验证用户信息
  defp validate_user_info(user_info) do
    Logger.debug("🔍 [支付系统服务] 验证用户信息")

    required_fields = [:id, :vip_level]

    case check_required_fields(user_info, required_fields) do
      :ok -> {:ok, user_info}
      {:error, missing_fields} ->
        Logger.error("❌ [支付系统服务] 用户信息缺少必需字段: #{inspect(missing_fields)}")
        {:error, :invalid_user_info}
    end
  end

  # 验证用户兑换资格
  defp verify_user_exchange_eligibility(user_info, config) do
    Logger.debug("🔍 [支付系统服务] 验证用户兑换资格")

    # 检查VIP等级要求
    if config.vip_level_required && user_info.vip_level < config.vip_level_required do
      {:error, :user_not_eligible}
    else
      {:ok, :eligible}
    end
  end

  # 检查每日兑换限额
  defp check_daily_exchange_limit(user_info, config) do
    Logger.debug("🔍 [支付系统服务] 检查每日兑换限额")

    # 这里应该实现实际的每日限额检查逻辑
    # 暂时返回配置的每日限额作为剩余限额
    remaining_limit = config.daily_limit || Decimal.new("0")

    if Decimal.compare(remaining_limit, Decimal.new("0")) == :gt do
      {:ok, remaining_limit}
    else
      {:error, :daily_limit_exceeded}
    end
  end

  # 增强兑换配置信息
  defp enhance_exchange_config_info(config, user_info, remaining_limit) do
    Map.merge(config, %{
      user_vip_level: user_info.vip_level,
      remaining_daily_limit: remaining_limit,
      is_vip_eligible: !config.vip_level_required || user_info.vip_level >= config.vip_level_required
    })
  end

  # ==================== 私有辅助函数 - 支付验证 ====================

  # 验证支付请求
  defp validate_payment_request(payment_request) do
    Logger.debug("🔍 [支付系统服务] 验证支付请求")

    required_fields = [:amount, :payment_type]

    case check_required_fields(payment_request, required_fields) do
      :ok ->
        # 验证金额格式
        case validate_payment_amount(payment_request.amount) do
          {:ok, _} -> {:ok, payment_request}
          error -> error
        end
      {:error, missing_fields} ->
        Logger.error("❌ [支付系统服务] 支付请求缺少必需字段: #{inspect(missing_fields)}")
        {:error, :invalid_payment_request}
    end
  end

  # 验证支付金额
  defp validate_payment_amount(amount) do
    try do
      decimal_amount = Decimal.new(amount)
      zero = Decimal.new("0")

      if Decimal.compare(decimal_amount, zero) == :gt do
        {:ok, decimal_amount}
      else
        {:error, :invalid_amount}
      end
    rescue
      _ -> {:error, :invalid_amount_format}
    end
  end

  # 过滤符合条件的支付方式
  defp filter_eligible_payment_methods(available_methods, user_info) do
    Logger.debug("🔍 [支付系统服务] 过滤符合条件的支付方式")

    eligible_methods = Enum.filter(available_methods, fn method ->
      # 这里可以添加更多用户资格检查逻辑
      # 例如：VIP等级、用户状态、地区限制等
      method.gateway.status == 1 and method.config.status == 1
    end)

    if length(eligible_methods) > 0 do
      {:ok, eligible_methods}
    else
      {:error, :no_eligible_methods}
    end
  end

  # 选择最佳支付方式
  defp select_best_payment_method(eligible_methods, payment_request) do
    Logger.debug("🔍 [支付系统服务] 选择最佳支付方式")

    if length(eligible_methods) > 0 do
      # 按优先级排序，选择最优方式
      best_method = Enum.max_by(eligible_methods, & &1.priority)
      {:ok, best_method}
    else
      {:error, :no_available_methods}
    end
  end

  # 计算支付费用信息
  defp calculate_payment_fee_info(payment_method, payment_request) do
    Logger.debug("🔍 [支付系统服务] 计算支付费用信息")

    PaymentSystemQueryBuilder.calculate_payment_fees(
      payment_method.gateway.id,
      payment_method.config.payment_type,
      payment_request.amount
    )
  end

  # 生成支付方式信息
  defp generate_payment_method_info(payment_method, fee_info, payment_request) do
    %{
      gateway: payment_method.gateway,
      config: payment_method.config,
      fee_info: fee_info,
      payment_request: payment_request,
      priority: payment_method.priority,
      recommended: true,
      available_payment_types: [@payment_types[payment_method.config.payment_type]],
      estimated_processing_time: "1-3分钟",
      success_rate: "99.5%"
    }
  end

  # ==================== 公共业务方法 ====================

  @doc """
  获取支付系统统计信息

  ## 参数
  - `requester_id` - 请求者ID
  - `date_range` - 日期范围
  - `options` - 选项参数

  ## 返回
  - `{:ok, stats}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_payment_system_statistics(requester_id, date_range \\ nil, options \\ []) do
    Logger.info("💳 [支付系统服务] 获取支付系统统计信息")

    try do
      # 1. 验证请求者权限
      with {:ok, _requester} <- verify_requester_permissions(requester_id, :view_statistics),
           # 2. 获取综合统计
           {:ok, comprehensive_stats} <- PaymentSystemQueryBuilder.get_payment_system_stats(date_range, options),
           # 3. 增强统计信息
           enhanced_stats <- enhance_statistics_info(comprehensive_stats, date_range) do

        Logger.info("✅ [支付系统服务] 支付系统统计信息获取成功")
        {:ok, enhanced_stats}
      else
        {:error, :permission_denied} ->
          Logger.error("❌ [支付系统服务] 查看统计权限不足: #{requester_id}")
          {:error, :permission_denied}

        error ->
          Logger.error("❌ [支付系统服务] 获取支付系统统计信息失败: #{inspect(error)}")
          {:error, :get_statistics_failed}
      end
    rescue
      exception ->
        Logger.error("💥 [支付系统服务] 获取支付系统统计信息异常: #{inspect(exception)}")
        {:error, :get_statistics_exception}
    end
  end

  @doc """
  测试支付网关连接

  ## 参数
  - `gateway_id` - 网关ID
  - `tester_id` - 测试者ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, test_result}` - 成功
  - `{:error, reason}` - 失败
  """
  def test_payment_gateway_connection(gateway_id, tester_id, options \\ []) do
    Logger.info("💳 [支付系统服务] 测试支付网关连接: #{gateway_id}")

    try do
      # 1. 验证测试者权限
      with {:ok, _tester} <- verify_requester_permissions(tester_id, :test_gateway),
           # 2. 获取网关信息
           {:ok, gateway} <- PaymentGatewayRepository.get_payment_gateway(gateway_id),
           {:ok, _} <- check_gateway_exists(gateway),
           # 3. 执行连接测试
           {:ok, test_result} <- perform_gateway_connection_test(gateway),
           # 4. 记录测试日志
           {:ok, _log} <- log_gateway_test_operation(tester_id, gateway_id, test_result) do

        Logger.info("✅ [支付系统服务] 支付网关连接测试完成: #{gateway.name}")
        {:ok, test_result}
      else
        {:error, :permission_denied} ->
          Logger.error("❌ [支付系统服务] 测试网关权限不足: #{tester_id}")
          {:error, :permission_denied}

        {:error, :gateway_not_found} ->
          Logger.error("❌ [支付系统服务] 网关不存在: #{gateway_id}")
          {:error, :gateway_not_found}

        error ->
          Logger.error("❌ [支付系统服务] 测试支付网关连接失败: #{inspect(error)}")
          {:error, :test_gateway_failed}
      end
    rescue
      exception ->
        Logger.error("💥 [支付系统服务] 测试支付网关连接异常: #{inspect(exception)}")
        {:error, :test_gateway_exception}
    end
  end

  # 增强统计信息
  defp enhance_statistics_info(stats, date_range) do
    Map.merge(stats, %{
      date_range: date_range,
      report_type: "comprehensive",
      health_score: calculate_system_health_score(stats),
      recommendations: generate_system_recommendations(stats)
    })
  end

  # 计算系统健康评分
  defp calculate_system_health_score(stats) do
    # 基于活跃网关比例、配置完整性等计算健康评分
    gateway_health = if stats.summary.total_gateways > 0 do
      stats.summary.active_gateways / stats.summary.total_gateways * 40
    else
      0
    end

    config_health = if stats.summary.total_configs > 0 do
      stats.summary.active_configs / stats.summary.total_configs * 30
    else
      0
    end

    exchange_health = if stats.summary.total_exchange_configs > 0 do
      stats.summary.active_exchange_configs / stats.summary.total_exchange_configs * 30
    else
      0
    end

    total_score = gateway_health + config_health + exchange_health
    round(total_score)
  end

  # 生成系统建议
  defp generate_system_recommendations(stats) do
    recommendations = []

    # 检查网关状态
    recommendations = if stats.summary.active_gateways < stats.summary.total_gateways do
      ["建议检查并激活未激活的支付网关" | recommendations]
    else
      recommendations
    end

    # 检查配置完整性
    recommendations = if stats.summary.active_configs < stats.summary.total_configs do
      ["建议检查并激活未激活的支付配置" | recommendations]
    else
      recommendations
    end

    # 检查兑换配置
    recommendations = if stats.summary.total_exchange_configs == 0 do
      ["建议添加兑换配置以支持兑换功能" | recommendations]
    else
      recommendations
    end

    if length(recommendations) == 0 do
      ["系统运行状态良好，无需特别关注"]
    else
      recommendations
    end
  end

  # 执行网关连接测试
  defp perform_gateway_connection_test(gateway) do
    Logger.debug("🔍 [支付系统服务] 执行网关连接测试: #{gateway.name}")

    # 这里应该实现实际的网关连接测试逻辑
    test_result = %{
      gateway_id: gateway.id,
      gateway_name: gateway.name,
      test_time: DateTime.utc_now(),
      connection_status: :success,
      response_time: :rand.uniform(500) + 100,  # 模拟响应时间
      test_details: %{
        api_reachable: true,
        authentication_valid: true,
        configuration_correct: true
      },
      recommendations: []
    }

    {:ok, test_result}
  end

  # 记录网关测试操作日志
  defp log_gateway_test_operation(tester_id, gateway_id, test_result) do
    Logger.info("📝 [支付系统服务] 记录网关测试操作: #{gateway_id}")
    {:ok, %{id: "test_log_#{:rand.uniform(1000)}", operation: :test}}
  end
end
