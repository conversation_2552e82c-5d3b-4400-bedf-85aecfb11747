# 积分转账余额验证修复

## 问题描述

用户报告管理员只有100,000积分，却能给用户转账10,000,000积分的问题。

## 问题分析

经过调查发现了两个主要问题：

### 1. 管理员"转账"实际上是系统充值

管理员在用户管理界面调整用户积分时，使用的是 `Cypridina.Accounts.add_points()` 函数，这个函数实际上是从系统的 `:rewards` 账户转账给用户，而不是从管理员个人账户转账。

**代码路径：**
- `lib/racing_game/live/admin_panel/user_management_component.ex` -> `adjust_user_points()`
- `lib/cypridina/accounts.ex` -> `add_points()`
- `lib/cypridina/ledger.ex` -> `add_user_points()`

**系统账户状态：**
```elixir
# 系统奖励账户余额为负数，说明已经"透支"
system_balance = Money.new(:XAA, "-********")  # -51,099,905积分
```

### 2. ash_double_entry缺少余额验证

ash_double_entry库允许账户余额变为负数，没有进行余额充足性验证。

**测试结果：**
```elixir
# 用户只有50,000积分，但能成功转账100,000积分
user1_balance = 50000
transfer_amount = 100000
result = {:ok, %{from_asset: %{points: -50000}, to_asset: %{points: 150000}}}
```

## 解决方案

### 1. 为用户间转账添加余额验证

修改 `lib/cypridina/ledger.ex` 中的 `transfer_user_points/4` 函数：

```elixir
def transfer_user_points(%{point_account: from_account} = from_user, %{point_account: to_account}, amount, opts \\ []) do
  # 验证转账金额
  if amount <= 0 do
    {:error, "转账金额必须大于0"}
  else
    # 检查发送方余额是否充足
    current_balance = get_user_balance(from_user, :XAA)
    balance_amount = Money.to_decimal(current_balance) |> Decimal.to_integer()
    
    if balance_amount < amount do
      {:error, "余额不足"}
    else
      # 执行转账...
    end
  end
end
```

### 2. 为用户消费积分添加余额验证

修改 `lib/cypridina/ledger.ex` 中的 `subtract_user_points/3` 函数：

```elixir
def subtract_user_points(%{point_account: user_account} = user, amount, opts \\ []) do
  # 验证消费金额
  if amount <= 0 do
    {:error, "消费金额必须大于0"}
  else
    # 检查是否为管理员操作（跳过余额检查）
    is_admin_operation = Keyword.get(opts, :skip_balance_check, false) or 
                        (get_in(Keyword.get(opts, :metadata, %{}), [:admin_operation]) == true)
    
    if not is_admin_operation do
      # 检查用户余额是否充足
      current_balance = get_user_balance(user, :XAA)
      balance_amount = Money.to_decimal(current_balance) |> Decimal.to_integer()
      
      if balance_amount < amount do
        {:error, "余额不足"}
      else
        do_subtract_user_points(user_account, amount, opts)
      end
    else
      # 管理员操作跳过余额检查
      do_subtract_user_points(user_account, amount, opts)
    end
  end
end
```

## 测试验证

### 修复前
```elixir
# 用户间转账 - 允许透支
user1_balance = 50000
transfer_result = Cypridina.Accounts.transfer_points(user1.id, user2.id, 100000, "测试")
# 结果：{:ok, %{from_asset: %{points: -50000}, to_asset: %{points: 150000}}}
```

### 修复后
```elixir
# 用户间转账 - 余额不足时拒绝
user3_balance = 50000
transfer_result = Cypridina.Accounts.transfer_points(user3.id, user4.id, 100000, "测试")
# 结果：{:error, "余额不足"}

# 正常转账 - 成功
normal_transfer = Cypridina.Accounts.transfer_points(user3.id, user4.id, 10000, "正常转账")
# 结果：{:ok, %{from_asset: %{points: 40000}, to_asset: %{points: 60000}}}
```

### 管理员操作仍然正常
```elixir
# 管理员减少积分 - 可以超过用户余额
admin_subtract = Cypridina.Accounts.subtract_points(user5.id, 60000, [
  transaction_type: :admin_operation,
  metadata: %{admin_operation: true}
])
# 结果：成功，余额变为-10000

# 管理员增加积分 - 从系统账户转入
admin_add = Cypridina.Accounts.add_points(user5.id, 100000, [
  transaction_type: :admin_operation,
  metadata: %{admin_operation: true}
])
# 结果：成功，余额变为90000
```

## 业务逻辑说明

1. **管理员调整积分**：这是系统充值/扣除操作，从系统账户进行，不受管理员个人余额限制
2. **用户间转账**：真正的用户间转账，需要验证发送方余额充足
3. **游戏消费**：需要验证用户余额充足，除非是管理员强制操作

## 影响范围

- ✅ 用户间转账：现在会验证余额
- ✅ 游戏投注：现在会验证余额  
- ✅ 管理员操作：仍然可以跳过余额检查
- ✅ 系统充值：不受影响

## 后续建议

1. **系统账户管理**：考虑为系统账户设置合理的初始余额
2. **界面优化**：在管理员界面明确区分"系统充值"和"用户转账"
3. **审计日志**：加强对管理员操作的审计记录
4. **权限控制**：考虑为不同级别的管理员设置不同的操作限额
