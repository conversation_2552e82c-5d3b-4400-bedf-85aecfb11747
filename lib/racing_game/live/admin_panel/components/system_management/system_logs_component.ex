defmodule RacingGame.Live.AdminPanel.SystemLogsComponent do
  @moduledoc """
  系统日志管理组件 - 记录和展示系统操作日志、错误日志等事件
  """
  use CypridinaWeb, :live_component

  alias CypridinaWeb.AuthHelper
  alias Cypridina.Utils.TimeHelper
  alias Cypridina.Utils.OperationLogger
  require Ash.Query
  require Logger

  @log_types [
    {:all, "所有类型"},
    {"login", "登录日志"},
    {"logout", "退出日志"},
    {"create", "创建操作"},
    {"update", "更新操作"},
    {"delete", "删除操作"},
    {"view", "查看操作"}
  ]

  def update(assigns, socket) do
    socket = assign(socket, assigns)

    if AuthHelper.has_permission?(socket.assigns.current_user, :admin) do
      socket =
        socket
        |> assign_defaults()
        |> load_logs_data()

      {:ok, socket}
    else
      # 非管理员用户显示无权限信息
      socket =
        socket
        |> assign_defaults()
        |> assign(:unauthorized, true)
      {:ok, socket}
    end
  end

  defp assign_defaults(socket) do
    socket
    |> assign(:logs, [])
    |> assign(:total_count, 0)
    |> assign(:search_query, "")
    |> assign(:selected_type, :all)
    |> assign(:start_date, nil)
    |> assign(:end_date, nil)
    |> assign(:page, 1)
    |> assign(:per_page, 10)
    |> assign(:log_types, @log_types)
    |> assign(:unauthorized, false)
  end

  def handle_event("refresh", _params, socket) do
    current_user = socket.assigns.current_user

    # 记录刷新日志操作
    client_info = OperationLogger.extract_client_info(socket)
    OperationLogger.log_view(
      current_user.id,
      "系统日志",
      "刷新系统日志列表",
      client_info
    )

    socket = load_logs_data(socket)
    {:noreply, socket}
  end

  def handle_event("search", %{"search" => params}, socket) do
    current_user = socket.assigns.current_user

    selected_type = case params["type"] do
      "" -> :all
      nil -> :all
      type -> type
    end

    # 记录搜索日志操作
    client_info = OperationLogger.extract_client_info(socket)
    OperationLogger.log_view(
      current_user.id,
      "系统日志",
      "搜索系统日志: #{params["query"] || ""}",
      [
        request_data: %{
          query: params["query"] || "",
          type: selected_type,
          start_date: params["start_date"],
          end_date: params["end_date"]
        }
      ] ++ client_info
    )

    socket = socket
    |> assign(:search_query, params["query"] || "")
    |> assign(:selected_type, selected_type)
    |> assign(:start_date, parse_empty_string(params["start_date"]))
    |> assign(:end_date, parse_empty_string(params["end_date"]))
    |> assign(:page, 1)
    |> load_logs_data()

    {:noreply, socket}
  end

  def handle_event("clear_search", _params, socket) do
    socket = socket
    |> assign(:search_query, "")
    |> assign(:selected_type, :all)
    |> assign(:start_date, nil)
    |> assign(:end_date, nil)
    |> assign(:page, 1)
    |> load_logs_data()

    {:noreply, socket}
  end

  def handle_event("page_change", %{"page" => page}, socket) do
    socket = socket
    |> assign(:page, String.to_integer(page))
    |> load_logs_data()

    {:noreply, socket}
  end

  defp load_logs_data(socket) do
    # 使用模拟数据（实际项目中应该从数据库获取）
    all_logs = [
      %{
        id: 1,
        operation_type: "login",
        admin_user_id: "admin",
        module: "用户管理",
        ip_address: "*************",
        description: "管理员登录系统",
        inserted_at: ~N[2024-01-15 10:30:00]
      },
      %{
        id: 2,
        operation_type: "create",
        admin_user_id: "admin",
        module: "用户管理",
        ip_address: "*************",
        description: "创建新用户：user123",
        inserted_at: ~N[2024-01-15 10:25:00]
      },
      %{
        id: 3,
        operation_type: "update",
        admin_user_id: "admin",
        module: "系统设置",
        ip_address: "*************",
        description: "更新系统配置参数",
        inserted_at: ~N[2024-01-15 10:20:00]
      },
      %{
        id: 4,
        operation_type: "delete",
        admin_user_id: "admin",
        module: "用户管理",
        ip_address: "*************",
        description: "删除用户：user456",
        inserted_at: ~N[2024-01-15 10:15:00]
      },
      %{
        id: 5,
        operation_type: "view",
        admin_user_id: "admin",
        module: "系统日志",
        ip_address: "*************",
        description: "查看系统日志",
        inserted_at: ~N[2024-01-15 10:10:00]
      },
      %{
        id: 6,
        operation_type: "logout",
        admin_user_id: "admin",
        module: "用户管理",
        ip_address: "*************",
        description: "管理员退出系统",
        inserted_at: ~N[2024-01-15 10:05:00]
      }
    ]

    # 应用过滤条件
    filtered_logs = apply_filters(all_logs, socket.assigns)

    # 按时间倒序排列
    sorted_logs = Enum.sort_by(filtered_logs, & &1.inserted_at, {:desc, NaiveDateTime})

    # 分页处理
    total_count = length(sorted_logs)
    page = Map.get(socket.assigns, :page, 1)
    per_page = Map.get(socket.assigns, :per_page, 20)
    start_index = (page - 1) * per_page

    paged_logs =
      sorted_logs
      |> Enum.drop(start_index)
      |> Enum.take(per_page)

    socket
    |> assign(:logs, paged_logs)
    |> assign(:total_count, total_count)
  end

  defp apply_filters(logs, assigns) do
    logs
    |> filter_by_type(Map.get(assigns, :selected_type, :all))
    |> filter_by_search(Map.get(assigns, :search_query, ""))
    |> filter_by_date_range(Map.get(assigns, :start_date), Map.get(assigns, :end_date))
  end

  defp filter_by_type(logs, :all), do: logs
  defp filter_by_type(logs, ""), do: logs
  defp filter_by_type(logs, type) do
    Enum.filter(logs, &(&1.operation_type == type))
  end

  defp filter_by_search(logs, ""), do: logs
  defp filter_by_search(logs, nil), do: logs
  defp filter_by_search(logs, query) do
    query_lower = String.downcase(query)
    Enum.filter(logs, fn log ->
      String.contains?(String.downcase(log.description || ""), query_lower) or
      String.contains?(String.downcase(log.module || ""), query_lower)
    end)
  end

  defp filter_by_date_range(logs, nil, nil), do: logs
  defp filter_by_date_range(logs, start_date, end_date) do
    start_datetime = if start_date, do: parse_date(start_date), else: nil
    end_datetime = if end_date, do: parse_date(end_date, true), else: nil

    Enum.filter(logs, fn log ->
      log_datetime = DateTime.from_naive!(log.inserted_at, "Etc/UTC")

      start_ok = if start_datetime, do: DateTime.compare(log_datetime, start_datetime) != :lt, else: true
      end_ok = if end_datetime, do: DateTime.compare(log_datetime, end_datetime) != :gt, else: true

      start_ok and end_ok
    end)
  end



  defp parse_empty_string(""), do: nil
  defp parse_empty_string(nil), do: nil
  defp parse_empty_string(value), do: value

  defp parse_date(date_str, end_of_day? \\ false) do
    case Date.from_iso8601(date_str) do
      {:ok, date} ->
        if end_of_day? do
          DateTime.new!(date, ~T[23:59:59], "Etc/UTC")
        else
          DateTime.new!(date, ~T[00:00:00], "Etc/UTC")
        end
      _ -> nil
    end
  end

  def render(assigns) do
    ~H"""
    <div class="p-6 bg-white rounded-lg shadow-md">
      <%= if @unauthorized do %>
        <h3 class="text-xl font-bold text-red-600 mb-4">权限不足</h3>
        <p class="text-gray-700">您没有访问系统日志的权限。只有管理员可以查看此页面。</p>
      <% else %>
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-xl font-bold text-gray-800">系统日志管理</h2>
          <div class="flex items-center gap-4">
            <div class="text-sm text-gray-600">
              共 <%= @total_count %> 条日志，第 <%= @page %> 页
            </div>
            <button
              phx-click="refresh"
              phx-target={@myself}
              class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
            >
              <i class="fas fa-sync-alt mr-2"></i>刷新
            </button>
          </div>
        </div>

        <!-- 搜索和过滤区域 -->
        <div class="bg-gray-50 p-4 rounded-lg mb-6">
          <form phx-submit="search" phx-target={@myself} class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">日志类型</label>
                <select name="search[type]" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                  <%= for {value, label} <- @log_types do %>
                    <option value={value} selected={@selected_type == value}><%= label %></option>
                  <% end %>
                </select>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">开始日期</label>
                <input type="date" name="search[start_date]" value={@start_date} class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">结束日期</label>
                <input type="date" name="search[end_date]" value={@end_date} class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" />
              </div>
            </div>

            <div class="flex gap-2">
              <input
                type="text"
                name="search[query]"
                value={@search_query}
                placeholder="搜索日志内容、模块..."
                class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors">
                <i class="fas fa-search mr-2"></i>搜索
              </button>
              <%= if @search_query != "" or @selected_type != :all or not is_nil(@start_date) or not is_nil(@end_date) do %>
                <button type="button" phx-click="clear_search" phx-target={@myself} class="px-4 py-2 bg-white text-black border border-black rounded-md hover:bg-gray-100 transition-colors">
                  <i class="fas fa-times mr-2"></i>清除
                </button>
              <% end %>
            </div>
          </form>
        </div>

        <!-- 日志列表 -->
        <div class="overflow-x-auto">
          <table class="min-w-full bg-white border border-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">时间</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">管理员</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">模块</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP地址</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">详情</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <%= if Enum.empty?(@logs) do %>
                <tr>
                  <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                    没有找到匹配的日志记录
                  </td>
                </tr>
              <% else %>
                <%= for log <- @logs do %>
                  <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <%= TimeHelper.format_local_datetime(log.inserted_at) %>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class={log_type_badge_class(log.operation_type)}>
                        <%= log_type_label(log.operation_type) %>
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <%= log.admin_user_id || "系统" %>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <%= log.module %>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <%= log.ip_address || "-" %>
                    </td>
                    <td class="px-6 py-4 text-sm text-gray-500 max-w-xs truncate" title={log.description || ""}>
                      <%= log.description %>
                    </td>
                  </tr>
                <% end %>
              <% end %>
            </tbody>
          </table>
        </div>

        <!-- 分页控件 -->
        <%= if @total_count > @per_page do %>
          <div class="mt-4 flex justify-between items-center">
            <div class="text-sm text-gray-700">
              显示第 <%= (@page - 1) * @per_page + 1 %> - <%= min(@page * @per_page, @total_count) %> 条，共 <%= @total_count %> 条记录
            </div>
            <div class="flex gap-2">
              <%= if @page > 1 do %>
                <button
                  phx-click="page_change"
                  phx-value-page={@page - 1}
                  phx-target={@myself}
                  class="px-3 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
                >
                  上一页
                </button>
              <% end %>
              <span class="px-3 py-1 bg-blue-500 text-white rounded">
                <%= @page %>
              </span>
              <%= if @page * @per_page < @total_count do %>
                <button
                  phx-click="page_change"
                  phx-value-page={@page + 1}
                  phx-target={@myself}
                  class="px-3 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
                >
                  下一页
                </button>
              <% end %>
            </div>
          </div>
        <% end %>
      <% end %>
    </div>
    """
  end

  defp log_type_label(type) do
    Enum.find_value(@log_types, fn {key, label} -> if key == type, do: label end) || to_string(type)
  end

  defp log_type_badge_class("login"), do: "px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800"
  defp log_type_badge_class("logout"), do: "px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800"
  defp log_type_badge_class("create"), do: "px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800"
  defp log_type_badge_class("update"), do: "px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800"
  defp log_type_badge_class("delete"), do: "px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800"
  defp log_type_badge_class("view"), do: "px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800"
  defp log_type_badge_class(_), do: "px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800"
end
