defmodule Teen.ActivitySystem do
  @moduledoc """
  活动管理域

  包含签到活动、CDKEY活动、限时礼包、Free Bonus、Free Cash、破产补助等功能
  """

  use Ash.Domain,
    otp_app: :cypridina,
    extensions: [AshAdmin.Domain]

  alias Teen.ActivitySystem.{SignInActivity, CdkeyActivity, LimitedGift, FreeBonus, FreeCash, BankruptcyAssist, TaskLevel, LevelReward, RewardMultiplier}
  alias Cypridina.Accounts.User

  admin do
    show? true
  end

  resources do
    resource Teen.ActivitySystem.SignInActivity
    resource Teen.ActivitySystem.CdkeyActivity
    resource Teen.ActivitySystem.LimitedGift
    resource Teen.ActivitySystem.FreeBonus
    resource Teen.ActivitySystem.FreeCash
    resource Teen.ActivitySystem.BankruptcyAssist
    resource Teen.ActivitySystem.TaskLevel
    resource Teen.ActivitySystem.LevelReward
    resource Teen.ActivitySystem.RewardMultiplier
  end

  # ==================== 业务逻辑函数 ====================

  @doc """
  处理用户签到
  """
  def process_user_sign_in(user_id, activity_id \\ nil) do
    with {:ok, user} <- User.read(user_id),
         {:ok, activity} <- get_current_sign_in_activity(activity_id),
         {:ok, sign_in_info} <- get_user_sign_in_info(user_id, activity.id),
         :ok <- validate_can_sign_in(sign_in_info),
         {:ok, rewards} <- calculate_sign_in_rewards(user, activity, sign_in_info) do

      # 更新用户签到信息
      updated_info = update_sign_in_info(sign_in_info, rewards)

      # 发放奖励
      distribute_rewards(user, rewards)

      {:ok, %{
        rewards: rewards,
        consecutive_days: updated_info.consecutive_days,
        total_sign_days: updated_info.total_sign_days,
        next_reward: get_next_day_reward(activity, updated_info.consecutive_days + 1)
      }}
    end
  end

  @doc """
  获取用户签到状态
  """
  def get_user_sign_in_status(user_id, activity_id \\ nil) do
    with {:ok, activity} <- get_current_sign_in_activity(activity_id),
         {:ok, sign_in_info} <- get_user_sign_in_info(user_id, activity.id) do

      today_signed = is_today_signed?(sign_in_info)
      can_sign_in = not today_signed

      {:ok, %{
        activity: activity,
        consecutive_days: sign_in_info.consecutive_days,
        total_sign_days: sign_in_info.total_sign_days,
        today_signed: today_signed,
        can_sign_in: can_sign_in,
        next_reward: if(can_sign_in, do: get_next_day_reward(activity, sign_in_info.consecutive_days + 1), else: nil)
      }}
    end
  end

  @doc """
  使用CDKEY兑换码
  """
  def use_cdkey(user_id, cdkey_string) do
    with {:ok, user} <- User.read(user_id),
         {:ok, cdkey} <- find_valid_cdkey(cdkey_string),
         :ok <- validate_cdkey_usage(cdkey, user),
         {:ok, rewards} <- parse_cdkey_rewards(cdkey.reward_config),
         {:ok, updated_cdkey} <- CdkeyActivity.use_cdkey(cdkey, %{user_id: user_id}) do

      # 发放奖励
      distribute_rewards(user, rewards)

      # 记录使用日志
      log_cdkey_usage(user, updated_cdkey, rewards)

      {:ok, %{
        cdkey: updated_cdkey,
        rewards: rewards,
        message: "CDKEY兑换成功"
      }}
    end
  end

  @doc """
  批量生成CDKEY
  """
  def generate_cdkey_batch(batch_name, quantity, reward_config, expires_at \\ nil) do
    cdkeys = Enum.map(1..quantity, fn _index ->
      %{
        batch_name: batch_name,
        reward_config: reward_config,
        expires_at: expires_at,
        status: 0
      }
    end)

    # 批量创建CDKEY
    results = Enum.map(cdkeys, fn attrs ->
      CdkeyActivity.generate_batch(attrs)
    end)

    {successes, failures} = Enum.split_with(results, fn
      {:ok, _} -> true
      _ -> false
    end)

    {:ok, %{
      batch_name: batch_name,
      total_generated: quantity,
      success_count: length(successes),
      failure_count: length(failures),
      cdkeys: Enum.map(successes, fn {:ok, cdkey} -> cdkey end)
    }}
  end

  @doc """
  获取活动统计信息
  """
  def get_activity_statistics(_activity_id, _date_range \\ nil) do
    # 这里应该实现具体的统计逻辑
    {:ok, %{
      total_participants: 1000,
      daily_participants: 150,
      total_rewards_distributed: Decimal.new("50000"),
      average_consecutive_days: 5.2,
      activity_engagement_rate: Decimal.new("75.5")
    }}
  end

  @doc """
  检查CDKEY批次状态
  """
  def get_cdkey_batch_status(batch_name) do
    case CdkeyActivity.list_by_batch(batch_name) do
      {:ok, cdkeys} ->
        total_count = length(cdkeys)
        used_count = Enum.count(cdkeys, & &1.status == 1)
        expired_count = Enum.count(cdkeys, & &1.status == 2)
        unused_count = total_count - used_count - expired_count

        {:ok, %{
          batch_name: batch_name,
          total_count: total_count,
          used_count: used_count,
          unused_count: unused_count,
          expired_count: expired_count,
          usage_rate: if(total_count > 0, do: Decimal.div(used_count, total_count), else: Decimal.new("0"))
        }}

      {:error, reason} ->
        {:error, reason}
    end
  end

  # ==================== 私有函数 ====================

  defp get_current_sign_in_activity(nil) do
    case SignInActivity.list_current_activities() do
      {:ok, [activity | _]} -> {:ok, activity}
      {:ok, []} -> {:error, "No active sign-in activity"}
      {:error, reason} -> {:error, reason}
    end
  end

  defp get_current_sign_in_activity(activity_id) do
    SignInActivity.read(activity_id)
  end

  defp get_user_sign_in_info(user_id, activity_id) do
    # 这里应该从用户签到记录表中获取信息
    {:ok, %{
      user_id: user_id,
      activity_id: activity_id,
      consecutive_days: 3,
      total_sign_days: 15,
      last_sign_date: Date.add(Date.utc_today(), -1)
    }}
  end

  defp validate_can_sign_in(sign_in_info) do
    if is_today_signed?(sign_in_info) do
      {:error, "Already signed in today"}
    else
      :ok
    end
  end

  defp is_today_signed?(sign_in_info) do
    Date.compare(sign_in_info.last_sign_date, Date.utc_today()) == :eq
  end

  defp calculate_sign_in_rewards(user, activity, sign_in_info) do
    consecutive_days = if is_consecutive_sign_in?(sign_in_info) do
      sign_in_info.consecutive_days + 1
    else
      1
    end

    # 获取基础奖励
    base_rewards = get_daily_reward(activity, consecutive_days)

    # 计算VIP加成
    vip_bonus_rate = activity.vip_bonus_rate || Decimal.new("0")
    vip_bonus = if user.vip_level && user.vip_level > 0 do
      Decimal.mult(base_rewards.coins, Decimal.div(vip_bonus_rate, 100))
    else
      Decimal.new("0")
    end

    # 检查连续签到奖励
    consecutive_bonus = get_consecutive_reward(activity, consecutive_days)

    {:ok, %{
      base_coins: base_rewards.coins,
      vip_bonus: vip_bonus,
      consecutive_bonus: consecutive_bonus,
      total_coins: Decimal.add(Decimal.add(base_rewards.coins, vip_bonus), consecutive_bonus),
      consecutive_days: consecutive_days
    }}
  end

  defp is_consecutive_sign_in?(sign_in_info) do
    yesterday = Date.add(Date.utc_today(), -1)
    Date.compare(sign_in_info.last_sign_date, yesterday) == :eq
  end

  defp get_daily_reward(activity, day) do
    daily_rewards = activity.daily_rewards || []

    reward = Enum.find(daily_rewards, fn reward ->
      reward["day"] == day
    end)

    if reward do
      %{coins: Decimal.new(reward["coins"] || "0")}
    else
      %{coins: Decimal.new("100")} # 默认奖励
    end
  end

  defp get_consecutive_reward(activity, consecutive_days) do
    consecutive_rewards = activity.consecutive_rewards || []

    reward = Enum.find(consecutive_rewards, fn reward ->
      reward["days"] == consecutive_days
    end)

    if reward do
      Decimal.new(reward["bonus"] || "0")
    else
      Decimal.new("0")
    end
  end

  defp get_next_day_reward(activity, next_day) do
    get_daily_reward(activity, next_day)
  end

  defp update_sign_in_info(sign_in_info, rewards) do
    # 这里应该更新数据库中的签到信息
    %{
      sign_in_info |
      consecutive_days: rewards.consecutive_days,
      total_sign_days: sign_in_info.total_sign_days + 1,
      last_sign_date: Date.utc_today()
    }
  end

  defp find_valid_cdkey(_cdkey_string) do
    {:error, "CDKEY not found or invalid"}
  end

  defp validate_cdkey_usage(cdkey, _user) do
    cond do
      cdkey.status != 0 ->
        {:error, "CDKEY already used or expired"}

      cdkey.expires_at && DateTime.compare(cdkey.expires_at, DateTime.utc_now()) == :lt ->
        {:error, "CDKEY has expired"}

      cdkey.used_count >= cdkey.usage_limit ->
        {:error, "CDKEY usage limit reached"}

      true ->
        :ok
    end
  end

  defp parse_cdkey_rewards(reward_config) do
    {:ok, %{
      coins: Decimal.new(reward_config["coins"] || "0"),
      items: reward_config["items"] || []
    }}
  end

  defp distribute_rewards(_user, _rewards), do: :ok
  defp log_cdkey_usage(_user, _cdkey, _rewards), do: :ok
end
