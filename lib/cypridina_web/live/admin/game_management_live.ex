defmodule CypridinaWeb.Admin.GameManagementLive do
  @moduledoc """
  游戏管理后台界面

  功能：
  - 查看已注册的游戏
  - 动态注册新游戏
  - 查看游戏统计
  - 管理游戏配置
  """

  use CypridinaWeb, :live_view
  alias Cypridina.RoomSystem.{GameRegistry, GameFactory}

  @impl true
  def mount(_params, _session, socket) do
    socket =
      socket
      |> assign(:games, GameRegistry.list_games())
      |> assign(:stats, GameRegistry.get_game_stats())
      |> assign(:page_title, "游戏管理")

    {:ok, socket}
  end

  @impl true
  def handle_event("refresh", _params, socket) do
    socket =
      socket
      |> assign(:games, GameRegistry.list_games())
      |> assign(:stats, GameRegistry.get_game_stats())

    {:noreply, socket}
  end

  @impl true
  def handle_event("register_baccarat", _params, socket) do
    case GameRegistry.hot_register_game(Cypridina.Teen.GameSystem.Games.Baccarat.BaccaratGame) do
      :ok ->
        socket =
          socket
          |> put_flash(:info, "百家乐游戏注册成功！")
          |> assign(:games, GameRegistry.list_games())
          |> assign(:stats, GameRegistry.get_game_stats())

        {:noreply, socket}

      {:error, reason} ->
        socket = put_flash(socket, :error, "注册失败: #{inspect(reason)}")
        {:noreply, socket}
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="p-6">
      <div class="mb-6">
        <h1 class="text-3xl font-bold text-gray-900">游戏管理</h1>
        <p class="mt-2 text-gray-600">管理系统中的游戏类型和配置</p>
      </div>

      <!-- 统计信息 -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                <span class="text-white font-bold">🎮</span>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">总游戏数</p>
              <p class="text-2xl font-semibold text-gray-900"><%= @stats.total_games %></p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                <span class="text-white font-bold">🏛️</span>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">百人场游戏</p>
              <p class="text-2xl font-semibold text-gray-900"><%= @stats.lobby_games %></p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                <span class="text-white font-bold">🃏</span>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">桌游</p>
              <p class="text-2xl font-semibold text-gray-900"><%= @stats.table_games %></p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                <span class="text-white font-bold">🔢</span>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">支持的游戏ID</p>
              <p class="text-2xl font-semibold text-gray-900"><%= length(@stats.supported_game_ids) %></p>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="mb-6 flex space-x-4">
        <button
          phx-click="refresh"
          class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
        >
          刷新
        </button>

        <button
          phx-click="register_baccarat"
          class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
        >
          注册百家乐游戏
        </button>
      </div>

      <!-- 游戏列表 -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
          <h2 class="text-lg font-semibold text-gray-900">已注册的游戏</h2>
        </div>

        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  游戏类型
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  游戏名称
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  房间模块
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  类型
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  支持的游戏ID
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <%= for game <- @games do %>
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    <%= game.game_type %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <%= game.game_name %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-mono">
                    <%= inspect(game.room_module) %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class={[
                      "inline-flex px-2 py-1 text-xs font-semibold rounded-full",
                      if(game.is_lobby_game, do: "bg-green-100 text-green-800", else: "bg-blue-100 text-blue-800")
                    ]}>
                      <%= if game.is_lobby_game, do: "百人场", else: "桌游" %>
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <%= Enum.join(game.supported_game_ids, ", ") %>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 支持的游戏ID列表 -->
      <div class="mt-8 bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
          <h2 class="text-lg font-semibold text-gray-900">支持的游戏ID映射</h2>
        </div>
        <div class="p-6">
          <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            <%= for game_id <- @stats.supported_game_ids do %>
              <div class="bg-gray-50 rounded-lg p-3 text-center">
                <div class="text-lg font-bold text-gray-900"><%= game_id %></div>
                <div class="text-sm text-gray-500">
                  <%= GameFactory.get_game_type(game_id) %>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
    """
  end
end
