/* 往期数据容器样式 */
.history-container {
  background-color: #fef7e4;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin: 15px;
  overflow: hidden;
}

.history-container .alltitle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0e0b6;
}

.history-container .title {
  display: flex;
  align-items: center;
  font-weight: bold;
  font-size: 16px;
  color: #8c6823;
}

.history-container .title img {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}

.date-picker-container {
  display: flex;
  align-items: center;
}

.date-picker-container a {
  margin: 0 8px;
  color: #8c6823;
  text-decoration: none;
  font-size: 14px;
}

.date-picker-container a:hover {
  color: #e74c3c;
}

.date-picker-container .block {
  display: flex;
  align-items: center;
}

.date-picker-container input[type="date"] {
  padding: 4px 8px;
  border: 1px solid #f0e0b6;
  border-radius: 4px;
  background-color: #fff;
  color: #8c6823;
}

/* 参赛动物队列号样式 */
.playnum {
  padding: 15px;
}

.title-with-line {
  position: relative;
  margin-bottom: 20px;
  text-align: center;
}

.title-with-line .title {
  font-size: 16px;
  font-weight: bold;
  color: #8c6823;
  display: inline-block;
  padding: 0 20px;
  position: relative;
  z-index: 2;
  background-color: #fef7e4;
}

.title-with-line:after {
  content: '';
  display: block;
  height: 1px;
  background-color: #f0e0b6;
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  z-index: 1;
}

.playerimg {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  margin-top: 15px;
}

.image-gallery {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 15px;
}

.image-gallery img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  margin-bottom: 5px;
  background-color: #fff;
  padding: 2px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.playertitle {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 12px;
}

.playertitle a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-bottom: 3px;
  color: white;
  text-decoration: none;
}

.playertitle a.one {
  background-color: #e74c3c;
}

.playertitle a.two {
  background-color: #3498db;
}

.playertitle a.three {
  background-color: #2ecc71;
}

.playertitle a.four {
  background-color: #f39c12;
}

.playertitle a.five {
  background-color: #9b59b6;
}

.playertitle a.six {
  background-color: #1abc9c;
}

/* 表格样式 */
.table-container {
  padding: 15px;
  overflow-x: auto;
}

.table-container table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.table-container th,
.table-container td {
  padding: 10px;
  text-align: center;
  border-bottom: 1px solid #f0e0b6;
}

.table-container th {
  background-color: #f8f0d9;
  font-weight: bold;
  color: #8c6823;
}

.table-container tr:nth-child(even) {
  background-color: #fefbf5;
}

.table-container .table-date {
  font-weight: bold;
  color: #8c6823;
}

.table-container .time {
  color: #8c6823;
}

.table-container .front {
  margin-bottom: 5px;
}

.table-container .front img {
  width: 30px;
  height: 30px;
  border-radius: 50%;
}

.table-container .back {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin: 0 auto;
  font-size: 12px;
  font-weight: bold;
}

.table-container .circle-bg.one {
  background-color: #e74c3c;
}

.table-container .circle-bg.two {
  background-color: #3498db;
}

.table-container .circle-bg.three {
  background-color: #2ecc71;
}

.table-container .circle-bg.four {
  background-color: #f39c12;
}

.table-container .circle-bg.five {
  background-color: #9b59b6;
}

.table-container .circle-bg.six {
  background-color: #1abc9c;
}

/* 分页样式 */
.pagination {
  display: flex;
  justify-content: center;
  margin: 20px 0 10px;
}

.el-pagination {
  display: flex;
  align-items: center;
  justify-content: center;
}

.el-pagination button {
  padding: 0 6px;
  min-width: 28px;
  height: 28px;
  line-height: 28px;
  cursor: pointer;
  border: 1px solid #e4e7ed;
  background-color: #fff;
  color: #606266;
  margin: 0 5px;
  border-radius: 3px;
}

.el-pagination button:hover:not(:disabled) {
  color: #409eff;
}

.el-pagination button:disabled {
  cursor: not-allowed;
  color: #c0c4cc;
}

.el-pagination .el-pager {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.el-pagination .el-pager li {
  min-width: 28px;
  height: 28px;
  line-height: 28px;
  cursor: pointer;
  border: 1px solid #e4e7ed;
  background-color: #fff;
  margin: 0 5px;
  border-radius: 3px;
  text-align: center;
}

.el-pagination .el-pager li:hover:not(.is-active) {
  color: #409eff;
}

.el-pagination .el-pager li.is-active {
  background-color: #f39c12;
  border-color: #f39c12;
  color: white;
}

.el-pagination__jump {
  margin-left: 15px;
  font-size: 14px;
  color: #606266;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .history-container .alltitle {
    flex-direction: column;
    align-items: flex-start;
  }

  .date-picker-container {
    margin-top: 10px;
  }

  .playerimg {
    justify-content: flex-start;
  }

  .image-gallery {
    margin-right: 15px;
  }
}