defmodule RacingGame.Live.AdminPanel.Services.Accounts.UserService do
  @moduledoc """
  用户账户业务逻辑服务
  
  提供用户账户相关的业务逻辑：
  - 用户注册和认证
  - 用户信息管理
  - 用户状态管理
  - 业务规则验证
  - 事务协调
  """

  require Logger
  alias RacingGame.Live.AdminPanel.Repositories.{RepositoryManager}
  alias RacingGame.Live.AdminPanel.Repositories.Accounts.{
    UserRepository, 
    UserIdentityRepository, 
    AgentRelationshipRepository
  }
  alias RacingGame.Live.AdminPanel.Utils.{ValidationHelper, NotificationHelper}
  alias Cypridina.Accounts.User

  # 常量定义
  @valid_agent_levels -1..10
  @max_username_length 50
  @min_username_length 3
  @max_email_length 255

  # ============================================================================
  # 用户注册和认证
  # ============================================================================

  @doc """
  注册新用户

  ## 参数
  - `user_data` - 用户数据
  - `options` - 选项

  ## 返回
  - `{:ok, user}` - 成功
  - `{:error, reason}` - 失败
  """
  def register_user(user_data, options \\ []) do
    Logger.info("📝 [用户服务] 注册新用户: #{user_data[:username]}")
    
    with {:ok, validated_data} <- validate_registration_data(user_data),
         {:ok, enriched_data} <- enrich_registration_data(validated_data, options),
         {:ok, user} <- create_user_with_transaction(enriched_data),
         :ok <- send_registration_notifications(user, options) do
      
      Logger.info("✅ [用户服务] 用户注册成功: #{user.id}")
      {:ok, user}
    else
      {:error, reason} ->
        Logger.error("❌ [用户服务] 用户注册失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  创建游客用户

  ## 参数
  - `guest_data` - 游客数据
  - `options` - 选项

  ## 返回
  - `{:ok, user}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_guest_user(guest_data \\ %{}, options \\ []) do
    Logger.info("👤 [用户服务] 创建游客用户")
    
    with {:ok, enriched_data} <- enrich_guest_data(guest_data, options),
         {:ok, user} <- UserRepository.create_guest_user(enriched_data),
         :ok <- handle_guest_user_creation(user, options) do
      
      Logger.info("✅ [用户服务] 游客用户创建成功: #{user.id}")
      {:ok, user}
    else
      {:error, reason} ->
        Logger.error("❌ [用户服务] 游客用户创建失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  用户登录验证

  ## 参数
  - `credentials` - 登录凭据
  - `options` - 选项

  ## 返回
  - `{:ok, %{user: user, token: token}}` - 成功
  - `{:error, reason}` - 失败
  """
  def authenticate_user(credentials, options \\ []) do
    Logger.info("🔐 [用户服务] 用户登录验证")
    
    with {:ok, user} <- find_user_by_credentials(credentials),
         :ok <- validate_user_status(user),
         {:ok, token} <- generate_authentication_token(user, options),
         :ok <- record_login_activity(user, options) do
      
      Logger.info("✅ [用户服务] 用户登录成功: #{user.id}")
      {:ok, %{user: user, token: token}}
    else
      {:error, reason} ->
        Logger.error("❌ [用户服务] 用户登录失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  # ============================================================================
  # 用户信息管理
  # ============================================================================

  @doc """
  获取用户详情

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - `{:ok, user}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_details(user_id, options \\ []) do
    Logger.debug("🔍 [用户服务] 获取用户详情: #{user_id}")
    
    with {:ok, user} <- UserRepository.get_user_by_id(user_id, options),
         {:ok, enriched_user} <- enrich_user_details(user, options) do
      {:ok, enriched_user}
    else
      error -> error
    end
  end

  @doc """
  更新用户信息

  ## 参数
  - `user_id` - 用户ID
  - `update_data` - 更新数据
  - `updater_id` - 更新者ID

  ## 返回
  - `{:ok, user}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_user_info(user_id, update_data, updater_id) do
    Logger.info("✏️ [用户服务] 更新用户信息: #{user_id}")
    
    with {:ok, validated_data} <- validate_update_data(update_data),
         {:ok, user} <- UserRepository.get_user_by_id(user_id),
         :ok <- validate_update_permissions(user, updater_id),
         {:ok, updated_user} <- update_user_with_transaction(user, validated_data, updater_id),
         :ok <- send_update_notifications(updated_user, validated_data) do
      
      Logger.info("✅ [用户服务] 用户信息更新成功: #{user_id}")
      {:ok, updated_user}
    else
      error -> error
    end
  end

  @doc """
  分页查询用户列表

  ## 参数
  - `params` - 查询参数
  - `options` - 选项

  ## 返回
  - `{:ok, %{users: users, page_info: page_info}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_users(params \\ %{}, options \\ []) do
    Logger.debug("📋 [用户服务] 分页查询用户列表")
    
    # 应用业务过滤规则
    filtered_params = apply_business_filters(params, options)
    
    UserRepository.list_users_paginated(filtered_params)
  end

  @doc """
  搜索用户

  ## 参数
  - `search_term` - 搜索词
  - `options` - 选项

  ## 返回
  - `{:ok, users}` - 成功
  - `{:error, reason}` - 失败
  """
  def search_users(search_term, options \\ []) do
    Logger.debug("🔍 [用户服务] 搜索用户: #{search_term}")
    
    # 应用搜索业务规则
    search_options = apply_search_business_rules(search_term, options)
    
    UserRepository.search_users(search_term, search_options)
  end

  # ============================================================================
  # 用户状态管理
  # ============================================================================

  @doc """
  激活用户

  ## 参数
  - `user_id` - 用户ID
  - `operator_id` - 操作者ID

  ## 返回
  - `{:ok, user}` - 成功
  - `{:error, reason}` - 失败
  """
  def activate_user(user_id, operator_id) do
    Logger.info("🟢 [用户服务] 激活用户: #{user_id}")
    
    update_user_status(user_id, :active, operator_id)
  end

  @doc """
  停用用户

  ## 参数
  - `user_id` - 用户ID
  - `operator_id` - 操作者ID
  - `reason` - 停用原因

  ## 返回
  - `{:ok, user}` - 成功
  - `{:error, reason}` - 失败
  """
  def deactivate_user(user_id, operator_id, reason \\ nil) do
    Logger.info("🔴 [用户服务] 停用用户: #{user_id}")
    
    with {:ok, user} <- update_user_status(user_id, :inactive, operator_id),
         :ok <- record_deactivation_reason(user, reason, operator_id),
         :ok <- handle_user_deactivation(user) do
      {:ok, user}
    else
      error -> error
    end
  end

  @doc """
  批量更新用户状态

  ## 参数
  - `user_ids` - 用户ID列表
  - `status` - 状态
  - `operator_id` - 操作者ID

  ## 返回
  - `{:ok, results}` - 成功
  - `{:error, reason}` - 失败
  """
  def batch_update_user_status(user_ids, status, operator_id) do
    Logger.info("📦 [用户服务] 批量更新用户状态: #{length(user_ids)}个用户")
    
    operations = Enum.map(user_ids, fn user_id ->
      {:update_user, [user_id, %{status: status}]}
    end)
    
    case RepositoryManager.batch_execute(:user, operations) do
      {:ok, results} ->
        # 处理批量操作后续逻辑
        handle_batch_status_update(user_ids, status, operator_id)
        {:ok, results}
      error -> error
    end
  end

  # ============================================================================
  # 代理关系管理
  # ============================================================================

  @doc """
  设置用户代理关系

  ## 参数
  - `user_id` - 用户ID
  - `agent_id` - 代理ID
  - `operator_id` - 操作者ID

  ## 返回
  - `{:ok, relationship}` - 成功
  - `{:error, reason}` - 失败
  """
  def set_user_agent(user_id, agent_id, operator_id) do
    Logger.info("🔗 [用户服务] 设置用户代理关系: #{user_id} -> #{agent_id}")
    
    with :ok <- validate_agent_relationship(user_id, agent_id),
         {:ok, relationship_data} <- prepare_relationship_data(user_id, agent_id, operator_id),
         {:ok, relationship} <- AgentRelationshipRepository.create_relationship(relationship_data),
         :ok <- update_user_agent_level(user_id, agent_id),
         :ok <- send_agent_relationship_notifications(relationship) do
      
      Logger.info("✅ [用户服务] 代理关系设置成功")
      {:ok, relationship}
    else
      error -> error
    end
  end

  @doc """
  获取用户代理信息

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - `{:ok, agent_info}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_agent_info(user_id, options \\ []) do
    Logger.debug("🔍 [用户服务] 获取用户代理信息: #{user_id}")
    
    with {:ok, relationships} <- AgentRelationshipRepository.get_user_relationships(user_id, options),
         {:ok, agent_stats} <- calculate_agent_statistics(user_id),
         {:ok, hierarchy} <- get_agent_hierarchy_info(user_id) do
      
      agent_info = %{
        relationships: relationships,
        statistics: agent_stats,
        hierarchy: hierarchy
      }
      
      {:ok, agent_info}
    else
      error -> error
    end
  end

  # ============================================================================
  # 统计和分析
  # ============================================================================

  @doc """
  获取用户统计信息

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, statistics}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_statistics(options \\ []) do
    Logger.debug("📊 [用户服务] 获取用户统计")
    
    with {:ok, basic_stats} <- UserRepository.get_user_statistics(options),
         {:ok, business_stats} <- calculate_business_statistics(options),
         {:ok, trend_stats} <- calculate_trend_statistics(options) do
      
      combined_stats = Map.merge(basic_stats, %{
        business: business_stats,
        trends: trend_stats
      })
      
      {:ok, combined_stats}
    else
      error -> error
    end
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 验证注册数据
  defp validate_registration_data(user_data) do
    with :ok <- ValidationHelper.validate_required(user_data, [:username, :password]),
         :ok <- validate_username(user_data[:username]),
         :ok <- validate_password(user_data[:password]),
         :ok <- validate_email(user_data[:email]),
         :ok <- check_username_uniqueness(user_data[:username]) do
      {:ok, user_data}
    else
      error -> error
    end
  end

  # 验证用户名
  defp validate_username(username) when is_binary(username) do
    cond do
      String.length(username) < @min_username_length ->
        {:error, "用户名长度不能少于#{@min_username_length}字符"}
      String.length(username) > @max_username_length ->
        {:error, "用户名长度不能超过#{@max_username_length}字符"}
      not Regex.match?(~r/^[a-zA-Z0-9_]+$/, username) ->
        {:error, "用户名只能包含字母、数字和下划线"}
      true -> :ok
    end
  end
  defp validate_username(_), do: {:error, "用户名必须是字符串"}

  # 验证密码
  defp validate_password(password) when is_binary(password) do
    cond do
      String.length(password) < 6 ->
        {:error, "密码长度不能少于6字符"}
      String.length(password) > 128 ->
        {:error, "密码长度不能超过128字符"}
      true -> :ok
    end
  end
  defp validate_password(_), do: {:error, "密码必须是字符串"}

  # 验证邮箱
  defp validate_email(nil), do: :ok
  defp validate_email(email) when is_binary(email) do
    if String.length(email) <= @max_email_length and 
       Regex.match?(~r/^[^\s]+@[^\s]+\.[^\s]+$/, email) do
      :ok
    else
      {:error, "邮箱格式无效"}
    end
  end
  defp validate_email(_), do: {:error, "邮箱必须是字符串"}

  # 检查用户名唯一性
  defp check_username_uniqueness(username) do
    case UserRepository.get_user_by_username(username) do
      {:ok, _user} -> {:error, "用户名已存在"}
      {:error, :not_found} -> :ok
      {:error, _} -> {:error, "检查用户名失败"}
    end
  end

  # 丰富注册数据
  defp enrich_registration_data(user_data, options) do
    enriched = user_data
    |> Map.put_new(:agent_level, -1)
    |> Map.put_new(:client_uniq_id, generate_client_uniq_id())
    |> maybe_set_agent_info(options[:agent_id])
    
    {:ok, enriched}
  end

  # 丰富游客数据
  defp enrich_guest_data(guest_data, _options) do
    enriched = guest_data
    |> Map.put_new(:agent_level, -1)
    |> Map.put_new(:client_uniq_id, generate_client_uniq_id())
    
    {:ok, enriched}
  end

  # 生成客户端唯一标识
  defp generate_client_uniq_id do
    :crypto.strong_rand_bytes(16) |> Base.encode16(case: :lower)
  end

  # 可能设置代理信息
  defp maybe_set_agent_info(data, nil), do: data
  defp maybe_set_agent_info(data, agent_id) do
    Map.put(data, :agent_id, agent_id)
  end

  # 使用事务创建用户
  defp create_user_with_transaction(user_data) do
    operations = [
      {:create_user, [user_data]},
      {:create_point_account, []}  # 创建积分账户
    ]
    
    case RepositoryManager.transaction(:user, operations) do
      {:ok, [user, _account]} -> {:ok, user}
      {:error, reason} -> {:error, reason}
    end
  end

  # 发送注册通知
  defp send_registration_notifications(user, options) do
    if options[:send_welcome_email] do
      NotificationHelper.send_welcome_email(user)
    else
      :ok
    end
  end

  # 处理游客用户创建
  defp handle_guest_user_creation(_user, _options) do
    # 游客用户创建后的处理逻辑
    :ok
  end

  # 根据凭据查找用户
  defp find_user_by_credentials(%{username: username, password: password}) do
    with {:ok, user} <- UserRepository.get_user_by_username(username),
         :ok <- verify_password(user, password) do
      {:ok, user}
    else
      {:error, :not_found} -> {:error, :invalid_credentials}
      error -> error
    end
  end

  defp find_user_by_credentials(%{client_uniq_id: client_uniq_id}) do
    case UserRepository.get_user_by_client_uniq_id(client_uniq_id) do
      {:ok, user} -> {:ok, user}
      {:error, :not_found} -> {:error, :invalid_credentials}
      error -> error
    end
  end

  defp find_user_by_credentials(_), do: {:error, :invalid_credentials}

  # 验证密码
  defp verify_password(user, password) do
    if Bcrypt.verify_pass(password, user.hashed_password) do
      :ok
    else
      {:error, :invalid_password}
    end
  end

  # 验证用户状态
  defp validate_user_status(user) do
    # 这里可以添加用户状态检查逻辑
    # 例如：检查用户是否被禁用、是否需要验证等
    :ok
  end

  # 生成认证令牌
  defp generate_authentication_token(user, _options) do
    # 这里应该调用实际的令牌生成逻辑
    token = "fake_token_#{user.id}_#{:os.system_time(:millisecond)}"
    {:ok, token}
  end

  # 记录登录活动
  defp record_login_activity(_user, _options) do
    # 记录登录日志
    :ok
  end

  # 丰富用户详情
  defp enrich_user_details(user, options) do
    if options[:include_agent_info] do
      case get_user_agent_info(user.id) do
        {:ok, agent_info} -> {:ok, Map.put(user, :agent_info, agent_info)}
        {:error, _} -> {:ok, user}
      end
    else
      {:ok, user}
    end
  end

  # 验证更新数据
  defp validate_update_data(update_data) do
    # 验证更新数据的有效性
    {:ok, update_data}
  end

  # 验证更新权限
  defp validate_update_permissions(_user, _updater_id) do
    # 检查更新权限
    :ok
  end

  # 使用事务更新用户
  defp update_user_with_transaction(user, update_data, updater_id) do
    operations = [
      {:update_user, [user.id, update_data]},
      {:log_operation, [user.id, :update_user, updater_id]}
    ]
    
    case RepositoryManager.transaction(:user, operations) do
      {:ok, [updated_user, _log]} -> {:ok, updated_user}
      {:error, reason} -> {:error, reason}
    end
  end

  # 发送更新通知
  defp send_update_notifications(_user, _update_data) do
    # 发送更新通知
    :ok
  end

  # 应用业务过滤规则
  defp apply_business_filters(params, _options) do
    # 应用业务相关的过滤规则
    params
  end

  # 应用搜索业务规则
  defp apply_search_business_rules(_search_term, options) do
    # 应用搜索相关的业务规则
    options
  end

  # 更新用户状态
  defp update_user_status(user_id, status, operator_id) do
    update_data = %{status: status, updated_by: operator_id}
    UserRepository.update_user(user_id, update_data)
  end

  # 记录停用原因
  defp record_deactivation_reason(_user, _reason, _operator_id) do
    # 记录停用原因
    :ok
  end

  # 处理用户停用
  defp handle_user_deactivation(_user) do
    # 处理用户停用相关逻辑
    :ok
  end

  # 处理批量状态更新
  defp handle_batch_status_update(_user_ids, _status, _operator_id) do
    # 处理批量状态更新后续逻辑
    :ok
  end

  # 验证代理关系
  defp validate_agent_relationship(user_id, agent_id) do
    cond do
      user_id == agent_id -> {:error, :self_reference}
      true -> :ok
    end
  end

  # 准备关系数据
  defp prepare_relationship_data(user_id, agent_id, operator_id) do
    relationship_data = %{
      user_id: user_id,
      agent_id: agent_id,
      level: 1,  # 默认为一级代理
      is_active: true,
      created_by: operator_id
    }
    
    {:ok, relationship_data}
  end

  # 更新用户代理等级
  defp update_user_agent_level(user_id, agent_id) do
    # 计算并更新用户的代理等级
    with {:ok, agent} <- UserRepository.get_user_by_id(agent_id),
         new_level <- agent.agent_level + 1,
         {:ok, _user} <- UserRepository.update_user(user_id, %{agent_level: new_level}) do
      :ok
    else
      error -> error
    end
  end

  # 发送代理关系通知
  defp send_agent_relationship_notifications(_relationship) do
    # 发送代理关系建立通知
    :ok
  end

  # 计算代理统计
  defp calculate_agent_statistics(_user_id) do
    # 计算代理相关统计信息
    {:ok, %{}}
  end

  # 获取代理层级信息
  defp get_agent_hierarchy_info(_user_id) do
    # 获取代理层级结构信息
    {:ok, %{}}
  end

  # 计算业务统计
  defp calculate_business_statistics(_options) do
    # 计算业务相关统计
    {:ok, %{}}
  end

  # 计算趋势统计
  defp calculate_trend_statistics(_options) do
    # 计算趋势统计
    {:ok, %{}}
  end
end
