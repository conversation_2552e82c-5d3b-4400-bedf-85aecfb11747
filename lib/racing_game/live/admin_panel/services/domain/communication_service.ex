defmodule RacingGame.Live.AdminPanel.Services.Domain.CommunicationService do
  @moduledoc """
  通信管理领域服务
  
  提供系统通信相关的业务逻辑处理，包括：
  - 通信记录的创建、更新、删除
  - 通信状态管理
  - 通信数据查询和过滤
  - 通信权限控制
  """

  require Logger
  alias RacingGame.SystemCommunication
  alias RacingGame.Live.AdminPanel.Utils.Validators.CommunicationValidator
  alias RacingGame.Live.AdminPanel.Utils.Validators.DeleteConstraintValidator
  require Ash.Query

  # 常量定义
  @default_per_page 20
  @max_per_page 100
  @valid_types [:message, :announcement, :notification]
  @valid_priorities [:low, :medium, :high, :urgent]
  @valid_recipient_types ["all", "user", "agent", "admin"]

  # ============================================================================
  # 公共API
  # ============================================================================

  @doc """
  获取通信列表

  ## 参数
  - `current_user` - 当前用户
  - `params` - 查询参数
    - `:search_query` - 搜索关键词
    - `:type` - 通信类型过滤
    - `:status` - 状态过滤
    - `:page` - 页码
    - `:per_page` - 每页数量

  ## 返回
  - `{:ok, %{communications: communications, page_info: page_info}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_communications(current_user, params \\ %{}) do
    Logger.debug("📋 [通信服务] 获取通信列表")
    
    try do
      query_params = normalize_list_params(params)
      
      with {:ok, query} <- build_communications_query(current_user, query_params),
           {:ok, results} <- execute_communications_query(query, query_params) do
        Logger.info("✅ [通信服务] 成功获取 #{length(results.communications)} 条通信")
        {:ok, results}
      else
        {:error, reason} -> {:error, reason}
      end
    rescue
      error ->
        Logger.error("❌ [通信服务] 获取通信列表异常: #{inspect(error)}")
        {:error, "获取通信列表失败"}
    end
  end

  @doc """
  创建通信

  ## 参数
  - `current_user` - 当前用户
  - `communication_params` - 通信参数

  ## 返回
  - `{:ok, communication}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_communication(current_user, communication_params) do
    Logger.info("➕ [通信服务] 创建通信: #{communication_params["title"]}")
    
    with :ok <- validate_create_permission(current_user),
         {:ok, validated_params} <- CommunicationValidator.validate_communication_params(communication_params),
         {:ok, create_params} <- prepare_create_params(validated_params, current_user) do
      
      case SystemCommunication.create(create_params) do
        {:ok, communication} ->
          Logger.info("✅ [通信服务] 通信创建成功: #{communication.title}")
          {:ok, communication}
        {:error, changeset} ->
          Logger.error("❌ [通信服务] 通信创建失败: #{inspect(changeset)}")
          {:error, changeset}
      end
    else
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  更新通信

  ## 参数
  - `current_user` - 当前用户
  - `communication_id` - 通信ID
  - `communication_params` - 更新参数

  ## 返回
  - `{:ok, communication}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_communication(current_user, communication_id, communication_params) do
    Logger.info("✏️ [通信服务] 更新通信: #{communication_id}")
    
    with {:ok, communication} <- get_communication_by_id(communication_id),
         :ok <- validate_update_permission(current_user, communication),
         {:ok, validated_params} <- CommunicationValidator.validate_communication_params(communication_params),
         {:ok, update_params} <- prepare_update_params(validated_params) do
      
      case SystemCommunication.update(communication, update_params) do
        {:ok, updated_communication} ->
          Logger.info("✅ [通信服务] 通信更新成功: #{updated_communication.title}")
          {:ok, updated_communication}
        {:error, changeset} ->
          Logger.error("❌ [通信服务] 通信更新失败: #{inspect(changeset)}")
          {:error, changeset}
      end
    else
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  删除通信

  ## 参数
  - `current_user` - 当前用户
  - `communication_id` - 通信ID

  ## 返回
  - `{:ok, message}` - 成功
  - `{:error, reason}` - 失败
  """
  def delete_communication(current_user, communication_id) do
    Logger.info("🗑️ [通信服务] 删除通信: #{communication_id}")
    
    with {:ok, communication} <- get_communication_by_id(communication_id),
         :ok <- validate_delete_permission(current_user, communication),
         :ok <- DeleteConstraintValidator.check_delete_constraints(communication) do
      
      case SystemCommunication.destroy(communication) do
        :ok ->
          Logger.info("✅ [通信服务] 通信删除成功")
          {:ok, "通信删除成功"}
        {:error, error} ->
          Logger.error("❌ [通信服务] 通信删除失败: #{inspect(error)}")
          {:error, "删除失败"}
      end
    else
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  切换通信状态

  ## 参数
  - `current_user` - 当前用户
  - `communication_id` - 通信ID

  ## 返回
  - `{:ok, {communication, action_name}}` - 成功
  - `{:error, reason}` - 失败
  """
  def toggle_communication_status(current_user, communication_id) do
    Logger.info("🔄 [通信服务] 切换通信状态: #{communication_id}")
    
    with {:ok, communication} <- get_communication_by_id(communication_id),
         :ok <- validate_update_permission(current_user, communication) do
      
      new_status = !communication.active
      action_name = if new_status, do: "启用", else: "禁用"
      
      case SystemCommunication.update(communication, %{active: new_status}) do
        {:ok, updated_communication} ->
          Logger.info("✅ [通信服务] 状态切换成功: #{action_name}")
          {:ok, {updated_communication, action_name}}
        {:error, error} ->
          Logger.error("❌ [通信服务] 状态切换失败: #{inspect(error)}")
          {:error, "状态切换失败"}
      end
    else
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  获取通信详情

  ## 参数
  - `current_user` - 当前用户
  - `communication_id` - 通信ID

  ## 返回
  - `{:ok, communication}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_communication_details(current_user, communication_id) do
    Logger.debug("📄 [通信服务] 获取通信详情: #{communication_id}")
    
    with {:ok, communication} <- get_communication_by_id(communication_id),
         :ok <- validate_view_permission(current_user, communication) do
      {:ok, communication}
    else
      {:error, reason} -> {:error, reason}
    end
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 标准化列表参数
  defp normalize_list_params(params) do
    %{
      search_query: String.trim(params[:search_query] || ""),
      type: normalize_type_filter(params[:type]),
      status: normalize_status_filter(params[:status]),
      page: max(1, params[:page] || 1),
      per_page: min(params[:per_page] || @default_per_page, @max_per_page)
    }
  end

  # 标准化类型过滤
  defp normalize_type_filter(nil), do: nil
  defp normalize_type_filter("all"), do: nil
  defp normalize_type_filter(type) when is_binary(type) do
    type_atom = String.to_atom(type)
    if type_atom in @valid_types, do: type_atom, else: nil
  end
  defp normalize_type_filter(type) when type in @valid_types, do: type
  defp normalize_type_filter(_), do: nil

  # 标准化状态过滤
  defp normalize_status_filter(nil), do: nil
  defp normalize_status_filter("all"), do: nil
  defp normalize_status_filter("true"), do: true
  defp normalize_status_filter("false"), do: false
  defp normalize_status_filter("active"), do: true
  defp normalize_status_filter("inactive"), do: false
  defp normalize_status_filter(status) when is_boolean(status), do: status
  defp normalize_status_filter(_), do: nil

  # 构建通信查询
  defp build_communications_query(current_user, params) do
    try do
      query = SystemCommunication
      |> apply_search_filter(params.search_query)
      |> apply_type_filter(params.type)
      |> apply_status_filter(params.status)
      |> Ash.Query.sort(inserted_at: :desc)
      
      {:ok, query}
    rescue
      error ->
        Logger.error("❌ [通信服务] 构建查询失败: #{inspect(error)}")
        {:error, "构建查询失败"}
    end
  end

  # 应用搜索过滤
  defp apply_search_filter(query, ""), do: query
  defp apply_search_filter(query, search_query) do
    import Ash.Query
    filter(query, contains(title, ^search_query) or contains(content, ^search_query))
  end

  # 应用类型过滤
  defp apply_type_filter(query, nil), do: query
  defp apply_type_filter(query, type) do
    Ash.Query.filter(query, type == ^type)
  end

  # 应用状态过滤
  defp apply_status_filter(query, nil), do: query
  defp apply_status_filter(query, status) do
    Ash.Query.filter(query, active == ^status)
  end

  # 执行通信查询
  defp execute_communications_query(query, params) do
    offset = (params.page - 1) * params.per_page
    
    case query
         |> Ash.Query.page(count: true, limit: params.per_page, offset: offset)
         |> Ash.read() do
      {:ok, %Ash.Page.Offset{results: communications, count: total_count}} ->
        page_info = %{
          total_count: total_count,
          page: params.page,
          per_page: params.per_page
        }
        
        {:ok, %{communications: communications, page_info: page_info}}
      {:error, error} ->
        Logger.error("❌ [通信服务] 执行查询失败: #{inspect(error)}")
        {:error, "查询通信失败"}
    end
  end

  # 获取通信
  defp get_communication_by_id(communication_id) do
    case SystemCommunication.read(communication_id) do
      {:ok, communication} -> {:ok, communication}
      {:error, %Ash.Error.Query.NotFound{}} -> {:error, "通信不存在"}
      {:error, error} -> {:error, "获取通信失败: #{inspect(error)}"}
    end
  end

  # 验证创建权限
  defp validate_create_permission(current_user) do
    if current_user.permission_level >= 1 do
      :ok
    else
      {:error, "权限不足，无法创建通信"}
    end
  end

  # 验证更新权限
  defp validate_update_permission(current_user, _communication) do
    if current_user.permission_level >= 1 do
      :ok
    else
      {:error, "权限不足，无法更新通信"}
    end
  end

  # 验证删除权限
  defp validate_delete_permission(current_user, _communication) do
    if current_user.permission_level >= 2 do
      :ok
    else
      {:error, "权限不足，无法删除通信"}
    end
  end

  # 验证查看权限
  defp validate_view_permission(_current_user, _communication) do
    # 所有用户都可以查看通信
    :ok
  end

  # 准备创建参数
  defp prepare_create_params(params, current_user) do
    create_params = Map.merge(params, %{
      "created_by" => current_user.id,
      "updated_by" => current_user.id
    })
    {:ok, create_params}
  end

  # 准备更新参数
  defp prepare_update_params(params) do
    # 移除不允许更新的字段
    update_params = Map.drop(params, ["id", "created_by", "inserted_at"])
    {:ok, update_params}
  end
end
