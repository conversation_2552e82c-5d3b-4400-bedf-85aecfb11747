defmodule RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.CustomerChatRepository do
  @moduledoc """
  客服聊天数据访问仓储
  
  提供客服聊天相关的数据访问抽象：
  - 聊天记录管理
  - 状态更新操作
  - 客服分配管理
  - 聊天统计分析
  """

  require Logger
  alias Teen.CustomerService.CustomerChat
  require Ash.Query

  # 常量定义
  @default_limit 20
  @max_limit 100
  @cache_ttl 300  # 5分钟缓存

  # ============================================================================
  # 聊天记录管理
  # ============================================================================

  @doc """
  根据ID获取聊天记录

  ## 参数
  - `chat_id` - 聊天记录ID
  - `options` - 选项

  ## 返回
  - `{:ok, chat}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_chat_by_id(chat_id, options \\ []) do
    Logger.debug("💬 [客服聊天仓储] 获取聊天记录: #{chat_id}")
    
    cache_key = build_cache_key("chat", chat_id, options[:preload])
    
    case get_from_cache(cache_key) do
      :miss ->
        fetch_and_cache_chat_by_id(chat_id, options[:preload] || [], cache_key)
      {:hit, chat} ->
        Logger.debug("💾 [客服聊天仓储] 缓存命中: #{chat_id}")
        {:ok, chat}
    end
  end

  @doc """
  创建聊天记录

  ## 参数
  - `chat_data` - 聊天数据

  ## 返回
  - `{:ok, chat}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_chat(chat_data) do
    Logger.info("📝 [客服聊天仓储] 创建聊天记录: 用户#{chat_data[:user_id]}")
    
    try do
      case CustomerChat.create(chat_data) do
        {:ok, chat} ->
          clear_cache(:chat, nil)
          Logger.info("✅ [客服聊天仓储] 聊天记录创建成功: #{chat.id}")
          {:ok, chat}
        {:error, error} ->
          Logger.error("❌ [客服聊天仓储] 创建聊天记录失败: #{inspect(error)}")
          {:error, :create_failed}
      end
    rescue
      error ->
        Logger.error("❌ [客服聊天仓储] 创建聊天记录异常: #{inspect(error)}")
        {:error, :create_error}
    end
  end

  @doc """
  更新聊天记录

  ## 参数
  - `chat_id` - 聊天记录ID
  - `update_data` - 更新数据

  ## 返回
  - `{:ok, chat}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_chat(chat_id, update_data) do
    Logger.info("📝 [客服聊天仓储] 更新聊天记录: #{chat_id}")
    
    with {:ok, chat} <- get_chat_by_id(chat_id),
         {:ok, updated_chat} <- do_update_chat(chat, update_data) do
      
      clear_cache(:chat, chat_id)
      Logger.info("✅ [客服聊天仓储] 聊天记录更新成功: #{chat_id}")
      {:ok, updated_chat}
    else
      error -> error
    end
  end

  @doc """
  标记聊天为已处理

  ## 参数
  - `chat_id` - 聊天记录ID
  - `reply_content` - 回复内容
  - `customer_service_id` - 客服ID

  ## 返回
  - `{:ok, chat}` - 成功
  - `{:error, reason}` - 失败
  """
  def mark_as_processed(chat_id, reply_content, customer_service_id \\ nil) do
    Logger.info("✅ [客服聊天仓储] 标记聊天已处理: #{chat_id}")
    
    with {:ok, chat} <- get_chat_by_id(chat_id),
         {:ok, processed_chat} <- do_mark_as_processed(chat, reply_content, customer_service_id) do
      
      clear_cache(:chat, chat_id)
      Logger.info("✅ [客服聊天仓储] 聊天标记处理成功: #{chat_id}")
      {:ok, processed_chat}
    else
      error -> error
    end
  end

  # ============================================================================
  # 聊天记录查询
  # ============================================================================

  @doc """
  分页获取聊天记录列表

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, %{items: chats, page_info: page_info}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_chats_paginated(params \\ %{}) do
    Logger.debug("📋 [客服聊天仓储] 分页获取聊天记录列表")
    
    normalized_params = normalize_list_params(params)
    cache_key = build_cache_key("chats_list", :paginated, normalized_params)
    
    case get_from_cache(cache_key) do
      :miss ->
        fetch_and_cache_chats_list(normalized_params, cache_key)
      {:hit, result} ->
        Logger.debug("💾 [客服聊天仓储] 缓存命中: 聊天列表")
        {:ok, result}
    end
  end

  @doc """
  获取用户聊天记录

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - `{:ok, chats}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_chats(user_id, options \\ []) do
    Logger.debug("👤 [客服聊天仓储] 获取用户聊天记录: #{user_id}")
    
    try do
      query = CustomerChat
      |> Ash.Query.filter(user_id == ^user_id)
      |> maybe_apply_status_filter(options[:status])
      |> maybe_apply_platform_filter(options[:platform])
      |> maybe_apply_date_range(options[:date_range])
      |> apply_sort(options[:sort] || [inserted_at: :desc])
      |> maybe_limit(options[:limit])
      
      case Ash.read(query) do
        {:ok, chats} -> {:ok, chats}
        {:error, error} ->
          Logger.error("❌ [客服聊天仓储] 获取用户聊天记录失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [客服聊天仓储] 获取用户聊天记录异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  获取待处理聊天记录

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, chats}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_pending_chats(options \\ []) do
    Logger.debug("⏳ [客服聊天仓储] 获取待处理聊天记录")
    
    cache_key = build_cache_key("pending_chats", :list, options)
    
    case get_from_cache(cache_key) do
      :miss ->
        fetch_and_cache_pending_chats(options, cache_key)
      {:hit, chats} ->
        Logger.debug("💾 [客服聊天仓储] 缓存命中: 待处理聊天")
        {:ok, chats}
    end
  end

  @doc """
  获取客服处理的聊天记录

  ## 参数
  - `customer_service_id` - 客服ID
  - `options` - 选项

  ## 返回
  - `{:ok, chats}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_customer_service_chats(customer_service_id, options \\ []) do
    Logger.debug("👨‍💼 [客服聊天仓储] 获取客服处理聊天: #{customer_service_id}")
    
    try do
      query = CustomerChat
      |> Ash.Query.filter(customer_service_id == ^customer_service_id)
      |> maybe_apply_status_filter(options[:status])
      |> maybe_apply_date_range(options[:date_range])
      |> apply_sort(options[:sort] || [processed_at: :desc])
      |> maybe_limit(options[:limit])
      
      case Ash.read(query) do
        {:ok, chats} -> {:ok, chats}
        {:error, error} ->
          Logger.error("❌ [客服聊天仓储] 获取客服聊天记录失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [客服聊天仓储] 获取客服聊天记录异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # ============================================================================
  # 统计查询
  # ============================================================================

  @doc """
  获取聊天统计

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, statistics}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_chat_statistics(options \\ []) do
    Logger.debug("📊 [客服聊天仓储] 获取聊天统计")
    
    try do
      stats = %{
        total_chats: get_total_chats(),
        pending_chats: get_pending_chats_count(),
        processed_chats: get_processed_chats_count(),
        recent_chats: get_recent_chats_count(options[:days] || 7),
        chats_by_platform: get_chats_by_platform(),
        average_response_time: get_average_response_time()
      }
      
      {:ok, stats}
    rescue
      error ->
        Logger.error("❌ [客服聊天仓储] 获取统计异常: #{inspect(error)}")
        {:error, :statistics_error}
    end
  end

  @doc """
  获取客服工作统计

  ## 参数
  - `customer_service_id` - 客服ID
  - `options` - 选项

  ## 返回
  - `{:ok, statistics}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_customer_service_statistics(customer_service_id, options \\ []) do
    Logger.debug("👨‍💼 [客服聊天仓储] 获取客服工作统计: #{customer_service_id}")
    
    try do
      stats = %{
        total_handled: get_customer_service_handled_count(customer_service_id),
        recent_handled: get_customer_service_recent_handled_count(customer_service_id, options[:days] || 7),
        average_response_time: get_customer_service_average_response_time(customer_service_id),
        satisfaction_rate: get_customer_service_satisfaction_rate(customer_service_id)
      }
      
      {:ok, stats}
    rescue
      error ->
        Logger.error("❌ [客服聊天仓储] 获取客服统计异常: #{inspect(error)}")
        {:error, :statistics_error}
    end
  end

  # ============================================================================
  # 缓存管理
  # ============================================================================

  @doc """
  清除聊天相关缓存

  ## 参数
  - `cache_type` - 缓存类型
  - `id` - ID（可选）
  """
  def clear_cache(cache_type, id \\ nil) do
    cache_patterns = case {cache_type, id} do
      {:chat, nil} -> ["chat:*"]
      {:chat, chat_id} -> ["chat:#{chat_id}:*"]
      {:user_chats, user_id} -> ["user_chats:#{user_id}:*"]
      {:pending_chats, _} -> ["pending_chats:*"]
      {:all, _} -> ["chat:*", "user_chats:*", "pending_chats:*", "chats_list:*"]
    end
    
    Logger.debug("🧹 [客服聊天仓储] 清除缓存: #{inspect(cache_patterns)}")
    Enum.each(cache_patterns, &clear_cache_pattern/1)
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 标准化列表参数
  defp normalize_list_params(params) do
    %{
      page: max(1, params[:page] || 1),
      limit: min(params[:limit] || @default_limit, @max_limit),
      search: String.trim(params[:search] || ""),
      filters: params[:filters] || %{},
      sort: params[:sort] || [inserted_at: :desc],
      preload: params[:preload] || []
    }
  end

  # 获取并缓存聊天记录
  defp fetch_and_cache_chat_by_id(chat_id, preload, cache_key) do
    case fetch_chat_by_id(chat_id, preload) do
      {:ok, chat} ->
        cache_put(cache_key, chat, @cache_ttl)
        {:ok, chat}
      error -> error
    end
  end

  # 获取聊天记录
  defp fetch_chat_by_id(chat_id, preload) do
    try do
      case CustomerChat
           |> maybe_preload(preload)
           |> Ash.get(chat_id) do
        {:ok, chat} -> {:ok, chat}
        {:error, %Ash.Error.Query.NotFound{}} -> {:error, :not_found}
        {:error, error} ->
          Logger.error("❌ [客服聊天仓储] 获取聊天记录失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [客服聊天仓储] 获取聊天记录异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # 执行更新聊天记录
  defp do_update_chat(chat, update_data) do
    try do
      case chat
           |> Ash.Changeset.for_update(:update, update_data)
           |> Ash.update() do
        {:ok, updated_chat} -> {:ok, updated_chat}
        {:error, error} ->
          Logger.error("❌ [客服聊天仓储] 更新聊天记录失败: #{inspect(error)}")
          {:error, :update_failed}
      end
    rescue
      error ->
        Logger.error("❌ [客服聊天仓储] 更新聊天记录异常: #{inspect(error)}")
        {:error, :update_error}
    end
  end

  # 执行标记为已处理
  defp do_mark_as_processed(chat, reply_content, customer_service_id) do
    update_data = %{
      reply_content: reply_content,
      status: 1,
      processed_at: DateTime.utc_now()
    }
    
    update_data = if customer_service_id do
      Map.put(update_data, :customer_service_id, customer_service_id)
    else
      update_data
    end
    
    try do
      case chat
           |> Ash.Changeset.for_update(:mark_as_processed, update_data)
           |> Ash.update() do
        {:ok, processed_chat} -> {:ok, processed_chat}
        {:error, error} ->
          Logger.error("❌ [客服聊天仓储] 标记处理失败: #{inspect(error)}")
          {:error, :mark_processed_failed}
      end
    rescue
      error ->
        Logger.error("❌ [客服聊天仓储] 标记处理异常: #{inspect(error)}")
        {:error, :mark_processed_error}
    end
  end

  # 获取并缓存聊天列表
  defp fetch_and_cache_chats_list(params, cache_key) do
    case fetch_chats_list(params) do
      {:ok, result} ->
        cache_put(cache_key, result, @cache_ttl)
        {:ok, result}
      error -> error
    end
  end

  # 获取聊天列表
  defp fetch_chats_list(params) do
    try do
      query = CustomerChat
      |> apply_chat_filters(params.filters)
      |> apply_chat_search(params.search)
      |> apply_sort(params.sort)
      
      execute_paginated_query(query, params)
    rescue
      error ->
        Logger.error("❌ [客服聊天仓储] 获取聊天列表异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # 获取并缓存待处理聊天
  defp fetch_and_cache_pending_chats(options, cache_key) do
    case fetch_pending_chats(options) do
      {:ok, chats} ->
        cache_put(cache_key, chats, @cache_ttl)
        {:ok, chats}
      error -> error
    end
  end

  # 获取待处理聊天
  defp fetch_pending_chats(options) do
    try do
      query = CustomerChat
      |> Ash.Query.filter(status == 0)
      |> maybe_apply_platform_filter(options[:platform])
      |> apply_sort(options[:sort] || [inserted_at: :asc])
      |> maybe_limit(options[:limit])
      
      case Ash.read(query) do
        {:ok, chats} -> {:ok, chats}
        {:error, error} ->
          Logger.error("❌ [客服聊天仓储] 获取待处理聊天失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [客服聊天仓储] 获取待处理聊天异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # 应用聊天过滤器
  defp apply_chat_filters(query, filters) when map_size(filters) == 0, do: query
  defp apply_chat_filters(query, filters) do
    Enum.reduce(filters, query, fn {key, value}, acc_query ->
      apply_chat_filter(acc_query, key, value)
    end)
  end

  # 应用单个聊天过滤器
  defp apply_chat_filter(query, :status, status) when not is_nil(status) do
    Ash.Query.filter(query, status == ^status)
  end
  defp apply_chat_filter(query, :platform, platform) when not is_nil(platform) do
    Ash.Query.filter(query, platform == ^platform)
  end
  defp apply_chat_filter(query, :user_id, user_id) when not is_nil(user_id) do
    Ash.Query.filter(query, user_id == ^user_id)
  end
  defp apply_chat_filter(query, :customer_service_id, customer_service_id) when not is_nil(customer_service_id) do
    Ash.Query.filter(query, customer_service_id == ^customer_service_id)
  end
  defp apply_chat_filter(query, _key, _value), do: query

  # 应用聊天搜索
  defp apply_chat_search(query, search) when search == "" or is_nil(search), do: query
  defp apply_chat_search(query, search) do
    search_term = "%#{search}%"
    Ash.Query.filter(query, ilike(question, ^search_term) or ilike(reply_content, ^search_term))
  end

  # 执行分页查询
  defp execute_paginated_query(query, params) do
    offset = (params.page - 1) * params.limit
    
    case query
         |> maybe_preload(params.preload)
         |> Ash.Query.page(count: true, limit: params.limit, offset: offset)
         |> Ash.read() do
      {:ok, %Ash.Page.Offset{results: items, count: total_count}} ->
        page_info = %{
          total_count: total_count,
          page: params.page,
          limit: params.limit,
          total_pages: ceil(total_count / params.limit)
        }
        {:ok, %{items: items, page_info: page_info}}
      {:error, error} ->
        Logger.error("❌ [客服聊天仓储] 分页查询失败: #{inspect(error)}")
        {:error, :query_failed}
    end
  end

  # 统计函数（简化实现）
  defp get_total_chats do
    case CustomerChat |> Ash.Query.aggregate(:count, :id) |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_pending_chats_count do
    case CustomerChat 
         |> Ash.Query.filter(status == 0)
         |> Ash.Query.aggregate(:count, :id) 
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_processed_chats_count do
    case CustomerChat 
         |> Ash.Query.filter(status == 1)
         |> Ash.Query.aggregate(:count, :id) 
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_recent_chats_count(days) do
    date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
    
    case CustomerChat 
         |> Ash.Query.filter(inserted_at >= ^date_threshold)
         |> Ash.Query.aggregate(:count, :id) 
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_chats_by_platform do
    # 这里需要根据实际需求实现平台分组统计
    %{}
  end

  defp get_average_response_time do
    # 这里需要根据实际需求计算平均响应时间
    0
  end

  defp get_customer_service_handled_count(customer_service_id) do
    case CustomerChat 
         |> Ash.Query.filter(customer_service_id == ^customer_service_id and status == 1)
         |> Ash.Query.aggregate(:count, :id) 
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_customer_service_recent_handled_count(customer_service_id, days) do
    date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
    
    case CustomerChat 
         |> Ash.Query.filter(customer_service_id == ^customer_service_id and status == 1 and processed_at >= ^date_threshold)
         |> Ash.Query.aggregate(:count, :id) 
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_customer_service_average_response_time(_customer_service_id) do
    # 这里需要根据实际需求计算客服平均响应时间
    0
  end

  defp get_customer_service_satisfaction_rate(_customer_service_id) do
    # 这里需要根据实际需求计算客服满意度
    0.0
  end

  # 可能预加载关联数据
  defp maybe_preload(query, []), do: query
  defp maybe_preload(query, preload) when is_list(preload) do
    Ash.Query.load(query, preload)
  end

  # 可能限制数量
  defp maybe_limit(query, nil), do: query
  defp maybe_limit(query, limit) when is_integer(limit) and limit > 0 do
    Ash.Query.limit(query, limit)
  end
  defp maybe_limit(query, _), do: query

  # 应用排序
  defp apply_sort(query, sort) when is_list(sort) do
    Ash.Query.sort(query, sort)
  end
  defp apply_sort(query, _), do: query

  # 可能应用状态过滤
  defp maybe_apply_status_filter(query, nil), do: query
  defp maybe_apply_status_filter(query, status) do
    Ash.Query.filter(query, status == ^status)
  end

  # 可能应用平台过滤
  defp maybe_apply_platform_filter(query, nil), do: query
  defp maybe_apply_platform_filter(query, platform) do
    Ash.Query.filter(query, platform == ^platform)
  end

  # 可能应用日期范围
  defp maybe_apply_date_range(query, nil), do: query
  defp maybe_apply_date_range(query, {start_date, end_date}) do
    Ash.Query.filter(query, inserted_at >= ^start_date and inserted_at <= ^end_date)
  end

  # 构建缓存键
  defp build_cache_key(prefix, id, extra \\ nil) do
    case extra do
      nil -> "#{prefix}:#{id}"
      extra -> "#{prefix}:#{id}:#{:erlang.phash2(extra)}"
    end
  end

  # 缓存操作（需要实现具体的缓存后端）
  defp get_from_cache(_key), do: :miss
  defp cache_put(_key, _value, _ttl), do: :ok
  defp clear_cache_pattern(_pattern), do: :ok
end
