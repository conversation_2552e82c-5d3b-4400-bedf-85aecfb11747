defmodule RacingGame.UtilsTest do
  use ExUnit.Case, async: true
  alias RacingG<PERSON>.Utils

  describe "text processing" do
    test "truncate_text/2 with preset" do
      long_text = "这是一个非常长的文本内容，需要被截断处理"
      result = Utils.truncate_text(long_text, :title)
      assert String.length(result) <= 53  # 50 + "..."
      assert String.ends_with?(result, "...")
    end

    test "smart_truncate/2 with context" do
      text = "智能截断测试文本"
      result = Utils.smart_truncate(text, :title)
      assert is_binary(result)
    end

    test "sanitize_string/1" do
      dirty_text = "  hello   world  "
      result = Utils.sanitize_string(dirty_text)
      assert result == "hello world"
    end
  end

  describe "type conversion" do
    test "safe_to_string/1" do
      assert Utils.safe_to_string(123) == "123"
      assert Utils.safe_to_string(nil) == ""
      assert Utils.safe_to_string("hello") == "hello"
    end

    test "safe_to_integer/1" do
      assert Utils.safe_to_integer("123") == {:ok, 123}
      assert Utils.safe_to_integer(456) == {:ok, 456}
      assert {:error, _} = Utils.safe_to_integer("invalid")
    end

    test "safe_to_boolean/1" do
      assert Utils.safe_to_boolean("true") == {:ok, true}
      assert Utils.safe_to_boolean("false") == {:ok, false}
      assert Utils.safe_to_boolean("yes") == {:ok, true}
      assert Utils.safe_to_boolean("no") == {:ok, false}
    end
  end

  describe "validation" do
    test "blank?/1" do
      assert Utils.blank?(nil) == true
      assert Utils.blank?("") == true
      assert Utils.blank?("  ") == true
      assert Utils.blank?("hello") == false
    end

    test "present?/1" do
      assert Utils.present?("hello") == true
      assert Utils.present?(nil) == false
      assert Utils.present?("") == false
    end

    test "valid_type?/2" do
      assert Utils.valid_type?("hello", :string) == true
      assert Utils.valid_type?(123, :integer) == true
      assert Utils.valid_type?("hello", :integer) == false
    end

    test "validate/3 with length" do
      assert Utils.validate("hello", :length, min: 3, max: 10) == {:ok, "hello"}
      assert {:error, _} = Utils.validate("hi", :length, min: 3, max: 10)
    end
  end

  describe "common utilities" do
    test "generate_unique_id/1" do
      id1 = Utils.generate_unique_id()
      id2 = Utils.generate_unique_id("prefix_")
      
      assert is_binary(id1)
      assert is_binary(id2)
      assert String.starts_with?(id2, "prefix_")
      assert id1 != id2
    end

    test "deep_merge/2" do
      left = %{a: 1, b: %{c: 2, d: 3}}
      right = %{b: %{c: 4, e: 5}, f: 6}
      result = Utils.deep_merge(left, right)
      
      expected = %{a: 1, b: %{c: 4, d: 3, e: 5}, f: 6}
      assert result == expected
    end

    test "should_show_clear_button?/3" do
      assert Utils.should_show_clear_button?("query", nil, nil) == true
      assert Utils.should_show_clear_button?("", :message, nil) == true
      assert Utils.should_show_clear_button?("", nil, true) == true
      assert Utils.should_show_clear_button?("", nil, nil) == false
    end

    test "safe_get_in/3" do
      data = %{user: %{profile: %{name: "John"}}}
      
      assert Utils.safe_get_in(data, [:user, :profile, :name]) == "John"
      assert Utils.safe_get_in(data, [:user, :settings, :theme], "default") == "default"
      assert Utils.safe_get_in(data, [:missing], nil) == nil
    end

    test "keyword_to_map/2" do
      keyword_list = [a: 1, b: 2]
      result = Utils.keyword_to_map(keyword_list)
      assert result == %{a: 1, b: 2}
    end

    test "filter_keys/2" do
      data = %{a: 1, b: 2, c: 3}
      result = Utils.filter_keys(data, [:a, :c])
      assert result == %{a: 1, c: 3}
    end
  end

  describe "convenience functions" do
    test "clean_and_truncate/2" do
      dirty_text = "  这是一个需要清理和截断的长文本内容  "
      result = Utils.clean_and_truncate(dirty_text, :short)
      
      assert is_binary(result)
      assert String.length(result) <= 33  # 30 + "..."
      refute String.starts_with?(result, " ")
      refute String.ends_with?(result, " ")
    end

    test "safe_get_and_convert/4" do
      data = %{user: %{age: "25"}}
      
      assert Utils.safe_get_and_convert(data, [:user, :age], :integer) == {:ok, 25}
      assert {:error, _} = Utils.safe_get_and_convert(data, [:user, :missing], :integer)
    end

    test "validate_and_convert/4" do
      validations = [
        {:length, [min: 1, max: 5]},
        {:format, [pattern: ~r/^\d+$/]}
      ]
      
      assert Utils.validate_and_convert("123", :integer, validations) == {:ok, 123}
      assert {:error, _} = Utils.validate_and_convert("abc", :integer, validations)
    end

    test "batch_process_texts/2" do
      texts = ["长文本1", "长文本2", "长文本3"]
      results = Utils.batch_process_texts(texts, :short)
      
      assert length(results) == 3
      assert Enum.all?(results, &is_binary/1)
    end

    test "create_config_map/3" do
      config = [database: [host: "localhost", port: "5432"]]
      required_paths = [[:database, :host]]
      
      assert {:ok, result} = Utils.create_config_map(config, required_paths)
      assert result.database.host == "localhost"
    end
  end
end
