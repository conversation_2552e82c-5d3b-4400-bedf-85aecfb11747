# 组件使用示例 - Racing Game Admin Panel

## 📋 概述

本文档提供了新设计的UI组件系统的详细使用示例，包括基础组件和业务组件的实际应用场景。

## 🧩 基础组件使用示例

### 1. 按钮组件 (Button)

```elixir
# 在LiveView模块中引入组件
import RacingGameWeb.UIComponents

# 基础按钮
<.button variant="primary" size="md">
  <.icon name="hero-plus" class="w-4 h-4 mr-2" />
  创建用户
</.button>

# 加载状态按钮
<.button variant="primary" loading={@creating_user}>
  <%= if @creating_user, do: "创建中...", else: "创建用户" %>
</.button>

# 危险操作按钮
<.button 
  variant="danger" 
  size="sm" 
  phx-click="delete_user" 
  phx-value-id={@user.id}
  data-confirm="确定要删除这个用户吗？"
>
  删除
</.button>

# 全宽按钮
<.button variant="primary" full_width={true}>
  保存设置
</.button>
```

### 2. 输入框组件 (Input)

```elixir
# 基础输入框
<.input
  type="text"
  name="username"
  label="用户名"
  placeholder="请输入用户名"
  required
  value={@form[:username].value}
  error={@form[:username].errors |> Enum.map(&elem(&1, 0)) |> Enum.join(", ")}
/>

# 带图标的输入框
<.input
  type="email"
  name="email"
  label="邮箱地址"
  placeholder="<EMAIL>"
  icon="hero-envelope"
  helper_text="我们不会分享您的邮箱地址"
/>

# 密码输入框
<.input
  type="password"
  name="password"
  label="密码"
  placeholder="请输入密码"
  required
  icon="hero-lock-closed"
/>
```

### 3. 卡片组件 (Card)

```elixir
# 基础卡片
<.card title="用户统计" subtitle="最近30天的用户活动数据">
  <div class="grid grid-cols-2 gap-4">
    <div class="text-center">
      <div class="text-2xl font-bold text-primary">1,234</div>
      <div class="text-sm text-gray-500">新增用户</div>
    </div>
    <div class="text-center">
      <div class="text-2xl font-bold text-success">89%</div>
      <div class="text-sm text-gray-500">活跃率</div>
    </div>
  </div>
</.card>

# 带操作按钮的卡片
<.card title="系统设置" class="shadow-lg">
  <:actions>
    <.button variant="ghost" size="sm" phx-click="edit_settings">
      <.icon name="hero-pencil" class="w-4 h-4 mr-1" />
      编辑
    </.button>
  </:actions>
  
  <div class="space-y-2">
    <div class="flex justify-between">
      <span>系统版本</span>
      <span class="font-mono">v2.1.0</span>
    </div>
    <div class="flex justify-between">
      <span>运行时间</span>
      <span>15天 8小时</span>
    </div>
  </div>
</.card>
```

### 4. 表格组件 (Table)

```elixir
# 用户列表表格
<.table rows={@users} row_id={&"user-#{&1.id}"}>
  <:col :let={user} label="用户信息">
    <div class="flex items-center space-x-3">
      <.avatar src={user.avatar} name={user.username} size="sm" />
      <div>
        <div class="font-bold"><%= user.username %></div>
        <div class="text-sm opacity-50">ID: <%= user.id %></div>
      </div>
    </div>
  </:col>
  
  <:col :let={user} label="邮箱">
    <%= user.email %>
  </:col>
  
  <:col :let={user} label="状态">
    <.badge variant={user.active && "success" || "warning"}>
      <%= user.active && "活跃" || "非活跃" %>
    </.badge>
  </:col>
  
  <:col :let={user} label="注册时间">
    <%= Calendar.strftime(user.inserted_at, "%Y-%m-%d") %>
  </:col>
  
  <:action :let={user}>
    <.button variant="ghost" size="xs" phx-click="edit_user" phx-value-id={user.id}>
      编辑
    </.button>
    <.button 
      variant="ghost" 
      size="xs" 
      class="text-error"
      phx-click="delete_user" 
      phx-value-id={user.id}
      data-confirm="确定要删除用户 #{user.username} 吗？"
    >
      删除
    </.button>
  </:action>
</.table>
```

### 5. 模态框组件 (Modal)

```elixir
# 用户编辑模态框
<.modal id="edit-user-modal" title="编辑用户" size="lg">
  <:actions>
    <.button variant="primary" phx-click="save_user">
      保存
    </.button>
    <.button variant="ghost" phx-click={JS.hide(to: "#edit-user-modal")}>
      取消
    </.button>
  </:actions>
  
  <div class="space-y-4">
    <.input name="username" label="用户名" value={@selected_user.username} />
    <.input name="email" label="邮箱" value={@selected_user.email} />
    <.input name="phone" label="手机号" value={@selected_user.phone} />
    
    <div class="form-control">
      <label class="label">
        <span class="label-text">用户状态</span>
      </label>
      <select name="status" class="select select-bordered">
        <option value="active" selected={@selected_user.status == "active"}>活跃</option>
        <option value="inactive" selected={@selected_user.status == "inactive"}>非活跃</option>
        <option value="banned" selected={@selected_user.status == "banned"}>已封禁</option>
      </select>
    </div>
  </div>
</.modal>
```

## 🏢 业务组件使用示例

### 1. 用户信息卡片

```elixir
# 引入业务组件
import RacingGameWeb.BusinessComponents

# 基础用户卡片
<.user_card user={@user} />

# 完整功能用户卡片
<.user_card 
  user={@user} 
  show_balance={true} 
  show_actions={true}
  class="hover:shadow-xl transition-shadow"
/>

# 紧凑模式用户卡片
<.user_card user={@user} compact={true} />
```

### 2. 支付状态组件

```elixir
# 支付状态显示
<.payment_status 
  status="completed"
  amount={1000}
  created_at={~N[2024-01-01 12:00:00]}
  show_amount={true}
/>

# 不同状态的支付
<div class="space-y-2">
  <.payment_status status="pending" amount={500} />
  <.payment_status status="failed" amount={300} />
  <.payment_status status="refunding" amount={800} />
</div>
```

### 3. 数据统计卡片

```elixir
# 用户统计
<.stat_card
  title="在线用户"
  value="1,234"
  change="+12%"
  trend="up"
  icon="hero-users"
  color="primary"
  description="较昨日"
/>

# 收入统计
<.stat_card
  title="今日收入"
  value="¥45,678"
  change="-5%"
  trend="down"
  icon="hero-currency-dollar"
  color="success"
  description="较昨日"
/>

# 系统性能
<.stat_card
  title="系统负载"
  value="68%"
  trend="flat"
  icon="hero-cpu-chip"
  color="warning"
  description="CPU使用率"
/>
```

### 4. 筛选面板

```elixir
# 用户筛选面板
<.filter_panel title="用户筛选" collapsible={true}>
  <.input 
    type="text" 
    name="search" 
    label="搜索" 
    placeholder="用户名、邮箱或手机号"
    phx-change="filter_users"
    phx-debounce="300"
  />
  
  <div class="form-control">
    <label class="label">
      <span class="label-text">用户状态</span>
    </label>
    <select name="status" class="select select-bordered" phx-change="filter_users">
      <option value="">全部状态</option>
      <option value="active">活跃</option>
      <option value="inactive">非活跃</option>
      <option value="banned">已封禁</option>
    </select>
  </div>
  
  <div class="form-control">
    <label class="label">
      <span class="label-text">注册时间</span>
    </label>
    <input 
      type="date" 
      name="date_from" 
      class="input input-bordered"
      phx-change="filter_users"
    />
  </div>
  
  <:actions>
    <.button variant="ghost" size="sm" phx-click="reset_filters">
      重置
    </.button>
    <.button variant="primary" size="sm" phx-click="apply_filters">
      应用筛选
    </.button>
  </:actions>
</.filter_panel>
```

### 5. 操作栏

```elixir
# 页面操作栏
<.action_bar>
  <:left>
    <.button variant="primary" phx-click={JS.show(to: "#create-user-modal")}>
      <.icon name="hero-plus" class="w-4 h-4 mr-2" />
      新建用户
    </.button>
    
    <.button variant="secondary" phx-click="import_users">
      <.icon name="hero-arrow-up-tray" class="w-4 h-4 mr-2" />
      批量导入
    </.button>
  </:left>
  
  <:right>
    <.button variant="ghost" size="sm" phx-click="export_users">
      <.icon name="hero-arrow-down-tray" class="w-4 h-4 mr-2" />
      导出
    </.button>
    
    <.button variant="ghost" size="sm" phx-click="refresh_data">
      <.icon name="hero-arrow-path" class="w-4 h-4 mr-2" />
      刷新
    </.button>
  </:right>
</.action_bar>
```

### 6. 空状态组件

```elixir
# 无用户时的空状态
<%= if Enum.empty?(@users) do %>
  <.empty_state
    icon="hero-users"
    title="暂无用户"
    description="还没有用户注册，点击下方按钮创建第一个用户"
  >
    <.button variant="primary" phx-click={JS.show(to: "#create-user-modal")}>
      <.icon name="hero-plus" class="w-4 h-4 mr-2" />
      创建用户
    </.button>
  </.empty_state>
<% end %>

# 搜索无结果时的空状态
<%= if @search_query != "" and Enum.empty?(@filtered_users) do %>
  <.empty_state
    icon="hero-magnifying-glass"
    title="未找到匹配的用户"
    description="尝试调整搜索条件或清除筛选器"
  >
    <.button variant="ghost" phx-click="clear_search">
      清除搜索
    </.button>
  </.empty_state>
<% end %>
```

## 🎨 主题系统使用

### 在LiveView中使用主题

```elixir
# 在mount函数中设置主题
def mount(_params, _session, socket) do
  # 从用户偏好或系统设置中获取主题
  theme = get_user_theme(socket.assigns.current_user) || "racing-professional"
  
  socket = 
    socket
    |> assign(:theme, theme)
    |> push_event("set-theme", %{theme: theme})
  
  {:ok, socket}
end

# 主题切换处理
def handle_event("change_theme", %{"theme" => theme}, socket) do
  # 保存用户主题偏好
  save_user_theme(socket.assigns.current_user, theme)
  
  socket = 
    socket
    |> assign(:theme, theme)
    |> push_event("set-theme", %{theme: theme})
  
  {:noreply, socket}
end
```

### 在模板中应用主题

```heex
<div data-theme={@theme} class="admin-panel min-h-screen">
  <!-- 页面内容 -->
  <div class="container mx-auto p-4">
    <!-- 使用组件 -->
    <.card title="欢迎使用 Racing Game Admin Panel" class="mb-6">
      <p>当前主题: <span class="font-semibold"><%= @theme %></span></p>
      
      <div class="mt-4 space-x-2">
        <.button 
          variant="primary" 
          phx-click="change_theme" 
          phx-value-theme="racing-professional"
        >
          专业版
        </.button>
        <.button 
          variant="secondary" 
          phx-click="change_theme" 
          phx-value-theme="racing-dark"
        >
          暗色版
        </.button>
        <.button 
          variant="info" 
          phx-click="change_theme" 
          phx-value-theme="racing-high-contrast"
        >
          高对比度
        </.button>
      </div>
    </.card>
  </div>
</div>
```

## 📱 响应式设计示例

```heex
<!-- 响应式网格布局 -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
  <%= for user <- @users do %>
    <.user_card user={user} compact={true} />
  <% end %>
</div>

<!-- 响应式表格 -->
<div class="hidden md:block">
  <!-- 桌面端显示完整表格 -->
  <.table rows={@users}>
    <!-- 表格列定义 -->
  </.table>
</div>

<div class="md:hidden space-y-4">
  <!-- 移动端显示卡片列表 -->
  <%= for user <- @users do %>
    <.user_card user={user} show_actions={true} />
  <% end %>
</div>
```

---

**文档版本**: v1.0  
**创建日期**: 2024-12-19  
**最后更新**: 2024-12-19  
**维护团队**: Racing Game Frontend Team
