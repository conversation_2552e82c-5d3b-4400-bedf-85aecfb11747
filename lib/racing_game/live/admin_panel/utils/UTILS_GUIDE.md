# RacingGame Utils - 工具模块集合

这个目录包含了 RacingGame 项目的所有通用工具模块，提供了文本处理、类型转换、验证和通用工具等功能。

## 📁 模块结构

```
lib/racing_game/utils/
├── UTILS_GUIDE.md           # 本文档
├── text_utils.ex            # 文本处理工具
├── type_converter.ex        # 类型转换工具
├── validator.ex             # 验证工具
├── common_utils.ex          # 通用工具
└── ../utils.ex              # 统一接口模块
```

## 🚀 快速开始

### 使用统一接口（推荐）

```elixir
# 导入统一接口
alias RacingGame.Utils

# 文本处理
Utils.truncate_text("长文本", :title)
Utils.sanitize_string("  脏数据  ")

# 类型转换
Utils.safe_to_integer("123")
Utils.safe_to_boolean("yes")

# 验证
Utils.validate("test", :present)
Utils.blank?(nil)

# 通用工具
Utils.generate_unique_id("prefix_")
Utils.deep_merge(map1, map2)
```

### 使用独立模块

```elixir
# 导入特定模块
alias RacingGame.Utils.TextUtils
alias RacingGame.Utils.TypeConverter
alias RacingGame.Utils.Validator
alias RacingGame.Utils.CommonUtils

# 使用特定功能
TextUtils.truncate_text("文本", max_length: 50)
TypeConverter.safe_convert("123", :integer)
Validator.validate("<EMAIL>", :format, pattern: ~r/@/)
CommonUtils.generate_unique_id()
```

## 📚 模块详细说明

### 1. TextUtils - 文本处理工具

提供文本截断、清理、格式化等功能。

#### 主要功能
- **文本截断**：支持预设配置和自定义选项
- **智能截断**：根据上下文自动选择最佳配置
- **字符串清理**：移除多余空格和特殊字符
- **单词边界处理**：智能的单词边界截断算法

#### 使用示例
```elixir
# 使用预设截断
TextUtils.truncate_text("长文本", :title)        # 50字符，单词边界
TextUtils.truncate_text("长文本", :content)      # 100字符，单词边界
TextUtils.truncate_text("长文本", :short)        # 30字符，字符边界

# 自定义截断
TextUtils.truncate_text("长文本", max_length: 80, suffix: "...", word_boundary: true)

# 智能截断
TextUtils.smart_truncate("标题文本", :title)
TextUtils.smart_truncate("内容文本", :content)

# 字符串清理
TextUtils.sanitize_string("  hello   world  ")  # => "hello world"
```

### 2. TypeConverter - 类型转换工具

提供安全的类型转换功能，支持错误处理和配置选项。

#### 主要功能
- **安全转换**：防止转换异常，返回结果元组
- **配置化转换**：支持自定义转换行为
- **多种类型支持**：字符串、整数、布尔值、时间等
- **错误处理策略**：灵活的错误处理方式

#### 使用示例
```elixir
# 基础转换
TypeConverter.safe_to_string(123)               # => "123"
TypeConverter.safe_to_integer("123")            # => {:ok, 123}
TypeConverter.safe_to_boolean("yes")            # => {:ok, true}

# 高级转换
TypeConverter.safe_to_integer("123.45", allow_float: true)     # => {:ok, 123}
TypeConverter.safe_to_boolean("maybe", strict: true)          # => {:error, "..."}
TypeConverter.format_time(datetime, format: :iso)             # => "2023-12-01T10:00:00Z"

# 统一接口
TypeConverter.safe_convert("123", :integer)     # => {:ok, 123}
TypeConverter.safe_convert("true", :boolean)    # => {:ok, true}
```

### 3. Validator - 验证工具

提供统一的数据验证功能，支持多种验证类型和组合验证。

#### 主要功能
- **基础验证**：空值、类型、长度、格式等
- **组合验证**：多个验证条件的组合
- **自定义验证**：支持自定义验证函数
- **详细错误信息**：提供具体的错误描述

#### 使用示例
```elixir
# 基础验证
Validator.blank?(nil)                           # => true
Validator.present?("hello")                     # => true
Validator.valid_type?("text", :string)          # => true

# 长度验证
Validator.validate("hello", :length, min: 3, max: 10)  # => {:ok, "hello"}

# 格式验证
Validator.validate("<EMAIL>", :format, pattern: ~r/@/)  # => {:ok, "..."}

# 组合验证
validations = [
  {:present, []},
  {:length, [min: 5, max: 50]},
  {:format, [pattern: ~r/^[a-zA-Z]/]}
]
Validator.validate("hello", :all, validations: validations)
```

### 4. CommonUtils - 通用工具

提供各种常用的工具函数，包括ID生成、映射操作、过滤器判断等。

#### 主要功能
- **ID生成**：生成唯一标识符
- **映射操作**：深度合并、键操作、安全访问
- **数据转换**：关键字列表与映射互转
- **UI辅助**：按钮显示判断等

#### 使用示例
```elixir
# ID生成
CommonUtils.generate_unique_id()                # => "1701234567890_1234"
CommonUtils.generate_unique_id("user_")         # => "user_1701234567890_5678"

# 映射操作
CommonUtils.deep_merge(map1, map2)
CommonUtils.safe_get_in(map, [:user, :name], "默认值")
CommonUtils.safe_put_in(map, [:user, :age], 25)

# 键操作
CommonUtils.filter_keys(map, [:name, :age])
CommonUtils.drop_keys(map, [:password])
CommonUtils.rename_keys(map, %{"old_key" => "new_key"})

# 数据转换
CommonUtils.keyword_to_map([a: 1, b: 2])        # => %{a: 1, b: 2}
CommonUtils.map_to_keyword(%{a: 1, b: 2})       # => [a: 1, b: 2]
```

## 🎯 便捷函数

统一接口模块还提供了一些便捷的组合函数：

```elixir
# 清理并截断文本
Utils.clean_and_truncate("  长文本  ", :title)

# 安全获取并转换
Utils.safe_get_and_convert(map, [:user, :age], :integer)

# 验证并转换
Utils.validate_and_convert("123", :integer, validations)

# 批量处理文本
Utils.batch_process_texts(["文本1", "文本2"], :title)

# 创建配置映射
Utils.create_config_map(keyword_list, required_paths, validations)
```

## 🔧 配置选项

### 文本截断预设

```elixir
@truncate_presets %{
  title: %{max_length: 50, suffix: "...", word_boundary: true},
  content: %{max_length: 100, suffix: "...", word_boundary: true},
  description: %{max_length: 80, suffix: "...", word_boundary: true},
  short: %{max_length: 30, suffix: "...", word_boundary: false},
  medium: %{max_length: 60, suffix: "...", word_boundary: true},
  long: %{max_length: 120, suffix: "...", word_boundary: true},
  preview: %{max_length: 200, suffix: "... 查看更多", word_boundary: true}
}
```

### 类型转换配置

```elixir
@conversion_defaults %{
  string: %{empty_value: "", error_handling: :return_string},
  integer: %{error_handling: :return_error, allow_float: false},
  boolean: %{error_handling: :return_error, strict: false},
  time: %{format: :local, fallback: "-", error_handling: :return_fallback}
}
```

## 🧪 测试

运行工具模块的测试：

```bash
mix test test/racing_game/utils_test.exs
```

## 📝 最佳实践

1. **优先使用统一接口**：`RacingGame.Utils` 提供了最简洁的API
2. **合理选择预设**：文本截断时优先使用预设配置
3. **处理错误结果**：类型转换和验证都可能返回错误，要妥善处理
4. **组合使用功能**：利用便捷函数进行复杂操作
5. **配置化使用**：根据需要调整转换和验证的配置选项

## 🔄 迁移指南

如果您正在从旧的工具函数迁移到新的模块：

### 旧代码
```elixir
defp truncate_text(text, max_length) do
  if String.length(text) > max_length do
    String.slice(text, 0, max_length) <> "..."
  else
    text
  end
end

defp safe_to_string(nil), do: ""
defp safe_to_string(value), do: to_string(value)
```

### 新代码
```elixir
alias RacingGame.Utils

defp truncate_text(text, options \\ []), do: Utils.truncate_text(text, options)
defp safe_to_string(value, opts \\ []), do: Utils.safe_to_string(value, opts)
```

这样可以获得更强大的功能和更好的错误处理。
