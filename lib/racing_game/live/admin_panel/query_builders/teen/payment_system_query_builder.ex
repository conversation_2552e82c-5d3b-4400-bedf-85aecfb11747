defmodule RacingGame.Live.AdminPanel.QueryBuilders.Teen.PaymentSystemQueryBuilder do
  @moduledoc """
  💳 支付系统查询构建器

  负责支付系统相关的复杂查询构建，包括：
  - 跨Repository查询协调
  - 支付方式选择和优化
  - 网关性能分析
  - 兑换配置匹配
  - 费用计算查询
  - 综合统计分析
  """

  require Logger
  alias RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.{
    PaymentGatewayRepository,
    PaymentConfigRepository,
    ExchangeConfigRepository
  }

  # ==================== 支付方式查询 ====================

  @doc """
  获取可用支付方式

  ## 参数
  - `user_info` - 用户信息
  - `amount` - 支付金额
  - `options` - 选项参数

  ## 返回
  - `{:ok, payment_methods}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_available_payment_methods(user_info, amount, options \\ []) do
    Logger.info("💳 [支付系统查询构建器] 获取可用支付方式: 金额=#{amount}")

    try do
      # 并行获取数据
      tasks = [
        Task.async(fn -> PaymentGatewayRepository.list_active_payment_gateways([load: [:payment_configs]]) end),
        Task.async(fn -> PaymentConfigRepository.list_active_payment_configs() end)
      ]

      [gateways_result, configs_result] = Task.await_many(tasks, 5000)

      with {:ok, gateways} <- gateways_result,
           {:ok, configs} <- configs_result do
        
        # 过滤适合的支付方式
        suitable_methods = filter_suitable_payment_methods(gateways, configs, amount, user_info)
        
        # 按优先级排序
        sorted_methods = sort_payment_methods_by_priority(suitable_methods)
        
        Logger.info("✅ [支付系统查询构建器] 可用支付方式获取成功: #{length(sorted_methods)}个")
        {:ok, sorted_methods}
      else
        error ->
          Logger.error("❌ [支付系统查询构建器] 获取可用支付方式失败: #{inspect(error)}")
          {:error, :get_payment_methods_failed}
      end
    rescue
      exception ->
        Logger.error("💥 [支付系统查询构建器] 获取可用支付方式异常: #{inspect(exception)}")
        {:error, :get_payment_methods_exception}
    end
  end

  @doc """
  选择最优支付网关

  ## 参数
  - `payment_type` - 支付类型
  - `amount` - 支付金额
  - `options` - 选项参数

  ## 返回
  - `{:ok, gateway}` - 成功
  - `{:error, reason}` - 失败
  """
  def select_optimal_gateway(payment_type, amount, options \\ []) do
    Logger.info("💳 [支付系统查询构建器] 选择最优支付网关: #{payment_type}")

    try do
      # 获取该支付类型的所有配置
      with {:ok, configs} <- PaymentConfigRepository.list_payment_configs_by_type(payment_type, [load: [:gateway]]) do
        
        # 过滤适合金额的配置
        suitable_configs = filter_configs_by_amount(configs, amount)
        
        if length(suitable_configs) > 0 do
          # 选择最优网关
          optimal_config = select_best_config(suitable_configs)
          
          Logger.info("✅ [支付系统查询构建器] 最优网关选择成功: #{optimal_config.gateway.name}")
          {:ok, optimal_config.gateway}
        else
          Logger.warn("⚠️ [支付系统查询构建器] 没有找到适合的网关")
          {:error, :no_suitable_gateway}
        end
      else
        error ->
          Logger.error("❌ [支付系统查询构建器] 选择最优网关失败: #{inspect(error)}")
          {:error, :select_gateway_failed}
      end
    rescue
      exception ->
        Logger.error("💥 [支付系统查询构建器] 选择最优网关异常: #{inspect(exception)}")
        {:error, :select_gateway_exception}
    end
  end

  # ==================== 兑换配置查询 ====================

  @doc """
  获取用户兑换配置

  ## 参数
  - `user_info` - 用户信息
  - `exchange_type` - 兑换类型
  - `options` - 选项参数

  ## 返回
  - `{:ok, config}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_exchange_config(user_info, exchange_type, options \\ []) do
    Logger.info("💳 [支付系统查询构建器] 获取用户兑换配置: 类型=#{exchange_type}")

    try do
      vip_level = Map.get(user_info, :vip_level, 0)
      
      # 获取该类型和VIP等级的配置
      with {:ok, configs} <- ExchangeConfigRepository.list_exchange_configs_by_type(exchange_type),
           {:ok, vip_configs} <- ExchangeConfigRepository.list_exchange_configs_by_vip_level(vip_level) do
        
        # 找到交集
        suitable_configs = find_config_intersection(configs, vip_configs)
        
        if length(suitable_configs) > 0 do
          # 选择最优配置
          optimal_config = select_best_exchange_config(suitable_configs, user_info)
          
          Logger.info("✅ [支付系统查询构建器] 用户兑换配置获取成功: #{optimal_config.config_name}")
          {:ok, optimal_config}
        else
          Logger.warn("⚠️ [支付系统查询构建器] 没有找到适合的兑换配置")
          {:error, :no_suitable_exchange_config}
        end
      else
        error ->
          Logger.error("❌ [支付系统查询构建器] 获取用户兑换配置失败: #{inspect(error)}")
          {:error, :get_exchange_config_failed}
      end
    rescue
      exception ->
        Logger.error("💥 [支付系统查询构建器] 获取用户兑换配置异常: #{inspect(exception)}")
        {:error, :get_exchange_config_exception}
    end
  end

  # ==================== 费用计算查询 ====================

  @doc """
  计算支付费用

  ## 参数
  - `gateway_id` - 网关ID
  - `payment_type` - 支付类型
  - `amount` - 支付金额
  - `options` - 选项参数

  ## 返回
  - `{:ok, fee_info}` - 成功
  - `{:error, reason}` - 失败
  """
  def calculate_payment_fees(gateway_id, payment_type, amount, options \\ []) do
    Logger.info("💳 [支付系统查询构建器] 计算支付费用: 网关=#{gateway_id}, 类型=#{payment_type}")

    try do
      # 获取支付配置
      with {:ok, configs} <- PaymentConfigRepository.list_payment_configs(%{
        "gateway_id" => gateway_id,
        "payment_type" => payment_type,
        "status" => 1
      }) do
        
        case configs do
          {[config], _count} ->
            # 计算费用
            fee_info = calculate_fees_from_config(config, amount)
            
            Logger.info("✅ [支付系统查询构建器] 支付费用计算成功")
            {:ok, fee_info}
          
          {[], _count} ->
            Logger.warn("⚠️ [支付系统查询构建器] 没有找到支付配置")
            {:error, :payment_config_not_found}
          
          {multiple_configs, _count} ->
            Logger.warn("⚠️ [支付系统查询构建器] 找到多个支付配置: #{length(multiple_configs)}")
            # 选择第一个配置
            fee_info = calculate_fees_from_config(hd(multiple_configs), amount)
            {:ok, fee_info}
        end
      else
        error ->
          Logger.error("❌ [支付系统查询构建器] 计算支付费用失败: #{inspect(error)}")
          {:error, :calculate_fees_failed}
      end
    rescue
      exception ->
        Logger.error("💥 [支付系统查询构建器] 计算支付费用异常: #{inspect(exception)}")
        {:error, :calculate_fees_exception}
    end
  end

  @doc """
  计算兑换金额

  ## 参数
  - `config_id` - 配置ID
  - `amount` - 兑换金额
  - `options` - 选项参数

  ## 返回
  - `{:ok, exchange_info}` - 成功
  - `{:error, reason}` - 失败
  """
  def calculate_exchange_amount(config_id, amount, options \\ []) do
    Logger.info("💳 [支付系统查询构建器] 计算兑换金额: 配置=#{config_id}")

    try do
      with {:ok, config} <- ExchangeConfigRepository.get_exchange_config(config_id) do
        if config do
          # 计算兑换信息
          exchange_info = calculate_exchange_from_config(config, amount)
          
          Logger.info("✅ [支付系统查询构建器] 兑换金额计算成功")
          {:ok, exchange_info}
        else
          Logger.warn("⚠️ [支付系统查询构建器] 兑换配置不存在")
          {:error, :exchange_config_not_found}
        end
      else
        error ->
          Logger.error("❌ [支付系统查询构建器] 计算兑换金额失败: #{inspect(error)}")
          {:error, :calculate_exchange_failed}
      end
    rescue
      exception ->
        Logger.error("💥 [支付系统查询构建器] 计算兑换金额异常: #{inspect(exception)}")
        {:error, :calculate_exchange_exception}
    end
  end

  # ==================== 统计分析查询 ====================

  @doc """
  获取支付系统综合统计

  ## 参数
  - `date_range` - 日期范围
  - `options` - 选项参数

  ## 返回
  - `{:ok, stats}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_payment_system_stats(date_range \\ nil, options \\ []) do
    Logger.info("💳 [支付系统查询构建器] 获取支付系统综合统计")

    try do
      # 并行获取各种统计数据
      tasks = [
        Task.async(fn -> get_gateway_stats() end),
        Task.async(fn -> get_config_stats() end),
        Task.async(fn -> get_exchange_stats() end)
      ]

      [gateway_stats, config_stats, exchange_stats] = Task.await_many(tasks, 10000)

      with {:ok, gateway_data} <- gateway_stats,
           {:ok, config_data} <- config_stats,
           {:ok, exchange_data} <- exchange_stats do
        
        comprehensive_stats = %{
          gateway_stats: gateway_data,
          config_stats: config_data,
          exchange_stats: exchange_data,
          summary: %{
            total_gateways: gateway_data.total_count,
            active_gateways: gateway_data.active_count,
            total_configs: config_data.total_count,
            active_configs: config_data.active_count,
            total_exchange_configs: exchange_data.total_count,
            active_exchange_configs: exchange_data.active_count
          },
          generated_at: DateTime.utc_now()
        }
        
        Logger.info("✅ [支付系统查询构建器] 支付系统综合统计获取成功")
        {:ok, comprehensive_stats}
      else
        error ->
          Logger.error("❌ [支付系统查询构建器] 获取支付系统综合统计失败: #{inspect(error)}")
          {:error, :get_stats_failed}
      end
    rescue
      exception ->
        Logger.error("💥 [支付系统查询构建器] 获取支付系统综合统计异常: #{inspect(exception)}")
        {:error, :get_stats_exception}
    end
  end

  # ==================== 私有辅助函数 ====================

  # 过滤适合的支付方式
  defp filter_suitable_payment_methods(gateways, configs, amount, user_info) do
    Enum.flat_map(gateways, fn gateway ->
      gateway_configs = Enum.filter(configs, &(&1.gateway_id == gateway.id))
      
      Enum.filter(gateway_configs, fn config ->
        amount_in_range?(config, amount) and user_eligible?(config, user_info)
      end)
      |> Enum.map(fn config ->
        %{
          gateway: gateway,
          config: config,
          priority: calculate_priority(gateway, config)
        }
      end)
    end)
  end

  # 检查金额是否在范围内
  defp amount_in_range?(config, amount) do
    decimal_amount = Decimal.new(amount)
    Decimal.compare(decimal_amount, config.min_amount) != :lt and
    Decimal.compare(decimal_amount, config.max_amount) != :gt
  end

  # 检查用户是否符合条件
  defp user_eligible?(_config, _user_info) do
    # 这里可以添加更多用户资格检查逻辑
    true
  end

  # 计算优先级
  defp calculate_priority(gateway, config) do
    # 基础优先级
    base_priority = gateway.sort_order || 0
    
    # 费率影响（费率越低优先级越高）
    fee_factor = if Decimal.compare(config.fee_rate, Decimal.new("0")) == :gt do
      -Decimal.to_float(config.fee_rate)
    else
      0
    end
    
    base_priority + fee_factor
  end

  # 按优先级排序支付方式
  defp sort_payment_methods_by_priority(methods) do
    Enum.sort_by(methods, & &1.priority, :desc)
  end

  # 根据金额过滤配置
  defp filter_configs_by_amount(configs, amount) do
    Enum.filter(configs, &amount_in_range?(&1, amount))
  end

  # 选择最佳配置
  defp select_best_config(configs) do
    # 按费率和排序选择最佳配置
    Enum.min_by(configs, fn config ->
      {Decimal.to_float(config.fee_rate), config.sort_order || 999}
    end)
  end

  # 找到配置交集
  defp find_config_intersection(configs1, configs2) do
    config1_ids = MapSet.new(configs1, & &1.id)
    Enum.filter(configs2, &MapSet.member?(config1_ids, &1.id))
  end

  # 选择最佳兑换配置
  defp select_best_exchange_config(configs, user_info) do
    # 根据用户VIP等级和兑换比例选择最佳配置
    vip_level = Map.get(user_info, :vip_level, 0)
    
    Enum.max_by(configs, fn config ->
      vip_bonus = if config.vip_level_required && config.vip_level_required <= vip_level do
        1.0
      else
        0.0
      end
      
      Decimal.to_float(config.exchange_rate) + vip_bonus
    end)
  end

  # 从配置计算费用
  defp calculate_fees_from_config(config, amount) do
    decimal_amount = Decimal.new(amount)
    
    # 计算手续费
    fee_amount = Decimal.mult(decimal_amount, Decimal.div(config.fee_rate, Decimal.new("100")))
    
    # 计算扣除费用
    deduction_amount = Decimal.mult(decimal_amount, Decimal.div(config.deduction_rate, Decimal.new("100")))
    
    # 实际到账金额
    actual_amount = Decimal.sub(decimal_amount, Decimal.add(fee_amount, deduction_amount))
    
    %{
      original_amount: decimal_amount,
      fee_amount: fee_amount,
      deduction_amount: deduction_amount,
      actual_amount: actual_amount,
      fee_rate: config.fee_rate,
      deduction_rate: config.deduction_rate
    }
  end

  # 从配置计算兑换
  defp calculate_exchange_from_config(config, amount) do
    decimal_amount = Decimal.new(amount)
    
    # 计算兑换金额
    exchange_amount = Decimal.mult(decimal_amount, config.exchange_rate)
    
    # 计算手续费
    fee_amount = Decimal.mult(exchange_amount, Decimal.div(config.fee_rate, Decimal.new("100")))
    
    # 计算税费
    tax_amount = Decimal.mult(exchange_amount, Decimal.div(config.tax_rate, Decimal.new("100")))
    
    # 实际兑换金额
    actual_exchange_amount = Decimal.sub(exchange_amount, Decimal.add(fee_amount, tax_amount))
    
    %{
      original_amount: decimal_amount,
      exchange_amount: exchange_amount,
      fee_amount: fee_amount,
      tax_amount: tax_amount,
      actual_exchange_amount: actual_exchange_amount,
      exchange_rate: config.exchange_rate,
      fee_rate: config.fee_rate,
      tax_rate: config.tax_rate
    }
  end

  # 获取网关统计
  defp get_gateway_stats do
    with {:ok, {gateways, total_count}} <- PaymentGatewayRepository.list_payment_gateways(),
         {:ok, active_gateways} <- PaymentGatewayRepository.list_active_payment_gateways() do
      
      gateway_types = Enum.group_by(gateways, & &1.gateway_type)
      
      {:ok, %{
        total_count: total_count,
        active_count: length(active_gateways),
        inactive_count: total_count - length(active_gateways),
        by_type: Enum.map(gateway_types, fn {type, gateways} -> 
          {type, length(gateways)} 
        end) |> Enum.into(%{})
      }}
    else
      error -> error
    end
  end

  # 获取配置统计
  defp get_config_stats do
    with {:ok, {configs, total_count}} <- PaymentConfigRepository.list_payment_configs(),
         {:ok, active_configs} <- PaymentConfigRepository.list_active_payment_configs() do
      
      payment_types = Enum.group_by(configs, & &1.payment_type)
      
      {:ok, %{
        total_count: total_count,
        active_count: length(active_configs),
        inactive_count: total_count - length(active_configs),
        by_payment_type: Enum.map(payment_types, fn {type, configs} -> 
          {type, length(configs)} 
        end) |> Enum.into(%{})
      }}
    else
      error -> error
    end
  end

  # 获取兑换统计
  defp get_exchange_stats do
    with {:ok, {configs, total_count}} <- ExchangeConfigRepository.list_exchange_configs(),
         {:ok, active_configs} <- ExchangeConfigRepository.list_active_exchange_configs() do
      
      exchange_types = Enum.group_by(configs, & &1.exchange_type)
      
      {:ok, %{
        total_count: total_count,
        active_count: length(active_configs),
        inactive_count: total_count - length(active_configs),
        by_exchange_type: Enum.map(exchange_types, fn {type, configs} -> 
          {type, length(configs)} 
        end) |> Enum.into(%{})
      }}
    else
      error -> error
    end
  end
end
