defmodule RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionChannelRepository do
  @moduledoc """
  📢 推广渠道数据访问仓储

  负责推广渠道相关的数据访问操作，包括：
  - 推广渠道的CRUD操作
  - 渠道类型管理
  - 渠道统计数据
  - 渠道效果分析
  - 渠道链接生成
  """

  require Logger
  alias Teen.PromotionSystem.PromotionChannel
  alias RacingGame.Live.AdminPanel.Utils.CacheUtils

  @cache_ttl 600  # 10分钟缓存

  # ==================== 基础CRUD操作 ====================

  @doc """
  创建推广渠道

  ## 参数
  - `channel_data` - 渠道数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, channel}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_channel(channel_data, options \\ []) do
    Logger.info("📢 [推广渠道仓储] 创建推广渠道: #{inspect(channel_data[:channel_name])}")

    case PromotionChannel.create(channel_data) do
      {:ok, channel} ->
        Logger.info("✅ [推广渠道仓储] 推广渠道创建成功: #{channel.id}")
        clear_channel_cache(channel.promoter_id)
        {:ok, channel}

      {:error, reason} ->
        Logger.error("❌ [推广渠道仓储] 推广渠道创建失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  获取推广渠道信息

  ## 参数
  - `channel_id` - 渠道ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, channel}` - 成功
  - `{:error, :not_found}` - 未找到
  """
  def get_channel(channel_id, options \\ []) do
    cache_key = "promotion_channel:#{channel_id}"

    case CacheUtils.get_or_set(cache_key, @cache_ttl, fn ->
      case PromotionChannel.read(channel_id) do
        {:ok, channel} -> {:ok, channel}
        {:error, _} -> {:error, :not_found}
      end
    end) do
      {:ok, channel} ->
        Logger.debug("📢 [推广渠道仓储] 获取推广渠道成功: #{channel_id}")
        {:ok, channel}

      {:error, reason} ->
        Logger.warning("⚠️ [推广渠道仓储] 推广渠道不存在: #{channel_id}")
        {:error, reason}
    end
  end

  @doc """
  更新推广渠道信息

  ## 参数
  - `channel_id` - 渠道ID
  - `update_data` - 更新数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, channel}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_channel(channel_id, update_data, options \\ []) do
    Logger.info("📢 [推广渠道仓储] 更新推广渠道: #{channel_id}")

    with {:ok, channel} <- get_channel(channel_id),
         {:ok, updated_channel} <- PromotionChannel.update(channel, update_data) do
      Logger.info("✅ [推广渠道仓储] 推广渠道更新成功: #{channel_id}")
      clear_channel_cache(updated_channel.promoter_id)
      {:ok, updated_channel}
    else
      {:error, reason} ->
        Logger.error("❌ [推广渠道仓储] 推广渠道更新失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  删除推广渠道

  ## 参数
  - `channel_id` - 渠道ID
  - `options` - 选项参数

  ## 返回
  - `:ok` - 成功
  - `{:error, reason}` - 失败
  """
  def delete_channel(channel_id, options \\ []) do
    Logger.info("📢 [推广渠道仓储] 删除推广渠道: #{channel_id}")

    with {:ok, channel} <- get_channel(channel_id),
         :ok <- PromotionChannel.destroy(channel) do
      Logger.info("✅ [推广渠道仓储] 推广渠道删除成功: #{channel_id}")
      clear_channel_cache(channel.promoter_id)
      :ok
    else
      {:error, reason} ->
        Logger.error("❌ [推广渠道仓储] 推广渠道删除失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  # ==================== 推广渠道查询操作 ====================

  @doc """
  获取推广员的渠道列表

  ## 参数
  - `promoter_id` - 推广员ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, channels}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_channels_by_promoter(promoter_id, options \\ []) do
    Logger.debug("📢 [推广渠道仓储] 获取推广员渠道列表: #{promoter_id}")

    page = Keyword.get(options, :page, 1)
    page_size = Keyword.get(options, :page_size, 20)
    sort = Keyword.get(options, :sort, inserted_at: :desc)

    case PromotionChannel.read_all(
      filter: [promoter_id: promoter_id],
      page: [limit: page_size, offset: (page - 1) * page_size],
      sort: sort
    ) do
      {:ok, channels} ->
        Logger.debug("✅ [推广渠道仓储] 获取推广员渠道列表成功: #{length(channels)} 条")
        {:ok, channels}

      {:error, reason} ->
        Logger.error("❌ [推广渠道仓储] 获取推广员渠道列表失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  根据渠道类型获取渠道列表

  ## 参数
  - `channel_type` - 渠道类型
  - `options` - 选项参数

  ## 返回
  - `{:ok, channels}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_channels_by_type(channel_type, options \\ []) do
    Logger.debug("📢 [推广渠道仓储] 根据类型获取渠道列表: #{channel_type}")

    page = Keyword.get(options, :page, 1)
    page_size = Keyword.get(options, :page_size, 20)
    sort = Keyword.get(options, :sort, inserted_at: :desc)

    case PromotionChannel.read_all(
      filter: [channel_type: channel_type],
      page: [limit: page_size, offset: (page - 1) * page_size],
      sort: sort
    ) do
      {:ok, channels} ->
        Logger.debug("✅ [推广渠道仓储] 根据类型获取渠道列表成功: #{length(channels)} 条")
        {:ok, channels}

      {:error, reason} ->
        Logger.error("❌ [推广渠道仓储] 根据类型获取渠道列表失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  获取活跃推广渠道列表

  ## 参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, channels}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_active_channels(options \\ []) do
    Logger.debug("📢 [推广渠道仓储] 获取活跃推广渠道列表")

    page = Keyword.get(options, :page, 1)
    page_size = Keyword.get(options, :page_size, 20)
    sort = Keyword.get(options, :sort, inserted_at: :desc)

    case PromotionChannel.read_all(
      filter: [status: 1],
      page: [limit: page_size, offset: (page - 1) * page_size],
      sort: sort
    ) do
      {:ok, channels} ->
        Logger.debug("✅ [推广渠道仓储] 获取活跃推广渠道列表成功: #{length(channels)} 条")
        {:ok, channels}

      {:error, reason} ->
        Logger.error("❌ [推广渠道仓储] 获取活跃推广渠道列表失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  # ==================== 渠道统计操作 ====================

  @doc """
  更新渠道点击统计

  ## 参数
  - `channel_id` - 渠道ID
  - `click_count` - 点击次数增量
  - `options` - 选项参数

  ## 返回
  - `{:ok, channel}` - 成功
  - `{:error, reason}` - 失败
  """
  def increment_channel_clicks(channel_id, click_count \\ 1, options \\ []) do
    Logger.debug("📢 [推广渠道仓储] 更新渠道点击统计: #{channel_id}, +#{click_count}")

    with {:ok, channel} <- get_channel(channel_id),
         new_click_count = (channel.click_count || 0) + click_count,
         {:ok, updated_channel} <- PromotionChannel.update(channel, %{click_count: new_click_count}) do
      Logger.debug("✅ [推广渠道仓储] 渠道点击统计更新成功: #{channel_id}")
      clear_channel_cache(updated_channel.promoter_id)
      {:ok, updated_channel}
    else
      {:error, reason} ->
        Logger.error("❌ [推广渠道仓储] 渠道点击统计更新失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  更新渠道注册统计

  ## 参数
  - `channel_id` - 渠道ID
  - `register_count` - 注册次数增量
  - `options` - 选项参数

  ## 返回
  - `{:ok, channel}` - 成功
  - `{:error, reason}` - 失败
  """
  def increment_channel_registers(channel_id, register_count \\ 1, options \\ []) do
    Logger.debug("📢 [推广渠道仓储] 更新渠道注册统计: #{channel_id}, +#{register_count}")

    with {:ok, channel} <- get_channel(channel_id),
         new_register_count = (channel.register_count || 0) + register_count,
         conversion_rate = calculate_conversion_rate(channel.click_count || 0, new_register_count),
         update_data = %{register_count: new_register_count, conversion_rate: conversion_rate},
         {:ok, updated_channel} <- PromotionChannel.update(channel, update_data) do
      Logger.debug("✅ [推广渠道仓储] 渠道注册统计更新成功: #{channel_id}")
      clear_channel_cache(updated_channel.promoter_id)
      {:ok, updated_channel}
    else
      {:error, reason} ->
        Logger.error("❌ [推广渠道仓储] 渠道注册统计更新失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  # ==================== 私有辅助函数 ====================

  defp calculate_conversion_rate(click_count, register_count) when click_count > 0 do
    Decimal.div(Decimal.new(register_count), Decimal.new(click_count))
    |> Decimal.mult(Decimal.new(100))
    |> Decimal.round(2)
  end
  defp calculate_conversion_rate(_, _), do: Decimal.new("0.00")

  defp clear_channel_cache(promoter_id) do
    CacheUtils.delete_pattern("promotion_channel:*")
    CacheUtils.delete_pattern("promoter:#{promoter_id}:channels:*")
  end
end
