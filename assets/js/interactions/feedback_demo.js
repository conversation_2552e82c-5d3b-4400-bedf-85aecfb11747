/**
 * Racing Game Admin Panel - 反馈系统演示
 * 
 * 展示反馈系统各种功能的演示页面
 */

/**
 * 反馈系统演示类
 */
class FeedbackDemo {
  constructor() {
    this.init();
  }

  init() {
    this.createDemoContainer();
    this.bindEvents();
  }

  /**
   * 创建演示容器
   */
  createDemoContainer() {
    // 检查是否已存在演示容器
    if (document.getElementById('feedback-demo')) return;

    const demoContainer = document.createElement('div');
    demoContainer.id = 'feedback-demo';
    demoContainer.className = 'feedback-demo-container';
    demoContainer.innerHTML = `
      <div class="demo-header">
        <h2>反馈系统演示</h2>
        <p>测试各种通知、加载状态和用户反馈功能</p>
      </div>
      
      <div class="demo-sections">
        <!-- 通知演示 -->
        <div class="demo-section">
          <h3>通知系统</h3>
          <div class="demo-buttons">
            <button class="demo-btn success" data-action="show-success">成功通知</button>
            <button class="demo-btn error" data-action="show-error">错误通知</button>
            <button class="demo-btn warning" data-action="show-warning">警告通知</button>
            <button class="demo-btn info" data-action="show-info">信息通知</button>
            <button class="demo-btn secondary" data-action="show-custom">自定义通知</button>
            <button class="demo-btn danger" data-action="clear-all">清除所有</button>
          </div>
        </div>

        <!-- 加载状态演示 -->
        <div class="demo-section">
          <h3>加载状态</h3>
          <div class="demo-buttons">
            <button class="demo-btn primary" data-action="show-global-loading">全局加载</button>
            <button class="demo-btn primary" data-action="show-element-loading">元素加载</button>
            <button class="demo-btn primary" data-action="show-progress-loading">进度加载</button>
            <button class="demo-btn secondary" data-action="hide-all-loading">隐藏加载</button>
          </div>
          <div id="loading-demo-target" class="loading-demo-target">
            <p>这是一个演示加载状态的目标元素</p>
            <p>点击"元素加载"按钮在此元素上显示加载状态</p>
          </div>
        </div>

        <!-- 交互反馈演示 -->
        <div class="demo-section">
          <h3>交互反馈</h3>
          <div class="demo-buttons">
            <button class="demo-btn success" data-action="simulate-success">模拟成功操作</button>
            <button class="demo-btn error" data-action="simulate-error">模拟错误操作</button>
            <button class="demo-btn warning" data-action="simulate-warning">模拟警告操作</button>
            <button class="demo-btn primary" data-action="simulate-async">模拟异步操作</button>
          </div>
        </div>

        <!-- 高级功能演示 -->
        <div class="demo-section">
          <h3>高级功能</h3>
          <div class="demo-buttons">
            <button class="demo-btn info" data-action="show-persistent">持久通知</button>
            <button class="demo-btn info" data-action="show-with-actions">带操作按钮</button>
            <button class="demo-btn info" data-action="show-queue">队列通知</button>
            <button class="demo-btn secondary" data-action="toggle-sound">切换声音</button>
            <button class="demo-btn secondary" data-action="toggle-vibration">切换振动</button>
          </div>
        </div>
      </div>
    `;

    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
      .feedback-demo-container {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 90vw;
        max-width: 800px;
        max-height: 90vh;
        background: white;
        border-radius: 1rem;
        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);
        z-index: 10001;
        overflow-y: auto;
        padding: 2rem;
      }

      .demo-header {
        text-align: center;
        margin-bottom: 2rem;
        border-bottom: 1px solid #e5e7eb;
        padding-bottom: 1rem;
      }

      .demo-header h2 {
        margin: 0 0 0.5rem 0;
        color: #1f2937;
        font-size: 1.5rem;
        font-weight: 600;
      }

      .demo-header p {
        margin: 0;
        color: #6b7280;
      }

      .demo-sections {
        display: grid;
        gap: 2rem;
      }

      .demo-section {
        border: 1px solid #e5e7eb;
        border-radius: 0.5rem;
        padding: 1.5rem;
      }

      .demo-section h3 {
        margin: 0 0 1rem 0;
        color: #1f2937;
        font-size: 1.125rem;
        font-weight: 600;
      }

      .demo-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 0.75rem;
      }

      .demo-btn {
        padding: 0.5rem 1rem;
        border: none;
        border-radius: 0.375rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: 0.875rem;
      }

      .demo-btn.primary {
        background: #3b82f6;
        color: white;
      }

      .demo-btn.primary:hover {
        background: #2563eb;
      }

      .demo-btn.success {
        background: #10b981;
        color: white;
      }

      .demo-btn.success:hover {
        background: #059669;
      }

      .demo-btn.error, .demo-btn.danger {
        background: #ef4444;
        color: white;
      }

      .demo-btn.error:hover, .demo-btn.danger:hover {
        background: #dc2626;
      }

      .demo-btn.warning {
        background: #f59e0b;
        color: white;
      }

      .demo-btn.warning:hover {
        background: #d97706;
      }

      .demo-btn.info {
        background: #3b82f6;
        color: white;
      }

      .demo-btn.info:hover {
        background: #2563eb;
      }

      .demo-btn.secondary {
        background: #6b7280;
        color: white;
      }

      .demo-btn.secondary:hover {
        background: #4b5563;
      }

      .loading-demo-target {
        margin-top: 1rem;
        padding: 1rem;
        border: 2px dashed #d1d5db;
        border-radius: 0.5rem;
        text-align: center;
        color: #6b7280;
        position: relative;
        min-height: 100px;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      @media (max-width: 640px) {
        .feedback-demo-container {
          width: 95vw;
          padding: 1rem;
        }

        .demo-buttons {
          gap: 0.5rem;
        }

        .demo-btn {
          font-size: 0.8rem;
          padding: 0.375rem 0.75rem;
        }
      }
    `;
    document.head.appendChild(style);

    document.body.appendChild(demoContainer);
  }

  /**
   * 绑定事件
   */
  bindEvents() {
    document.addEventListener('click', (e) => {
      if (!e.target.matches('[data-action]')) return;

      const action = e.target.dataset.action;
      this.handleAction(action, e.target);
    });

    // ESC键关闭演示
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        this.closeDemoContainer();
      }
    });
  }

  /**
   * 处理演示操作
   */
  handleAction(action, button) {
    const feedback = window.feedbackSystem;
    if (!feedback) {
      console.error('Feedback system not available');
      return;
    }

    switch (action) {
      case 'show-success':
        feedback.success('操作成功完成！', {
          duration: 3000
        });
        break;

      case 'show-error':
        feedback.error('发生了一个错误，请重试。', {
          duration: 5000,
          actions: [{
            id: 'retry',
            label: '重试',
            handler: () => console.log('重试操作')
          }]
        });
        break;

      case 'show-warning':
        feedback.warning('请注意：此操作不可撤销。', {
          duration: 4000
        });
        break;

      case 'show-info':
        feedback.info('这是一条信息提示。', {
          duration: 3000
        });
        break;

      case 'show-custom':
        feedback.showNotification('info', '这是一个自定义通知', {
          duration: 0, // 不自动隐藏
          icon: '🎉',
          actions: [
            {
              id: 'ok',
              label: '确定',
              handler: () => console.log('确定')
            },
            {
              id: 'cancel',
              label: '取消',
              handler: () => console.log('取消')
            }
          ]
        });
        break;

      case 'clear-all':
        feedback.clearAllNotifications();
        break;

      case 'show-global-loading':
        feedback.showGlobalLoading('正在处理请求...');
        setTimeout(() => {
          feedback.hideGlobalLoading();
          feedback.success('处理完成！');
        }, 3000);
        break;

      case 'show-element-loading':
        const target = document.getElementById('loading-demo-target');
        const loadingId = feedback.showLoading(target, {
          text: '加载数据中...',
          spinner: 'dots'
        });
        setTimeout(() => {
          feedback.hideLoading(loadingId);
          feedback.success('数据加载完成！');
        }, 2000);
        break;

      case 'show-progress-loading':
        feedback.showGlobalLoading('上传文件中...', 0);
        let progress = 0;
        const interval = setInterval(() => {
          progress += 10;
          feedback.showGlobalLoading(`上传文件中... ${progress}%`, progress);
          if (progress >= 100) {
            clearInterval(interval);
            setTimeout(() => {
              feedback.hideGlobalLoading();
              feedback.success('文件上传成功！');
            }, 500);
          }
        }, 200);
        break;

      case 'hide-all-loading':
        feedback.hideGlobalLoading();
        // 隐藏所有元素加载状态
        for (const [id] of feedback.loadingStates) {
          feedback.hideLoading(id);
        }
        break;

      case 'simulate-success':
        button.disabled = true;
        button.textContent = '处理中...';
        setTimeout(() => {
          button.disabled = false;
          button.textContent = '模拟成功操作';
          feedback.success('操作成功完成！');
        }, 1500);
        break;

      case 'simulate-error':
        button.disabled = true;
        button.textContent = '处理中...';
        setTimeout(() => {
          button.disabled = false;
          button.textContent = '模拟错误操作';
          feedback.error('操作失败，请重试。');
        }, 1500);
        break;

      case 'simulate-warning':
        feedback.warning('即将执行危险操作...', {
          actions: [{
            id: 'confirm',
            label: '确认',
            handler: () => {
              setTimeout(() => {
                feedback.success('操作已确认执行');
              }, 500);
            }
          }, {
            id: 'cancel',
            label: '取消',
            handler: () => feedback.info('操作已取消')
          }]
        });
        break;

      case 'simulate-async':
        const asyncLoadingId = feedback.showLoading(button, {
          text: '异步处理中...',
          spinner: 'wave'
        });
        
        // 模拟异步操作
        Promise.resolve()
          .then(() => new Promise(resolve => setTimeout(resolve, 1000)))
          .then(() => {
            feedback.hideLoading(asyncLoadingId);
            feedback.success('异步操作完成！');
          })
          .catch(() => {
            feedback.hideLoading(asyncLoadingId);
            feedback.error('异步操作失败！');
          });
        break;

      case 'show-persistent':
        feedback.showNotification('info', '这是一个持久通知，不会自动消失', {
          duration: 0,
          closable: true
        });
        break;

      case 'show-with-actions':
        feedback.showNotification('warning', '确认删除此项目？', {
          duration: 0,
          actions: [
            {
              id: 'delete',
              label: '删除',
              handler: () => feedback.success('项目已删除')
            },
            {
              id: 'cancel',
              label: '取消',
              handler: () => feedback.info('操作已取消')
            }
          ]
        });
        break;

      case 'show-queue':
        // 快速显示多个通知测试队列功能
        for (let i = 1; i <= 8; i++) {
          setTimeout(() => {
            feedback.info(`队列通知 ${i}`, { duration: 2000 });
          }, i * 100);
        }
        break;

      case 'toggle-sound':
        feedback.options.enableSound = !feedback.options.enableSound;
        feedback.info(`声音效果已${feedback.options.enableSound ? '开启' : '关闭'}`);
        break;

      case 'toggle-vibration':
        feedback.options.enableVibration = !feedback.options.enableVibration;
        feedback.info(`振动反馈已${feedback.options.enableVibration ? '开启' : '关闭'}`);
        break;
    }
  }

  /**
   * 关闭演示容器
   */
  closeDemoContainer() {
    const container = document.getElementById('feedback-demo');
    if (container) {
      container.remove();
    }
  }

  /**
   * 显示演示
   */
  show() {
    this.createDemoContainer();
  }

  /**
   * 隐藏演示
   */
  hide() {
    this.closeDemoContainer();
  }
}

// 全局实例
window.feedbackDemo = new FeedbackDemo();

// 导出
export { FeedbackDemo };
