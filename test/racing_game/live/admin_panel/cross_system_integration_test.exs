defmodule RacingGame.Live.AdminPanel.CrossSystemIntegrationTest do
  @moduledoc """
  🧪 跨系统集成测试

  测试7个Teen系统模块之间的数据流转、业务协调、一致性保证等功能
  """

  use ExUnit.Case, async: true
  
  alias RacingGame.Live.AdminPanel.TestHelper
  
  # All Service modules
  alias RacingGame.Live.AdminPanel.Services.Teen.{
    CustomerServiceService,
    GameManagementService,
    SystemSettingsService,
    PaymentSystemService,
    ActivitySystemService,
    DataStatisticsService,
    PromotionSystemService
  }

  setup do
    TestHelper.setup_test_environment()
    test_data = TestHelper.create_test_dataset()
    {:ok, test_data: test_data}
  end

  describe "用户生命周期完整流程" do
    test "新用户注册到成为推广员的完整流程" do
      user_id = :rand.uniform(10000)
      
      # 1. 用户注册，系统自动配置
      {:ok, system_config} = SystemSettingsService.initialize_user_settings(user_id)
      assert system_config.user_id == user_id
      
      # 2. 用户首次充值
      order_data = TestHelper.generate_test_payment_order(%{
        user_id: user_id,
        amount: Decimal.new("1000")
      })
      {:ok, payment_order} = PaymentSystemService.create_payment_order(order_data)
      
      # 模拟支付成功
      payment_info = %{
        transaction_id: "TXN_#{System.system_time(:millisecond)}",
        paid_amount: payment_order.amount,
        payment_time: DateTime.utc_now()
      }
      {:ok, paid_order} = PaymentSystemService.process_payment(payment_order.id, payment_info)
      
      # 3. 根据充值金额计算VIP等级
      {:ok, vip_status} = GameManagementService.calculate_user_vip_status(user_id, paid_order.amount)
      assert vip_status.current_level.level >= 1
      
      # 4. 参与新手活动
      activity_data = TestHelper.generate_test_activity(%{
        activity_type: "newbie_bonus",
        rules: Jason.encode!(%{"target_users" => "new_users"})
      })
      {:ok, activity} = ActivitySystemService.create_activity(activity_data, :rand.uniform(100))
      {:ok, participation} = ActivitySystemService.participate_in_activity(user_id, activity.id)
      
      # 5. 获得新手奖励
      reward_data = %{
        user_id: user_id,
        activity_id: activity.id,
        reward_type: "welcome_bonus",
        reward_amount: Decimal.new("100")
      }
      {:ok, reward} = ActivitySystemService.distribute_reward(reward_data)
      
      # 6. 申请成为推广员
      application_data = %{
        real_name: "测试用户#{user_id}",
        phone: "138#{:rand.uniform(********) |> Integer.to_string() |> String.pad_leading(8, "0")}",
        bank_account: "6222#{:rand.uniform(************) |> Integer.to_string() |> String.pad_leading(12, "0")}"
      }
      {:ok, promoter} = PromotionSystemService.apply_for_promoter(user_id, application_data)
      
      # 7. 审核通过
      admin_id = :rand.uniform(100)
      {:ok, approved_promoter} = PromotionSystemService.review_promoter_application(
        promoter.id, 
        :approve, 
        admin_id, 
        "新用户表现良好，审核通过"
      )
      
      # 8. 创建推广渠道
      channel_data = %{
        channel_name: "用户#{user_id}的推广渠道",
        channel_type: "social",
        description: "社交媒体推广"
      }
      {:ok, channel} = PromotionSystemService.create_promotion_channel(approved_promoter.id, channel_data)
      
      # 验证整个流程的数据一致性
      assert paid_order.status == 1
      assert vip_status.current_level.level >= 1
      assert participation.user_id == user_id
      assert reward.status == 1
      assert approved_promoter.status == 1
      assert channel.promoter_id == approved_promoter.id
      
      # 9. 生成用户统计数据
      {:ok, user_stats} = DataStatisticsService.generate_user_statistics(user_id)
      
      assert user_stats.user_id == user_id
      assert Decimal.compare(user_stats.total_recharge, paid_order.amount) == :eq
      assert user_stats.vip_level == vip_status.current_level.level
    end

    test "VIP用户专享服务流程" do
      user_id = :rand.uniform(10000)
      
      # 1. 大额充值成为高级VIP
      order_data = TestHelper.generate_test_payment_order(%{
        user_id: user_id,
        amount: Decimal.new("10000")  # 大额充值
      })
      {:ok, payment_order} = PaymentSystemService.create_payment_order(order_data)
      
      # 模拟支付成功
      payment_info = %{
        transaction_id: "VIP_TXN_#{System.system_time(:millisecond)}",
        paid_amount: payment_order.amount,
        payment_time: DateTime.utc_now()
      }
      {:ok, paid_order} = PaymentSystemService.process_payment(payment_order.id, payment_info)
      
      # 2. 自动升级到高级VIP
      {:ok, vip_status} = GameManagementService.calculate_user_vip_status(user_id, paid_order.amount)
      
      if vip_status.next_level do
        {:ok, upgrade_result} = GameManagementService.process_vip_upgrade(
          user_id, 
          vip_status.next_level.level
        )
        assert upgrade_result.success == true
      end
      
      # 3. 享受VIP专属客服服务
      vip_ticket_data = TestHelper.generate_test_ticket(%{
        user_id: user_id,
        priority: 1,  # 高优先级
        category: "vip_service"
      })
      {:ok, vip_ticket} = CustomerServiceService.create_ticket(vip_ticket_data)
      
      # VIP工单应该被优先处理
      vip_agent_id = :rand.uniform(10) + 90  # VIP专属客服ID范围
      {:ok, assigned_ticket} = CustomerServiceService.assign_ticket(vip_ticket.id, vip_agent_id)
      
      # 4. 参与VIP专属活动
      vip_activity_data = TestHelper.generate_test_activity(%{
        activity_type: "vip_exclusive",
        rules: Jason.encode!(%{
          "min_vip_level" => 3,
          "max_participants" => 100,
          "reward_multiplier" => 2.0
        })
      })
      {:ok, vip_activity} = ActivitySystemService.create_activity(vip_activity_data, :rand.uniform(100))
      
      if vip_status.current_level.level >= 3 do
        {:ok, vip_participation} = ActivitySystemService.participate_in_activity(user_id, vip_activity.id)
        
        # 5. 获得VIP专属奖励
        vip_reward_data = %{
          user_id: user_id,
          activity_id: vip_activity.id,
          reward_type: "vip_exclusive_bonus",
          reward_amount: Decimal.mult(Decimal.new("500"), Decimal.new("2"))  # 双倍奖励
        }
        {:ok, vip_reward} = ActivitySystemService.distribute_reward(vip_reward_data)
        
        assert Decimal.equal?(vip_reward.reward_amount, Decimal.new("1000"))
      end
      
      # 验证VIP服务的特殊待遇
      assert assigned_ticket.priority == 1
      assert vip_status.current_level.level >= 3
    end
  end

  describe "推广系统与其他系统协调" do
    test "推广员邀请用户的完整奖励流程" do
      # 1. 创建推广员
      promoter_user_id = :rand.uniform(10000)
      {:ok, promoter} = PromotionSystemService.apply_for_promoter(promoter_user_id, %{})
      {:ok, approved_promoter} = PromotionSystemService.review_promoter_application(
        promoter.id, :approve, :rand.uniform(100), "审核通过"
      )
      
      # 2. 创建推广渠道
      channel_data = %{
        channel_name: "推广员#{promoter_user_id}的渠道",
        channel_type: "referral",
        description: "好友推荐"
      }
      {:ok, channel} = PromotionSystemService.create_promotion_channel(approved_promoter.id, channel_data)
      
      # 3. 新用户通过推广链接注册
      new_user_id = :rand.uniform(10000)
      {:ok, click_result} = PromotionSystemService.handle_channel_click(channel.id)
      {:ok, conversion_result} = PromotionSystemService.handle_channel_conversion(channel.id, new_user_id)
      
      # 4. 新用户首次充值
      order_data = TestHelper.generate_test_payment_order(%{
        user_id: new_user_id,
        amount: Decimal.new("500")
      })
      {:ok, payment_order} = PaymentSystemService.create_payment_order(order_data)
      
      payment_info = %{
        transaction_id: "REF_TXN_#{System.system_time(:millisecond)}",
        paid_amount: payment_order.amount,
        payment_time: DateTime.utc_now()
      }
      {:ok, paid_order} = PaymentSystemService.process_payment(payment_order.id, payment_info)
      
      # 5. 推广员获得推广佣金
      commission_rate = Decimal.new("0.05")  # 5%佣金
      commission_amount = Decimal.mult(paid_order.amount, commission_rate)
      
      {:ok, commission_settlement} = PromotionSystemService.create_commission_settlement(
        approved_promoter,
        channel,
        new_user_id,
        commission_amount
      )
      
      # 6. 新用户获得推荐奖励
      referral_bonus_data = %{
        user_id: new_user_id,
        activity_id: nil,  # 系统奖励，不关联具体活动
        reward_type: "referral_bonus",
        reward_amount: Decimal.new("50")  # 推荐奖励
      }
      {:ok, referral_bonus} = ActivitySystemService.distribute_reward(referral_bonus_data)
      
      # 7. 推广员获得邀请奖励
      invitation_bonus_data = %{
        user_id: promoter_user_id,
        activity_id: nil,
        reward_type: "invitation_bonus", 
        reward_amount: Decimal.new("100")  # 邀请奖励
      }
      {:ok, invitation_bonus} = ActivitySystemService.distribute_reward(invitation_bonus_data)
      
      # 验证整个推广奖励流程
      assert conversion_result.channel.register_count == 1
      assert Decimal.equal?(commission_settlement.commission_amount, commission_amount)
      assert referral_bonus.user_id == new_user_id
      assert invitation_bonus.user_id == promoter_user_id
      
      # 8. 更新数据统计
      {:ok, channel_stats} = DataStatisticsService.update_channel_statistics(channel.id)
      {:ok, promoter_stats} = DataStatisticsService.update_promoter_statistics(approved_promoter.id)
      
      assert channel_stats.total_conversions >= 1
      assert Decimal.compare(promoter_stats.total_commission, Decimal.new("0")) == :gt
    end
  end

  describe "活动系统与支付系统协调" do
    test "充值活动的完整流程" do
      # 1. 创建充值返利活动
      activity_data = TestHelper.generate_test_activity(%{
        activity_type: "recharge_bonus",
        rules: Jason.encode!(%{
          "min_recharge_amount" => 1000,
          "bonus_rate" => 0.2,  # 20%返利
          "max_bonus" => 500
        })
      })
      {:ok, recharge_activity} = ActivitySystemService.create_activity(activity_data, :rand.uniform(100))
      
      # 2. 用户参与活动
      user_id = :rand.uniform(10000)
      {:ok, participation} = ActivitySystemService.participate_in_activity(user_id, recharge_activity.id)
      
      # 3. 用户充值满足活动条件
      order_data = TestHelper.generate_test_payment_order(%{
        user_id: user_id,
        amount: Decimal.new("2000")  # 满足最低充值要求
      })
      {:ok, payment_order} = PaymentSystemService.create_payment_order(order_data)
      
      payment_info = %{
        transaction_id: "ACTIVITY_TXN_#{System.system_time(:millisecond)}",
        paid_amount: payment_order.amount,
        payment_time: DateTime.utc_now()
      }
      {:ok, paid_order} = PaymentSystemService.process_payment(payment_order.id, payment_info)
      
      # 4. 自动计算并发放活动奖励
      bonus_amount = Decimal.mult(paid_order.amount, Decimal.new("0.2"))
      max_bonus = Decimal.new("500")
      actual_bonus = if Decimal.compare(bonus_amount, max_bonus) == :gt do
        max_bonus
      else
        bonus_amount
      end
      
      reward_data = %{
        user_id: user_id,
        activity_id: recharge_activity.id,
        reward_type: "recharge_bonus",
        reward_amount: actual_bonus
      }
      {:ok, activity_reward} = ActivitySystemService.distribute_reward(reward_data)
      
      # 5. 更新活动统计
      {:ok, activity_stats} = ActivitySystemService.evaluate_activity_effectiveness(recharge_activity.id)
      
      # 验证充值活动流程
      assert paid_order.status == 1
      assert Decimal.equal?(activity_reward.reward_amount, actual_bonus)
      assert activity_stats.participation_rate >= Decimal.new("0")
      
      # 6. 生成财务统计报告
      date_range = {Date.utc_today(), Date.utc_today()}
      {:ok, financial_stats} = DataStatisticsService.generate_financial_statistics(date_range)
      
      assert Decimal.compare(financial_stats.total_recharge, paid_order.amount) >= :eq
      assert Decimal.compare(financial_stats.total_bonus, activity_reward.reward_amount) >= :eq
    end
  end

  describe "客服系统与其他系统协调" do
    test "用户投诉处理的跨系统协调" do
      user_id = :rand.uniform(10000)
      
      # 1. 用户充值后遇到问题
      order_data = TestHelper.generate_test_payment_order(%{
        user_id: user_id,
        amount: Decimal.new("1000"),
        status: 2  # 支付失败
      })
      {:ok, failed_order} = PaymentSystemService.create_payment_order(order_data)
      
      # 2. 用户创建投诉工单
      complaint_data = TestHelper.generate_test_ticket(%{
        user_id: user_id,
        title: "充值失败但扣款了",
        content: "订单号：#{failed_order.order_number}，充值失败但银行卡被扣款",
        category: "payment_issue",
        priority: 2  # 高优先级
      })
      {:ok, complaint_ticket} = CustomerServiceService.create_ticket(complaint_data)
      
      # 3. 客服查看用户的支付历史
      {:ok, user_orders} = PaymentSystemService.get_user_payment_history(user_id)
      failed_orders = Enum.filter(user_orders, fn order -> order.status == 2 end)
      
      # 4. 客服查看用户的VIP状态（影响处理优先级）
      {:ok, vip_status} = GameManagementService.calculate_user_vip_status(user_id, Decimal.new("0"))
      
      # 5. 根据VIP等级分配专门的客服
      agent_id = if vip_status.current_level.level >= 3 do
        :rand.uniform(10) + 90  # VIP专属客服
      else
        :rand.uniform(50) + 1   # 普通客服
      end
      
      {:ok, assigned_ticket} = CustomerServiceService.assign_ticket(complaint_ticket.id, agent_id)
      
      # 6. 客服处理：退款并补偿
      if length(failed_orders) > 0 do
        failed_order = List.first(failed_orders)
        
        # 创建退款记录
        refund_data = %{
          original_order_id: failed_order.id,
          user_id: user_id,
          refund_amount: failed_order.amount,
          refund_reason: "充值失败退款",
          status: 0  # 待处理
        }
        {:ok, refund} = PaymentSystemService.create_refund(refund_data)
        
        # 处理退款
        {:ok, processed_refund} = PaymentSystemService.process_refund(refund.id, agent_id)
        
        # 发放补偿奖励
        compensation_data = %{
          user_id: user_id,
          activity_id: nil,
          reward_type: "service_compensation",
          reward_amount: Decimal.mult(failed_order.amount, Decimal.new("0.1"))  # 10%补偿
        }
        {:ok, compensation} = ActivitySystemService.distribute_reward(compensation_data)
        
        # 7. 客服回复并关闭工单
        reply_content = "您好，经核实您的充值确实失败，我们已为您处理退款#{Decimal.to_string(processed_refund.refund_amount)}元，并额外补偿#{Decimal.to_string(compensation.reward_amount)}元。"
        {:ok, replied_ticket} = CustomerServiceService.add_ticket_reply(
          complaint_ticket.id, 
          agent_id, 
          reply_content
        )
        
        {:ok, closed_ticket} = CustomerServiceService.close_ticket(complaint_ticket.id, agent_id)
        
        # 验证投诉处理流程
        assert processed_refund.status == 1  # 已退款
        assert compensation.status == 1      # 已发放
        assert closed_ticket.status == 2     # 已关闭
      end
      
      # 8. 更新客服绩效统计
      date_range = {Date.utc_today(), Date.utc_today()}
      {:ok, agent_performance} = CustomerServiceService.get_agent_performance(agent_id, date_range)
      
      assert agent_performance.total_handled >= 1
    end
  end

  describe "数据统计系统协调" do
    test "全系统数据统计和分析" do
      date_range = {Date.utc_today() |> Date.add(-7), Date.utc_today()}
      
      # 1. 生成各系统的统计数据
      tasks = [
        Task.async(fn -> CustomerServiceService.get_service_statistics(date_range) end),
        Task.async(fn -> GameManagementService.get_platform_statistics(date_range) end),
        Task.async(fn -> PaymentSystemService.get_payment_statistics(date_range) end),
        Task.async(fn -> ActivitySystemService.get_activity_statistics(date_range) end),
        Task.async(fn -> PromotionSystemService.get_promotion_statistics(date_range) end)
      ]
      
      results = Task.await_many(tasks, 15_000)
      
      # 验证所有统计查询都成功
      assert Enum.all?(results, fn {:ok, _} -> true; _ -> false end)
      
      [service_stats, platform_stats, payment_stats, activity_stats, promotion_stats] = results
      
      # 2. 汇总生成综合报告
      {:ok, comprehensive_report} = DataStatisticsService.generate_comprehensive_statistics(date_range)
      
      # 验证综合报告包含各系统数据
      assert TestHelper.validate_structure(comprehensive_report, [
        :online_stats, :user_stats, :financial_stats, :system_health
      ]) == :ok
      
      # 3. 数据一致性验证
      {:ok, consistency_report} = DataStatisticsService.perform_cross_system_consistency_check()
      
      assert consistency_report.overall_consistency_score >= 80  # 一致性分数应该较高
      
      # 4. 如果发现不一致，执行数据修复
      if consistency_report.overall_consistency_score < 95 do
        {:ok, repair_result} = DataStatisticsService.repair_data_inconsistencies(
          consistency_report.inconsistencies
        )
        
        assert repair_result.repair_success_rate >= 0.8  # 修复成功率应该较高
      end
    end
  end

  describe "系统健康监控" do
    test "全系统健康检查和优化建议" do
      # 1. 各系统健康检查
      health_checks = [
        Task.async(fn -> CustomerServiceService.system_health_check() end),
        Task.async(fn -> GameManagementService.system_health_check() end),
        Task.async(fn -> PaymentSystemService.system_health_check() end),
        Task.async(fn -> ActivitySystemService.system_health_check() end),
        Task.async(fn -> PromotionSystemService.system_health_check() end),
        Task.async(fn -> DataStatisticsService.system_health_check() end)
      ]
      
      health_results = Task.await_many(health_checks, 20_000)
      
      # 验证所有健康检查都成功
      assert Enum.all?(health_results, fn {:ok, _} -> true; _ -> false end)
      
      # 2. 汇总系统健康状态
      overall_health_score = health_results
      |> Enum.map(fn {:ok, health_report} -> health_report.overall_score end)
      |> Enum.sum()
      |> Kernel./(length(health_results))
      
      assert overall_health_score >= 70  # 整体健康分数应该良好
      
      # 3. 收集优化建议
      all_recommendations = health_results
      |> Enum.flat_map(fn {:ok, health_report} -> 
        Map.get(health_report, :optimization_suggestions, [])
      end)
      
      # 4. 如果健康分数较低，执行自动优化
      if overall_health_score < 80 do
        optimization_tasks = [
          Task.async(fn -> GameManagementService.perform_system_auto_optimization() end),
          Task.async(fn -> PaymentSystemService.optimize_payment_channels() end),
          Task.async(fn -> DataStatisticsService.optimize_data_processing() end)
        ]
        
        optimization_results = Task.await_many(optimization_tasks, 30_000)
        
        # 验证优化操作成功
        assert Enum.all?(optimization_results, fn {:ok, _} -> true; _ -> false end)
      end
      
      # 验证系统健康监控功能
      assert is_number(overall_health_score)
      assert is_list(all_recommendations)
    end
  end
end
