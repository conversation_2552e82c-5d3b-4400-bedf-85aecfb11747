defmodule RacingGame.Changes.RefundRejectedPoints do
  @moduledoc """
  当退费申请被拒绝时，返还已扣除的积分给用户
  """
  use Ash.Resource.Change

  alias Cypridina.Ledger

  @impl true
  def change(changeset, _opts, _context) do
    # 只在状态变为 rejected 时执行
    if Ash.Changeset.get_attribute(changeset, :status) == :rejected do
      handle_refund_rejection(changeset)
    else
      changeset
    end
  end

  defp handle_refund_rejection(changeset) do
    user_id = Ash.Changeset.get_attribute(changeset, :user_id)
    amount = Ash.Changeset.get_attribute(changeset, :amount)
    extra_data = Ash.Changeset.get_attribute(changeset, :extra_data) || %{}

    # 检查是否是退费申请且积分已被扣除
    if is_refund_with_deducted_points?(extra_data) do
      refund_amount = abs(Decimal.to_integer(amount))

      case return_points_to_user(user_id, refund_amount, extra_data) do
        {:ok, _} ->
          # 更新 extra_data 标记积分已返还
          new_extra_data = Map.put(extra_data, "points_returned", true)
          Ash.Changeset.change_attribute(changeset, :extra_data, new_extra_data)

        {:error, reason} ->
          Ash.Changeset.add_error(changeset, :base, "返还积分失败: #{reason}")
      end
    else
      changeset
    end
  end

  defp is_refund_with_deducted_points?(extra_data) do
    extra_data["request_type"] == "refund" and extra_data["points_deducted"] == true
  end

  defp return_points_to_user(user_id, amount, extra_data) do
    # 使用 Cypridina.Accounts.add_points 来增加积分并自动记录交易
    case Cypridina.Accounts.add_points(user_id, amount, [
      transaction_type: :refund,
      refund_reason: extra_data["refund_reason"],
      original_request_type: "refund",
      return_reason: "申请被拒绝",
      reason: "退费申请被拒绝，返还积分: #{extra_data["refund_reason"] || "无原因"}"
    ]) do
      %{points: _new_balance} ->
        {:ok, :points_returned}

      {:error, reason} ->
        {:error, reason}
    end
  end
end
