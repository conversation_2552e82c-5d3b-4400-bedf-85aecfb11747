defmodule Teen.GameManagement.RobotConfig do
  @moduledoc """
  机器人配置资源
  
  管理游戏机器人的配置，包括行为参数、策略等
  """
  
  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.GameManagement,
    extensions: [AshAdmin.Resource]

  postgres do
    table "robot_configs"
    repo Cypridina.Repo
  end

  admin do
    table_columns [:id, :robot_name, :game_type, :difficulty_level, :status]
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_robots
    define :list_by_game_type
    define :list_by_difficulty
  end

  actions do
    defaults [:create, :read, :update, :destroy]

    read :list_active_robots do
      filter expr(status == 1)
    end

    read :list_by_game_type do
      argument :game_type, :string, allow_nil?: false
      filter expr(game_type == ^arg(:game_type))
    end

    read :list_by_difficulty do
      argument :difficulty_level, :integer, allow_nil?: false
      filter expr(difficulty_level == ^arg(:difficulty_level))
    end

    update :enable do
      change set_attribute(:status, 1)
    end

    update :disable do
      change set_attribute(:status, 0)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :robot_name, :string do
      allow_nil? false
      public? true
      description "机器人名称"
      constraints max_length: 100
    end

    attribute :game_type, :string do
      allow_nil? false
      public? true
      description "游戏类型"
      constraints max_length: 50
    end

    attribute :difficulty_level, :integer do
      allow_nil? false
      public? true
      description "难度等级：1-简单，2-普通，3-困难，4-专家"
      constraints min: 1, max: 4
    end

    attribute :win_rate, :decimal do
      allow_nil? false
      public? true
      description "胜率（%）"
      constraints min: Decimal.new("0"), max: Decimal.new("100")
    end

    attribute :reaction_time_min, :integer do
      allow_nil? false
      public? true
      description "最小反应时间（毫秒）"
      default 1000
      constraints min: 100
    end

    attribute :reaction_time_max, :integer do
      allow_nil? false
      public? true
      description "最大反应时间（毫秒）"
      default 3000
      constraints min: 100
    end

    attribute :status, :integer do
      allow_nil? false
      public? true
      description "状态：0-禁用，1-启用"
      default 1
      constraints min: 0, max: 1
    end

    attribute :avatar_url, :string do
      allow_nil? true
      public? true
      description "头像URL"
      constraints max_length: 500
    end

    attribute :personality_traits, {:array, :string} do
      allow_nil? true
      public? true
      description "性格特征"
      default []
    end

    attribute :strategy_config, :map do
      allow_nil? true
      public? true
      description "策略配置（JSON）"
    end

    attribute :chat_messages, {:array, :string} do
      allow_nil? true
      public? true
      description "聊天消息库"
      default []
    end

    timestamps()
  end

  identities do
    identity :unique_robot_name_game, [:robot_name, :game_type]
  end
end
