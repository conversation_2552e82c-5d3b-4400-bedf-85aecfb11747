defmodule CypridinaWeb.LobbyChannel do
  @moduledoc """
  Phoenix Channel for handling Teen game messages.

  This channel processes messages sent by the client, handles authentication,
  and manages game-related communications.

  Since the WebSocket serializer has been changed to msgpack, the channel now
  receives already unpacked client messages in the format:
  %{"mainId" => main_id, "subId" => sub_id, "data" => data}

  And sends responses as map data without manual encoding.
  """
  use Phoenix.Channel
  require Logger
  alias Cypridina.Protocol.WebSocketHandler

  @impl true
  def join("game:" <> topic, params, socket) do
    Logger.info("🎮 [CHANNEL_JOIN] 客户端参加大厅频道: #{topic}, 参数: #{inspect(params)}")

    # 解析游戏频道类型
    {channel_type, channel_info} = parse_game_channel(topic)

    # Assign channel info to socket
    socket =
      socket
      |> assign(:game_id, channel_info[:game_id])
      |> assign(:channel_type, channel_type)
      |> assign(:channel_info, channel_info)

    Logger.info(
      "🎮 [CHANNEL_JOIN] 频道类型: #{channel_type}, 信息: #{inspect(channel_info)}, 频道数据: #{inspect(socket.assigns)}"
    )

    # Send a message to self to push the welcome message after join
    send(self(), :after_join)

    {:ok, socket}
  end

  # 处理客户端发送的消息 - 现在payload已经是msgpack解包后的map格式
  @impl true
  def handle_in("message", payload, socket) when is_map(payload) do
    Logger.info("🔵 [REQUEST] 收到客户端消息: #{inspect(payload)}")

    # 提取消息字段 - 客户端发送格式（data字段可能为空）:
    %{"mainId" => main_id, "subId" => sub_id} = payload
    data = Map.get(payload, "data", %{})
    # 构造标准化的消息格式给WebSocketHandler处理
    Logger.info(
      "🔵 [REQUEST] 处理消息 - MainID: #{main_id}, SubID: #{sub_id}, Data: #{inspect(data)}"
    )

    # 使用WebSocketHandler处理消息
    case WebSocketHandler.handle_message(
           %{main_id: main_id, sub_id: sub_id, data: data},
           socket.assigns
         ) do
      {:reply, response_map, new_assigns} ->
        Logger.info(
          "🟢 [RESPONSE] 处理消息成功，发送响应 - MainID: #{response_map["mainId"]}, SubID: #{response_map["subId"]}, Data: #{inspect(response_map["data"])}"
        )

        # 更新socket assigns
        socket = update_socket_assigns(socket, new_assigns)
        # 正确的Phoenix Channel响应格式 - 回复客户端请求
        {:reply, {:ok, response_map}, socket}

      {:ok, new_assigns} ->
        Logger.info("🟡 [NO_RESPONSE] 处理消息成功，无需响应 - MainID: #{main_id}, SubID: #{sub_id}")

        # 更新socket assigns但不发送响应
        socket = update_socket_assigns(socket, new_assigns)
        {:noreply, socket}

      other ->
        Logger.warning(
          "🔴 [ERROR] WebSocketHandler返回未知格式 - MainID: #{main_id}, SubID: #{sub_id}, Result: #{inspect(other)}"
        )

        # 返回错误响应给客户端
        {:reply, {:error, %{reason: "handler_error", details: inspect(other)}}, socket}
    end
  end

  @impl true
  def handle_in("heartbeat", _payload, socket) do
    # Handle heartbeat messages - 返回正确的Phoenix格式
    response = %{
      "mainId" => 0,
      "subId" => 19,
      "data" => %{
        "server_time" => System.system_time(:millisecond)
      }
    }

    {:reply, {:ok, response}, socket}
  end

  # Handle info messages from other processes
  @impl true
  def handle_info(:after_join, socket) do
    # 根据频道类型发送不同的欢迎消息
    welcome_data =
      %{
        "type" => "lobby_welcome",
        "message" => "欢迎来到游戏大厅",
        "session_id" => socket.assigns.session_id,
        "server_time" => System.system_time(:millisecond)
      }

    welcome_msg = %{
      "mainId" => 0,
      "subId" => 0,
      "data" => welcome_data
    }

    Logger.info("🎮 [WELCOME] 发送欢迎消息: #{socket.assigns.channel_type} - #{inspect(welcome_data)}")

    # Push welcome message - msgpack serializer will handle encoding
    push(socket, "message", welcome_msg)

    {:noreply, socket}
  end

  # 处理来自 PubSub 的广播消息
  def handle_info(
        %Phoenix.Socket.Broadcast{topic: _, event: "room_message", payload: payload},
        socket
      ) do
    push(socket, "message", payload)
    {:noreply, socket}
  end

  def handle_info(
        %Phoenix.Socket.Broadcast{topic: _, event: "private_message", payload: payload},
        socket
      ) do
    push(socket, "message", payload)
    {:noreply, socket}
  end

  @impl true
  def handle_info(info, socket) do
    Logger.debug("未处理的info消息: #{inspect(info)}")
    {:noreply, socket}
  end

  # Handle client disconnect
  @impl true
  def terminate(reason, socket) do
    Logger.info("Channel终止 [#{socket.assigns.session_id}]: #{inspect(reason)}")
    :ok
  end

  # 解析游戏频道类型和信息
  defp parse_game_channel("lobby"), do: {:lobby, %{}}
  defp parse_game_channel(other), do: {:unknown, %{raw: other}}

  # 辅助函数：更新socket assigns
  defp update_socket_assigns(socket, new_assigns) when is_map(new_assigns) do
    Enum.reduce(new_assigns, socket, fn {key, value}, acc ->
      assign(acc, key, value)
    end)
  end

  defp update_socket_assigns(socket, _), do: socket
end
