defmodule RacingGame.Utils.TypeConverter do
  @moduledoc """
  安全类型转换工具模块

  提供各种数据类型之间的安全转换功能。

  ## 功能特性

  ### 基础类型转换
  - 字符串转换（支持空值处理）
  - 整数转换（支持浮点数转换）
  - 布尔值转换（支持多种格式）
  - 时间转换（支持多种格式）

  ### 高级类型转换
  - 列表转换
  - 映射转换
  - 原子转换
  - 数字格式化

  ### 批量转换
  - 映射批量转换
  - 列表批量转换
  - 嵌套结构转换

  ### 验证和清理
  - 类型验证
  - 数据清理
  - 格式标准化
  """

  require Logger
  alias Cypridina.Utils.TimeHelper

  # 常量定义
  @truthy_values ["true", "yes", "on", "1", "enabled", "active", "是", "真"]
  @falsy_values ["false", "no", "off", "0", "disabled", "inactive", "否", "假"]
  @numeric_regex ~r/^-?\d+(\.\d+)?$/
  @email_regex ~r/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/

  # 类型转换配置
  @conversion_defaults %{
    string: %{empty_value: "", error_handling: :return_string},
    integer: %{error_handling: :return_error, allow_float: false},
    boolean: %{error_handling: :return_error, strict: false},
    time: %{format: :local, fallback: "-", error_handling: :return_fallback}
  }

  @doc """
  安全类型转换 - 统一入口

  ## 参数
  - `value` - 要转换的值
  - `target_type` - 目标类型（:string, :integer, :boolean, :time）
  - `opts` - 转换选项

  ## 选项
  ### 字符串转换
  - `:empty_value` - 空值时返回的默认值（默认 ""）
  - `:error_handling` - 错误处理方式（:return_string, :return_error）

  ### 整数转换
  - `:allow_float` - 是否允许浮点数转换（默认 false）
  - `:allow_partial` - 是否允许部分解析（默认 false）
  - `:error_handling` - 错误处理方式（:return_error）

  ### 布尔值转换
  - `:strict` - 是否使用严格模式（默认 false）
  - `:error_handling` - 错误处理方式（:return_error）

  ### 时间转换
  - `:format` - 时间格式（:local, :iso, :date_only）
  - `:fallback` - 转换失败时的备用值（默认 "-"）
  - `:error_handling` - 错误处理方式（:return_fallback, :return_error）

  ## 示例
      iex> TypeConverter.safe_convert("123", :integer)
      {:ok, 123}

      iex> TypeConverter.safe_convert("true", :boolean)
      {:ok, true}

      iex> TypeConverter.safe_convert(nil, :string, empty_value: "N/A")
      "N/A"
  """
  def safe_convert(value, target_type, opts \\ [])

  # 字符串转换
  def safe_convert(value, :string, opts) do
    config = get_conversion_config(:string, opts)
    perform_string_conversion(value, config)
  end

  # 整数转换
  def safe_convert(value, :integer, opts) do
    config = get_conversion_config(:integer, opts)
    perform_integer_conversion(value, config)
  end

  # 布尔值转换
  def safe_convert(value, :boolean, opts) do
    config = get_conversion_config(:boolean, opts)
    perform_boolean_conversion(value, config)
  end

  # 时间转换
  def safe_convert(value, :time, opts) do
    config = get_conversion_config(:time, opts)
    perform_time_conversion(value, config)
  end

  @doc """
  安全字符串转换

  ## 示例
      iex> TypeConverter.safe_to_string(123)
      "123"

      iex> TypeConverter.safe_to_string(nil, empty_value: "N/A")
      "N/A"
  """
  def safe_to_string(value, opts \\ []), do: safe_convert(value, :string, opts)

  @doc """
  安全整数转换

  ## 示例
      iex> TypeConverter.safe_to_integer("123")
      {:ok, 123}

      iex> TypeConverter.safe_to_integer("123.45", allow_float: true)
      {:ok, 123}
  """
  def safe_to_integer(value, opts \\ []), do: safe_convert(value, :integer, opts)

  @doc """
  安全布尔值转换

  ## 示例
      iex> TypeConverter.safe_to_boolean("yes")
      {:ok, true}

      iex> TypeConverter.safe_to_boolean("invalid", strict: true)
      {:error, "严格模式下无效的布尔值: invalid"}
  """
  def safe_to_boolean(value, opts \\ []), do: safe_convert(value, :boolean, opts)

  @doc """
  格式化时间

  ## 示例
      iex> TypeConverter.format_time(~U[2023-12-01 10:00:00Z])
      "2023-12-01 18:00:00"

      iex> TypeConverter.format_time(nil, fallback: "未知时间")
      "未知时间"
  """
  def format_time(value, opts \\ []), do: safe_convert(value, :time, opts)

  @doc """
  安全列表转换

  ## 参数
  - `value` - 要转换的值
  - `opts` - 选项
    - `:separator` - 分隔符（默认","）
    - `:trim` - 是否修剪空格（默认true）
    - `:remove_empty` - 是否移除空项（默认true）

  ## 示例
      iex> TypeConverter.safe_to_list("a,b,c")
      {:ok, ["a", "b", "c"]}

      iex> TypeConverter.safe_to_list("a, b , c", trim: true)
      {:ok, ["a", "b", "c"]}
  """
  def safe_to_list(value, opts \\ [])
  def safe_to_list(value, _opts) when is_list(value), do: {:ok, value}
  def safe_to_list(value, opts) when is_binary(value) do
    separator = Keyword.get(opts, :separator, ",")
    trim = Keyword.get(opts, :trim, true)
    remove_empty = Keyword.get(opts, :remove_empty, true)

    result = value
    |> String.split(separator)
    |> maybe_trim_list_items(trim)
    |> maybe_remove_empty_items(remove_empty)

    {:ok, result}
  end
  def safe_to_list(value, _opts), do: {:ok, [to_string(value)]}

  @doc """
  安全原子转换

  ## 参数
  - `value` - 要转换的值
  - `opts` - 选项
    - `:existing` - 是否只转换已存在的原子（默认true）

  ## 示例
      iex> TypeConverter.safe_to_atom("hello")
      {:ok, :hello}

      iex> TypeConverter.safe_to_atom("nonexistent", existing: true)
      {:error, "原子不存在: nonexistent"}
  """
  def safe_to_atom(value, opts \\ [])
  def safe_to_atom(value, _opts) when is_atom(value), do: {:ok, value}
  def safe_to_atom(value, opts) when is_binary(value) do
    existing_only = Keyword.get(opts, :existing, true)

    try do
      if existing_only do
        {:ok, String.to_existing_atom(value)}
      else
        {:ok, String.to_atom(value)}
      end
    rescue
      ArgumentError -> {:error, "原子不存在: #{value}"}
    end
  end
  def safe_to_atom(value, _opts), do: {:error, "无法转换为原子: #{inspect(value)}"}

  @doc """
  安全浮点数转换

  ## 示例
      iex> TypeConverter.safe_to_float("123.45")
      {:ok, 123.45}

      iex> TypeConverter.safe_to_float("invalid")
      {:error, "无效的浮点数格式: invalid"}
  """
  def safe_to_float(value) when is_float(value), do: {:ok, value}
  def safe_to_float(value) when is_integer(value), do: {:ok, value * 1.0}
  def safe_to_float(value) when is_binary(value) do
    case Float.parse(value) do
      {float, ""} -> {:ok, float}
      {float, _remainder} -> {:ok, float}  # 允许部分解析
      _ -> {:error, "无效的浮点数格式: #{value}"}
    end
  end
  def safe_to_float(value), do: {:error, "无法转换为浮点数: #{inspect(value)}"}

  @doc """
  验证和转换邮箱

  ## 示例
      iex> TypeConverter.validate_email("<EMAIL>")
      {:ok, "<EMAIL>"}

      iex> TypeConverter.validate_email("invalid-email")
      {:error, "无效的邮箱格式"}
  """
  def validate_email(email) when is_binary(email) do
    trimmed = String.trim(email)
    if String.match?(trimmed, @email_regex) do
      {:ok, String.downcase(trimmed)}
    else
      {:error, "无效的邮箱格式"}
    end
  end
  def validate_email(_), do: {:error, "邮箱必须是字符串"}

  @doc """
  验证数字字符串

  ## 示例
      iex> TypeConverter.validate_numeric("123.45")
      {:ok, "123.45"}

      iex> TypeConverter.validate_numeric("abc")
      {:error, "不是有效的数字"}
  """
  def validate_numeric(value) when is_binary(value) do
    if String.match?(value, @numeric_regex) do
      {:ok, value}
    else
      {:error, "不是有效的数字"}
    end
  end
  def validate_numeric(value) when is_number(value), do: {:ok, to_string(value)}
  def validate_numeric(_), do: {:error, "不是有效的数字"}

  @doc """
  批量转换映射的值

  ## 参数
  - `map` - 要转换的映射
  - `conversions` - 转换规则映射 %{key => {type, opts}}

  ## 示例
      iex> data = %{"age" => "25", "active" => "true"}
      iex> rules = %{"age" => {:integer, []}, "active" => {:boolean, []}}
      iex> TypeConverter.convert_map(data, rules)
      {:ok, %{"age" => 25, "active" => true}}
  """
  def convert_map(map, conversions) when is_map(map) and is_map(conversions) do
    try do
      converted = Map.new(map, fn {key, value} ->
        case Map.get(conversions, key) do
          {type, opts} ->
            case safe_convert(value, type, opts) do
              {:ok, converted_value} -> {key, converted_value}
              converted_value when is_binary(converted_value) -> {key, converted_value}
              _ -> {key, value}  # 转换失败时保持原值
            end
          nil -> {key, value}  # 没有转换规则时保持原值
        end
      end)
      {:ok, converted}
    rescue
      error -> {:error, "批量转换失败: #{inspect(error)}"}
    end
  end
  def convert_map(value, _conversions), do: {:error, "输入必须是映射: #{inspect(value)}"}

  @doc """
  批量转换列表

  ## 参数
  - `list` - 要转换的列表
  - `type` - 目标类型
  - `opts` - 转换选项

  ## 示例
      iex> TypeConverter.convert_list(["1", "2", "3"], :integer)
      {:ok, [1, 2, 3]}
  """
  def convert_list(list, type, opts \\ []) when is_list(list) do
    try do
      converted = Enum.map(list, fn item ->
        case safe_convert(item, type, opts) do
          {:ok, converted_value} -> converted_value
          converted_value when is_binary(converted_value) -> converted_value
          _ -> item  # 转换失败时保持原值
        end
      end)
      {:ok, converted}
    rescue
      error -> {:error, "列表转换失败: #{inspect(error)}"}
    end
  end
  def convert_list(value, _type, _opts), do: {:error, "输入必须是列表: #{inspect(value)}"}

  @doc """
  深度转换嵌套结构

  ## 参数
  - `data` - 要转换的数据
  - `schema` - 转换模式

  ## 示例
      iex> data = %{"user" => %{"age" => "25", "tags" => "a,b,c"}}
      iex> schema = %{"user" => %{"age" => {:integer, []}, "tags" => {:list, []}}}
      iex> TypeConverter.deep_convert(data, schema)
      {:ok, %{"user" => %{"age" => 25, "tags" => ["a", "b", "c"]}}}
  """
  def deep_convert(data, schema) when is_map(data) and is_map(schema) do
    try do
      converted = Map.new(data, fn {key, value} ->
        case Map.get(schema, key) do
          {type, opts} when type in [:string, :integer, :boolean, :time, :float, :atom] ->
            case safe_convert(value, type, opts) do
              {:ok, converted_value} -> {key, converted_value}
              converted_value when is_binary(converted_value) -> {key, converted_value}
              _ -> {key, value}
            end
          {:list, opts} ->
            case safe_to_list(value, opts) do
              {:ok, converted_value} -> {key, converted_value}
              _ -> {key, value}
            end
          nested_schema when is_map(nested_schema) ->
            case deep_convert(value, nested_schema) do
              {:ok, converted_value} -> {key, converted_value}
              _ -> {key, value}
            end
          nil -> {key, value}
        end
      end)
      {:ok, converted}
    rescue
      error -> {:error, "深度转换失败: #{inspect(error)}"}
    end
  end
  def deep_convert(data, _schema), do: {:ok, data}

  # ============================================================================
  # 私有辅助函数
  # ============================================================================

  # 私有函数 - 获取转换配置
  defp get_conversion_config(type, opts) do
    defaults = Map.get(@conversion_defaults, type, %{})
    user_opts = Enum.into(opts, %{})
    Map.merge(defaults, user_opts)
  end

  # 私有函数 - 可能修剪列表项
  defp maybe_trim_list_items(list, false), do: list
  defp maybe_trim_list_items(list, true), do: Enum.map(list, &String.trim/1)

  # 私有函数 - 可能移除空项
  defp maybe_remove_empty_items(list, false), do: list
  defp maybe_remove_empty_items(list, true), do: Enum.reject(list, &(&1 == ""))

  # 私有函数 - 执行字符串转换
  defp perform_string_conversion(nil, %{empty_value: empty_value}), do: empty_value
  defp perform_string_conversion(value, _config) when is_binary(value), do: value
  defp perform_string_conversion(value, config) do
    case config.error_handling do
      :return_string -> to_string(value)
      :return_error -> {:error, "无法转换为字符串: #{inspect(value)}"}
      _ -> to_string(value)
    end
  end

  # 私有函数 - 执行整数转换
  defp perform_integer_conversion(value, _config) when is_integer(value), do: {:ok, value}

  defp perform_integer_conversion(value, config) when is_float(value) do
    if config[:allow_float] do
      {:ok, trunc(value)}
    else
      handle_conversion_error("浮点数不允许转换为整数", config)
    end
  end

  defp perform_integer_conversion(value, config) when is_binary(value) do
    case Integer.parse(value) do
      {int, ""} -> {:ok, int}
      {int, _remainder} ->
        if Map.get(config, :allow_partial, false) do
          {:ok, int}
        else
          handle_conversion_error("无效的数字格式: #{value}", config)
        end
      _ -> handle_conversion_error("无效的数字格式: #{value}", config)
    end
  end

  defp perform_integer_conversion(value, config) do
    handle_conversion_error("无效的数字类型: #{inspect(value)}", config)
  end

  # 私有函数 - 执行布尔值转换
  defp perform_boolean_conversion(value, _config) when is_boolean(value), do: {:ok, value}

  defp perform_boolean_conversion(value, config) when is_binary(value) do
    normalized = String.downcase(String.trim(value))

    cond do
      normalized in @truthy_values -> {:ok, true}
      normalized in @falsy_values -> {:ok, false}
      Map.get(config, :strict, false) ->
        handle_conversion_error("严格模式下无效的布尔值: #{value}", config)
      true ->
        {:ok, false}  # 非严格模式下默认为 false
    end
  end

  defp perform_boolean_conversion(value, _config) when value in [1, "1"], do: {:ok, true}
  defp perform_boolean_conversion(value, _config) when value in [0, "0"], do: {:ok, false}

  defp perform_boolean_conversion(value, config) do
    handle_conversion_error("无效的布尔值: #{inspect(value)}", config)
  end

  # 私有函数 - 执行时间转换
  defp perform_time_conversion(nil, %{fallback: fallback}), do: fallback

  defp perform_time_conversion(datetime, config) do
    try do
      case config[:format] do
        :local -> TimeHelper.format_local_datetime(datetime)
        :iso -> DateTime.to_iso8601(datetime)
        :date_only -> datetime |> DateTime.to_date() |> Date.to_string()
        _ -> TimeHelper.format_local_datetime(datetime)
      end
    rescue
      _ -> handle_time_conversion_error(datetime, config)
    end
  end

  # 私有函数 - 处理转换错误
  defp handle_conversion_error(message, %{error_handling: :return_error}), do: {:error, message}
  defp handle_conversion_error(message, %{error_handling: :log_and_return_nil}) do
    Logger.warn("类型转换失败: #{message}")
    nil
  end
  defp handle_conversion_error(_message, _config), do: {:error, "转换失败"}

  # 私有函数 - 处理时间转换错误
  defp handle_time_conversion_error(datetime, config) do
    fallback_result = try do
      datetime |> DateTime.to_date() |> Date.to_string()
    rescue
      _ -> config[:fallback] || "无效时间"
    end

    case config[:error_handling] do
      :return_fallback -> fallback_result
      :return_error -> {:error, "时间格式化失败"}
      _ -> fallback_result
    end
  end
end
