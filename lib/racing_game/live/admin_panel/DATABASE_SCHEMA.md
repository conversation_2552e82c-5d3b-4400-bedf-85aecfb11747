# 数据库模式设计文档

## 📋 概述

本文档详细描述了 Racing Game Admin Panel 系统的完整数据库模式设计，包括表结构、关系、索引、约束和性能优化策略。

## 🏗️ 数据库架构概览

```
┌─────────────────────────────────────────────────────────────┐
│                    Racing Game Database                     │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Core System   │  │  Teen Backend   │  │   Analytics  │ │
│  │                 │  │                 │  │              │ │
│  │ • Accounts      │  │ • CustomerSvc   │  │ • Statistics │ │
│  │ • Ledger        │  │ • GameMgmt      │  │ • Reports    │ │
│  │ • Racing Game   │  │ • PaymentSys    │  │ • Monitoring │ │
│  │ • Communications│  │ • ActivitySys   │  │              │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 📊 核心系统表结构

### 1. 用户账户系统 (Cypridina.Accounts)

#### 1.1 users - 用户基础信息表
```sql
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE,
    phone VARCHAR(20) UNIQUE,
    nickname VARCHAR(100),
    avatar_url TEXT,
    status INTEGER DEFAULT 1,  -- 1:正常 0:禁用 -1:删除
    level INTEGER DEFAULT 1,
    experience_points BIGINT DEFAULT 0,
    last_login_at TIMESTAMP,
    last_login_ip INET,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_level ON users(level);
CREATE INDEX idx_users_created_at ON users(created_at);
```

#### 1.2 user_identities - 用户身份认证表
```sql
CREATE TABLE user_identities (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id),
    provider VARCHAR(50) NOT NULL,  -- password, google, facebook, etc.
    uid VARCHAR(255) NOT NULL,
    encrypted_password TEXT,
    confirmed_at TIMESTAMP,
    confirmation_token VARCHAR(255),
    reset_password_token VARCHAR(255),
    reset_password_sent_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    UNIQUE(provider, uid)
);

-- 索引
CREATE INDEX idx_user_identities_user_id ON user_identities(user_id);
CREATE INDEX idx_user_identities_provider_uid ON user_identities(provider, uid);
```

#### 1.3 tokens - 认证令牌表
```sql
CREATE TABLE tokens (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id),
    token_type VARCHAR(50) NOT NULL,  -- access, refresh, api
    token_hash VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP,
    revoked_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    
    UNIQUE(token_hash)
);

-- 索引
CREATE INDEX idx_tokens_user_id ON tokens(user_id);
CREATE INDEX idx_tokens_token_hash ON tokens(token_hash);
CREATE INDEX idx_tokens_expires_at ON tokens(expires_at);
```

### 2. 账本系统 (Cypridina.Ledger)

#### 2.1 ledger_accounts - 账户表
```sql
CREATE TABLE ledger_accounts (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id),
    account_type VARCHAR(50) NOT NULL,  -- main, bonus, frozen
    currency VARCHAR(10) NOT NULL DEFAULT 'COINS',
    balance DECIMAL(20,2) DEFAULT 0.00,
    frozen_balance DECIMAL(20,2) DEFAULT 0.00,
    total_income DECIMAL(20,2) DEFAULT 0.00,
    total_expense DECIMAL(20,2) DEFAULT 0.00,
    version INTEGER DEFAULT 1,  -- 乐观锁版本号
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    UNIQUE(user_id, account_type, currency)
);

-- 索引
CREATE INDEX idx_ledger_accounts_user_id ON ledger_accounts(user_id);
CREATE INDEX idx_ledger_accounts_type ON ledger_accounts(account_type);
CREATE INDEX idx_ledger_accounts_balance ON ledger_accounts(balance);
```

#### 2.2 ledger_transfers - 转账记录表
```sql
CREATE TABLE ledger_transfers (
    id BIGSERIAL PRIMARY KEY,
    transfer_id UUID UNIQUE NOT NULL DEFAULT gen_random_uuid(),
    from_account_id BIGINT REFERENCES ledger_accounts(id),
    to_account_id BIGINT REFERENCES ledger_accounts(id),
    amount DECIMAL(20,2) NOT NULL,
    currency VARCHAR(10) NOT NULL,
    transfer_type VARCHAR(50) NOT NULL,  -- deposit, withdraw, bet, win, etc.
    reference_type VARCHAR(50),  -- race, activity, payment
    reference_id BIGINT,
    description TEXT,
    status INTEGER DEFAULT 1,  -- 1:成功 0:失败 2:处理中
    processed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    
    CHECK (amount > 0),
    CHECK (from_account_id != to_account_id OR (from_account_id IS NULL OR to_account_id IS NULL))
);

-- 索引
CREATE INDEX idx_ledger_transfers_from_account ON ledger_transfers(from_account_id);
CREATE INDEX idx_ledger_transfers_to_account ON ledger_transfers(to_account_id);
CREATE INDEX idx_ledger_transfers_type ON ledger_transfers(transfer_type);
CREATE INDEX idx_ledger_transfers_reference ON ledger_transfers(reference_type, reference_id);
CREATE INDEX idx_ledger_transfers_created_at ON ledger_transfers(created_at);
```

### 3. 赛车游戏系统 (RacingGame)

#### 3.1 races - 比赛记录表
```sql
CREATE TABLE races (
    id BIGSERIAL PRIMARY KEY,
    race_number VARCHAR(50) UNIQUE NOT NULL,
    race_type VARCHAR(50) NOT NULL,  -- standard, premium, tournament
    track_id INTEGER NOT NULL,
    weather_condition VARCHAR(20) DEFAULT 'sunny',
    total_participants INTEGER DEFAULT 0,
    total_bet_amount DECIMAL(20,2) DEFAULT 0.00,
    total_payout DECIMAL(20,2) DEFAULT 0.00,
    house_edge DECIMAL(5,4) DEFAULT 0.05,
    status VARCHAR(20) DEFAULT 'scheduled',  -- scheduled, running, finished, cancelled
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    results JSONB,  -- 比赛结果JSON
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 索引
CREATE INDEX idx_races_race_number ON races(race_number);
CREATE INDEX idx_races_status ON races(status);
CREATE INDEX idx_races_start_time ON races(start_time);
CREATE INDEX idx_races_race_type ON races(race_type);
CREATE GIN INDEX idx_races_results ON races USING GIN(results);
```

#### 3.2 racing_game_bets - 投注记录表
```sql
CREATE TABLE racing_game_bets (
    id BIGSERIAL PRIMARY KEY,
    bet_id UUID UNIQUE NOT NULL DEFAULT gen_random_uuid(),
    user_id BIGINT NOT NULL REFERENCES users(id),
    race_id BIGINT NOT NULL REFERENCES races(id),
    bet_type VARCHAR(50) NOT NULL,  -- win, place, show, exacta, etc.
    bet_selection JSONB NOT NULL,  -- 投注选择JSON
    bet_amount DECIMAL(20,2) NOT NULL,
    potential_payout DECIMAL(20,2),
    actual_payout DECIMAL(20,2) DEFAULT 0.00,
    odds DECIMAL(10,4),
    status VARCHAR(20) DEFAULT 'pending',  -- pending, won, lost, cancelled, refunded
    placed_at TIMESTAMP DEFAULT NOW(),
    settled_at TIMESTAMP,
    
    CHECK (bet_amount > 0)
);

-- 索引
CREATE INDEX idx_racing_bets_user_id ON racing_game_bets(user_id);
CREATE INDEX idx_racing_bets_race_id ON racing_game_bets(race_id);
CREATE INDEX idx_racing_bets_status ON racing_game_bets(status);
CREATE INDEX idx_racing_bets_placed_at ON racing_game_bets(placed_at);
CREATE GIN INDEX idx_racing_bets_selection ON racing_game_bets USING GIN(bet_selection);
```

#### 3.3 system_communications - 系统通信表
```sql
CREATE TABLE system_communications (
    id BIGSERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    communication_type VARCHAR(50) NOT NULL,  -- announcement, notification, alert
    priority INTEGER DEFAULT 1,  -- 1:低 2:中 3:高 4:紧急
    target_audience VARCHAR(50) DEFAULT 'all',  -- all, vip, new_users, etc.
    status INTEGER DEFAULT 1,  -- 1:启用 0:禁用
    publish_at TIMESTAMP DEFAULT NOW(),
    expire_at TIMESTAMP,
    created_by BIGINT REFERENCES users(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 索引
CREATE INDEX idx_system_comms_type ON system_communications(communication_type);
CREATE INDEX idx_system_comms_status ON system_communications(status);
CREATE INDEX idx_system_comms_publish_at ON system_communications(publish_at);
CREATE INDEX idx_system_comms_priority ON system_communications(priority);
```

## 🔗 表关系图

```mermaid
erDiagram
    users ||--o{ user_identities : has
    users ||--o{ tokens : has
    users ||--o{ ledger_accounts : owns
    users ||--o{ racing_game_bets : places
    
    ledger_accounts ||--o{ ledger_transfers : from
    ledger_accounts ||--o{ ledger_transfers : to
    
    races ||--o{ racing_game_bets : contains
    
    users ||--o{ system_communication_reads : reads
    system_communications ||--o{ system_communication_reads : read_by
    
    users {
        bigint id PK
        varchar username UK
        varchar email UK
        varchar phone UK
        varchar nickname
        text avatar_url
        integer status
        integer level
        bigint experience_points
        timestamp last_login_at
        inet last_login_ip
        timestamp created_at
        timestamp updated_at
    }
    
    ledger_accounts {
        bigint id PK
        bigint user_id FK
        varchar account_type
        varchar currency
        decimal balance
        decimal frozen_balance
        decimal total_income
        decimal total_expense
        integer version
        timestamp created_at
        timestamp updated_at
    }
    
    races {
        bigint id PK
        varchar race_number UK
        varchar race_type
        integer track_id
        varchar weather_condition
        integer total_participants
        decimal total_bet_amount
        decimal total_payout
        decimal house_edge
        varchar status
        timestamp start_time
        timestamp end_time
        jsonb results
        timestamp created_at
        timestamp updated_at
    }
```

## 📈 性能优化策略

### 1. 索引策略
- **主键索引**: 所有表都有自增主键
- **唯一索引**: 用户名、邮箱、令牌等唯一字段
- **复合索引**: 常用查询组合字段
- **部分索引**: 针对特定条件的查询
- **GIN索引**: JSON字段的全文搜索

### 2. 分区策略
```sql
-- 按时间分区大表
CREATE TABLE ledger_transfers_y2024m01 PARTITION OF ledger_transfers
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

CREATE TABLE racing_game_bets_y2024m01 PARTITION OF racing_game_bets
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
```

### 3. 缓存策略
- **Redis缓存**: 用户会话、配置信息、热点数据
- **应用缓存**: 查询结果缓存、计算结果缓存
- **CDN缓存**: 静态资源、图片、文件

### 4. 读写分离
```
┌─────────────────┐    ┌─────────────────┐
│   Master DB     │───▶│   Slave DB 1    │
│   (Write)       │    │   (Read)        │
└─────────────────┘    └─────────────────┘
                       ┌─────────────────┐
                       │   Slave DB 2    │
                       │   (Read)        │
                       └─────────────────┘
```

## 🔒 数据安全与约束

### 1. 数据完整性约束
- **外键约束**: 确保引用完整性
- **检查约束**: 数据范围和格式验证
- **唯一约束**: 防止重复数据
- **非空约束**: 必填字段验证

### 2. 数据加密
```sql
-- 敏感数据加密存储
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- 密码加密
UPDATE user_identities 
SET encrypted_password = crypt(plain_password, gen_salt('bf', 12));
```

### 3. 审计日志
```sql
-- 操作日志表
CREATE TABLE audit_logs (
    id BIGSERIAL PRIMARY KEY,
    table_name VARCHAR(100) NOT NULL,
    operation VARCHAR(20) NOT NULL,  -- INSERT, UPDATE, DELETE
    old_values JSONB,
    new_values JSONB,
    user_id BIGINT,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);
```

## 📊 监控与维护

### 1. 性能监控
- **慢查询监控**: 识别性能瓶颈
- **连接池监控**: 数据库连接使用情况
- **磁盘空间监控**: 存储空间使用情况
- **索引使用监控**: 索引效率分析

### 2. 定期维护
```sql
-- 定期清理过期数据
DELETE FROM tokens WHERE expires_at < NOW() - INTERVAL '30 days';

-- 重建索引
REINDEX INDEX CONCURRENTLY idx_ledger_transfers_created_at;

-- 更新统计信息
ANALYZE ledger_transfers;
```

## 🏢 Teen 后台系统表结构

### 4. 客服管理系统 (Teen.CustomerService)

#### 4.1 customer_chats - 客服聊天记录表
```sql
CREATE TABLE customer_chats (
    id BIGSERIAL PRIMARY KEY,
    session_id UUID NOT NULL DEFAULT gen_random_uuid(),
    user_id BIGINT NOT NULL REFERENCES users(id),
    customer_service_id BIGINT REFERENCES users(id),
    message_type VARCHAR(20) NOT NULL,  -- text, image, file, system
    message_content TEXT NOT NULL,
    sender_type VARCHAR(20) NOT NULL,  -- user, customer_service, system
    status INTEGER DEFAULT 1,  -- 1:正常 0:删除
    read_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),

    INDEX idx_customer_chats_session(session_id),
    INDEX idx_customer_chats_user(user_id),
    INDEX idx_customer_chats_cs(customer_service_id),
    INDEX idx_customer_chats_created(created_at)
);
```

#### 4.2 user_questions - 用户问题表
```sql
CREATE TABLE user_questions (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id),
    category VARCHAR(50) NOT NULL,  -- account, payment, game, technical
    priority INTEGER DEFAULT 1,  -- 1:低 2:中 3:高 4:紧急
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',  -- pending, processing, resolved, closed
    assigned_to BIGINT REFERENCES users(id),
    resolution TEXT,
    resolved_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),

    INDEX idx_user_questions_user(user_id),
    INDEX idx_user_questions_status(status),
    INDEX idx_user_questions_category(category),
    INDEX idx_user_questions_priority(priority)
);
```

#### 4.3 exchange_orders - 兑换订单表
```sql
CREATE TABLE exchange_orders (
    id BIGSERIAL PRIMARY KEY,
    order_no VARCHAR(50) UNIQUE NOT NULL,
    user_id BIGINT NOT NULL REFERENCES users(id),
    exchange_type VARCHAR(50) NOT NULL,  -- coins_to_cash, points_to_coins
    from_currency VARCHAR(20) NOT NULL,
    to_currency VARCHAR(20) NOT NULL,
    from_amount DECIMAL(20,2) NOT NULL,
    to_amount DECIMAL(20,2) NOT NULL,
    exchange_rate DECIMAL(10,6) NOT NULL,
    fee_amount DECIMAL(20,2) DEFAULT 0.00,
    status VARCHAR(20) DEFAULT 'pending',  -- pending, processing, completed, failed, cancelled
    processed_by BIGINT REFERENCES users(id),
    processed_at TIMESTAMP,
    remarks TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),

    INDEX idx_exchange_orders_user(user_id),
    INDEX idx_exchange_orders_status(status),
    INDEX idx_exchange_orders_type(exchange_type),
    INDEX idx_exchange_orders_created(created_at)
);
```

### 5. 游戏管理系统 (Teen.GameManagement)

#### 5.1 platforms - 平台配置表
```sql
CREATE TABLE platforms (
    id BIGSERIAL PRIMARY KEY,
    platform_name VARCHAR(100) NOT NULL,
    platform_code VARCHAR(50) UNIQUE NOT NULL,
    platform_type VARCHAR(50) NOT NULL,  -- web, mobile, desktop
    status INTEGER DEFAULT 1,  -- 1:启用 0:禁用
    config JSONB NOT NULL DEFAULT '{}',
    api_endpoint TEXT,
    api_key VARCHAR(255),
    webhook_url TEXT,
    max_concurrent_users INTEGER DEFAULT 1000,
    maintenance_mode BOOLEAN DEFAULT FALSE,
    maintenance_message TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),

    INDEX idx_platforms_code(platform_code),
    INDEX idx_platforms_status(status),
    INDEX idx_platforms_type(platform_type),
    GIN INDEX idx_platforms_config ON platforms USING GIN(config)
);
```

#### 5.2 vip_levels - VIP等级表
```sql
CREATE TABLE vip_levels (
    id BIGSERIAL PRIMARY KEY,
    level_number INTEGER UNIQUE NOT NULL,
    level_name VARCHAR(100) NOT NULL,
    min_experience BIGINT NOT NULL DEFAULT 0,
    max_experience BIGINT,
    benefits JSONB NOT NULL DEFAULT '{}',
    daily_bonus DECIMAL(20,2) DEFAULT 0.00,
    weekly_bonus DECIMAL(20,2) DEFAULT 0.00,
    monthly_bonus DECIMAL(20,2) DEFAULT 0.00,
    cashback_rate DECIMAL(5,4) DEFAULT 0.0000,
    withdrawal_limit DECIMAL(20,2),
    priority_support BOOLEAN DEFAULT FALSE,
    status INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),

    INDEX idx_vip_levels_number(level_number),
    INDEX idx_vip_levels_experience(min_experience, max_experience),
    GIN INDEX idx_vip_levels_benefits ON vip_levels USING GIN(benefits)
);
```

#### 5.3 robot_configs - 机器人配置表
```sql
CREATE TABLE robot_configs (
    id BIGSERIAL PRIMARY KEY,
    robot_name VARCHAR(100) NOT NULL,
    robot_type VARCHAR(50) NOT NULL,  -- betting, chat, game_play
    difficulty_level INTEGER DEFAULT 1,  -- 1-10
    behavior_config JSONB NOT NULL DEFAULT '{}',
    active_hours JSONB DEFAULT '[]',  -- 活跃时间段
    max_bet_amount DECIMAL(20,2) DEFAULT 100.00,
    min_bet_amount DECIMAL(20,2) DEFAULT 1.00,
    win_rate DECIMAL(5,4) DEFAULT 0.5000,
    status INTEGER DEFAULT 1,
    room_assignments JSONB DEFAULT '[]',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),

    INDEX idx_robot_configs_type(robot_type),
    INDEX idx_robot_configs_status(status),
    INDEX idx_robot_configs_difficulty(difficulty_level),
    GIN INDEX idx_robot_configs_behavior ON robot_configs USING GIN(behavior_config)
);
```

### 6. 系统设置 (Teen.SystemSettings)

#### 6.1 admin_users - 管理员用户表
```sql
CREATE TABLE admin_users (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    encrypted_password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    role_id BIGINT REFERENCES roles(id),
    status INTEGER DEFAULT 1,  -- 1:正常 0:禁用 -1:删除
    last_login_at TIMESTAMP,
    last_login_ip INET,
    login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP,
    password_changed_at TIMESTAMP,
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    two_factor_secret VARCHAR(255),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),

    INDEX idx_admin_users_username(username),
    INDEX idx_admin_users_email(email),
    INDEX idx_admin_users_role(role_id),
    INDEX idx_admin_users_status(status)
);
```

#### 6.2 roles - 角色管理表
```sql
CREATE TABLE roles (
    id BIGSERIAL PRIMARY KEY,
    role_name VARCHAR(100) UNIQUE NOT NULL,
    role_code VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    permissions JSONB DEFAULT '[]',
    status INTEGER DEFAULT 1,
    created_by BIGINT REFERENCES admin_users(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),

    INDEX idx_roles_code(role_code),
    INDEX idx_roles_status(status),
    GIN INDEX idx_roles_permissions ON roles USING GIN(permissions)
);
```

#### 6.3 permissions - 权限管理表
```sql
CREATE TABLE permissions (
    id BIGSERIAL PRIMARY KEY,
    permission_name VARCHAR(100) NOT NULL,
    permission_code VARCHAR(100) UNIQUE NOT NULL,
    resource VARCHAR(100) NOT NULL,
    action VARCHAR(50) NOT NULL,
    description TEXT,
    parent_id BIGINT REFERENCES permissions(id),
    sort_order INTEGER DEFAULT 0,
    status INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),

    INDEX idx_permissions_code(permission_code),
    INDEX idx_permissions_resource(resource),
    INDEX idx_permissions_parent(parent_id),
    INDEX idx_permissions_status(status)
);
```

### 7. 支付系统 (Teen.PaymentSystem)

#### 7.1 payment_gateways - 支付网关表
```sql
CREATE TABLE payment_gateways (
    id BIGSERIAL PRIMARY KEY,
    gateway_name VARCHAR(100) NOT NULL,
    gateway_code VARCHAR(50) UNIQUE NOT NULL,
    gateway_type VARCHAR(50) NOT NULL,  -- alipay, wechat, bank, crypto
    provider VARCHAR(100) NOT NULL,
    config JSONB NOT NULL DEFAULT '{}',
    supported_currencies JSONB DEFAULT '["CNY"]',
    min_amount DECIMAL(20,2) DEFAULT 1.00,
    max_amount DECIMAL(20,2) DEFAULT 50000.00,
    fee_type VARCHAR(20) DEFAULT 'percentage',  -- fixed, percentage
    fee_value DECIMAL(10,6) DEFAULT 0.0000,
    status INTEGER DEFAULT 1,
    priority INTEGER DEFAULT 1,
    daily_limit DECIMAL(20,2),
    monthly_limit DECIMAL(20,2),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),

    INDEX idx_payment_gateways_code(gateway_code),
    INDEX idx_payment_gateways_type(gateway_type),
    INDEX idx_payment_gateways_status(status),
    INDEX idx_payment_gateways_priority(priority),
    GIN INDEX idx_payment_gateways_config ON payment_gateways USING GIN(config)
);
```

#### 7.2 payment_orders - 支付订单表
```sql
CREATE TABLE payment_orders (
    id BIGSERIAL PRIMARY KEY,
    order_no VARCHAR(50) UNIQUE NOT NULL,
    user_id BIGINT NOT NULL REFERENCES users(id),
    gateway_id BIGINT NOT NULL REFERENCES payment_gateways(id),
    amount DECIMAL(20,2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'CNY',
    fee_amount DECIMAL(20,2) DEFAULT 0.00,
    actual_amount DECIMAL(20,2),
    payment_method VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',  -- pending, processing, completed, failed, cancelled, refunded
    gateway_order_no VARCHAR(255),
    gateway_response JSONB,
    callback_url TEXT,
    return_url TEXT,
    notify_url TEXT,
    client_ip INET,
    user_agent TEXT,
    paid_at TIMESTAMP,
    expired_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),

    INDEX idx_payment_orders_user(user_id),
    INDEX idx_payment_orders_gateway(gateway_id),
    INDEX idx_payment_orders_status(status),
    INDEX idx_payment_orders_created(created_at),
    INDEX idx_payment_orders_gateway_order(gateway_order_no),
    GIN INDEX idx_payment_orders_response ON payment_orders USING GIN(gateway_response)
);
```

---

**文档版本**: v1.0
**创建日期**: 2024-12-19
**最后更新**: 2024-12-19
**维护团队**: Racing Game Development Team
