defmodule Cypridina.RoomSystem.RoomBehaviour do
  @moduledoc """
  定义游戏的通用行为接口
  """

  # @callback _type() :: atom()
  # @callback min_players() :: integer()
  # @callback max_players() :: integer()
  # @callback initial_state(room_id :: String.t(), options :: map()) :: map()

  # @callback handle_action(action :: atom(), params :: map(), state :: map()) ::
  #   {:ok, new_state :: map()} | {:error, reason :: term()}

  # @callback validate_action(action :: atom(), params :: map(), state :: map()) ::
  #   :ok | {:error, reason :: term()}

  # @callback game_over?(state :: map()) :: boolean()
  # @callback get_winner(state :: map()) :: {:ok, player_id :: String.t()} | {:draw, [player_id :: String.t()]} | :none
end
