# 性能监控指南

## 📋 概述

本文档为 Racing Game Admin Panel 系统提供全面的性能监控指南，包括监控指标、工具使用、问题诊断和优化建议。

## 📊 关键性能指标 (KPIs)

### 1. 系统响应时间指标

#### 1.1 API响应时间
```elixir
# 目标指标
- 平均响应时间: < 200ms
- 95%分位数: < 500ms
- 99%分位数: < 1000ms
- 超时率: < 0.1%
```

#### 1.2 数据库查询性能
```sql
-- 监控慢查询
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements 
WHERE mean_time > 100 
ORDER BY mean_time DESC;
```

#### 1.3 页面加载时间
- **首屏加载**: < 2秒
- **完全加载**: < 5秒
- **交互就绪**: < 3秒

### 2. 系统资源指标

#### 2.1 CPU使用率
```bash
# 监控CPU使用率
top -p $(pgrep beam.smp)
htop -p $(pgrep beam.smp)

# 目标指标
- 平均CPU使用率: < 70%
- 峰值CPU使用率: < 90%
```

#### 2.2 内存使用情况
```elixir
# Elixir内存监控
:erlang.memory()
:observer.start()

# 目标指标
- 内存使用率: < 80%
- 内存泄漏: 0
- GC频率: 合理范围内
```

#### 2.3 磁盘I/O性能
```bash
# 监控磁盘I/O
iostat -x 1
iotop

# 目标指标
- 磁盘使用率: < 85%
- I/O等待时间: < 10%
```

### 3. 数据库性能指标

#### 3.1 连接池状态
```elixir
# 监控连接池
Ecto.Adapters.SQL.query(RacingGame.Repo, "SELECT * FROM pg_stat_activity")

# 目标指标
- 活跃连接数: < 80% 池大小
- 等待连接时间: < 100ms
- 连接超时: < 0.1%
```

#### 3.2 查询性能
```sql
-- 监控查询统计
SELECT 
    schemaname,
    tablename,
    seq_scan,
    seq_tup_read,
    idx_scan,
    idx_tup_fetch
FROM pg_stat_user_tables
ORDER BY seq_scan DESC;
```

## 🛠️ 监控工具和配置

### 1. 应用层监控

#### 1.1 Phoenix LiveDashboard
```elixir
# 在router.ex中配置
import Phoenix.LiveDashboard.Router

scope "/" do
  pipe_through :browser
  live_dashboard "/dashboard", metrics: RacingGameWeb.Telemetry
end
```

#### 1.2 Telemetry配置
```elixir
# lib/racing_game_web/telemetry.ex
defmodule RacingGameWeb.Telemetry do
  use Supervisor
  import Telemetry.Metrics

  def start_link(arg) do
    Supervisor.start_link(__MODULE__, arg, name: __MODULE__)
  end

  def init(_arg) do
    children = [
      {:telemetry_poller, measurements: periodic_measurements(), period: 10_000}
    ]
    
    Supervisor.init(children, strategy: :one_for_one)
  end

  def metrics do
    [
      # Phoenix Metrics
      summary("phoenix.endpoint.stop.duration",
        unit: {:native, :millisecond}
      ),
      
      # Database Metrics
      summary("racing_game.repo.query.total_time",
        unit: {:native, :millisecond}
      ),
      
      # Custom Business Metrics
      counter("racing_game.users.login.count"),
      counter("racing_game.payments.success.count"),
      counter("racing_game.games.bet.count")
    ]
  end

  defp periodic_measurements do
    [
      {RacingGameWeb.Telemetry, :dispatch_system_metrics, []}
    ]
  end

  def dispatch_system_metrics do
    :telemetry.execute([:vm, :memory], :erlang.memory())
    :telemetry.execute([:vm, :total_run_queue_lengths], %{
      total: :erlang.statistics(:total_run_queue_lengths),
      cpu: :erlang.statistics(:run_queue_lengths)
    })
  end
end
```

### 2. 数据库监控

#### 2.1 PostgreSQL监控查询
```sql
-- 创建监控视图
CREATE VIEW performance_monitor AS
SELECT 
    now() as timestamp,
    (SELECT count(*) FROM pg_stat_activity WHERE state = 'active') as active_connections,
    (SELECT count(*) FROM pg_stat_activity WHERE state = 'idle') as idle_connections,
    (SELECT sum(numbackends) FROM pg_stat_database) as total_connections,
    (SELECT sum(tup_inserted + tup_updated + tup_deleted) FROM pg_stat_database) as total_operations;
```

#### 2.2 慢查询监控
```sql
-- 启用慢查询日志
ALTER SYSTEM SET log_min_duration_statement = 1000;
ALTER SYSTEM SET log_statement = 'all';
SELECT pg_reload_conf();

-- 查询慢查询统计
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    stddev_time,
    rows
FROM pg_stat_statements 
WHERE mean_time > 100
ORDER BY total_time DESC
LIMIT 20;
```

### 3. 系统级监控

#### 3.1 系统资源监控脚本
```bash
#!/bin/bash
# system_monitor.sh

# CPU使用率
CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')

# 内存使用率
MEM_USAGE=$(free | grep Mem | awk '{printf("%.2f"), $3/$2 * 100.0}')

# 磁盘使用率
DISK_USAGE=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')

# 网络连接数
CONNECTIONS=$(netstat -an | grep :4000 | wc -l)

echo "$(date): CPU=${CPU_USAGE}%, MEM=${MEM_USAGE}%, DISK=${DISK_USAGE}%, CONN=${CONNECTIONS}"
```

#### 3.2 日志监控
```bash
# 监控错误日志
tail -f /var/log/racing_game/error.log | grep -E "(ERROR|CRITICAL|FATAL)"

# 监控访问日志
tail -f /var/log/racing_game/access.log | awk '{print $1, $7, $9, $10}'
```

## 🔍 性能问题诊断

### 1. 响应时间过长诊断

#### 1.1 诊断步骤
```elixir
# 1. 检查Phoenix请求处理时间
# 在controller中添加日志
def index(conn, params) do
  start_time = System.monotonic_time()
  
  # 业务逻辑
  result = perform_business_logic(params)
  
  end_time = System.monotonic_time()
  duration = System.convert_time_unit(end_time - start_time, :native, :millisecond)
  
  Logger.info("Request processed in #{duration}ms")
  
  render(conn, "index.html", result: result)
end
```

#### 1.2 数据库查询优化
```sql
-- 分析查询执行计划
EXPLAIN ANALYZE SELECT * FROM users WHERE status = 1;

-- 检查索引使用情况
SELECT 
    schemaname,
    tablename,
    attname,
    n_distinct,
    correlation
FROM pg_stats 
WHERE tablename = 'users';
```

### 2. 内存泄漏诊断

#### 2.1 Elixir进程监控
```elixir
# 监控进程内存使用
defmodule RacingGame.ProcessMonitor do
  def monitor_processes do
    processes = Process.list()
    
    memory_info = 
      processes
      |> Enum.map(fn pid ->
        info = Process.info(pid, [:memory, :message_queue_len, :registered_name])
        {pid, info}
      end)
      |> Enum.sort_by(fn {_pid, info} -> info[:memory] end, :desc)
      |> Enum.take(10)
    
    Logger.info("Top 10 memory consuming processes: #{inspect(memory_info)}")
  end
end
```

#### 2.2 ETS表监控
```elixir
# 监控ETS表大小
defmodule RacingGame.ETSMonitor do
  def monitor_ets_tables do
    tables = :ets.all()
    
    table_info = 
      tables
      |> Enum.map(fn table ->
        info = :ets.info(table)
        {table, info[:size], info[:memory]}
      end)
      |> Enum.sort_by(fn {_table, _size, memory} -> memory end, :desc)
      |> Enum.take(10)
    
    Logger.info("Top 10 ETS tables by memory: #{inspect(table_info)}")
  end
end
```

### 3. 数据库性能问题

#### 3.1 锁等待诊断
```sql
-- 查看锁等待情况
SELECT 
    blocked_locks.pid AS blocked_pid,
    blocked_activity.usename AS blocked_user,
    blocking_locks.pid AS blocking_pid,
    blocking_activity.usename AS blocking_user,
    blocked_activity.query AS blocked_statement,
    blocking_activity.query AS current_statement_in_blocking_process
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
JOIN pg_catalog.pg_locks blocking_locks ON blocking_locks.locktype = blocked_locks.locktype
JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
WHERE NOT blocked_locks.granted;
```

#### 3.2 表膨胀检查
```sql
-- 检查表膨胀情况
SELECT 
    schemaname,
    tablename,
    n_tup_ins,
    n_tup_upd,
    n_tup_del,
    n_dead_tup,
    last_vacuum,
    last_autovacuum
FROM pg_stat_user_tables
WHERE n_dead_tup > 1000
ORDER BY n_dead_tup DESC;
```

## 🚨 告警和通知

### 1. 告警规则配置

#### 1.1 响应时间告警
```elixir
defmodule RacingGame.AlertManager do
  def check_response_time(duration) when duration > 1000 do
    send_alert(:high_response_time, %{
      duration: duration,
      threshold: 1000,
      timestamp: DateTime.utc_now()
    })
  end
  
  def check_response_time(_duration), do: :ok
  
  defp send_alert(type, data) do
    # 发送邮件告警
    RacingGame.Mailer.send_alert_email(type, data)
    
    # 发送短信告警
    RacingGame.SMS.send_alert_sms(type, data)
    
    # 记录告警日志
    Logger.error("Alert triggered: #{type}, data: #{inspect(data)}")
  end
end
```

#### 1.2 资源使用告警
```bash
#!/bin/bash
# alert_monitor.sh

CPU_THRESHOLD=80
MEM_THRESHOLD=85
DISK_THRESHOLD=90

CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')
MEM_USAGE=$(free | grep Mem | awk '{printf("%.0f"), $3/$2 * 100.0}')
DISK_USAGE=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')

if (( $(echo "$CPU_USAGE > $CPU_THRESHOLD" | bc -l) )); then
    echo "HIGH CPU USAGE: ${CPU_USAGE}%" | mail -s "CPU Alert" <EMAIL>
fi

if (( MEM_USAGE > MEM_THRESHOLD )); then
    echo "HIGH MEMORY USAGE: ${MEM_USAGE}%" | mail -s "Memory Alert" <EMAIL>
fi

if (( DISK_USAGE > DISK_THRESHOLD )); then
    echo "HIGH DISK USAGE: ${DISK_USAGE}%" | mail -s "Disk Alert" <EMAIL>
fi
```

### 2. 监控仪表板

#### 2.1 Grafana配置示例
```json
{
  "dashboard": {
    "title": "Racing Game Performance",
    "panels": [
      {
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, phoenix_endpoint_stop_duration_seconds)",
            "legendFormat": "95th percentile"
          }
        ]
      },
      {
        "title": "Database Connections",
        "type": "singlestat",
        "targets": [
          {
            "expr": "pg_stat_database_numbackends",
            "legendFormat": "Active Connections"
          }
        ]
      }
    ]
  }
}
```

## 📈 性能优化建议

### 1. 应用层优化

#### 1.1 缓存策略
```elixir
# 实现多级缓存
defmodule RacingGame.Cache do
  @ttl 300_000  # 5分钟

  def get(key) do
    case :ets.lookup(:cache, key) do
      [{^key, value, expires_at}] when expires_at > System.monotonic_time() ->
        {:ok, value}
      _ ->
        :miss
    end
  end

  def put(key, value) do
    expires_at = System.monotonic_time() + @ttl
    :ets.insert(:cache, {key, value, expires_at})
    :ok
  end
end
```

#### 1.2 异步处理
```elixir
# 使用GenServer处理异步任务
defmodule RacingGame.AsyncProcessor do
  use GenServer

  def start_link(_) do
    GenServer.start_link(__MODULE__, [], name: __MODULE__)
  end

  def process_async(task) do
    GenServer.cast(__MODULE__, {:process, task})
  end

  def handle_cast({:process, task}, state) do
    Task.start(fn -> perform_task(task) end)
    {:noreply, state}
  end

  defp perform_task(task) do
    # 执行耗时任务
    Logger.info("Processing task: #{inspect(task)}")
  end
end
```

### 2. 数据库优化

#### 2.1 索引优化
```sql
-- 创建复合索引
CREATE INDEX CONCURRENTLY idx_users_status_created 
ON users(status, created_at) 
WHERE status = 1;

-- 创建部分索引
CREATE INDEX CONCURRENTLY idx_orders_pending 
ON payment_orders(created_at) 
WHERE status = 'pending';
```

#### 2.2 查询优化
```elixir
# 使用预加载减少N+1查询
def list_users_with_accounts do
  User
  |> preload([:ledger_accounts, :identities])
  |> Repo.all()
end

# 使用批量操作
def batch_update_users(user_ids, attrs) do
  from(u in User, where: u.id in ^user_ids)
  |> Repo.update_all(set: attrs)
end
```

## 📋 监控检查清单

### 日常监控检查
- [ ] 检查系统响应时间是否正常
- [ ] 查看错误日志是否有异常
- [ ] 监控数据库连接池状态
- [ ] 检查磁盘空间使用情况
- [ ] 查看内存使用趋势

### 周期性检查
- [ ] 分析慢查询并优化
- [ ] 检查数据库表膨胀情况
- [ ] 清理过期数据和日志
- [ ] 更新监控告警阈值
- [ ] 生成性能报告

### 紧急情况处理
- [ ] 识别性能瓶颈
- [ ] 实施临时优化措施
- [ ] 通知相关人员
- [ ] 记录问题和解决方案
- [ ] 制定预防措施

---

**文档版本**: v1.0  
**创建日期**: 2024-12-19  
**最后更新**: 2024-12-19  
**维护团队**: Racing Game Development Team
