defmodule RacingGame.Live.AdminPanel.QueryBuilders.Teen.DataStatisticsQueryBuilder do
  @moduledoc """
  📊 数据统计查询构建器

  负责数据统计系统的复杂查询构建，包括：
  - 跨仓储的复杂统计查询
  - 数据分析和报表查询
  - 统计数据聚合查询
  - 趋势分析查询
  - 实时数据查询
  """

  require Logger

  alias RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.{
    SystemReportRepository,
    OnlineStatsRepository,
    ChannelStatsRepository,
    UserStatsRepository,
    CoinStatsRepository,
    RetentionStatsRepository,
    PaymentStatsRepository,
    LtvStatsRepository,
    RobotStatsRepository
  }

  # ==================== 综合统计查询 ====================

  @doc """
  获取系统综合统计数据

  ## 参数
  - `date_range` - 日期范围
  - `options` - 选项参数

  ## 返回
  - `{:ok, comprehensive_stats}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_comprehensive_stats(date_range, options \\ []) do
    Logger.info("📊 [数据统计查询构建器] 获取系统综合统计数据: #{inspect(date_range)}")

    try do
      {start_date, end_date} = extract_date_range(date_range)

      # 并行获取各类统计数据
      tasks = [
        Task.async(fn -> get_online_summary(start_date, end_date, options) end),
        Task.async(fn -> get_user_summary(start_date, end_date, options) end),
        Task.async(fn -> get_payment_summary(start_date, end_date, options) end),
        Task.async(fn -> get_channel_summary(start_date, end_date, options) end),
        Task.async(fn -> get_retention_summary(start_date, end_date, options) end)
      ]

      results = Task.await_many(tasks, 30_000)

      comprehensive_stats = %{
        online_stats: Enum.at(results, 0),
        user_stats: Enum.at(results, 1),
        payment_stats: Enum.at(results, 2),
        channel_stats: Enum.at(results, 3),
        retention_stats: Enum.at(results, 4),
        date_range: %{start_date: start_date, end_date: end_date},
        generated_at: DateTime.utc_now()
      }

      Logger.info("✅ [数据统计查询构建器] 系统综合统计数据获取成功")
      {:ok, comprehensive_stats}
    rescue
      exception ->
        Logger.error("💥 [数据统计查询构建器] 获取系统综合统计数据异常: #{inspect(exception)}")
        {:error, :get_comprehensive_stats_failed}
    end
  end

  @doc """
  获取实时数据仪表板

  ## 参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, dashboard_data}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_realtime_dashboard(options \\ []) do
    Logger.info("📊 [数据统计查询构建器] 获取实时数据仪表板")

    try do
      today = Date.utc_today()
      yesterday = Date.add(today, -1)

      # 并行获取实时数据
      tasks = [
        Task.async(fn -> get_today_online_stats(today, options) end),
        Task.async(fn -> get_today_user_stats(today, options) end),
        Task.async(fn -> get_today_payment_stats(today, options) end),
        Task.async(fn -> get_system_health_stats(today, options) end),
        Task.async(fn -> get_robot_activity_stats(today, options) end)
      ]

      results = Task.await_many(tasks, 15_000)

      dashboard_data = %{
        online_realtime: Enum.at(results, 0),
        user_realtime: Enum.at(results, 1),
        payment_realtime: Enum.at(results, 2),
        system_health: Enum.at(results, 3),
        robot_activity: Enum.at(results, 4),
        last_updated: DateTime.utc_now()
      }

      Logger.info("✅ [数据统计查询构建器] 实时数据仪表板获取成功")
      {:ok, dashboard_data}
    rescue
      exception ->
        Logger.error("💥 [数据统计查询构建器] 获取实时数据仪表板异常: #{inspect(exception)}")
        {:error, :get_realtime_dashboard_failed}
    end
  end

  @doc """
  获取趋势分析数据

  ## 参数
  - `metric_type` - 指标类型
  - `days` - 分析天数
  - `options` - 选项参数

  ## 返回
  - `{:ok, trend_data}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_trend_analysis(metric_type, days \\ 30, options \\ []) do
    Logger.info("📊 [数据统计查询构建器] 获取趋势分析数据: #{metric_type}, #{days}天")

    try do
      end_date = Date.utc_today()
      start_date = Date.add(end_date, -days)

      trend_data = case metric_type do
        :online -> get_online_trend(start_date, end_date, options)
        :user -> get_user_trend(start_date, end_date, options)
        :payment -> get_payment_trend(start_date, end_date, options)
        :retention -> get_retention_trend_analysis(start_date, end_date, options)
        :ltv -> get_ltv_trend_analysis(start_date, end_date, options)
        _ -> {:error, :invalid_metric_type}
      end

      case trend_data do
        {:ok, data} ->
          Logger.info("✅ [数据统计查询构建器] 趋势分析数据获取成功: #{metric_type}")
          {:ok, %{
            metric_type: metric_type,
            trend_data: data,
            date_range: %{start_date: start_date, end_date: end_date},
            analysis_period: days
          }}
        error ->
          Logger.error("❌ [数据统计查询构建器] 趋势分析数据获取失败: #{inspect(error)}")
          error
      end
    rescue
      exception ->
        Logger.error("💥 [数据统计查询构建器] 获取趋势分析数据异常: #{inspect(exception)}")
        {:error, :get_trend_analysis_failed}
    end
  end

  @doc """
  获取用户行为分析

  ## 参数
  - `analysis_type` - 分析类型
  - `date_range` - 日期范围
  - `options` - 选项参数

  ## 返回
  - `{:ok, behavior_analysis}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_behavior_analysis(analysis_type, date_range, options \\ []) do
    Logger.info("📊 [数据统计查询构建器] 获取用户行为分析: #{analysis_type}")

    try do
      {start_date, end_date} = extract_date_range(date_range)

      behavior_analysis = case analysis_type do
        :activity -> get_user_activity_analysis(start_date, end_date, options)
        :retention -> get_user_retention_analysis(start_date, end_date, options)
        :payment -> get_user_payment_analysis(start_date, end_date, options)
        :ltv -> get_user_ltv_analysis(start_date, end_date, options)
        :segmentation -> get_user_segmentation_analysis(start_date, end_date, options)
        _ -> {:error, :invalid_analysis_type}
      end

      case behavior_analysis do
        {:ok, data} ->
          Logger.info("✅ [数据统计查询构建器] 用户行为分析获取成功: #{analysis_type}")
          {:ok, %{
            analysis_type: analysis_type,
            behavior_data: data,
            date_range: %{start_date: start_date, end_date: end_date}
          }}
        error ->
          Logger.error("❌ [数据统计查询构建器] 用户行为分析获取失败: #{inspect(error)}")
          error
      end
    rescue
      exception ->
        Logger.error("💥 [数据统计查询构建器] 获取用户行为分析异常: #{inspect(exception)}")
        {:error, :get_user_behavior_analysis_failed}
    end
  end

  @doc """
  获取收入分析数据

  ## 参数
  - `analysis_period` - 分析周期
  - `options` - 选项参数

  ## 返回
  - `{:ok, revenue_analysis}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_revenue_analysis(analysis_period, options \\ []) do
    Logger.info("📊 [数据统计查询构建器] 获取收入分析数据: #{analysis_period}")

    try do
      {start_date, end_date} = get_analysis_period_dates(analysis_period)

      # 并行获取收入相关数据
      tasks = [
        Task.async(fn -> get_payment_revenue_data(start_date, end_date, options) end),
        Task.async(fn -> get_ltv_revenue_data(start_date, end_date, options) end),
        Task.async(fn -> get_channel_revenue_data(start_date, end_date, options) end),
        Task.async(fn -> get_coin_economy_data(start_date, end_date, options) end)
      ]

      results = Task.await_many(tasks, 30_000)

      revenue_analysis = %{
        payment_revenue: Enum.at(results, 0),
        ltv_analysis: Enum.at(results, 1),
        channel_performance: Enum.at(results, 2),
        coin_economy: Enum.at(results, 3),
        analysis_period: analysis_period,
        date_range: %{start_date: start_date, end_date: end_date}
      }

      Logger.info("✅ [数据统计查询构建器] 收入分析数据获取成功")
      {:ok, revenue_analysis}
    rescue
      exception ->
        Logger.error("💥 [数据统计查询构建器] 获取收入分析数据异常: #{inspect(exception)}")
        {:error, :get_revenue_analysis_failed}
    end
  end

  # ==================== 私有辅助函数 ====================

  # 提取日期范围
  defp extract_date_range({start_date, end_date}), do: {start_date, end_date}
  defp extract_date_range(%{start_date: start_date, end_date: end_date}), do: {start_date, end_date}
  defp extract_date_range(days) when is_integer(days) do
    end_date = Date.utc_today()
    start_date = Date.add(end_date, -days)
    {start_date, end_date}
  end

  # 获取分析周期日期
  defp get_analysis_period_dates(:daily) do
    today = Date.utc_today()
    {today, today}
  end
  defp get_analysis_period_dates(:weekly) do
    end_date = Date.utc_today()
    start_date = Date.add(end_date, -7)
    {start_date, end_date}
  end
  defp get_analysis_period_dates(:monthly) do
    end_date = Date.utc_today()
    start_date = Date.add(end_date, -30)
    {start_date, end_date}
  end
  defp get_analysis_period_dates(:quarterly) do
    end_date = Date.utc_today()
    start_date = Date.add(end_date, -90)
    {start_date, end_date}
  end

  # 获取在线统计摘要
  defp get_online_summary(start_date, end_date, options) do
    case OnlineStatsRepository.get_stats_by_date_range(start_date, end_date, options) do
      {:ok, stats_list} ->
        %{
          total_records: length(stats_list),
          peak_online: calculate_peak_online(stats_list),
          average_online: calculate_average_online(stats_list),
          trend: calculate_online_trend(stats_list)
        }
      {:error, _} -> %{error: "获取在线统计失败"}
    end
  end

  # 获取用户统计摘要
  defp get_user_summary(start_date, end_date, options) do
    case UserStatsRepository.get_stats_by_date_range(start_date, end_date, options) do
      {:ok, stats_list} ->
        %{
          total_records: length(stats_list),
          new_users: calculate_total_new_users(stats_list),
          active_users: calculate_total_active_users(stats_list),
          growth_rate: calculate_user_growth_rate(stats_list)
        }
      {:error, _} -> %{error: "获取用户统计失败"}
    end
  end

  # 获取支付统计摘要
  defp get_payment_summary(start_date, end_date, options) do
    case PaymentStatsRepository.get_stats_by_date_range(start_date, end_date, options) do
      {:ok, stats_list} ->
        %{
          total_records: length(stats_list),
          total_revenue: calculate_total_revenue(stats_list),
          paying_users: calculate_total_paying_users(stats_list),
          arpu: calculate_arpu(stats_list)
        }
      {:error, _} -> %{error: "获取支付统计失败"}
    end
  end

  # 获取渠道统计摘要
  defp get_channel_summary(start_date, end_date, options) do
    case ChannelStatsRepository.get_stats_by_date_range(start_date, end_date, options) do
      {:ok, stats_list} ->
        %{
          total_records: length(stats_list),
          top_channels: get_top_performing_channels(stats_list),
          total_conversions: calculate_total_conversions(stats_list),
          average_cpa: calculate_average_cpa(stats_list)
        }
      {:error, _} -> %{error: "获取渠道统计失败"}
    end
  end

  # 获取留存统计摘要
  defp get_retention_summary(start_date, end_date, options) do
    case RetentionStatsRepository.get_stats_by_date_range(start_date, end_date, options) do
      {:ok, stats_list} ->
        %{
          total_records: length(stats_list),
          day1_retention: calculate_average_retention(stats_list, 1),
          day7_retention: calculate_average_retention(stats_list, 7),
          day30_retention: calculate_average_retention(stats_list, 30)
        }
      {:error, _} -> %{error: "获取留存统计失败"}
    end
  end

  # 计算相关统计指标的辅助函数
  defp calculate_peak_online(stats_list) do
    stats_list
    |> Enum.map(& &1.peak_online_count)
    |> Enum.max(fn -> 0 end)
  end

  defp calculate_average_online(stats_list) do
    if length(stats_list) > 0 do
      total = stats_list |> Enum.map(& &1.average_online_count) |> Enum.sum()
      div(total, length(stats_list))
    else
      0
    end
  end

  defp calculate_online_trend(stats_list) do
    # 简单趋势计算：比较最后一天和第一天
    if length(stats_list) >= 2 do
      first = List.first(stats_list).average_online_count
      last = List.last(stats_list).average_online_count
      if first > 0, do: (last - first) / first * 100, else: 0
    else
      0
    end
  end

  defp calculate_total_new_users(stats_list) do
    stats_list |> Enum.map(& &1.new_user_count) |> Enum.sum()
  end

  defp calculate_total_active_users(stats_list) do
    stats_list |> Enum.map(& &1.active_user_count) |> Enum.sum()
  end

  defp calculate_user_growth_rate(stats_list) do
    if length(stats_list) >= 2 do
      first = List.first(stats_list).new_user_count
      last = List.last(stats_list).new_user_count
      if first > 0, do: (last - first) / first * 100, else: 0
    else
      0
    end
  end

  defp calculate_total_revenue(stats_list) do
    stats_list |> Enum.map(& &1.total_revenue) |> Enum.sum()
  end

  defp calculate_total_paying_users(stats_list) do
    stats_list |> Enum.map(& &1.paying_user_count) |> Enum.sum()
  end

  defp calculate_arpu(stats_list) do
    total_revenue = calculate_total_revenue(stats_list)
    total_users = calculate_total_active_users(stats_list)
    if total_users > 0, do: total_revenue / total_users, else: 0
  end

  defp get_top_performing_channels(stats_list) do
    stats_list
    |> Enum.group_by(& &1.channel_name)
    |> Enum.map(fn {channel, stats} ->
      total_conversions = stats |> Enum.map(& &1.conversion_count) |> Enum.sum()
      {channel, total_conversions}
    end)
    |> Enum.sort_by(&elem(&1, 1), :desc)
    |> Enum.take(5)
  end

  defp calculate_total_conversions(stats_list) do
    stats_list |> Enum.map(& &1.conversion_count) |> Enum.sum()
  end

  defp calculate_average_cpa(stats_list) do
    if length(stats_list) > 0 do
      total = stats_list |> Enum.map(& &1.cost_per_acquisition) |> Enum.sum()
      total / length(stats_list)
    else
      0
    end
  end

  defp calculate_average_retention(stats_list, day) do
    day_stats = stats_list |> Enum.filter(& &1.retention_day == day)
    if length(day_stats) > 0 do
      total = day_stats |> Enum.map(& &1.retention_rate) |> Enum.sum()
      total / length(day_stats)
    else
      0
    end
  end

  # 获取今日统计数据的辅助函数
  defp get_today_online_stats(today, options) do
    case OnlineStatsRepository.get_stats_by_date_range(today, today, options) do
      {:ok, [stats | _]} -> stats
      _ -> %{error: "获取今日在线统计失败"}
    end
  end

  defp get_today_user_stats(today, options) do
    case UserStatsRepository.get_stats_by_date_range(today, today, options) do
      {:ok, [stats | _]} -> stats
      _ -> %{error: "获取今日用户统计失败"}
    end
  end

  defp get_today_payment_stats(today, options) do
    case PaymentStatsRepository.get_stats_by_date_range(today, today, options) do
      {:ok, [stats | _]} -> stats
      _ -> %{error: "获取今日支付统计失败"}
    end
  end

  defp get_system_health_stats(today, options) do
    case SystemReportRepository.get_reports_by_date_range(today, today, options) do
      {:ok, [report | _]} -> report
      _ -> %{error: "获取系统健康统计失败"}
    end
  end

  defp get_robot_activity_stats(today, options) do
    case RobotStatsRepository.get_robot_activity_stats(today, options) do
      {:ok, stats} -> stats
      _ -> %{error: "获取机器人活动统计失败"}
    end
  end

  # 趋势分析辅助函数
  defp get_online_trend(start_date, end_date, options) do
    OnlineStatsRepository.get_stats_by_date_range(start_date, end_date, options)
  end

  defp get_user_trend(start_date, end_date, options) do
    UserStatsRepository.get_stats_by_date_range(start_date, end_date, options)
  end

  defp get_payment_trend(start_date, end_date, options) do
    PaymentStatsRepository.get_stats_by_date_range(start_date, end_date, options)
  end

  defp get_retention_trend_analysis(start_date, end_date, options) do
    RetentionStatsRepository.get_stats_by_date_range(start_date, end_date, options)
  end

  defp get_ltv_trend_analysis(start_date, end_date, options) do
    LtvStatsRepository.get_stats_by_date_range(start_date, end_date, options)
  end

  # 用户行为分析辅助函数
  defp get_user_activity_analysis(start_date, end_date, options) do
    UserStatsRepository.get_stats_by_date_range(start_date, end_date, options)
  end

  defp get_user_retention_analysis(start_date, end_date, options) do
    RetentionStatsRepository.get_stats_by_date_range(start_date, end_date, options)
  end

  defp get_user_payment_analysis(start_date, end_date, options) do
    PaymentStatsRepository.get_stats_by_date_range(start_date, end_date, options)
  end

  defp get_user_ltv_analysis(start_date, end_date, options) do
    LtvStatsRepository.get_stats_by_date_range(start_date, end_date, options)
  end

  defp get_user_segmentation_analysis(start_date, end_date, options) do
    # 综合多个数据源进行用户分群分析
    with {:ok, user_stats} <- UserStatsRepository.get_stats_by_date_range(start_date, end_date, options),
         {:ok, payment_stats} <- PaymentStatsRepository.get_stats_by_date_range(start_date, end_date, options),
         {:ok, ltv_stats} <- LtvStatsRepository.get_stats_by_date_range(start_date, end_date, options) do
      {:ok, %{
        user_stats: user_stats,
        payment_stats: payment_stats,
        ltv_stats: ltv_stats
      }}
    else
      error -> error
    end
  end

  # 收入分析辅助函数
  defp get_payment_revenue_data(start_date, end_date, options) do
    PaymentStatsRepository.get_stats_by_date_range(start_date, end_date, options)
  end

  defp get_ltv_revenue_data(start_date, end_date, options) do
    LtvStatsRepository.get_stats_by_date_range(start_date, end_date, options)
  end

  defp get_channel_revenue_data(start_date, end_date, options) do
    ChannelStatsRepository.get_stats_by_date_range(start_date, end_date, options)
  end

  defp get_coin_economy_data(start_date, end_date, options) do
    CoinStatsRepository.get_stats_by_date_range(start_date, end_date, options)
  end

  # ==================== 高级分析查询 ====================

  @doc """
  获取队列分析数据

  ## 参数
  - `cohort_date` - 队列日期
  - `analysis_days` - 分析天数
  - `options` - 选项参数

  ## 返回
  - `{:ok, cohort_analysis}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_cohort_analysis(cohort_date, analysis_days \\ 30, options \\ []) do
    Logger.info("📊 [数据统计查询构建器] 获取队列分析数据: #{cohort_date}, #{analysis_days}天")

    try do
      # 并行获取留存和LTV数据
      tasks = [
        Task.async(fn -> RetentionStatsRepository.get_cohort_analysis(cohort_date, options) end),
        Task.async(fn -> LtvStatsRepository.get_stats_by_cohort(cohort_date, options) end)
      ]

      results = Task.await_many(tasks, 15_000)

      cohort_analysis = %{
        cohort_date: cohort_date,
        retention_analysis: Enum.at(results, 0),
        ltv_analysis: Enum.at(results, 1),
        analysis_days: analysis_days,
        generated_at: DateTime.utc_now()
      }

      Logger.info("✅ [数据统计查询构建器] 队列分析数据获取成功")
      {:ok, cohort_analysis}
    rescue
      exception ->
        Logger.error("💥 [数据统计查询构建器] 获取队列分析数据异常: #{inspect(exception)}")
        {:error, :get_cohort_analysis_failed}
    end
  end

  @doc """
  获取渠道效果对比分析

  ## 参数
  - `date_range` - 日期范围
  - `channel_list` - 渠道列表
  - `options` - 选项参数

  ## 返回
  - `{:ok, channel_comparison}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_channel_comparison(date_range, channel_list \\ [], options \\ []) do
    Logger.info("📊 [数据统计查询构建器] 获取渠道效果对比分析: #{inspect(channel_list)}")

    try do
      {start_date, end_date} = extract_date_range(date_range)

      # 获取渠道统计数据
      case ChannelStatsRepository.get_stats_by_date_range(start_date, end_date, options) do
        {:ok, all_stats} ->
          filtered_stats = if Enum.empty?(channel_list) do
            all_stats
          else
            Enum.filter(all_stats, fn stat -> stat.channel_name in channel_list end)
          end

          channel_comparison = %{
            channels: build_channel_comparison_data(filtered_stats),
            date_range: %{start_date: start_date, end_date: end_date},
            total_channels: length(channel_list),
            comparison_metrics: calculate_channel_metrics(filtered_stats)
          }

          Logger.info("✅ [数据统计查询构建器] 渠道效果对比分析获取成功")
          {:ok, channel_comparison}

        {:error, error} ->
          Logger.error("❌ [数据统计查询构建器] 渠道效果对比分析获取失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [数据统计查询构建器] 获取渠道效果对比分析异常: #{inspect(exception)}")
        {:error, :get_channel_comparison_failed}
    end
  end

  @doc """
  获取用户价值分析

  ## 参数
  - `date_range` - 日期范围
  - `segment_type` - 分群类型
  - `options` - 选项参数

  ## 返回
  - `{:ok, user_value_analysis}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_value_analysis(date_range, segment_type \\ :ltv, options \\ []) do
    Logger.info("📊 [数据统计查询构建器] 获取用户价值分析: #{segment_type}")

    try do
      {start_date, end_date} = extract_date_range(date_range)

      # 并行获取用户相关数据
      tasks = [
        Task.async(fn -> UserStatsRepository.get_stats_by_date_range(start_date, end_date, options) end),
        Task.async(fn -> PaymentStatsRepository.get_stats_by_date_range(start_date, end_date, options) end),
        Task.async(fn -> LtvStatsRepository.get_stats_by_date_range(start_date, end_date, options) end),
        Task.async(fn -> RetentionStatsRepository.get_stats_by_date_range(start_date, end_date, options) end)
      ]

      results = Task.await_many(tasks, 30_000)

      user_value_analysis = %{
        segment_type: segment_type,
        user_stats: Enum.at(results, 0),
        payment_stats: Enum.at(results, 1),
        ltv_stats: Enum.at(results, 2),
        retention_stats: Enum.at(results, 3),
        value_segments: build_user_value_segments(results, segment_type),
        date_range: %{start_date: start_date, end_date: end_date}
      }

      Logger.info("✅ [数据统计查询构建器] 用户价值分析获取成功")
      {:ok, user_value_analysis}
    rescue
      exception ->
        Logger.error("💥 [数据统计查询构建器] 获取用户价值分析异常: #{inspect(exception)}")
        {:error, :get_user_value_analysis_failed}
    end
  end

  @doc """
  获取系统健康监控数据

  ## 参数
  - `monitor_type` - 监控类型
  - `options` - 选项参数

  ## 返回
  - `{:ok, health_monitor}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_system_health_monitor(monitor_type \\ :comprehensive, options \\ []) do
    Logger.info("📊 [数据统计查询构建器] 获取系统健康监控数据: #{monitor_type}")

    try do
      today = Date.utc_today()
      yesterday = Date.add(today, -1)

      # 并行获取系统健康相关数据
      tasks = [
        Task.async(fn -> SystemReportRepository.get_reports_by_date_range(yesterday, today, options) end),
        Task.async(fn -> OnlineStatsRepository.get_stats_by_date_range(yesterday, today, options) end),
        Task.async(fn -> RobotStatsRepository.get_robot_effect_stats(7, options) end),
        Task.async(fn -> CoinStatsRepository.get_economy_health(7, options) end)
      ]

      results = Task.await_many(tasks, 20_000)

      health_monitor = %{
        monitor_type: monitor_type,
        system_reports: Enum.at(results, 0),
        online_health: Enum.at(results, 1),
        robot_health: Enum.at(results, 2),
        economy_health: Enum.at(results, 3),
        health_score: calculate_system_health_score(results),
        alerts: generate_health_alerts(results),
        last_check: DateTime.utc_now()
      }

      Logger.info("✅ [数据统计查询构建器] 系统健康监控数据获取成功")
      {:ok, health_monitor}
    rescue
      exception ->
        Logger.error("💥 [数据统计查询构建器] 获取系统健康监控数据异常: #{inspect(exception)}")
        {:error, :get_system_health_monitor_failed}
    end
  end

  # ==================== 高级分析辅助函数 ====================

  # 构建渠道对比数据
  defp build_channel_comparison_data(stats_list) do
    stats_list
    |> Enum.group_by(& &1.channel_name)
    |> Enum.map(fn {channel, channel_stats} ->
      %{
        channel_name: channel,
        total_impressions: channel_stats |> Enum.map(& &1.impression_count) |> Enum.sum(),
        total_clicks: channel_stats |> Enum.map(& &1.click_count) |> Enum.sum(),
        total_conversions: channel_stats |> Enum.map(& &1.conversion_count) |> Enum.sum(),
        total_cost: channel_stats |> Enum.map(& &1.total_cost) |> Enum.sum(),
        average_ctr: calculate_average_ctr(channel_stats),
        average_cvr: calculate_average_cvr(channel_stats),
        average_cpa: calculate_average_cpa(channel_stats),
        roi: calculate_channel_roi(channel_stats)
      }
    end)
    |> Enum.sort_by(& &1.roi, :desc)
  end

  # 计算渠道指标
  defp calculate_channel_metrics(stats_list) do
    %{
      total_impressions: stats_list |> Enum.map(& &1.impression_count) |> Enum.sum(),
      total_clicks: stats_list |> Enum.map(& &1.click_count) |> Enum.sum(),
      total_conversions: stats_list |> Enum.map(& &1.conversion_count) |> Enum.sum(),
      overall_ctr: calculate_overall_ctr(stats_list),
      overall_cvr: calculate_overall_cvr(stats_list),
      best_performing_channel: get_best_performing_channel(stats_list),
      worst_performing_channel: get_worst_performing_channel(stats_list)
    }
  end

  # 构建用户价值分群
  defp build_user_value_segments(results, segment_type) do
    case segment_type do
      :ltv -> build_ltv_segments(Enum.at(results, 2))
      :payment -> build_payment_segments(Enum.at(results, 1))
      :retention -> build_retention_segments(Enum.at(results, 3))
      _ -> %{error: "不支持的分群类型"}
    end
  end

  # 构建LTV分群
  defp build_ltv_segments({:ok, ltv_stats}) do
    ltv_stats
    |> Enum.group_by(fn stat ->
      cond do
        stat.ltv_value >= 1000 -> :high_value
        stat.ltv_value >= 100 -> :medium_value
        true -> :low_value
      end
    end)
    |> Enum.map(fn {segment, stats} ->
      ltv_values = stats |> Enum.map(& &1.ltv_value)
      total_ltv = Enum.sum(ltv_values)
      user_count = length(stats)

      {segment, %{
        user_count: user_count,
        average_ltv: if(user_count > 0, do: total_ltv / user_count, else: 0),
        total_ltv: total_ltv
      }}
    end)
    |> Enum.into(%{})
  end
  defp build_ltv_segments(_), do: %{error: "LTV数据获取失败"}

  # 构建支付分群
  defp build_payment_segments({:ok, payment_stats}) do
    payment_stats
    |> Enum.group_by(fn stat ->
      cond do
        stat.arpu >= 50 -> :whale
        stat.arpu >= 10 -> :dolphin
        stat.arpu > 0 -> :minnow
        true -> :free
      end
    end)
    |> Enum.map(fn {segment, stats} ->
      arpu_values = stats |> Enum.map(& &1.arpu)
      total_arpu = Enum.sum(arpu_values)
      stats_count = length(stats)

      {segment, %{
        user_count: stats |> Enum.map(& &1.paying_user_count) |> Enum.sum(),
        total_revenue: stats |> Enum.map(& &1.total_revenue) |> Enum.sum(),
        average_arpu: if(stats_count > 0, do: total_arpu / stats_count, else: 0)
      }}
    end)
    |> Enum.into(%{})
  end
  defp build_payment_segments(_), do: %{error: "支付数据获取失败"}

  # 构建留存分群
  defp build_retention_segments({:ok, retention_stats}) do
    retention_stats
    |> Enum.group_by(fn stat ->
      cond do
        stat.retention_rate >= 0.5 -> :high_retention
        stat.retention_rate >= 0.2 -> :medium_retention
        true -> :low_retention
      end
    end)
    |> Enum.map(fn {segment, stats} ->
      retention_rates = stats |> Enum.map(& &1.retention_rate)
      total_retention = Enum.sum(retention_rates)
      cohort_count = length(stats)

      {segment, %{
        cohort_count: cohort_count,
        average_retention: if(cohort_count > 0, do: total_retention / cohort_count, else: 0),
        total_retained_users: stats |> Enum.map(& &1.retained_user_count) |> Enum.sum()
      }}
    end)
    |> Enum.into(%{})
  end
  defp build_retention_segments(_), do: %{error: "留存数据获取失败"}

  # 计算系统健康评分
  defp calculate_system_health_score(results) do
    # 基于各项指标计算综合健康评分 (0-100)
    base_score = 100

    # 根据各项数据调整评分
    score = base_score
    |> adjust_score_by_system_reports(Enum.at(results, 0))
    |> adjust_score_by_online_health(Enum.at(results, 1))
    |> adjust_score_by_robot_health(Enum.at(results, 2))
    |> adjust_score_by_economy_health(Enum.at(results, 3))

    max(0, min(100, score))
  end

  # 生成健康告警
  defp generate_health_alerts(results) do
    alerts = []

    alerts
    |> add_system_alerts(Enum.at(results, 0))
    |> add_online_alerts(Enum.at(results, 1))
    |> add_robot_alerts(Enum.at(results, 2))
    |> add_economy_alerts(Enum.at(results, 3))
  end

  # 评分调整辅助函数
  defp adjust_score_by_system_reports(score, {:ok, reports}) do
    # 根据系统报告调整评分
    if length(reports) > 0 do
      error_count = reports |> Enum.map(& &1.error_count) |> Enum.sum()
      if error_count > 100, do: score - 20, else: score
    else
      score - 10  # 没有报告数据扣分
    end
  end
  defp adjust_score_by_system_reports(score, _), do: score - 15

  defp adjust_score_by_online_health(score, {:ok, online_stats}) do
    # 根据在线健康状况调整评分
    if length(online_stats) > 0 do
      online_counts = online_stats |> Enum.map(& &1.average_online_count)
      total_online = Enum.sum(online_counts)
      avg_online = total_online / length(online_stats)
      if avg_online < 100, do: score - 15, else: score
    else
      score - 10
    end
  end
  defp adjust_score_by_online_health(score, _), do: score - 15

  defp adjust_score_by_robot_health(score, {:ok, robot_stats}) do
    # 根据机器人健康状况调整评分
    if length(robot_stats) > 0 do
      score  # 机器人正常运行
    else
      score - 5  # 机器人数据缺失
    end
  end
  defp adjust_score_by_robot_health(score, _), do: score - 10

  defp adjust_score_by_economy_health(score, {:ok, coin_stats}) do
    # 根据经济健康状况调整评分
    if length(coin_stats) > 0 do
      score  # 经济系统正常
    else
      score - 5  # 经济数据缺失
    end
  end
  defp adjust_score_by_economy_health(score, _), do: score - 10

  # 告警生成辅助函数
  defp add_system_alerts(alerts, {:ok, reports}) do
    system_alerts = reports
    |> Enum.filter(fn report -> report.error_count > 50 end)
    |> Enum.map(fn report ->
      %{
        type: :system_error,
        level: :high,
        message: "系统错误数量过高: #{report.error_count}",
        timestamp: report.report_date
      }
    end)

    alerts ++ system_alerts
  end
  defp add_system_alerts(alerts, _), do: alerts

  defp add_online_alerts(alerts, {:ok, online_stats}) do
    online_alerts = online_stats
    |> Enum.filter(fn stat -> stat.average_online_count < 50 end)
    |> Enum.map(fn stat ->
      %{
        type: :low_online,
        level: :medium,
        message: "在线用户数过低: #{stat.average_online_count}",
        timestamp: stat.stat_date
      }
    end)

    alerts ++ online_alerts
  end
  defp add_online_alerts(alerts, _), do: alerts

  defp add_robot_alerts(alerts, {:ok, _robot_stats}) do
    # 机器人相关告警逻辑
    alerts
  end
  defp add_robot_alerts(alerts, _) do
    alerts ++ [%{
      type: :robot_data_missing,
      level: :low,
      message: "机器人统计数据缺失",
      timestamp: DateTime.utc_now()
    }]
  end

  defp add_economy_alerts(alerts, {:ok, _coin_stats}) do
    # 经济系统相关告警逻辑
    alerts
  end
  defp add_economy_alerts(alerts, _) do
    alerts ++ [%{
      type: :economy_data_missing,
      level: :low,
      message: "经济统计数据缺失",
      timestamp: DateTime.utc_now()
    }]
  end

  # 渠道分析辅助函数
  defp calculate_average_ctr(channel_stats) do
    total_impressions = channel_stats |> Enum.map(& &1.impression_count) |> Enum.sum()
    total_clicks = channel_stats |> Enum.map(& &1.click_count) |> Enum.sum()
    if total_impressions > 0, do: total_clicks / total_impressions, else: 0
  end

  defp calculate_average_cvr(channel_stats) do
    total_clicks = channel_stats |> Enum.map(& &1.click_count) |> Enum.sum()
    total_conversions = channel_stats |> Enum.map(& &1.conversion_count) |> Enum.sum()
    if total_clicks > 0, do: total_conversions / total_clicks, else: 0
  end

  defp calculate_channel_roi(channel_stats) do
    total_cost = channel_stats |> Enum.map(& &1.total_cost) |> Enum.sum()
    total_revenue = channel_stats |> Enum.map(& &1.total_revenue) |> Enum.sum()
    if total_cost > 0, do: (total_revenue - total_cost) / total_cost, else: 0
  end

  defp calculate_overall_ctr(stats_list) do
    total_impressions = stats_list |> Enum.map(& &1.impression_count) |> Enum.sum()
    total_clicks = stats_list |> Enum.map(& &1.click_count) |> Enum.sum()
    if total_impressions > 0, do: total_clicks / total_impressions, else: 0
  end

  defp calculate_overall_cvr(stats_list) do
    total_clicks = stats_list |> Enum.map(& &1.click_count) |> Enum.sum()
    total_conversions = stats_list |> Enum.map(& &1.conversion_count) |> Enum.sum()
    if total_clicks > 0, do: total_conversions / total_clicks, else: 0
  end

  defp get_best_performing_channel(stats_list) do
    stats_list
    |> Enum.group_by(& &1.channel_name)
    |> Enum.map(fn {channel, stats} ->
      roi = calculate_channel_roi(stats)
      {channel, roi}
    end)
    |> Enum.max_by(&elem(&1, 1), fn -> {"无", 0} end)
    |> elem(0)
  end

  defp get_worst_performing_channel(stats_list) do
    stats_list
    |> Enum.group_by(& &1.channel_name)
    |> Enum.map(fn {channel, stats} ->
      roi = calculate_channel_roi(stats)
      {channel, roi}
    end)
    |> Enum.min_by(&elem(&1, 1), fn -> {"无", 0} end)
    |> elem(0)
  end
end
