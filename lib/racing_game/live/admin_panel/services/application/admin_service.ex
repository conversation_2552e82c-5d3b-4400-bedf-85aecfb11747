defmodule RacingGame.Live.AdminPanel.Services.Application.AdminService do
  @moduledoc """
  管理员应用服务
  
  提供管理员相关的应用层逻辑，协调多个领域服务：
  - 管理员操作编排
  - 跨领域业务流程
  - 事务管理
  - 权限验证
  """

  require Logger
  alias RacingGame.Live.AdminPanel.Services.Domain.{UserService, CommunicationService, DataService}
  alias RacingGame.Live.AdminPanel.Services.Application.{PermissionService, NotificationService}

  # ============================================================================
  # 用户管理编排
  # ============================================================================

  @doc """
  管理员创建用户（包含通知）

  ## 参数
  - `admin_user` - 管理员用户
  - `user_params` - 用户参数
  - `options` - 选项
    - `:send_notification` - 是否发送通知（默认true）

  ## 返回
  - `{:ok, user}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_user_with_notification(admin_user, user_params, options \\ []) do
    Logger.info("👤 [管理服务] 管理员创建用户: #{user_params["username"]}")
    
    send_notification = Keyword.get(options, :send_notification, true)
    
    with {:ok, user} <- UserService.create_user(admin_user, user_params),
         :ok <- maybe_send_user_creation_notification(user, admin_user, send_notification) do
      
      Logger.info("✅ [管理服务] 用户创建完成，包含通知")
      {:ok, user}
    else
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  批量用户操作

  ## 参数
  - `admin_user` - 管理员用户
  - `user_ids` - 用户ID列表
  - `operation` - 操作类型 (:activate, :deactivate, :delete)
  - `options` - 选项

  ## 返回
  - `{:ok, results}` - 成功
  - `{:error, reason}` - 失败
  """
  def batch_user_operation(admin_user, user_ids, operation, options \\ []) do
    Logger.info("👥 [管理服务] 批量用户操作: #{operation}, 用户数: #{length(user_ids)}")
    
    try do
      results = Enum.map(user_ids, fn user_id ->
        case perform_user_operation(admin_user, user_id, operation, options) do
          {:ok, result} -> {:ok, user_id, result}
          {:error, reason} -> {:error, user_id, reason}
        end
      end)
      
      {success_count, error_count} = count_batch_results(results)
      
      Logger.info("✅ [管理服务] 批量操作完成: 成功#{success_count}, 失败#{error_count}")
      {:ok, %{results: results, success_count: success_count, error_count: error_count}}
    rescue
      error ->
        Logger.error("❌ [管理服务] 批量操作异常: #{inspect(error)}")
        {:error, "批量操作失败"}
    end
  end

  # ============================================================================
  # 通信管理编排
  # ============================================================================

  @doc """
  发布系统通知（包含推送）

  ## 参数
  - `admin_user` - 管理员用户
  - `communication_params` - 通信参数
  - `options` - 选项
    - `:immediate_push` - 是否立即推送（默认true）

  ## 返回
  - `{:ok, communication}` - 成功
  - `{:error, reason}` - 失败
  """
  def publish_system_notification(admin_user, communication_params, options \\ []) do
    Logger.info("📢 [管理服务] 发布系统通知: #{communication_params["title"]}")
    
    immediate_push = Keyword.get(options, :immediate_push, true)
    
    with {:ok, communication} <- CommunicationService.create_communication(admin_user, communication_params),
         :ok <- maybe_push_notification(communication, immediate_push) do
      
      Logger.info("✅ [管理服务] 系统通知发布完成")
      {:ok, communication}
    else
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  批量通信操作

  ## 参数
  - `admin_user` - 管理员用户
  - `communication_ids` - 通信ID列表
  - `operation` - 操作类型 (:activate, :deactivate, :delete)

  ## 返回
  - `{:ok, results}` - 成功
  - `{:error, reason}` - 失败
  """
  def batch_communication_operation(admin_user, communication_ids, operation) do
    Logger.info("📨 [管理服务] 批量通信操作: #{operation}, 通信数: #{length(communication_ids)}")
    
    try do
      results = Enum.map(communication_ids, fn comm_id ->
        case perform_communication_operation(admin_user, comm_id, operation) do
          {:ok, result} -> {:ok, comm_id, result}
          {:error, reason} -> {:error, comm_id, reason}
        end
      end)
      
      {success_count, error_count} = count_batch_results(results)
      
      Logger.info("✅ [管理服务] 批量通信操作完成: 成功#{success_count}, 失败#{error_count}")
      {:ok, %{results: results, success_count: success_count, error_count: error_count}}
    rescue
      error ->
        Logger.error("❌ [管理服务] 批量通信操作异常: #{inspect(error)}")
        {:error, "批量通信操作失败"}
    end
  end

  # ============================================================================
  # 数据管理编排
  # ============================================================================

  @doc """
  生成综合数据报表

  ## 参数
  - `admin_user` - 管理员用户
  - `report_params` - 报表参数
    - `:type` - 报表类型 (:daily, :weekly, :monthly)
    - `:date_range` - 日期范围

  ## 返回
  - `{:ok, report}` - 成功
  - `{:error, reason}` - 失败
  """
  def generate_comprehensive_report(admin_user, report_params) do
    Logger.info("📊 [管理服务] 生成综合数据报表")
    
    try do
      with {:ok, bet_stats} <- DataService.get_bet_statistics(admin_user, report_params),
           {:ok, stock_stats} <- DataService.get_stock_statistics(admin_user, report_params),
           {:ok, user_stats} <- get_user_statistics(admin_user, report_params),
           {:ok, report} <- compile_comprehensive_report(bet_stats, stock_stats, user_stats, report_params) do
        
        Logger.info("✅ [管理服务] 综合报表生成完成")
        {:ok, report}
      else
        {:error, reason} -> {:error, reason}
      end
    rescue
      error ->
        Logger.error("❌ [管理服务] 生成报表异常: #{inspect(error)}")
        {:error, "生成报表失败"}
    end
  end

  @doc """
  数据清理任务

  ## 参数
  - `admin_user` - 管理员用户
  - `cleanup_params` - 清理参数
    - `:type` - 清理类型
    - `:before_date` - 清理日期前的数据

  ## 返回
  - `{:ok, cleanup_result}` - 成功
  - `{:error, reason}` - 失败
  """
  def perform_data_cleanup(admin_user, cleanup_params) do
    Logger.info("🧹 [管理服务] 执行数据清理任务")
    
    with :ok <- validate_cleanup_permission(admin_user),
         {:ok, cleanup_plan} <- create_cleanup_plan(cleanup_params),
         {:ok, cleanup_result} <- execute_cleanup_plan(cleanup_plan) do
      
      Logger.info("✅ [管理服务] 数据清理完成")
      {:ok, cleanup_result}
    else
      {:error, reason} -> {:error, reason}
    end
  end

  # ============================================================================
  # 系统监控和健康检查
  # ============================================================================

  @doc """
  系统健康检查

  ## 参数
  - `admin_user` - 管理员用户

  ## 返回
  - `{:ok, health_status}` - 成功
  - `{:error, reason}` - 失败
  """
  def system_health_check(admin_user) do
    Logger.info("🏥 [管理服务] 执行系统健康检查")
    
    try do
      health_checks = [
        check_database_health(),
        check_cache_health(),
        check_external_services_health(),
        check_system_resources()
      ]
      
      overall_status = determine_overall_health(health_checks)
      
      health_status = %{
        overall: overall_status,
        checks: health_checks,
        timestamp: DateTime.utc_now()
      }
      
      Logger.info("✅ [管理服务] 系统健康检查完成: #{overall_status}")
      {:ok, health_status}
    rescue
      error ->
        Logger.error("❌ [管理服务] 健康检查异常: #{inspect(error)}")
        {:error, "健康检查失败"}
    end
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 可能发送用户创建通知
  defp maybe_send_user_creation_notification(_user, _admin_user, false), do: :ok
  defp maybe_send_user_creation_notification(user, admin_user, true) do
    NotificationService.send_user_creation_notification(user, admin_user)
  end

  # 执行用户操作
  defp perform_user_operation(admin_user, user_id, operation, options) do
    case operation do
      :activate -> activate_user(admin_user, user_id, options)
      :deactivate -> deactivate_user(admin_user, user_id, options)
      :delete -> delete_user(admin_user, user_id, options)
      _ -> {:error, "不支持的操作"}
    end
  end

  # 激活用户
  defp activate_user(admin_user, user_id, _options) do
    UserService.update_user(admin_user, user_id, %{"active" => true})
  end

  # 停用用户
  defp deactivate_user(admin_user, user_id, _options) do
    UserService.update_user(admin_user, user_id, %{"active" => false})
  end

  # 删除用户
  defp delete_user(admin_user, user_id, _options) do
    # TODO: 实现用户删除逻辑
    {:error, "用户删除功能暂未实现"}
  end

  # 统计批量操作结果
  defp count_batch_results(results) do
    success_count = Enum.count(results, fn {status, _, _} -> status == :ok end)
    error_count = length(results) - success_count
    {success_count, error_count}
  end

  # 可能推送通知
  defp maybe_push_notification(_communication, false), do: :ok
  defp maybe_push_notification(communication, true) do
    NotificationService.push_communication(communication)
  end

  # 执行通信操作
  defp perform_communication_operation(admin_user, comm_id, operation) do
    case operation do
      :activate -> CommunicationService.toggle_communication_status(admin_user, comm_id)
      :deactivate -> CommunicationService.toggle_communication_status(admin_user, comm_id)
      :delete -> CommunicationService.delete_communication(admin_user, comm_id)
      _ -> {:error, "不支持的操作"}
    end
  end

  # 获取用户统计
  defp get_user_statistics(admin_user, params) do
    # TODO: 实现用户统计获取
    {:ok, %{}}
  end

  # 编译综合报表
  defp compile_comprehensive_report(bet_stats, stock_stats, user_stats, params) do
    # TODO: 实现综合报表编译
    report = %{
      bet_statistics: bet_stats,
      stock_statistics: stock_stats,
      user_statistics: user_stats,
      generated_at: DateTime.utc_now(),
      parameters: params
    }
    {:ok, report}
  end

  # 验证清理权限
  defp validate_cleanup_permission(admin_user) do
    if admin_user.permission_level >= 2 do
      :ok
    else
      {:error, "权限不足，无法执行数据清理"}
    end
  end

  # 创建清理计划
  defp create_cleanup_plan(params) do
    # TODO: 实现清理计划创建
    {:ok, %{}}
  end

  # 执行清理计划
  defp execute_cleanup_plan(plan) do
    # TODO: 实现清理计划执行
    {:ok, %{}}
  end

  # 检查数据库健康
  defp check_database_health do
    # TODO: 实现数据库健康检查
    %{component: "database", status: :healthy, details: "连接正常"}
  end

  # 检查缓存健康
  defp check_cache_health do
    # TODO: 实现缓存健康检查
    %{component: "cache", status: :healthy, details: "缓存正常"}
  end

  # 检查外部服务健康
  defp check_external_services_health do
    # TODO: 实现外部服务健康检查
    %{component: "external_services", status: :healthy, details: "外部服务正常"}
  end

  # 检查系统资源
  defp check_system_resources do
    # TODO: 实现系统资源检查
    %{component: "system_resources", status: :healthy, details: "系统资源正常"}
  end

  # 确定整体健康状态
  defp determine_overall_health(checks) do
    if Enum.all?(checks, fn check -> check.status == :healthy end) do
      :healthy
    else
      :unhealthy
    end
  end
end
