defmodule RacingGame.Live.AdminPanel.Repositories.Teen.PaymentSystem.PaymentConfigRepository do
  @moduledoc """
  ⚙️ 支付配置数据访问层

  负责支付配置相关的数据库操作，包括：
  - 支付配置的CRUD操作
  - 配置状态管理
  - 网关配置查询
  - 支付类型配置
  - 费率和限额管理
  - 配置统计分析
  """

  require Logger
  alias Teen.PaymentSystem.PaymentConfig

  # 缓存配置
  @cache_ttl 300  # 5分钟缓存

  # ==================== 基础CRUD操作 ====================

  @doc """
  创建支付配置

  ## 参数
  - `config_data` - 配置数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, config}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_payment_config(config_data, options \\ []) do
    Logger.info("⚙️ [支付配置仓储] 创建支付配置: #{inspect(config_data[:payment_type_name])}")

    try do
      result = PaymentConfig
      |> Ash.Changeset.for_create(:create, config_data)
      |> Ash.create()

      case result do
        {:ok, config} ->
          Logger.info("✅ [支付配置仓储] 支付配置创建成功: #{config.payment_type_name}")
          clear_cache()
          {:ok, config}
        
        {:error, error} ->
          Logger.error("❌ [支付配置仓储] 支付配置创建失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [支付配置仓储] 创建支付配置异常: #{inspect(exception)}")
        {:error, :create_config_exception}
    end
  end

  @doc """
  获取支付配置

  ## 参数
  - `config_id` - 配置ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, config}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_payment_config(config_id, options \\ []) do
    Logger.info("⚙️ [支付配置仓储] 获取支付配置: #{config_id}")

    try do
      result = PaymentConfig
      |> Ash.Query.for_read(:read)
      |> Ash.Query.filter(id == ^config_id)
      |> maybe_load_associations(options)
      |> Ash.read_one()

      case result do
        {:ok, config} when not is_nil(config) ->
          Logger.info("✅ [支付配置仓储] 支付配置获取成功: #{config.payment_type_name}")
          {:ok, config}
        
        {:ok, nil} ->
          Logger.warn("⚠️ [支付配置仓储] 支付配置不存在: #{config_id}")
          {:ok, nil}
        
        {:error, error} ->
          Logger.error("❌ [支付配置仓储] 支付配置获取失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [支付配置仓储] 获取支付配置异常: #{inspect(exception)}")
        {:error, :get_config_exception}
    end
  end

  @doc """
  更新支付配置

  ## 参数
  - `config_id` - 配置ID
  - `update_data` - 更新数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, config}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_payment_config(config_id, update_data, options \\ []) do
    Logger.info("⚙️ [支付配置仓储] 更新支付配置: #{config_id}")

    try do
      result = PaymentConfig
      |> Ash.get!(config_id)
      |> Ash.Changeset.for_update(:update, update_data)
      |> Ash.update()

      case result do
        {:ok, config} ->
          Logger.info("✅ [支付配置仓储] 支付配置更新成功: #{config.payment_type_name}")
          clear_cache()
          {:ok, config}
        
        {:error, error} ->
          Logger.error("❌ [支付配置仓储] 支付配置更新失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [支付配置仓储] 更新支付配置异常: #{inspect(exception)}")
        {:error, :update_config_exception}
    end
  end

  @doc """
  删除支付配置

  ## 参数
  - `config_id` - 配置ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, config}` - 成功
  - `{:error, reason}` - 失败
  """
  def delete_payment_config(config_id, options \\ []) do
    Logger.info("⚙️ [支付配置仓储] 删除支付配置: #{config_id}")

    try do
      result = PaymentConfig
      |> Ash.get!(config_id)
      |> Ash.destroy()

      case result do
        :ok ->
          Logger.info("✅ [支付配置仓储] 支付配置删除成功: #{config_id}")
          clear_cache()
          {:ok, :deleted}
        
        {:error, error} ->
          Logger.error("❌ [支付配置仓储] 支付配置删除失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [支付配置仓储] 删除支付配置异常: #{inspect(exception)}")
        {:error, :delete_config_exception}
    end
  end

  # ==================== 查询操作 ====================

  @doc """
  列出支付配置

  ## 参数
  - `params` - 查询参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, {configs, total_count}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_payment_configs(params \\ %{}, options \\ []) do
    Logger.info("⚙️ [支付配置仓储] 列出支付配置: #{inspect(params)}")

    try do
      query = PaymentConfig
      |> Ash.Query.for_read(:read)
      |> apply_filters(params)
      |> apply_sorting(params)
      |> maybe_load_associations(options)

      # 获取总数
      total_count_result = query |> Ash.count()
      
      # 应用分页
      paginated_query = apply_pagination(query, params)
      
      # 执行查询
      configs_result = paginated_query |> Ash.read()

      case {configs_result, total_count_result} do
        {{:ok, configs}, {:ok, total_count}} ->
          Logger.info("✅ [支付配置仓储] 支付配置列表获取成功: #{length(configs)}/#{total_count}")
          {:ok, {configs, total_count}}
        
        {{:error, error}, _} ->
          Logger.error("❌ [支付配置仓储] 支付配置列表获取失败: #{inspect(error)}")
          {:error, error}
        
        {_, {:error, error}} ->
          Logger.error("❌ [支付配置仓储] 支付配置计数失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [支付配置仓储] 列出支付配置异常: #{inspect(exception)}")
        {:error, :list_configs_exception}
    end
  end

  @doc """
  获取活跃支付配置

  ## 参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, configs}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_active_payment_configs(options \\ []) do
    Logger.info("⚙️ [支付配置仓储] 获取活跃支付配置")

    try do
      result = PaymentConfig
      |> Ash.Query.for_read(:list_active_configs)
      |> maybe_load_associations(options)
      |> Ash.read()

      case result do
        {:ok, configs} ->
          Logger.info("✅ [支付配置仓储] 活跃支付配置获取成功: #{length(configs)}个")
          {:ok, configs}
        
        {:error, error} ->
          Logger.error("❌ [支付配置仓储] 活跃支付配置获取失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [支付配置仓储] 获取活跃支付配置异常: #{inspect(exception)}")
        {:error, :list_active_configs_exception}
    end
  end

  @doc """
  根据网关获取支付配置

  ## 参数
  - `gateway_id` - 网关ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, configs}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_payment_configs_by_gateway(gateway_id, options \\ []) do
    Logger.info("⚙️ [支付配置仓储] 根据网关获取支付配置: #{gateway_id}")

    try do
      result = PaymentConfig
      |> Ash.Query.for_read(:list_by_gateway, %{gateway_id: gateway_id})
      |> maybe_load_associations(options)
      |> Ash.read()

      case result do
        {:ok, configs} ->
          Logger.info("✅ [支付配置仓储] 网关配置获取成功: #{length(configs)}个")
          {:ok, configs}
        
        {:error, error} ->
          Logger.error("❌ [支付配置仓储] 网关配置获取失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [支付配置仓储] 根据网关获取支付配置异常: #{inspect(exception)}")
        {:error, :list_configs_by_gateway_exception}
    end
  end

  @doc """
  根据支付类型获取配置

  ## 参数
  - `payment_type` - 支付类型
  - `options` - 选项参数

  ## 返回
  - `{:ok, configs}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_payment_configs_by_type(payment_type, options \\ []) do
    Logger.info("⚙️ [支付配置仓储] 根据支付类型获取配置: #{payment_type}")

    try do
      result = PaymentConfig
      |> Ash.Query.for_read(:list_by_payment_type, %{payment_type: payment_type})
      |> maybe_load_associations(options)
      |> Ash.read()

      case result do
        {:ok, configs} ->
          Logger.info("✅ [支付配置仓储] 类型配置获取成功: #{length(configs)}个")
          {:ok, configs}
        
        {:error, error} ->
          Logger.error("❌ [支付配置仓储] 类型配置获取失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [支付配置仓储] 根据支付类型获取配置异常: #{inspect(exception)}")
        {:error, :list_configs_by_type_exception}
    end
  end

  # ==================== 状态管理操作 ====================

  @doc """
  激活支付配置

  ## 参数
  - `config_id` - 配置ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, config}` - 成功
  - `{:error, reason}` - 失败
  """
  def activate_payment_config(config_id, options \\ []) do
    Logger.info("⚙️ [支付配置仓储] 激活支付配置: #{config_id}")

    try do
      result = PaymentConfig
      |> Ash.get!(config_id)
      |> Ash.Changeset.for_update(:enable)
      |> Ash.update()

      case result do
        {:ok, config} ->
          Logger.info("✅ [支付配置仓储] 支付配置激活成功: #{config.payment_type_name}")
          clear_cache()
          {:ok, config}
        
        {:error, error} ->
          Logger.error("❌ [支付配置仓储] 支付配置激活失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [支付配置仓储] 激活支付配置异常: #{inspect(exception)}")
        {:error, :activate_config_exception}
    end
  end

  @doc """
  禁用支付配置

  ## 参数
  - `config_id` - 配置ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, config}` - 成功
  - `{:error, reason}` - 失败
  """
  def deactivate_payment_config(config_id, options \\ []) do
    Logger.info("⚙️ [支付配置仓储] 禁用支付配置: #{config_id}")

    try do
      result = PaymentConfig
      |> Ash.get!(config_id)
      |> Ash.Changeset.for_update(:disable)
      |> Ash.update()

      case result do
        {:ok, config} ->
          Logger.info("✅ [支付配置仓储] 支付配置禁用成功: #{config.payment_type_name}")
          clear_cache()
          {:ok, config}
        
        {:error, error} ->
          Logger.error("❌ [支付配置仓储] 支付配置禁用失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [支付配置仓储] 禁用支付配置异常: #{inspect(exception)}")
        {:error, :deactivate_config_exception}
    end
  end

  # ==================== 私有辅助函数 ====================

  # 应用过滤条件
  defp apply_filters(query, params) do
    Enum.reduce(params, query, fn
      {"gateway_id", gateway_id}, query when is_binary(gateway_id) and gateway_id != "" ->
        Ash.Query.filter(query, gateway_id == ^gateway_id)
      
      {"gateway_name", gateway_name}, query when is_binary(gateway_name) and gateway_name != "" ->
        Ash.Query.filter(query, contains(gateway_name, ^gateway_name))
      
      {"payment_type", payment_type}, query when is_binary(payment_type) and payment_type != "" ->
        Ash.Query.filter(query, payment_type == ^payment_type)
      
      {"payment_type_name", payment_type_name}, query when is_binary(payment_type_name) and payment_type_name != "" ->
        Ash.Query.filter(query, contains(payment_type_name, ^payment_type_name))
      
      {"status", status}, query when is_integer(status) ->
        Ash.Query.filter(query, status == ^status)
      
      {"min_amount_gte", min_amount}, query when is_binary(min_amount) ->
        case Decimal.parse(min_amount) do
          {amount, ""} -> Ash.Query.filter(query, min_amount >= ^amount)
          _ -> query
        end
      
      {"max_amount_lte", max_amount}, query when is_binary(max_amount) ->
        case Decimal.parse(max_amount) do
          {amount, ""} -> Ash.Query.filter(query, max_amount <= ^amount)
          _ -> query
        end
      
      _other, query ->
        query
    end)
  end

  # 应用排序
  defp apply_sorting(query, params) do
    case params do
      %{"sort_by" => sort_by, "sort_order" => sort_order} ->
        sort_field = String.to_existing_atom(sort_by)
        case sort_order do
          "desc" -> Ash.Query.sort(query, desc: sort_field)
          _ -> Ash.Query.sort(query, asc: sort_field)
        end
      
      %{"sort_by" => sort_by} ->
        sort_field = String.to_existing_atom(sort_by)
        Ash.Query.sort(query, asc: sort_field)
      
      _ ->
        Ash.Query.sort(query, asc: :sort_order, desc: :inserted_at)
    end
  end

  # 应用分页
  defp apply_pagination(query, params) do
    page = Map.get(params, "page", 1) |> ensure_integer()
    page_size = Map.get(params, "page_size", 20) |> ensure_integer()
    offset = (page - 1) * page_size

    query
    |> Ash.Query.limit(page_size)
    |> Ash.Query.offset(offset)
  end

  # 加载关联数据
  defp maybe_load_associations(query, options) do
    case Keyword.get(options, :load, []) do
      [] -> query
      associations -> Ash.Query.load(query, associations)
    end
  end

  # 确保整数类型
  defp ensure_integer(value) when is_integer(value), do: value
  defp ensure_integer(value) when is_binary(value) do
    case Integer.parse(value) do
      {int, ""} -> int
      _ -> 1
    end
  end
  defp ensure_integer(_), do: 1

  # 清除缓存
  defp clear_cache do
    # 这里可以实现缓存清除逻辑
    :ok
  end
end
