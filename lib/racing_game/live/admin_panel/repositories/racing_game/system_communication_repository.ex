defmodule RacingGame.Live.AdminPanel.Repositories.RacingGame.SystemCommunicationRepository do
  @moduledoc """
  系统通信数据访问仓储
  
  提供系统通信相关的数据访问抽象：
  - 系统消息管理
  - 通信记录查询
  - 阅读状态管理
  - 消息统计分析
  """

  require Logger
  alias Ash.Query
  import Ash.Query
  alias RacingGame.{SystemCommunication, SystemCommunicationRead}
  require Ash.Query

  # 常量定义
  @default_limit 20
  @max_limit 100
  @cache_ttl 300  # 5分钟缓存

  # ============================================================================
  # 系统消息管理
  # ============================================================================

  @doc """
  根据ID获取系统消息

  ## 参数
  - `communication_id` - 消息ID
  - `options` - 选项

  ## 返回
  - `{:ok, communication}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_communication_by_id(communication_id, options \\ []) do
    Logger.debug("📨 [系统通信仓储] 获取系统消息: #{communication_id}")
    
    cache_key = build_cache_key("communication", communication_id, options[:preload])
    
    case get_from_cache(cache_key) do
      :miss ->
        fetch_and_cache_communication_by_id(communication_id, options[:preload] || [], cache_key)
      {:hit, communication} ->
        Logger.debug("💾 [系统通信仓储] 缓存命中: #{communication_id}")
        {:ok, communication}
    end
  end

  @doc """
  创建系统消息

  ## 参数
  - `communication_data` - 消息数据

  ## 返回
  - `{:ok, communication}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_communication(communication_data) do
    Logger.info("📝 [系统通信仓储] 创建系统消息: #{communication_data[:title]}")
    
    try do
      case SystemCommunication.create(communication_data) do
        {:ok, communication} ->
          clear_cache(:communication, nil)
          Logger.info("✅ [系统通信仓储] 消息创建成功: #{communication.id}")
          {:ok, communication}
        {:error, error} ->
          Logger.error("❌ [系统通信仓储] 创建消息失败: #{inspect(error)}")
          {:error, :create_failed}
      end
    rescue
      error ->
        Logger.error("❌ [系统通信仓储] 创建消息异常: #{inspect(error)}")
        {:error, :create_error}
    end
  end

  @doc """
  更新系统消息

  ## 参数
  - `communication_id` - 消息ID
  - `update_data` - 更新数据

  ## 返回
  - `{:ok, communication}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_communication(communication_id, update_data) do
    Logger.info("📝 [系统通信仓储] 更新系统消息: #{communication_id}")
    
    with {:ok, communication} <- get_communication_by_id(communication_id),
         {:ok, updated_communication} <- do_update_communication(communication, update_data) do
      
      clear_cache(:communication, communication_id)
      Logger.info("✅ [系统通信仓储] 消息更新成功: #{communication_id}")
      {:ok, updated_communication}
    else
      error -> error
    end
  end

  @doc """
  删除系统消息

  ## 参数
  - `communication_id` - 消息ID

  ## 返回
  - `{:ok, communication}` - 成功
  - `{:error, reason}` - 失败
  """
  def delete_communication(communication_id) do
    Logger.info("🗑️ [系统通信仓储] 删除系统消息: #{communication_id}")
    
    with {:ok, communication} <- get_communication_by_id(communication_id),
         {:ok, deleted_communication} <- do_delete_communication(communication) do
      
      clear_cache(:communication, communication_id)
      Logger.info("✅ [系统通信仓储] 消息删除成功: #{communication_id}")
      {:ok, deleted_communication}
    else
      error -> error
    end
  end

  # ============================================================================
  # 消息查询
  # ============================================================================

  @doc """
  分页获取系统消息列表

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, %{items: communications, page_info: page_info}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_communications_paginated(params \\ %{}) do
    Logger.debug("📋 [系统通信仓储] 分页获取系统消息列表")
    
    normalized_params = normalize_list_params(params)
    cache_key = build_cache_key("communications_list", :paginated, normalized_params)
    
    case get_from_cache(cache_key) do
      :miss ->
        fetch_and_cache_communications_list(normalized_params, cache_key)
      {:hit, result} ->
        Logger.debug("💾 [系统通信仓储] 缓存命中: 消息列表")
        {:ok, result}
    end
  end

  @doc """
  获取活跃系统消息

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, communications}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_active_communications(options \\ []) do
    Logger.debug("🔥 [系统通信仓储] 获取活跃系统消息")
    
    cache_key = build_cache_key("active_communications", :list, options)
    
    case get_from_cache(cache_key) do
      :miss ->
        fetch_and_cache_active_communications(options, cache_key)
      {:hit, communications} ->
        Logger.debug("💾 [系统通信仓储] 缓存命中: 活跃消息")
        {:ok, communications}
    end
  end

  @doc """
  获取用户未读消息

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - `{:ok, communications}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_unread_communications(user_id, options \\ []) do
    Logger.debug("👤 [系统通信仓储] 获取用户未读消息: #{user_id}")
    
    try do
      # 获取用户已读的消息ID列表
      read_communication_ids = case get_user_read_communication_ids(user_id) do
        {:ok, ids} -> ids
        _ -> []
      end
      
      # 查询未读消息
      query = SystemCommunication
      |> Ash.Query.filter(is_active == true)
      |> Ash.Query.filter(not (id in read_communication_ids))
      |> maybe_limit(options[:limit])
      |> apply_sort(options[:sort] || [inserted_at: :desc])
      
      case Ash.read(query) do
        {:ok, communications} -> {:ok, communications}
        {:error, error} ->
          Logger.error("❌ [系统通信仓储] 获取未读消息失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [系统通信仓储] 获取未读消息异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # ============================================================================
  # 阅读状态管理
  # ============================================================================

  @doc """
  标记消息为已读

  ## 参数
  - `user_id` - 用户ID
  - `communication_id` - 消息ID

  ## 返回
  - `{:ok, read_record}` - 成功
  - `{:error, reason}` - 失败
  """
  def mark_as_read(user_id, communication_id) do
    Logger.info("👁️ [系统通信仓储] 标记消息已读: 用户#{user_id} 消息#{communication_id}")
    
    read_data = %{
      user_id: user_id,
      system_communication_id: communication_id,
      read_at: DateTime.utc_now()
    }
    
    try do
      case SystemCommunicationRead.create(read_data) do
        {:ok, read_record} ->
          clear_cache(:user_unread, user_id)
          Logger.info("✅ [系统通信仓储] 标记已读成功")
          {:ok, read_record}
        {:error, error} ->
          Logger.error("❌ [系统通信仓储] 标记已读失败: #{inspect(error)}")
          {:error, :mark_read_failed}
      end
    rescue
      error ->
        Logger.error("❌ [系统通信仓储] 标记已读异常: #{inspect(error)}")
        {:error, :mark_read_error}
    end
  end

  @doc """
  批量标记消息为已读

  ## 参数
  - `user_id` - 用户ID
  - `communication_ids` - 消息ID列表

  ## 返回
  - `{:ok, read_records}` - 成功
  - `{:error, reason}` - 失败
  """
  def mark_multiple_as_read(user_id, communication_ids) do
    Logger.info("👁️ [系统通信仓储] 批量标记消息已读: 用户#{user_id} #{length(communication_ids)}条消息")
    
    try do
      read_records = Enum.map(communication_ids, fn communication_id ->
        read_data = %{
          user_id: user_id,
          system_communication_id: communication_id,
          read_at: DateTime.utc_now()
        }
        SystemCommunicationRead.create(read_data)
      end)
      
      # 检查是否所有操作都成功
      case Enum.all?(read_records, fn {status, _} -> status == :ok end) do
        true ->
          clear_cache(:user_unread, user_id)
          successful_records = Enum.map(read_records, fn {:ok, record} -> record end)
          Logger.info("✅ [系统通信仓储] 批量标记已读成功")
          {:ok, successful_records}
        false ->
          Logger.error("❌ [系统通信仓储] 批量标记已读部分失败")
          {:error, :partial_mark_read_failed}
      end
    rescue
      error ->
        Logger.error("❌ [系统通信仓储] 批量标记已读异常: #{inspect(error)}")
        {:error, :mark_read_error}
    end
  end

  # ============================================================================
  # 统计查询
  # ============================================================================

  @doc """
  获取系统消息统计

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, statistics}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_communication_statistics(options \\ []) do
    Logger.debug("📊 [系统通信仓储] 获取系统消息统计")
    
    try do
      stats = %{
        total_communications: get_total_communications(),
        active_communications: get_active_communications_count(),
        recent_communications: get_recent_communications_count(options[:days] || 7),
        total_reads: get_total_reads()
      }
      
      {:ok, stats}
    rescue
      error ->
        Logger.error("❌ [系统通信仓储] 获取统计异常: #{inspect(error)}")
        {:error, :statistics_error}
    end
  end

  @doc """
  获取用户消息统计

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - `{:ok, statistics}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_communication_statistics(user_id, options \\ []) do
    Logger.debug("👤 [系统通信仓储] 获取用户消息统计: #{user_id}")
    
    try do
      stats = %{
        total_unread: get_user_unread_count(user_id),
        total_read: get_user_read_count(user_id),
        recent_reads: get_user_recent_reads_count(user_id, options[:days] || 7)
      }
      
      {:ok, stats}
    rescue
      error ->
        Logger.error("❌ [系统通信仓储] 获取用户统计异常: #{inspect(error)}")
        {:error, :statistics_error}
    end
  end

  # ============================================================================
  # 缓存管理
  # ============================================================================

  @doc """
  清除系统通信相关缓存

  ## 参数
  - `cache_type` - 缓存类型
  - `id` - ID（可选）
  """
  def clear_cache(cache_type, id \\ nil) do
    cache_patterns = case {cache_type, id} do
      {:communication, nil} -> ["communication:*"]
      {:communication, communication_id} -> ["communication:#{communication_id}:*"]
      {:user_unread, user_id} -> ["user_unread:#{user_id}:*"]
      {:all, _} -> ["communication:*", "user_unread:*", "communications_list:*"]
    end
    
    Logger.debug("🧹 [系统通信仓储] 清除缓存: #{inspect(cache_patterns)}")
    Enum.each(cache_patterns, &clear_cache_pattern/1)
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 标准化列表参数
  defp normalize_list_params(params) do
    %{
      page: max(1, params[:page] || 1),
      limit: min(params[:limit] || @default_limit, @max_limit),
      search: String.trim(params[:search] || ""),
      filters: params[:filters] || %{},
      sort: params[:sort] || [inserted_at: :desc],
      preload: params[:preload] || []
    }
  end

  # 获取并缓存系统消息
  defp fetch_and_cache_communication_by_id(communication_id, preload, cache_key) do
    case fetch_communication_by_id(communication_id, preload) do
      {:ok, communication} ->
        cache_put(cache_key, communication, @cache_ttl)
        {:ok, communication}
      error -> error
    end
  end

  # 获取系统消息
  defp fetch_communication_by_id(communication_id, preload) do
    try do
      case SystemCommunication
           |> maybe_preload(preload)
           |> Ash.get(communication_id) do
        {:ok, communication} -> {:ok, communication}
        {:error, %Ash.Error.Query.NotFound{}} -> {:error, :not_found}
        {:error, error} ->
          Logger.error("❌ [系统通信仓储] 获取消息失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [系统通信仓储] 获取消息异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # 执行更新系统消息
  defp do_update_communication(communication, update_data) do
    try do
      case communication
           |> Ash.Changeset.for_update(:update, update_data)
           |> Ash.update() do
        {:ok, updated_communication} -> {:ok, updated_communication}
        {:error, error} ->
          Logger.error("❌ [系统通信仓储] 更新消息失败: #{inspect(error)}")
          {:error, :update_failed}
      end
    rescue
      error ->
        Logger.error("❌ [系统通信仓储] 更新消息异常: #{inspect(error)}")
        {:error, :update_error}
    end
  end

  # 执行删除系统消息
  defp do_delete_communication(communication) do
    try do
      case Ash.destroy(communication) do
        :ok -> {:ok, communication}
        {:error, error} ->
          Logger.error("❌ [系统通信仓储] 删除消息失败: #{inspect(error)}")
          {:error, :delete_failed}
      end
    rescue
      error ->
        Logger.error("❌ [系统通信仓储] 删除消息异常: #{inspect(error)}")
        {:error, :delete_error}
    end
  end

  # 获取并缓存消息列表
  defp fetch_and_cache_communications_list(params, cache_key) do
    case fetch_communications_list(params) do
      {:ok, result} ->
        cache_put(cache_key, result, @cache_ttl)
        {:ok, result}
      error -> error
    end
  end

  # 获取消息列表
  defp fetch_communications_list(params) do
    try do
      query = SystemCommunication
      |> apply_communication_filters(params.filters)
      |> apply_communication_search(params.search)
      |> apply_sort(params.sort)
      
      execute_paginated_query(query, params)
    rescue
      error ->
        Logger.error("❌ [系统通信仓储] 获取消息列表异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # 获取并缓存活跃消息
  defp fetch_and_cache_active_communications(options, cache_key) do
    case fetch_active_communications(options) do
      {:ok, communications} ->
        cache_put(cache_key, communications, @cache_ttl)
        {:ok, communications}
      error -> error
    end
  end

  # 获取活跃消息
  defp fetch_active_communications(options) do
    try do
      query = SystemCommunication
      |> Ash.Query.filter(is_active == true)
      |> maybe_limit(options[:limit])
      |> apply_sort(options[:sort] || [inserted_at: :desc])
      
      case Ash.read(query) do
        {:ok, communications} -> {:ok, communications}
        {:error, error} ->
          Logger.error("❌ [系统通信仓储] 获取活跃消息失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [系统通信仓储] 获取活跃消息异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # 获取用户已读消息ID列表
  defp get_user_read_communication_ids(user_id) do
    try do
      case SystemCommunicationRead
           |> Ash.Query.filter(user_id  == user_id)
           |> Ash.Query.select([:system_communication_id])
           |> Ash.read() do
        {:ok, read_records} ->
          ids = Enum.map(read_records, & &1.system_communication_id)
          {:ok, ids}
        {:error, error} ->
          Logger.error("❌ [系统通信仓储] 获取已读消息ID失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [系统通信仓储] 获取已读消息ID异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # 应用消息过滤器
  defp apply_communication_filters(query, filters) when map_size(filters) == 0, do: query
  defp apply_communication_filters(query, filters) do
    Enum.reduce(filters, query, fn {key, value}, acc_query ->
      apply_communication_filter(acc_query, key, value)
    end)
  end

  # 应用单个消息过滤器
  defp apply_communication_filter(query, :is_active, is_active) when not is_nil(is_active) do
    Ash.Query.filter(query, is_active  == is_active)
  end
  defp apply_communication_filter(query, :communication_type, type) when not is_nil(type) do
    Ash.Query.filter(query, communication_type  == type)
  end
  defp apply_communication_filter(query, _key, _value), do: query

  # 应用消息搜索
  defp apply_communication_search(query, search) when search == "" or is_nil(search), do: query
  defp apply_communication_search(query, search) do
    search_term = "%#{search}%"
    Ash.Query.filter(query, ilike(title, search_term) or ilike(content, search_term))
  end

  # 执行分页查询
  defp execute_paginated_query(query, params) do
    offset = (params.page - 1) * params.limit
    
    case query
         |> maybe_preload(params.preload)
         |> Ash.Query.page(count: true, limit: params.limit, offset: offset)
         |> Ash.read() do
      {:ok, %Ash.Page.Offset{results: items, count: total_count}} ->
        page_info = %{
          total_count: total_count,
          page: params.page,
          limit: params.limit,
          total_pages: ceil(total_count / params.limit)
        }
        {:ok, %{items: items, page_info: page_info}}
      {:error, error} ->
        Logger.error("❌ [系统通信仓储] 分页查询失败: #{inspect(error)}")
        {:error, :query_failed}
    end
  end

  # 统计函数（简化实现）
  defp get_total_communications do
    case SystemCommunication |> Ash.Query.aggregate(:count, :id) |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_active_communications_count do
    case SystemCommunication 
         |> Ash.Query.filter(is_active == true)
         |> Ash.Query.aggregate(:count, :id) 
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_recent_communications_count(days) do
    date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
    
    case SystemCommunication 
         |> Ash.Query.filter(inserted_at >= date_threshold)
         |> Ash.Query.aggregate(:count, :id) 
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_total_reads do
    case SystemCommunicationRead |> Ash.Query.aggregate(:count, :id) |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_user_unread_count(user_id) do
    # 这里需要实现用户未读消息数量统计
    # 简化实现
    0
  end

  defp get_user_read_count(user_id) do
    case SystemCommunicationRead 
         |> Ash.Query.filter(user_id  == user_id)
         |> Ash.Query.aggregate(:count, :id) 
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_user_recent_reads_count(user_id, days) do
    date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
    
    case SystemCommunicationRead 
         |> Ash.Query.filter(user_id == user_id and read_at >= date_threshold)
         |> Ash.Query.aggregate(:count, :id) 
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  # 可能预加载关联数据
  defp maybe_preload(query, []), do: query
  defp maybe_preload(query, preload) when is_list(preload) do
    Ash.Query.load(query, preload)
  end

  # 可能限制数量
  defp maybe_limit(query, nil), do: query
  defp maybe_limit(query, limit) when is_integer(limit) and limit > 0 do
    Ash.Query.limit(query, limit)
  end
  defp maybe_limit(query, _), do: query

  # 应用排序
  defp apply_sort(query, sort) when is_list(sort) do
    Ash.Query.sort(query, sort)
  end
  defp apply_sort(query, _), do: query

  # 构建缓存键
  defp build_cache_key(prefix, id, extra \\ nil) do
    case extra do
      nil -> "#{prefix}:#{id}"
      extra -> "#{prefix}:#{id}:#{:erlang.phash2(extra)}"
    end
  end

  # 缓存操作（需要实现具体的缓存后端）
  defp get_from_cache(_key), do: :miss
  defp cache_put(_key, _value, _ttl), do: :ok
  defp clear_cache_pattern(_pattern), do: :ok
end
