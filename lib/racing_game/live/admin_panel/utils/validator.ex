defmodule RacingGame.Utils.Validator do
  @moduledoc """
  统一验证工具模块

  提供各种数据验证功能，包括空值检查、类型验证、长度验证、格式验证等。
  """

  @doc """
  统一验证函数 - 主入口

  ## 参数
  - `value` - 要验证的值
  - `validation_type` - 验证类型
  - `opts` - 验证选项

  ## 验证类型
  - `:blank` - 检查是否为空
  - `:present` - 检查是否有内容
  - `:empty` - 检查是否为空（更严格）
  - `:type` - 类型验证
  - `:length` - 长度验证
  - `:format` - 格式验证
  - `:range` - 范围验证
  - `:custom` - 自定义验证
  - `:all` - 组合验证

  ## 示例
      iex> Validator.validate("hello", :present)
      true

      iex> Validator.validate("<EMAIL>", :format, pattern: ~r/@/)
      {:ok, "<EMAIL>"}

      iex> Validator.validate("hello", :length, min: 3, max: 10)
      {:ok, "hello"}
  """
  def validate(value, validation_type, opts \\ [])

  # 空值检查
  def validate(value, :blank, _opts), do: blank?(value)
  def validate(value, :present, _opts), do: present?(value)
  def validate(value, :empty, _opts), do: empty?(value)

  # 类型验证
  def validate(value, :type, opts) do
    target_type = Keyword.get(opts, :type, :string)
    valid_type?(value, target_type)
  end

  # 长度验证
  def validate(value, :length, opts) do
    validate_length_with_options(value, opts)
  end

  # 格式验证
  def validate(value, :format, opts) do
    pattern = Keyword.get(opts, :pattern)
    validate_format_with_pattern(value, pattern)
  end

  # 范围验证
  def validate(value, :range, opts) do
    min_val = Keyword.get(opts, :min)
    max_val = Keyword.get(opts, :max)
    validate_range_with_bounds(value, min_val, max_val)
  end

  # 自定义验证
  def validate(value, :custom, opts) do
    validator_fn = Keyword.get(opts, :validator)
    if is_function(validator_fn, 1) do
      validator_fn.(value)
    else
      {:error, "无效的验证函数"}
    end
  end

  # 组合验证
  def validate(value, :all, opts) do
    validations = Keyword.get(opts, :validations, [])
    validate_all(value, validations)
  end

  @doc """
  检查值是否为空或仅包含空白字符

  ## 示例
      iex> Validator.blank?(nil)
      true

      iex> Validator.blank?("  ")
      true

      iex> Validator.blank?("hello")
      false
  """
  def blank?(nil), do: true
  def blank?(""), do: true
  def blank?(value) when is_binary(value), do: String.trim(value) == ""
  def blank?([]), do: true
  def blank?(%{} = map), do: Map.keys(map) == []
  def blank?(_), do: false

  @doc """
  检查值是否有内容

  ## 示例
      iex> Validator.present?("hello")
      true

      iex> Validator.present?(nil)
      false
  """
  def present?(value), do: not blank?(value)

  @doc """
  检查值是否为空（更严格的检查）

  ## 示例
      iex> Validator.empty?([])
      true

      iex> Validator.empty?(%{})
      true
  """
  def empty?(nil), do: true
  def empty?(""), do: true
  def empty?([]), do: true
  def empty?(%{} = map), do: Map.keys(map) == []
  def empty?(_), do: false

  @doc """
  检查值是否为指定类型

  ## 支持的类型
  - `:string` - 字符串
  - `:integer` - 整数
  - `:float` - 浮点数
  - `:number` - 数值（整数或浮点数）
  - `:boolean` - 布尔值
  - `:atom` - 原子
  - `:list` - 列表
  - `:map` - 映射
  - `:tuple` - 元组
  - `:function` - 函数
  - `:pid` - 进程ID
  - `:reference` - 引用
  - `:port` - 端口

  ## 示例
      iex> Validator.valid_type?("hello", :string)
      true

      iex> Validator.valid_type?(123, :integer)
      true
  """
  def valid_type?(value, :string), do: is_binary(value)
  def valid_type?(value, :integer), do: is_integer(value)
  def valid_type?(value, :float), do: is_float(value)
  def valid_type?(value, :number), do: is_number(value)
  def valid_type?(value, :boolean), do: is_boolean(value)
  def valid_type?(value, :atom), do: is_atom(value)
  def valid_type?(value, :list), do: is_list(value)
  def valid_type?(value, :map), do: is_map(value)
  def valid_type?(value, :tuple), do: is_tuple(value)
  def valid_type?(value, :function), do: is_function(value)
  def valid_type?(value, :pid), do: is_pid(value)
  def valid_type?(value, :reference), do: is_reference(value)
  def valid_type?(value, :port), do: is_port(value)
  def valid_type?(_value, _type), do: false

  # 私有函数 - 长度验证
  defp validate_length_with_options(value, opts) when is_binary(value) do
    length = String.length(value)
    validate_numeric_bounds(length, opts, "字符串长度")
  end

  defp validate_length_with_options(value, opts) when is_list(value) do
    length = length(value)
    validate_numeric_bounds(length, opts, "列表长度")
  end

  defp validate_length_with_options(value, opts) when is_map(value) do
    length = Map.size(value)
    validate_numeric_bounds(length, opts, "映射大小")
  end

  defp validate_length_with_options(_value, _opts) do
    {:error, "无法验证此类型的长度"}
  end

  # 私有函数 - 数值边界验证
  defp validate_numeric_bounds(value, opts, context) do
    min_val = Keyword.get(opts, :min)
    max_val = Keyword.get(opts, :max)
    exact_val = Keyword.get(opts, :exact)

    cond do
      exact_val && value != exact_val ->
        {:error, "#{context}必须等于 #{exact_val}，实际为 #{value}"}

      min_val && value < min_val ->
        {:error, "#{context}不能小于 #{min_val}，实际为 #{value}"}

      max_val && value > max_val ->
        {:error, "#{context}不能大于 #{max_val}，实际为 #{value}"}

      true ->
        {:ok, value}
    end
  end

  # 私有函数 - 格式验证
  defp validate_format_with_pattern(value, nil), do: {:error, "缺少验证模式"}
  defp validate_format_with_pattern(value, pattern) when is_binary(value) do
    if String.match?(value, pattern) do
      {:ok, value}
    else
      {:error, "格式不匹配"}
    end
  end
  defp validate_format_with_pattern(_value, _pattern), do: {:error, "只能验证字符串格式"}

  # 私有函数 - 范围验证
  defp validate_range_with_bounds(value, min_val, max_val) when is_number(value) do
    cond do
      min_val && value < min_val ->
        {:error, "值不能小于 #{min_val}"}

      max_val && value > max_val ->
        {:error, "值不能大于 #{max_val}"}

      true ->
        {:ok, value}
    end
  end
  defp validate_range_with_bounds(_value, _min_val, _max_val), do: {:error, "只能验证数值范围"}

  # 私有函数 - 组合验证
  defp validate_all(value, validations) do
    Enum.reduce_while(validations, {:ok, value}, fn {validation_type, opts}, {:ok, val} ->
      case validate(val, validation_type, opts) do
        {:ok, _} -> {:cont, {:ok, val}}
        {:error, _} = error -> {:halt, error}
        true -> {:cont, {:ok, val}}
        false -> {:halt, {:error, "验证失败: #{validation_type}"}}
      end
    end)
  end
end
