defmodule RacingGame.Live.AdminPanel.Repositories.Teen.GameManagement.RobotConfigRepository do
  @moduledoc """
  🤖 机器人配置仓储层

  负责机器人配置的数据访问操作，包括：
  - 机器人配置的CRUD操作
  - 机器人查询和过滤
  - 机器人状态管理
  - 机器人配置缓存
  - 机器人统计信息
  """

  require Logger
  import Ash.Query

  alias Teen.GameManagement.RobotConfig

  # 缓存配置
  @cache_ttl 300_000  # 5分钟缓存
  @cache_prefix "robot_config_repo"

  # ==================== 基础 CRUD 操作 ====================

  @doc """
  创建机器人配置

  ## 参数
  - `robot_data` - 机器人配置数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, robot_config}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_robot_config(robot_data, options \\ []) do
    Logger.info("🤖 [机器人配置仓储] 创建机器人配置: #{inspect(robot_data)}")

    try do
      case RobotConfig.create(robot_data) do
        {:ok, robot_config} ->
          Logger.info("✅ [机器人配置仓储] 机器人配置创建成功: #{robot_config.id}")
          clear_robot_config_cache()
          {:ok, robot_config}

        {:error, error} ->
          Logger.error("❌ [机器人配置仓储] 机器人配置创建失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [机器人配置仓储] 创建机器人配置异常: #{inspect(exception)}")
        {:error, :create_robot_config_exception}
    end
  end

  @doc """
  根据ID获取机器人配置

  ## 参数
  - `robot_config_id` - 机器人配置ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, robot_config}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_robot_config_by_id(robot_config_id, options \\ []) do
    Logger.info("🔍 [机器人配置仓储] 获取机器人配置: #{robot_config_id}")

    cache_key = "#{@cache_prefix}:robot_config:#{robot_config_id}"
    use_cache = Keyword.get(options, :use_cache, true)

    if use_cache do
      case get_from_cache(cache_key) do
        {:ok, robot_config} ->
          Logger.info("📦 [机器人配置仓储] 从缓存获取机器人配置: #{robot_config_id}")
          {:ok, robot_config}

        {:error, :not_found} ->
          fetch_and_cache_robot_config(robot_config_id, cache_key)
      end
    else
      fetch_robot_config_by_id(robot_config_id)
    end
  end

  @doc """
  更新机器人配置

  ## 参数
  - `robot_config_id` - 机器人配置ID
  - `update_data` - 更新数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, robot_config}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_robot_config(robot_config_id, update_data, options \\ []) do
    Logger.info("📝 [机器人配置仓储] 更新机器人配置: #{robot_config_id}")

    with {:ok, robot_config} <- get_robot_config_by_id(robot_config_id, use_cache: false),
         {:ok, updated_robot_config} <- RobotConfig.update(robot_config, update_data) do
      Logger.info("✅ [机器人配置仓储] 机器人配置更新成功: #{robot_config_id}")
      clear_robot_config_cache()
      {:ok, updated_robot_config}
    else
      {:error, error} ->
        Logger.error("❌ [机器人配置仓储] 机器人配置更新失败: #{inspect(error)}")
        {:error, error}
    end
  end

  @doc """
  删除机器人配置

  ## 参数
  - `robot_config_id` - 机器人配置ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, robot_config}` - 成功
  - `{:error, reason}` - 失败
  """
  def delete_robot_config(robot_config_id, options \\ []) do
    Logger.info("🗑️ [机器人配置仓储] 删除机器人配置: #{robot_config_id}")

    with {:ok, robot_config} <- get_robot_config_by_id(robot_config_id, use_cache: false),
         {:ok, deleted_robot_config} <- RobotConfig.destroy(robot_config) do
      Logger.info("✅ [机器人配置仓储] 机器人配置删除成功: #{robot_config_id}")
      clear_robot_config_cache()
      {:ok, deleted_robot_config}
    else
      {:error, error} ->
        Logger.error("❌ [机器人配置仓储] 机器人配置删除失败: #{inspect(error)}")
        {:error, error}
    end
  end

  # ==================== 查询操作 ====================

  @doc """
  获取所有活跃机器人配置

  ## 参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, robot_configs}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_active_robot_configs(options \\ []) do
    Logger.info("📋 [机器人配置仓储] 获取活跃机器人配置列表")

    cache_key = "#{@cache_prefix}:active_robot_configs"
    use_cache = Keyword.get(options, :use_cache, true)

    if use_cache do
      case get_from_cache(cache_key) do
        {:ok, robot_configs} ->
          Logger.info("📦 [机器人配置仓储] 从缓存获取活跃机器人配置")
          {:ok, robot_configs}

        {:error, :not_found} ->
          fetch_and_cache_active_robot_configs(cache_key)
      end
    else
      fetch_active_robot_configs()
    end
  end

  @doc """
  根据游戏类型获取机器人配置

  ## 参数
  - `game_type` - 游戏类型
  - `options` - 选项参数

  ## 返回
  - `{:ok, robot_configs}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_robots_by_game_type(game_type, options \\ []) do
    Logger.info("🎮 [机器人配置仓储] 根据游戏类型获取机器人: #{game_type}")

    cache_key = "#{@cache_prefix}:game_type:#{game_type}"
    use_cache = Keyword.get(options, :use_cache, true)

    if use_cache do
      case get_from_cache(cache_key) do
        {:ok, robot_configs} ->
          Logger.info("📦 [机器人配置仓储] 从缓存获取游戏机器人: #{game_type}")
          {:ok, robot_configs}

        {:error, :not_found} ->
          fetch_and_cache_robots_by_game_type(game_type, cache_key)
      end
    else
      fetch_robots_by_game_type(game_type)
    end
  end

  @doc """
  根据难度等级获取机器人配置

  ## 参数
  - `difficulty_level` - 难度等级
  - `options` - 选项参数

  ## 返回
  - `{:ok, robot_configs}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_robots_by_difficulty(difficulty_level, options \\ []) do
    Logger.info("⚡ [机器人配置仓储] 根据难度等级获取机器人: #{difficulty_level}")

    try do
      case RobotConfig.list_by_difficulty(difficulty_level) do
        {:ok, robot_configs} ->
          Logger.info("✅ [机器人配置仓储] 获取到 #{length(robot_configs)} 个难度机器人")
          {:ok, robot_configs}

        {:error, error} ->
          Logger.error("❌ [机器人配置仓储] 获取难度机器人失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [机器人配置仓储] 获取难度机器人异常: #{inspect(exception)}")
        {:error, :list_robots_by_difficulty_exception}
    end
  end

  @doc """
  获取机器人配置列表（带分页和过滤）

  ## 参数
  - `params` - 查询参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, %{robot_configs: robot_configs, pagination: pagination}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_robot_configs(params \\ %{}, options \\ []) do
    Logger.info("📋 [机器人配置仓储] 获取机器人配置列表: #{inspect(params)}")

    try do
      query = RobotConfig
      |> apply_robot_config_filters(params)
      |> apply_robot_config_sorting(params)
      |> apply_robot_config_pagination(params)

      case Ash.read(query) do
        {:ok, robot_configs} ->
          pagination = build_pagination_info(robot_configs, params)
          Logger.info("✅ [机器人配置仓储] 获取到 #{length(robot_configs)} 个机器人配置")
          {:ok, %{robot_configs: robot_configs, pagination: pagination}}

        {:error, error} ->
          Logger.error("❌ [机器人配置仓储] 获取机器人配置列表失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [机器人配置仓储] 获取机器人配置列表异常: #{inspect(exception)}")
        {:error, :list_robot_configs_exception}
    end
  end

  # ==================== 状态管理操作 ====================

  @doc """
  启用机器人配置

  ## 参数
  - `robot_config_id` - 机器人配置ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, robot_config}` - 成功
  - `{:error, reason}` - 失败
  """
  def enable_robot_config(robot_config_id, options \\ []) do
    Logger.info("🟢 [机器人配置仓储] 启用机器人配置: #{robot_config_id}")
    update_robot_config_status(robot_config_id, 1, options)
  end

  @doc """
  禁用机器人配置

  ## 参数
  - `robot_config_id` - 机器人配置ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, robot_config}` - 成功
  - `{:error, reason}` - 失败
  """
  def disable_robot_config(robot_config_id, options \\ []) do
    Logger.info("🔴 [机器人配置仓储] 禁用机器人配置: #{robot_config_id}")
    update_robot_config_status(robot_config_id, 0, options)
  end

  # ==================== 业务查询操作 ====================

  @doc """
  为用户选择合适的机器人

  ## 参数
  - `game_type` - 游戏类型
  - `user_skill_level` - 用户技能等级
  - `options` - 选项参数

  ## 返回
  - `{:ok, robot_config}` - 成功
  - `{:error, reason}` - 失败
  """
  def select_robot_for_user(game_type, user_skill_level, options \\ []) do
    Logger.info("🎯 [机器人配置仓储] 为用户选择机器人: #{game_type}, 技能等级: #{user_skill_level}")

    with {:ok, game_robots} <- list_robots_by_game_type(game_type, options) do
      # 根据用户技能等级选择合适难度的机器人
      difficulty_level = calculate_robot_difficulty(user_skill_level)

      suitable_robots = Enum.filter(game_robots, fn robot ->
        robot.difficulty_level == difficulty_level and robot.status == 1
      end)

      case suitable_robots do
        [] ->
          Logger.info("📭 [机器人配置仓储] 未找到合适的机器人")
          {:error, :no_suitable_robot}

        robots ->
          # 随机选择一个机器人
          selected_robot = Enum.random(robots)
          Logger.info("✅ [机器人配置仓储] 选择机器人: #{selected_robot.robot_name}")
          {:ok, selected_robot}
      end
    end
  end

  @doc """
  获取机器人统计信息

  ## 参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, stats}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_robot_stats(options \\ []) do
    Logger.info("📊 [机器人配置仓储] 获取机器人统计信息")

    with {:ok, all_robots} <- list_robot_configs(%{}, options) do
      robots = all_robots.robot_configs

      stats = %{
        total_count: length(robots),
        active_count: Enum.count(robots, & &1.status == 1),
        inactive_count: Enum.count(robots, & &1.status == 0),
        by_game_type: group_by_game_type(robots),
        by_difficulty: group_by_difficulty(robots),
        average_win_rate: calculate_average_win_rate(robots)
      }

      Logger.info("✅ [机器人配置仓储] 统计信息: #{inspect(stats)}")
      {:ok, stats}
    end
  end

  # ==================== 私有辅助函数 ====================

  # 从数据库获取机器人配置并缓存
  defp fetch_and_cache_robot_config(robot_config_id, cache_key) do
    case fetch_robot_config_by_id(robot_config_id) do
      {:ok, robot_config} ->
        cache_robot_config(cache_key, robot_config)
        {:ok, robot_config}

      error ->
        error
    end
  end

  # 从数据库获取机器人配置
  defp fetch_robot_config_by_id(robot_config_id) do
    try do
      case RobotConfig.read(robot_config_id) do
        {:ok, robot_config} ->
          Logger.info("✅ [机器人配置仓储] 从数据库获取机器人配置: #{robot_config_id}")
          {:ok, robot_config}

        {:error, error} ->
          Logger.error("❌ [机器人配置仓储] 获取机器人配置失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [机器人配置仓储] 获取机器人配置异常: #{inspect(exception)}")
        {:error, :fetch_robot_config_exception}
    end
  end

  # 获取活跃机器人配置并缓存
  defp fetch_and_cache_active_robot_configs(cache_key) do
    case fetch_active_robot_configs() do
      {:ok, robot_configs} ->
        cache_robot_configs(cache_key, robot_configs)
        {:ok, robot_configs}

      error ->
        error
    end
  end

  # 从数据库获取活跃机器人配置
  defp fetch_active_robot_configs do
    try do
      case RobotConfig.list_active_robots() do
        {:ok, robot_configs} ->
          Logger.info("✅ [机器人配置仓储] 获取到 #{length(robot_configs)} 个活跃机器人配置")
          {:ok, robot_configs}

        {:error, error} ->
          Logger.error("❌ [机器人配置仓储] 获取活跃机器人配置失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [机器人配置仓储] 获取活跃机器人配置异常: #{inspect(exception)}")
        {:error, :fetch_active_robot_configs_exception}
    end
  end

  # 获取游戏类型机器人并缓存
  defp fetch_and_cache_robots_by_game_type(game_type, cache_key) do
    case fetch_robots_by_game_type(game_type) do
      {:ok, robot_configs} ->
        cache_robot_configs(cache_key, robot_configs)
        {:ok, robot_configs}

      error ->
        error
    end
  end

  # 从数据库获取游戏类型机器人
  defp fetch_robots_by_game_type(game_type) do
    try do
      case RobotConfig.list_by_game_type(game_type) do
        {:ok, robot_configs} ->
          Logger.info("✅ [机器人配置仓储] 获取到 #{length(robot_configs)} 个游戏机器人")
          {:ok, robot_configs}

        {:error, error} ->
          Logger.error("❌ [机器人配置仓储] 获取游戏机器人失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [机器人配置仓储] 获取游戏机器人异常: #{inspect(exception)}")
        {:error, :fetch_robots_by_game_type_exception}
    end
  end

  # 更新机器人配置状态
  defp update_robot_config_status(robot_config_id, status, options) do
    update_robot_config(robot_config_id, %{status: status}, options)
  end

  # 计算机器人难度等级
  defp calculate_robot_difficulty(user_skill_level) do
    cond do
      user_skill_level <= 20 -> 1  # 简单
      user_skill_level <= 50 -> 2  # 普通
      user_skill_level <= 80 -> 3  # 困难
      true -> 4  # 专家
    end
  end

  # 按游戏类型分组
  defp group_by_game_type(robots) do
    Enum.group_by(robots, & &1.game_type)
    |> Enum.map(fn {game_type, robots} -> {game_type, length(robots)} end)
    |> Enum.into(%{})
  end

  # 按难度等级分组
  defp group_by_difficulty(robots) do
    Enum.group_by(robots, & &1.difficulty_level)
    |> Enum.map(fn {difficulty, robots} -> {difficulty, length(robots)} end)
    |> Enum.into(%{})
  end

  # 计算平均胜率
  defp calculate_average_win_rate(robots) do
    if length(robots) > 0 do
      total_win_rate = Enum.reduce(robots, Decimal.new("0"), fn robot, acc ->
        Decimal.add(acc, robot.win_rate)
      end)

      Decimal.div(total_win_rate, Decimal.new(length(robots)))
    else
      Decimal.new("0")
    end
  end

  # 应用机器人配置过滤条件
  defp apply_robot_config_filters(query, params) do
    Enum.reduce(params, query, fn
      {"status", status}, acc when not is_nil(status) ->
        filter(acc, status == ^status)

      {"game_type", game_type}, acc when not is_nil(game_type) and game_type != "" ->
        filter(acc, game_type == ^game_type)

      {"difficulty_level", level}, acc when not is_nil(level) ->
        filter(acc, difficulty_level == ^level)

      {"robot_name", name}, acc when not is_nil(name) and name != "" ->
        filter(acc, contains(robot_name, ^name))

      {"min_win_rate", min_rate}, acc when not is_nil(min_rate) ->
        filter(acc, win_rate >= ^min_rate)

      {"max_win_rate", max_rate}, acc when not is_nil(max_rate) ->
        filter(acc, win_rate <= ^max_rate)

      _other, acc ->
        acc
    end)
  end

  # 应用排序
  defp apply_robot_config_sorting(query, params) do
    sort_by = Map.get(params, "sort_by", "inserted_at")
    sort_order = Map.get(params, "sort_order", "desc")

    case {sort_by, sort_order} do
      {"inserted_at", "desc"} -> sort(query, desc: :inserted_at)
      {"inserted_at", "asc"} -> sort(query, asc: :inserted_at)
      {"robot_name", "asc"} -> sort(query, asc: :robot_name)
      {"robot_name", "desc"} -> sort(query, desc: :robot_name)
      {"win_rate", "desc"} -> sort(query, desc: :win_rate)
      {"win_rate", "asc"} -> sort(query, asc: :win_rate)
      {"difficulty_level", "asc"} -> sort(query, asc: :difficulty_level)
      {"difficulty_level", "desc"} -> sort(query, desc: :difficulty_level)
      _ -> sort(query, desc: :inserted_at)
    end
  end

  # 应用分页
  defp apply_robot_config_pagination(query, params) do
    page = Map.get(params, "page", 1) |> ensure_integer()
    page_size = Map.get(params, "page_size", 20) |> ensure_integer()
    offset_value = (page - 1) * page_size

    query
    |> Ash.Query.limit(page_size)
    |> Ash.Query.offset(offset_value)
  end

  # 构建分页信息
  defp build_pagination_info(robot_configs, params) do
    page = Map.get(params, "page", 1) |> ensure_integer()
    page_size = Map.get(params, "page_size", 20) |> ensure_integer()

    %{
      current_page: page,
      page_size: page_size,
      total_count: length(robot_configs),
      has_next: length(robot_configs) == page_size,
      has_prev: page > 1
    }
  end

  # 确保整数类型
  defp ensure_integer(value) when is_integer(value), do: value
  defp ensure_integer(value) when is_binary(value) do
    case Integer.parse(value) do
      {int, _} -> int
      :error -> 1
    end
  end
  defp ensure_integer(_), do: 1

  # 缓存操作
  defp get_from_cache(cache_key) do
    # 这里可以集成实际的缓存系统，如 Cachex 或 ETS
    {:error, :not_found}
  end

  defp cache_robot_config(cache_key, robot_config) do
    # 这里可以集成实际的缓存系统
    :ok
  end

  defp cache_robot_configs(cache_key, robot_configs) do
    # 这里可以集成实际的缓存系统
    :ok
  end

  defp clear_robot_config_cache do
    # 这里可以清理相关缓存
    :ok
  end
end
