defmodule <PERSON>pridina.Accounts.AdminServiceTest do
  use <PERSON><PERSON><PERSON><PERSON>.DataCase

  alias <PERSON>pridina.Accounts


  describe "权限级别系统" do
    test "is_admin?/1 正确识别管理员" do
      # 创建普通用户
      {:ok, user} =
        Accounts.User
        |> Ash.Changeset.for_create(:register_with_username, %{
          username: "testuser",
          password: "password123",
          password_confirmation: "password123"
        })
        |> Ash.create()

      refute Accounts.is_admin?(user)

      # 创建管理员
      {:ok, admin} =
        Accounts.User
        |> Ash.Changeset.for_create(:create_admin, %{
          username: "admin",
          password: "password123",
          password_confirmation: "password123",
          permission_level: 1,
          email: "<EMAIL>"
        })
        |> Ash.create()

      assert Accounts.is_admin?(admin)
    end

    test "is_super_admin?/1 正确识别超级管理员" do
      # 创建管理员
      {:ok, admin} =
        Accounts.User
        |> Ash.Changeset.for_create(:create_admin, %{
          username: "admin2",
          password: "password123",
          password_confirmation: "password123",
          permission_level: 1,
          email: "<EMAIL>"
        })
        |> Ash.create()

      refute Accounts.is_super_admin?(admin)

      # 创建超级管理员
      {:ok, super_admin} =
        Accounts.User
        |> Ash.Changeset.for_create(:create_admin, %{
          username: "superadmin",
          password: "password123",
          password_confirmation: "password123",
          permission_level: 2,
          email: "<EMAIL>"
        })
        |> Ash.create()

      assert Accounts.is_super_admin?(super_admin)
    end

    test "has_permission_level?/2 正确检查权限级别" do
      # 创建不同权限级别的用户
      {:ok, user} =
        Accounts.User
        |> Ash.Changeset.for_create(:register_with_username, %{
          username: "user",
          password: "password123",
          password_confirmation: "password123"
        })
        |> Ash.create()

      {:ok, admin} =
        Accounts.User
        |> Ash.Changeset.for_create(:create_admin, %{
          username: "admin3",
          password: "password123",
          password_confirmation: "password123",
          permission_level: 1,
          email: "<EMAIL>"
        })
        |> Ash.create()

      {:ok, super_admin} =
        Accounts.User
        |> Ash.Changeset.for_create(:create_admin, %{
          username: "superadmin2",
          password: "password123",
          password_confirmation: "password123",
          permission_level: 2,
          email: "<EMAIL>"
        })
        |> Ash.create()

      # 普通用户权限检查
      assert Accounts.has_permission_level?(user, 0)
      refute Accounts.has_permission_level?(user, 1)
      refute Accounts.has_permission_level?(user, 2)

      # 管理员权限检查
      assert Accounts.has_permission_level?(admin, 0)
      assert Accounts.has_permission_level?(admin, 1)
      refute Accounts.has_permission_level?(admin, 2)

      # 超级管理员权限检查
      assert Accounts.has_permission_level?(super_admin, 0)
      assert Accounts.has_permission_level?(super_admin, 1)
      assert Accounts.has_permission_level?(super_admin, 2)
    end

    test "permission_level_name/1 返回正确的权限级别名称" do
      assert Accounts.permission_level_name(0) == "普通用户"
      assert Accounts.permission_level_name(1) == "管理员"
      assert Accounts.permission_level_name(2) == "超级管理员"
      assert Accounts.permission_level_name(99) == "未知"
    end

    test "权限级别常量" do
      assert Accounts.user_level() == 0
      assert Accounts.admin_level() == 1
      assert Accounts.super_admin_level() == 2
    end
  end

  describe "兼容性函数" do
    test "兼容性函数正常工作" do
      {:ok, super_admin} =
        Accounts.User
        |> Ash.Changeset.for_create(:create_admin, %{
          username: "superadmin3",
          password: "password123",
          password_confirmation: "password123",
          permission_level: 2,
          email: "<EMAIL>"
        })
        |> Ash.create()

      # 测试兼容性别名
      assert Accounts.is_root_admin?(super_admin)
      assert Accounts.root_admin_exists?()
    end
  end
end
