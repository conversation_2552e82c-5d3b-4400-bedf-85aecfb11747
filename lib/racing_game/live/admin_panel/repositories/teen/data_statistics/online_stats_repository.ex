defmodule RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.OnlineStatsRepository do
  @moduledoc """
  📈 在线统计仓储层

  负责在线统计的数据访问操作，包括：
  - 在线统计的CRUD操作
  - 在线用户数据统计和分析
  - 峰值统计和趋势分析
  - 在线数据缓存管理
  - 实时统计数据处理
  """

  require Logger
  import Ash.Query

  alias Teen.Statistics.OnlineStats

  # 缓存配置
  @cache_ttl 300_000  # 5分钟缓存
  @cache_prefix "online_stats_repo"

  # ==================== 基础 CRUD 操作 ====================

  @doc """
  创建在线统计记录

  ## 参数
  - `stats_data` - 统计数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, online_stats}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_online_stats(stats_data, options \\ []) do
    Logger.info("📈 [在线统计仓储] 创建在线统计记录: #{inspect(stats_data[:stat_date])}")

    try do
      result = OnlineStats
      |> Ash.Changeset.for_create(:create, stats_data)
      |> Ash.create()

      case result do
        {:ok, online_stats} ->
          Logger.info("✅ [在线统计仓储] 在线统计记录创建成功: #{online_stats.stat_date}")
          clear_cache()
          {:ok, online_stats}
        {:error, error} ->
          Logger.error("❌ [在线统计仓储] 在线统计记录创建失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [在线统计仓储] 创建在线统计记录异常: #{inspect(exception)}")
        {:error, :create_failed}
    end
  end

  @doc """
  获取在线统计记录

  ## 参数
  - `stats_id` - 统计ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, online_stats}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_online_stats(stats_id, options \\ []) do
    Logger.info("📈 [在线统计仓储] 获取在线统计记录: #{stats_id}")

    cache_key = "#{@cache_prefix}:get:#{stats_id}"
    
    case get_from_cache(cache_key) do
      {:ok, cached_result} ->
        Logger.info("🎯 [在线统计仓储] 缓存命中: #{stats_id}")
        {:ok, cached_result}
      
      {:error, :not_found} ->
        try do
          result = OnlineStats |> Ash.get(stats_id)

          case result do
            {:ok, online_stats} ->
              Logger.info("✅ [在线统计仓储] 在线统计记录获取成功: #{online_stats.id}")
              put_to_cache(cache_key, online_stats)
              {:ok, online_stats}
            {:error, error} ->
              Logger.error("❌ [在线统计仓储] 在线统计记录获取失败: #{inspect(error)}")
              {:error, error}
          end
        rescue
          exception ->
            Logger.error("💥 [在线统计仓储] 获取在线统计记录异常: #{inspect(exception)}")
            {:error, :get_failed}
        end
    end
  end

  @doc """
  更新在线统计记录

  ## 参数
  - `online_stats` - 在线统计实例
  - `update_data` - 更新数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, updated_online_stats}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_online_stats(online_stats, update_data, options \\ []) do
    Logger.info("📈 [在线统计仓储] 更新在线统计记录: #{online_stats.id}")

    try do
      result = online_stats
      |> Ash.Changeset.for_update(:update, update_data)
      |> Ash.update()

      case result do
        {:ok, updated_online_stats} ->
          Logger.info("✅ [在线统计仓储] 在线统计记录更新成功: #{updated_online_stats.id}")
          clear_cache()
          {:ok, updated_online_stats}
        {:error, error} ->
          Logger.error("❌ [在线统计仓储] 在线统计记录更新失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [在线统计仓储] 更新在线统计记录异常: #{inspect(exception)}")
        {:error, :update_failed}
    end
  end

  @doc """
  删除在线统计记录

  ## 参数
  - `online_stats` - 在线统计实例
  - `options` - 选项参数

  ## 返回
  - `{:ok, deleted_online_stats}` - 成功
  - `{:error, reason}` - 失败
  """
  def delete_online_stats(online_stats, options \\ []) do
    Logger.info("📈 [在线统计仓储] 删除在线统计记录: #{online_stats.id}")

    try do
      result = online_stats |> Ash.destroy()

      case result do
        :ok ->
          Logger.info("✅ [在线统计仓储] 在线统计记录删除成功: #{online_stats.id}")
          clear_cache()
          {:ok, online_stats}
        {:error, error} ->
          Logger.error("❌ [在线统计仓储] 在线统计记录删除失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [在线统计仓储] 删除在线统计记录异常: #{inspect(exception)}")
        {:error, :delete_failed}
    end
  end

  # ==================== 查询操作 ====================

  @doc """
  列出在线统计记录

  ## 参数
  - `params` - 查询参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, {stats_list, total_count}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_online_stats(params \\ %{}, options \\ []) do
    Logger.info("📈 [在线统计仓储] 列出在线统计记录: #{inspect(params)}")

    try do
      query = OnlineStats
      |> build_list_query(params)
      |> maybe_load_associations(options)

      # 获取总数
      total_count = query |> Ash.count!()

      # 应用分页
      query = query
      |> limit(Map.get(params, :page_size, 20))
      |> offset(Map.get(params, :offset, 0))

      stats_list = query |> Ash.read!()

      Logger.info("✅ [在线统计仓储] 在线统计记录列表获取成功，共 #{length(stats_list)} 条")
      {:ok, {stats_list, total_count}}
    rescue
      exception ->
        Logger.error("💥 [在线统计仓储] 列出在线统计记录异常: #{inspect(exception)}")
        {:error, :list_failed}
    end
  end

  @doc """
  按日期获取在线统计

  ## 参数
  - `stat_date` - 统计日期
  - `options` - 选项参数

  ## 返回
  - `{:ok, stats_list}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_stats_by_date(stat_date, options \\ []) do
    Logger.info("📈 [在线统计仓储] 按日期获取在线统计: #{stat_date}")

    cache_key = "#{@cache_prefix}:by_date:#{stat_date}"
    
    case get_from_cache(cache_key) do
      {:ok, cached_result} ->
        Logger.info("🎯 [在线统计仓储] 缓存命中: #{stat_date}")
        {:ok, cached_result}
      
      {:error, :not_found} ->
        try do
          stats_list = OnlineStats
          |> Ash.Query.filter(stat_date == stat_date)
          |> Ash.Query.sort(stat_hour: :asc)
          |> maybe_load_associations(options)
          |> Ash.read!()

          Logger.info("✅ [在线统计仓储] 按日期获取在线统计成功: #{length(stats_list)} 条")
          put_to_cache(cache_key, stats_list)
          {:ok, stats_list}
        rescue
          exception ->
            Logger.error("💥 [在线统计仓储] 按日期获取在线统计异常: #{inspect(exception)}")
            {:error, :get_by_date_failed}
        end
    end
  end

  @doc """
  按日期范围获取在线统计

  ## 参数
  - `start_date` - 开始日期
  - `end_date` - 结束日期
  - `options` - 选项参数

  ## 返回
  - `{:ok, stats_list}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_stats_by_date_range(start_date, end_date, options \\ []) do
    Logger.info("📈 [在线统计仓储] 按日期范围获取在线统计: #{start_date} - #{end_date}")

    try do
      stats_list = OnlineStats
      |> Ash.Query.filter(stat_date >= start_date and stat_date  <= end_date)
      |> Ash.Query.sort([stat_date: :desc, stat_hour: :asc])
      |> maybe_load_associations(options)
      |> Ash.read!()

      Logger.info("✅ [在线统计仓储] 按日期范围获取在线统计成功: #{length(stats_list)} 条")
      {:ok, stats_list}
    rescue
      exception ->
        Logger.error("💥 [在线统计仓储] 按日期范围获取在线统计异常: #{inspect(exception)}")
        {:error, :get_by_date_range_failed}
    end
  end

  @doc """
  获取峰值统计

  ## 参数
  - `days` - 天数
  - `options` - 选项参数

  ## 返回
  - `{:ok, peak_stats}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_peak_stats(days \\ 7, options \\ []) do
    Logger.info("📈 [在线统计仓储] 获取峰值统计: #{days} 天")

    try do
      start_date = Date.utc_today() |> Date.add(-days)
      
      peak_stats = OnlineStats
      |> Ash.Query.filter(stat_date >= start_date)
      |> Ash.Query.sort(peak_count: :desc)
      |> Ash.Query.limit(10)
      |> maybe_load_associations(options)
      |> Ash.read!()

      Logger.info("✅ [在线统计仓储] 获取峰值统计成功: #{length(peak_stats)} 条")
      {:ok, peak_stats}
    rescue
      exception ->
        Logger.error("💥 [在线统计仓储] 获取峰值统计异常: #{inspect(exception)}")
        {:error, :get_peak_stats_failed}
    end
  end

  @doc """
  获取小时统计

  ## 参数
  - `stat_date` - 统计日期
  - `options` - 选项参数

  ## 返回
  - `{:ok, hourly_stats}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_hourly_stats(stat_date, options \\ []) do
    Logger.info("📈 [在线统计仓储] 获取小时统计: #{stat_date}")

    try do
      hourly_stats = OnlineStats
      |> Ash.Query.filter(stat_date == stat_date)
      |> Ash.Query.sort(stat_hour: :asc)
      |> maybe_load_associations(options)
      |> Ash.read!()

      Logger.info("✅ [在线统计仓储] 获取小时统计成功: #{length(hourly_stats)} 条")
      {:ok, hourly_stats}
    rescue
      exception ->
        Logger.error("💥 [在线统计仓储] 获取小时统计异常: #{inspect(exception)}")
        {:error, :get_hourly_stats_failed}
    end
  end

  # ==================== 私有辅助函数 ====================

  # 构建列表查询
  defp build_list_query(query, params) do
    query
    |> maybe_filter_by_date_range(params)
    |> maybe_filter_by_hour(params)
    |> maybe_apply_search(params)
    |> maybe_apply_sort(params)
  end

  # 按日期范围过滤
  defp maybe_filter_by_date_range(query, %{start_date: start_date, end_date: end_date}) 
    when not is_nil(start_date) and not is_nil(end_date) do
    query |> Ash.Query.filter(stat_date >= start_date and stat_date  <= end_date)
  end
  defp maybe_filter_by_date_range(query, _), do: query

  # 按小时过滤
  defp maybe_filter_by_hour(query, %{stat_hour: stat_hour}) when not is_nil(stat_hour) do
    query |> Ash.Query.filter(stat_hour  == stat_hour)
  end
  defp maybe_filter_by_hour(query, _), do: query

  # 应用搜索
  defp maybe_apply_search(query, %{search: search}) when search != "" do
    # 在线统计主要是数值数据，搜索功能有限
    query
  end
  defp maybe_apply_search(query, _), do: query

  # 应用排序
  defp maybe_apply_sort(query, %{sort_by: sort_by, sort_order: sort_order}) do
    sort_field = String.to_atom(sort_by)
    sort_direction = String.to_atom(sort_order)
    query |> Ash.Query.sort([{sort_direction, sort_field}])
  end
  defp maybe_apply_sort(query, _) do
    query |> Ash.Query.sort([stat_date: :desc, stat_hour: :asc])
  end

  # 加载关联数据
  defp maybe_load_associations(query, options) do
    case Keyword.get(options, :load_associations, false) do
      true -> query |> Ash.Query.load([])
      _ -> query
    end
  end

  # 缓存操作
  defp get_from_cache(_key), do: {:error, :not_found}
  defp put_to_cache(_key, _value), do: :ok
  defp clear_cache(), do: :ok
end
