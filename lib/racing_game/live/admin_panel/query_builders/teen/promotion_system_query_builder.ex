defmodule RacingGame.Live.AdminPanel.QueryBuilders.Teen.PromotionSystemQueryBuilder do
  @moduledoc """
  🎯 推广系统查询构建器

  负责推广系统的复杂查询构建，包括：
  - 推广员综合查询和统计
  - 推广关系链查询和分析
  - 推广渠道效果分析
  - 佣金结算统计分析
  - 分享系统统计分析
  - 推广系统综合报表
  """

  require Logger

  alias RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.{
    PromoterRepository,
    PromotionChannelRepository,
    PromotionSettlementRepository,
    ShareConfigRepository,
    ShareSettlementRepository
  }

  # ==================== 推广员综合查询 ====================

  @doc """
  构建推广员综合统计查询

  ## 参数
  - `filters` - 过滤条件
  - `options` - 选项参数

  ## 返回
  - `{:ok, stats}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_promoter_comprehensive_stats(filters \\ [], options \\ []) do
    Logger.info("🎯 [推广系统查询构建器] 构建推广员综合统计查询")

    try do
      # 并行执行多个统计查询
      tasks = [
        Task.async(fn -> get_promoter_basic_stats(filters, options) end),
        Task.async(fn -> get_promoter_level_distribution(filters, options) end),
        Task.async(fn -> get_promoter_status_distribution(filters, options) end),
        Task.async(fn -> get_top_promoters_by_performance(filters, options) end)
      ]

      [basic_stats, level_distribution, status_distribution, top_promoters] =
        Task.await_many(tasks, 30_000)

      comprehensive_stats = %{
        basic_stats: basic_stats,
        level_distribution: level_distribution,
        status_distribution: status_distribution,
        top_promoters: top_promoters,
        generated_at: DateTime.utc_now()
      }

      Logger.info("✅ [推广系统查询构建器] 推广员综合统计查询构建成功")
      {:ok, comprehensive_stats}

    rescue
      error ->
        Logger.error("❌ [推广系统查询构建器] 推广员综合统计查询构建失败: #{inspect(error)}")
        {:error, :query_build_failed}
    end
  end

  @doc """
  构建推广员关系链查询

  ## 参数
  - `promoter_id` - 推广员ID
  - `depth` - 查询深度
  - `options` - 选项参数

  ## 返回
  - `{:ok, relationship_tree}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_promoter_relationship_tree(promoter_id, depth \\ 3, options \\ []) do
    Logger.info("🎯 [推广系统查询构建器] 构建推广员关系链查询: #{promoter_id}")

    try do
      with {:ok, root_promoter} <- PromoterRepository.get_promoter(promoter_id),
           {:ok, relationship_tree} <- build_relationship_tree_recursive(root_promoter, depth, 0) do

        Logger.info("✅ [推广系统查询构建器] 推广员关系链查询构建成功")
        {:ok, relationship_tree}
      else
        {:error, reason} ->
          Logger.error("❌ [推广系统查询构建器] 推广员关系链查询构建失败: #{inspect(reason)}")
          {:error, reason}
      end

    rescue
      error ->
        Logger.error("❌ [推广系统查询构建器] 推广员关系链查询构建异常: #{inspect(error)}")
        {:error, :query_build_failed}
    end
  end

  # ==================== 推广渠道效果分析 ====================

  @doc """
  构建推广渠道效果分析查询

  ## 参数
  - `filters` - 过滤条件
  - `options` - 选项参数

  ## 返回
  - `{:ok, channel_analysis}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_channel_performance_analysis(filters \\ [], options \\ []) do
    Logger.info("🎯 [推广系统查询构建器] 构建推广渠道效果分析查询")

    try do
      # 并行执行渠道分析查询
      tasks = [
        Task.async(fn -> get_channel_type_performance(filters, options) end),
        Task.async(fn -> get_channel_conversion_analysis(filters, options) end),
        Task.async(fn -> get_top_performing_channels(filters, options) end),
        Task.async(fn -> get_channel_trend_analysis(filters, options) end)
      ]

      [type_performance, conversion_analysis, top_channels, trend_analysis] =
        Task.await_many(tasks, 30_000)

      channel_analysis = %{
        type_performance: type_performance,
        conversion_analysis: conversion_analysis,
        top_channels: top_channels,
        trend_analysis: trend_analysis,
        generated_at: DateTime.utc_now()
      }

      Logger.info("✅ [推广系统查询构建器] 推广渠道效果分析查询构建成功")
      {:ok, channel_analysis}

    rescue
      error ->
        Logger.error("❌ [推广系统查询构建器] 推广渠道效果分析查询构建失败: #{inspect(error)}")
        {:error, :query_build_failed}
    end
  end

  # ==================== 佣金结算统计分析 ====================

  @doc """
  构建佣金结算统计分析查询

  ## 参数
  - `date_range` - 日期范围
  - `options` - 选项参数

  ## 返回
  - `{:ok, commission_analysis}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_commission_settlement_analysis(date_range \\ nil, options \\ []) do
    Logger.info("🎯 [推广系统查询构建器] 构建佣金结算统计分析查询")

    try do
      # 并行执行佣金分析查询
      tasks = [
        Task.async(fn -> get_commission_overview_stats(date_range, options) end),
        Task.async(fn -> get_commission_trend_analysis(date_range, options) end),
        Task.async(fn -> get_top_commission_earners(date_range, options) end),
        Task.async(fn -> get_commission_settlement_status(date_range, options) end)
      ]

      [overview_stats, trend_analysis, top_earners, settlement_status] =
        Task.await_many(tasks, 30_000)

      commission_analysis = %{
        overview_stats: overview_stats,
        trend_analysis: trend_analysis,
        top_earners: top_earners,
        settlement_status: settlement_status,
        generated_at: DateTime.utc_now()
      }

      Logger.info("✅ [推广系统查询构建器] 佣金结算统计分析查询构建成功")
      {:ok, commission_analysis}

    rescue
      error ->
        Logger.error("❌ [推广系统查询构建器] 佣金结算统计分析查询构建失败: #{inspect(error)}")
        {:error, :query_build_failed}
    end
  end

  # ==================== 分享系统统计分析 ====================

  @doc """
  构建分享系统统计分析查询

  ## 参数
  - `date_range` - 日期范围
  - `options` - 选项参数

  ## 返回
  - `{:ok, share_analysis}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_share_system_analysis(date_range \\ nil, options \\ []) do
    Logger.info("🎯 [推广系统查询构建器] 构建分享系统统计分析查询")

    try do
      # 并行执行分享系统分析查询
      tasks = [
        Task.async(fn -> get_share_overview_stats(date_range, options) end),
        Task.async(fn -> get_share_type_distribution(date_range, options) end),
        Task.async(fn -> get_share_settlement_analysis(date_range, options) end),
        Task.async(fn -> get_active_share_configs(options) end)
      ]

      [overview_stats, type_distribution, settlement_analysis, active_configs] =
        Task.await_many(tasks, 30_000)

      share_analysis = %{
        overview_stats: overview_stats,
        type_distribution: type_distribution,
        settlement_analysis: settlement_analysis,
        active_configs: active_configs,
        generated_at: DateTime.utc_now()
      }

      Logger.info("✅ [推广系统查询构建器] 分享系统统计分析查询构建成功")
      {:ok, share_analysis}

    rescue
      error ->
        Logger.error("❌ [推广系统查询构建器] 分享系统统计分析查询构建失败: #{inspect(error)}")
        {:error, :query_build_failed}
    end
  end

  # ==================== 推广系统综合报表 ====================

  @doc """
  构建推广系统综合报表查询

  ## 参数
  - `date_range` - 日期范围
  - `options` - 选项参数

  ## 返回
  - `{:ok, comprehensive_report}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_comprehensive_promotion_report(date_range \\ nil, options \\ []) do
    Logger.info("🎯 [推广系统查询构建器] 构建推广系统综合报表查询")

    try do
      # 并行执行综合报表查询
      tasks = [
        Task.async(fn -> build_promoter_comprehensive_stats([], options) end),
        Task.async(fn -> build_channel_performance_analysis([], options) end),
        Task.async(fn -> build_commission_settlement_analysis(date_range, options) end),
        Task.async(fn -> build_share_system_analysis(date_range, options) end),
        Task.async(fn -> get_system_health_indicators(options) end)
      ]

      [promoter_stats, channel_analysis, commission_analysis, share_analysis, health_indicators] =
        Task.await_many(tasks, 60_000)

      comprehensive_report = %{
        promoter_stats: promoter_stats,
        channel_analysis: channel_analysis,
        commission_analysis: commission_analysis,
        share_analysis: share_analysis,
        health_indicators: health_indicators,
        report_period: date_range,
        generated_at: DateTime.utc_now()
      }

      Logger.info("✅ [推广系统查询构建器] 推广系统综合报表查询构建成功")
      {:ok, comprehensive_report}

    rescue
      error ->
        Logger.error("❌ [推广系统查询构建器] 推广系统综合报表查询构建失败: #{inspect(error)}")
        {:error, :query_build_failed}
    end
  end

  # ==================== 私有辅助函数 ====================

  defp get_promoter_basic_stats(filters, options) do
    with {:ok, promoters} <- PromoterRepository.list_promoters(filters, options) do
      total_count = length(promoters)
      active_count = promoters |> Enum.count(& &1.status == 1)
      total_commission = promoters |> Enum.map(& &1.total_commission) |> Enum.reduce(Decimal.new("0"), &Decimal.add/2)
      total_invites = promoters |> Enum.map(& &1.total_invites) |> Enum.sum()

      %{
        total_count: total_count,
        active_count: active_count,
        total_commission: total_commission,
        total_invites: total_invites,
        average_commission: if(total_count > 0, do: Decimal.div(total_commission, Decimal.new(total_count)), else: Decimal.new("0"))
      }
    else
      _ -> %{total_count: 0, active_count: 0, total_commission: Decimal.new("0"), total_invites: 0, average_commission: Decimal.new("0")}
    end
  end

  defp get_promoter_level_distribution(filters, options) do
    with {:ok, promoters} <- PromoterRepository.list_promoters(filters, options) do
      promoters
      |> Enum.group_by(& &1.level)
      |> Enum.map(fn {level, level_promoters} ->
        level_name = case level do
          1 -> "初级推广员"
          2 -> "中级推广员"
          3 -> "高级推广员"
          4 -> "超级推广员"
          _ -> "未知等级"
        end

        {level, %{
          level_name: level_name,
          count: length(level_promoters),
          total_commission: level_promoters |> Enum.map(& &1.total_commission) |> Enum.reduce(Decimal.new("0"), &Decimal.add/2)
        }}
      end)
      |> Enum.into(%{})
    else
      _ -> %{}
    end
  end

  defp get_promoter_status_distribution(filters, options) do
    with {:ok, promoters} <- PromoterRepository.list_promoters(filters, options) do
      promoters
      |> Enum.group_by(& &1.status)
      |> Enum.map(fn {status, status_promoters} ->
        status_name = case status do
          0 -> "禁用"
          1 -> "启用"
          2 -> "审核中"
          3 -> "已拒绝"
          _ -> "未知状态"
        end

        {status, %{
          status_name: status_name,
          count: length(status_promoters)
        }}
      end)
      |> Enum.into(%{})
    else
      _ -> %{}
    end
  end

  defp get_top_promoters_by_performance(filters, options) do
    options_with_limit = Keyword.put(options, :page_size, 10)
    options_with_sort = Keyword.put(options_with_limit, :sort, total_commission: :desc)

    case PromoterRepository.list_promoters(filters, options_with_sort) do
      {:ok, promoters} -> promoters
      _ -> []
    end
  end

  defp build_relationship_tree_recursive(promoter, max_depth, current_depth) when current_depth >= max_depth do
    {:ok, %{
      promoter: promoter,
      children: [],
      depth: current_depth
    }}
  end

  defp build_relationship_tree_recursive(promoter, max_depth, current_depth) do
    # 查找下级推广员
    case PromoterRepository.list_promoters([parent_promoter_id: promoter.id]) do
      {:ok, children} ->
        child_trees = Enum.map(children, fn child ->
          case build_relationship_tree_recursive(child, max_depth, current_depth + 1) do
            {:ok, child_tree} -> child_tree
            _ -> %{promoter: child, children: [], depth: current_depth + 1}
          end
        end)

        {:ok, %{
          promoter: promoter,
          children: child_trees,
          depth: current_depth
        }}

      _ ->
        {:ok, %{
          promoter: promoter,
          children: [],
          depth: current_depth
        }}
    end
  end

  defp get_channel_type_performance(filters, options) do
    with {:ok, channels} <- PromotionChannelRepository.list_channels(filters, options) do
      channels
      |> Enum.group_by(& &1.channel_type)
      |> Enum.map(fn {type, type_channels} ->
        total_clicks = type_channels |> Enum.map(& &1.click_count || 0) |> Enum.sum()
        total_registers = type_channels |> Enum.map(& &1.register_count || 0) |> Enum.sum()
        avg_conversion = if total_clicks > 0, do: Decimal.div(Decimal.new(total_registers), Decimal.new(total_clicks)) |> Decimal.mult(Decimal.new(100)), else: Decimal.new("0")

        {type, %{
          channel_count: length(type_channels),
          total_clicks: total_clicks,
          total_registers: total_registers,
          conversion_rate: avg_conversion
        }}
      end)
      |> Enum.into(%{})
    else
      _ -> %{}
    end
  end

  defp get_channel_conversion_analysis(filters, options) do
    with {:ok, channels} <- PromotionChannelRepository.list_channels(filters, options) do
      total_clicks = channels |> Enum.map(& &1.click_count || 0) |> Enum.sum()
      total_registers = channels |> Enum.map(& &1.register_count || 0) |> Enum.sum()

      # 转化率分布
      conversion_ranges = [
        {0, 1, "0-1%"},
        {1, 5, "1-5%"},
        {5, 10, "5-10%"},
        {10, 20, "10-20%"},
        {20, 100, "20%+"}
      ]

      range_distribution = Enum.map(conversion_ranges, fn {min, max, label} ->
        count = channels |> Enum.count(fn channel ->
          rate = if (channel.click_count || 0) > 0 do
            Decimal.div(Decimal.new(channel.register_count || 0), Decimal.new(channel.click_count))
            |> Decimal.mult(Decimal.new(100))
            |> Decimal.to_float()
          else
            0.0
          end
          rate >= min and rate < max
        end)

        {label, count}
      end)

      %{
        total_clicks: total_clicks,
        total_registers: total_registers,
        overall_conversion_rate: if(total_clicks > 0, do: Decimal.div(Decimal.new(total_registers), Decimal.new(total_clicks)) |> Decimal.mult(Decimal.new(100)), else: Decimal.new("0")),
        conversion_distribution: range_distribution
      }
    else
      _ -> %{total_clicks: 0, total_registers: 0, overall_conversion_rate: Decimal.new("0"), conversion_distribution: []}
    end
  end

  defp get_top_performing_channels(filters, options) do
    options_with_limit = Keyword.put(options, :page_size, 10)
    options_with_sort = Keyword.put(options_with_limit, :sort, register_count: :desc)

    case PromotionChannelRepository.list_channels(filters, options_with_sort) do
      {:ok, channels} -> channels
      _ -> []
    end
  end

  defp get_channel_trend_analysis(filters, options) do
    # 获取最近30天的渠道趋势数据
    end_date = Date.utc_today()
    start_date = Date.add(end_date, -30)

    date_range = {start_date, end_date}
    filters_with_date = filters ++ [created_at: [gte: start_date, lte: end_date]]

    with {:ok, channels} <- PromotionChannelRepository.list_channels(filters_with_date, options) do
      # 按日期分组统计
      daily_stats = channels
      |> Enum.group_by(fn channel ->
        channel.inserted_at |> DateTime.to_date()
      end)
      |> Enum.map(fn {date, date_channels} ->
        {date, %{
          new_channels: length(date_channels),
          total_clicks: date_channels |> Enum.map(& &1.click_count || 0) |> Enum.sum(),
          total_registers: date_channels |> Enum.map(& &1.register_count || 0) |> Enum.sum()
        }}
      end)
      |> Enum.sort_by(fn {date, _} -> date end)

      %{
        date_range: date_range,
        daily_stats: daily_stats
      }
    else
      _ -> %{date_range: date_range, daily_stats: []}
    end
  end

  defp get_commission_overview_stats(date_range, options) do
    with {:ok, settlements} <- PromotionSettlementRepository.get_system_settlement_stats(date_range, options) do
      settlements
    else
      _ -> %{total_count: 0, total_amount: Decimal.new("0"), pending_count: 0, completed_count: 0, failed_count: 0}
    end
  end

  defp get_commission_trend_analysis(date_range, options) do
    # 获取佣金趋势分析
    filters = if date_range, do: [settlement_date: date_range], else: []

    with {:ok, settlements} <- PromotionSettlementRepository.list_settlements(filters, options) do
      # 按月份分组统计
      monthly_stats = settlements
      |> Enum.group_by(fn settlement ->
        settlement.settlement_date |> Date.beginning_of_month()
      end)
      |> Enum.map(fn {month, month_settlements} ->
        total_amount = month_settlements |> Enum.map(& &1.commission_amount) |> Enum.reduce(Decimal.new("0"), &Decimal.add/2)

        {month, %{
          settlement_count: length(month_settlements),
          total_amount: total_amount,
          average_amount: if(length(month_settlements) > 0, do: Decimal.div(total_amount, Decimal.new(length(month_settlements))), else: Decimal.new("0"))
        }}
      end)
      |> Enum.sort_by(fn {month, _} -> month end)

      %{monthly_stats: monthly_stats}
    else
      _ -> %{monthly_stats: []}
    end
  end

  defp get_top_commission_earners(date_range, options) do
    filters = if date_range, do: [settlement_date: date_range], else: []
    options_with_limit = Keyword.put(options, :page_size, 10)

    with {:ok, settlements} <- PromotionSettlementRepository.list_settlements(filters, options_with_limit) do
      # 按推广员分组统计
      settlements
      |> Enum.group_by(& &1.promoter_id)
      |> Enum.map(fn {promoter_id, promoter_settlements} ->
        total_commission = promoter_settlements |> Enum.map(& &1.commission_amount) |> Enum.reduce(Decimal.new("0"), &Decimal.add/2)

        %{
          promoter_id: promoter_id,
          settlement_count: length(promoter_settlements),
          total_commission: total_commission
        }
      end)
      |> Enum.sort_by(& &1.total_commission, :desc)
      |> Enum.take(10)
    else
      _ -> []
    end
  end

  defp get_commission_settlement_status(date_range, options) do
    with {:ok, stats} <- PromotionSettlementRepository.get_system_settlement_stats(date_range, options) do
      %{
        pending_settlements: %{count: stats.pending_count, percentage: calculate_percentage(stats.pending_count, stats.total_count)},
        completed_settlements: %{count: stats.completed_count, percentage: calculate_percentage(stats.completed_count, stats.total_count)},
        failed_settlements: %{count: stats.failed_count, percentage: calculate_percentage(stats.failed_count, stats.total_count)}
      }
    else
      _ -> %{pending_settlements: %{count: 0, percentage: 0}, completed_settlements: %{count: 0, percentage: 0}, failed_settlements: %{count: 0, percentage: 0}}
    end
  end

  defp get_share_overview_stats(date_range, options) do
    with {:ok, stats} <- ShareSettlementRepository.get_system_settlement_stats(date_range, options) do
      stats
    else
      _ -> %{total_count: 0, total_amount: Decimal.new("0"), type_stats: %{}, pending_count: 0, completed_count: 0, failed_count: 0}
    end
  end

  defp get_share_type_distribution(date_range, options) do
    with {:ok, stats} <- ShareSettlementRepository.get_system_settlement_stats(date_range, options) do
      stats.type_stats
    else
      _ -> %{}
    end
  end

  defp get_share_settlement_analysis(date_range, options) do
    filters = if date_range, do: [settlement_date: date_range], else: []

    with {:ok, settlements} <- ShareSettlementRepository.list_settlements(filters, options) do
      # 按结算类型分析
      type_analysis = settlements
      |> Enum.group_by(& &1.settlement_type)
      |> Enum.map(fn {type, type_settlements} ->
        total_amount = type_settlements |> Enum.map(& &1.reward_amount) |> Enum.reduce(Decimal.new("0"), &Decimal.add/2)
        avg_amount = if length(type_settlements) > 0, do: Decimal.div(total_amount, Decimal.new(length(type_settlements))), else: Decimal.new("0")

        {type, %{
          count: length(type_settlements),
          total_amount: total_amount,
          average_amount: avg_amount
        }}
      end)
      |> Enum.into(%{})

      %{type_analysis: type_analysis}
    else
      _ -> %{type_analysis: %{}}
    end
  end

  defp get_active_share_configs(options) do
    case ShareConfigRepository.list_active_configs(options) do
      {:ok, configs} -> configs
      _ -> []
    end
  end

  defp get_system_health_indicators(options) do
    # 系统健康指标
    %{
      promoter_activity_rate: calculate_promoter_activity_rate(),
      channel_effectiveness_score: calculate_channel_effectiveness_score(),
      commission_settlement_efficiency: calculate_settlement_efficiency(),
      share_system_utilization: calculate_share_utilization(),
      overall_health_score: calculate_overall_health_score()
    }
  end

  defp calculate_percentage(count, total) when total > 0 do
    Decimal.div(Decimal.new(count), Decimal.new(total))
    |> Decimal.mult(Decimal.new(100))
    |> Decimal.round(2)
    |> Decimal.to_float()
  end
  defp calculate_percentage(_, _), do: 0.0

  defp calculate_promoter_activity_rate do
    # 计算推广员活跃率
    case PromoterRepository.list_promoters() do
      {:ok, promoters} ->
        total_count = length(promoters)
        active_count = promoters |> Enum.count(& &1.status == 1)
        if total_count > 0, do: (active_count / total_count * 100) |> Float.round(2), else: 0.0
      _ -> 0.0
    end
  end

  defp calculate_channel_effectiveness_score do
    # 计算渠道有效性评分
    case PromotionChannelRepository.list_active_channels() do
      {:ok, channels} ->
        if length(channels) > 0 do
          total_conversion = channels
          |> Enum.map(fn channel ->
            if (channel.click_count || 0) > 0 do
              (channel.register_count || 0) / (channel.click_count || 1) * 100
            else
              0.0
            end
          end)
          |> Enum.sum()

          (total_conversion / length(channels)) |> Float.round(2)
        else
          0.0
        end
      _ -> 0.0
    end
  end

  defp calculate_settlement_efficiency do
    # 计算结算效率
    case PromotionSettlementRepository.get_system_settlement_stats() do
      {:ok, stats} ->
        if stats.total_count > 0 do
          Decimal.to_float(stats.completion_rate)
        else
          0.0
        end
      _ -> 0.0
    end
  end

  defp calculate_share_utilization do
    # 计算分享系统利用率
    case ShareSettlementRepository.get_system_settlement_stats() do
      {:ok, stats} ->
        if stats.total_count > 0 do
          Decimal.to_float(stats.completion_rate)
        else
          0.0
        end
      _ -> 0.0
    end
  end

  defp calculate_overall_health_score do
    # 计算整体健康评分
    activity_rate = calculate_promoter_activity_rate()
    effectiveness_score = calculate_channel_effectiveness_score()
    settlement_efficiency = calculate_settlement_efficiency()
    share_utilization = calculate_share_utilization()

    # 加权平均计算整体评分
    ((activity_rate * 0.3) + (effectiveness_score * 0.3) + (settlement_efficiency * 0.2) + (share_utilization * 0.2))
    |> Float.round(2)
  end
end
