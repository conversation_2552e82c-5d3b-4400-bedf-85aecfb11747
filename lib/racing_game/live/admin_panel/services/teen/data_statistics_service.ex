defmodule RacingGame.Live.AdminPanel.Services.Teen.DataStatisticsService do
  @moduledoc """
  📊 数据统计服务层

  负责数据统计系统的业务逻辑协调，包括：
  - 统计数据的业务逻辑处理
  - 跨仓储的数据协调和整合
  - 数据分析和报表生成
  - 统计任务的调度和管理
  - 数据质量监控和验证
  """

  require Logger

  alias RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.{
    SystemReportRepository,
    OnlineStatsRepository,
    ChannelStatsRepository,
    UserStatsRepository,
    CoinStatsRepository,
    RetentionStatsRepository,
    PaymentStatsRepository,
    LtvStatsRepository,
    RobotStatsRepository
  }

  alias RacingGame.Live.AdminPanel.QueryBuilders.Teen.DataStatisticsQueryBuilder

  # ==================== 统计数据管理 ====================

  @doc """
  创建系统报表

  ## 参数
  - `report_data` - 报表数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, system_report}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_system_report(report_data, options \\ []) do
    Logger.info("📊 [数据统计服务] 创建系统报表: #{inspect(report_data[:report_type])}")

    with {:ok, validated_data} <- validate_report_data(report_data),
         {:ok, system_report} <- SystemReportRepository.create_system_report(validated_data, options),
         :ok <- trigger_report_notifications(system_report, options) do
      Logger.info("✅ [数据统计服务] 系统报表创建成功: #{system_report.id}")
      {:ok, system_report}
    else
      {:error, reason} ->
        Logger.error("❌ [数据统计服务] 系统报表创建失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  批量创建统计数据

  ## 参数
  - `stats_batch` - 统计数据批次
  - `options` - 选项参数

  ## 返回
  - `{:ok, batch_results}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_stats_batch(stats_batch, options \\ []) do
    Logger.info("📊 [数据统计服务] 批量创建统计数据: #{length(stats_batch)} 条")

    try do
      # 按类型分组处理
      grouped_stats = group_stats_by_type(stats_batch)
      
      # 并行处理各类型统计数据
      tasks = Enum.map(grouped_stats, fn {type, stats_list} ->
        Task.async(fn -> process_stats_by_type(type, stats_list, options) end)
      end)

      results = Task.await_many(tasks, 60_000)
      
      batch_results = %{
        total_processed: length(stats_batch),
        results_by_type: Enum.zip(Map.keys(grouped_stats), results),
        processing_time: DateTime.utc_now(),
        success_count: count_successful_results(results),
        error_count: count_error_results(results)
      }

      Logger.info("✅ [数据统计服务] 批量统计数据创建完成: 成功 #{batch_results.success_count}, 失败 #{batch_results.error_count}")
      {:ok, batch_results}
    rescue
      exception ->
        Logger.error("💥 [数据统计服务] 批量创建统计数据异常: #{inspect(exception)}")
        {:error, :batch_create_failed}
    end
  end

  @doc """
  生成综合统计报告

  ## 参数
  - `report_config` - 报告配置
  - `options` - 选项参数

  ## 返回
  - `{:ok, comprehensive_report}` - 成功
  - `{:error, reason}` - 失败
  """
  def generate_comprehensive_report(report_config, options \\ []) do
    Logger.info("📊 [数据统计服务] 生成综合统计报告: #{inspect(report_config[:report_type])}")

    try do
      with {:ok, validated_config} <- validate_report_config(report_config),
           {:ok, raw_data} <- collect_report_data(validated_config, options),
           {:ok, processed_data} <- process_report_data(raw_data, validated_config, options),
           {:ok, formatted_report} <- format_comprehensive_report(processed_data, validated_config, options) do
        
        # 保存报告记录
        report_record = %{
          report_type: validated_config.report_type,
          report_date: Date.utc_today(),
          data_summary: extract_report_summary(formatted_report),
          status: "completed",
          generated_at: DateTime.utc_now()
        }

        case SystemReportRepository.create_system_report(report_record, options) do
          {:ok, _saved_report} ->
            Logger.info("✅ [数据统计服务] 综合统计报告生成成功")
            {:ok, formatted_report}
          {:error, save_error} ->
            Logger.warn("⚠️ [数据统计服务] 报告保存失败，但报告生成成功: #{inspect(save_error)}")
            {:ok, formatted_report}
        end
      else
        {:error, reason} ->
          Logger.error("❌ [数据统计服务] 综合统计报告生成失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [数据统计服务] 生成综合统计报告异常: #{inspect(exception)}")
        {:error, :generate_report_failed}
    end
  end

  @doc """
  执行数据分析任务

  ## 参数
  - `analysis_config` - 分析配置
  - `options` - 选项参数

  ## 返回
  - `{:ok, analysis_results}` - 成功
  - `{:error, reason}` - 失败
  """
  def execute_data_analysis(analysis_config, options \\ []) do
    Logger.info("📊 [数据统计服务] 执行数据分析任务: #{inspect(analysis_config[:analysis_type])}")

    try do
      with {:ok, validated_config} <- validate_analysis_config(analysis_config),
           {:ok, analysis_results} <- perform_data_analysis(validated_config, options),
           :ok <- store_analysis_results(analysis_results, validated_config, options) do
        
        Logger.info("✅ [数据统计服务] 数据分析任务执行成功")
        {:ok, analysis_results}
      else
        {:error, reason} ->
          Logger.error("❌ [数据统计服务] 数据分析任务执行失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [数据统计服务] 执行数据分析任务异常: #{inspect(exception)}")
        {:error, :execute_analysis_failed}
    end
  end

  @doc """
  获取实时统计数据

  ## 参数
  - `metrics` - 指标列表
  - `options` - 选项参数

  ## 返回
  - `{:ok, realtime_data}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_realtime_statistics(metrics \\ [:all], options \\ []) do
    Logger.info("📊 [数据统计服务] 获取实时统计数据: #{inspect(metrics)}")

    try do
      case DataStatisticsQueryBuilder.get_realtime_dashboard(options) do
        {:ok, dashboard_data} ->
          filtered_data = filter_metrics_data(dashboard_data, metrics)
          
          realtime_data = %{
            metrics: filtered_data,
            last_updated: DateTime.utc_now(),
            data_freshness: calculate_data_freshness(filtered_data),
            system_status: assess_system_status(filtered_data)
          }

          Logger.info("✅ [数据统计服务] 实时统计数据获取成功")
          {:ok, realtime_data}
        
        {:error, reason} ->
          Logger.error("❌ [数据统计服务] 实时统计数据获取失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [数据统计服务] 获取实时统计数据异常: #{inspect(exception)}")
        {:error, :get_realtime_failed}
    end
  end

  @doc """
  执行统计数据同步

  ## 参数
  - `sync_config` - 同步配置
  - `options` - 选项参数

  ## 返回
  - `{:ok, sync_results}` - 成功
  - `{:error, reason}` - 失败
  """
  def synchronize_statistics(sync_config \\ %{}, options \\ []) do
    Logger.info("📊 [数据统计服务] 执行统计数据同步")

    try do
      sync_tasks = [
        {:online_stats, fn -> sync_online_statistics(sync_config, options) end},
        {:user_stats, fn -> sync_user_statistics(sync_config, options) end},
        {:payment_stats, fn -> sync_payment_statistics(sync_config, options) end},
        {:channel_stats, fn -> sync_channel_statistics(sync_config, options) end},
        {:retention_stats, fn -> sync_retention_statistics(sync_config, options) end}
      ]

      # 并行执行同步任务
      tasks = Enum.map(sync_tasks, fn {type, task_fn} ->
        Task.async(fn -> {type, task_fn.()} end)
      end)

      results = Task.await_many(tasks, 120_000)
      
      sync_results = %{
        sync_timestamp: DateTime.utc_now(),
        results: results,
        success_count: count_sync_successes(results),
        error_count: count_sync_errors(results),
        total_synced: length(results)
      }

      Logger.info("✅ [数据统计服务] 统计数据同步完成: 成功 #{sync_results.success_count}, 失败 #{sync_results.error_count}")
      {:ok, sync_results}
    rescue
      exception ->
        Logger.error("💥 [数据统计服务] 统计数据同步异常: #{inspect(exception)}")
        {:error, :sync_failed}
    end
  end

  # ==================== 数据质量管理 ====================

  @doc """
  验证统计数据质量

  ## 参数
  - `validation_config` - 验证配置
  - `options` - 选项参数

  ## 返回
  - `{:ok, validation_results}` - 成功
  - `{:error, reason}` - 失败
  """
  def validate_data_quality(validation_config \\ %{}, options \\ []) do
    Logger.info("📊 [数据统计服务] 验证统计数据质量")

    try do
      validation_tasks = [
        {:completeness, fn -> check_data_completeness(validation_config, options) end},
        {:consistency, fn -> check_data_consistency(validation_config, options) end},
        {:accuracy, fn -> check_data_accuracy(validation_config, options) end},
        {:timeliness, fn -> check_data_timeliness(validation_config, options) end}
      ]

      # 并行执行验证任务
      tasks = Enum.map(validation_tasks, fn {type, task_fn} ->
        Task.async(fn -> {type, task_fn.()} end)
      end)

      results = Task.await_many(tasks, 60_000)
      
      validation_results = %{
        validation_timestamp: DateTime.utc_now(),
        quality_checks: results,
        overall_score: calculate_quality_score(results),
        issues_found: extract_quality_issues(results),
        recommendations: generate_quality_recommendations(results)
      }

      Logger.info("✅ [数据统计服务] 数据质量验证完成，质量评分: #{validation_results.overall_score}")
      {:ok, validation_results}
    rescue
      exception ->
        Logger.error("💥 [数据统计服务] 验证统计数据质量异常: #{inspect(exception)}")
        {:error, :validate_quality_failed}
    end
  end

  @doc """
  修复数据质量问题

  ## 参数
  - `repair_config` - 修复配置
  - `options` - 选项参数

  ## 返回
  - `{:ok, repair_results}` - 成功
  - `{:error, reason}` - 失败
  """
  def repair_data_issues(repair_config, options \\ []) do
    Logger.info("📊 [数据统计服务] 修复数据质量问题")

    try do
      with {:ok, validated_config} <- validate_repair_config(repair_config),
           {:ok, issue_analysis} <- analyze_data_issues(validated_config, options),
           {:ok, repair_plan} <- create_repair_plan(issue_analysis, validated_config, options),
           {:ok, repair_results} <- execute_repair_plan(repair_plan, options) do
        
        Logger.info("✅ [数据统计服务] 数据质量问题修复完成")
        {:ok, repair_results}
      else
        {:error, reason} ->
          Logger.error("❌ [数据统计服务] 数据质量问题修复失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      exception ->
        Logger.error("💥 [数据统计服务] 修复数据质量问题异常: #{inspect(exception)}")
        {:error, :repair_issues_failed}
    end
  end

  # ==================== 私有辅助函数 ====================

  # 验证报表数据
  defp validate_report_data(report_data) do
    required_fields = [:report_type, :report_date]
    
    case check_required_fields(report_data, required_fields) do
      :ok ->
        validated_data = report_data
        |> Map.put_new(:status, "pending")
        |> Map.put_new(:created_at, DateTime.utc_now())
        
        {:ok, validated_data}
      {:error, missing_fields} ->
        {:error, {:missing_fields, missing_fields}}
    end
  end

  # 触发报表通知
  defp trigger_report_notifications(system_report, options) do
    if Keyword.get(options, :send_notifications, false) do
      # 这里可以集成通知系统
      Logger.info("📧 [数据统计服务] 触发报表通知: #{system_report.id}")
    end
    :ok
  end

  # 按类型分组统计数据
  defp group_stats_by_type(stats_batch) do
    Enum.group_by(stats_batch, fn stat -> 
      Map.get(stat, :type, :unknown)
    end)
  end

  # 按类型处理统计数据
  defp process_stats_by_type(type, stats_list, options) do
    case type do
      :online_stats -> 
        Enum.map(stats_list, &OnlineStatsRepository.create_online_stats(&1, options))
      :user_stats -> 
        Enum.map(stats_list, &UserStatsRepository.create_user_stats(&1, options))
      :payment_stats -> 
        Enum.map(stats_list, &PaymentStatsRepository.create_payment_stats(&1, options))
      :channel_stats -> 
        Enum.map(stats_list, &ChannelStatsRepository.create_channel_stats(&1, options))
      :retention_stats -> 
        Enum.map(stats_list, &RetentionStatsRepository.create_retention_stats(&1, options))
      :ltv_stats -> 
        Enum.map(stats_list, &LtvStatsRepository.create_ltv_stats(&1, options))
      :coin_stats -> 
        Enum.map(stats_list, &CoinStatsRepository.create_coin_stats(&1, options))
      :robot_stats -> 
        Enum.map(stats_list, &RobotStatsRepository.create_robot_stats(&1, options))
      _ -> 
        [{:error, :unknown_stats_type}]
    end
  end

  # 统计成功结果数量
  defp count_successful_results(results) do
    results
    |> List.flatten()
    |> Enum.count(fn result -> match?({:ok, _}, result) end)
  end

  # 统计错误结果数量
  defp count_error_results(results) do
    results
    |> List.flatten()
    |> Enum.count(fn result -> match?({:error, _}, result) end)
  end

  # 验证报告配置
  defp validate_report_config(report_config) do
    required_fields = [:report_type, :date_range]
    
    case check_required_fields(report_config, required_fields) do
      :ok -> {:ok, report_config}
      error -> error
    end
  end

  # 收集报告数据
  defp collect_report_data(config, options) do
    case DataStatisticsQueryBuilder.get_comprehensive_stats(config.date_range, options) do
      {:ok, data} -> {:ok, data}
      error -> error
    end
  end

  # 处理报告数据
  defp process_report_data(raw_data, config, _options) do
    processed_data = raw_data
    |> add_calculated_metrics()
    |> add_trend_analysis()
    |> add_comparative_analysis()
    
    {:ok, processed_data}
  end

  # 格式化综合报告
  defp format_comprehensive_report(processed_data, config, _options) do
    formatted_report = %{
      report_type: config.report_type,
      generated_at: DateTime.utc_now(),
      data: processed_data,
      summary: generate_report_summary(processed_data),
      insights: generate_report_insights(processed_data),
      recommendations: generate_report_recommendations(processed_data)
    }
    
    {:ok, formatted_report}
  end

  # 提取报告摘要
  defp extract_report_summary(formatted_report) do
    %{
      total_metrics: count_report_metrics(formatted_report),
      key_insights: length(formatted_report.insights),
      recommendations: length(formatted_report.recommendations)
    }
  end

  # 检查必需字段
  defp check_required_fields(data, required_fields) do
    missing_fields = Enum.filter(required_fields, fn field ->
      not Map.has_key?(data, field) or is_nil(Map.get(data, field))
    end)
    
    if Enum.empty?(missing_fields) do
      :ok
    else
      {:error, {:missing_fields, missing_fields}}
    end
  end

  # 添加计算指标
  defp add_calculated_metrics(data) do
    # 添加计算得出的指标
    Map.put(data, :calculated_metrics, %{
      growth_rates: calculate_growth_rates(data),
      conversion_rates: calculate_conversion_rates(data),
      efficiency_metrics: calculate_efficiency_metrics(data)
    })
  end

  # 添加趋势分析
  defp add_trend_analysis(data) do
    Map.put(data, :trend_analysis, %{
      user_trends: analyze_user_trends(data),
      revenue_trends: analyze_revenue_trends(data),
      engagement_trends: analyze_engagement_trends(data)
    })
  end

  # 添加对比分析
  defp add_comparative_analysis(data) do
    Map.put(data, :comparative_analysis, %{
      period_comparison: compare_periods(data),
      channel_comparison: compare_channels(data),
      segment_comparison: compare_segments(data)
    })
  end

  # 生成报告摘要
  defp generate_report_summary(processed_data) do
    %{
      key_metrics: extract_key_metrics(processed_data),
      performance_highlights: extract_performance_highlights(processed_data),
      areas_of_concern: extract_areas_of_concern(processed_data)
    }
  end

  # 生成报告洞察
  defp generate_report_insights(processed_data) do
    [
      analyze_user_behavior_insights(processed_data),
      analyze_revenue_insights(processed_data),
      analyze_retention_insights(processed_data),
      analyze_channel_insights(processed_data)
    ]
    |> Enum.filter(&(&1 != nil))
  end

  # 生成报告建议
  defp generate_report_recommendations(processed_data) do
    [
      recommend_user_acquisition_strategies(processed_data),
      recommend_retention_improvements(processed_data),
      recommend_monetization_optimizations(processed_data),
      recommend_operational_improvements(processed_data)
    ]
    |> Enum.filter(&(&1 != nil))
  end

  # 计算报告指标数量
  defp count_report_metrics(formatted_report) do
    # 计算报告中包含的指标总数
    base_metrics = map_size(formatted_report.data)
    calculated_metrics = map_size(Map.get(formatted_report.data, :calculated_metrics, %{}))
    base_metrics + calculated_metrics
  end

  # 辅助函数的占位符实现
  defp calculate_growth_rates(_data), do: %{}
  defp calculate_conversion_rates(_data), do: %{}
  defp calculate_efficiency_metrics(_data), do: %{}
  defp analyze_user_trends(_data), do: %{}
  defp analyze_revenue_trends(_data), do: %{}
  defp analyze_engagement_trends(_data), do: %{}
  defp compare_periods(_data), do: %{}
  defp compare_channels(_data), do: %{}
  defp compare_segments(_data), do: %{}
  defp extract_key_metrics(_data), do: %{}
  defp extract_performance_highlights(_data), do: %{}
  defp extract_areas_of_concern(_data), do: %{}
  defp analyze_user_behavior_insights(_data), do: nil
  defp analyze_revenue_insights(_data), do: nil
  defp analyze_retention_insights(_data), do: nil
  defp analyze_channel_insights(_data), do: nil
  defp recommend_user_acquisition_strategies(_data), do: nil
  defp recommend_retention_improvements(_data), do: nil
  defp recommend_monetization_optimizations(_data), do: nil
  defp recommend_operational_improvements(_data), do: nil
end
