// If you want to use Phoenix channels, run `mix help phx.gen.channel`
// to get started and then uncomment the line below.
// import "./user_socket.js"

// You can include dependencies in two ways.
//
// The simplest option is to put them in assets/vendor and
// import them using relative paths:
//
//     import "../vendor/some-package.js"
//
// Alternatively, you can `npm install some-package --prefix assets` and import
// them using a path starting with the package name:
//
//     import "some-package"
//
// If you have dependencies that try to import CSS, esbuild will generate a separate `app.css` file.
// To load it, simply add a second `<link>` to your `root.html.heex` file.

// Include phoenix_html to handle method=PUT/DELETE in forms and buttons.
import "phoenix_html"
// Establish Phoenix Socket and LiveView configuration.
import { Socket } from "phoenix"
import { Hooks as BackpexHooks } from 'backpex';
import { LiveSocket } from "phoenix_live_view"
import topbar from "../vendor/topbar"
import { <PERSON><PERSON>hartHook, FlashMessagesHook } from "./hooks"
import { SortableRanking } from "./sortable_ranking"
import { DraggableDialog } from "./draggable_dialog"
import { ThemeManager, InteractionEnhancer } from "./theme_manager"

// Import performance optimization systems
import { LazyLoadHook } from "./performance/lazy_loader"
import { VirtualScrollHook } from "./performance/virtual_scroll"
import { PerformanceMonitorHook } from "./performance/performance_monitor"
import { ResourceOptimizerHook } from "./performance/resource_optimizer"

// Import interaction enhancement systems
import { AnimationEngineHook } from "./interactions/animation_engine"
import { TransitionHook } from "./interactions/transition_components"
import { FeedbackHook } from "./interactions/feedback_system"


const csrfToken = document.querySelector("meta[name='csrf-token']").getAttribute("content")

// 定义Hooks (包含性能优化和交互增强Hooks)
const Hooks = {
  RacingChart: RacingChartHook,
  FlashMessages: FlashMessagesHook,
  SortableRanking: SortableRanking,
  DraggableDialog: DraggableDialog,
  // Performance optimization hooks
  LazyLoad: LazyLoadHook,
  VirtualScroll: VirtualScrollHook,
  PerformanceMonitor: PerformanceMonitorHook,
  ResourceOptimizer: ResourceOptimizerHook,
  // Interaction enhancement hooks
  AnimationEngine: AnimationEngineHook,
  Transition: TransitionHook,
  Feedback: FeedbackHook
}

const liveSocket = new LiveSocket("/live", Socket, {
  // longPollFallbackMs: 2500,
  params: { _csrf_token: csrfToken },
  hooks: { ...Hooks, ...BackpexHooks },
  dom: {
    onBeforeElUpdated(from, to) {
      // Preserve focus and scroll position during updates
      if (from._x_dataStack) {
        to._x_dataStack = from._x_dataStack;
      }

      // Preserve component state for performance
      if (from.dataset.preserveState) {
        to.dataset.preserveState = from.dataset.preserveState;
      }
    }
  }
})

// Show progress bar on live navigation and form submits
topbar.config({ barColors: { 0: "#29d" }, shadowColor: "rgba(0, 0, 0, .3)" })
window.addEventListener("phx:page-loading-start", _info => topbar.show(300))
window.addEventListener("phx:page-loading-stop", _info => topbar.hide())

// connect if there are any LiveViews on the page
liveSocket.connect()

// expose liveSocket on window for web console debug logs and latency simulation:
// >> liveSocket.enableDebug()
// >> liveSocket.enableLatencySim(1000)  // enabled for duration of browser session
// >> liveSocket.disableLatencySim()
window.liveSocket = liveSocket

// 初始化主题管理器、交互增强器、性能优化和动画系统
document.addEventListener('DOMContentLoaded', () => {
  // Initialize theme system
  window.themeManager = new ThemeManager()
  window.interactionEnhancer = new InteractionEnhancer()

  // Performance optimization is auto-initialized via imports
  console.log('🚀 Racing Game Admin Panel - Performance Optimized')

  // Animation and transition systems are auto-initialized via imports
  console.log('✨ Animation and transition systems active')

  // Preload high-priority components
  if (window.componentLazyLoader) {
    window.componentLazyLoader.preloadHighPriorityComponents()
  }

  // Log performance optimization status
  if (window.performanceMonitor) {
    console.log('📊 Performance monitoring active')
  }

  if (window.resourceOptimizer) {
    console.log('⚡ Resource optimization active')
  }

  // Log animation system status
  if (window.animationEngine) {
    console.log('🎬 Animation engine active')
  }

  if (window.transitionManager) {
    console.log('🔄 Transition manager active')
  }

  if (window.feedbackSystem) {
    console.log('💬 Feedback system active')
  }
})

// The lines below enable quality of life phoenix_live_reload
// development features:
//
//     1. stream server logs to the browser console
//     2. click on elements to jump to their definitions in your code editor
//
if (process.env.NODE_ENV === "development") {
  window.addEventListener("phx:live_reload:attached", ({ detail: reloader }) => {
    // Enable server log streaming to client.
    // Disable with reloader.disableServerLogs()
    reloader.enableServerLogs()

    // Open configured PLUG_EDITOR at file:line of the clicked element's HEEx component
    //
    //   * click with "c" key pressed to open at caller location
    //   * click with "d" key pressed to open at function component definition location
    let keyDown
    window.addEventListener("keydown", e => keyDown = e.key)
    window.addEventListener("keyup", e => keyDown = null)
    window.addEventListener("click", e => {
      if (keyDown === "c") {
        e.preventDefault()
        e.stopImmediatePropagation()
        reloader.openEditorAtCaller(e.target)
      } else if (keyDown === "d") {
        e.preventDefault()
        e.stopImmediatePropagation()
        reloader.openEditorAtDef(e.target)
      }
    }, true)

    window.liveReloader = reloader
  })
}

