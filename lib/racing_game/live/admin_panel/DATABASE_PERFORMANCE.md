# 数据库性能优化指南

## 📋 概述

本文档提供 Racing Game Admin Panel 系统数据库性能优化的全面指南，包括查询优化、索引策略、连接池配置、缓存策略和监控方法，确保数据库在高并发场景下的稳定性能。

## 🎯 性能优化目标

### 核心性能指标
- **查询响应时间**: 95% 查询 < 100ms，99% 查询 < 500ms
- **并发处理能力**: 支持 1000+ 并发连接
- **数据库可用性**: 99.9% 以上
- **事务吞吐量**: 10000+ TPS
- **存储效率**: 数据压缩率 > 30%

### 业务性能要求
- **用户查询**: 响应时间 < 50ms
- **支付处理**: 端到端延迟 < 200ms
- **数据统计**: 复杂报表查询 < 2s
- **批量操作**: 10万条记录处理 < 30s

## 🔍 查询性能优化

### 查询分析和优化
```sql
-- 1. 启用查询性能监控
ALTER SYSTEM SET log_min_duration_statement = 1000; -- 记录超过1秒的查询
ALTER SYSTEM SET log_statement = 'all'; -- 记录所有SQL语句
ALTER SYSTEM SET log_checkpoints = on;
ALTER SYSTEM SET log_connections = on;
ALTER SYSTEM SET log_disconnections = on;
SELECT pg_reload_conf();

-- 2. 查询执行计划分析
EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) 
SELECT u.id, u.username, COUNT(p.id) as payment_count
FROM users u
LEFT JOIN payments p ON u.id = p.user_id
WHERE u.created_at >= '2024-01-01'
GROUP BY u.id, u.username
ORDER BY payment_count DESC
LIMIT 100;

-- 3. 慢查询优化示例
-- 优化前: 全表扫描
SELECT * FROM payments WHERE created_at >= '2024-01-01' AND status = 'completed';

-- 优化后: 使用复合索引
CREATE INDEX CONCURRENTLY idx_payments_status_created 
ON payments(status, created_at) 
WHERE status = 'completed';

-- 4. 子查询优化
-- 优化前: 相关子查询
SELECT u.id, u.username,
  (SELECT COUNT(*) FROM payments p WHERE p.user_id = u.id) as payment_count
FROM users u;

-- 优化后: JOIN查询
SELECT u.id, u.username, COALESCE(p.payment_count, 0) as payment_count
FROM users u
LEFT JOIN (
  SELECT user_id, COUNT(*) as payment_count
  FROM payments
  GROUP BY user_id
) p ON u.id = p.user_id;
```

### Ecto 查询优化
```elixir
# 查询优化模块
defmodule RacingGame.QueryOptimization do
  import Ecto.Query
  alias RacingGame.Repo

  # 1. 预加载优化
  def get_user_with_payments_optimized(user_id) do
    # 优化前: N+1 查询问题
    # user = Repo.get(User, user_id)
    # payments = Enum.map(user.payments, &load_payment_details/1)

    # 优化后: 预加载
    from(u in User,
      where: u.id == ^user_id,
      preload: [payments: ^from(p in Payment, order_by: [desc: p.created_at])]
    )
    |> Repo.one()
  end

  # 2. 分页查询优化
  def list_users_paginated(page, per_page, filters \\ %{}) do
    base_query = from(u in User)
    
    query = 
      base_query
      |> apply_filters(filters)
      |> order_by([u], desc: u.created_at)
      |> limit(^per_page)
      |> offset(^((page - 1) * per_page))

    # 使用窗口函数优化计数查询
    count_query = 
      from(u in User)
      |> apply_filters(filters)
      |> select([u], count(u.id))

    users = Repo.all(query)
    total_count = Repo.one(count_query)

    %{
      users: users,
      total_count: total_count,
      page: page,
      per_page: per_page,
      total_pages: ceil(total_count / per_page)
    }
  end

  # 3. 批量操作优化
  def bulk_update_user_status(user_ids, status) when is_list(user_ids) do
    # 使用批量更新而不是循环单个更新
    from(u in User, where: u.id in ^user_ids)
    |> Repo.update_all(set: [status: status, updated_at: DateTime.utc_now()])
  end

  # 4. 复杂聚合查询优化
  def payment_statistics_optimized(date_range) do
    from(p in Payment,
      where: p.created_at >= ^date_range.start_date and p.created_at <= ^date_range.end_date,
      group_by: [fragment("DATE(?)", p.created_at), p.status],
      select: %{
        date: fragment("DATE(?)", p.created_at),
        status: p.status,
        count: count(p.id),
        total_amount: sum(p.amount),
        avg_amount: avg(p.amount)
      }
    )
    |> Repo.all()
  end

  defp apply_filters(query, filters) do
    Enum.reduce(filters, query, fn
      {:status, status}, query when not is_nil(status) ->
        from(q in query, where: q.status == ^status)
      
      {:created_after, date}, query when not is_nil(date) ->
        from(q in query, where: q.created_at >= ^date)
      
      {:search, term}, query when not is_nil(term) and term != "" ->
        search_term = "%#{term}%"
        from(q in query, 
          where: ilike(q.username, ^search_term) or ilike(q.email, ^search_term)
        )
      
      _, query -> query
    end)
  end
end
```

## 📊 索引策略优化

### 索引设计原则
```sql
-- 1. 单列索引
CREATE INDEX CONCURRENTLY idx_users_email ON users(email);
CREATE INDEX CONCURRENTLY idx_users_phone ON users(phone);
CREATE INDEX CONCURRENTLY idx_payments_user_id ON payments(user_id);

-- 2. 复合索引 (注意列的顺序)
-- 高选择性列在前
CREATE INDEX CONCURRENTLY idx_payments_status_created_user 
ON payments(status, created_at, user_id);

-- 3. 部分索引 (条件索引)
CREATE INDEX CONCURRENTLY idx_payments_pending 
ON payments(created_at) 
WHERE status = 'pending';

CREATE INDEX CONCURRENTLY idx_users_active 
ON users(last_login_at) 
WHERE status = 'active';

-- 4. 表达式索引
CREATE INDEX CONCURRENTLY idx_users_email_lower 
ON users(LOWER(email));

CREATE INDEX CONCURRENTLY idx_payments_amount_rounded 
ON payments(ROUND(amount, 2));

-- 5. 全文搜索索引
ALTER TABLE users ADD COLUMN search_vector tsvector;

UPDATE users SET search_vector = 
  to_tsvector('english', COALESCE(username, '') || ' ' || COALESCE(email, ''));

CREATE INDEX CONCURRENTLY idx_users_search 
ON users USING gin(search_vector);

-- 6. 哈希索引 (等值查询)
CREATE INDEX CONCURRENTLY idx_users_id_hash 
ON users USING hash(id);
```

### 索引维护和监控
```sql
-- 索引使用情况分析
SELECT 
  schemaname,
  tablename,
  indexname,
  idx_tup_read,
  idx_tup_fetch,
  idx_tup_read::float / NULLIF(idx_tup_fetch, 0) as selectivity
FROM pg_stat_user_indexes 
ORDER BY idx_tup_read DESC;

-- 未使用的索引检测
SELECT 
  schemaname,
  tablename,
  indexname,
  pg_size_pretty(pg_relation_size(indexrelid)) as size
FROM pg_stat_user_indexes 
WHERE idx_tup_read = 0 AND idx_tup_fetch = 0
ORDER BY pg_relation_size(indexrelid) DESC;

-- 重复索引检测
WITH index_columns AS (
  SELECT 
    schemaname,
    tablename,
    indexname,
    array_agg(attname ORDER BY attnum) as columns
  FROM pg_indexes 
  JOIN pg_attribute ON attrelid = (schemaname||'.'||tablename)::regclass
  WHERE attnum > 0
  GROUP BY schemaname, tablename, indexname
)
SELECT 
  i1.schemaname,
  i1.tablename,
  i1.indexname as index1,
  i2.indexname as index2,
  i1.columns
FROM index_columns i1
JOIN index_columns i2 ON 
  i1.schemaname = i2.schemaname AND 
  i1.tablename = i2.tablename AND 
  i1.columns = i2.columns AND 
  i1.indexname < i2.indexname;

-- 索引膨胀检测
SELECT 
  schemaname,
  tablename,
  indexname,
  pg_size_pretty(pg_relation_size(indexrelid)) as size,
  CASE 
    WHEN pg_relation_size(indexrelid) > 100 * 1024 * 1024 THEN '需要重建'
    ELSE '正常'
  END as status
FROM pg_stat_user_indexes
ORDER BY pg_relation_size(indexrelid) DESC;
```

## 🔧 连接池和配置优化

### PostgreSQL 配置优化
```sql
-- postgresql.conf 优化配置
-- 内存配置
shared_buffers = '256MB'                    -- 共享缓冲区
effective_cache_size = '1GB'                -- 有效缓存大小
work_mem = '4MB'                           -- 工作内存
maintenance_work_mem = '64MB'               -- 维护工作内存

-- 连接配置
max_connections = 200                       -- 最大连接数
superuser_reserved_connections = 3          -- 超级用户保留连接

-- 检查点配置
checkpoint_completion_target = 0.9          -- 检查点完成目标
wal_buffers = '16MB'                       -- WAL缓冲区
checkpoint_timeout = '10min'                -- 检查点超时

-- 日志配置
log_min_duration_statement = 1000          -- 记录慢查询
log_checkpoints = on                       -- 记录检查点
log_connections = on                       -- 记录连接
log_disconnections = on                    -- 记录断开连接
log_lock_waits = on                        -- 记录锁等待

-- 统计配置
track_activities = on                      -- 跟踪活动
track_counts = on                          -- 跟踪计数
track_io_timing = on                       -- 跟踪IO时间
track_functions = 'all'                    -- 跟踪函数
```

### Ecto 连接池配置
```elixir
# config/prod.exs - 生产环境数据库配置
config :racing_game, RacingGame.Repo,
  # 连接池配置
  pool_size: 20,                           # 连接池大小
  queue_target: 5000,                      # 队列目标时间(ms)
  queue_interval: 1000,                    # 队列检查间隔(ms)
  
  # 连接配置
  timeout: 15_000,                         # 查询超时(ms)
  ownership_timeout: 10_000,               # 连接所有权超时(ms)
  
  # SSL配置
  ssl: true,
  ssl_opts: [
    verify: :verify_peer,
    cacertfile: "/path/to/ca-cert.pem",
    certfile: "/path/to/client-cert.pem",
    keyfile: "/path/to/client-key.pem"
  ],
  
  # 连接参数
  parameters: [
    application_name: "racing_game_prod",
    statement_timeout: "30s",
    lock_timeout: "10s"
  ]

# 连接池监控
defmodule RacingGame.DatabaseMonitor do
  use GenServer
  require Logger

  def start_link(_) do
    GenServer.start_link(__MODULE__, %{}, name: __MODULE__)
  end

  def init(state) do
    schedule_check()
    {:ok, state}
  end

  def handle_info(:check_pool, state) do
    pool_status = :poolboy.status(RacingGame.Repo.Pool)
    
    Logger.info("Database pool status", %{
      size: pool_status[:size],
      max_overflow: pool_status[:max_overflow],
      overflow: pool_status[:overflow],
      monitors: pool_status[:monitors]
    })
    
    # 检查连接池健康状态
    if pool_status[:overflow] > pool_status[:size] * 0.8 do
      Logger.warn("Database pool overflow high", %{
        overflow: pool_status[:overflow],
        size: pool_status[:size]
      })
    end
    
    schedule_check()
    {:noreply, state}
  end

  defp schedule_check do
    Process.send_after(self(), :check_pool, 30_000) # 30秒检查一次
  end
end
```

## 💾 缓存策略优化

### 多层缓存架构
```elixir
defmodule RacingGame.CacheStrategy do
  @moduledoc "多层缓存策略实现"
  
  # L1缓存: ETS本地缓存
  defmodule L1Cache do
    def start_link do
      :ets.new(:l1_cache, [:set, :public, :named_table])
      {:ok, self()}
    end
    
    def get(key) do
      case :ets.lookup(:l1_cache, key) do
        [{^key, value, expires_at}] ->
          if System.system_time(:second) < expires_at do
            {:ok, value}
          else
            :ets.delete(:l1_cache, key)
            {:error, :expired}
          end
        [] -> {:error, :not_found}
      end
    end
    
    def put(key, value, ttl \\ 300) do
      expires_at = System.system_time(:second) + ttl
      :ets.insert(:l1_cache, {key, value, expires_at})
      :ok
    end
  end
  
  # L2缓存: Redis分布式缓存
  defmodule L2Cache do
    def get(key) do
      case Redix.command(:redix, ["GET", key]) do
        {:ok, nil} -> {:error, :not_found}
        {:ok, value} -> {:ok, :erlang.binary_to_term(value)}
        {:error, _} = error -> error
      end
    end
    
    def put(key, value, ttl \\ 600) do
      serialized = :erlang.term_to_binary(value)
      Redix.command(:redix, ["SETEX", key, ttl, serialized])
    end
    
    def delete(key) do
      Redix.command(:redix, ["DEL", key])
    end
  end
  
  # 缓存策略协调器
  def get(key) do
    case L1Cache.get(key) do
      {:ok, value} -> 
        {:ok, value}
      {:error, _} ->
        case L2Cache.get(key) do
          {:ok, value} ->
            L1Cache.put(key, value, 300) # 回填L1缓存
            {:ok, value}
          {:error, _} = error -> error
        end
    end
  end
  
  def put(key, value, l1_ttl \\ 300, l2_ttl \\ 600) do
    L1Cache.put(key, value, l1_ttl)
    L2Cache.put(key, value, l2_ttl)
  end
  
  def invalidate(key) do
    :ets.delete(:l1_cache, key)
    L2Cache.delete(key)
  end
end

# 查询结果缓存
defmodule RacingGame.QueryCache do
  alias RacingGame.CacheStrategy
  
  def cached_query(query_key, query_fun, ttl \\ 600) do
    case CacheStrategy.get(query_key) do
      {:ok, result} -> result
      {:error, _} ->
        result = query_fun.()
        CacheStrategy.put(query_key, result, 300, ttl)
        result
    end
  end
  
  # 用户信息缓存
  def get_user_cached(user_id) do
    cache_key = "user:#{user_id}"
    
    cached_query(cache_key, fn ->
      RacingGame.CustomerService.UserRepository.get_user(user_id)
    end, 1800) # 30分钟缓存
  end
  
  # 支付统计缓存
  def get_payment_stats_cached(date_range) do
    cache_key = "payment_stats:#{date_range.start_date}:#{date_range.end_date}"
    
    cached_query(cache_key, fn ->
      RacingGame.PaymentSystem.PaymentQueryBuilder.payment_statistics(date_range)
    end, 3600) # 1小时缓存
  end
end
```

## 📈 性能监控和分析

### 实时性能监控
```sql
-- 创建性能监控视图
CREATE OR REPLACE VIEW performance_dashboard AS
SELECT 
  'active_connections' as metric,
  count(*) as value
FROM pg_stat_activity 
WHERE state = 'active'

UNION ALL

SELECT 
  'slow_queries' as metric,
  count(*) as value
FROM pg_stat_activity 
WHERE state = 'active' 
AND now() - query_start > interval '1 second'

UNION ALL

SELECT 
  'database_size' as metric,
  pg_database_size(current_database()) / 1024 / 1024 as value

UNION ALL

SELECT 
  'cache_hit_ratio' as metric,
  ROUND(
    sum(blks_hit) * 100.0 / NULLIF(sum(blks_hit) + sum(blks_read), 0), 2
  ) as value
FROM pg_stat_database;

-- 性能监控函数
CREATE OR REPLACE FUNCTION get_performance_metrics()
RETURNS TABLE(
  metric_name text,
  metric_value numeric,
  status text,
  timestamp timestamptz
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    pd.metric,
    pd.value,
    CASE 
      WHEN pd.metric = 'active_connections' AND pd.value > 150 THEN 'warning'
      WHEN pd.metric = 'slow_queries' AND pd.value > 10 THEN 'critical'
      WHEN pd.metric = 'cache_hit_ratio' AND pd.value < 95 THEN 'warning'
      ELSE 'ok'
    END as status,
    now() as timestamp
  FROM performance_dashboard pd;
END;
$$ LANGUAGE plpgsql;
```

### Elixir 性能监控
```elixir
defmodule RacingGame.PerformanceMonitor do
  use GenServer
  require Logger
  
  @check_interval 30_000 # 30秒检查一次
  
  def start_link(_) do
    GenServer.start_link(__MODULE__, %{}, name: __MODULE__)
  end
  
  def init(state) do
    schedule_check()
    {:ok, state}
  end
  
  def handle_info(:performance_check, state) do
    metrics = collect_metrics()
    analyze_metrics(metrics)
    schedule_check()
    {:noreply, state}
  end
  
  defp collect_metrics do
    %{
      database: collect_database_metrics(),
      application: collect_application_metrics(),
      system: collect_system_metrics()
    }
  end
  
  defp collect_database_metrics do
    case RacingGame.Repo.query("SELECT * FROM get_performance_metrics()") do
      {:ok, result} ->
        Enum.map(result.rows, fn [name, value, status, timestamp] ->
          %{name: name, value: value, status: status, timestamp: timestamp}
        end)
      {:error, _} -> []
    end
  end
  
  defp collect_application_metrics do
    %{
      memory_usage: :erlang.memory(:total) / 1024 / 1024, # MB
      process_count: length(Process.list()),
      message_queue_lengths: get_message_queue_lengths(),
      ets_memory: :ets.info(:l1_cache, :memory) || 0
    }
  end
  
  defp collect_system_metrics do
    %{
      load_average: get_load_average(),
      disk_usage: get_disk_usage(),
      network_connections: get_network_connections()
    }
  end
  
  defp analyze_metrics(metrics) do
    # 数据库性能分析
    Enum.each(metrics.database, fn metric ->
      case metric.status do
        "critical" ->
          Logger.error("Critical database performance issue", %{
            metric: metric.name,
            value: metric.value
          })
        "warning" ->
          Logger.warn("Database performance warning", %{
            metric: metric.name,
            value: metric.value
          })
        _ -> :ok
      end
    end)
    
    # 应用性能分析
    if metrics.application.memory_usage > 1000 do # 1GB
      Logger.warn("High memory usage", %{
        memory_mb: metrics.application.memory_usage
      })
    end
    
    if metrics.application.process_count > 10000 do
      Logger.warn("High process count", %{
        process_count: metrics.application.process_count
      })
    end
  end
  
  defp get_message_queue_lengths do
    Process.list()
    |> Enum.map(fn pid ->
      case Process.info(pid, :message_queue_len) do
        {:message_queue_len, len} -> len
        nil -> 0
      end
    end)
    |> Enum.filter(&(&1 > 100))
    |> length()
  end
  
  defp get_load_average do
    case System.cmd("uptime", []) do
      {output, 0} ->
        output
        |> String.split("load average:")
        |> List.last()
        |> String.trim()
        |> String.split(",")
        |> List.first()
        |> String.trim()
        |> String.to_float()
      _ -> 0.0
    end
  rescue
    _ -> 0.0
  end
  
  defp get_disk_usage do
    case System.cmd("df", ["-h", "/"]) do
      {output, 0} ->
        output
        |> String.split("\n")
        |> Enum.at(1)
        |> String.split()
        |> Enum.at(4)
        |> String.replace("%", "")
        |> String.to_integer()
      _ -> 0
    end
  rescue
    _ -> 0
  end
  
  defp get_network_connections do
    case System.cmd("ss", ["-tn"]) do
      {output, 0} ->
        output
        |> String.split("\n")
        |> Enum.count(fn line -> String.contains?(line, ":4000") end)
      _ -> 0
    end
  rescue
    _ -> 0
  end
  
  defp schedule_check do
    Process.send_after(self(), :performance_check, @check_interval)
  end
end
```

## 🔧 性能优化最佳实践

### 查询优化检查清单
```
✅ 查询优化检查清单:
□ 避免SELECT * 查询
□ 使用适当的WHERE条件
□ 合理使用索引
□ 避免N+1查询问题
□ 使用LIMIT限制结果集
□ 优化JOIN查询
□ 使用预加载(preload)
□ 避免在WHERE中使用函数
□ 使用批量操作替代循环
□ 合理使用事务
```

### 数据库维护任务
```bash
#!/bin/bash
# database_maintenance.sh - 数据库维护脚本

echo "=== 数据库维护任务 ==="

# 1. 更新表统计信息
echo "更新统计信息..."
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "ANALYZE;"

# 2. 重建索引 (如果需要)
echo "检查索引膨胀..."
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "
SELECT schemaname, tablename, indexname, 
       pg_size_pretty(pg_relation_size(indexrelid)) as size
FROM pg_stat_user_indexes 
WHERE pg_relation_size(indexrelid) > 100 * 1024 * 1024
ORDER BY pg_relation_size(indexrelid) DESC;
"

# 3. 清理过期数据
echo "清理过期数据..."
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "
DELETE FROM temp_sessions WHERE created_at < NOW() - INTERVAL '7 days';
DELETE FROM audit_logs WHERE created_at < NOW() - INTERVAL '90 days';
"

# 4. 检查数据库大小
echo "数据库大小统计:"
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "
SELECT 
  schemaname,
  tablename,
  pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
LIMIT 10;
"

echo "✅ 数据库维护完成"
```

---

**文档版本**: v1.0  
**创建日期**: 2024-12-19  
**最后更新**: 2024-12-19  
**维护团队**: Racing Game Development Team
