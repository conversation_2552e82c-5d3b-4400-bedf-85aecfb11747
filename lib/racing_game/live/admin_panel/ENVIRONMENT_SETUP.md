# 环境配置详解

## 📋 概述

本文档详细说明 Racing Game Admin Panel 系统在不同环境下的配置要求和最佳实践，确保开发、测试和生产环境的一致性和稳定性。

## 🏗️ 环境分类

### 环境类型定义
```
开发环境 (Development)  - 本地开发和调试
测试环境 (Testing)      - 自动化测试和集成测试  
预发环境 (Staging)      - 生产前验证环境
生产环境 (Production)   - 正式运行环境
```

## 🔧 开发环境配置

### 1. 本地开发环境

#### 系统要求
```bash
# 操作系统支持
macOS 10.15+ / Ubuntu 18.04+ / Windows 10 (WSL2)

# 硬件要求
CPU: 2核心以上
内存: 4GB以上
磁盘: 20GB可用空间
```

#### 软件安装
```bash
# 安装 Elixir 和 Erlang (使用 asdf)
git clone https://github.com/asdf-vm/asdf.git ~/.asdf
echo '. ~/.asdf/asdf.sh' >> ~/.bashrc
source ~/.bashrc

asdf plugin add erlang
asdf plugin add elixir
asdf plugin add nodejs

# 安装指定版本
asdf install erlang 25.3
asdf install elixir 1.14.5-otp-25
asdf install nodejs 18.16.0

# 设置全局版本
asdf global erlang 25.3
asdf global elixir 1.14.5-otp-25
asdf global nodejs 18.16.0
```

#### 数据库配置
```bash
# PostgreSQL 安装 (macOS)
brew install postgresql@14
brew services start postgresql@14

# PostgreSQL 安装 (Ubuntu)
sudo apt install postgresql-14 postgresql-client-14

# 创建开发数据库
createdb racing_game_dev
createdb racing_game_test

# Redis 安装
brew install redis  # macOS
sudo apt install redis-server  # Ubuntu
```

#### 环境变量配置
```bash
# .env.dev
# 数据库配置
DATABASE_URL=postgresql://localhost:5432/racing_game_dev
TEST_DATABASE_URL=postgresql://localhost:5432/racing_game_test
POOL_SIZE=5

# Redis 配置
REDIS_URL=redis://localhost:6379/0

# 应用配置
SECRET_KEY_BASE=dev_secret_key_base_at_least_64_characters_long_for_security
PHX_HOST=localhost
PORT=4000
MIX_ENV=dev

# 开发工具配置
LIVE_RELOAD=true
DEBUG_ERRORS=true
CODE_RELOADER=true

# 邮件配置 (开发环境使用本地适配器)
MAILER_ADAPTER=local
SMTP_HOST=localhost
SMTP_PORT=1025

# 日志级别
LOG_LEVEL=debug

# 外部服务配置 (开发环境使用模拟)
PAYMENT_GATEWAY_MODE=sandbox
AWS_S3_BUCKET=racing-game-dev
```

### 2. 开发工具配置

#### VS Code 配置
```json
// .vscode/settings.json
{
  "elixirLS.dialyzerEnabled": true,
  "elixirLS.fetchDeps": true,
  "elixirLS.suggestSpecs": true,
  "files.associations": {
    "*.ex": "elixir",
    "*.exs": "elixir",
    "*.eex": "phoenix-heex",
    "*.heex": "phoenix-heex",
    "*.leex": "phoenix-heex"
  },
  "emmet.includeLanguages": {
    "phoenix-heex": "html"
  }
}
```

#### Git 配置
```bash
# .gitignore 补充
# 环境配置文件
.env*
!.env.example

# 开发工具文件
.vscode/
.idea/
*.swp
*.swo

# 构建产物
_build/
deps/
assets/node_modules/
priv/static/

# 日志文件
*.log
logs/

# 临时文件
tmp/
.DS_Store
```

## 🧪 测试环境配置

### 1. 自动化测试配置

#### 测试数据库配置
```elixir
# config/test.exs
import Config

# 测试数据库配置
config :racing_game, RacingGame.Repo,
  username: System.get_env("TEST_DB_USER", "postgres"),
  password: System.get_env("TEST_DB_PASS", "postgres"),
  hostname: System.get_env("TEST_DB_HOST", "localhost"),
  database: "racing_game_test#{System.get_env("MIX_TEST_PARTITION")}",
  pool: Ecto.Adapters.SQL.Sandbox,
  pool_size: 10

# 测试环境特定配置
config :racing_game, RacingGameWeb.Endpoint,
  http: [ip: {127, 0, 0, 1}, port: 4002],
  secret_key_base: "test_secret_key_base",
  server: false

# 禁用日志输出
config :logger, level: :warn

# 快速密码哈希
config :bcrypt_elixir, :log_rounds, 1
```

#### CI/CD 环境变量
```yaml
# .github/workflows/ci.yml 环境变量示例
env:
  MIX_ENV: test
  DATABASE_URL: postgresql://postgres:postgres@localhost:5432/racing_game_test
  REDIS_URL: redis://localhost:6379/1
  SECRET_KEY_BASE: test_secret_key_base_for_ci_environment
```

### 2. 集成测试环境

#### Docker Compose 测试配置
```yaml
# docker-compose.test.yml
version: '3.8'

services:
  test_db:
    image: postgres:14-alpine
    environment:
      POSTGRES_DB: racing_game_test
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5433:5432"
    tmpfs:
      - /var/lib/postgresql/data

  test_redis:
    image: redis:6-alpine
    ports:
      - "6380:6379"
    tmpfs:
      - /data

  test_app:
    build: .
    environment:
      MIX_ENV: test
      DATABASE_URL: *******************************************/racing_game_test
      REDIS_URL: redis://test_redis:6379/0
    depends_on:
      - test_db
      - test_redis
    command: mix test
```

## 🎭 预发环境配置

### 1. 预发环境特点
```bash
# 预发环境配置原则
- 与生产环境配置完全一致
- 使用生产级别的数据量
- 启用所有监控和日志
- 使用真实的第三方服务 (沙盒模式)
```

#### 环境变量配置
```bash
# .env.staging
# 数据库配置 (生产级别)
DATABASE_URL=*********************************************************/racing_game_staging
POOL_SIZE=20

# Redis 配置
REDIS_URL=redis://staging-redis:6379/0

# 应用配置
SECRET_KEY_BASE=staging_secret_key_base_64_characters_minimum_length_required
PHX_HOST=staging.racing-game.com
PORT=4000
MIX_ENV=prod

# SSL 配置
FORCE_SSL=true
SSL_REDIRECT=true

# 邮件配置 (真实 SMTP)
MAILER_ADAPTER=smtp
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_USERNAME=apikey
SMTP_PASSWORD=staging_sendgrid_api_key

# 第三方服务 (沙盒模式)
PAYMENT_GATEWAY_MODE=sandbox
PAYMENT_GATEWAY_API_KEY=sandbox_api_key

# 监控配置
PROMETHEUS_ENABLED=true
GRAFANA_ENABLED=true
LOG_LEVEL=info

# 文件存储
AWS_S3_BUCKET=racing-game-staging
AWS_REGION=us-east-1
```

### 2. 数据库配置优化

#### PostgreSQL 预发环境配置
```sql
-- postgresql.conf 预发环境优化
shared_buffers = 512MB
effective_cache_size = 2GB
maintenance_work_mem = 128MB
checkpoint_completion_target = 0.9
wal_buffers = 32MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200
work_mem = 8MB
min_wal_size = 2GB
max_wal_size = 8GB

# 连接配置
max_connections = 200
shared_preload_libraries = 'pg_stat_statements'

# 日志配置
log_statement = 'mod'
log_min_duration_statement = 1000
log_checkpoints = on
log_connections = on
log_disconnections = on
```

## 🚀 生产环境配置

### 1. 生产环境安全配置

#### 环境变量管理
```bash
# 使用加密的环境变量管理
# 推荐使用 AWS Secrets Manager, HashiCorp Vault 等

# .env.prod (示例，实际应使用密钥管理服务)
# 数据库配置
DATABASE_URL=postgresql://prod_user:${DB_PASSWORD}@prod-db-cluster:5432/racing_game_prod
POOL_SIZE=50

# Redis 配置 (集群模式)
REDIS_URL=redis://prod-redis-cluster:6379/0
REDIS_CLUSTER_ENABLED=true

# 应用配置
SECRET_KEY_BASE=${SECRET_KEY_BASE}
PHX_HOST=racing-game.com
PORT=4000
MIX_ENV=prod

# SSL 强制配置
FORCE_SSL=true
HSTS_ENABLED=true
SSL_REDIRECT=true

# 安全头配置
CSP_ENABLED=true
X_FRAME_OPTIONS=DENY
X_CONTENT_TYPE_OPTIONS=nosniff

# 邮件配置
MAILER_ADAPTER=smtp
SMTP_HOST=${SMTP_HOST}
SMTP_PORT=587
SMTP_USERNAME=${SMTP_USERNAME}
SMTP_PASSWORD=${SMTP_PASSWORD}
SMTP_TLS=true

# 第三方服务 (生产模式)
PAYMENT_GATEWAY_MODE=production
PAYMENT_GATEWAY_API_KEY=${PAYMENT_API_KEY}

# 监控和日志
PROMETHEUS_ENABLED=true
GRAFANA_ENABLED=true
LOG_LEVEL=warn
STRUCTURED_LOGGING=true

# 文件存储
AWS_S3_BUCKET=racing-game-production
AWS_REGION=us-east-1
CDN_ENABLED=true
CDN_URL=https://cdn.racing-game.com

# 缓存配置
CACHE_TTL=3600
CACHE_ENABLED=true

# 性能配置
POOL_TIMEOUT=15000
QUEUE_TARGET=50
QUEUE_INTERVAL=5000
```

### 2. 生产级数据库配置

#### PostgreSQL 生产环境优化
```sql
-- postgresql.conf 生产环境配置
# 内存配置
shared_buffers = 2GB
effective_cache_size = 8GB
maintenance_work_mem = 256MB
work_mem = 16MB

# 检查点配置
checkpoint_completion_target = 0.9
checkpoint_timeout = 15min
max_wal_size = 16GB
min_wal_size = 4GB

# 连接配置
max_connections = 500
superuser_reserved_connections = 3

# 查询优化
default_statistics_target = 100
constraint_exclusion = partition
cursor_tuple_fraction = 0.1

# 并发配置
effective_io_concurrency = 200
max_worker_processes = 8
max_parallel_workers_per_gather = 4
max_parallel_workers = 8

# 日志配置
log_destination = 'csvlog'
logging_collector = on
log_directory = 'pg_log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_rotation_age = 1d
log_rotation_size = 100MB
log_min_duration_statement = 5000
log_checkpoints = on
log_lock_waits = on
log_temp_files = 0

# 监控配置
shared_preload_libraries = 'pg_stat_statements'
track_activity_query_size = 2048
track_functions = all
```

### 3. Redis 生产环境配置

```bash
# redis.conf 生产环境配置
# 内存配置
maxmemory 4gb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000
rdbcompression yes
rdbchecksum yes

# 网络配置
tcp-keepalive 300
timeout 0

# 安全配置
requirepass ${REDIS_PASSWORD}
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command DEBUG ""

# 日志配置
loglevel notice
logfile /var/log/redis/redis-server.log

# 性能配置
tcp-backlog 511
databases 16
```

## 🔍 配置验证

### 1. 配置检查脚本

```bash
#!/bin/bash
# config_check.sh

echo "开始配置验证..."

# 检查必需的环境变量
required_vars=(
    "DATABASE_URL"
    "REDIS_URL" 
    "SECRET_KEY_BASE"
    "PHX_HOST"
)

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "错误: 环境变量 $var 未设置"
        exit 1
    fi
done

# 检查数据库连接
echo "检查数据库连接..."
if ! psql $DATABASE_URL -c "SELECT 1;" > /dev/null 2>&1; then
    echo "错误: 数据库连接失败"
    exit 1
fi

# 检查 Redis 连接
echo "检查 Redis 连接..."
if ! redis-cli -u $REDIS_URL ping > /dev/null 2>&1; then
    echo "错误: Redis 连接失败"
    exit 1
fi

# 检查端口可用性
echo "检查端口 $PORT..."
if netstat -tuln | grep ":$PORT " > /dev/null; then
    echo "警告: 端口 $PORT 已被占用"
fi

echo "配置验证完成!"
```

### 2. 应用配置验证

```elixir
# lib/racing_game/config_validator.ex
defmodule RacingGame.ConfigValidator do
  @moduledoc """
  配置验证模块
  """

  def validate_config do
    with :ok <- validate_database_config(),
         :ok <- validate_redis_config(),
         :ok <- validate_security_config(),
         :ok <- validate_external_services() do
      {:ok, "配置验证通过"}
    else
      {:error, reason} -> {:error, "配置验证失败: #{reason}"}
    end
  end

  defp validate_database_config do
    case Ecto.Adapters.SQL.query(RacingGame.Repo, "SELECT 1") do
      {:ok, _} -> :ok
      {:error, _} -> {:error, "数据库连接失败"}
    end
  end

  defp validate_redis_config do
    case Redix.command(:redix, ["PING"]) do
      {:ok, "PONG"} -> :ok
      _ -> {:error, "Redis 连接失败"}
    end
  end

  defp validate_security_config do
    secret_key_base = Application.get_env(:racing_game, RacingGameWeb.Endpoint)[:secret_key_base]
    
    if byte_size(secret_key_base) >= 64 do
      :ok
    else
      {:error, "SECRET_KEY_BASE 长度不足"}
    end
  end

  defp validate_external_services do
    # 验证邮件服务配置
    # 验证支付网关配置
    # 验证文件存储配置
    :ok
  end
end
```

## 📊 环境监控

### 1. 配置监控指标

```elixir
# lib/racing_game_web/telemetry.ex 配置监控
defmodule RacingGameWeb.Telemetry do
  def metrics do
    [
      # 环境配置监控
      last_value("racing_game.config.database_pool_size"),
      last_value("racing_game.config.redis_connections"),
      
      # 资源使用监控
      summary("vm.memory.total", unit: :byte),
      summary("vm.total_run_queue_lengths.total"),
      
      # 应用性能监控
      summary("phoenix.endpoint.stop.duration",
        unit: {:native, :millisecond}
      )
    ]
  end
end
```

### 2. 配置告警规则

```yaml
# prometheus/alerts.yml
groups:
  - name: racing_game_config
    rules:
      - alert: DatabasePoolExhausted
        expr: racing_game_config_database_pool_size > 45
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "数据库连接池接近耗尽"
          
      - alert: RedisConnectionHigh
        expr: racing_game_config_redis_connections > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Redis 连接数过高"
```

## 🔧 故障排查

### 常见配置问题

1. **环境变量未加载**
```bash
# 检查环境变量
printenv | grep RACING_GAME
source .env.prod
```

2. **数据库连接池耗尽**
```elixir
# 检查连接池状态
DBConnection.status(RacingGame.Repo)

# 调整连接池大小
config :racing_game, RacingGame.Repo,
  pool_size: 20,
  queue_target: 5000,
  queue_interval: 5000
```

3. **内存不足**
```bash
# 监控内存使用
free -h
ps aux --sort=-%mem | head

# 调整 Erlang VM 参数
export ERL_FLAGS="+hms 1024 +hmbs 1024"
```

---

**文档版本**: v1.0  
**创建日期**: 2024-12-19  
**最后更新**: 2024-12-19  
**维护团队**: Racing Game Development Team
