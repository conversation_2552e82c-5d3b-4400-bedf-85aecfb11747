defmodule Teen.SystemSettings.Role do
  @moduledoc """
  角色资源

  管理系统角色，包括角色权限配置
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.SystemSettings,
    extensions: [AshAdmin.Resource]

  postgres do
    table "roles"
    repo <PERSON><PERSON><PERSON><PERSON>.Repo
  end

  admin do
    table_columns [:id, :name, :code, :level, :status, :description, :inserted_at]
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_by_status
    define :list_by_level
    define :get_by_code
  end

  actions do
    defaults [:create, :read, :update, :destroy]

    read :list_by_status do
      argument :status, :integer, allow_nil?: false
      filter expr(status == ^arg(:status))
    end

    read :list_by_level do
      argument :level, :integer, allow_nil?: false
      filter expr(level == ^arg(:level))
    end

    read :get_by_code do
      argument :code, :string, allow_nil?: false
      get? true
      filter expr(code == ^arg(:code))
    end

    update :activate do
      accept []
      change set_attribute(:status, 1)
      change set_attribute(:updated_at, &DateTime.utc_now/0)
    end

    update :deactivate do
      accept []
      change set_attribute(:status, 0)
      change set_attribute(:updated_at, &DateTime.utc_now/0)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :name, :string do
      allow_nil? false
      public? true
      description "角色名称"
      constraints min_length: 2, max_length: 50
    end

    attribute :code, :string do
      allow_nil? false
      public? true
      description "角色代码"
      constraints min_length: 2, max_length: 50
    end

    attribute :level, :integer do
      allow_nil? false
      public? true
      description "角色级别：0=普通，1=高级，2=超级"
      default 0
      constraints min: 0, max: 2
    end

    attribute :status, :integer do
      allow_nil? false
      public? true
      description "状态：0=禁用，1=启用"
      default 1
      constraints min: 0, max: 1
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "角色描述"
      constraints max_length: 500
    end

    attribute :permission_codes, {:array, :string} do
      allow_nil? true
      public? true
      description "权限代码列表"
      default []
    end

    attribute :menu_permissions, {:array, :string} do
      allow_nil? true
      public? true
      description "菜单权限列表"
      default []
    end

    attribute :data_permissions, :map do
      allow_nil? true
      public? true
      description "数据权限配置"
      default %{}
    end

    timestamps()
  end

  relationships do
    has_many :admin_users, Teen.SystemSettings.AdminUser do
      destination_attribute :role_id
    end

    many_to_many :permissions, Teen.SystemSettings.Permission do
      through Teen.SystemSettings.RolePermission
      source_attribute_on_join_resource :role_id
      destination_attribute_on_join_resource :permission_id
    end
  end

  calculations do
    calculate :is_active, :boolean, expr(status == 1)
    calculate :is_super_role, :boolean, expr(level >= 2)
    calculate :admin_count, :integer, expr(count(admin_users, query: [filter: expr(status == 1)]))
  end

  identities do
    identity :unique_name, [:name]
    identity :unique_code, [:code]
  end

  validations do
    validate match(:code, ~r/^[a-zA-Z][a-zA-Z0-9_]*$/) do
      message "角色代码只能包含字母、数字和下划线，且必须以字母开头"
    end
  end
end
