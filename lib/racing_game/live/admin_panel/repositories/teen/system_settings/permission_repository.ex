defmodule RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.PermissionRepository do
  @moduledoc """
  🔐 权限仓储层

  负责权限的数据访问操作，包括：
  - 权限的CRUD操作
  - 权限树结构管理
  - 权限类型管理
  - 权限查询和过滤
  - 权限统计信息
  """

  require Logger
  import Ash.Query

  alias Teen.SystemSettings.Permission

  # 缓存配置
  @cache_ttl 900_000  # 15分钟缓存（权限变化较少）
  @cache_prefix "permission_repo"

  # ==================== 基础 CRUD 操作 ====================

  @doc """
  创建权限

  ## 参数
  - `permission_data` - 权限数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, permission}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_permission(permission_data, options \\ []) do
    Logger.info("🔐 [权限仓储] 创建权限: #{inspect(permission_data[:name])}")

    try do
      result = Permission
      |> Ash.Changeset.for_create(:create, permission_data)
      |> Ash.create()

      case result do
        {:ok, permission} ->
          Logger.info("✅ [权限仓储] 权限创建成功: #{permission.name}")
          {:ok, permission}
        {:error, error} ->
          Logger.error("❌ [权限仓储] 权限创建失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [权限仓储] 创建权限异常: #{inspect(exception)}")
        {:error, :create_permission_exception}
    end
  end

  @doc """
  根据ID获取权限

  ## 参数
  - `permission_id` - 权限ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, permission}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_permission(permission_id, options \\ []) do
    Logger.info("🔐 [权限仓储] 获取权限: #{permission_id}")

    try do
      result = Permission
      |> Ash.get(permission_id)

      case result do
        {:ok, permission} when not is_nil(permission) ->
          Logger.info("✅ [权限仓储] 权限获取成功: #{permission.name}")
          {:ok, permission}
        {:ok, nil} ->
          Logger.warn("⚠️ [权限仓储] 权限不存在: #{permission_id}")
          {:error, :permission_not_found}
        {:error, error} ->
          Logger.error("❌ [权限仓储] 权限获取失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [权限仓储] 获取权限异常: #{inspect(exception)}")
        {:error, :get_permission_exception}
    end
  end

  @doc """
  根据代码获取权限

  ## 参数
  - `permission_code` - 权限代码
  - `options` - 选项参数

  ## 返回
  - `{:ok, permission}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_permission_by_code(permission_code, options \\ []) do
    Logger.info("🔐 [权限仓储] 根据代码获取权限: #{permission_code}")

    try do
      result = Permission
      |> Ash.Query.for_read(:get_by_code, %{code: permission_code})
      |> maybe_load_associations(options)
      |> Ash.read_one()

      case result do
        {:ok, permission} when not is_nil(permission) ->
          Logger.info("✅ [权限仓储] 权限获取成功: #{permission.name}")
          {:ok, permission}
        {:ok, nil} ->
          Logger.warn("⚠️ [权限仓储] 权限不存在: #{permission_code}")
          {:error, :permission_not_found}
        {:error, error} ->
          Logger.error("❌ [权限仓储] 权限获取失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [权限仓储] 根据代码获取权限异常: #{inspect(exception)}")
        {:error, :get_permission_by_code_exception}
    end
  end

  @doc """
  更新权限

  ## 参数
  - `permission_id` - 权限ID
  - `update_data` - 更新数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, permission}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_permission(permission_id, update_data, options \\ []) do
    Logger.info("🔐 [权限仓储] 更新权限: #{permission_id}")

    try do
      with {:ok, permission} <- get_permission(permission_id, options) do
        result = permission
        |> Ash.Changeset.for_update(:update, update_data)
        |> Ash.update()

        case result do
          {:ok, updated_permission} ->
            Logger.info("✅ [权限仓储] 权限更新成功: #{updated_permission.name}")
            {:ok, updated_permission}
          {:error, error} ->
            Logger.error("❌ [权限仓储] 权限更新失败: #{inspect(error)}")
            {:error, error}
        end
      end
    rescue
      exception ->
        Logger.error("💥 [权限仓储] 更新权限异常: #{inspect(exception)}")
        {:error, :update_permission_exception}
    end
  end

  @doc """
  删除权限

  ## 参数
  - `permission_id` - 权限ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, permission}` - 成功
  - `{:error, reason}` - 失败
  """
  def delete_permission(permission_id, options \\ []) do
    Logger.info("🔐 [权限仓储] 删除权限: #{permission_id}")

    try do
      with {:ok, permission} <- get_permission(permission_id, options) do
        result = permission |> Ash.destroy()

        case result do
          :ok ->
            Logger.info("✅ [权限仓储] 权限删除成功: #{permission.name}")
            {:ok, permission}
          {:error, error} ->
            Logger.error("❌ [权限仓储] 权限删除失败: #{inspect(error)}")
            {:error, error}
        end
      end
    rescue
      exception ->
        Logger.error("💥 [权限仓储] 删除权限异常: #{inspect(exception)}")
        {:error, :delete_permission_exception}
    end
  end

  # ==================== 查询操作 ====================

  @doc """
  获取权限列表

  ## 参数
  - `params` - 查询参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, {permissions, total_count}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_permissions(params \\ %{}, options \\ []) do
    Logger.info("🔐 [权限仓储] 获取权限列表: #{inspect(params)}")

    try do
      query = Permission
      |> build_list_query(params)
      |> maybe_load_associations(options)

      # 获取总数
      count_result = query |> Ash.count()

      # 获取数据
      list_result = query |> Ash.read()

      case {count_result, list_result} do
        {{:ok, total_count}, {:ok, permissions}} ->
          Logger.info("✅ [权限仓储] 权限列表获取成功: #{length(permissions)}条记录")
          {:ok, {permissions, total_count}}
        {{:error, error}, _} ->
          Logger.error("❌ [权限仓储] 获取权限总数失败: #{inspect(error)}")
          {:error, error}
        {_, {:error, error}} ->
          Logger.error("❌ [权限仓储] 获取权限列表失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [权限仓储] 获取权限列表异常: #{inspect(exception)}")
        {:error, :list_permissions_exception}
    end
  end

  @doc """
  根据类型获取权限

  ## 参数
  - `permission_type` - 权限类型
  - `options` - 选项参数

  ## 返回
  - `{:ok, permissions}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_permissions_by_type(permission_type, options \\ []) do
    Logger.info("🔐 [权限仓储] 根据类型获取权限: #{permission_type}")

    try do
      result = Permission
      |> Ash.Query.for_read(:list_by_type, %{type: permission_type})
      |> maybe_load_associations(options)
      |> Ash.read()

      case result do
        {:ok, permissions} ->
          Logger.info("✅ [权限仓储] 类型权限获取成功: #{length(permissions)}条记录")
          {:ok, permissions}
        {:error, error} ->
          Logger.error("❌ [权限仓储] 类型权限获取失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [权限仓储] 根据类型获取权限异常: #{inspect(exception)}")
        {:error, :list_by_type_exception}
    end
  end

  @doc """
  根据父权限获取子权限

  ## 参数
  - `parent_id` - 父权限ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, permissions}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_permissions_by_parent(parent_id, options \\ []) do
    Logger.info("🔐 [权限仓储] 根据父权限获取子权限: #{parent_id}")

    try do
      result = Permission
      |> Ash.Query.for_read(:list_by_parent, %{parent_id: parent_id})
      |> maybe_load_associations(options)
      |> Ash.read()

      case result do
        {:ok, permissions} ->
          Logger.info("✅ [权限仓储] 子权限获取成功: #{length(permissions)}条记录")
          {:ok, permissions}
        {:error, error} ->
          Logger.error("❌ [权限仓储] 子权限获取失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [权限仓储] 根据父权限获取子权限异常: #{inspect(exception)}")
        {:error, :list_by_parent_exception}
    end
  end

  @doc """
  根据状态获取权限

  ## 参数
  - `status` - 状态
  - `options` - 选项参数

  ## 返回
  - `{:ok, permissions}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_permissions_by_status(status, options \\ []) do
    Logger.info("🔐 [权限仓储] 根据状态获取权限: #{status}")

    try do
      result = Permission
      |> Ash.Query.for_read(:list_by_status, %{status: status})
      |> maybe_load_associations(options)
      |> Ash.read()

      case result do
        {:ok, permissions} ->
          Logger.info("✅ [权限仓储] 状态权限获取成功: #{length(permissions)}条记录")
          {:ok, permissions}
        {:error, error} ->
          Logger.error("❌ [权限仓储] 状态权限获取失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [权限仓储] 根据状态获取权限异常: #{inspect(exception)}")
        {:error, :list_by_status_exception}
    end
  end

  @doc """
  获取权限树结构

  ## 参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, permission_tree}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_permission_tree(options \\ []) do
    Logger.info("🔐 [权限仓储] 获取权限树结构")

    try do
      # 获取所有启用的权限
      with {:ok, permissions} <- list_permissions_by_status(1, options) do
        permission_tree = build_permission_tree(permissions)
        Logger.info("✅ [权限仓储] 权限树构建成功: #{length(permission_tree)}个根节点")
        {:ok, permission_tree}
      end
    rescue
      exception ->
        Logger.error("💥 [权限仓储] 获取权限树异常: #{inspect(exception)}")
        {:error, :get_permission_tree_exception}
    end
  end

  # ==================== 状态管理操作 ====================

  @doc """
  激活权限

  ## 参数
  - `permission_id` - 权限ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, permission}` - 成功
  - `{:error, reason}` - 失败
  """
  def activate_permission(permission_id, options \\ []) do
    Logger.info("🔐 [权限仓储] 激活权限: #{permission_id}")

    try do
      with {:ok, permission} <- get_permission(permission_id, options) do
        result = permission
        |> Ash.Changeset.for_update(:activate)
        |> Ash.update()

        case result do
          {:ok, updated_permission} ->
            Logger.info("✅ [权限仓储] 权限激活成功: #{updated_permission.name}")
            {:ok, updated_permission}
          {:error, error} ->
            Logger.error("❌ [权限仓储] 权限激活失败: #{inspect(error)}")
            {:error, error}
        end
      end
    rescue
      exception ->
        Logger.error("💥 [权限仓储] 激活权限异常: #{inspect(exception)}")
        {:error, :activate_permission_exception}
    end
  end

  @doc """
  禁用权限

  ## 参数
  - `permission_id` - 权限ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, permission}` - 成功
  - `{:error, reason}` - 失败
  """
  def deactivate_permission(permission_id, options \\ []) do
    Logger.info("🔐 [权限仓储] 禁用权限: #{permission_id}")

    try do
      with {:ok, permission} <- get_permission(permission_id, options) do
        result = permission
        |> Ash.Changeset.for_update(:deactivate)
        |> Ash.update()

        case result do
          {:ok, updated_permission} ->
            Logger.info("✅ [权限仓储] 权限禁用成功: #{updated_permission.name}")
            {:ok, updated_permission}
          {:error, error} ->
            Logger.error("❌ [权限仓储] 权限禁用失败: #{inspect(error)}")
            {:error, error}
        end
      end
    rescue
      exception ->
        Logger.error("💥 [权限仓储] 禁用权限异常: #{inspect(exception)}")
        {:error, :deactivate_permission_exception}
    end
  end

  # ==================== 私有辅助函数 ====================

  # 构建列表查询
  defp build_list_query(query, params) do
    query
    |> maybe_filter_by_name(params)
    |> maybe_filter_by_code(params)
    |> maybe_filter_by_type(params)
    |> maybe_filter_by_status(params)
    |> maybe_filter_by_parent(params)
    |> apply_pagination(params)
    |> apply_sorting(params)
  end

  # 按名称过滤
  defp maybe_filter_by_name(query, %{"name" => name}) when is_binary(name) and name != "" do
    query |> Ash.Query.filter(contains(name, name))
  end
  defp maybe_filter_by_name(query, _), do: query

  # 按代码过滤
  defp maybe_filter_by_code(query, %{"code" => code}) when is_binary(code) and code != "" do
    query |> Ash.Query.filter(contains(code, code))
  end
  defp maybe_filter_by_code(query, _), do: query

  # 按类型过滤
  defp maybe_filter_by_type(query, %{"type" => type}) when is_binary(type) and type != "" do
    query |> Ash.Query.filter(type == type)
  end
  defp maybe_filter_by_type(query, _), do: query

  # 按状态过滤
  defp maybe_filter_by_status(query, %{"status" => status}) when is_integer(status) do
    query |> Ash.Query.filter(status == status)
  end
  defp maybe_filter_by_status(query, _), do: query

  # 按父权限过滤
  defp maybe_filter_by_parent(query, %{"parent_id" => parent_id}) when is_binary(parent_id) and parent_id != "" do
    query |> Ash.Query.filter(parent_id == parent_id)
  end
  defp maybe_filter_by_parent(query, _), do: query

  # 应用分页
  defp apply_pagination(query, params) do
    page = Map.get(params, "page", 1) |> ensure_integer()
    page_size = Map.get(params, "page_size", 20) |> ensure_integer() |> min(100)
    offset = (page - 1) * page_size

    query
    |> Ash.Query.limit(page_size)
    |> Ash.Query.offset(offset)
  end

  # 应用排序
  defp apply_sorting(query, params) do
    sort_by = Map.get(params, "sort_by", "sort_order")
    sort_order = Map.get(params, "sort_order", "asc")

    case {sort_by, sort_order} do
      {"sort_order", "asc"} -> query |> Ash.Query.sort(sort_order: :asc)
      {"sort_order", "desc"} -> query |> Ash.Query.sort(sort_order: :desc)
      {"name", "asc"} -> query |> Ash.Query.sort(name: :asc)
      {"name", "desc"} -> query |> Ash.Query.sort(name: :desc)
      {"inserted_at", "desc"} -> query |> Ash.Query.sort(inserted_at: :desc)
      {"inserted_at", "asc"} -> query |> Ash.Query.sort(inserted_at: :asc)
      _ -> query |> Ash.Query.sort(sort_order: :asc)
    end
  end

  # 构建权限树
  defp build_permission_tree(permissions) do
    # 按父子关系分组
    {root_permissions, child_permissions} = Enum.split_with(permissions, fn p -> is_nil(p.parent_id) end)

    # 为每个根权限构建子树
    Enum.map(root_permissions, fn root ->
      Map.put(root, :children, build_children(root.id, child_permissions))
    end)
    |> Enum.sort_by(& &1.sort_order)
  end

  # 递归构建子权限
  defp build_children(parent_id, all_permissions) do
    children = Enum.filter(all_permissions, fn p -> p.parent_id == parent_id end)

    Enum.map(children, fn child ->
      Map.put(child, :children, build_children(child.id, all_permissions))
    end)
    |> Enum.sort_by(& &1.sort_order)
  end

  # 加载关联数据
  defp maybe_load_associations(query, options) do
    load_associations = Keyword.get(options, :load, [])
    if Enum.empty?(load_associations) do
      query
    else
      query |> Ash.Query.load(load_associations)
    end
  end

  # 确保整数类型
  defp ensure_integer(value) when is_integer(value), do: value
  defp ensure_integer(value) when is_binary(value) do
    case Integer.parse(value) do
      {int, _} -> int
      :error -> 1
    end
  end
  defp ensure_integer(_), do: 1
end
