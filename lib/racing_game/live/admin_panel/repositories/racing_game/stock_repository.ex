defmodule RacingGame.Live.AdminPanel.Repositories.RacingGame.StockRepository do
  @moduledoc """
  股票持仓数据访问仓储

  提供股票持仓相关的数据访问抽象：
  - 股票持仓管理
  - 持仓查询和统计
  - 交易历史记录
  - 用户持仓分析
  """

  require Logger
  alias Ash.Query
  import Ash.Query
  alias RacingGame.{Stock, StockHolding, StockTransaction}
  alias RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder
  require Ash.Query

  # 常量定义
  @cache_ttl 300  # 5分钟缓存
  @default_limit 20
  @max_limit 100

  # ============================================================================
  # 股票基础查询
  # ============================================================================

  @doc """
  根据ID获取股票

  ## 参数
  - `stock_id` - 股票ID
  - `options` - 选项

  ## 返回
  - `{:ok, stock}` - 成功
  - `{:error, :not_found}` - 股票不存在
  - `{:error, reason}` - 其他错误
  """
  def get_stock_by_id(stock_id, options \\ []) do
    Logger.debug("🔍 [股票仓储] 根据ID获取股票: #{stock_id}")

    preload = Keyword.get(options, :preload, [])
    use_cache = Keyword.get(options, :use_cache, true)

    cache_key = build_cache_key("stock", stock_id, preload)

    if use_cache do
      case get_from_cache(cache_key) do
        {:ok, stock} -> {:ok, stock}
        :miss -> fetch_and_cache_stock_by_id(stock_id, preload, cache_key)
      end
    else
      fetch_stock_by_id(stock_id, preload)
    end
  end

  @doc """
  根据股票代码获取股票

  ## 参数
  - `stock_code` - 股票代码
  - `options` - 选项

  ## 返回
  - `{:ok, stock}` - 成功
  - `{:error, :not_found}` - 股票不存在
  - `{:error, reason}` - 其他错误
  """
  def get_stock_by_code(stock_code, options \\ []) do
    Logger.debug("🔍 [股票仓储] 根据代码获取股票: #{stock_code}")

    try do
      query = Stock
      |> Ash.Query.filter(stock_code == stock_code)
      |> maybe_preload(options[:preload])

      case Ash.read_one(query) do
        {:ok, stock} when not is_nil(stock) -> {:ok, stock}
        {:ok, nil} -> {:error, :not_found}
        {:error, error} ->
          Logger.error("❌ [股票仓储] 获取股票失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [股票仓储] 获取股票异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  分页查询股票列表

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, %{stocks: stocks, page_info: page_info}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_stocks_paginated(params \\ %{}) do
    Logger.debug("📋 [股票仓储] 分页查询股票列表")

    try do
      normalized_params = normalize_list_params(params)

      with {:ok, query} <- RacingGameQueryBuilder.build_stock_list_query(normalized_params),
           {:ok, results} <- execute_paginated_query(query, normalized_params) do
        {:ok, %{stocks: results.items, page_info: results.page_info}}
      else
        {:error, reason} -> {:error, reason}
      end
    rescue
      error ->
        Logger.error("❌ [股票仓储] 分页查询异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  获取活跃股票

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, stocks}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_active_stocks(options \\ []) do
    Logger.debug("📈 [股票仓储] 获取活跃股票")

    try do
      query = Stock
      |> Ash.Query.filter(is_active == true)
      |> maybe_preload(options[:preload])
      |> apply_sort(options[:sort] || [market_cap: :desc])
      |> maybe_limit(options[:limit])

      case Ash.read(query) do
        {:ok, stocks} -> {:ok, stocks}
        {:error, error} ->
          Logger.error("❌ [股票仓储] 获取活跃股票失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [股票仓储] 获取活跃股票异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # ============================================================================
  # 股票持仓查询
  # ============================================================================

  @doc """
  获取用户持仓

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - `{:ok, holdings}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_holdings(user_id, options \\ []) do
    Logger.debug("👤 [股票仓储] 获取用户持仓: #{user_id}")

    try do
      query = StockHolding
      |> Ash.Query.filter(user_id == user_id)
      |> maybe_preload(options[:preload])
      |> apply_sort(options[:sort] || [market_value: :desc])
      |> maybe_limit(options[:limit])
      |> maybe_filter_active_only(options[:active_only])

      case Ash.read(query) do
        {:ok, holdings} -> {:ok, holdings}
        {:error, error} ->
          Logger.error("❌ [股票仓储] 获取用户持仓失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [股票仓储] 获取用户持仓异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  获取用户特定股票持仓

  ## 参数
  - `user_id` - 用户ID
  - `stock_id` - 股票ID
  - `options` - 选项

  ## 返回
  - `{:ok, holding}` - 成功
  - `{:error, :not_found}` - 持仓不存在
  - `{:error, reason}` - 其他错误
  """
  def get_user_stock_holding(user_id, stock_id, options \\ []) do
    Logger.debug("👤 [股票仓储] 获取用户股票持仓: #{user_id} - #{stock_id}")

    try do
      query = StockHolding
      |> Ash.Query.filter(user_id == user_id and stock_id == stock_id)
      |> maybe_preload(options[:preload])

      case Ash.read_one(query) do
        {:ok, holding} when not is_nil(holding) -> {:ok, holding}
        {:ok, nil} -> {:error, :not_found}
        {:error, error} ->
          Logger.error("❌ [股票仓储] 获取用户股票持仓失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [股票仓储] 获取用户股票持仓异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  获取股票持有者

  ## 参数
  - `stock_id` - 股票ID
  - `options` - 选项

  ## 返回
  - `{:ok, holders}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_stock_holders(stock_id, options \\ []) do
    Logger.debug("📊 [股票仓储] 获取股票持有者: #{stock_id}")

    try do
      query = StockHolding
      |> Ash.Query.filter(stock_id == stock_id and quantity > 0)
      |> maybe_preload(options[:preload])
      |> apply_sort(options[:sort] || [quantity: :desc])
      |> maybe_limit(options[:limit])

      case Ash.read(query) do
        {:ok, holders} -> {:ok, holders}
        {:error, error} ->
          Logger.error("❌ [股票仓储] 获取股票持有者失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [股票仓储] 获取股票持有者异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # ============================================================================
  # 股票交易管理
  # ============================================================================

  @doc """
  创建股票交易

  ## 参数
  - `transaction_data` - 交易数据

  ## 返回
  - `{:ok, transaction}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_stock_transaction(transaction_data) do
    Logger.info("➕ [股票仓储] 创建股票交易: 用户#{transaction_data[:user_id]} #{transaction_data[:transaction_type]} #{transaction_data[:quantity]}股")

    try do
      case StockTransaction.create(transaction_data) do
        {:ok, transaction} ->
          clear_cache(:stock_transaction)
          Logger.info("✅ [股票仓储] 股票交易创建成功: #{transaction.id}")
          {:ok, transaction}
        {:error, error} ->
          Logger.error("❌ [股票仓储] 创建股票交易失败: #{inspect(error)}")
          {:error, :create_failed}
      end
    rescue
      error ->
        Logger.error("❌ [股票仓储] 创建股票交易异常: #{inspect(error)}")
        {:error, :create_error}
    end
  end

  @doc """
  更新持仓

  ## 参数
  - `user_id` - 用户ID
  - `stock_id` - 股票ID
  - `quantity_change` - 数量变化
  - `options` - 选项

  ## 返回
  - `{:ok, holding}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_holding(user_id, stock_id, quantity_change, options \\ []) do
    Logger.info("🔄 [股票仓储] 更新持仓: 用户#{user_id} 股票#{stock_id} 变化#{quantity_change}")

    with {:ok, current_holding} <- get_or_create_holding(user_id, stock_id),
         {:ok, updated_holding} <- do_update_holding(current_holding, quantity_change, options) do

      clear_cache(:stock_holding, user_id)
      Logger.info("✅ [股票仓储] 持仓更新成功: #{updated_holding.id}")
      {:ok, updated_holding}
    else
      error -> error
    end
  end

  @doc """
  获取用户交易历史

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - `{:ok, transactions}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_transactions(user_id, options \\ []) do
    Logger.debug("👤 [股票仓储] 获取用户交易历史: #{user_id}")

    try do
      query = StockTransaction
      |> Ash.Query.filter(user_id == user_id)
      |> maybe_preload(options[:preload])
      |> apply_sort(options[:sort] || [inserted_at: :desc])
      |> maybe_limit(options[:limit])
      |> maybe_date_range(options[:date_range])
      |> maybe_filter_by_type(options[:transaction_type])

      case Ash.read(query) do
        {:ok, transactions} -> {:ok, transactions}
        {:error, error} ->
          Logger.error("❌ [股票仓储] 获取用户交易历史失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [股票仓储] 获取用户交易历史异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # ============================================================================
  # 股票统计
  # ============================================================================

  @doc """
  获取股票统计信息

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, statistics}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_stock_statistics(options \\ []) do
    Logger.debug("📊 [股票仓储] 获取股票统计")

    try do
      stats = %{
        total_stocks: get_total_stocks(),
        active_stocks: get_active_stocks_count(),
        total_market_cap: get_total_market_cap(),
        total_holdings: get_total_holdings(),
        total_transactions: get_total_transactions(),
        recent_transactions: get_recent_transactions_count(options[:days] || 7)
      }

      {:ok, stats}
    rescue
      error ->
        Logger.error("❌ [股票仓储] 获取统计异常: #{inspect(error)}")
        {:error, :statistics_error}
    end
  end

  @doc """
  获取用户股票统计

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - `{:ok, statistics}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_stock_statistics(user_id, options \\ []) do
    Logger.debug("👤 [股票仓储] 获取用户股票统计: #{user_id}")

    try do
      stats = %{
        total_holdings: get_user_total_holdings(user_id),
        total_market_value: get_user_total_market_value(user_id),
        total_transactions: get_user_total_transactions(user_id),
        portfolio_diversity: get_user_portfolio_diversity(user_id),
        recent_activity: get_user_recent_activity(user_id, options[:days] || 30)
      }

      {:ok, stats}
    rescue
      error ->
        Logger.error("❌ [股票仓储] 获取用户统计异常: #{inspect(error)}")
        {:error, :statistics_error}
    end
  end

  # ============================================================================
  # 缓存管理
  # ============================================================================

  @doc """
  清除股票相关缓存

  ## 参数
  - `cache_type` - 缓存类型
  - `id` - ID（可选）
  """
  def clear_cache(cache_type, id \\ nil) do
    cache_patterns = case {cache_type, id} do
      {:stock, nil} -> ["stock:*"]
      {:stock, stock_id} -> ["stock:#{stock_id}:*"]
      {:stock_holding, user_id} -> ["stock_holding:#{user_id}:*"]
      {:stock_transaction, _} -> ["stock_transaction:*"]
      {:all, _} -> ["stock:*", "stock_holding:*", "stock_transaction:*"]
    end

    Logger.debug("🧹 [股票仓储] 清除缓存: #{inspect(cache_patterns)}")
    Enum.each(cache_patterns, &clear_cache_pattern/1)
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 标准化列表参数
  defp normalize_list_params(params) do
    %{
      page: max(1, params[:page] || 1),
      limit: min(params[:limit] || @default_limit, @max_limit),
      search: String.trim(params[:search] || ""),
      filters: params[:filters] || %{},
      sort: params[:sort] || [market_cap: :desc],
      preload: params[:preload] || []
    }
  end

  # 获取并缓存股票
  defp fetch_and_cache_stock_by_id(stock_id, preload, cache_key) do
    case fetch_stock_by_id(stock_id, preload) do
      {:ok, stock} ->
        cache_put(cache_key, stock, @cache_ttl)
        {:ok, stock}
      error -> error
    end
  end

  # 获取股票
  defp fetch_stock_by_id(stock_id, preload) do
    try do
      case Stock
           |> maybe_preload(preload)
           |> Ash.get(stock_id) do
        {:ok, stock} -> {:ok, stock}
        {:error, %Ash.Error.Query.NotFound{}} -> {:error, :not_found}
        {:error, error} ->
          Logger.error("❌ [股票仓储] 获取股票失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [股票仓储] 获取股票异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # 获取或创建持仓
  defp get_or_create_holding(user_id, stock_id) do
    case get_user_stock_holding(user_id, stock_id) do
      {:ok, holding} -> {:ok, holding}
      {:error, :not_found} -> create_initial_holding(user_id, stock_id)
      error -> error
    end
  end

  # 创建初始持仓
  defp create_initial_holding(user_id, stock_id) do
    holding_data = %{
      user_id: user_id,
      stock_id: stock_id,
      quantity: 0,
      average_cost: Money.new(0, :XAA),
      market_value: Money.new(0, :XAA)
    }

    try do
      case StockHolding.create(holding_data) do
        {:ok, holding} -> {:ok, holding}
        {:error, error} ->
          Logger.error("❌ [股票仓储] 创建初始持仓失败: #{inspect(error)}")
          {:error, :create_failed}
      end
    rescue
      error ->
        Logger.error("❌ [股票仓储] 创建初始持仓异常: #{inspect(error)}")
        {:error, :create_error}
    end
  end

  # 执行更新持仓
  defp do_update_holding(holding, quantity_change, options) do
    new_quantity = holding.quantity + quantity_change

    update_data = %{
      quantity: new_quantity,
      last_updated: DateTime.utc_now()
    }

    # 如果有价格信息，更新平均成本
    update_data = if options[:price] do
      new_average_cost = calculate_average_cost(holding, quantity_change, options[:price])
      Map.put(update_data, :average_cost, new_average_cost)
    else
      update_data
    end

    try do
      case holding
           |> Ash.Changeset.for_update(:update, update_data)
           |> Ash.update() do
        {:ok, updated_holding} -> {:ok, updated_holding}
        {:error, error} ->
          Logger.error("❌ [股票仓储] 更新持仓失败: #{inspect(error)}")
          {:error, :update_failed}
      end
    rescue
      error ->
        Logger.error("❌ [股票仓储] 更新持仓异常: #{inspect(error)}")
        {:error, :update_error}
    end
  end

  # 计算平均成本
  defp calculate_average_cost(holding, quantity_change, price) do
    if quantity_change > 0 do
      # 买入：重新计算平均成本
      total_cost = Money.add(
        Money.multiply(holding.average_cost, holding.quantity),
        Money.multiply(price, quantity_change)
      )
      total_quantity = holding.quantity + quantity_change
      Money.divide(total_cost, total_quantity)
    else
      # 卖出：保持原平均成本
      holding.average_cost
    end
  end

  # 执行分页查询
  defp execute_paginated_query(query, params) do
    offset = (params.page - 1) * params.limit

    case query
         |> maybe_preload(params.preload)
         |> Ash.Query.page(count: true, limit: params.limit, offset: offset)
         |> Ash.read() do
      {:ok, %Ash.Page.Offset{results: items, count: total_count}} ->
        page_info = %{
          total_count: total_count,
          page: params.page,
          limit: params.limit,
          total_pages: ceil(total_count / params.limit)
        }
        {:ok, %{items: items, page_info: page_info}}
      {:error, error} ->
        Logger.error("❌ [股票仓储] 分页查询失败: #{inspect(error)}")
        {:error, :query_failed}
    end
  end

  # 可能预加载关联数据
  defp maybe_preload(query, []), do: query
  defp maybe_preload(query, preload) when is_list(preload) do
    Ash.Query.load(query, preload)
  end

  # 可能限制数量
  defp maybe_limit(query, nil), do: query
  defp maybe_limit(query, limit) when is_integer(limit) and limit > 0 do
    Ash.Query.limit(query, limit)
  end
  defp maybe_limit(query, _), do: query

  # 应用排序
  defp apply_sort(query, sort) when is_list(sort) do
    Ash.Query.sort(query, sort)
  end
  defp apply_sort(query, _), do: query

  # 可能应用日期范围
  defp maybe_date_range(query, nil), do: query
  defp maybe_date_range(query, {start_date, end_date}) do
    query
    |> Ash.Query.filter(inserted_at >= start_date and inserted_at  <= end_date)
  end
  defp maybe_date_range(query, _), do: query

  # 可能按交易类型过滤
  defp maybe_filter_by_type(query, nil), do: query
  defp maybe_filter_by_type(query, transaction_type) do
    Ash.Query.filter(query, transaction_type  == transaction_type)
  end

  # 可能只显示活跃持仓
  defp maybe_filter_active_only(query, nil), do: query
  defp maybe_filter_active_only(query, true) do
    Ash.Query.filter(query, quantity > 0)
  end
  defp maybe_filter_active_only(query, _), do: query

  # 统计函数（简化实现）
  defp get_total_stocks do
    case Stock |> Ash.Query.aggregate(:count, :id) |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_active_stocks_count do
    case Stock
         |> Ash.Query.filter(is_active == true)
         |> Ash.Query.aggregate(:count, :id)
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_total_market_cap do
    # 这里需要根据实际的市值聚合逻辑实现
    Money.new(0, :XAA)
  end

  defp get_total_holdings do
    case StockHolding |> Ash.Query.aggregate(:count, :id) |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_total_transactions do
    case StockTransaction |> Ash.Query.aggregate(:count, :id) |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_recent_transactions_count(days) do
    date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)

    case StockTransaction
         |> Ash.Query.filter(inserted_at >= date_threshold)
         |> Ash.Query.aggregate(:count, :id)
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  # 用户统计函数
  defp get_user_total_holdings(user_id) do
    case StockHolding
         |> Ash.Query.filter(user_id  == user_id and quantity > 0)
         |> Ash.Query.aggregate(:count, :id)
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_user_total_market_value(user_id) do
    # 这里需要根据实际的用户市值聚合逻辑实现
    Money.new(0, :XAA)
  end

  defp get_user_total_transactions(user_id) do
    case StockTransaction
         |> Ash.Query.filter(user_id == user_id)
         |> Ash.Query.aggregate(:count, :id)
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_user_portfolio_diversity(user_id) do
    case StockHolding
         |> Ash.Query.filter(user_id  == user_id and quantity > 0)
         |> Ash.Query.aggregate(:count, :stock_id)
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_user_recent_activity(user_id, days) do
    date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)

    case StockTransaction
         |> Ash.Query.filter(user_id == user_id and inserted_at >= date_threshold)
         |> Ash.Query.aggregate(:count, :id)
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  # 构建缓存键
  defp build_cache_key(prefix, id, extra \\ nil) do
    case extra do
      nil -> "#{prefix}:#{id}"
      extra -> "#{prefix}:#{id}:#{:erlang.phash2(extra)}"
    end
  end

  # 缓存操作（需要实现具体的缓存后端）
  defp get_from_cache(_key), do: :miss
  defp cache_put(_key, _value, _ttl), do: :ok
  defp clear_cache_pattern(_pattern), do: :ok
end
