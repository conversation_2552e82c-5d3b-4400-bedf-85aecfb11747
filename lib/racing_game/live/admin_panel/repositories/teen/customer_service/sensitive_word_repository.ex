defmodule RacingGame.Live.AdminPanel.Repositories.Teen.CustomerService.SensitiveWordRepository do
  @moduledoc """
  敏感词数据访问仓储

  提供敏感词相关的数据访问抽象：
  - 敏感词管理
  - 内容过滤
  - 词库维护
  - 匹配统计
  """

  require Logger
  alias Teen.CustomerService.SensitiveWord
  require Ash.Query

  # 常量定义
  @default_limit 20
  @max_limit 100
  @cache_ttl 1800  # 30分钟缓存（敏感词变化较少）

  # ============================================================================
  # 敏感词管理
  # ============================================================================

  @doc """
  根据ID获取敏感词

  ## 参数
  - `word_id` - 敏感词ID
  - `options` - 选项

  ## 返回
  - `{:ok, word}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_word_by_id(word_id, options \\ []) do
    Logger.debug("🔍 [敏感词仓储] 获取敏感词: #{word_id}")

    cache_key = build_cache_key("word", word_id, options[:preload])

    case get_from_cache(cache_key) do
      :miss ->
        fetch_and_cache_word_by_id(word_id, options[:preload] || [], cache_key)
      {:hit, word} ->
        Logger.debug("💾 [敏感词仓储] 缓存命中: #{word_id}")
        {:ok, word}
    end
  end

  @doc """
  根据词内容获取敏感词

  ## 参数
  - `word_content` - 词内容
  - `options` - 选项

  ## 返回
  - `{:ok, word}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_word_by_content(word_content, options \\ []) do
    Logger.debug("🔍 [敏感词仓储] 根据内容获取敏感词: #{word_content}")

    try do
      case SensitiveWord
           |> maybe_preload(options[:preload] || [])
           |> Ash.Query.filter(word == ^word_content)
           |> Ash.read_one() do
        {:ok, word} when not is_nil(word) -> {:ok, word}
        {:ok, nil} -> {:error, :not_found}
        {:error, error} ->
          Logger.error("❌ [敏感词仓储] 获取敏感词失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [敏感词仓储] 获取敏感词异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  创建敏感词

  ## 参数
  - `word_data` - 敏感词数据

  ## 返回
  - `{:ok, word}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_word(word_data) do
    Logger.info("📝 [敏感词仓储] 创建敏感词: #{word_data[:word]}")

    try do
      case SensitiveWord.create(word_data) do
        {:ok, word} ->
          clear_cache(:word, nil)
          Logger.info("✅ [敏感词仓储] 敏感词创建成功: #{word.id}")
          {:ok, word}
        {:error, error} ->
          Logger.error("❌ [敏感词仓储] 创建敏感词失败: #{inspect(error)}")
          {:error, :create_failed}
      end
    rescue
      error ->
        Logger.error("❌ [敏感词仓储] 创建敏感词异常: #{inspect(error)}")
        {:error, :create_error}
    end
  end

  @doc """
  更新敏感词

  ## 参数
  - `word_id` - 敏感词ID
  - `update_data` - 更新数据

  ## 返回
  - `{:ok, word}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_word(word_id, update_data) do
    Logger.info("📝 [敏感词仓储] 更新敏感词: #{word_id}")

    with {:ok, word} <- get_word_by_id(word_id),
         {:ok, updated_word} <- do_update_word(word, update_data) do

      clear_cache(:word, word_id)
      Logger.info("✅ [敏感词仓储] 敏感词更新成功: #{word_id}")
      {:ok, updated_word}
    else
      error -> error
    end
  end

  @doc """
  删除敏感词

  ## 参数
  - `word_id` - 敏感词ID

  ## 返回
  - `{:ok, word}` - 成功
  - `{:error, reason}` - 失败
  """
  def delete_word(word_id) do
    Logger.info("🗑️ [敏感词仓储] 删除敏感词: #{word_id}")

    with {:ok, word} <- get_word_by_id(word_id),
         {:ok, deleted_word} <- do_delete_word(word) do

      clear_cache(:word, word_id)
      Logger.info("✅ [敏感词仓储] 敏感词删除成功: #{word_id}")
      {:ok, deleted_word}
    else
      error -> error
    end
  end

  @doc """
  启用/禁用敏感词

  ## 参数
  - `word_id` - 敏感词ID
  - `enabled` - 是否启用

  ## 返回
  - `{:ok, word}` - 成功
  - `{:error, reason}` - 失败
  """
  def toggle_word_status(word_id, enabled) do
    status_text = if enabled, do: "启用", else: "禁用"
    Logger.info("🔄 [敏感词仓储] #{status_text}敏感词: #{word_id}")

    update_word(word_id, %{enabled: enabled})
  end

  # ============================================================================
  # 敏感词查询
  # ============================================================================

  @doc """
  分页获取敏感词列表

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, %{items: words, page_info: page_info}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_words_paginated(params \\ %{}) do
    Logger.debug("📋 [敏感词仓储] 分页获取敏感词列表")

    normalized_params = normalize_list_params(params)
    cache_key = build_cache_key("words_list", :paginated, normalized_params)

    case get_from_cache(cache_key) do
      :miss ->
        fetch_and_cache_words_list(normalized_params, cache_key)
      {:hit, result} ->
        Logger.debug("💾 [敏感词仓储] 缓存命中: 敏感词列表")
        {:ok, result}
    end
  end

  @doc """
  获取启用的敏感词列表

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, words}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_enabled_words(options \\ []) do
    Logger.debug("✅ [敏感词仓储] 获取启用的敏感词")

    cache_key = build_cache_key("enabled_words", :list, options)

    case get_from_cache(cache_key) do
      :miss ->
        fetch_and_cache_enabled_words(options, cache_key)
      {:hit, words} ->
        Logger.debug("💾 [敏感词仓储] 缓存命中: 启用敏感词")
        {:ok, words}
    end
  end

  @doc """
  根据类型获取敏感词

  ## 参数
  - `word_type` - 词类型
  - `options` - 选项

  ## 返回
  - `{:ok, words}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_words_by_type(word_type, options \\ []) do
    Logger.debug("🏷️ [敏感词仓储] 根据类型获取敏感词: #{word_type}")

    try do
      query = SensitiveWord
      |> Ash.Query.filter(word_type == ^word_type)
      |> maybe_apply_enabled_filter(options[:enabled])
      |> apply_sort(options[:sort] || [word: :asc])
      |> maybe_limit(options[:limit])

      case Ash.read(query) do
        {:ok, words} -> {:ok, words}
        {:error, error} ->
          Logger.error("❌ [敏感词仓储] 获取类型敏感词失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [敏感词仓储] 获取类型敏感词异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # ============================================================================
  # 内容过滤
  # ============================================================================

  @doc """
  检查文本是否包含敏感词

  ## 参数
  - `text` - 待检查文本
  - `options` - 选项

  ## 返回
  - `{:ok, %{has_sensitive: boolean, matched_words: [word]}}` - 成功
  - `{:error, reason}` - 失败
  """
  def check_text_for_sensitive_words(text, options \\ []) do
    Logger.debug("🔍 [敏感词仓储] 检查文本敏感词")

    with {:ok, enabled_words} <- get_enabled_words(options) do
      matched_words = find_matched_words(text, enabled_words)

      result = %{
        has_sensitive: length(matched_words) > 0,
        matched_words: matched_words
      }

      if result.has_sensitive do
        Logger.warn("⚠️ [敏感词仓储] 发现敏感词: #{inspect(Enum.map(matched_words, & &1.word))}")
      end

      {:ok, result}
    else
      error -> error
    end
  end

  @doc """
  过滤文本中的敏感词

  ## 参数
  - `text` - 待过滤文本
  - `replacement` - 替换字符（默认为 "*"）
  - `options` - 选项

  ## 返回
  - `{:ok, filtered_text}` - 成功
  - `{:error, reason}` - 失败
  """
  def filter_sensitive_words(text, replacement \\ "*", options \\ []) do
    Logger.debug("🔧 [敏感词仓储] 过滤文本敏感词")

    with {:ok, enabled_words} <- get_enabled_words(options) do
      filtered_text = replace_sensitive_words(text, enabled_words, replacement)
      {:ok, filtered_text}
    else
      error -> error
    end
  end

  @doc """
  批量创建敏感词

  ## 参数
  - `words_data` - 敏感词数据列表

  ## 返回
  - `{:ok, results}` - 成功
  - `{:error, reason}` - 失败
  """
  def batch_create_words(words_data) do
    Logger.info("📝 [敏感词仓储] 批量创建敏感词: #{length(words_data)}个")

    results = Enum.map(words_data, fn word_data ->
      case create_word(word_data) do
        {:ok, word} -> {:ok, word}
        {:error, reason} -> {:error, {word_data[:word], reason}}
      end
    end)

    success_count = Enum.count(results, fn {status, _} -> status == :ok end)
    Logger.info("✅ [敏感词仓储] 批量创建完成: #{success_count}/#{length(words_data)}")

    {:ok, results}
  end

  @doc """
  批量删除敏感词

  ## 参数
  - `word_ids` - 敏感词ID列表

  ## 返回
  - `{:ok, results}` - 成功
  - `{:error, reason}` - 失败
  """
  def batch_delete_words(word_ids) do
    Logger.info("🗑️ [敏感词仓储] 批量删除敏感词: #{length(word_ids)}个")

    results = Enum.map(word_ids, fn word_id ->
      case delete_word(word_id) do
        {:ok, word} -> {:ok, word}
        {:error, reason} -> {:error, {word_id, reason}}
      end
    end)

    success_count = Enum.count(results, fn {status, _} -> status == :ok end)
    Logger.info("✅ [敏感词仓储] 批量删除完成: #{success_count}/#{length(word_ids)}")

    {:ok, results}
  end

  # ============================================================================
  # 统计查询
  # ============================================================================

  @doc """
  获取敏感词统计

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, statistics}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_word_statistics(options \\ []) do
    Logger.debug("📊 [敏感词仓储] 获取敏感词统计")

    try do
      stats = %{
        total_words: get_total_words(),
        enabled_words: get_enabled_words_count(),
        disabled_words: get_disabled_words_count(),
        words_by_type: get_words_by_type_count(),
        recent_words: get_recent_words_count(options[:days] || 7)
      }

      {:ok, stats}
    rescue
      error ->
        Logger.error("❌ [敏感词仓储] 获取统计异常: #{inspect(error)}")
        {:error, :statistics_error}
    end
  end

  # ============================================================================
  # 缓存管理
  # ============================================================================

  @doc """
  清除敏感词相关缓存

  ## 参数
  - `cache_type` - 缓存类型
  - `id` - ID（可选）
  """
  def clear_cache(cache_type, id \\ nil) do
    cache_patterns = case {cache_type, id} do
      {:word, nil} -> ["word:*"]
      {:word, word_id} -> ["word:#{word_id}:*"]
      {:enabled_words, _} -> ["enabled_words:*"]
      {:words_by_type, _} -> ["words_by_type:*"]
      {:all, _} -> ["word:*", "enabled_words:*", "words_by_type:*", "words_list:*"]
    end

    Logger.debug("🧹 [敏感词仓储] 清除缓存: #{inspect(cache_patterns)}")
    Enum.each(cache_patterns, &clear_cache_pattern/1)
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 标准化列表参数
  defp normalize_list_params(params) do
    %{
      page: max(1, params[:page] || 1),
      limit: min(params[:limit] || @default_limit, @max_limit),
      search: String.trim(params[:search] || ""),
      filters: params[:filters] || %{},
      sort: params[:sort] || [word: :asc],
      preload: params[:preload] || []
    }
  end

  # 获取并缓存敏感词
  defp fetch_and_cache_word_by_id(word_id, preload, cache_key) do
    case fetch_word_by_id(word_id, preload) do
      {:ok, word} ->
        cache_put(cache_key, word, @cache_ttl)
        {:ok, word}
      error -> error
    end
  end

  # 获取敏感词
  defp fetch_word_by_id(word_id, preload) do
    try do
      case SensitiveWord
           |> maybe_preload(preload)
           |> Ash.get(word_id) do
        {:ok, word} -> {:ok, word}
        {:error, %Ash.Error.Query.NotFound{}} -> {:error, :not_found}
        {:error, error} ->
          Logger.error("❌ [敏感词仓储] 获取敏感词失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [敏感词仓储] 获取敏感词异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # 执行更新敏感词
  defp do_update_word(word, update_data) do
    try do
      case word
           |> Ash.Changeset.for_update(:update, update_data)
           |> Ash.update() do
        {:ok, updated_word} -> {:ok, updated_word}
        {:error, error} ->
          Logger.error("❌ [敏感词仓储] 更新敏感词失败: #{inspect(error)}")
          {:error, :update_failed}
      end
    rescue
      error ->
        Logger.error("❌ [敏感词仓储] 更新敏感词异常: #{inspect(error)}")
        {:error, :update_error}
    end
  end

  # 执行删除敏感词
  defp do_delete_word(word) do
    try do
      case Ash.destroy(word) do
        :ok -> {:ok, word}
        {:error, error} ->
          Logger.error("❌ [敏感词仓储] 删除敏感词失败: #{inspect(error)}")
          {:error, :delete_failed}
      end
    rescue
      error ->
        Logger.error("❌ [敏感词仓储] 删除敏感词异常: #{inspect(error)}")
        {:error, :delete_error}
    end
  end

  # 获取并缓存敏感词列表
  defp fetch_and_cache_words_list(params, cache_key) do
    case fetch_words_list(params) do
      {:ok, result} ->
        cache_put(cache_key, result, @cache_ttl)
        {:ok, result}
      error -> error
    end
  end

  # 获取敏感词列表
  defp fetch_words_list(params) do
    try do
      query = SensitiveWord
      |> apply_word_filters(params.filters)
      |> apply_word_search(params.search)
      |> apply_sort(params.sort)

      execute_paginated_query(query, params)
    rescue
      error ->
        Logger.error("❌ [敏感词仓储] 获取敏感词列表异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # 获取并缓存启用的敏感词
  defp fetch_and_cache_enabled_words(options, cache_key) do
    case fetch_enabled_words(options) do
      {:ok, words} ->
        cache_put(cache_key, words, @cache_ttl)
        {:ok, words}
      error -> error
    end
  end

  # 获取启用的敏感词
  defp fetch_enabled_words(options) do
    try do
      query = SensitiveWord
      |> Ash.Query.filter(enabled == true)
      |> maybe_apply_word_type_filter(options[:word_type])
      |> apply_sort(options[:sort] || [word: :asc])
      |> maybe_limit(options[:limit])

      case Ash.read(query) do
        {:ok, words} -> {:ok, words}
        {:error, error} ->
          Logger.error("❌ [敏感词仓储] 获取启用敏感词失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [敏感词仓储] 获取启用敏感词异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # 查找匹配的敏感词
  defp find_matched_words(text, words) do
    text_lower = String.downcase(text)

    Enum.filter(words, fn word ->
      word_content = String.downcase(word.word)
      String.contains?(text_lower, word_content)
    end)
  end

  # 替换敏感词
  defp replace_sensitive_words(text, words, replacement) do
    Enum.reduce(words, text, fn word, acc_text ->
      replacement_str = String.duplicate(replacement, String.length(word.word))
      String.replace(acc_text, word.word, replacement_str, global: true)
    end)
  end

  # 应用敏感词过滤器
  defp apply_word_filters(query, filters) when map_size(filters) == 0, do: query
  defp apply_word_filters(query, filters) do
    Enum.reduce(filters, query, fn {key, value}, acc_query ->
      apply_word_filter(acc_query, key, value)
    end)
  end

  # 应用单个敏感词过滤器
  defp apply_word_filter(query, :enabled, enabled) when not is_nil(enabled) do
    Ash.Query.filter(query, enabled == ^enabled)
  end
  defp apply_word_filter(query, :word_type, word_type) when not is_nil(word_type) do
    Ash.Query.filter(query, word_type == ^word_type)
  end
  defp apply_word_filter(query, _key, _value), do: query

  # 应用敏感词搜索
  defp apply_word_search(query, search) when search == "" or is_nil(search), do: query
  defp apply_word_search(query, search) do
    search_term = "%#{search}%"
    Ash.Query.filter(query, ilike(word, ^search_term))
  end

  # 执行分页查询
  defp execute_paginated_query(query, params) do
    offset = (params.page - 1) * params.limit

    case query
         |> maybe_preload(params.preload)
         |> Ash.Query.page(count: true, limit: params.limit, offset: offset)
         |> Ash.read() do
      {:ok, %Ash.Page.Offset{results: items, count: total_count}} ->
        page_info = %{
          total_count: total_count,
          page: params.page,
          limit: params.limit,
          total_pages: ceil(total_count / params.limit)
        }
        {:ok, %{items: items, page_info: page_info}}
      {:error, error} ->
        Logger.error("❌ [敏感词仓储] 分页查询失败: #{inspect(error)}")
        {:error, :query_failed}
    end
  end

  # 统计函数
  defp get_total_words do
    case SensitiveWord |> Ash.Query.aggregate(:count, :id) |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_enabled_words_count do
    case SensitiveWord
         |> Ash.Query.filter(enabled == true)
         |> Ash.Query.aggregate(:count, :id)
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_disabled_words_count do
    case SensitiveWord
         |> Ash.Query.filter(enabled == false)
         |> Ash.Query.aggregate(:count, :id)
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_words_by_type_count do
    # 这里需要根据实际需求实现类型分组统计
    %{}
  end

  defp get_recent_words_count(days) do
    date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)

    case SensitiveWord
         |> Ash.Query.filter(inserted_at >= ^date_threshold)
         |> Ash.Query.aggregate(:count, :id)
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  # 可能预加载关联数据
  defp maybe_preload(query, []), do: query
  defp maybe_preload(query, preload) when is_list(preload) do
    Ash.Query.load(query, preload)
  end

  # 可能限制数量
  defp maybe_limit(query, nil), do: query
  defp maybe_limit(query, limit) when is_integer(limit) and limit > 0 do
    Ash.Query.limit(query, limit)
  end
  defp maybe_limit(query, _), do: query

  # 应用排序
  defp apply_sort(query, sort) when is_list(sort) do
    Ash.Query.sort(query, sort)
  end
  defp apply_sort(query, _), do: query

  # 可能应用启用状态过滤
  defp maybe_apply_enabled_filter(query, nil), do: query
  defp maybe_apply_enabled_filter(query, enabled) do
    Ash.Query.filter(query, enabled == ^enabled)
  end

  # 可能应用词类型过滤
  defp maybe_apply_word_type_filter(query, nil), do: query
  defp maybe_apply_word_type_filter(query, word_type) do
    Ash.Query.filter(query, word_type == ^word_type)
  end

  # 构建缓存键
  defp build_cache_key(prefix, id, extra \\ nil) do
    case extra do
      nil -> "#{prefix}:#{id}"
      extra -> "#{prefix}:#{id}:#{:erlang.phash2(extra)}"
    end
  end

  # 缓存操作（需要实现具体的缓存后端）
  defp get_from_cache(_key), do: :miss
  defp cache_put(_key, _value, _ttl), do: :ok
  defp clear_cache_pattern(_pattern), do: :ok
end
