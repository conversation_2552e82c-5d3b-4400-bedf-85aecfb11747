defmodule Teen.BanSystem.UserBan do
  @moduledoc """
  用户封号资源

  管理用户封号状态，包括封号原因、时长、解封等功能
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.BanSystem,
    extensions: [AshAdmin.Resource]

  postgres do
    table "user_bans"
    repo Cypridina.Repo

    identity_wheres_to_sql [
      unique_active_user_ban: "status = 1"
    ]
  end

  admin do
    table_columns [:id, :user_id, :ban_type, :reason, :status, :banned_at, :expires_at]
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_bans
    define :list_by_user
    define :list_by_ban_type
    define :ban_user
    define :unban_user
  end

  actions do
    defaults [:create, :read, :update, :destroy]

    read :list_active_bans do
      filter expr(status == 1 and (is_nil(expires_at) or expires_at > ^DateTime.utc_now()))
    end

    read :list_by_user do
      argument :user_id, :uuid, allow_nil?: false
      filter expr(user_id == ^arg(:user_id))
    end

    read :list_by_ban_type do
      argument :ban_type, :integer, allow_nil?: false
      filter expr(ban_type == ^arg(:ban_type))
    end

    create :ban_user do
      argument :user_id, :uuid, allow_nil?: false
      argument :ban_type, :integer, allow_nil?: false
      argument :reason, :string, allow_nil?: false
      argument :duration_hours, :integer, allow_nil?: true
      argument :operator_id, :uuid, allow_nil?: false

      change set_attribute(:user_id, arg(:user_id))
      change set_attribute(:ban_type, arg(:ban_type))
      change set_attribute(:reason, arg(:reason))
      change set_attribute(:operator_id, arg(:operator_id))
      change set_attribute(:status, 1)
      change set_attribute(:banned_at, &DateTime.utc_now/0)

      change fn changeset, _context ->
        case Ash.Changeset.get_argument(changeset, :duration_hours) do
          nil -> changeset
          hours when is_integer(hours) ->
            expires_at = DateTime.add(DateTime.utc_now(), hours * 3600, :second)
            Ash.Changeset.change_attribute(changeset, :expires_at, expires_at)
          _ -> changeset
        end
      end
    end

    update :unban_user do
      argument :operator_id, :uuid, allow_nil?: false
      argument :unban_reason, :string, allow_nil?: true

      change set_attribute(:status, 0)
      change set_attribute(:unbanned_at, &DateTime.utc_now/0)
      change set_attribute(:unban_operator_id, arg(:operator_id))
      change set_attribute(:unban_reason, arg(:unban_reason))
    end

    update :extend_ban do
      argument :additional_hours, :integer, allow_nil?: false
      argument :operator_id, :uuid, allow_nil?: false

      change fn changeset, _context ->
        case Ash.Changeset.get_data(changeset) do
          %{expires_at: nil} -> changeset
          %{expires_at: current_expires} ->
            additional_hours = Ash.Changeset.get_argument(changeset, :additional_hours)
            new_expires = DateTime.add(current_expires, additional_hours * 3600, :second)
            Ash.Changeset.change_attribute(changeset, :expires_at, new_expires)
          _ -> changeset
        end
      end
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :user_id, :uuid do
      allow_nil? false
      public? true
      description "被封用户ID"
    end

    attribute :ban_type, :integer do
      allow_nil? false
      public? true
      description "封号类型：1-账号封禁，2-设备封禁，3-IP封禁"
      constraints min: 1, max: 3
    end

    attribute :reason, :string do
      allow_nil? false
      public? true
      description "封号原因"
      constraints max_length: 1000
    end

    attribute :status, :integer do
      allow_nil? false
      public? true
      description "状态：0-已解封，1-封禁中"
      default 1
      constraints min: 0, max: 1
    end

    attribute :cash_amount, :decimal do
      allow_nil? false
      public? true
      description "封号时现金数"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :bank_amount, :decimal do
      allow_nil? false
      public? true
      description "封号时银行数"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :ip_address, :string do
      allow_nil? true
      public? true
      description "封号时IP地址"
      constraints max_length: 45
    end

    attribute :operator_id, :uuid do
      allow_nil? false
      public? true
      description "操作员ID"
    end

    attribute :banned_at, :utc_datetime do
      allow_nil? false
      public? true
      description "封号时间"
    end

    attribute :expires_at, :utc_datetime do
      allow_nil? true
      public? true
      description "解封时间（null表示永久封禁）"
    end

    attribute :unbanned_at, :utc_datetime do
      allow_nil? true
      public? true
      description "实际解封时间"
    end

    attribute :unban_operator_id, :uuid do
      allow_nil? true
      public? true
      description "解封操作员ID"
    end

    attribute :unban_reason, :string do
      allow_nil? true
      public? true
      description "解封原因"
      constraints max_length: 1000
    end

    timestamps()
  end

  relationships do
    belongs_to :user, Cypridina.Accounts.User do
      public? true
      source_attribute :user_id
      destination_attribute :id
    end

    belongs_to :operator, Cypridina.Accounts.User do
      public? true
      source_attribute :operator_id
      destination_attribute :id
    end

    belongs_to :unban_operator, Cypridina.Accounts.User do
      public? true
      source_attribute :unban_operator_id
      destination_attribute :id
    end
  end

  identities do
    identity :unique_active_user_ban, [:user_id, :status], where: expr(status == 1)
  end
end
