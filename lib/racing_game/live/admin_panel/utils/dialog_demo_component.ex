defmodule RacingGame.Utils.DialogDemoComponent do
  @moduledoc """
  动态对话框演示组件

  展示如何使用 DynamicDialog 和 DialogHelper 组件。
  这个组件可以作为参考示例，展示各种对话框的使用方法。
  """

  use Phoenix.LiveComponent
  import RacingGame.Utils.DynamicDialog
  import RacingGame.Utils.DialogHelper
  alias CypridinaWeb.Components.AdminButtonGroup

  def mount(socket) do
    # 初始化多个对话框
    socket = init_dialogs(socket, [
      :delete_confirm,
      :save_confirm,
      :info_dialog,
      :warning_dialog,
      :error_dialog,
      :success_dialog,
      :custom_dialog
    ])

    socket = assign(socket, :demo_data, %{
      selected_id: nil,
      operation_result: nil
    })

    {:ok, socket}
  end

  def handle_event("show_delete_dialog", %{"id" => id}, socket) do
    socket = show_confirm_dialog(socket, :delete_confirm,
      title: "确认删除记录",
      message: "您确定要删除ID为 #{id} 的记录吗？此操作不可撤销，请谨慎操作。",
      confirm_action: "confirm_delete",
      confirm_data: %{"id" => id},
      danger: true
    )
    {:noreply, socket}
  end

  def handle_event("show_save_dialog", _params, socket) do
    socket = show_preset_dialog(socket, :save_confirm, :save_confirm,
      message: "您确定要保存当前的所有更改吗？保存后将无法撤销。",
      confirm_action: "confirm_save"
    )
    {:noreply, socket}
  end

  def handle_event("show_info_dialog", _params, socket) do
    socket = show_info_dialog(socket, :info_dialog,
      title: "系统信息",
      message: "这是一个信息提示对话框。用于向用户展示重要信息或操作结果。",
      confirm_action: "hide_dialog",
      confirm_data: %{"dialog" => "info_dialog"}
    )
    {:noreply, socket}
  end

  def handle_event("show_warning_dialog", _params, socket) do
    socket = show_warning_dialog(socket, :warning_dialog,
      title: "警告提示",
      message: "检测到系统配置可能存在问题，建议您检查相关设置。继续操作可能会影响系统稳定性。",
      confirm_text: "继续",
      confirm_action: "continue_operation",
      cancel_text: "取消"
    )
    {:noreply, socket}
  end

  def handle_event("show_error_dialog", _params, socket) do
    socket = show_error_dialog(socket, :error_dialog,
      title: "操作失败",
      message: "抱歉，操作执行失败。错误代码：E001。请检查网络连接或联系系统管理员。",
      confirm_action: "hide_dialog",
      confirm_data: %{"dialog" => "error_dialog"}
    )
    {:noreply, socket}
  end

  def handle_event("show_success_dialog", _params, socket) do
    socket = show_success_dialog(socket, :success_dialog,
      title: "操作成功",
      message: "恭喜！您的操作已成功完成。所有更改已保存并生效。",
      confirm_action: "hide_dialog",
      confirm_data: %{"dialog" => "success_dialog"}
    )
    {:noreply, socket}
  end

  def handle_event("show_custom_dialog", _params, socket) do
    socket = show_custom_dialog(socket, :custom_dialog,
      title: "自定义对话框",
      message: "这是一个自定义样式的对话框，您可以根据需要调整大小、颜色和图标。",
      size: "lg",
      icon: "fas fa-cog",
      confirm_text: "应用设置",
      confirm_action: "apply_settings"
    )
    {:noreply, socket}
  end

  # 确认操作处理
  def handle_event("confirm_delete", %{"id" => id}, socket) do
    # 模拟删除操作
    socket =
      socket
      |> hide_dialog(:delete_confirm)
      |> assign(:demo_data, %{socket.assigns.demo_data | operation_result: "已删除记录 ID: #{id}"})
      |> show_success_dialog(:success_dialog,
          title: "删除成功",
          message: "记录 ID: #{id} 已成功删除！",
          confirm_action: "hide_dialog",
          confirm_data: %{"dialog" => "success_dialog"}
        )
    {:noreply, socket}
  end

  def handle_event("confirm_save", _params, socket) do
    # 模拟保存操作
    socket =
      socket
      |> hide_dialog(:save_confirm)
      |> assign(:demo_data, %{socket.assigns.demo_data | operation_result: "数据已保存"})
      |> show_success_dialog(:success_dialog,
          title: "保存成功",
          message: "所有更改已成功保存！",
          confirm_action: "hide_dialog",
          confirm_data: %{"dialog" => "success_dialog"}
        )
    {:noreply, socket}
  end

  def handle_event("continue_operation", _params, socket) do
    socket =
      socket
      |> hide_dialog(:warning_dialog)
      |> assign(:demo_data, %{socket.assigns.demo_data | operation_result: "继续执行操作"})
    {:noreply, socket}
  end

  def handle_event("apply_settings", _params, socket) do
    socket =
      socket
      |> hide_dialog(:custom_dialog)
      |> assign(:demo_data, %{socket.assigns.demo_data | operation_result: "设置已应用"})
    {:noreply, socket}
  end

  # 通用隐藏对话框处理
  def handle_event("hide_dialog", %{"dialog" => dialog_name}, socket) do
    dialog_atom = String.to_existing_atom(dialog_name)
    socket = hide_dialog(socket, dialog_atom)
    {:noreply, socket}
  end

  def handle_event("hide_dialog", _params, socket) do
    # 隐藏所有对话框
    socket =
      socket
      |> hide_dialog(:delete_confirm)
      |> hide_dialog(:save_confirm)
      |> hide_dialog(:info_dialog)
      |> hide_dialog(:warning_dialog)
      |> hide_dialog(:error_dialog)
      |> hide_dialog(:success_dialog)
      |> hide_dialog(:custom_dialog)
    {:noreply, socket}
  end

  def render(assigns) do
    ~H"""
    <div class="p-8 bg-gradient-to-br from-white to-gray-50 rounded-2xl shadow-xl border border-gray-200">
      <div class="text-center mb-8">
        <h2 class="text-3xl font-bold text-gray-900 mb-2">美化对话框演示</h2>
        <p class="text-gray-600">体验全新的对话框设计，支持多种类型和美化样式</p>
      </div>

      <!-- 操作结果显示 -->
      <%= if @demo_data.operation_result do %>
        <div class="mb-8 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl shadow-sm">
          <div class="flex items-center">
            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-4">
              <i class="fas fa-info-circle text-blue-600"></i>
            </div>
            <div>
              <h4 class="text-blue-900 font-semibold">操作结果</h4>
              <p class="text-blue-800"><%= @demo_data.operation_result %></p>
            </div>
          </div>
        </div>
      <% end %>

      <!-- 美化的演示按钮 -->
      <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-10">
        <AdminButtonGroup.danger_button
          text="删除确认"
          action="show_delete_dialog"
          target={@myself}
          icon="fas fa-trash"
          id="12345"
          class="w-full justify-center shadow-lg hover:shadow-xl transition-all duration-300"
        />

        <AdminButtonGroup.primary_button
          text="保存确认"
          action="show_save_dialog"
          target={@myself}
          icon="fas fa-save"
          class="w-full justify-center shadow-lg hover:shadow-xl transition-all duration-300"
        />

        <AdminButtonGroup.info_button
          text="信息提示"
          action="show_info_dialog"
          target={@myself}
          icon="fas fa-info-circle"
          class="w-full justify-center shadow-lg hover:shadow-xl transition-all duration-300"
        />

        <AdminButtonGroup.warning_button
          text="警告提示"
          action="show_warning_dialog"
          target={@myself}
          icon="fas fa-exclamation-triangle"
          class="w-full justify-center shadow-lg hover:shadow-xl transition-all duration-300"
        />

        <AdminButtonGroup.danger_button
          text="错误提示"
          action="show_error_dialog"
          target={@myself}
          icon="fas fa-times-circle"
          class="w-full justify-center shadow-lg hover:shadow-xl transition-all duration-300"
        />

        <AdminButtonGroup.success_button
          text="成功提示"
          action="show_success_dialog"
          target={@myself}
          icon="fas fa-check-circle"
          class="w-full justify-center shadow-lg hover:shadow-xl transition-all duration-300"
        />

        <AdminButtonGroup.add_button
          text="自定义对话框"
          action="show_custom_dialog"
          target={@myself}
          icon="fas fa-cog"
          class="w-full justify-center shadow-lg hover:shadow-xl transition-all duration-300"
        />

        <AdminButtonGroup.secondary_button
          text="关闭所有"
          action="hide_dialog"
          target={@myself}
          icon="fas fa-times"
          class="w-full justify-center shadow-lg hover:shadow-xl transition-all duration-300"
        />
      </div>

      <!-- 美化的使用说明 -->
      <div class="bg-gradient-to-r from-gray-50 to-gray-100 p-6 rounded-xl border border-gray-200 shadow-sm">
        <div class="flex items-center mb-4">
          <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
            <i class="fas fa-lightbulb text-blue-600"></i>
          </div>
          <h3 class="text-xl font-bold text-gray-900">使用说明</h3>
        </div>
        <div class="grid md:grid-cols-2 gap-4">
          <div class="space-y-3">
            <div class="flex items-start">
              <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
              <p class="text-gray-700">点击上方按钮测试不同类型的对话框</p>
            </div>
            <div class="flex items-start">
              <div class="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
              <p class="text-gray-700">支持确认、警告、信息、错误、成功等多种类型</p>
            </div>
            <div class="flex items-start">
              <div class="w-2 h-2 bg-purple-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
              <p class="text-gray-700">可以自定义标题、内容、按钮文字和图标</p>
            </div>
          </div>
          <div class="space-y-3">
            <div class="flex items-start">
              <div class="w-2 h-2 bg-yellow-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
              <p class="text-gray-700">支持键盘 ESC 键和点击遮罩关闭</p>
            </div>
            <div class="flex items-start">
              <div class="w-2 h-2 bg-red-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
              <p class="text-gray-700">提供预设配置和完全自定义两种使用方式</p>
            </div>
            <div class="flex items-start">
              <div class="w-2 h-2 bg-indigo-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
              <p class="text-gray-700">对话框尺寸为管理系统容器的一半，居中显示</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 所有对话框组件 -->
      <.dynamic_dialog
        id="delete-confirm-dialog"
        type={get_dialog_state(@socket, :delete_confirm).type}
        title={get_dialog_state(@socket, :delete_confirm).title}
        message={get_dialog_state(@socket, :delete_confirm).message}
        show={get_dialog_state(@socket, :delete_confirm).show}
        confirm_text={get_dialog_state(@socket, :delete_confirm).confirm_text}
        cancel_text={get_dialog_state(@socket, :delete_confirm).cancel_text}
        confirm_action={get_dialog_state(@socket, :delete_confirm).confirm_action}
        cancel_action="hide_dialog"
        target={@myself}
        danger={get_dialog_state(@socket, :delete_confirm).danger}
        size={get_dialog_state(@socket, :delete_confirm).size}
        closable={get_dialog_state(@socket, :delete_confirm).closable}
      />

      <.dynamic_dialog
        id="save-confirm-dialog"
        type={get_dialog_state(@socket, :save_confirm).type}
        title={get_dialog_state(@socket, :save_confirm).title}
        message={get_dialog_state(@socket, :save_confirm).message}
        show={get_dialog_state(@socket, :save_confirm).show}
        confirm_text={get_dialog_state(@socket, :save_confirm).confirm_text}
        cancel_text={get_dialog_state(@socket, :save_confirm).cancel_text}
        confirm_action={get_dialog_state(@socket, :save_confirm).confirm_action}
        cancel_action="hide_dialog"
        target={@myself}
        danger={get_dialog_state(@socket, :save_confirm).danger}
        size={get_dialog_state(@socket, :save_confirm).size}
        closable={get_dialog_state(@socket, :save_confirm).closable}
      />

      <.dynamic_dialog
        id="info-dialog"
        type={get_dialog_state(@socket, :info_dialog).type}
        title={get_dialog_state(@socket, :info_dialog).title}
        message={get_dialog_state(@socket, :info_dialog).message}
        show={get_dialog_state(@socket, :info_dialog).show}
        confirm_text={get_dialog_state(@socket, :info_dialog).confirm_text}
        cancel_text={get_dialog_state(@socket, :info_dialog).cancel_text}
        confirm_action={get_dialog_state(@socket, :info_dialog).confirm_action}
        cancel_action={get_dialog_state(@socket, :info_dialog).cancel_action}
        target={@myself}
        danger={get_dialog_state(@socket, :info_dialog).danger}
        size={get_dialog_state(@socket, :info_dialog).size}
        closable={get_dialog_state(@socket, :info_dialog).closable}
      />

      <.dynamic_dialog
        id="warning-dialog"
        type={get_dialog_state(@socket, :warning_dialog).type}
        title={get_dialog_state(@socket, :warning_dialog).title}
        message={get_dialog_state(@socket, :warning_dialog).message}
        show={get_dialog_state(@socket, :warning_dialog).show}
        confirm_text={get_dialog_state(@socket, :warning_dialog).confirm_text}
        cancel_text={get_dialog_state(@socket, :warning_dialog).cancel_text}
        confirm_action={get_dialog_state(@socket, :warning_dialog).confirm_action}
        cancel_action="hide_dialog"
        target={@myself}
        danger={get_dialog_state(@socket, :warning_dialog).danger}
        size={get_dialog_state(@socket, :warning_dialog).size}
        closable={get_dialog_state(@socket, :warning_dialog).closable}
      />

      <.dynamic_dialog
        id="error-dialog"
        type={get_dialog_state(@socket, :error_dialog).type}
        title={get_dialog_state(@socket, :error_dialog).title}
        message={get_dialog_state(@socket, :error_dialog).message}
        show={get_dialog_state(@socket, :error_dialog).show}
        confirm_text={get_dialog_state(@socket, :error_dialog).confirm_text}
        cancel_text={get_dialog_state(@socket, :error_dialog).cancel_text}
        confirm_action={get_dialog_state(@socket, :error_dialog).confirm_action}
        cancel_action={get_dialog_state(@socket, :error_dialog).cancel_action}
        target={@myself}
        danger={get_dialog_state(@socket, :error_dialog).danger}
        size={get_dialog_state(@socket, :error_dialog).size}
        closable={get_dialog_state(@socket, :error_dialog).closable}
      />

      <.dynamic_dialog
        id="success-dialog"
        type={get_dialog_state(@socket, :success_dialog).type}
        title={get_dialog_state(@socket, :success_dialog).title}
        message={get_dialog_state(@socket, :success_dialog).message}
        show={get_dialog_state(@socket, :success_dialog).show}
        confirm_text={get_dialog_state(@socket, :success_dialog).confirm_text}
        cancel_text={get_dialog_state(@socket, :success_dialog).cancel_text}
        confirm_action={get_dialog_state(@socket, :success_dialog).confirm_action}
        cancel_action={get_dialog_state(@socket, :success_dialog).cancel_action}
        target={@myself}
        danger={get_dialog_state(@socket, :success_dialog).danger}
        size={get_dialog_state(@socket, :success_dialog).size}
        closable={get_dialog_state(@socket, :success_dialog).closable}
      />

      <.dynamic_dialog
        id="custom-dialog"
        type={get_dialog_state(@socket, :custom_dialog).type}
        title={get_dialog_state(@socket, :custom_dialog).title}
        message={get_dialog_state(@socket, :custom_dialog).message}
        show={get_dialog_state(@socket, :custom_dialog).show}
        confirm_text={get_dialog_state(@socket, :custom_dialog).confirm_text}
        cancel_text={get_dialog_state(@socket, :custom_dialog).cancel_text}
        confirm_action={get_dialog_state(@socket, :custom_dialog).confirm_action}
        cancel_action="hide_dialog"
        target={@myself}
        danger={get_dialog_state(@socket, :custom_dialog).danger}
        size={get_dialog_state(@socket, :custom_dialog).size}
        closable={get_dialog_state(@socket, :custom_dialog).closable}
        icon="fas fa-cog"
      />
    </div>
    """
  end
end
