defmodule RacingGame.Live.AdminPanel.UserManagementComponent do
  @moduledoc """
  用户管理组件 - 仅限管理员使用

  提供用户的创建、编辑、搜索和管理功能。
  支持权限管理、代理关系管理和积分调整。

  ## 架构说明

  本组件采用分层架构设计：
  - 表现层：处理用户交互和数据展示
  - 业务逻辑层：通过服务层处理业务逻辑
  - 数据访问层：通过仓储层访问数据
  """
  use CypridinaWeb, :live_component

  # 服务层依赖
  alias RacingGame.Live.AdminPanel.Services.Domain.UserService
  alias RacingGame.Live.AdminPanel.Services.Application.AdminService
  alias RacingGame.Live.AdminPanel.Config.Business.BusinessRules

  # 工具层依赖
  alias RacingGame.Live.AdminPanel.Utils.Validators.InputValidator
  alias CypridinaWeb.AuthHelper
  alias Cypridina.Utils.TimeHelper
  alias CypridinaWeb.Components.{PointsHistoryComponent, AdminButtonGroup}

  require Logger

  # 常量定义 - 从业务规则配置获取
  @default_per_page 20
  @max_per_page 100

  # UI相关常量
  @permission_level_ui %{
    0 => %{text: "普通权限", class: "badge-ghost"},
    1 => %{text: "管理员", class: "badge-warning"},
    2 => %{text: "超级管理员", class: "badge-error"}
  }

  # 表单默认值
  @default_create_form %{
    "username" => "",
    "password" => "",
    "password_confirmation" => "",
    "permission_level" => "0",
    "user_type" => "normal",
    "initial_points" => "0"
  }

  @default_edit_form %{
    "permission_level" => "0",
    "user_type" => "normal",
    "points_adjustment" => "0",
    "adjustment_reason" => ""
  }

  def update(assigns, socket) do
    socket =
      socket
      |> assign(assigns)
      |> assign_defaults()
      |> load_users_data()

    {:ok, socket}
  end

  # ============================================================================
  # 事件处理函数
  # ============================================================================

  def handle_event("search", %{"search" => %{"query" => query}}, socket) do
    Logger.info("🔍 [用户管理] 搜索用户: #{query}")
    socket = perform_search(socket, String.trim(query))
    {:noreply, socket}
  end

  def handle_event("page_change", %{"page" => page_str}, socket) do
    case Integer.parse(page_str) do
      {page, ""} when page > 0 ->
        Logger.info("📄 [用户管理] 切换到第 #{page} 页")
        socket = change_page(socket, page)
        {:noreply, socket}
      _ ->
        Logger.warning("⚠️ [用户管理] 无效的页码: #{page_str}")
        {:noreply, socket}
    end
  end

  def handle_event("refresh", _params, socket) do
    Logger.info("🔄 [用户管理] 刷新用户数据")
    socket = load_users_data(socket)
    {:noreply, socket}
  end

  # ============================================================================
  # 私有辅助函数
  # ============================================================================

  defp assign_defaults(socket) do
    socket
    |> assign(:search_query, "")
    |> assign(:page, 1)
    |> assign(:per_page, @default_per_page)
    |> assign(:page_info, nil)
    |> assign(:show_create_modal, false)
    |> assign(:show_edit_modal, false)
    |> assign(:selected_user, nil)
    |> assign(:create_form, @default_create_form)
    |> assign(:edit_form, @default_edit_form)
    |> assign(:loading, false)
  end

  defp perform_search(socket, query) do
    socket
    |> assign(:search_query, query)
    |> assign(:page, 1)
    |> load_users_data()
  end

  defp change_page(socket, page) do
    socket
    |> assign(:page, page)
    |> load_users_data()
  end

  # ============================================================================
  # 模态框事件处理
  # ============================================================================

  def handle_event("show_create_modal", _params, socket) do
    Logger.info("➕ [用户管理] 显示创建用户模态框")
    socket = show_create_modal(socket)
    {:noreply, socket}
  end

  def handle_event("hide_create_modal", _params, socket) do
    Logger.info("❌ [用户管理] 隐藏创建用户模态框")
    socket = hide_create_modal(socket)
    {:noreply, socket}
  end

  def handle_event("show_edit_modal", %{"user_id" => user_id}, socket) do
    Logger.info("✏️ [用户管理] 显示编辑用户模态框: #{user_id}")
    socket = show_edit_modal(socket, user_id)
    {:noreply, socket}
  end

  def handle_event("hide_edit_modal", _params, socket) do
    Logger.info("❌ [用户管理] 隐藏编辑用户模态框")
    socket = hide_edit_modal(socket)
    {:noreply, socket}
  end

  # 模态框辅助函数
  defp show_create_modal(socket) do
    socket
    |> assign(:show_create_modal, true)
    |> assign(:create_form, @default_create_form)
  end

  defp hide_create_modal(socket) do
    assign(socket, :show_create_modal, false)
  end

  defp show_edit_modal(socket, user_id) do
    case get_user_by_id(user_id) do
      {:ok, user} ->
        user_type = if user.agent_level >= 0, do: "agent", else: "normal"

        edit_form = %{
          "permission_level" => to_string(user.permission_level),
          "user_type" => user_type,
          "points_adjustment" => "0",
          "adjustment_reason" => ""
        }

        socket
        |> assign(:show_edit_modal, true)
        |> assign(:selected_user, user)
        |> assign(:edit_form, edit_form)

      {:error, error} ->
        Logger.error("❌ [用户管理] 获取用户失败: #{inspect(error)}")
        socket
    end
  end

  defp hide_edit_modal(socket) do
    socket
    |> assign(:show_edit_modal, false)
    |> assign(:selected_user, nil)
  end



  # ============================================================================
  # 用户操作事件处理
  # ============================================================================

  def handle_event("create_user", %{"user" => user_params}, socket) do
    Logger.info("➕ [用户管理] 创建新用户: #{user_params["username"]}")
    current_user = socket.assigns.current_user

    # 使用应用服务创建用户（包含通知）
    case AdminService.create_user_with_notification(current_user, user_params) do
      {:ok, user} ->
        Logger.info("✅ [用户管理] 用户创建成功: #{user.username}")
        send(self(), {:flash, :info, "用户 #{user.username} 创建成功"})

        socket =
          socket
          |> hide_create_modal()
          |> load_users_data()

        {:noreply, socket}

      {:error, reason} ->
        Logger.error("❌ [用户管理] 用户创建失败: #{reason}")
        error_message = format_service_error(reason, "用户创建失败")
        send(self(), {:flash, :error, error_message})
        {:noreply, socket}
    end
  end

  def handle_event("update_user", %{"user" => user_params}, socket) do
    user = socket.assigns.selected_user
    current_user = socket.assigns.current_user

    Logger.info("✏️ [用户管理] 更新用户: #{user.username}")

    # 使用用户服务更新用户信息
    case UserService.update_user(current_user, user.id, user_params) do
      {:ok, updated_user} ->
        Logger.info("✅ [用户管理] 用户更新成功: #{updated_user.username}")
        send(self(), {:flash, :info, "用户信息更新成功"})

        # 处理积分调整
        handle_points_adjustment(user_params, user.id)

        socket =
          socket
          |> hide_edit_modal()
          |> load_users_data()

        {:noreply, socket}

      {:error, reason} ->
        Logger.error("❌ [用户管理] 用户更新失败: #{reason}")
        error_message = format_service_error(reason, "用户信息更新失败")
        send(self(), {:flash, :error, error_message})
        {:noreply, socket}
    end
  end

  # ============================================================================
  # 业务逻辑辅助函数
  # ============================================================================

  # 处理积分调整
  defp handle_points_adjustment(user_params, user_id) do
    points_adjustment = String.to_integer(user_params["points_adjustment"] || "0")

    if points_adjustment != 0 do
      reason = user_params["adjustment_reason"] || "管理员调整"

      # TODO: 使用积分服务处理积分调整
      # PointsService.adjust_points(user_id, points_adjustment, reason)

      # 等待缓存刷新完成
      Process.sleep(500)
    end
  end

  # 格式化服务层错误消息
  defp format_service_error(error, default_message) do
    case error do
      error when is_binary(error) -> error
      %Ash.Error.Invalid{} -> "输入数据无效，请检查后重试"
      %{errors: errors} when is_list(errors) ->
        errors
        |> Enum.map(&format_error/1)
        |> Enum.join(", ")
      _ -> default_message
    end
  end

  defp format_error(%{message: message}), do: message
  defp format_error(error) when is_binary(error), do: error
  defp format_error(_), do: "未知错误"

  # 验证用户操作权限
  defp validate_user_operation_permission(current_user, operation, target_user \\ nil) do
    case operation do
      :create -> BusinessRules.has_permission?(current_user, :user_management, :create)
      :update -> BusinessRules.has_permission?(current_user, :user_management, :update)
      :delete -> BusinessRules.has_permission?(current_user, :user_management, :delete)
      _ -> false
    end
  end



  # ============================================================================
  # 数据加载函数 - 使用服务层
  # ============================================================================

  defp load_users_data(socket) do
    Logger.debug("📊 [用户管理] 开始加载用户数据")

    socket = assign(socket, :loading, true)
    current_user = socket.assigns.current_user

    # 验证权限
    if AuthHelper.has_permission?(current_user, :admin) do
      query_params = extract_user_query_params(socket)

      case UserService.list_users(current_user, query_params) do
        {:ok, results} ->
          Logger.info("✅ [用户管理] 成功加载 #{length(results.users)} 个用户")
          assign_users_results(socket, results, query_params)
        {:error, reason} ->
          Logger.error("❌ [用户管理] 加载失败: #{reason}")
          send(self(), {:flash, :error, "加载用户数据失败: #{reason}"})
          assign_users_error_state(socket, query_params)
      end
    else
      Logger.warning("⚠️ [用户管理] 用户无权限访问")
      send(self(), {:flash, :error, "权限不足，无法访问用户管理"})
      assign_no_permission_state(socket)
    end
  end

  # 提取查询参数
  defp extract_user_query_params(socket) do
    %{
      search_query: String.trim(socket.assigns.search_query || ""),
      page: socket.assigns.page,
      per_page: min(socket.assigns.per_page, @max_per_page)
    }
  end

  defp assign_users_results(socket, results, params) do
    page_info = %{
      total_count: results.total_count,
      page: params.page,
      per_page: params.per_page
    }

    socket
    |> assign(:users_data, results.users)
    |> assign(:page_info, page_info)
    |> assign(:loading, false)
  end

  defp assign_users_error_state(socket, params) do
    page_info = %{
      total_count: 0,
      page: params.page,
      per_page: params.per_page
    }

    socket
    |> assign(:users_data, [])
    |> assign(:page_info, page_info)
    |> assign(:loading, false)
  end

  defp assign_no_permission_state(socket) do
    socket
    |> assign(:users_data, [])
    |> assign(:page_info, %{total_count: 0, page: 1, per_page: @default_per_page})
    |> assign(:loading, false)
  end

  # ============================================================================
  # UI辅助函数
  # ============================================================================

  # 获取权限级别UI信息
  defp get_permission_info(level) do
    Map.get(@permission_level_ui, level, %{text: "未知权限", class: "badge-ghost"})
  end

  # 获取用户身份UI信息
  defp get_user_identity_info(agent_level) do
    if agent_level >= 0 do
      %{text: "代理L#{agent_level}", class: "badge-info"}
    else
      %{text: "普通用户", class: "badge-ghost"}
    end
  end

  # 获取用户状态UI信息
  defp get_user_status_info(confirmed_at) do
    if confirmed_at do
      %{text: "已激活", class: "badge-success badge-sm"}
    else
      %{text: "未激活", class: "badge-warning badge-sm"}
    end
  end

  # 格式化用户积分显示
  defp format_user_points(user_id) do
    try do
      Cypridina.Accounts.get_user_points(user_id)
    rescue
      _ -> 0
    end
  end

  # 格式化上级代理信息显示
  defp format_agent_info(agent_info) do
    if agent_info && agent_info.has_agent do
      %{
        username: agent_info.agent.username,
        numeric_id: agent_info.agent.numeric_id
      }
    else
      nil
    end
  end

  # 格式化权限级别名称
  defp format_permission_level_name(level) do
    UserService.get_permission_level_name(level)
  end

  # 创建新用户
  defp create_new_user(params, _current_user) do
    # 对于新用户，如果设置为代理，默认为根代理（0级）
    agent_level = case params["user_type"] do
      "agent" -> 0  # 新创建的代理默认为根代理
      _ -> -1       # 普通用户
    end

    # 准备用户参数
    user_params = %{
      username: params["username"],
      password: params["password"],
      password_confirmation: params["password_confirmation"],
      permission_level: String.to_integer(params["permission_level"]),
      agent_level: agent_level,
      # confirmed_at: DateTime.utc_now(),
      asset: %{
        points: String.to_integer(params["initial_points"] || "0")
      }
    }

    # 创建用户
    case User |> Ash.Changeset.for_create(:register_with_username, user_params) |> Ash.create() do
      {:ok, user} ->
        {:ok, user}

      {:error, changeset} ->
        {:error, changeset}
    end
  end

  # 更新用户信息
  defp update_user_info(user, params, _current_user) do
    # 更新权限级别和代理等级
    permission_level = String.to_integer(params["permission_level"])
    # 根据用户类型和用户的上线计算 agent_level
    agent_level = calculate_agent_level(params["user_type"], user)

    # 先更新权限级别
    case user |> Ash.Changeset.for_update(:update_permission_level, %{permission_level: permission_level}) |> Ash.update() do
      {:ok, updated_user} ->
        # 更新代理等级
        case updated_user |> Ash.Changeset.for_update(:update_agent_level, %{agent_level: agent_level}) |> Ash.update() do
          {:ok, final_user} ->
            # 处理积分调整
            points_adjustment = String.to_integer(params["points_adjustment"] || "0")

            if points_adjustment != 0 do
              case adjust_user_points(user.id, points_adjustment) do
                %{points: _} ->
                  # 积分调整成功后，手动刷新用户缓存
                  Cypridina.UserCache.refresh_user_balance(user.id)
                  :ok
                {:error, _error} -> :ok  # 积分调整失败不影响主要更新
                _ -> :ok  # 其他情况也不影响主要更新
              end
            end

            {:ok, final_user}

          {:error, changeset} ->
            {:error, changeset}
        end

      {:error, changeset} ->
        {:error, changeset}
    end
  end

  # 获取用户信息
  defp get_user_by_id(_user_id) do
    # TODO: 实现用户查询逻辑
    {:error, :not_implemented}
  end

  # 根据用户类型和用户的上线计算 agent_level
  defp calculate_agent_level(user_type, user) do
    case user_type do
      "agent" ->
        # 设置为代理：需要根据用户的上线来计算
        case get_user_agent_level_from_superior(user) do
          {:ok, superior_level} ->
            # 上线的 agent_level + 1
            superior_level + 1

          {:error, :no_agent} ->
            # 如果没有上线，设置为根代理（0级）
            0

          _ ->
            # 出错时默认为根代理
            0
        end

      "normal" ->
        # 设置为普通用户：-1
        -1

      _ ->
        # 默认为普通用户
        -1
    end
  end

  # 获取用户上线的 agent_level
  defp get_user_agent_level_from_superior(user) do
    case Cypridina.Accounts.get_user_agent(user.id) do
      {:ok, agent} ->
        {:ok, agent.agent_level}

      {:error, :no_agent} ->
        {:error, :no_agent}

      error ->
        error
    end
  end

  # 调整用户积分 - 使用新的账户系统
  defp adjust_user_points(user_id, amount) do
    if amount > 0 do
      # 增加积分
      Cypridina.Accounts.add_points(user_id, amount, [
        transaction_type: :admin_operation,
        description: "管理员增加积分",
        metadata: %{admin_operation: true}
      ])
    else
      # 减少积分
      Cypridina.Accounts.subtract_points(user_id, abs(amount), [
        transaction_type: :admin_operation,
        description: "管理员减少积分",
        metadata: %{admin_operation: true}
      ])
    end
  end

  def render(assigns) do
    ~H"""
    <div class="p-6">
      <!-- 页面头部 -->
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-xl font-semibold">用户管理</h2>
        <AdminButtonGroup.add_button
          text="新建用户"
          action="show_create_modal"
          target={@myself}
          icon="fas fa-plus"
        />
      </div>

      <!-- 搜索和统计 -->
      <div class="flex justify-between items-center mb-4">
        <form phx-submit="search" phx-target={@myself} class="flex space-x-2">
          <input
            type="text"
            name="search[query]"
            value={@search_query}
            placeholder="按用户名搜索..."
            class="input input-bordered input-sm w-64"
          />
          <AdminButtonGroup.admin_button
            text="搜索"
            type="submit"
            icon="fas fa-search"
            class="bg-blue-500 text-white hover:bg-blue-600"
            bg_color="#3b82f6"
          />
        </form>
        <div class="text-sm text-base-content/60">
          <%= if @page_info do %>
            共 <%= @page_info.total_count %> 个用户，第 <%= @page_info.page %> 页
          <% else %>
            加载中...
          <% end %>
        </div>
      </div>

      <!-- 用户列表 -->
      <%= if @users_data && length(@users_data) > 0 do %>
        <div class="overflow-x-auto">
          <table class="table table-zebra w-full">
            <thead>
              <tr>
                <th>数字ID</th>
                <th>用户名</th>
                <th>权限</th>
                <th>身份</th>
                <th>上线</th>
                <th>积分</th>
                <th>注册时间</th>
                <th>状态</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <%= for user <- @users_data do %>
                <tr>
                  <td class="font-mono"><%= user.numeric_id %></td>
                  <td class="font-medium"><%= to_string(user.username) %></td>
                  <td>
                    <%= case user.permission_level do %>
                      <% 2 -> %>
                        <span class="badge badge-error">超级管理员</span>
                      <% 1 -> %>
                        <span class="badge badge-warning">管理员</span>
                      <% 0 -> %>
                        <span class="badge badge-ghost">普通权限</span>
                    <% end %>
                  </td>
                  <td>
                    <%= if user.agent_level >= 0 do %>
                      <span class="badge badge-info">代理L<%= user.agent_level %></span>
                    <% else %>
                      <span class="badge badge-ghost">普通用户</span>
                    <% end %>
                  </td>
                  <td>
                    <%= if user.agent_info && user.agent_info.has_agent do %>
                      <div class="flex flex-col">
                        <span class="font-medium text-blue-600">
                          <%= user.agent_info.agent.username %>
                        </span>
                        <span class="text-xs text-base-content/60">
                          ID: <%= user.agent_info.agent.numeric_id %>
                        </span>
                      </div>
                    <% else %>
                      <span class="text-base-content/40">无上线</span>
                    <% end %>
                  </td>
                  <td class="font-medium">
                    <%= Cypridina.Accounts.get_user_points(user.id) %>
                  </td>
                  <td><%= TimeHelper.format_local_date(user.inserted_at) %></td>
                  <td>
                    <%= if user.confirmed_at do %>
                      <span class="badge badge-success badge-sm">已激活</span>
                    <% else %>
                      <span class="badge badge-warning badge-sm">未激活</span>
                    <% end %>
                  </td>
                  <td>
                    <div class="flex space-x-1">
                      <AdminButtonGroup.edit_button
                        action="show_edit_modal"
                        target={@myself}
                        id={user.id}
                        text="编辑"
                        class="btn-xs"
                      />
                      <.live_component
                        module={PointsHistoryComponent}
                        id={"points_history_#{user.id}"}
                        user_id={user.id}
                        current_user={@current_user}
                        show_admin_actions={true}
                        user_info={%{
                          username: user.username,
                          numeric_id: user.numeric_id,
                          current_points: Cypridina.Accounts.get_user_points(user.id)
                        }}
                      />
                    </div>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>

        <!-- 分页控件 -->
        <%= if @page_info && @page_info.total_count > @page_info.per_page do %>
          <div class="flex justify-center mt-6">
            <div class="flex items-center gap-2">
              <%= if @page_info.page > 1 do %>
                <AdminButtonGroup.pagination_button
                  text="«"
                  action="page_change"
                  target={@myself}
                  page={@page_info.page - 1}
                />
              <% end %>

              <%= for page_num <- max(1, @page_info.page - 2)..min(ceil(@page_info.total_count / @page_info.per_page), @page_info.page + 2) do %>
                <%= if page_num == @page_info.page do %>
                  <span class="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg font-medium text-sm">
                    <%= page_num %>
                  </span>
                <% else %>
                  <AdminButtonGroup.pagination_button
                    text={to_string(page_num)}
                    action="page_change"
                    target={@myself}
                    page={page_num}
                  />
                <% end %>
              <% end %>

              <%= if @page_info.page < ceil(@page_info.total_count / @page_info.per_page) do %>
                <AdminButtonGroup.pagination_button
                  text="»"
                  action="page_change"
                  target={@myself}
                  page={@page_info.page + 1}
                />
              <% end %>
            </div>
          </div>
        <% end %>
      <% else %>
        <div class="text-center py-12">
          <div class="text-base-content/40 mb-4">
            <.icon name="hero-users" class="w-16 h-16 mx-auto" />
          </div>
          <p class="text-base-content/60">
            <%= if @search_query != "" do %>
              未找到匹配的用户
            <% else %>
              暂无用户数据
            <% end %>
          </p>
        </div>
      <% end %>

      <!-- 创建用户模态框 -->
      <%= if @show_create_modal do %>
        <div class="modal modal-open">
          <div class="modal-box w-11/12 max-w-2xl">
            <h3 class="font-bold text-lg mb-4">新建用户</h3>

            <form phx-submit="create_user" phx-target={@myself} class="space-y-4">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- 用户名 -->
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">用户名 *</span>
                  </label>
                  <input
                    type="text"
                    name="user[username]"
                    value={@create_form["username"]}
                    placeholder="请输入用户名"
                    class="input input-bordered"
                    required
                  />
                </div>



                <!-- 密码 -->
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">密码 *</span>
                  </label>
                  <input
                    type="password"
                    name="user[password]"
                    value={@create_form["password"]}
                    placeholder="请输入密码"
                    class="input input-bordered"
                    required
                  />
                </div>

                <!-- 确认密码 -->
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">确认密码 *</span>
                  </label>
                  <input
                    type="password"
                    name="user[password_confirmation]"
                    value={@create_form["password_confirmation"]}
                    placeholder="请再次输入密码"
                    class="input input-bordered"
                    required
                  />
                </div>

                <!-- 权限级别 -->
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">权限级别</span>
                  </label>
                  <select name="user[permission_level]" class="select select-bordered">
                    <option value="0" selected={@create_form["permission_level"] == "0"}>普通用户</option>
                    <option value="1" selected={@create_form["permission_level"] == "1"}>管理员</option>
                    <option value="2" selected={@create_form["permission_level"] == "2"}>超级管理员</option>
                  </select>
                </div>

                <!-- 身份 -->
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">身份</span>
                  </label>
                  <select name="user[user_type]" class="select select-bordered">
                    <option value="normal" selected={@create_form["user_type"] == "normal"}>普通用户</option>
                    <option value="agent" selected={@create_form["user_type"] == "agent"}>代理</option>
                  </select>
                </div>

                <!-- 初始积分 -->
                <div class="form-control md:col-span-2">
                  <label class="label">
                    <span class="label-text">初始积分</span>
                  </label>
                  <input
                    type="number"
                    name="user[initial_points]"
                    value={@create_form["initial_points"]}
                    placeholder="0"
                    class="input input-bordered"
                    min="0"
                  />
                </div>
              </div>

              <div class="modal-action">
                <AdminButtonGroup.cancel_button
                  text="取消"
                  action="hide_create_modal"
                  target={@myself}
                />
                <AdminButtonGroup.save_button
                  text="创建用户"
                  type="submit"
                />
              </div>
            </form>
          </div>
        </div>
      <% end %>

      <!-- 编辑用户模态框 -->
      <%= if @show_edit_modal and @selected_user do %>
        <div class="modal modal-open">
          <div class="modal-box w-11/12 max-w-2xl">
            <h3 class="font-bold text-lg mb-4">编辑用户 - <%= to_string(@selected_user.username) %></h3>

            <!-- 用户基本信息 -->
            <div class="bg-base-200 p-4 rounded-lg mb-4">
              <div class="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span class="text-base-content/60">数字ID:</span>
                  <span class="font-mono"><%= @selected_user.numeric_id %></span>
                </div>

                <div>
                  <span class="text-base-content/60">当前积分:</span>
                  <span class="font-medium">
                    <%= Cypridina.Accounts.get_user_points(@selected_user.id) %>
                  </span>
                </div>
                <div>
                  <span class="text-base-content/60">注册时间:</span>
                  <span><%= TimeHelper.format_local_date(@selected_user.inserted_at) %></span>
                </div>
              </div>
            </div>

            <form phx-submit="update_user" phx-target={@myself} class="space-y-4">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- 权限级别 -->
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">权限级别</span>
                  </label>
                  <select name="user[permission_level]" class="select select-bordered">
                    <option value="0" selected={@edit_form["permission_level"] == "0"}>普通用户</option>
                    <option value="1" selected={@edit_form["permission_level"] == "1"}>管理员</option>
                    <option value="2" selected={@edit_form["permission_level"] == "2"}>超级管理员</option>
                  </select>
                </div>

                <!-- 身份 -->
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">身份</span>
                  </label>
                  <select name="user[user_type]" class="select select-bordered">
                    <option value="normal" selected={@edit_form["user_type"] == "normal"}>普通用户</option>
                    <option value="agent" selected={@edit_form["user_type"] == "agent"}>代理</option>
                  </select>
                </div>

                <!-- 积分调整 -->
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">积分调整</span>
                    <span class="label-text-alt">正数增加，负数减少</span>
                  </label>
                  <input
                    type="number"
                    name="user[points_adjustment]"
                    value={@edit_form["points_adjustment"]}
                    placeholder="0"
                    class="input input-bordered"
                  />
                </div>

                <!-- 调整原因 -->
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">调整原因</span>
                  </label>
                  <input
                    type="text"
                    name="user[adjustment_reason]"
                    value={@edit_form["adjustment_reason"]}
                    placeholder="请输入调整原因"
                    class="input input-bordered"
                  />
                </div>
              </div>

              <div class="modal-action">
                <AdminButtonGroup.cancel_button
                  text="取消"
                  action="hide_edit_modal"
                  target={@myself}
                />
                <AdminButtonGroup.save_button
                  text="保存更改"
                  type="submit"
                />
              </div>
            </form>
          </div>
        </div>
      <% end %>


    </div>
    """
  end


end
