/**
 * Racing Game Admin Panel - 性能监控系统
 * 
 * 实时监控和分析前端性能指标，提供性能优化建议
 */

/**
 * 性能监控管理器
 */
class PerformanceMonitor {
  constructor(options = {}) {
    this.options = {
      enableRealTimeMonitoring: true,
      reportInterval: 30000, // 30秒
      enableWebVitals: true,
      enableResourceTiming: true,
      enableUserTiming: true,
      enableMemoryMonitoring: true,
      maxReports: 100,
      ...options
    };

    this.metrics = {
      webVitals: {},
      resourceTiming: [],
      userTiming: [],
      memoryUsage: [],
      customMetrics: {},
      errors: []
    };

    this.observers = [];
    this.reportTimer = null;
    this.isMonitoring = false;

    this.init();
  }

  init() {
    if (this.options.enableRealTimeMonitoring) {
      this.startMonitoring();
    }
  }

  /**
   * 开始性能监控
   */
  startMonitoring() {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    
    if (this.options.enableWebVitals) {
      this.setupWebVitalsMonitoring();
    }
    
    if (this.options.enableResourceTiming) {
      this.setupResourceTimingMonitoring();
    }
    
    if (this.options.enableUserTiming) {
      this.setupUserTimingMonitoring();
    }
    
    if (this.options.enableMemoryMonitoring) {
      this.setupMemoryMonitoring();
    }

    this.setupErrorMonitoring();
    this.startReporting();
    
    console.log('🚀 Performance monitoring started');
  }

  /**
   * 停止性能监控
   */
  stopMonitoring() {
    if (!this.isMonitoring) return;

    this.isMonitoring = false;
    
    // 断开所有观察器
    this.observers.forEach(observer => {
      if (observer && typeof observer.disconnect === 'function') {
        observer.disconnect();
      }
    });
    this.observers = [];

    // 停止报告定时器
    if (this.reportTimer) {
      clearInterval(this.reportTimer);
      this.reportTimer = null;
    }

    console.log('⏹️ Performance monitoring stopped');
  }

  /**
   * 设置Web Vitals监控
   */
  setupWebVitalsMonitoring() {
    // Largest Contentful Paint (LCP)
    if ('PerformanceObserver' in window) {
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        this.metrics.webVitals.lcp = {
          value: lastEntry.startTime,
          timestamp: Date.now(),
          element: lastEntry.element?.tagName || 'unknown'
        };
        this.onMetricUpdate('lcp', this.metrics.webVitals.lcp);
      });
      
      try {
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        this.observers.push(lcpObserver);
      } catch (e) {
        console.warn('LCP monitoring not supported');
      }

      // First Input Delay (FID)
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach(entry => {
          this.metrics.webVitals.fid = {
            value: entry.processingStart - entry.startTime,
            timestamp: Date.now(),
            eventType: entry.name
          };
          this.onMetricUpdate('fid', this.metrics.webVitals.fid);
        });
      });

      try {
        fidObserver.observe({ entryTypes: ['first-input'] });
        this.observers.push(fidObserver);
      } catch (e) {
        console.warn('FID monitoring not supported');
      }

      // Cumulative Layout Shift (CLS)
      let clsValue = 0;
      const clsObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach(entry => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        });
        
        this.metrics.webVitals.cls = {
          value: clsValue,
          timestamp: Date.now()
        };
        this.onMetricUpdate('cls', this.metrics.webVitals.cls);
      });

      try {
        clsObserver.observe({ entryTypes: ['layout-shift'] });
        this.observers.push(clsObserver);
      } catch (e) {
        console.warn('CLS monitoring not supported');
      }
    }

    // First Contentful Paint (FCP)
    this.measureFCP();
  }

  /**
   * 测量First Contentful Paint
   */
  measureFCP() {
    if ('PerformanceObserver' in window) {
      const fcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach(entry => {
          if (entry.name === 'first-contentful-paint') {
            this.metrics.webVitals.fcp = {
              value: entry.startTime,
              timestamp: Date.now()
            };
            this.onMetricUpdate('fcp', this.metrics.webVitals.fcp);
          }
        });
      });

      try {
        fcpObserver.observe({ entryTypes: ['paint'] });
        this.observers.push(fcpObserver);
      } catch (e) {
        console.warn('FCP monitoring not supported');
      }
    }
  }

  /**
   * 设置资源时间监控
   */
  setupResourceTimingMonitoring() {
    if ('PerformanceObserver' in window) {
      const resourceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach(entry => {
          const resourceData = {
            name: entry.name,
            type: entry.initiatorType,
            duration: entry.duration,
            size: entry.transferSize || 0,
            startTime: entry.startTime,
            timestamp: Date.now()
          };
          
          this.metrics.resourceTiming.push(resourceData);
          
          // 保持数组大小限制
          if (this.metrics.resourceTiming.length > this.options.maxReports) {
            this.metrics.resourceTiming.shift();
          }
          
          this.analyzeResourcePerformance(resourceData);
        });
      });

      try {
        resourceObserver.observe({ entryTypes: ['resource'] });
        this.observers.push(resourceObserver);
      } catch (e) {
        console.warn('Resource timing monitoring not supported');
      }
    }
  }

  /**
   * 设置用户时间监控
   */
  setupUserTimingMonitoring() {
    if ('PerformanceObserver' in window) {
      const userTimingObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach(entry => {
          const timingData = {
            name: entry.name,
            type: entry.entryType,
            duration: entry.duration || 0,
            startTime: entry.startTime,
            timestamp: Date.now()
          };
          
          this.metrics.userTiming.push(timingData);
          
          if (this.metrics.userTiming.length > this.options.maxReports) {
            this.metrics.userTiming.shift();
          }
        });
      });

      try {
        userTimingObserver.observe({ entryTypes: ['measure', 'mark'] });
        this.observers.push(userTimingObserver);
      } catch (e) {
        console.warn('User timing monitoring not supported');
      }
    }
  }

  /**
   * 设置内存监控
   */
  setupMemoryMonitoring() {
    if ('memory' in performance) {
      const measureMemory = () => {
        const memoryInfo = {
          usedJSHeapSize: performance.memory.usedJSHeapSize,
          totalJSHeapSize: performance.memory.totalJSHeapSize,
          jsHeapSizeLimit: performance.memory.jsHeapSizeLimit,
          timestamp: Date.now()
        };
        
        this.metrics.memoryUsage.push(memoryInfo);
        
        if (this.metrics.memoryUsage.length > this.options.maxReports) {
          this.metrics.memoryUsage.shift();
        }
        
        this.analyzeMemoryUsage(memoryInfo);
      };

      // 立即测量一次
      measureMemory();
      
      // 定期测量
      const memoryTimer = setInterval(measureMemory, 5000);
      this.observers.push({ disconnect: () => clearInterval(memoryTimer) });
    }
  }

  /**
   * 设置错误监控
   */
  setupErrorMonitoring() {
    const errorHandler = (event) => {
      const errorData = {
        message: event.error?.message || event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack,
        timestamp: Date.now(),
        type: 'javascript'
      };
      
      this.metrics.errors.push(errorData);
      
      if (this.metrics.errors.length > this.options.maxReports) {
        this.metrics.errors.shift();
      }
      
      this.onError(errorData);
    };

    const rejectionHandler = (event) => {
      const errorData = {
        message: event.reason?.message || 'Unhandled Promise Rejection',
        stack: event.reason?.stack,
        timestamp: Date.now(),
        type: 'promise'
      };
      
      this.metrics.errors.push(errorData);
      this.onError(errorData);
    };

    window.addEventListener('error', errorHandler);
    window.addEventListener('unhandledrejection', rejectionHandler);
    
    this.observers.push({
      disconnect: () => {
        window.removeEventListener('error', errorHandler);
        window.removeEventListener('unhandledrejection', rejectionHandler);
      }
    });
  }

  /**
   * 分析资源性能
   */
  analyzeResourcePerformance(resource) {
    // 检查慢资源
    if (resource.duration > 1000) {
      console.warn(`🐌 Slow resource detected: ${resource.name} (${resource.duration.toFixed(2)}ms)`);
    }
    
    // 检查大文件
    if (resource.size > 1024 * 1024) { // 1MB
      console.warn(`📦 Large resource detected: ${resource.name} (${(resource.size / 1024 / 1024).toFixed(2)}MB)`);
    }
  }

  /**
   * 分析内存使用
   */
  analyzeMemoryUsage(memoryInfo) {
    const usagePercent = (memoryInfo.usedJSHeapSize / memoryInfo.jsHeapSizeLimit) * 100;
    
    if (usagePercent > 80) {
      console.warn(`🧠 High memory usage: ${usagePercent.toFixed(2)}%`);
    }
    
    // 检查内存泄漏
    if (this.metrics.memoryUsage.length >= 10) {
      const recent = this.metrics.memoryUsage.slice(-10);
      const trend = this.calculateMemoryTrend(recent);
      
      if (trend > 0.1) { // 10%增长趋势
        console.warn('🔍 Potential memory leak detected');
      }
    }
  }

  /**
   * 计算内存趋势
   */
  calculateMemoryTrend(memoryData) {
    if (memoryData.length < 2) return 0;
    
    const first = memoryData[0].usedJSHeapSize;
    const last = memoryData[memoryData.length - 1].usedJSHeapSize;
    
    return (last - first) / first;
  }

  /**
   * 指标更新回调
   */
  onMetricUpdate(metricName, data) {
    // 触发自定义事件
    const event = new CustomEvent('performanceMetricUpdate', {
      detail: { metricName, data }
    });
    document.dispatchEvent(event);
    
    // 检查性能阈值
    this.checkPerformanceThresholds(metricName, data);
  }

  /**
   * 检查性能阈值
   */
  checkPerformanceThresholds(metricName, data) {
    const thresholds = {
      lcp: 2500, // 2.5s
      fid: 100,  // 100ms
      cls: 0.1,  // 0.1
      fcp: 1800  // 1.8s
    };
    
    if (thresholds[metricName] && data.value > thresholds[metricName]) {
      console.warn(`⚠️ Performance threshold exceeded for ${metricName}: ${data.value} > ${thresholds[metricName]}`);
    }
  }

  /**
   * 错误回调
   */
  onError(errorData) {
    console.error('🚨 Error detected:', errorData);
    
    // 可以在这里发送错误报告到服务器
    if (this.options.onError) {
      this.options.onError(errorData);
    }
  }

  /**
   * 开始定期报告
   */
  startReporting() {
    if (this.reportTimer) return;
    
    this.reportTimer = setInterval(() => {
      this.generateReport();
    }, this.options.reportInterval);
  }

  /**
   * 生成性能报告
   */
  generateReport() {
    const report = {
      timestamp: Date.now(),
      webVitals: this.metrics.webVitals,
      resourceSummary: this.summarizeResources(),
      memorySummary: this.summarizeMemory(),
      errorSummary: this.summarizeErrors(),
      recommendations: this.generateRecommendations()
    };
    
    console.log('📊 Performance Report:', report);
    
    // 触发报告事件
    const event = new CustomEvent('performanceReport', {
      detail: report
    });
    document.dispatchEvent(event);
    
    return report;
  }

  /**
   * 汇总资源信息
   */
  summarizeResources() {
    const resources = this.metrics.resourceTiming;
    if (resources.length === 0) return {};
    
    const totalSize = resources.reduce((sum, r) => sum + r.size, 0);
    const avgDuration = resources.reduce((sum, r) => sum + r.duration, 0) / resources.length;
    
    return {
      totalRequests: resources.length,
      totalSize: totalSize,
      averageDuration: avgDuration,
      slowestResource: resources.reduce((slowest, r) => 
        r.duration > slowest.duration ? r : slowest, resources[0])
    };
  }

  /**
   * 汇总内存信息
   */
  summarizeMemory() {
    const memory = this.metrics.memoryUsage;
    if (memory.length === 0) return {};
    
    const latest = memory[memory.length - 1];
    const usagePercent = (latest.usedJSHeapSize / latest.jsHeapSizeLimit) * 100;
    
    return {
      currentUsage: latest.usedJSHeapSize,
      usagePercent: usagePercent,
      trend: this.calculateMemoryTrend(memory.slice(-10))
    };
  }

  /**
   * 汇总错误信息
   */
  summarizeErrors() {
    return {
      totalErrors: this.metrics.errors.length,
      recentErrors: this.metrics.errors.slice(-5)
    };
  }

  /**
   * 生成优化建议
   */
  generateRecommendations() {
    const recommendations = [];
    
    // Web Vitals建议
    if (this.metrics.webVitals.lcp?.value > 2500) {
      recommendations.push('考虑优化最大内容绘制(LCP)：压缩图片、使用CDN、优化服务器响应时间');
    }
    
    if (this.metrics.webVitals.fid?.value > 100) {
      recommendations.push('考虑优化首次输入延迟(FID)：减少JavaScript执行时间、使用Web Workers');
    }
    
    if (this.metrics.webVitals.cls?.value > 0.1) {
      recommendations.push('考虑优化累积布局偏移(CLS)：为图片设置尺寸、避免动态插入内容');
    }
    
    // 内存建议
    const memorySummary = this.summarizeMemory();
    if (memorySummary.usagePercent > 80) {
      recommendations.push('内存使用率过高，考虑优化内存管理、清理未使用的对象');
    }
    
    return recommendations;
  }

  /**
   * 获取当前指标
   */
  getMetrics() {
    return { ...this.metrics };
  }

  /**
   * 清除指标数据
   */
  clearMetrics() {
    this.metrics = {
      webVitals: {},
      resourceTiming: [],
      userTiming: [],
      memoryUsage: [],
      customMetrics: {},
      errors: []
    };
  }

  /**
   * 销毁监控器
   */
  destroy() {
    this.stopMonitoring();
    this.clearMetrics();
  }
}

// 全局实例
window.performanceMonitor = new PerformanceMonitor();

// Phoenix LiveView Hook
const PerformanceMonitorHook = {
  mounted() {
    // 监听性能报告
    document.addEventListener('performanceReport', (event) => {
      this.pushEvent('performance_report', event.detail);
    });
    
    // 监听错误
    document.addEventListener('performanceMetricUpdate', (event) => {
      if (event.detail.metricName === 'error') {
        this.pushEvent('performance_error', event.detail.data);
      }
    });
  },

  destroyed() {
    // 清理事件监听器
  }
};

// 导出
export { PerformanceMonitor, PerformanceMonitorHook };
