import Config

# 🧪 Teen系统测试配置
# 为测试环境提供专门的配置设置

# ==================== 基础测试配置 ====================

config :racing_game, :test_mode, true

# 测试数据库配置
config :racing_game, :database,
  test_database: "racing_game_test",
  pool_size: 20,  # 增加连接池大小以支持并发测试
  timeout: 30_000,
  ownership_timeout: 60_000

# ==================== 缓存配置 ====================

# 测试环境使用较短的缓存TTL
config :racing_game, :cache,
  # Repository层缓存配置
  repository_cache_ttl: 1_000,      # 1秒，便于测试缓存机制
  query_builder_cache_ttl: 2_000,   # 2秒
  service_cache_ttl: 3_000,         # 3秒
  
  # 统计数据缓存配置
  stats_cache_ttl: 5_000,           # 5秒
  report_cache_ttl: 10_000,         # 10秒
  
  # 缓存键前缀
  cache_key_prefix: "test_teen_",
  
  # 缓存适配器
  adapter: :ets  # 测试环境使用ETS缓存

# ==================== 并发和性能配置 ====================

config :racing_game, :performance,
  # Task并发配置
  max_concurrent_tasks: 50,
  task_timeout: 30_000,
  
  # 批处理配置
  batch_size: 100,
  max_batch_size: 500,
  
  # 查询超时配置
  query_timeout: 15_000,
  complex_query_timeout: 30_000,
  
  # 内存限制
  max_memory_usage: 200 * 1024 * 1024,  # 200MB
  gc_threshold: 50 * 1024 * 1024        # 50MB时触发GC

# ==================== 日志配置 ====================

config :logger,
  level: :info,  # 测试时使用info级别，避免过多debug信息
  backends: [:console, {LoggerFileBackend, :test_file}]

config :logger, :console,
  format: "[$level] $message\n",
  metadata: [:request_id, :module, :function]

config :logger, :test_file,
  path: "test/logs/test.log",
  level: :debug,
  format: "$time [$level] $metadata$message\n",
  metadata: [:request_id, :module, :function, :line]

# ==================== Teen系统模块配置 ====================

# 客服系统测试配置
config :racing_game, :customer_service,
  auto_assign_tickets: false,        # 测试时禁用自动分配
  max_tickets_per_agent: 1000,      # 增加限制以支持测试
  response_time_sla: 3600,          # 1小时SLA
  escalation_enabled: false         # 测试时禁用升级

# 游戏管理系统测试配置
config :racing_game, :game_management,
  auto_vip_upgrade: true,           # 启用自动VIP升级便于测试
  robot_selection_algorithm: :skill_based,
  platform_health_check_interval: 60_000,  # 1分钟检查间隔
  vip_calculation_cache_ttl: 5_000  # 5秒缓存

# 系统设置测试配置
config :racing_game, :system_settings,
  config_validation_enabled: true,
  permission_cache_ttl: 10_000,     # 10秒权限缓存
  role_hierarchy_max_depth: 5,
  audit_log_enabled: true

# 支付系统测试配置
config :racing_game, :payment_system,
  risk_monitoring_enabled: true,
  auto_settlement_enabled: false,   # 测试时禁用自动结算
  payment_timeout: 300_000,         # 5分钟支付超时
  refund_auto_approval_limit: 1000, # 1000元以下自动批准退款
  fraud_detection_enabled: false    # 测试时禁用欺诈检测

# 活动系统测试配置
config :racing_game, :activity_system,
  auto_reward_distribution: true,   # 启用自动奖励发放
  activity_validation_strict: false, # 测试时放宽验证
  participation_limit_check: false, # 测试时禁用参与限制检查
  reward_processing_batch_size: 50

# 数据统计系统测试配置
config :racing_game, :data_statistics,
  real_time_stats_enabled: false,   # 测试时禁用实时统计
  stats_aggregation_interval: 10_000, # 10秒聚合间隔
  data_retention_days: 7,           # 测试数据保留7天
  quality_check_enabled: true,
  auto_repair_enabled: false        # 测试时禁用自动修复

# 推广系统测试配置
config :racing_game, :promotion_system,
  auto_promoter_approval: false,    # 测试时禁用自动审核
  commission_calculation_method: :percentage,
  settlement_batch_size: 100,
  channel_click_tracking: true,
  relationship_tree_max_depth: 5

# ==================== 测试数据配置 ====================

config :racing_game, :test_data,
  # 测试数据生成配置
  generate_test_users: 100,
  generate_test_tickets: 200,
  generate_test_orders: 150,
  generate_test_activities: 20,
  generate_test_promoters: 30,
  
  # 测试数据范围
  user_id_range: {1, 100_000},
  amount_range: {10, 10_000},
  date_range_days: 30,
  
  # 随机数种子（确保测试可重现）
  random_seed: 12345

# ==================== 外部服务Mock配置 ====================

config :racing_game, :external_services,
  # 支付网关Mock
  payment_gateway_mock: true,
  payment_success_rate: 0.9,        # 90%成功率
  
  # 短信服务Mock
  sms_service_mock: true,
  sms_delivery_rate: 0.95,          # 95%送达率
  
  # 邮件服务Mock
  email_service_mock: true,
  email_delivery_rate: 0.98,        # 98%送达率
  
  # 第三方API Mock
  third_party_api_mock: true,
  api_response_delay: 100           # 100ms模拟延迟

# ==================== 测试报告配置 ====================

config :racing_game, :test_reporting,
  # 报告输出目录
  report_output_dir: "test/reports",
  
  # 报告格式
  report_formats: [:json, :html, :csv],
  
  # 性能基准
  performance_benchmarks: %{
    repository_crud_max_time: 1000,      # 1秒
    service_operation_max_time: 3000,    # 3秒
    complex_query_max_time: 8000,        # 8秒
    batch_operation_max_time: 10_000,    # 10秒
    max_memory_increase: 100 * 1024 * 1024  # 100MB
  },
  
  # 成功率阈值
  success_rate_thresholds: %{
    unit_tests: 0.95,           # 95%
    integration_tests: 0.90,    # 90%
    performance_tests: 0.80,    # 80%
    load_tests: 0.70           # 70%
  }

# ==================== 错误处理和重试配置 ====================

config :racing_game, :error_handling,
  # 重试配置
  max_retries: 3,
  retry_delay: 1000,            # 1秒
  exponential_backoff: true,
  
  # 错误分类
  retriable_errors: [
    :timeout,
    :connection_error,
    :temporary_failure
  ],
  
  # 断路器配置
  circuit_breaker_enabled: false,  # 测试时禁用断路器
  failure_threshold: 5,
  recovery_timeout: 30_000

# ==================== 监控和指标配置 ====================

config :racing_game, :monitoring,
  # 指标收集
  metrics_enabled: true,
  metrics_interval: 5_000,      # 5秒收集间隔
  
  # 健康检查
  health_check_enabled: true,
  health_check_interval: 10_000, # 10秒检查间隔
  
  # 告警配置
  alerting_enabled: false,      # 测试时禁用告警
  alert_thresholds: %{
    error_rate: 0.05,           # 5%错误率
    response_time: 5000,        # 5秒响应时间
    memory_usage: 0.8           # 80%内存使用率
  }

# ==================== 安全配置 ====================

config :racing_game, :security,
  # 测试环境安全配置
  encryption_enabled: false,    # 测试时禁用加密以提高性能
  audit_logging: true,
  rate_limiting_enabled: false, # 测试时禁用限流
  
  # 测试用户权限
  test_admin_permissions: :all,
  test_user_permissions: [:read, :create],
  
  # 数据脱敏
  data_masking_enabled: false   # 测试时禁用数据脱敏

# ==================== 环境特定配置 ====================

# CI/CD环境配置
if System.get_env("CI") do
  config :racing_game, :ci_mode, true
  
  # CI环境下的特殊配置
  config :racing_game, :performance,
    max_concurrent_tasks: 20,    # 降低并发数
    task_timeout: 60_000        # 增加超时时间
  
  config :logger, level: :warn  # CI环境减少日志输出
end

# 本地开发测试配置
if System.get_env("MIX_ENV") == "test" and !System.get_env("CI") do
  config :racing_game, :local_test_mode, true
  
  # 本地测试的特殊配置
  config :racing_game, :test_data,
    generate_test_users: 50,     # 减少测试数据量
    generate_test_tickets: 100
  
  config :logger, level: :debug # 本地测试显示详细日志
end
