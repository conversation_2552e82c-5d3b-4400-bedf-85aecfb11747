defmodule RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder do
  @moduledoc """
  客服系统查询构建器

  提供复杂客服查询的构建功能：
  - 动态查询条件构建
  - 跨Repository联合查询
  - 搜索查询优化
  - 过滤条件组合
  - 排序和分页支持
  - 统计查询构建
  """

  require Logger
  alias Teen.CustomerService.{CustomerChat, UserQuestion, ExchangeOrder, SensitiveWord, UserTag, VerificationCode}
  require Ash.Query

  # 常量定义
  @chat_searchable_fields [:content, :notes]
  @question_searchable_fields [:title, :content, :notes]
  @order_searchable_fields [:order_number, :notes]
  @word_searchable_fields [:word]
  @tag_searchable_fields [:tag_name, :notes]
  @code_searchable_fields [:phone]

  @chat_filterable_fields [:user_id, :customer_service_id, :status, :priority, :inserted_at, :updated_at]
  @question_filterable_fields [:user_id, :assigned_to, :status, :priority, :inserted_at, :updated_at]
  @order_filterable_fields [:user_id, :auditor_id, :status, :progress_status, :inserted_at, :updated_at]
  @word_filterable_fields [:word_type, :enabled, :inserted_at, :updated_at]
  @tag_filterable_fields [:user_id, :tag_type, :created_by, :inserted_at, :updated_at]
  @code_filterable_fields [:phone, :code_type, :is_used, :inserted_at, :expires_at]

  @chat_sortable_fields [:status, :priority, :inserted_at, :updated_at]
  @question_sortable_fields [:status, :priority, :inserted_at, :updated_at]
  @order_sortable_fields [:status, :progress_status, :inserted_at, :updated_at]
  @word_sortable_fields [:word, :word_type, :inserted_at, :updated_at]
  @tag_sortable_fields [:tag_name, :tag_type, :inserted_at, :updated_at]
  @code_sortable_fields [:phone, :code_type, :inserted_at, :expires_at]

  # ============================================================================
  # 客服聊天查询构建
  # ============================================================================

  @doc """
  构建客服聊天列表查询

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, query}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_chat_list_query(params) do
    Logger.debug("🔨 [客服查询构建器] 构建聊天列表查询")

    try do
      query = CustomerChat
      |> apply_search_conditions(params[:search], @chat_searchable_fields)
      |> apply_filter_conditions(params[:filters], @chat_filterable_fields)
      |> apply_sort_conditions(params[:sort], @chat_sortable_fields)

      {:ok, query}
    rescue
      error ->
        Logger.error("❌ [客服查询构建器] 构建聊天查询失败: #{inspect(error)}")
        {:error, "聊天查询构建失败"}
    end
  end

  @doc """
  构建客服聊天搜索查询

  ## 参数
  - `search_term` - 搜索词
  - `fields` - 搜索字段列表

  ## 返回
  - 查询对象
  """
  def build_chat_search_query(search_term, fields \\ @chat_searchable_fields) do
    Logger.debug("🔍 [客服查询构建器] 构建聊天搜索查询: #{search_term}")

    CustomerChat
    |> apply_search_across_fields(search_term, fields)
    |> Ash.Query.sort(inserted_at: :desc)
  end

  @doc """
  构建用户聊天查询

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - 查询对象
  """
  def build_user_chats_query(user_id, options \\ []) do
    Logger.debug("👤 [客服查询构建器] 构建用户聊天查询: #{user_id}")

    CustomerChat
    |> Ash.Query.filter(user_id == ^user_id)
    |> apply_status_filter(options[:status])
    |> apply_date_range_filter(options[:date_from], options[:date_to])
    |> Ash.Query.sort(inserted_at: :desc)
  end

  @doc """
  构建客服工作量查询

  ## 参数
  - `customer_service_id` - 客服ID
  - `options` - 选项

  ## 返回
  - 查询对象
  """
  def build_customer_service_workload_query(customer_service_id, options \\ []) do
    Logger.debug("👨‍💼 [客服查询构建器] 构建客服工作量查询: #{customer_service_id}")

    CustomerChat
    |> Ash.Query.filter(customer_service_id == ^customer_service_id)
    |> apply_status_filter(options[:status])
    |> apply_date_range_filter(options[:date_from], options[:date_to])
    |> Ash.Query.sort(inserted_at: :desc)
  end

  # ============================================================================
  # 用户问题查询构建
  # ============================================================================

  @doc """
  构建用户问题列表查询

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, query}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_question_list_query(params) do
    Logger.debug("🔨 [客服查询构建器] 构建问题列表查询")

    try do
      query = UserQuestion
      |> apply_search_conditions(params[:search], @question_searchable_fields)
      |> apply_filter_conditions(params[:filters], @question_filterable_fields)
      |> apply_sort_conditions(params[:sort], @question_sortable_fields)

      {:ok, query}
    rescue
      error ->
        Logger.error("❌ [客服查询构建器] 构建问题查询失败: #{inspect(error)}")
        {:error, "问题查询构建失败"}
    end
  end

  @doc """
  构建待处理问题查询

  ## 参数
  - `options` - 选项

  ## 返回
  - 查询对象
  """
  def build_pending_questions_query(options \\ []) do
    Logger.debug("⏳ [客服查询构建器] 构建待处理问题查询")

    UserQuestion
    |> Ash.Query.filter(status == "pending")
    |> apply_priority_filter(options[:priority])
    |> apply_date_range_filter(options[:date_from], options[:date_to])
    |> Ash.Query.sort(priority: :desc, inserted_at: :asc)
  end

  @doc """
  构建员工分配问题查询

  ## 参数
  - `staff_id` - 员工ID
  - `options` - 选项

  ## 返回
  - 查询对象
  """
  def build_staff_assigned_questions_query(staff_id, options \\ []) do
    Logger.debug("👨‍💼 [客服查询构建器] 构建员工分配问题查询: #{staff_id}")

    UserQuestion
    |> Ash.Query.filter(assigned_to == ^staff_id)
    |> apply_status_filter(options[:status])
    |> apply_date_range_filter(options[:date_from], options[:date_to])
    |> Ash.Query.sort(priority: :desc, inserted_at: :asc)
  end

  # ============================================================================
  # 兑换订单查询构建
  # ============================================================================

  @doc """
  构建兑换订单列表查询

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, query}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_order_list_query(params) do
    Logger.debug("🔨 [客服查询构建器] 构建订单列表查询")

    try do
      query = ExchangeOrder
      |> apply_search_conditions(params[:search], @order_searchable_fields)
      |> apply_filter_conditions(params[:filters], @order_filterable_fields)
      |> apply_sort_conditions(params[:sort], @order_sortable_fields)

      {:ok, query}
    rescue
      error ->
        Logger.error("❌ [客服查询构建器] 构建订单查询失败: #{inspect(error)}")
        {:error, "订单查询构建失败"}
    end
  end

  @doc """
  构建待审核订单查询

  ## 参数
  - `options` - 选项

  ## 返回
  - 查询对象
  """
  def build_pending_audit_orders_query(options \\ []) do
    Logger.debug("⏳ [客服查询构建器] 构建待审核订单查询")

    ExchangeOrder
    |> Ash.Query.filter(status == "pending_audit")
    |> apply_date_range_filter(options[:date_from], options[:date_to])
    |> Ash.Query.sort(inserted_at: :asc)
  end

  @doc """
  构建用户订单查询

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - 查询对象
  """
  def build_user_orders_query(user_id, options \\ []) do
    Logger.debug("👤 [客服查询构建器] 构建用户订单查询: #{user_id}")

    ExchangeOrder
    |> Ash.Query.filter(user_id == ^user_id)
    |> apply_status_filter(options[:status])
    |> apply_date_range_filter(options[:date_from], options[:date_to])
    |> Ash.Query.sort(inserted_at: :desc)
  end

  # ============================================================================
  # 敏感词查询构建
  # ============================================================================

  @doc """
  构建敏感词列表查询

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, query}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_word_list_query(params) do
    Logger.debug("🔨 [客服查询构建器] 构建敏感词列表查询")

    try do
      query = SensitiveWord
      |> apply_search_conditions(params[:search], @word_searchable_fields)
      |> apply_filter_conditions(params[:filters], @word_filterable_fields)
      |> apply_sort_conditions(params[:sort], @word_sortable_fields)

      {:ok, query}
    rescue
      error ->
        Logger.error("❌ [客服查询构建器] 构建敏感词查询失败: #{inspect(error)}")
        {:error, "敏感词查询构建失败"}
    end
  end

  @doc """
  构建启用敏感词查询

  ## 参数
  - `options` - 选项

  ## 返回
  - 查询对象
  """
  def build_enabled_words_query(options \\ []) do
    Logger.debug("✅ [客服查询构建器] 构建启用敏感词查询")

    SensitiveWord
    |> Ash.Query.filter(enabled == true)
    |> apply_word_type_filter(options[:word_type])
    |> Ash.Query.sort(word: :asc)
  end

  # ============================================================================
  # 用户标签查询构建
  # ============================================================================

  @doc """
  构建用户标签列表查询

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, query}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_tag_list_query(params) do
    Logger.debug("🔨 [客服查询构建器] 构建标签列表查询")

    try do
      query = UserTag
      |> apply_search_conditions(params[:search], @tag_searchable_fields)
      |> apply_filter_conditions(params[:filters], @tag_filterable_fields)
      |> apply_sort_conditions(params[:sort], @tag_sortable_fields)

      {:ok, query}
    rescue
      error ->
        Logger.error("❌ [客服查询构建器] 构建标签查询失败: #{inspect(error)}")
        {:error, "标签查询构建失败"}
    end
  end

  @doc """
  构建用户标签查询

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - 查询对象
  """
  def build_user_tags_query(user_id, options \\ []) do
    Logger.debug("👤 [客服查询构建器] 构建用户标签查询: #{user_id}")

    UserTag
    |> Ash.Query.filter(user_id == ^user_id)
    |> apply_tag_type_filter(options[:tag_type])
    |> Ash.Query.sort(inserted_at: :desc)
  end

  # ============================================================================
  # 验证码查询构建
  # ============================================================================

  @doc """
  构建验证码列表查询

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, query}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_code_list_query(params) do
    Logger.debug("🔨 [客服查询构建器] 构建验证码列表查询")

    try do
      query = VerificationCode
      |> apply_search_conditions(params[:search], @code_searchable_fields)
      |> apply_filter_conditions(params[:filters], @code_filterable_fields)
      |> apply_sort_conditions(params[:sort], @code_sortable_fields)

      {:ok, query}
    rescue
      error ->
        Logger.error("❌ [客服查询构建器] 构建验证码查询失败: #{inspect(error)}")
        {:error, "验证码查询构建失败"}
    end
  end

  @doc """
  构建手机验证码历史查询

  ## 参数
  - `phone` - 手机号
  - `options` - 选项

  ## 返回
  - 查询对象
  """
  def build_phone_code_history_query(phone, options \\ []) do
    Logger.debug("📱 [客服查询构建器] 构建手机验证码历史查询: #{phone}")

    VerificationCode
    |> Ash.Query.filter(phone == ^phone)
    |> apply_code_type_filter(options[:code_type])
    |> apply_date_range_filter(options[:date_from], options[:date_to])
    |> Ash.Query.sort(inserted_at: :desc)
  end

  # ============================================================================
  # 统计查询构建
  # ============================================================================

  @doc """
  构建客服系统统计查询

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, queries}` - 成功，返回各种统计查询
  - `{:error, reason}` - 失败
  """
  def build_statistics_queries(params \\ %{}) do
    Logger.debug("📊 [客服查询构建器] 构建统计查询")

    try do
      date_from = params[:date_from]
      date_to = params[:date_to]

      queries = %{
        chat_stats: build_chat_statistics_query(date_from, date_to),
        question_stats: build_question_statistics_query(date_from, date_to),
        order_stats: build_order_statistics_query(date_from, date_to),
        word_stats: build_word_statistics_query(),
        tag_stats: build_tag_statistics_query(),
        code_stats: build_code_statistics_query(date_from, date_to)
      }

      {:ok, queries}
    rescue
      error ->
        Logger.error("❌ [客服查询构建器] 构建统计查询失败: #{inspect(error)}")
        {:error, "统计查询构建失败"}
    end
  end

  @doc """
  构建跨Repository联合查询

  ## 参数
  - `query_type` - 查询类型
  - `params` - 查询参数

  ## 返回
  - `{:ok, query}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_cross_repository_query(query_type, params) do
    Logger.debug("🔗 [客服查询构建器] 构建跨Repository查询: #{query_type}")

    case query_type do
      :user_activity_summary ->
        build_user_activity_summary_query(params[:user_id], params)
      :customer_service_performance ->
        build_customer_service_performance_query(params[:customer_service_id], params)
      :system_health_check ->
        build_system_health_check_query(params)
      _ ->
        {:error, "不支持的查询类型"}
    end
  end

  # ============================================================================
  # 私有辅助函数
  # ============================================================================

  # 应用搜索条件
  defp apply_search_conditions(query, search, _fields) when search == "" or is_nil(search), do: query
  defp apply_search_conditions(query, search, fields) do
    apply_search_across_fields(query, search, fields)
  end

  # 跨字段搜索
  defp apply_search_across_fields(query, search_term, fields) do
    search_pattern = "%#{search_term}%"

    conditions = Enum.map(fields, fn field ->
      case field do
        :content -> {:ilike, field, search_pattern}
        :title -> {:ilike, field, search_pattern}
        :notes -> {:ilike, field, search_pattern}
        :word -> {:ilike, field, search_pattern}
        :tag_name -> {:ilike, field, search_pattern}
        :phone -> {:ilike, field, search_pattern}
        :order_number -> {:ilike, field, search_pattern}
        _ -> {:ilike, field, search_pattern}
      end
    end)

    # 使用 OR 条件连接所有搜索字段
    Enum.reduce(conditions, query, fn {op, field, pattern}, acc_query ->
      case op do
        :ilike -> Ash.Query.filter(acc_query, ilike(^field, ^pattern))
        _ -> acc_query
      end
    end)
  end

  # 应用过滤条件
  defp apply_filter_conditions(query, filters, _allowed_fields) when is_nil(filters), do: query
  defp apply_filter_conditions(query, filters, _allowed_fields) when map_size(filters) == 0, do: query
  defp apply_filter_conditions(query, filters, allowed_fields) do
    Enum.reduce(filters, query, fn {key, value}, acc_query ->
      if key in allowed_fields and not is_nil(value) do
        apply_single_filter(acc_query, key, value)
      else
        acc_query
      end
    end)
  end

  # 应用单个过滤条件
  defp apply_single_filter(query, :user_id, user_id) do
    Ash.Query.filter(query, user_id == ^user_id)
  end
  defp apply_single_filter(query, :customer_service_id, customer_service_id) do
    Ash.Query.filter(query, customer_service_id == ^customer_service_id)
  end
  defp apply_single_filter(query, :assigned_to, assigned_to) do
    Ash.Query.filter(query, assigned_to == ^assigned_to)
  end
  defp apply_single_filter(query, :auditor_id, auditor_id) do
    Ash.Query.filter(query, auditor_id == ^auditor_id)
  end
  defp apply_single_filter(query, :created_by, created_by) do
    Ash.Query.filter(query, created_by == ^created_by)
  end
  defp apply_single_filter(query, :status, status) do
    Ash.Query.filter(query, status == ^status)
  end
  defp apply_single_filter(query, :priority, priority) do
    Ash.Query.filter(query, priority == ^priority)
  end
  defp apply_single_filter(query, :progress_status, progress_status) do
    Ash.Query.filter(query, progress_status == ^progress_status)
  end
  defp apply_single_filter(query, :word_type, word_type) do
    Ash.Query.filter(query, word_type == ^word_type)
  end
  defp apply_single_filter(query, :tag_type, tag_type) do
    Ash.Query.filter(query, tag_type == ^tag_type)
  end
  defp apply_single_filter(query, :code_type, code_type) do
    Ash.Query.filter(query, code_type == ^code_type)
  end
  defp apply_single_filter(query, :enabled, enabled) do
    Ash.Query.filter(query, enabled == ^enabled)
  end
  defp apply_single_filter(query, :is_used, is_used) do
    Ash.Query.filter(query, is_used == ^is_used)
  end
  defp apply_single_filter(query, :inserted_at, date_range) when is_tuple(date_range) do
    {start_date, end_date} = date_range
    Ash.Query.filter(query, inserted_at >= ^start_date and inserted_at <= ^end_date)
  end
  defp apply_single_filter(query, :updated_at, date_range) when is_tuple(date_range) do
    {start_date, end_date} = date_range
    Ash.Query.filter(query, updated_at >= ^start_date and updated_at <= ^end_date)
  end
  defp apply_single_filter(query, :expires_at, date_range) when is_tuple(date_range) do
    {start_date, end_date} = date_range
    Ash.Query.filter(query, expires_at >= ^start_date and expires_at <= ^end_date)
  end
  defp apply_single_filter(query, _key, _value), do: query

  # 应用排序条件
  defp apply_sort_conditions(query, sort, _allowed_fields) when sort == [] or is_nil(sort), do: query
  defp apply_sort_conditions(query, sort, allowed_fields) when is_list(sort) do
    valid_sort = Enum.filter(sort, fn {field, _direction} -> field in allowed_fields end)
    if length(valid_sort) > 0 do
      Ash.Query.sort(query, valid_sort)
    else
      query
    end
  end
  defp apply_sort_conditions(query, _sort, _allowed_fields), do: query

  # 应用状态过滤
  defp apply_status_filter(query, nil), do: query
  defp apply_status_filter(query, status) do
    Ash.Query.filter(query, status == ^status)
  end

  # 应用优先级过滤
  defp apply_priority_filter(query, nil), do: query
  defp apply_priority_filter(query, priority) do
    Ash.Query.filter(query, priority == ^priority)
  end

  # 应用日期范围过滤
  defp apply_date_range_filter(query, nil, nil), do: query
  defp apply_date_range_filter(query, date_from, date_to) do
    query
    |> maybe_apply_date_from(date_from)
    |> maybe_apply_date_to(date_to)
  end

  defp maybe_apply_date_from(query, nil), do: query
  defp maybe_apply_date_from(query, date_from) do
    Ash.Query.filter(query, inserted_at >= ^date_from)
  end

  defp maybe_apply_date_to(query, nil), do: query
  defp maybe_apply_date_to(query, date_to) do
    Ash.Query.filter(query, inserted_at <= ^date_to)
  end

  # 应用词类型过滤
  defp apply_word_type_filter(query, nil), do: query
  defp apply_word_type_filter(query, word_type) do
    Ash.Query.filter(query, word_type == ^word_type)
  end

  # 应用标签类型过滤
  defp apply_tag_type_filter(query, nil), do: query
  defp apply_tag_type_filter(query, tag_type) do
    Ash.Query.filter(query, tag_type == ^tag_type)
  end

  # 应用验证码类型过滤
  defp apply_code_type_filter(query, nil), do: query
  defp apply_code_type_filter(query, code_type) do
    Ash.Query.filter(query, code_type == ^code_type)
  end

  # ============================================================================
  # 统计查询私有函数
  # ============================================================================

  # 构建聊天统计查询
  defp build_chat_statistics_query(date_from, date_to) do
    CustomerChat
    |> apply_date_range_filter(date_from, date_to)
    |> Ash.Query.aggregate(:count, :id)
  end

  # 构建问题统计查询
  defp build_question_statistics_query(date_from, date_to) do
    UserQuestion
    |> apply_date_range_filter(date_from, date_to)
    |> Ash.Query.aggregate(:count, :id)
  end

  # 构建订单统计查询
  defp build_order_statistics_query(date_from, date_to) do
    ExchangeOrder
    |> apply_date_range_filter(date_from, date_to)
    |> Ash.Query.aggregate(:count, :id)
  end

  # 构建敏感词统计查询
  defp build_word_statistics_query do
    SensitiveWord
    |> Ash.Query.aggregate(:count, :id)
  end

  # 构建标签统计查询
  defp build_tag_statistics_query do
    UserTag
    |> Ash.Query.aggregate(:count, :id)
  end

  # 构建验证码统计查询
  defp build_code_statistics_query(date_from, date_to) do
    VerificationCode
    |> apply_date_range_filter(date_from, date_to)
    |> Ash.Query.aggregate(:count, :id)
  end

  # ============================================================================
  # 跨Repository查询私有函数
  # ============================================================================

  # 构建用户活动摘要查询
  defp build_user_activity_summary_query(user_id, params) do
    Logger.debug("👤 [客服查询构建器] 构建用户活动摘要查询: #{user_id}")

    try do
      date_from = params[:date_from]
      date_to = params[:date_to]

      queries = %{
        chats: build_user_chats_query(user_id, date_from: date_from, date_to: date_to),
        questions: build_user_questions_query(user_id, date_from: date_from, date_to: date_to),
        orders: build_user_orders_query(user_id, date_from: date_from, date_to: date_to),
        tags: build_user_tags_query(user_id),
        codes: build_user_codes_query(user_id, date_from: date_from, date_to: date_to)
      }

      {:ok, queries}
    rescue
      error ->
        Logger.error("❌ [客服查询构建器] 构建用户活动摘要查询失败: #{inspect(error)}")
        {:error, "用户活动摘要查询构建失败"}
    end
  end

  # 构建客服绩效查询
  defp build_customer_service_performance_query(customer_service_id, params) do
    Logger.debug("👨‍💼 [客服查询构建器] 构建客服绩效查询: #{customer_service_id}")

    try do
      date_from = params[:date_from]
      date_to = params[:date_to]

      queries = %{
        handled_chats: build_customer_service_workload_query(customer_service_id, date_from: date_from, date_to: date_to),
        assigned_questions: build_staff_assigned_questions_query(customer_service_id, date_from: date_from, date_to: date_to)
      }

      {:ok, queries}
    rescue
      error ->
        Logger.error("❌ [客服查询构建器] 构建客服绩效查询失败: #{inspect(error)}")
        {:error, "客服绩效查询构建失败"}
    end
  end

  # 构建系统健康检查查询
  defp build_system_health_check_query(params) do
    Logger.debug("🏥 [客服查询构建器] 构建系统健康检查查询")

    try do
      current_time = DateTime.utc_now()
      one_hour_ago = DateTime.add(current_time, -3600, :second)

      queries = %{
        recent_chats: CustomerChat |> Ash.Query.filter(inserted_at >= ^one_hour_ago),
        pending_questions: UserQuestion |> Ash.Query.filter(status == "pending"),
        pending_orders: ExchangeOrder |> Ash.Query.filter(status == "pending_audit"),
        recent_codes: VerificationCode |> Ash.Query.filter(inserted_at >= ^one_hour_ago),
        enabled_words: SensitiveWord |> Ash.Query.filter(enabled == true)
      }

      {:ok, queries}
    rescue
      error ->
        Logger.error("❌ [客服查询构建器] 构建系统健康检查查询失败: #{inspect(error)}")
        {:error, "系统健康检查查询构建失败"}
    end
  end

  # 构建用户问题查询
  defp build_user_questions_query(user_id, options \\ []) do
    UserQuestion
    |> Ash.Query.filter(user_id == ^user_id)
    |> apply_date_range_filter(options[:date_from], options[:date_to])
    |> Ash.Query.sort(inserted_at: :desc)
  end

  # 构建用户验证码查询
  defp build_user_codes_query(user_id, options \\ []) do
    # 注意：这里假设 VerificationCode 有 user_id 字段，如果没有则需要通过其他方式关联
    VerificationCode
    |> apply_date_range_filter(options[:date_from], options[:date_to])
    |> Ash.Query.sort(inserted_at: :desc)
  end
end
