# 备份恢复指南

## 📋 概述

本文档提供 Racing Game Admin Panel 系统的完整备份和恢复策略，包括数据库备份、文件备份、灾难恢复和数据验证流程。

## 🏗️ 备份架构

```
┌─────────────────────────────────────────────────────────────┐
│                    备份架构图                                │
│                                                             │
│  生产数据库 ──→ 本地备份 ──→ 远程存储 (S3/云存储)            │
│      │              │              │                        │
│      │              │              ▼                        │
│      │              │         异地备份存储                   │
│      │              │                                        │
│      ▼              ▼                                        │
│  实时复制 ──→ 备用数据库 ──→ 灾难恢复环境                    │
│                                                             │
│  应用文件 ──→ 文件备份 ──→ 版本控制 + 云存储                 │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## 🗄️ 数据库备份策略

### 1. PostgreSQL 备份配置

#### 全量备份脚本
```bash
#!/bin/bash
# full_backup.sh

set -e

# 配置变量
DB_HOST="localhost"
DB_PORT="5432"
DB_NAME="racing_game_prod"
DB_USER="postgres"
BACKUP_DIR="/backup/postgresql"
S3_BUCKET="racing-game-backups"
RETENTION_DAYS=30

# 创建备份目录
mkdir -p $BACKUP_DIR

# 生成备份文件名
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="racing_game_full_${TIMESTAMP}.sql"
BACKUP_PATH="$BACKUP_DIR/$BACKUP_FILE"

echo "开始全量备份: $BACKUP_FILE"

# 执行 pg_dump
PGPASSWORD=$DB_PASSWORD pg_dump \
    -h $DB_HOST \
    -p $DB_PORT \
    -U $DB_USER \
    -d $DB_NAME \
    --verbose \
    --no-owner \
    --no-privileges \
    --format=custom \
    --compress=9 \
    --file=$BACKUP_PATH

# 验证备份文件
if [ -f "$BACKUP_PATH" ] && [ -s "$BACKUP_PATH" ]; then
    echo "备份文件创建成功: $BACKUP_PATH"
    
    # 计算文件哈希
    CHECKSUM=$(sha256sum $BACKUP_PATH | cut -d' ' -f1)
    echo $CHECKSUM > "${BACKUP_PATH}.sha256"
    
    # 上传到 S3
    aws s3 cp $BACKUP_PATH s3://$S3_BUCKET/postgresql/full/
    aws s3 cp "${BACKUP_PATH}.sha256" s3://$S3_BUCKET/postgresql/full/
    
    echo "备份已上传到 S3"
else
    echo "备份失败!" >&2
    exit 1
fi

# 清理本地旧备份
find $BACKUP_DIR -name "racing_game_full_*.sql" -mtime +$RETENTION_DAYS -delete
find $BACKUP_DIR -name "racing_game_full_*.sql.sha256" -mtime +$RETENTION_DAYS -delete

echo "全量备份完成"
```

#### 增量备份配置
```bash
#!/bin/bash
# incremental_backup.sh

set -e

# WAL 归档配置
WAL_ARCHIVE_DIR="/backup/postgresql/wal"
S3_BUCKET="racing-game-backups"

# 创建 WAL 归档目录
mkdir -p $WAL_ARCHIVE_DIR

# WAL 归档命令 (在 postgresql.conf 中配置)
# archive_command = '/backup/scripts/archive_wal.sh %p %f'

# WAL 归档脚本
cat > /backup/scripts/archive_wal.sh << 'EOF'
#!/bin/bash
WAL_PATH=$1
WAL_FILE=$2
WAL_ARCHIVE_DIR="/backup/postgresql/wal"
S3_BUCKET="racing-game-backups"

# 复制 WAL 文件到本地归档目录
cp $WAL_PATH $WAL_ARCHIVE_DIR/$WAL_FILE

# 上传到 S3
aws s3 cp $WAL_ARCHIVE_DIR/$WAL_FILE s3://$S3_BUCKET/postgresql/wal/

# 验证上传成功
if aws s3 ls s3://$S3_BUCKET/postgresql/wal/$WAL_FILE > /dev/null; then
    exit 0
else
    exit 1
fi
EOF

chmod +x /backup/scripts/archive_wal.sh
```

#### PostgreSQL 配置优化
```sql
-- postgresql.conf 备份相关配置
# WAL 配置
wal_level = replica
archive_mode = on
archive_command = '/backup/scripts/archive_wal.sh %p %f'
archive_timeout = 300

# 检查点配置
checkpoint_timeout = 15min
checkpoint_completion_target = 0.9

# 复制配置
max_wal_senders = 3
wal_keep_segments = 64
```

### 2. 自动化备份调度

#### Cron 任务配置
```bash
# 编辑 crontab
crontab -e

# 添加备份任务
# 每日凌晨 2 点执行全量备份
0 2 * * * /backup/scripts/full_backup.sh >> /var/log/backup.log 2>&1

# 每小时执行一次 WAL 归档检查
0 * * * * /backup/scripts/check_wal_archive.sh >> /var/log/backup.log 2>&1

# 每周日执行备份验证
0 3 * * 0 /backup/scripts/verify_backups.sh >> /var/log/backup.log 2>&1
```

#### Systemd Timer 配置
```ini
# /etc/systemd/system/racing-game-backup.service
[Unit]
Description=Racing Game Database Backup
Wants=racing-game-backup.timer

[Service]
Type=oneshot
User=postgres
ExecStart=/backup/scripts/full_backup.sh
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

```ini
# /etc/systemd/system/racing-game-backup.timer
[Unit]
Description=Racing Game Backup Timer
Requires=racing-game-backup.service

[Timer]
OnCalendar=daily
Persistent=true

[Install]
WantedBy=timers.target
```

```bash
# 启用定时器
sudo systemctl enable racing-game-backup.timer
sudo systemctl start racing-game-backup.timer
```

## 📁 文件系统备份

### 1. 应用文件备份

```bash
#!/bin/bash
# app_backup.sh

set -e

APP_DIR="/opt/racing_game"
BACKUP_DIR="/backup/application"
S3_BUCKET="racing-game-backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份应用文件
BACKUP_FILE="racing_game_app_${TIMESTAMP}.tar.gz"
BACKUP_PATH="$BACKUP_DIR/$BACKUP_FILE"

echo "开始应用文件备份..."

# 创建压缩包，排除不必要的文件
tar -czf $BACKUP_PATH \
    --exclude='_build' \
    --exclude='deps' \
    --exclude='assets/node_modules' \
    --exclude='*.log' \
    --exclude='.git' \
    -C $(dirname $APP_DIR) \
    $(basename $APP_DIR)

# 验证备份文件
if [ -f "$BACKUP_PATH" ] && [ -s "$BACKUP_PATH" ]; then
    echo "应用备份创建成功: $BACKUP_PATH"
    
    # 计算校验和
    CHECKSUM=$(sha256sum $BACKUP_PATH | cut -d' ' -f1)
    echo $CHECKSUM > "${BACKUP_PATH}.sha256"
    
    # 上传到 S3
    aws s3 cp $BACKUP_PATH s3://$S3_BUCKET/application/
    aws s3 cp "${BACKUP_PATH}.sha256" s3://$S3_BUCKET/application/
    
    echo "应用备份已上传到 S3"
else
    echo "应用备份失败!" >&2
    exit 1
fi

# 清理旧备份
find $BACKUP_DIR -name "racing_game_app_*.tar.gz" -mtime +7 -delete
find $BACKUP_DIR -name "racing_game_app_*.tar.gz.sha256" -mtime +7 -delete

echo "应用文件备份完成"
```

### 2. 配置文件备份

```bash
#!/bin/bash
# config_backup.sh

set -e

CONFIG_DIRS=(
    "/etc/nginx/sites-available"
    "/etc/systemd/system"
    "/opt/racing_game/config"
    "/etc/postgresql/14/main"
    "/etc/redis"
)

BACKUP_DIR="/backup/configuration"
S3_BUCKET="racing-game-backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

mkdir -p $BACKUP_DIR

BACKUP_FILE="racing_game_config_${TIMESTAMP}.tar.gz"
BACKUP_PATH="$BACKUP_DIR/$BACKUP_FILE"

echo "开始配置文件备份..."

# 备份配置文件
tar -czf $BACKUP_PATH "${CONFIG_DIRS[@]}" 2>/dev/null || true

# 上传到 S3
aws s3 cp $BACKUP_PATH s3://$S3_BUCKET/configuration/

echo "配置文件备份完成"
```

## 🔄 数据恢复流程

### 1. 数据库恢复

#### 完整恢复脚本
```bash
#!/bin/bash
# restore_database.sh

set -e

BACKUP_FILE=$1
DB_NAME="racing_game_prod"
DB_USER="postgres"
RESTORE_DB_NAME="${DB_NAME}_restore_$(date +%Y%m%d_%H%M%S)"

if [ -z "$BACKUP_FILE" ]; then
    echo "用法: $0 <backup_file>"
    echo "示例: $0 racing_game_full_20241219_020000.sql"
    exit 1
fi

echo "开始数据库恢复..."
echo "备份文件: $BACKUP_FILE"
echo "目标数据库: $RESTORE_DB_NAME"

# 1. 验证备份文件
if [ ! -f "$BACKUP_FILE" ]; then
    echo "备份文件不存在: $BACKUP_FILE"
    exit 1
fi

# 验证校验和
if [ -f "${BACKUP_FILE}.sha256" ]; then
    echo "验证备份文件完整性..."
    if ! sha256sum -c "${BACKUP_FILE}.sha256"; then
        echo "备份文件校验失败!"
        exit 1
    fi
    echo "备份文件校验通过"
fi

# 2. 创建恢复数据库
echo "创建恢复数据库: $RESTORE_DB_NAME"
createdb -U $DB_USER $RESTORE_DB_NAME

# 3. 恢复数据
echo "开始恢复数据..."
PGPASSWORD=$DB_PASSWORD pg_restore \
    -U $DB_USER \
    -d $RESTORE_DB_NAME \
    --verbose \
    --no-owner \
    --no-privileges \
    $BACKUP_FILE

# 4. 验证恢复结果
echo "验证恢复结果..."
TABLE_COUNT=$(PGPASSWORD=$DB_PASSWORD psql -U $DB_USER -d $RESTORE_DB_NAME -t -c "SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public';")
echo "恢复的表数量: $TABLE_COUNT"

if [ "$TABLE_COUNT" -gt 0 ]; then
    echo "数据库恢复成功!"
    echo "恢复数据库名称: $RESTORE_DB_NAME"
    echo ""
    echo "如需切换到恢复的数据库，请执行以下步骤:"
    echo "1. 停止应用服务: sudo systemctl stop racing-game"
    echo "2. 重命名当前数据库: ALTER DATABASE $DB_NAME RENAME TO ${DB_NAME}_backup_$(date +%Y%m%d);"
    echo "3. 重命名恢复数据库: ALTER DATABASE $RESTORE_DB_NAME RENAME TO $DB_NAME;"
    echo "4. 启动应用服务: sudo systemctl start racing-game"
else
    echo "数据库恢复失败!"
    dropdb -U $DB_USER $RESTORE_DB_NAME
    exit 1
fi
```

#### 时间点恢复 (PITR)
```bash
#!/bin/bash
# point_in_time_recovery.sh

set -e

BASE_BACKUP=$1
RECOVERY_TIME=$2
WAL_ARCHIVE_DIR="/backup/postgresql/wal"

if [ -z "$BASE_BACKUP" ] || [ -z "$RECOVERY_TIME" ]; then
    echo "用法: $0 <base_backup> <recovery_time>"
    echo "示例: $0 racing_game_full_20241219_020000.sql '2024-12-19 14:30:00'"
    exit 1
fi

echo "开始时间点恢复..."
echo "基础备份: $BASE_BACKUP"
echo "恢复时间点: $RECOVERY_TIME"

# 1. 恢复基础备份
./restore_database.sh $BASE_BACKUP

# 2. 配置恢复参数
RECOVERY_DB_NAME="racing_game_prod_restore_$(date +%Y%m%d_%H%M%S)"

# 创建 recovery.conf
cat > /tmp/recovery.conf << EOF
restore_command = 'cp $WAL_ARCHIVE_DIR/%f %p'
recovery_target_time = '$RECOVERY_TIME'
recovery_target_action = 'promote'
EOF

# 3. 启动恢复过程
echo "启动时间点恢复..."
# 这里需要根据具体的 PostgreSQL 版本调整恢复流程

echo "时间点恢复完成"
```

### 2. 应用恢复

```bash
#!/bin/bash
# restore_application.sh

set -e

BACKUP_FILE=$1
RESTORE_DIR="/opt/racing_game_restore"

if [ -z "$BACKUP_FILE" ]; then
    echo "用法: $0 <backup_file>"
    exit 1
fi

echo "开始应用恢复..."

# 1. 创建恢复目录
mkdir -p $RESTORE_DIR

# 2. 解压备份文件
tar -xzf $BACKUP_FILE -C $RESTORE_DIR

# 3. 恢复配置文件
echo "恢复配置文件..."
# 根据需要恢复特定的配置文件

# 4. 重新编译应用
cd $RESTORE_DIR/racing_game
mix deps.get --only prod
MIX_ENV=prod mix compile
MIX_ENV=prod mix release

echo "应用恢复完成"
echo "恢复目录: $RESTORE_DIR"
```

## 🧪 备份验证

### 1. 自动化验证脚本

```bash
#!/bin/bash
# verify_backups.sh

set -e

BACKUP_DIR="/backup/postgresql"
S3_BUCKET="racing-game-backups"
TEST_DB_NAME="racing_game_test_restore"

echo "开始备份验证..."

# 1. 获取最新备份文件
LATEST_BACKUP=$(ls -t $BACKUP_DIR/racing_game_full_*.sql | head -n1)

if [ -z "$LATEST_BACKUP" ]; then
    echo "未找到备份文件"
    exit 1
fi

echo "验证备份文件: $LATEST_BACKUP"

# 2. 验证文件完整性
if [ -f "${LATEST_BACKUP}.sha256" ]; then
    if ! sha256sum -c "${LATEST_BACKUP}.sha256"; then
        echo "备份文件校验失败!"
        exit 1
    fi
    echo "文件完整性验证通过"
fi

# 3. 测试恢复
echo "测试数据库恢复..."

# 删除测试数据库（如果存在）
dropdb -U postgres --if-exists $TEST_DB_NAME

# 创建测试数据库
createdb -U postgres $TEST_DB_NAME

# 恢复到测试数据库
PGPASSWORD=$DB_PASSWORD pg_restore \
    -U postgres \
    -d $TEST_DB_NAME \
    --no-owner \
    --no-privileges \
    $LATEST_BACKUP

# 4. 验证数据完整性
echo "验证数据完整性..."

# 检查表数量
TABLE_COUNT=$(PGPASSWORD=$DB_PASSWORD psql -U postgres -d $TEST_DB_NAME -t -c "SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public';")
echo "表数量: $TABLE_COUNT"

# 检查关键表的记录数
USER_COUNT=$(PGPASSWORD=$DB_PASSWORD psql -U postgres -d $TEST_DB_NAME -t -c "SELECT count(*) FROM users;")
echo "用户数量: $USER_COUNT"

# 5. 清理测试数据库
dropdb -U postgres $TEST_DB_NAME

# 6. 验证 S3 备份
echo "验证 S3 备份..."
S3_BACKUP_COUNT=$(aws s3 ls s3://$S3_BUCKET/postgresql/full/ | wc -l)
echo "S3 备份文件数量: $S3_BACKUP_COUNT"

echo "备份验证完成"

# 7. 生成验证报告
REPORT_FILE="/var/log/backup_verification_$(date +%Y%m%d_%H%M%S).log"
cat > $REPORT_FILE << EOF
备份验证报告
=============
验证时间: $(date)
备份文件: $LATEST_BACKUP
文件大小: $(du -h $LATEST_BACKUP | cut -f1)
表数量: $TABLE_COUNT
用户数量: $USER_COUNT
S3备份数量: $S3_BACKUP_COUNT
验证状态: 成功
EOF

echo "验证报告已生成: $REPORT_FILE"
```

### 2. 监控和告警

```bash
#!/bin/bash
# backup_monitoring.sh

# 检查备份是否按时执行
LAST_BACKUP=$(ls -t /backup/postgresql/racing_game_full_*.sql 2>/dev/null | head -n1)
if [ -n "$LAST_BACKUP" ]; then
    BACKUP_AGE=$(( ($(date +%s) - $(stat -c %Y "$LAST_BACKUP")) / 3600 ))
    if [ $BACKUP_AGE -gt 25 ]; then  # 超过25小时
        echo "警告: 备份文件过旧 ($BACKUP_AGE 小时)"
        # 发送告警
        curl -X POST -H 'Content-type: application/json' \
            --data '{"text":"Racing Game 备份异常: 备份文件超过25小时未更新"}' \
            $SLACK_WEBHOOK_URL
    fi
else
    echo "错误: 未找到备份文件"
fi

# 检查 S3 同步状态
S3_SYNC_STATUS=$(aws s3 sync /backup/postgresql/ s3://$S3_BUCKET/postgresql/ --dryrun | wc -l)
if [ $S3_SYNC_STATUS -gt 0 ]; then
    echo "警告: S3 同步不完整"
fi
```

## 🚨 灾难恢复计划

### 1. 灾难恢复流程

```markdown
# 灾难恢复检查清单

## 紧急响应 (0-15分钟)
- [ ] 确认灾难类型和影响范围
- [ ] 通知相关人员和管理层
- [ ] 激活灾难恢复团队
- [ ] 评估数据丢失程度

## 系统恢复 (15分钟-2小时)
- [ ] 准备灾难恢复环境
- [ ] 从最新备份恢复数据库
- [ ] 恢复应用程序
- [ ] 配置网络和负载均衡
- [ ] 执行系统验证测试

## 服务恢复 (2-4小时)
- [ ] 切换DNS指向恢复环境
- [ ] 验证所有功能正常
- [ ] 通知用户服务恢复
- [ ] 监控系统稳定性

## 后续处理 (4小时后)
- [ ] 分析灾难原因
- [ ] 更新恢复文档
- [ ] 改进备份策略
- [ ] 进行恢复演练
```

### 2. RTO/RPO 目标

```
恢复时间目标 (RTO): 4小时
恢复点目标 (RPO): 1小时

关键业务功能:
- 用户登录: RTO 2小时, RPO 30分钟
- 支付处理: RTO 1小时, RPO 15分钟
- 游戏数据: RTO 4小时, RPO 1小时
```

## 📋 备份最佳实践

### 1. 3-2-1 备份规则
- **3** 份数据副本
- **2** 种不同的存储介质
- **1** 份异地备份

### 2. 定期测试
- 每月进行恢复测试
- 每季度进行灾难恢复演练
- 每年进行完整的业务连续性测试

### 3. 监控和告警
- 备份任务执行状态监控
- 备份文件完整性验证
- 存储空间使用率监控
- 恢复时间监控

---

**文档版本**: v1.0  
**创建日期**: 2024-12-19  
**最后更新**: 2024-12-19  
**维护团队**: Racing Game Development Team
