defmodule RacingGame.Live.AdminPanel.Operations.SearchOperations do
  @moduledoc """
  搜索操作模块

  处理系统通信管理组件中的数据加载、搜索和过滤操作。
  """

  require Logger
  require Ash.Query
  alias RacingGame.SystemCommunication
  alias RacingGame.Utils.DynamicPagination
  alias RacingGame.Live.AdminPanel.Mappings.TypeMappings
  import Phoenix.Component, only: [assign: 3]
  import Phoenix.LiveView, only: [put_flash: 3]

  @doc """
  加载通信数据

  ## 参数
  - `socket` - Phoenix LiveView socket

  ## 返回
  - `socket` - 更新后的socket，包含加载的数据
  """
  def load_communications(socket) do
    Logger.info("📊 [数据加载] 开始加载通信数据")

    try do
      search_communications_from_database(socket)
    rescue
      error ->
        Logger.error("❌ [数据加载] 加载通信数据时发生错误: #{inspect(error)}")

        socket
        |> assign(:communications, [])
        |> assign(:total_count, 0)
        |> assign(:loading, false)
        |> put_flash(:error, "加载数据时发生错误，请稍后重试")
    end
  end

  @doc """
  执行带过滤器的搜索

  ## 参数
  - `socket` - Phoenix LiveView socket
  - `query` - 搜索查询字符串

  ## 返回
  - `{:noreply, socket}` - 更新后的socket
  """
  def perform_search_with_filters(socket, query) do
    Logger.info("🔍 [搜索操作] 执行搜索，查询: #{inspect(query)}")

    socket =
      socket
      |> assign(:search_query, query)
      |> assign(:page, 1)
      |> load_communications()
      |> put_flash(:info, "搜索完成")

    {:noreply, socket}
  end

  # 私有函数 - 从数据库搜索通信数据
  defp search_communications_from_database(socket) do
    search_params = build_search_params(socket)

    Logger.debug("🔍 [搜索操作] 搜索参数: #{inspect(search_params)}")

    # 构建查询参数
    query_args = %{
      type_filter: search_params.type,
      status_filter: search_params.status,
      search_query: search_params.query,
      page: search_params.page,
      per_page: search_params.per_page
    }

    case SystemCommunication.list_with_filters(query_args) do
      {:ok, %{results: communications, count: total_count}} ->
        Logger.info("✅ [搜索操作] 成功加载 #{length(communications)} 条记录，总计 #{total_count} 条")

        formatted_communications = Enum.map(communications, &communication_to_display_format/1)

        socket
        |> assign(:communications, formatted_communications)
        |> assign(:total_count, total_count)
        |> assign(:loading, false)

      {:error, error} ->
        Logger.error("❌ [搜索操作] 搜索失败: #{inspect(error)}")

        socket
        |> assign(:communications, [])
        |> assign(:total_count, 0)
        |> assign(:loading, false)
        |> put_flash(:error, "搜索失败，请稍后重试")
    end
  end

  # 私有函数 - 构建搜索参数
  defp build_search_params(socket) do
    %{
      query: socket.assigns.search_query || "",
      type: normalize_type_filter(socket.assigns.selected_type),
      status: normalize_status_filter(socket.assigns.selected_status),
      page: socket.assigns.page || 1,
      per_page: socket.assigns.per_page || 10,
      sort_by: socket.assigns[:sort_by] || :updated_at,
      sort_order: socket.assigns[:sort_order] || :desc
    }
  end

  # 规范化类型过滤器
  defp normalize_type_filter(type) do
    case type do
      nil -> :all
      "all" -> :all
      type when is_binary(type) -> String.to_atom(type)
      type when is_atom(type) -> type
    end
  end

  # 规范化状态过滤器
  defp normalize_status_filter(status) do
    case status do
      value when value in [nil, "all"] -> :all
      value when value in [true, "true"] -> :active
      value when value in [false, "false"] -> :inactive
      status when is_atom(status) -> status
    end
  end

  # 私有函数 - 通信记录转换为显示格式
  defp communication_to_display_format(communication) do
    base_data = extract_base_data(communication)
    type_data = extract_type_data(communication)
    priority_data = extract_priority_data(communication)
    status_data = extract_status_data(communication)
    recipient_data = extract_recipient_data(communication)
    time_data = extract_time_data(communication)

    Map.merge(base_data, type_data)
    |> Map.merge(priority_data)
    |> Map.merge(status_data)
    |> Map.merge(recipient_data)
    |> Map.merge(time_data)
  end

  # 提取基础数据
  defp extract_base_data(communication) do
    %{
      id: communication.id,
      title: communication.title,
      content: communication.content
    }
  end

  # 提取类型相关数据
  defp extract_type_data(communication) do
    %{
      type: communication.type,
      type_display: TypeMappings.get_type_display_name(communication.type),
      type_icon: TypeMappings.get_type_icon(communication.type),
      type_color_class: TypeMappings.get_type_color_class(communication.type)
    }
  end

  # 提取优先级相关数据
  defp extract_priority_data(communication) do
    %{
      priority: communication.priority,
      priority_display: TypeMappings.get_priority_display_name(communication.priority),
      priority_badge_class: TypeMappings.get_priority_badge_class(communication.priority)
    }
  end

  # 提取状态相关数据
  defp extract_status_data(communication) do
    active = communication.active
    %{
      active: active,
      status_display: if(active, do: "启用", else: "禁用"),
      status_badge_class: get_status_badge_class(active)
    }
  end

  # 提取接收者相关数据
  defp extract_recipient_data(communication) do
    %{
      recipient_type: communication.recipient_type,
      recipient_id: communication.recipient_id,
      recipient_name: get_recipient_name(communication.recipient_id)
    }
  end

  # 提取时间相关数据
  defp extract_time_data(communication) do
    %{
      expires_at: communication.expires_at,
      inserted_at: communication.inserted_at,
      updated_at: communication.updated_at,
      created_time: format_time(communication.inserted_at),
      updated_time: format_time(communication.updated_at)
    }
  end

  # 获取状态徽章样式
  defp get_status_badge_class(true), do: "bg-green-100 text-green-800 border-green-200"
  defp get_status_badge_class(false), do: "bg-red-100 text-red-800 border-red-200"

  # 私有函数 - 获取接收者名称
  defp get_recipient_name(nil), do: nil
  defp get_recipient_name(_recipient_id) do
    # TODO: 实现根据recipient_id获取用户名称的逻辑
    # 这里可以调用用户服务来获取用户名称
    # 暂时返回占位符
    "用户名称"
  end

  # 私有函数 - 格式化时间
  defp format_time(nil), do: "-"
  defp format_time(datetime) do
    case Cypridina.Utils.TimeHelper.format_local_datetime(datetime) do
      formatted when is_binary(formatted) -> formatted
      _ -> format_fallback_time(datetime)
    end
  rescue
    _ -> format_fallback_time(datetime)
  end

  # 私有函数 - 备用时间格式化
  defp format_fallback_time(datetime) do
    try do
      datetime
      |> DateTime.to_date()
      |> Date.to_string()
    rescue
      _ -> "无效时间"
    end
  end
end
