defmodule Cypridina.Ledger.AccountTest do
  use <PERSON><PERSON><PERSON><PERSON>.DataCase, async: true

  alias <PERSON><PERSON><PERSON><PERSON>.Accounts.User
  alias <PERSON><PERSON><PERSON><PERSON>.Ledger.Account

  describe "account creation" do
    test "open/1 creates user account" do
      {:ok, user} = User.create_guest_user()

      # Create account manually
      assert {:ok, account} = Account.open(%{
        account_type: :user,
        user_id: user.id,
        currency: :XAA,
        description: "测试账户"
      })

      assert account.account_type == :user
      assert account.user_id == user.id
      assert account.currency == :XAA
      assert account.description == "测试账户"
      assert account.is_active == true
    end

    test "open/1 creates system account" do
      assert {:ok, account} = Account.open(%{
        account_type: :system,
        system_account_type: :rewards,
        currency: :XAA,
        description: "奖励系统账户"
      })

      assert account.account_type == :system
      assert account.system_account_type == :rewards
      assert account.user_id == nil
    end
  end

  describe "account queries" do
    test "by_user_id/1 finds user account" do
      {:ok, user} = User.create_guest_user()

      # Account should be automatically created
      assert account = Account.by_user_id!(user.id)
      assert account.user_id == user.id
      assert account.account_type == :user
    end

    test "by_system_type/1 finds system account" do
      # Create system account
      {:ok, account} = Account.open(%{
        account_type: :system,
        system_account_type: :main,
        currency: :XAA
      })

      # Find by system type
      found_account = Account.by_system_type!(:main)
      assert found_account.id == account.id
      assert found_account.system_account_type == :main
    end
  end

  describe "account management" do
    test "activate/1 and deactivate/1" do
      {:ok, user} = User.create_guest_user()
      account = Account.by_user_id!(user.id)

      # Account should be active by default
      assert account.is_active == true

      # Deactivate account
      {:ok, deactivated} = Account.deactivate(account)
      assert deactivated.is_active == false

      # Reactivate account
      {:ok, reactivated} = Account.activate(deactivated)
      assert reactivated.is_active == true
    end
  end

  describe "account identities" do
    test "user_account identity prevents duplicate user accounts" do
      {:ok, user} = User.create_guest_user()

      # First account creation should succeed (automatic)
      account1 = Account.by_user_id!(user.id)
      assert account1.user_id == user.id

      # Attempting to create another user account for same user should fail
      assert_raise Ash.Error.Invalid, fn ->
        Account.open!(%{
          account_type: :user,
          user_id: user.id,
          currency: :XAA
        })
      end
    end
  end

  describe "account relationships" do
    test "account belongs_to user" do
      {:ok, user} = User.create_guest_user()
      account = Account.by_user_id!(user.id)

      # Load user relationship
      account_with_user = Account.read!(account.id, load: [:user])
      assert account_with_user.user.id == user.id
      assert account_with_user.user.username == user.username
    end
  end
end
