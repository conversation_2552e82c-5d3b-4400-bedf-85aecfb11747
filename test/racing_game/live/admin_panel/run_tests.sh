#!/bin/bash

# 🧪 Teen系统测试运行脚本
# 提供简单的命令行接口来运行各种测试套件

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="/app/cypridina"

# 测试目录
TEST_DIR="$PROJECT_ROOT/test/racing_game/live/admin_panel"

# 日志目录
LOG_DIR="$TEST_DIR/logs"
REPORT_DIR="$TEST_DIR/reports"

# 创建必要的目录
mkdir -p "$LOG_DIR"
mkdir -p "$REPORT_DIR"

# 显示帮助信息
show_help() {
    echo -e "${CYAN}🧪 Teen系统测试运行器${NC}"
    echo -e "${CYAN}================================${NC}"
    echo ""
    echo -e "${YELLOW}用法:${NC}"
    echo "  $0 [命令] [选项]"
    echo ""
    echo -e "${YELLOW}命令:${NC}"
    echo -e "  ${GREEN}quick${NC}        运行快速测试（基础功能验证）"
    echo -e "  ${GREEN}integration${NC}  运行集成测试（不包括性能测试）"
    echo -e "  ${GREEN}performance${NC}  运行性能和负载测试"
    echo -e "  ${GREEN}all${NC}          运行所有测试套件"
    echo -e "  ${GREEN}repository${NC}   只运行Repository层测试"
    echo -e "  ${GREEN}querybuilder${NC} 只运行QueryBuilder层测试"
    echo -e "  ${GREEN}service${NC}      只运行Service层测试"
    echo -e "  ${GREEN}cross${NC}        只运行跨系统集成测试"
    echo -e "  ${GREEN}help${NC}         显示此帮助信息"
    echo ""
    echo -e "${YELLOW}选项:${NC}"
    echo -e "  ${BLUE}--verbose${NC}    显示详细输出"
    echo -e "  ${BLUE}--trace${NC}      显示测试执行跟踪"
    echo -e "  ${BLUE}--timeout N${NC}  设置测试超时时间（秒）"
    echo -e "  ${BLUE}--parallel N${NC} 设置并行测试数量"
    echo ""
    echo -e "${YELLOW}示例:${NC}"
    echo "  $0 quick --verbose"
    echo "  $0 integration --timeout 120"
    echo "  $0 performance --parallel 10"
    echo "  $0 all --trace"
}

# 记录日志
log() {
    local level=$1
    shift
    local message="$@"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" >> "$LOG_DIR/test_runner.log"
    
    case $level in
        "INFO")  echo -e "${GREEN}[INFO]${NC} $message" ;;
        "WARN")  echo -e "${YELLOW}[WARN]${NC} $message" ;;
        "ERROR") echo -e "${RED}[ERROR]${NC} $message" ;;
        "DEBUG") echo -e "${PURPLE}[DEBUG]${NC} $message" ;;
        *)       echo -e "${CYAN}[$level]${NC} $message" ;;
    esac
}

# 检查环境
check_environment() {
    log "INFO" "检查测试环境..."
    
    # 检查是否在正确的目录
    if [ ! -f "$PROJECT_ROOT/mix.exs" ]; then
        log "ERROR" "未找到mix.exs文件，请确保在正确的项目目录中运行"
        exit 1
    fi
    
    # 检查Elixir是否安装
    if ! command -v elixir &> /dev/null; then
        log "ERROR" "未找到Elixir，请先安装Elixir"
        exit 1
    fi
    
    # 检查Mix是否可用
    if ! command -v mix &> /dev/null; then
        log "ERROR" "未找到Mix，请确保Elixir正确安装"
        exit 1
    fi
    
    log "INFO" "环境检查通过"
}

# 运行单个测试文件
run_test_file() {
    local test_file=$1
    local test_name=$2
    local options=$3

    log "INFO" "运行测试: $test_name"
    log "DEBUG" "测试文件: $test_file"
    log "DEBUG" "测试选项: $options"

    local start_time=$(date +%s)
    local log_file="$LOG_DIR/${test_name}_$(date +%Y%m%d_%H%M%S).log"

    # 切换到项目根目录
    cd "$PROJECT_ROOT"

    # 过滤掉不支持的选项，只保留mix test支持的选项
    local mix_options=""
    for option in $options; do
        case $option in
            --timeout*)
                mix_options="$mix_options $option"
                ;;
            --only*)
                mix_options="$mix_options $option"
                ;;
            --exclude*)
                mix_options="$mix_options $option"
                ;;
            --parallel*)
                mix_options="$mix_options $option"
                ;;
            --trace)
                mix_options="$mix_options $option"
                ;;
            --verbose)
                # --verbose 不是mix test的选项，忽略
                ;;
            *)
                # 其他选项也传递给mix test
                mix_options="$mix_options $option"
                ;;
        esac
    done

    # 运行测试
    if mix test "$test_file" $mix_options > "$log_file" 2>&1; then
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        log "INFO" "✅ $test_name 测试通过 (${duration}s)"
        return 0
    else
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        log "ERROR" "❌ $test_name 测试失败 (${duration}s)"
        log "ERROR" "详细日志: $log_file"

        # 显示最后几行错误信息
        echo -e "${RED}最近的错误信息:${NC}"
        tail -10 "$log_file"
        return 1
    fi
}

# 运行快速测试
run_quick_tests() {
    local options=$1
    log "INFO" "🚀 开始运行快速测试..."
    
    local failed=0
    
    # Repository层基础测试
    if ! run_test_file "$TEST_DIR/repositories/repository_integration_test.exs" "Repository层基础测试" "$options --only basic"; then
        ((failed++))
    fi
    
    # Service层基础测试  
    if ! run_test_file "$TEST_DIR/services/service_integration_test.exs" "Service层基础测试" "$options --only basic"; then
        ((failed++))
    fi
    
    if [ $failed -eq 0 ]; then
        log "INFO" "🎉 快速测试全部通过！"
        return 0
    else
        log "ERROR" "❌ $failed 个快速测试失败"
        return 1
    fi
}

# 运行集成测试
run_integration_tests() {
    local options=$1
    log "INFO" "🔗 开始运行集成测试..."
    
    local failed=0
    local tests=(
        "$TEST_DIR/repositories/repository_integration_test.exs:Repository层测试"
        "$TEST_DIR/query_builders/query_builder_integration_test.exs:QueryBuilder层测试"
        "$TEST_DIR/services/service_integration_test.exs:Service层测试"
        "$TEST_DIR/cross_system_integration_test.exs:跨系统集成测试"
    )
    
    for test_info in "${tests[@]}"; do
        IFS=':' read -r test_file test_name <<< "$test_info"
        if [ -f "$test_file" ]; then
            if ! run_test_file "$test_file" "$test_name" "$options --exclude performance"; then
                ((failed++))
            fi
        else
            log "WARN" "测试文件不存在: $test_file"
        fi
    done
    
    if [ $failed -eq 0 ]; then
        log "INFO" "🎉 集成测试全部通过！"
        return 0
    else
        log "ERROR" "❌ $failed 个集成测试失败"
        return 1
    fi
}

# 运行性能测试
run_performance_tests() {
    local options=$1
    log "INFO" "⚡ 开始运行性能测试..."
    
    # 性能测试需要更长的超时时间
    local perf_options="$options --timeout 120000"
    
    if run_test_file "$TEST_DIR/performance_load_test.exs" "性能负载测试" "$perf_options"; then
        log "INFO" "🎉 性能测试通过！"
        return 0
    else
        log "ERROR" "❌ 性能测试失败"
        return 1
    fi
}

# 运行所有测试
run_all_tests() {
    local options=$1
    log "INFO" "🚀 开始运行完整测试套件..."
    
    local start_time=$(date +%s)
    local failed=0
    
    # 运行集成测试
    if ! run_integration_tests "$options"; then
        ((failed++))
    fi
    
    # 运行性能测试
    if ! run_performance_tests "$options"; then
        ((failed++))
    fi
    
    local end_time=$(date +%s)
    local total_duration=$((end_time - start_time))
    
    # 生成测试报告
    generate_test_report $failed $total_duration
    
    if [ $failed -eq 0 ]; then
        log "INFO" "🎉 所有测试都通过了！总耗时: ${total_duration}s"
        return 0
    else
        log "ERROR" "❌ $failed 个测试套件失败，总耗时: ${total_duration}s"
        return 1
    fi
}

# 生成测试报告
generate_test_report() {
    local failed_count=$1
    local duration=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local report_file="$REPORT_DIR/test_report_$(date +%Y%m%d_%H%M%S).json"
    
    log "INFO" "生成测试报告: $report_file"
    
    cat > "$report_file" << EOF
{
  "timestamp": "$timestamp",
  "duration_seconds": $duration,
  "failed_suites": $failed_count,
  "status": "$([ $failed_count -eq 0 ] && echo "PASSED" || echo "FAILED")",
  "log_directory": "$LOG_DIR",
  "report_directory": "$REPORT_DIR"
}
EOF
    
    log "INFO" "📊 测试报告已生成"
}

# 主函数
main() {
    local command=${1:-help}
    local options=""
    
    # 解析选项
    shift
    while [[ $# -gt 0 ]]; do
        case $1 in
            --verbose)
                options="$options --verbose"
                shift
                ;;
            --trace)
                options="$options --trace"
                shift
                ;;
            --timeout)
                options="$options --timeout $2"
                shift 2
                ;;
            --parallel)
                options="$options --max-cases $2"
                shift 2
                ;;
            *)
                log "WARN" "未知选项: $1"
                shift
                ;;
        esac
    done
    
    # 检查环境
    check_environment
    
    # 执行命令
    case $command in
        quick)
            run_quick_tests "$options"
            ;;
        integration)
            run_integration_tests "$options"
            ;;
        performance)
            run_performance_tests "$options"
            ;;
        all)
            run_all_tests "$options"
            ;;
        repository)
            run_test_file "$TEST_DIR/repositories/repository_integration_test.exs" "Repository层测试" "$options"
            ;;
        querybuilder)
            run_test_file "$TEST_DIR/query_builders/query_builder_integration_test.exs" "QueryBuilder层测试" "$options"
            ;;
        service)
            run_test_file "$TEST_DIR/services/service_integration_test.exs" "Service层测试" "$options"
            ;;
        cross)
            run_test_file "$TEST_DIR/cross_system_integration_test.exs" "跨系统集成测试" "$options"
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log "ERROR" "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
