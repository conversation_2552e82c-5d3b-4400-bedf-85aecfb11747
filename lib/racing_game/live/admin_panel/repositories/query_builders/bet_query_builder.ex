defmodule RacingGame.Live.AdminPanel.Repositories.QueryBuilders.BetQueryBuilder do
  @moduledoc """
  下注记录查询构建器

  提供复杂下注查询的构建功能：
  - 动态查询条件构建
  - 用户和比赛过滤
  - 状态和金额过滤
  - 统计查询构建
  """

  require Logger
  alias RacingGame.Bet
  alias Cypridina.Accounts.User
  require Ash.Query

  # 常量定义
  @filterable_fields [:user_id, :race_issue, :selection, :amount, :status, :payout, :inserted_at, :updated_at]
  @sortable_fields [:amount, :payout, :status, :inserted_at, :updated_at]
  @bet_statuses %{0 => :pending, 1 => :won, 2 => :lost}
  @status_codes %{pending: 0, won: 1, lost: 2}

  # ============================================================================
  # 公共API
  # ============================================================================

  @doc """
  构建列表查询

  ## 参数
  - `params` - 查询参数
    - `:user_id` - 用户ID过滤
    - `:race_issue` - 比赛期号过滤
    - `:status` - 状态过滤
    - `:amount_range` - 金额范围过滤
    - `:date_from` - 开始日期
    - `:date_to` - 结束日期
    - `:filters` - 其他过滤条件
    - `:sort` - 排序条件

  ## 返回
  - `{:ok, query}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_list_query(params) do
    Logger.debug("🔨 [下注查询构建器] 构建下注列表查询")

    try do
      query = Bet
      |> apply_user_filter(params[:user_id])
      |> apply_race_issue_filter(params[:race_issue])
      |> apply_status_filter(params[:status])
      |> apply_amount_range_filter(params[:amount_range])
      |> apply_date_range_filter(params[:date_from], params[:date_to])
      |> apply_filter_conditions(params[:filters])
      |> apply_sort_conditions(params[:sort])

      {:ok, query}
    rescue
      error ->
        Logger.error("❌ [下注查询构建器] 构建查询失败: #{inspect(error)}")
        {:error, "查询构建失败"}
    end
  end

  @doc """
  构建用户下注查询

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - 查询对象
  """
  def build_user_bets_query(user_id, options \\ []) do
    Logger.debug("👤 [下注查询构建器] 构建用户下注查询: #{user_id}")

    Bet
    |> Ash.Query.filter(user_id == ^user_id)
    |> apply_status_filter(options[:status])
    |> apply_race_issue_filter(options[:race_issue])
    |> apply_date_range_filter(options[:date_from], options[:date_to])
    |> Ash.Query.sort(inserted_at: :desc)
  end

  @doc """
  构建比赛下注查询

  ## 参数
  - `race_issue` - 比赛期号
  - `options` - 选项

  ## 返回
  - 查询对象
  """
  def build_race_bets_query(race_issue, options \\ []) do
    Logger.debug("🏁 [下注查询构建器] 构建比赛下注查询: #{race_issue}")

    Bet
    |> Ash.Query.filter(race_issue == ^race_issue)
    |> apply_status_filter(options[:status])
    |> apply_user_filter(options[:user_id])
    |> Ash.Query.load([:user])
    |> Ash.Query.sort(amount: :desc, inserted_at: :desc)
  end

  @doc """
  构建统计查询

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, query}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_statistics_query(params \\ %{}) do
    Logger.debug("📊 [下注查询构建器] 构建统计查询")

    try do
      query = Bet
      |> apply_user_filter(params[:user_id])
      |> apply_race_issue_filter(params[:race_issue])
      |> apply_status_filter(params[:status])
      |> apply_date_range_filter(params[:date_from], params[:date_to])
      |> apply_filter_conditions(params[:filters])

      {:ok, query}
    rescue
      error ->
        Logger.error("❌ [下注查询构建器] 构建统计查询失败: #{inspect(error)}")
        {:error, "统计查询构建失败"}
    end
  end

  @doc """
  构建热门选择查询

  ## 参数
  - `race_issue` - 比赛期号（可选）
  - `options` - 选项

  ## 返回
  - 查询对象
  """
  def build_popular_selections_query(race_issue \\ nil, options \\ []) do
    Logger.debug("🔥 [下注查询构建器] 构建热门选择查询")

    query = Bet
    |> apply_race_issue_filter(race_issue)
    |> apply_date_range_filter(options[:date_from], options[:date_to])

    # TODO: 添加按选择分组的聚合查询
    query
  end

  @doc """
  构建用户搜索查询

  ## 参数
  - `search_term` - 搜索词（用户名）
  - `options` - 选项

  ## 返回
  - 查询对象
  """
  def build_user_search_query(search_term, options \\ []) do
    Logger.debug("🔍 [下注查询构建器] 构建用户搜索查询: #{search_term}")

    # 首先查找匹配的用户
    matching_user_ids = get_matching_user_ids(search_term)

    if length(matching_user_ids) > 0 do
      Bet
      |> Ash.Query.filter(user_id in ^matching_user_ids)
      |> apply_status_filter(options[:status])
      |> apply_date_range_filter(options[:date_from], options[:date_to])
      |> Ash.Query.load([:user])
      |> Ash.Query.sort(inserted_at: :desc)
    else
      # 返回空结果查询
      Bet
      |> Ash.Query.filter(user_id == "no-match-found")
    end
  end

  # ============================================================================
  # 过滤条件应用
  # ============================================================================

  # 应用用户过滤
  defp apply_user_filter(query, nil), do: query
  defp apply_user_filter(query, user_id) when is_binary(user_id) do
    Ash.Query.filter(query, user_id == ^user_id)
  end
  defp apply_user_filter(query, user_ids) when is_list(user_ids) do
    Ash.Query.filter(query, user_id in ^user_ids)
  end

  # 应用比赛期号过滤
  defp apply_race_issue_filter(query, nil), do: query
  defp apply_race_issue_filter(query, race_issue) when is_binary(race_issue) do
    Ash.Query.filter(query, race_issue == ^race_issue)
  end
  defp apply_race_issue_filter(query, race_issues) when is_list(race_issues) do
    Ash.Query.filter(query, race_issue in ^race_issues)
  end

  # 应用状态过滤
  defp apply_status_filter(query, nil), do: query
  defp apply_status_filter(query, status) when is_integer(status) and status in [0, 1, 2] do
    Ash.Query.filter(query, status == ^status)
  end
  defp apply_status_filter(query, status) when is_atom(status) do
    case Map.get(@status_codes, status) do
      nil ->
        Logger.warning("⚠️ [下注查询构建器] 无效的状态过滤: #{status}")
        query
      status_code ->
        Ash.Query.filter(query, status == ^status_code)
    end
  end
  defp apply_status_filter(query, status) when is_binary(status) do
    try do
      atom_status = String.to_existing_atom(status)
      apply_status_filter(query, atom_status)
    rescue
      ArgumentError ->
        Logger.warning("⚠️ [下注查询构建器] 无效的状态过滤: #{status}")
        query
    end
  end
  defp apply_status_filter(query, statuses) when is_list(statuses) do
    status_codes = Enum.map(statuses, fn status ->
      case status do
        code when is_integer(code) and code in [0, 1, 2] -> code
        atom when is_atom(atom) -> Map.get(@status_codes, atom)
        _ -> nil
      end
    end)
    |> Enum.reject(&is_nil/1)

    if length(status_codes) > 0 do
      Ash.Query.filter(query, status in ^status_codes)
    else
      query
    end
  end

  # 应用金额范围过滤
  defp apply_amount_range_filter(query, nil), do: query
  defp apply_amount_range_filter(query, %{min: min_amount, max: max_amount}) do
    query = if min_amount do
      Ash.Query.filter(query, amount >= ^min_amount)
    else
      query
    end

    if max_amount do
      Ash.Query.filter(query, amount <= ^max_amount)
    else
      query
    end
  end
  defp apply_amount_range_filter(query, %{min: min_amount}) do
    apply_amount_range_filter(query, %{min: min_amount, max: nil})
  end
  defp apply_amount_range_filter(query, %{max: max_amount}) do
    apply_amount_range_filter(query, %{min: nil, max: max_amount})
  end

  # 应用日期范围过滤
  defp apply_date_range_filter(query, nil, nil), do: query
  defp apply_date_range_filter(query, from_date, nil) do
    Ash.Query.filter(query, inserted_at >= ^from_date)
  end
  defp apply_date_range_filter(query, nil, to_date) do
    Ash.Query.filter(query, inserted_at <= ^to_date)
  end
  defp apply_date_range_filter(query, from_date, to_date) do
    query
    |> Ash.Query.filter(inserted_at >= ^from_date)
    |> Ash.Query.filter(inserted_at <= ^to_date)
  end

  # 应用其他过滤条件
  defp apply_filter_conditions(query, nil), do: query
  defp apply_filter_conditions(query, filters) when map_size(filters) == 0, do: query
  defp apply_filter_conditions(query, filters) do
    Enum.reduce(filters, query, fn {field, value}, acc_query ->
      apply_single_filter(acc_query, field, value)
    end)
  end

  # 应用单个过滤条件
  defp apply_single_filter(query, field, value) when field in @filterable_fields do
    case {field, value} do
      {:selection, selection} when is_binary(selection) ->
        Ash.Query.filter(query, selection == ^selection)

      {:amount, amount} when is_integer(amount) ->
        Ash.Query.filter(query, amount == ^amount)

      {:payout, payout} when is_integer(payout) ->
        Ash.Query.filter(query, payout == ^payout)

      {:inserted_at, %{from: from_date, to: to_date}} ->
        apply_date_range_filter(query, from_date, to_date)

      {:updated_at, %{from: from_date, to: to_date}} ->
        query
        |> Ash.Query.filter(updated_at >= ^from_date)
        |> Ash.Query.filter(updated_at <= ^to_date)

      _ ->
        Logger.warning("⚠️ [下注查询构建器] 忽略无效过滤条件: #{field} = #{inspect(value)}")
        query
    end
  end
  defp apply_single_filter(query, field, _value) do
    Logger.warning("⚠️ [下注查询构建器] 忽略不支持的过滤字段: #{field}")
    query
  end

  # ============================================================================
  # 排序条件应用
  # ============================================================================

  # 应用排序条件
  defp apply_sort_conditions(query, nil), do: Ash.Query.sort(query, inserted_at: :desc)
  defp apply_sort_conditions(query, []), do: Ash.Query.sort(query, inserted_at: :desc)
  defp apply_sort_conditions(query, sort_conditions) when is_list(sort_conditions) do
    validated_sorts = validate_sort_conditions(sort_conditions)

    case validated_sorts do
      [] -> Ash.Query.sort(query, inserted_at: :desc)
      sorts -> Ash.Query.sort(query, sorts)
    end
  end
  defp apply_sort_conditions(query, sort_condition) do
    apply_sort_conditions(query, [sort_condition])
  end

  # 验证排序条件
  defp validate_sort_conditions(sort_conditions) do
    Enum.reduce(sort_conditions, [], fn sort_condition, acc ->
      case validate_single_sort_condition(sort_condition) do
        {:ok, validated_sort} -> [validated_sort | acc]
        :error -> acc
      end
    end)
    |> Enum.reverse()
  end

  # 验证单个排序条件
  defp validate_single_sort_condition({field, direction})
       when field in @sortable_fields and direction in [:asc, :desc] do
    {:ok, {field, direction}}
  end
  defp validate_single_sort_condition(field) when field in @sortable_fields do
    {:ok, {field, :asc}}
  end
  defp validate_single_sort_condition(sort_condition) do
    Logger.warning("⚠️ [下注查询构建器] 忽略无效排序条件: #{inspect(sort_condition)}")
    :error
  end

  # ============================================================================
  # 辅助函数
  # ============================================================================

  # 获取匹配的用户ID
  defp get_matching_user_ids(search_term) do
    try do
      case User
           |> Ash.Query.filter(contains(username, ^search_term))
           |> Ash.Query.select([:id])
           |> Ash.read() do
        {:ok, users} -> Enum.map(users, & &1.id)
        {:error, error} ->
          Logger.warning("⚠️ [下注查询构建器] 搜索用户失败: #{inspect(error)}")
          []
      end
    rescue
      error ->
        Logger.error("❌ [下注查询构建器] 搜索用户异常: #{inspect(error)}")
        []
    end
  end

  # ============================================================================
  # 查询优化
  # ============================================================================

  @doc """
  优化查询性能

  ## 参数
  - `query` - 查询对象
  - `options` - 优化选项

  ## 返回
  - 优化后的查询对象
  """
  def optimize_query(query, options \\ []) do
    Logger.debug("⚡ [下注查询构建器] 优化查询性能")

    query
    |> maybe_add_index_hints(options)
    |> maybe_limit_results(options)
    |> maybe_select_fields(options)
  end

  # 可能添加索引提示
  defp maybe_add_index_hints(query, _options) do
    # TODO: 根据查询条件添加索引提示
    query
  end

  # 可能限制结果数量
  defp maybe_limit_results(query, options) do
    case Keyword.get(options, :max_results) do
      nil -> query
      max_results when is_integer(max_results) and max_results > 0 ->
        Ash.Query.limit(query, max_results)
      _ -> query
    end
  end

  # 可能选择特定字段
  defp maybe_select_fields(query, options) do
    case Keyword.get(options, :select_fields) do
      nil -> query
      fields when is_list(fields) ->
        Ash.Query.select(query, fields)
      _ -> query
    end
  end
end
