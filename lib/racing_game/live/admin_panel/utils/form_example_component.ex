defmodule Cypridina.Utils.FormExampleComponent do
  @moduledoc """
  表单组件使用示例

  展示如何使用 FormModalComponent 和 FormBuilder 创建各种类型的表单
  """
  use CypridinaWeb, :live_component

  alias Cypridina.Utils.{FormModalComponent, FormBuilder}
  alias CypridinaWeb.Components.AdminButtonGroup

  def update(assigns, socket) do
    socket =
      socket
      |> assign(assigns)
      |> assign(:show_user_form, false)
      |> assign(:show_product_form, false)
      |> assign(:user_form_config, nil)
      |> assign(:product_form_config, nil)

    {:ok, socket}
  end

  # 显示用户表单
  def handle_event("show_user_form", _params, socket) do
    form_config = build_user_form()

    socket =
      socket
      |> assign(:show_user_form, true)
      |> assign(:user_form_config, form_config)

    {:noreply, socket}
  end

  # 显示产品表单
  def handle_event("show_product_form", _params, socket) do
    form_config = build_product_form()

    socket =
      socket
      |> assign(:show_product_form, true)
      |> assign(:product_form_config, form_config)

    {:noreply, socket}
  end

  # 处理用户表单模态框事件
  def handle_info({:form_modal_event, :hide_modal, _params}, socket) do
    socket =
      socket
      |> assign(:show_user_form, false)
      |> assign(:show_product_form, false)
      |> assign(:user_form_config, nil)
      |> assign(:product_form_config, nil)
      |> put_flash(:info, "表单已关闭")

    {:noreply, socket}
  end

  def handle_info({:form_modal_event, :submit_form, params}, socket) do
    # 模拟保存操作
    IO.inspect(params, label: "表单数据")

    socket =
      socket
      |> put_flash(:info, "表单提交成功！数据已保存到控制台")
      |> assign(:show_user_form, false)
      |> assign(:show_product_form, false)
      |> assign(:user_form_config, nil)
      |> assign(:product_form_config, nil)

    {:noreply, socket}
  end

  # 构建用户表单
  defp build_user_form do
    FormBuilder.new_form(%{
      title: "创建用户",
      subtitle: "请填写用户基本信息",
      icon: "fas fa-user-plus",
      theme: "blue",
      submit_text: "创建用户"
    })
    |> FormBuilder.add_field_group("基本信息", icon: "fas fa-info-circle")
    |> FormBuilder.add_text_field("user[name]", "用户名",
         icon: "fas fa-user",
         placeholder: "请输入用户名",
         required: true,
         maxlength: 50,
         help_text: "用户名将用于登录")
    |> FormBuilder.add_text_field("user[email]", "邮箱地址",
         icon: "fas fa-envelope",
         placeholder: "<EMAIL>",
         required: true,
         input_type: "email",
         help_text: "请输入有效的邮箱地址")
    |> FormBuilder.add_field_group("详细信息", icon: "fas fa-address-card", grid: "grid-cols-1 md:grid-cols-2")
    |> FormBuilder.add_text_field("user[phone]", "手机号码",
         icon: "fas fa-phone",
         placeholder: "请输入手机号",
         maxlength: 11)
    |> FormBuilder.add_select_field("user[role]", "用户角色",
         [
           %{value: "user", label: "普通用户"},
           %{value: "admin", label: "管理员"},
           %{value: "moderator", label: "版主"}
         ],
         icon: "fas fa-user-tag",
         value: "user")
    |> FormBuilder.add_field_group("其他设置", icon: "fas fa-cogs")
    |> FormBuilder.add_checkbox_field("user[active]", "立即激活账户",
         checked: true,
         help_text: "勾选后用户可以立即登录")
    |> FormBuilder.add_textarea_field("user[bio]", "个人简介",
         icon: "fas fa-align-left",
         placeholder: "请输入个人简介...",
         rows: 3,
         maxlength: 500,
         help_text: "最多500个字符")
  end

  # 构建产品表单
  defp build_product_form do
    FormBuilder.new_form(%{
      title: "添加产品",
      subtitle: "请填写产品详细信息",
      icon: "fas fa-box",
      theme: "green",
      submit_text: "添加产品"
    })
    |> FormBuilder.add_field_group("产品信息", icon: "fas fa-info-circle")
    |> FormBuilder.add_text_field("product[name]", "产品名称",
         icon: "fas fa-tag",
         placeholder: "请输入产品名称",
         required: true,
         maxlength: 100)
    |> FormBuilder.add_text_field("product[price]", "价格",
         icon: "fas fa-dollar-sign",
         placeholder: "0.00",
         required: true,
         input_type: "number")
    |> FormBuilder.add_field_group("分类和状态", icon: "fas fa-layer-group", grid: "grid-cols-1 md:grid-cols-2")
    |> FormBuilder.add_select_field("product[category]", "产品分类",
         [
           %{value: "electronics", label: "电子产品"},
           %{value: "clothing", label: "服装"},
           %{value: "books", label: "图书"},
           %{value: "home", label: "家居用品"}
         ],
         icon: "fas fa-list")
    |> FormBuilder.add_select_field("product[status]", "产品状态",
         [
           %{value: "draft", label: "草稿"},
           %{value: "active", label: "上架"},
           %{value: "inactive", label: "下架"}
         ],
         icon: "fas fa-toggle-on",
         value: "draft")
    |> FormBuilder.add_field_group("详细描述", icon: "fas fa-align-left")
    |> FormBuilder.add_textarea_field("product[description]", "产品描述",
         placeholder: "请输入产品详细描述...",
         rows: 5,
         maxlength: 1000,
         help_text: "详细描述有助于用户了解产品")
    |> FormBuilder.add_field_group("其他选项", icon: "fas fa-cogs")
    |> FormBuilder.add_checkbox_field("product[featured]", "设为推荐产品",
         help_text: "推荐产品将在首页显示")
    |> FormBuilder.add_checkbox_field("product[notify_users]", "通知用户新产品",
         help_text: "向订阅用户发送新产品通知")
  end

  def render(assigns) do
    ~H"""
    <div class="p-6 bg-white rounded-lg shadow">
      <h2 class="text-2xl font-bold text-gray-900 mb-6">表单组件示例</h2>

      <div class="space-y-4">
        <div class="flex space-x-4">
          <AdminButtonGroup.admin_button
            text="创建用户表单"
            action="show_user_form"
            target={@myself}
            icon="fas fa-user-plus"
            class="bg-blue-600 text-white hover:bg-blue-700"
            bg_color="#2563eb"
          />

          <AdminButtonGroup.admin_button
            text="添加产品表单"
            action="show_product_form"
            target={@myself}
            icon="fas fa-box"
            class="bg-green-600 text-white hover:bg-green-700"
            bg_color="#16a34a"
          />
        </div>

        <div class="text-sm text-gray-600">
          <p>点击上面的按钮查看不同类型的表单示例。</p>
          <p>表单数据将在提交后显示在浏览器控制台中。</p>
        </div>
      </div>

      <!-- 用户表单模态框 -->
      <%= if @show_user_form and @user_form_config do %>
        <.live_component
          module={FormModalComponent}
          id="user_form_modal"
          config={@user_form_config.config}
          form_fields={@user_form_config.form_fields}
        />
      <% end %>

      <!-- 产品表单模态框 -->
      <%= if @show_product_form and @product_form_config do %>
        <.live_component
          module={FormModalComponent}
          id="product_form_modal"
          config={@product_form_config.config}
          form_fields={@product_form_config.form_fields}
        />
      <% end %>
    </div>
    """
  end
end
