defmodule RacingGame.Live.AdminPanel.TestHelper do
  @moduledoc """
  🧪 Teen系统集成测试辅助工具

  提供测试数据生成、Mock数据创建、测试环境配置等功能
  """

  alias Teen.{
    CustomerService,
    GameManagement,
    SystemSettings,
    PaymentSystem,
    ActivitySystem,
    DataStatistics,
    PromotionSystem
  }

  # ==================== 测试数据生成器 ====================

  @doc """
  生成测试用户数据
  """
  def generate_test_user(attrs \\ %{}) do
    base_attrs = %{
      username: "test_user_#{System.unique_integer([:positive])}",
      email: "test#{System.unique_integer([:positive])}@example.com",
      phone: "1380000#{:rand.uniform(9999) |> Integer.to_string() |> String.pad_leading(4, "0")}",
      status: 1,
      created_at: DateTime.utc_now(),
      updated_at: DateTime.utc_now()
    }
    
    Map.merge(base_attrs, attrs)
  end

  @doc """
  生成测试客服工单数据
  """
  def generate_test_ticket(attrs \\ %{}) do
    user_data = generate_test_user()
    
    base_attrs = %{
      user_id: user_data.id || :rand.uniform(10000),
      title: "测试工单_#{System.unique_integer([:positive])}",
      content: "这是一个测试工单内容",
      category: Enum.random(["account", "payment", "game", "technical"]),
      priority: Enum.random([1, 2, 3]),
      status: Enum.random([0, 1, 2, 3]),
      created_at: DateTime.utc_now(),
      updated_at: DateTime.utc_now()
    }
    
    Map.merge(base_attrs, attrs)
  end

  @doc """
  生成测试游戏平台数据
  """
  def generate_test_platform(attrs \\ %{}) do
    base_attrs = %{
      platform_name: "测试平台_#{System.unique_integer([:positive])}",
      platform_number: "P#{:rand.uniform(999999) |> Integer.to_string() |> String.pad_leading(6, "0")}",
      platform_type: Enum.random(["web", "mobile", "desktop"]),
      status: 1,
      agent_recharge_enabled: true,
      created_at: DateTime.utc_now(),
      updated_at: DateTime.utc_now()
    }
    
    Map.merge(base_attrs, attrs)
  end

  @doc """
  生成测试VIP等级数据
  """
  def generate_test_vip_level(attrs \\ %{}) do
    level = attrs[:level] || :rand.uniform(10)
    
    base_attrs = %{
      level: level,
      level_name: "VIP#{level}",
      min_recharge_amount: Decimal.new(level * 1000),
      max_recharge_amount: Decimal.new((level + 1) * 1000 - 1),
      privileges: Jason.encode!(%{
        "daily_bonus" => level * 10,
        "withdrawal_limit" => level * 5000,
        "customer_service_priority" => level > 5
      }),
      status: 1,
      created_at: DateTime.utc_now(),
      updated_at: DateTime.utc_now()
    }
    
    Map.merge(base_attrs, attrs)
  end

  @doc """
  生成测试机器人配置数据
  """
  def generate_test_robot_config(attrs \\ %{}) do
    base_attrs = %{
      robot_name: "测试机器人_#{System.unique_integer([:positive])}",
      game_type: Enum.random(["racing", "card", "puzzle"]),
      difficulty_level: :rand.uniform(4),
      win_rate: Decimal.new(:rand.uniform(80) + 10),
      strategy_config: Jason.encode!(%{
        "aggression" => :rand.uniform(10),
        "defense" => :rand.uniform(10),
        "speed" => :rand.uniform(10)
      }),
      status: 1,
      created_at: DateTime.utc_now(),
      updated_at: DateTime.utc_now()
    }
    
    Map.merge(base_attrs, attrs)
  end

  @doc """
  生成测试支付订单数据
  """
  def generate_test_payment_order(attrs \\ %{}) do
    user_data = generate_test_user()
    
    base_attrs = %{
      user_id: user_data.id || :rand.uniform(10000),
      order_number: "PAY#{System.system_time(:millisecond)}",
      amount: Decimal.new(:rand.uniform(1000) + 100),
      payment_method: Enum.random(["alipay", "wechat", "bank_card"]),
      status: Enum.random([0, 1, 2, 3]),
      created_at: DateTime.utc_now(),
      updated_at: DateTime.utc_now()
    }
    
    Map.merge(base_attrs, attrs)
  end

  @doc """
  生成测试活动数据
  """
  def generate_test_activity(attrs \\ %{}) do
    start_time = DateTime.utc_now()
    end_time = DateTime.add(start_time, 7 * 24 * 3600, :second)
    
    base_attrs = %{
      activity_name: "测试活动_#{System.unique_integer([:positive])}",
      activity_type: Enum.random(["bonus", "tournament", "promotion"]),
      description: "这是一个测试活动",
      start_time: start_time,
      end_time: end_time,
      status: 1,
      rules: Jason.encode!(%{
        "min_participation" => 1,
        "max_participation" => 1000,
        "reward_type" => "coins"
      }),
      created_at: DateTime.utc_now(),
      updated_at: DateTime.utc_now()
    }
    
    Map.merge(base_attrs, attrs)
  end

  @doc """
  生成测试推广员数据
  """
  def generate_test_promoter(attrs \\ %{}) do
    user_data = generate_test_user()
    
    base_attrs = %{
      user_id: user_data.id || :rand.uniform(10000),
      promoter_code: "P#{user_data.id || :rand.uniform(10000)}_#{System.unique_integer([:positive])}",
      level: :rand.uniform(4),
      status: 1,
      total_commission: Decimal.new(:rand.uniform(10000)),
      total_invites: :rand.uniform(100),
      created_at: DateTime.utc_now(),
      updated_at: DateTime.utc_now()
    }
    
    Map.merge(base_attrs, attrs)
  end

  # ==================== 测试环境配置 ====================

  @doc """
  设置测试环境
  """
  def setup_test_environment do
    # 清理测试数据
    cleanup_test_data()
    
    # 设置测试配置
    Application.put_env(:racing_game, :test_mode, true)
    Application.put_env(:racing_game, :cache_ttl, 1000) # 1秒缓存用于测试
    
    :ok
  end

  @doc """
  清理测试数据
  """
  def cleanup_test_data do
    # 这里应该清理所有测试相关的数据
    # 具体实现取决于数据库配置
    :ok
  end

  # ==================== Mock数据创建 ====================

  @doc """
  创建完整的测试数据集
  """
  def create_test_dataset do
    # 创建测试用户
    users = Enum.map(1..10, fn _ -> generate_test_user() end)
    
    # 创建测试工单
    tickets = Enum.map(1..20, fn _ -> generate_test_ticket() end)
    
    # 创建测试平台
    platforms = Enum.map(1..5, fn _ -> generate_test_platform() end)
    
    # 创建测试VIP等级
    vip_levels = Enum.map(1..10, fn level -> generate_test_vip_level(%{level: level}) end)
    
    # 创建测试机器人配置
    robot_configs = Enum.map(1..15, fn _ -> generate_test_robot_config() end)
    
    # 创建测试支付订单
    payment_orders = Enum.map(1..30, fn _ -> generate_test_payment_order() end)
    
    # 创建测试活动
    activities = Enum.map(1..8, fn _ -> generate_test_activity() end)
    
    # 创建测试推广员
    promoters = Enum.map(1..12, fn _ -> generate_test_promoter() end)
    
    %{
      users: users,
      tickets: tickets,
      platforms: platforms,
      vip_levels: vip_levels,
      robot_configs: robot_configs,
      payment_orders: payment_orders,
      activities: activities,
      promoters: promoters
    }
  end

  # ==================== 测试断言辅助 ====================

  @doc """
  断言结果成功
  """
  def assert_success({:ok, result}), do: result
  def assert_success({:error, reason}), do: raise("Expected success, got error: #{inspect(reason)}")

  @doc """
  断言结果失败
  """
  def assert_error({:error, _reason} = error), do: error
  def assert_error({:ok, result}), do: raise("Expected error, got success: #{inspect(result)}")

  @doc """
  断言列表不为空
  """
  def assert_not_empty([]), do: raise("Expected non-empty list")
  def assert_not_empty(list) when is_list(list), do: list

  @doc """
  断言数值在范围内
  """
  def assert_in_range(value, min, max) when value >= min and value <= max, do: value
  def assert_in_range(value, min, max), do: raise("Expected #{value} to be between #{min} and #{max}")

  # ==================== 性能测试辅助 ====================

  @doc """
  测量函数执行时间
  """
  def measure_time(fun) do
    start_time = System.monotonic_time(:millisecond)
    result = fun.()
    end_time = System.monotonic_time(:millisecond)
    
    {result, end_time - start_time}
  end

  @doc """
  并发执行测试
  """
  def concurrent_test(fun, concurrency \\ 10) do
    tasks = Enum.map(1..concurrency, fn _ ->
      Task.async(fun)
    end)
    
    Task.await_many(tasks, 30_000)
  end

  # ==================== 数据验证辅助 ====================

  @doc """
  验证数据结构
  """
  def validate_structure(data, expected_keys) when is_map(data) do
    missing_keys = expected_keys -- Map.keys(data)
    
    if Enum.empty?(missing_keys) do
      :ok
    else
      {:error, {:missing_keys, missing_keys}}
    end
  end

  @doc """
  验证Decimal类型
  """
  def validate_decimal(%Decimal{} = decimal), do: {:ok, decimal}
  def validate_decimal(_), do: {:error, :not_decimal}

  @doc """
  验证日期时间
  """
  def validate_datetime(%DateTime{} = datetime), do: {:ok, datetime}
  def validate_datetime(_), do: {:error, :not_datetime}
end
