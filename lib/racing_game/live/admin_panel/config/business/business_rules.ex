defmodule RacingGame.Live.AdminPanel.Config.Business.BusinessRules do
  @moduledoc """
  业务规则配置
  
  定义系统的业务规则和约束：
  - 用户管理规则
  - 权限控制规则
  - 数据操作规则
  - 业务流程规则
  """

  # ============================================================================
  # 用户管理规则
  # ============================================================================

  @doc """
  用户创建规则
  """
  def user_creation_rules do
    %{
      # 用户名规则
      username: %{
        min_length: 3,
        max_length: 20,
        pattern: ~r/^[a-zA-Z0-9_]+$/,
        reserved_names: ["admin", "root", "system", "test"],
        case_sensitive: false
      },
      
      # 密码规则
      password: %{
        min_length: 6,
        max_length: 50,
        require_uppercase: false,
        require_lowercase: false,
        require_numbers: false,
        require_special_chars: false,
        forbidden_patterns: ["123456", "password", "admin"]
      },
      
      # 邮箱规则
      email: %{
        required: false,
        unique: true,
        pattern: ~r/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
      },
      
      # 手机号规则
      phone: %{
        required: false,
        unique: true,
        pattern: ~r/^1[3-9]\d{9}$/
      }
    }
  end

  @doc """
  用户更新规则
  """
  def user_update_rules do
    %{
      # 允许更新的字段
      updatable_fields: [
        :username, :email, :phone, :active, :permission_level
      ],
      
      # 自我更新限制
      self_update_restrictions: [
        :permission_level, :active
      ],
      
      # 权限级别更新规则
      permission_level_update: %{
        # 只有超级管理员可以设置超级管理员权限
        super_admin_only: [2],
        # 管理员可以设置的权限级别
        admin_allowed: [0, 1],
        # 普通用户不能修改权限级别
        user_forbidden: [:permission_level]
      }
    }
  end

  @doc """
  用户删除规则
  """
  def user_deletion_rules do
    %{
      # 软删除还是硬删除
      deletion_type: :soft,
      
      # 删除前检查
      pre_deletion_checks: [
        :has_active_bets,
        :has_stock_holdings,
        :has_subordinates,
        :has_pending_transactions
      ],
      
      # 删除限制
      deletion_restrictions: %{
        # 不能删除自己
        cannot_delete_self: true,
        # 不能删除更高权限的用户
        cannot_delete_higher_permission: true,
        # 超级管理员删除需要特殊确认
        super_admin_requires_confirmation: true
      }
    }
  end

  # ============================================================================
  # 权限控制规则
  # ============================================================================

  @doc """
  权限级别定义
  """
  def permission_levels do
    %{
      0 => %{
        name: "普通用户",
        description: "基础用户权限",
        capabilities: [
          :view_own_profile,
          :update_own_profile,
          :view_own_bets,
          :view_own_stocks
        ]
      },
      1 => %{
        name: "管理员",
        description: "管理员权限",
        capabilities: [
          :view_all_users,
          :create_users,
          :update_users,
          :view_all_bets,
          :view_all_stocks,
          :manage_communications,
          :view_system_logs
        ]
      },
      2 => %{
        name: "超级管理员",
        description: "最高权限",
        capabilities: [
          :all_admin_capabilities,
          :delete_users,
          :manage_admins,
          :system_maintenance,
          :data_export,
          :system_configuration
        ]
      }
    }
  end

  @doc """
  操作权限矩阵
  """
  def operation_permissions do
    %{
      # 用户管理操作
      user_management: %{
        create: [1, 2],
        read: [0, 1, 2],  # 用户可以读取自己的信息
        update: [1, 2],   # 用户可以更新自己的基本信息
        delete: [2]
      },
      
      # 通信管理操作
      communication_management: %{
        create: [1, 2],
        read: [0, 1, 2],
        update: [1, 2],
        delete: [2]
      },
      
      # 数据管理操作
      data_management: %{
        view_all: [1, 2],
        export: [2],
        cleanup: [2]
      },
      
      # 系统管理操作
      system_management: %{
        logs: [1, 2],
        monitoring: [1, 2],
        maintenance: [2],
        configuration: [2]
      }
    }
  end

  # ============================================================================
  # 数据操作规则
  # ============================================================================

  @doc """
  数据查询规则
  """
  def data_query_rules do
    %{
      # 分页限制
      pagination: %{
        default_page_size: 20,
        max_page_size: 100,
        max_total_results: 10000
      },
      
      # 搜索限制
      search: %{
        min_search_length: 2,
        max_search_length: 50,
        search_timeout_seconds: 30
      },
      
      # 导出限制
      export: %{
        max_export_records: 50000,
        allowed_formats: [:csv, :xlsx],
        export_timeout_seconds: 300
      }
    }
  end

  @doc """
  数据修改规则
  """
  def data_modification_rules do
    %{
      # 批量操作限制
      batch_operations: %{
        max_batch_size: 100,
        batch_timeout_seconds: 60,
        require_confirmation_above: 50
      },
      
      # 数据完整性检查
      integrity_checks: %{
        check_foreign_keys: true,
        check_business_constraints: true,
        validate_data_format: true
      },
      
      # 审计日志
      audit_logging: %{
        log_all_modifications: true,
        include_old_values: true,
        retention_days: 365
      }
    }
  end

  # ============================================================================
  # 业务流程规则
  # ============================================================================

  @doc """
  通信发布流程规则
  """
  def communication_workflow_rules do
    %{
      # 发布前检查
      pre_publish_checks: [
        :content_validation,
        :recipient_validation,
        :permission_check
      ],
      
      # 自动发布规则
      auto_publish: %{
        enabled: false,
        conditions: [],
        schedule_delay_minutes: 0
      },
      
      # 发布后操作
      post_publish_actions: [
        :send_notifications,
        :update_statistics,
        :log_activity
      ]
    }
  end

  @doc """
  用户注册流程规则
  """
  def user_registration_workflow_rules do
    %{
      # 注册验证步骤
      verification_steps: [
        :username_uniqueness,
        :email_format,
        :password_strength
      ],
      
      # 自动激活
      auto_activation: %{
        enabled: true,
        conditions: [:email_verified]
      },
      
      # 欢迎流程
      welcome_process: %{
        send_welcome_email: false,
        create_default_settings: true,
        assign_default_permissions: true
      }
    }
  end

  # ============================================================================
  # 安全规则
  # ============================================================================

  @doc """
  安全策略规则
  """
  def security_policy_rules do
    %{
      # 会话管理
      session_management: %{
        max_concurrent_sessions: 3,
        session_timeout_minutes: 120,
        require_reauth_for_sensitive_ops: true
      },
      
      # 操作限制
      operation_limits: %{
        max_failed_login_attempts: 5,
        lockout_duration_minutes: 30,
        rate_limit_requests_per_minute: 60
      },
      
      # 数据保护
      data_protection: %{
        encrypt_sensitive_data: true,
        mask_personal_info_in_logs: true,
        require_https: true
      }
    }
  end

  @doc """
  输入验证规则
  """
  def input_validation_rules do
    %{
      # 通用验证
      general: %{
        max_string_length: 1000,
        allowed_html_tags: [],
        sanitize_input: true
      },
      
      # SQL注入防护
      sql_injection_protection: %{
        enabled: true,
        blocked_patterns: [
          ~r/union\s+select/i,
          ~r/drop\s+table/i,
          ~r/delete\s+from/i
        ]
      },
      
      # XSS防护
      xss_protection: %{
        enabled: true,
        escape_html: true,
        content_security_policy: true
      }
    }
  end

  # ============================================================================
  # 辅助函数
  # ============================================================================

  @doc """
  检查用户是否有指定操作的权限

  ## 参数
  - `user` - 用户对象
  - `operation_category` - 操作类别
  - `operation_type` - 操作类型

  ## 返回
  - `true` - 有权限
  - `false` - 无权限
  """
  def has_permission?(user, operation_category, operation_type) do
    permissions = operation_permissions()
    
    case get_in(permissions, [operation_category, operation_type]) do
      nil -> false
      allowed_levels -> user.permission_level in allowed_levels
    end
  end

  @doc """
  获取用户权限级别信息

  ## 参数
  - `permission_level` - 权限级别

  ## 返回
  - 权限级别信息映射
  """
  def get_permission_level_info(permission_level) do
    permission_levels()
    |> Map.get(permission_level, %{name: "未知权限", description: "", capabilities: []})
  end

  @doc """
  验证业务规则

  ## 参数
  - `rule_category` - 规则类别
  - `data` - 要验证的数据

  ## 返回
  - `{:ok, validated_data}` - 验证通过
  - `{:error, violations}` - 验证失败
  """
  def validate_business_rule(rule_category, data) do
    # TODO: 实现具体的业务规则验证逻辑
    {:ok, data}
  end
end
