defmodule RacingGame.Live.AdminPanel.Services.Domain.UserService do
  @moduledoc """
  用户管理领域服务
  
  提供用户相关的业务逻辑处理，包括：
  - 用户创建和更新
  - 权限管理
  - 代理关系管理
  - 用户数据查询和过滤
  """

  require Logger
  alias Cypridina.Accounts.{User, AgentRelationship}
  alias RacingGame.Live.AdminPanel.Services.Application.PermissionFilter
  require Ash.Query

  # 常量定义
  @default_per_page 20
  @max_per_page 100
  @permission_levels %{
    0 => "普通用户",
    1 => "管理员", 
    2 => "超级管理员"
  }

  # ============================================================================
  # 公共API
  # ============================================================================

  @doc """
  获取用户列表

  ## 参数
  - `current_user` - 当前用户
  - `params` - 查询参数
    - `:search_query` - 搜索关键词
    - `:page` - 页码
    - `:per_page` - 每页数量

  ## 返回
  - `{:ok, %{users: users, page_info: page_info}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_users(current_user, params \\ %{}) do
    Logger.debug("📋 [用户服务] 获取用户列表")
    
    try do
      query_params = normalize_list_params(params)
      
      with {:ok, query} <- build_users_query(current_user, query_params),
           {:ok, results} <- execute_users_query(query, query_params) do
        Logger.info("✅ [用户服务] 成功获取 #{length(results.users)} 个用户")
        {:ok, results}
      else
        {:error, reason} -> {:error, reason}
      end
    rescue
      error ->
        Logger.error("❌ [用户服务] 获取用户列表异常: #{inspect(error)}")
        {:error, "获取用户列表失败"}
    end
  end

  @doc """
  创建用户

  ## 参数
  - `current_user` - 当前用户
  - `user_params` - 用户参数

  ## 返回
  - `{:ok, user}` - 成功
  - `{:error, changeset}` - 失败
  """
  def create_user(current_user, user_params) do
    Logger.info("➕ [用户服务] 创建用户: #{user_params["username"]}")
    
    with :ok <- validate_create_permission(current_user),
         {:ok, validated_params} <- validate_user_params(user_params),
         {:ok, create_params} <- prepare_create_params(validated_params, current_user) do
      
      case User.create(create_params) do
        {:ok, user} ->
          Logger.info("✅ [用户服务] 用户创建成功: #{user.username}")
          {:ok, user}
        {:error, changeset} ->
          Logger.error("❌ [用户服务] 用户创建失败: #{inspect(changeset)}")
          {:error, changeset}
      end
    else
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  更新用户

  ## 参数
  - `current_user` - 当前用户
  - `user_id` - 用户ID
  - `user_params` - 更新参数

  ## 返回
  - `{:ok, user}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_user(current_user, user_id, user_params) do
    Logger.info("✏️ [用户服务] 更新用户: #{user_id}")
    
    with {:ok, user} <- get_user_by_id(user_id),
         :ok <- validate_update_permission(current_user, user),
         {:ok, validated_params} <- validate_update_params(user_params),
         {:ok, update_params} <- prepare_update_params(validated_params) do
      
      case User.update(user, update_params) do
        {:ok, updated_user} ->
          Logger.info("✅ [用户服务] 用户更新成功: #{updated_user.username}")
          {:ok, updated_user}
        {:error, changeset} ->
          Logger.error("❌ [用户服务] 用户更新失败: #{inspect(changeset)}")
          {:error, changeset}
      end
    else
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  获取用户详情

  ## 参数
  - `current_user` - 当前用户
  - `user_id` - 用户ID

  ## 返回
  - `{:ok, user}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_details(current_user, user_id) do
    Logger.debug("👤 [用户服务] 获取用户详情: #{user_id}")
    
    with {:ok, user} <- get_user_by_id(user_id),
         :ok <- validate_view_permission(current_user, user) do
      
      # 加载额外信息
      user_with_details = load_user_details(user)
      {:ok, user_with_details}
    else
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  获取权限级别显示名称

  ## 参数
  - `level` - 权限级别

  ## 返回
  - 权限级别名称
  """
  def get_permission_level_name(level) do
    Map.get(@permission_levels, level, "未知权限")
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 标准化列表参数
  defp normalize_list_params(params) do
    %{
      search_query: String.trim(params[:search_query] || ""),
      page: max(1, params[:page] || 1),
      per_page: min(params[:per_page] || @default_per_page, @max_per_page)
    }
  end

  # 构建用户查询
  defp build_users_query(current_user, params) do
    try do
      query = User
      |> Ash.Query.load([:point_account, :subordinate_relationships])
      |> PermissionFilter.apply_user_filter(current_user, :id)
      |> apply_search_filter(params.search_query)
      |> Ash.Query.sort(inserted_at: :desc)
      
      {:ok, query}
    rescue
      error ->
        Logger.error("❌ [用户服务] 构建查询失败: #{inspect(error)}")
        {:error, "构建查询失败"}
    end
  end

  # 应用搜索过滤
  defp apply_search_filter(query, ""), do: query
  defp apply_search_filter(query, search_query) do
    Ash.Query.filter(query, contains(username, ^search_query))
  end

  # 执行用户查询
  defp execute_users_query(query, params) do
    offset = (params.page - 1) * params.per_page
    
    case query
         |> Ash.Query.page(count: true, limit: params.per_page, offset: offset)
         |> Ash.read() do
      {:ok, %Ash.Page.Offset{results: users, count: total_count}} ->
        # 为每个用户加载代理信息
        users_with_agent_info = Enum.map(users, &load_user_agent_info/1)
        
        page_info = %{
          total_count: total_count,
          page: params.page,
          per_page: params.per_page
        }
        
        {:ok, %{users: users_with_agent_info, page_info: page_info}}
      {:error, error} ->
        Logger.error("❌ [用户服务] 执行查询失败: #{inspect(error)}")
        {:error, "查询用户失败"}
    end
  end

  # 加载用户代理信息
  defp load_user_agent_info(user) do
    agent_info = case AgentRelationship
                      |> Ash.Query.filter(subordinate_id == ^user.id and status == 1)
                      |> Ash.Query.load([:agent])
                      |> Ash.read() do
      {:ok, [relationship | _]} ->
        %{
          has_agent: true,
          agent: relationship.agent,
          commission_rate: relationship.commission_rate,
          level: relationship.level
        }
      {:ok, []} ->
        %{has_agent: false, agent: nil, commission_rate: nil, level: nil}
      _ ->
        %{has_agent: false, agent: nil, commission_rate: nil, level: nil}
    end

    Map.put(user, :agent_info, agent_info)
  end

  # 加载用户详细信息
  defp load_user_details(user) do
    # TODO: 加载更多详细信息，如统计数据等
    user
  end

  # 获取用户
  defp get_user_by_id(user_id) do
    case User.read(user_id) do
      {:ok, user} -> {:ok, user}
      {:error, %Ash.Error.Query.NotFound{}} -> {:error, "用户不存在"}
      {:error, error} -> {:error, "获取用户失败: #{inspect(error)}"}
    end
  end

  # 验证创建权限
  defp validate_create_permission(current_user) do
    if current_user.permission_level >= 1 do
      :ok
    else
      {:error, "权限不足，无法创建用户"}
    end
  end

  # 验证更新权限
  defp validate_update_permission(current_user, target_user) do
    cond do
      current_user.permission_level >= 2 -> :ok
      current_user.permission_level >= 1 and target_user.permission_level < 1 -> :ok
      current_user.id == target_user.id -> :ok
      true -> {:error, "权限不足，无法更新此用户"}
    end
  end

  # 验证查看权限
  defp validate_view_permission(current_user, target_user) do
    cond do
      current_user.permission_level >= 1 -> :ok
      current_user.id == target_user.id -> :ok
      true -> {:error, "权限不足，无法查看此用户"}
    end
  end

  # 验证用户参数
  defp validate_user_params(params) do
    # TODO: 实现详细的参数验证
    {:ok, params}
  end

  # 验证更新参数
  defp validate_update_params(params) do
    # TODO: 实现详细的更新参数验证
    {:ok, params}
  end

  # 准备创建参数
  defp prepare_create_params(params, current_user) do
    # TODO: 准备创建参数，添加创建者信息等
    {:ok, params}
  end

  # 准备更新参数
  defp prepare_update_params(params) do
    # TODO: 准备更新参数，过滤不允许更新的字段等
    {:ok, params}
  end
end
