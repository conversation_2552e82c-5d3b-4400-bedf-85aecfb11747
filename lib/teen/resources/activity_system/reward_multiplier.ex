defmodule Teen.ActivitySystem.RewardMultiplier do
  @moduledoc """
  奖励倍率资源

  管理奖励倍率配置
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  postgres do
    table "reward_multipliers"
    repo Cypridina.Repo
  end

  admin do
    table_columns [:id, :multiplier_name, :multiplier_type, :multiplier_value, :status]
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_multipliers
    define :list_by_type
    define :list_current_multipliers
  end

  actions do
    defaults [:create, :read, :update, :destroy]

    read :list_active_multipliers do
      filter expr(status == 1)
    end

    read :list_by_type do
      argument :multiplier_type, :integer, allow_nil?: false
      filter expr(multiplier_type == ^arg(:multiplier_type) and status == 1)
    end

    read :list_current_multipliers do
      filter expr(status == 1 and (is_nil(start_time) or start_time <= ^DateTime.utc_now()) and (is_nil(end_time) or end_time >= ^DateTime.utc_now()))
    end

    update :enable do
      change set_attribute(:status, 1)
    end

    update :disable do
      change set_attribute(:status, 0)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :multiplier_name, :string do
      allow_nil? false
      public? true
      description "倍率名称"
      constraints max_length: 100
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "倍率描述"
      constraints max_length: 1000
    end

    attribute :multiplier_type, :integer do
      allow_nil? false
      public? true
      description "倍率类型：1-签到奖励，2-任务奖励，3-活动奖励，4-VIP奖励，5-全局奖励"
      default 1
      constraints min: 1, max: 5
    end

    attribute :multiplier_value, :decimal do
      allow_nil? false
      public? true
      description "倍率值"
      default Decimal.new("1.0")
      constraints min: Decimal.new("0.1")
    end

    attribute :target_activity, :string do
      allow_nil? true
      public? true
      description "目标活动（为空则应用于所有相关活动）"
      constraints max_length: 100
    end

    attribute :target_level_min, :integer do
      allow_nil? true
      public? true
      description "目标等级最小值"
      constraints min: 1
    end

    attribute :target_level_max, :integer do
      allow_nil? true
      public? true
      description "目标等级最大值"
      constraints min: 1
    end

    attribute :vip_level_min, :integer do
      allow_nil? true
      public? true
      description "VIP等级最小值"
      constraints min: 0
    end

    attribute :vip_level_max, :integer do
      allow_nil? true
      public? true
      description "VIP等级最大值"
      constraints min: 0
    end

    attribute :start_time, :utc_datetime do
      allow_nil? true
      public? true
      description "开始时间（为空则立即生效）"
    end

    attribute :end_time, :utc_datetime do
      allow_nil? true
      public? true
      description "结束时间（为空则永久有效）"
    end

    attribute :is_stackable, :boolean do
      allow_nil? false
      public? true
      description "是否可叠加"
      default false
    end

    attribute :max_stack, :integer do
      allow_nil? false
      public? true
      description "最大叠加次数"
      default 1
      constraints min: 1
    end

    attribute :conditions, :map do
      allow_nil? true
      public? true
      description "应用条件配置"
    end

    attribute :usage_limit, :integer do
      allow_nil? false
      public? true
      description "使用次数限制（0为无限制）"
      default 0
      constraints min: 0
    end

    attribute :used_count, :integer do
      allow_nil? false
      public? true
      description "已使用次数"
      default 0
      constraints min: 0
    end

    attribute :status, :integer do
      allow_nil? false
      public? true
      description "状态：0-禁用，1-启用"
      default 1
      constraints min: 0, max: 1
    end

    attribute :priority, :integer do
      allow_nil? false
      public? true
      description "优先级（数字越大优先级越高）"
      default 0
      constraints min: 0
    end

    timestamps()
  end

  identities do
    identity :unique_multiplier_name, [:multiplier_name]
  end
end
