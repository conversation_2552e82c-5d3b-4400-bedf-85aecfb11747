defmodule RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.PaymentStatsRepository do
  @moduledoc """
  💰 支付统计仓储层

  负责支付统计的数据访问操作，包括：
  - 支付统计的CRUD操作
  - 支付收入数据统计和分析
  - 支付转化率和ARPU分析
  - 支付渠道统计
  - 支付数据缓存管理
  """

  require Logger
  import Ash.Query

  alias Teen.Statistics.PaymentStats

  # 缓存配置
  @cache_ttl 600_000  # 10分钟缓存
  @cache_prefix "payment_stats_repo"

  # ==================== 基础 CRUD 操作 ====================

  @doc """
  创建支付统计记录

  ## 参数
  - `stats_data` - 统计数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, payment_stats}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_payment_stats(stats_data, options \\ []) do
    Logger.info("💰 [支付统计仓储] 创建支付统计记录: #{inspect(stats_data[:stat_date])}")

    try do
      result = PaymentStats
      |> Ash.Changeset.for_create(:create, stats_data)
      |> Ash.create()

      case result do
        {:ok, payment_stats} ->
          Logger.info("✅ [支付统计仓储] 支付统计记录创建成功: #{payment_stats.stat_date}")
          clear_cache()
          {:ok, payment_stats}
        {:error, error} ->
          Logger.error("❌ [支付统计仓储] 支付统计记录创建失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [支付统计仓储] 创建支付统计记录异常: #{inspect(exception)}")
        {:error, :create_failed}
    end
  end

  @doc """
  获取支付统计记录

  ## 参数
  - `stats_id` - 统计ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, payment_stats}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_payment_stats(stats_id, options \\ []) do
    Logger.info("💰 [支付统计仓储] 获取支付统计记录: #{stats_id}")

    cache_key = "#{@cache_prefix}:get:#{stats_id}"
    
    case get_from_cache(cache_key) do
      {:ok, cached_result} ->
        Logger.info("🎯 [支付统计仓储] 缓存命中: #{stats_id}")
        {:ok, cached_result}
      
      {:error, :not_found} ->
        try do
          result = PaymentStats |> Ash.get(stats_id)

          case result do
            {:ok, payment_stats} ->
              Logger.info("✅ [支付统计仓储] 支付统计记录获取成功: #{payment_stats.id}")
              put_to_cache(cache_key, payment_stats)
              {:ok, payment_stats}
            {:error, error} ->
              Logger.error("❌ [支付统计仓储] 支付统计记录获取失败: #{inspect(error)}")
              {:error, error}
          end
        rescue
          exception ->
            Logger.error("💥 [支付统计仓储] 获取支付统计记录异常: #{inspect(exception)}")
            {:error, :get_failed}
        end
    end
  end

  @doc """
  更新支付统计记录

  ## 参数
  - `payment_stats` - 支付统计实例
  - `update_data` - 更新数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, updated_payment_stats}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_payment_stats(payment_stats, update_data, options \\ []) do
    Logger.info("💰 [支付统计仓储] 更新支付统计记录: #{payment_stats.id}")

    try do
      result = payment_stats
      |> Ash.Changeset.for_update(:update, update_data)
      |> Ash.update()

      case result do
        {:ok, updated_payment_stats} ->
          Logger.info("✅ [支付统计仓储] 支付统计记录更新成功: #{updated_payment_stats.id}")
          clear_cache()
          {:ok, updated_payment_stats}
        {:error, error} ->
          Logger.error("❌ [支付统计仓储] 支付统计记录更新失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [支付统计仓储] 更新支付统计记录异常: #{inspect(exception)}")
        {:error, :update_failed}
    end
  end

  @doc """
  删除支付统计记录

  ## 参数
  - `payment_stats` - 支付统计实例
  - `options` - 选项参数

  ## 返回
  - `{:ok, deleted_payment_stats}` - 成功
  - `{:error, reason}` - 失败
  """
  def delete_payment_stats(payment_stats, options \\ []) do
    Logger.info("💰 [支付统计仓储] 删除支付统计记录: #{payment_stats.id}")

    try do
      result = payment_stats |> Ash.destroy()

      case result do
        :ok ->
          Logger.info("✅ [支付统计仓储] 支付统计记录删除成功: #{payment_stats.id}")
          clear_cache()
          {:ok, payment_stats}
        {:error, error} ->
          Logger.error("❌ [支付统计仓储] 支付统计记录删除失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [支付统计仓储] 删除支付统计记录异常: #{inspect(exception)}")
        {:error, :delete_failed}
    end
  end

  # ==================== 查询操作 ====================

  @doc """
  列出支付统计记录

  ## 参数
  - `params` - 查询参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, {stats_list, total_count}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_payment_stats(params \\ %{}, options \\ []) do
    Logger.info("💰 [支付统计仓储] 列出支付统计记录: #{inspect(params)}")

    try do
      query = PaymentStats
      |> build_list_query(params)
      |> maybe_load_associations(options)

      # 获取总数
      total_count = query |> Ash.count!()

      # 应用分页
      query = query
      |> limit(Map.get(params, :page_size, 20))
      |> offset(Map.get(params, :offset, 0))

      stats_list = query |> Ash.read!()

      Logger.info("✅ [支付统计仓储] 支付统计记录列表获取成功，共 #{length(stats_list)} 条")
      {:ok, {stats_list, total_count}}
    rescue
      exception ->
        Logger.error("💥 [支付统计仓储] 列出支付统计记录异常: #{inspect(exception)}")
        {:error, :list_failed}
    end
  end

  @doc """
  按日期范围获取支付统计

  ## 参数
  - `start_date` - 开始日期
  - `end_date` - 结束日期
  - `options` - 选项参数

  ## 返回
  - `{:ok, stats_list}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_stats_by_date_range(start_date, end_date, options \\ []) do
    Logger.info("💰 [支付统计仓储] 按日期范围获取支付统计: #{start_date} - #{end_date}")

    try do
      stats_list = PaymentStats
      |> Ash.Query.filter(stat_date >= start_date and stat_date  <= end_date)
      |> Ash.Query.sort(stat_date: :desc)
      |> maybe_load_associations(options)
      |> Ash.read!()

      Logger.info("✅ [支付统计仓储] 按日期范围获取支付统计成功: #{length(stats_list)} 条")
      {:ok, stats_list}
    rescue
      exception ->
        Logger.error("💥 [支付统计仓储] 按日期范围获取支付统计异常: #{inspect(exception)}")
        {:error, :get_by_date_range_failed}
    end
  end

  @doc """
  获取收入统计

  ## 参数
  - `stat_date` - 统计日期
  - `options` - 选项参数

  ## 返回
  - `{:ok, revenue_stats}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_revenue_stats(stat_date, options \\ []) do
    Logger.info("💰 [支付统计仓储] 获取收入统计: #{stat_date}")

    try do
      revenue_stats = PaymentStats
      |> Ash.Query.filter(stat_date == stat_date)
      |> maybe_load_associations(options)
      |> Ash.read!()
      |> List.first()

      case revenue_stats do
        nil ->
          Logger.info("ℹ️ [支付统计仓储] 未找到收入统计: #{stat_date}")
          {:error, :not_found}
        stats ->
          Logger.info("✅ [支付统计仓储] 获取收入统计成功: #{stats.id}")
          {:ok, stats}
      end
    rescue
      exception ->
        Logger.error("💥 [支付统计仓储] 获取收入统计异常: #{inspect(exception)}")
        {:error, :get_revenue_stats_failed}
    end
  end

  @doc """
  获取支付转化统计

  ## 参数
  - `days` - 天数
  - `options` - 选项参数

  ## 返回
  - `{:ok, conversion_stats}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_conversion_stats(days \\ 7, options \\ []) do
    Logger.info("💰 [支付统计仓储] 获取支付转化统计: #{days} 天")

    try do
      start_date = Date.utc_today() |> Date.add(-days)
      
      conversion_stats = PaymentStats
      |> Ash.Query.filter(stat_date >= start_date)
      |> Ash.Query.sort(stat_date: :desc)
      |> maybe_load_associations(options)
      |> Ash.read!()

      Logger.info("✅ [支付统计仓储] 获取支付转化统计成功: #{length(conversion_stats)} 条")
      {:ok, conversion_stats}
    rescue
      exception ->
        Logger.error("💥 [支付统计仓储] 获取支付转化统计异常: #{inspect(exception)}")
        {:error, :get_conversion_stats_failed}
    end
  end

  @doc """
  获取ARPU统计

  ## 参数
  - `stat_date` - 统计日期
  - `options` - 选项参数

  ## 返回
  - `{:ok, arpu_stats}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_arpu_stats(stat_date, options \\ []) do
    Logger.info("💰 [支付统计仓储] 获取ARPU统计: #{stat_date}")

    try do
      arpu_stats = PaymentStats
      |> Ash.Query.filter(stat_date == stat_date)
      |> maybe_load_associations(options)
      |> Ash.read!()

      Logger.info("✅ [支付统计仓储] 获取ARPU统计成功: #{length(arpu_stats)} 条")
      {:ok, arpu_stats}
    rescue
      exception ->
        Logger.error("💥 [支付统计仓储] 获取ARPU统计异常: #{inspect(exception)}")
        {:error, :get_arpu_stats_failed}
    end
  end

  # ==================== 私有辅助函数 ====================

  # 构建列表查询
  defp build_list_query(query, params) do
    query
    |> maybe_filter_by_date_range(params)
    |> maybe_filter_by_payment_method(params)
    |> maybe_apply_search(params)
    |> maybe_apply_sort(params)
  end

  # 按日期范围过滤
  defp maybe_filter_by_date_range(query, %{start_date: start_date, end_date: end_date}) 
    when not is_nil(start_date) and not is_nil(end_date) do
    query |> Ash.Query.filter(stat_date >= start_date and stat_date  <= end_date)
  end
  defp maybe_filter_by_date_range(query, _), do: query

  # 按支付方式过滤
  defp maybe_filter_by_payment_method(query, %{payment_method: payment_method}) when payment_method != "" do
    query
  end
  defp maybe_filter_by_payment_method(query, _), do: query

  # 应用搜索
  defp maybe_apply_search(query, %{search: search}) when search != "" do
    query
  end
  defp maybe_apply_search(query, _), do: query

  # 应用排序
  defp maybe_apply_sort(query, %{sort_by: sort_by, sort_order: sort_order}) do
    sort_field = String.to_atom(sort_by)
    sort_direction = String.to_atom(sort_order)
    query |> Ash.Query.sort([{sort_direction, sort_field}])
  end
  defp maybe_apply_sort(query, _) do
    query |> Ash.Query.sort(stat_date: :desc)
  end

  # 加载关联数据
  defp maybe_load_associations(query, options) do
    case Keyword.get(options, :load_associations, false) do
      true -> query |> Ash.Query.load([])
      _ -> query
    end
  end

  # 缓存操作
  defp get_from_cache(_key), do: {:error, :not_found}
  defp put_to_cache(_key, _value), do: :ok
  defp clear_cache(), do: :ok
end
