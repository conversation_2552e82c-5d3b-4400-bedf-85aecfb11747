defmodule Teen.ActivitySystem.SignInActivity do
  @moduledoc """
  签到活动资源

  管理用户签到活动配置和奖励
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  postgres do
    table "sign_in_activities"
    repo Cypridina.Repo
  end

  admin do
    table_columns [:id, :activity_name, :start_date, :end_date, :status]
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_activities
    define :list_current_activities
  end

  actions do
    defaults [:create, :read, :update, :destroy]

    read :list_active_activities do
      filter expr(status == 1)
    end

    read :list_current_activities do
      filter expr(status == 1 and start_date <= ^DateTime.utc_now() and end_date >= ^DateTime.utc_now())
    end

    update :enable do
      change set_attribute(:status, 1)
    end

    update :disable do
      change set_attribute(:status, 0)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :activity_name, :string do
      allow_nil? false
      public? true
      description "活动名称"
      constraints max_length: 100
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "活动描述"
      constraints max_length: 1000
    end

    attribute :start_date, :utc_datetime do
      allow_nil? false
      public? true
      description "开始时间"
    end

    attribute :end_date, :utc_datetime do
      allow_nil? false
      public? true
      description "结束时间"
    end

    attribute :status, :integer do
      allow_nil? false
      public? true
      description "状态：0-禁用，1-启用"
      default 1
      constraints min: 0, max: 1
    end

    attribute :daily_rewards, {:array, :map} do
      allow_nil? true
      public? true
      description "每日奖励配置"
      default []
    end

    attribute :consecutive_rewards, {:array, :map} do
      allow_nil? true
      public? true
      description "连续签到奖励"
      default []
    end

    attribute :monthly_rewards, {:array, :map} do
      allow_nil? true
      public? true
      description "月度奖励"
      default []
    end

    attribute :vip_bonus_rate, :decimal do
      allow_nil? false
      public? true
      description "VIP奖励加成比例（%）"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :max_consecutive_days, :integer do
      allow_nil? false
      public? true
      description "最大连续签到天数"
      default 30
      constraints min: 1
    end

    timestamps()
  end

  identities do
    identity :unique_activity_name, [:activity_name]
  end
end
