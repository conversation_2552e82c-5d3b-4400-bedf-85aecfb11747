defmodule RacingGame.Live.AdminPanel.Repositories.RacingGame.RaceRepository do
  @moduledoc """
  赛车比赛数据访问仓储

  提供赛车比赛相关的数据访问抽象：
  - 比赛基础管理
  - 比赛状态查询
  - 比赛结果管理
  - 比赛统计分析
  """

  require Logger
  alias RacingGame.{Race, RaceResult}
  alias RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder
  require Ash.Query

  # 常量定义
  @cache_ttl 300  # 5分钟缓存
  @default_limit 20
  @max_limit 100

  # ============================================================================
  # 比赛基础查询
  # ============================================================================

  @doc """
  根据ID获取比赛

  ## 参数
  - `race_id` - 比赛ID
  - `options` - 选项

  ## 返回
  - `{:ok, race}` - 成功
  - `{:error, :not_found}` - 比赛不存在
  - `{:error, reason}` - 其他错误
  """
  def get_race_by_id(race_id, options \\ []) do
    Logger.debug("🔍 [比赛仓储] 根据ID获取比赛: #{race_id}")

    preload = Keyword.get(options, :preload, [])
    use_cache = Keyword.get(options, :use_cache, true)

    cache_key = build_cache_key("race", race_id, preload)

    if use_cache do
      case get_from_cache(cache_key) do
        {:ok, race} -> {:ok, race}
        :miss -> fetch_and_cache_race_by_id(race_id, preload, cache_key)
      end
    else
      fetch_race_by_id(race_id, preload)
    end
  end

  @doc """
  分页查询比赛列表

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, %{races: races, page_info: page_info}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_races_paginated(params \\ %{}) do
    Logger.debug("📋 [比赛仓储] 分页查询比赛列表")

    try do
      normalized_params = normalize_list_params(params)

      with {:ok, query} <- RacingGameQueryBuilder.build_race_list_query(normalized_params),
           {:ok, results} <- execute_paginated_query(query, normalized_params) do
        {:ok, %{races: results.items, page_info: results.page_info}}
      else
        {:error, reason} -> {:error, reason}
      end
    rescue
      error ->
        Logger.error("❌ [比赛仓储] 分页查询异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  获取当前进行中的比赛

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, races}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_active_races(options \\ []) do
    Logger.debug("🏁 [比赛仓储] 获取当前进行中的比赛")

    try do
      query = Race
      |> Ash.Query.filter(status in [:waiting, :running])
      |> maybe_preload(options[:preload])
      |> apply_sort(options[:sort] || [start_time: :asc])
      |> maybe_limit(options[:limit])

      case Ash.read(query) do
        {:ok, races} -> {:ok, races}
        {:error, error} ->
          Logger.error("❌ [比赛仓储] 获取进行中比赛失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [比赛仓储] 获取进行中比赛异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  获取即将开始的比赛

  ## 参数
  - `minutes` - 未来多少分钟内
  - `options` - 选项

  ## 返回
  - `{:ok, races}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_upcoming_races(minutes \\ 30, options \\ []) do
    Logger.debug("⏰ [比赛仓储] 获取即将开始的比赛: #{minutes}分钟内")

    try do
      now = DateTime.utc_now()
      future_time = DateTime.add(now, minutes * 60, :second)

      query = Race
      |> Ash.Query.filter(status == :waiting and start_time >= ^now and start_time <= ^future_time)
      |> maybe_preload(options[:preload])
      |> apply_sort([start_time: :asc])
      |> maybe_limit(options[:limit])

      case Ash.read(query) do
        {:ok, races} -> {:ok, races}
        {:error, error} ->
          Logger.error("❌ [比赛仓储] 获取即将开始比赛失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [比赛仓储] 获取即将开始比赛异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  搜索比赛

  ## 参数
  - `search_term` - 搜索词
  - `options` - 选项

  ## 返回
  - `{:ok, races}` - 成功
  - `{:error, reason}` - 失败
  """
  def search_races(search_term, options \\ []) do
    Logger.debug("🔍 [比赛仓储] 搜索比赛: #{search_term}")

    limit = Keyword.get(options, :limit, @default_limit)
    fields = Keyword.get(options, :fields, [:name, :description])

    try do
      query = RacingGameQueryBuilder.build_race_search_query(search_term, fields)

      case query
           |> Ash.Query.limit(limit)
           |> Ash.read() do
        {:ok, races} -> {:ok, races}
        {:error, error} ->
          Logger.error("❌ [比赛仓储] 搜索失败: #{inspect(error)}")
          {:error, :search_failed}
      end
    rescue
      error ->
        Logger.error("❌ [比赛仓储] 搜索异常: #{inspect(error)}")
        {:error, :search_error}
    end
  end

  # ============================================================================
  # 比赛创建和管理
  # ============================================================================

  @doc """
  创建比赛

  ## 参数
  - `race_data` - 比赛数据

  ## 返回
  - `{:ok, race}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_race(race_data) do
    Logger.info("➕ [比赛仓储] 创建比赛: #{race_data[:name]}")

    try do
      case Race.create(race_data) do
        {:ok, race} ->
          clear_cache(:race)
          Logger.info("✅ [比赛仓储] 比赛创建成功: #{race.id}")
          {:ok, race}
        {:error, error} ->
          Logger.error("❌ [比赛仓储] 创建比赛失败: #{inspect(error)}")
          {:error, :create_failed}
      end
    rescue
      error ->
        Logger.error("❌ [比赛仓储] 创建比赛异常: #{inspect(error)}")
        {:error, :create_error}
    end
  end

  @doc """
  更新比赛

  ## 参数
  - `race_id` - 比赛ID
  - `update_data` - 更新数据

  ## 返回
  - `{:ok, race}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_race(race_id, update_data) do
    Logger.info("✏️ [比赛仓储] 更新比赛: #{race_id}")

    with {:ok, race} <- get_race_by_id(race_id, use_cache: false),
         {:ok, updated_race} <- do_update_race(race, update_data) do

      clear_cache(:race, race_id)
      Logger.info("✅ [比赛仓储] 比赛更新成功: #{race_id}")
      {:ok, updated_race}
    else
      error -> error
    end
  end

  @doc """
  更新比赛状态

  ## 参数
  - `race_id` - 比赛ID
  - `status` - 新状态
  - `options` - 选项

  ## 返回
  - `{:ok, race}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_race_status(race_id, status, options \\ []) do
    Logger.info("🔄 [比赛仓储] 更新比赛状态: #{race_id} -> #{status}")

    with {:ok, race} <- get_race_by_id(race_id, use_cache: false),
         :ok <- validate_status_transition(race.status, status),
         {:ok, updated_race} <- do_update_race_status(race, status, options) do

      clear_cache(:race, race_id)
      Logger.info("✅ [比赛仓储] 比赛状态更新成功: #{race_id}")
      {:ok, updated_race}
    else
      error -> error
    end
  end

  @doc """
  删除比赛

  ## 参数
  - `race_id` - 比赛ID

  ## 返回
  - `:ok` - 成功
  - `{:error, reason}` - 失败
  """
  def delete_race(race_id) do
    Logger.info("🗑️ [比赛仓储] 删除比赛: #{race_id}")

    with {:ok, race} <- get_race_by_id(race_id),
         :ok <- validate_race_deletion(race),
         :ok <- do_delete_race(race) do

      clear_cache(:race, race_id)
      Logger.info("✅ [比赛仓储] 比赛删除成功: #{race_id}")
      :ok
    else
      error -> error
    end
  end

  # ============================================================================
  # 比赛结果管理
  # ============================================================================

  @doc """
  获取比赛结果

  ## 参数
  - `race_id` - 比赛ID
  - `options` - 选项

  ## 返回
  - `{:ok, results}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_race_results(race_id, options \\ []) do
    Logger.debug("🏆 [比赛仓储] 获取比赛结果: #{race_id}")

    try do
      query = RaceResult
      |> Ash.Query.filter(race_id == ^race_id)
      |> maybe_preload(options[:preload])
      |> apply_sort(options[:sort] || [position: :asc])

      case Ash.read(query) do
        {:ok, results} -> {:ok, results}
        {:error, error} ->
          Logger.error("❌ [比赛仓储] 获取比赛结果失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [比赛仓储] 获取比赛结果异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  创建比赛结果

  ## 参数
  - `result_data` - 结果数据

  ## 返回
  - `{:ok, result}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_race_result(result_data) do
    Logger.info("🏆 [比赛仓储] 创建比赛结果: #{result_data[:race_id]}")

    try do
      case RaceResult.create(result_data) do
        {:ok, result} ->
          clear_cache(:race_result, result_data[:race_id])
          Logger.info("✅ [比赛仓储] 比赛结果创建成功: #{result.id}")
          {:ok, result}
        {:error, error} ->
          Logger.error("❌ [比赛仓储] 创建比赛结果失败: #{inspect(error)}")
          {:error, :create_failed}
      end
    rescue
      error ->
        Logger.error("❌ [比赛仓储] 创建比赛结果异常: #{inspect(error)}")
        {:error, :create_error}
    end
  end

  # ============================================================================
  # 比赛统计
  # ============================================================================

  @doc """
  获取比赛统计信息

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, statistics}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_race_statistics(options \\ []) do
    Logger.debug("📊 [比赛仓储] 获取比赛统计")

    try do
      stats = %{
        total_races: get_total_races(),
        races_by_status: get_races_by_status(),
        races_by_type: get_races_by_type(),
        active_races: get_active_races_count(),
        completed_races: get_completed_races_count(),
        recent_races: get_recent_races_count(options[:days] || 7)
      }

      {:ok, stats}
    rescue
      error ->
        Logger.error("❌ [比赛仓储] 获取统计异常: #{inspect(error)}")
        {:error, :statistics_error}
    end
  end

  # ============================================================================
  # 缓存管理
  # ============================================================================

  @doc """
  清除比赛相关缓存

  ## 参数
  - `cache_type` - 缓存类型
  - `id` - ID（可选）
  """
  def clear_cache(cache_type, id \\ nil) do
    cache_patterns = case {cache_type, id} do
      {:race, nil} -> ["race:*"]
      {:race, race_id} -> ["race:#{race_id}:*"]
      {:race_result, race_id} -> ["race_result:#{race_id}:*"]
      {:all, _} -> ["race:*", "race_result:*"]
    end

    Logger.debug("🧹 [比赛仓储] 清除缓存: #{inspect(cache_patterns)}")
    Enum.each(cache_patterns, &clear_cache_pattern/1)
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 标准化列表参数
  defp normalize_list_params(params) do
    %{
      page: max(1, params[:page] || 1),
      limit: min(params[:limit] || @default_limit, @max_limit),
      search: String.trim(params[:search] || ""),
      filters: params[:filters] || %{},
      sort: params[:sort] || [start_time: :desc],
      preload: params[:preload] || []
    }
  end

  # 获取并缓存比赛
  defp fetch_and_cache_race_by_id(race_id, preload, cache_key) do
    case fetch_race_by_id(race_id, preload) do
      {:ok, race} ->
        cache_put(cache_key, race, @cache_ttl)
        {:ok, race}
      error -> error
    end
  end

  # 获取比赛
  defp fetch_race_by_id(race_id, preload) do
    try do
      case Race
           |> maybe_preload(preload)
           |> Ash.get(race_id) do
        {:ok, race} -> {:ok, race}
        {:error, %Ash.Error.Query.NotFound{}} -> {:error, :not_found}
        {:error, error} ->
          Logger.error("❌ [比赛仓储] 获取比赛失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [比赛仓储] 获取比赛异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # 执行更新比赛
  defp do_update_race(race, update_data) do
    try do
      case race
           |> Ash.Changeset.for_update(:update, update_data)
           |> Ash.update() do
        {:ok, updated_race} -> {:ok, updated_race}
        {:error, error} ->
          Logger.error("❌ [比赛仓储] 更新比赛失败: #{inspect(error)}")
          {:error, :update_failed}
      end
    rescue
      error ->
        Logger.error("❌ [比赛仓储] 更新比赛异常: #{inspect(error)}")
        {:error, :update_error}
    end
  end

  # 验证状态转换
  defp validate_status_transition(current_status, new_status) do
    valid_transitions = %{
      :waiting => [:running, :cancelled],
      :running => [:finished, :cancelled],
      :finished => [],
      :cancelled => []
    }

    allowed_statuses = Map.get(valid_transitions, current_status, [])

    if new_status in allowed_statuses do
      :ok
    else
      {:error, :invalid_status_transition}
    end
  end

  # 执行更新比赛状态
  defp do_update_race_status(race, status, options) do
    update_data = %{status: status}
    update_data = if options[:reason], do: Map.put(update_data, :status_reason, options[:reason]), else: update_data

    do_update_race(race, update_data)
  end

  # 验证比赛删除
  defp validate_race_deletion(race) do
    case race.status do
      :waiting -> :ok
      :cancelled -> :ok
      :running -> {:error, :race_in_progress}
      :finished -> {:error, :race_completed}
      _ -> {:error, :invalid_status}
    end
  end

  # 执行删除比赛
  defp do_delete_race(race) do
    try do
      case Ash.destroy(race) do
        :ok -> :ok
        {:error, error} ->
          Logger.error("❌ [比赛仓储] 删除比赛失败: #{inspect(error)}")
          {:error, :delete_failed}
      end
    rescue
      error ->
        Logger.error("❌ [比赛仓储] 删除比赛异常: #{inspect(error)}")
        {:error, :delete_error}
    end
  end

  # 执行分页查询
  defp execute_paginated_query(query, params) do
    offset = (params.page - 1) * params.limit

    case query
         |> maybe_preload(params.preload)
         |> Ash.Query.page(count: true, limit: params.limit, offset: offset)
         |> Ash.read() do
      {:ok, %Ash.Page.Offset{results: items, count: total_count}} ->
        page_info = %{
          total_count: total_count,
          page: params.page,
          limit: params.limit,
          total_pages: ceil(total_count / params.limit)
        }
        {:ok, %{items: items, page_info: page_info}}
      {:error, error} ->
        Logger.error("❌ [比赛仓储] 分页查询失败: #{inspect(error)}")
        {:error, :query_failed}
    end
  end

  # 可能预加载关联数据
  defp maybe_preload(query, []), do: query
  defp maybe_preload(query, preload) when is_list(preload) do
    Ash.Query.load(query, preload)
  end

  # 可能限制数量
  defp maybe_limit(query, nil), do: query
  defp maybe_limit(query, limit) when is_integer(limit) and limit > 0 do
    Ash.Query.limit(query, limit)
  end
  defp maybe_limit(query, _), do: query

  # 应用排序
  defp apply_sort(query, sort) when is_list(sort) do
    Ash.Query.sort(query, sort)
  end
  defp apply_sort(query, _), do: query

  # 统计函数（简化实现）
  defp get_total_races do
    case Race |> Ash.Query.aggregate(:count, :id) |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_races_by_status do
    case Race
         |> Ash.Query.aggregate(:count, :id, group: [:status])
         |> Ash.read() do
      {:ok, results} ->
        Enum.into(results, %{}, fn %{status: status, count: count} ->
          {status, count}
        end)
      _ -> %{}
    end
  end

  defp get_races_by_type do
    case Race
         |> Ash.Query.aggregate(:count, :id, group: [:race_type])
         |> Ash.read() do
      {:ok, results} ->
        Enum.into(results, %{}, fn %{race_type: type, count: count} ->
          {type, count}
        end)
      _ -> %{}
    end
  end

  defp get_active_races_count do
    case Race
         |> Ash.Query.filter(status in [:waiting, :running])
         |> Ash.Query.aggregate(:count, :id)
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_completed_races_count do
    case Race
         |> Ash.Query.filter(status == :finished)
         |> Ash.Query.aggregate(:count, :id)
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_recent_races_count(days) do
    date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)

    case Race
         |> Ash.Query.filter(inserted_at >= ^date_threshold)
         |> Ash.Query.aggregate(:count, :id)
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  # 构建缓存键
  defp build_cache_key(prefix, id, extra \\ nil) do
    case extra do
      nil -> "#{prefix}:#{id}"
      extra -> "#{prefix}:#{id}:#{:erlang.phash2(extra)}"
    end
  end

  # 缓存操作（需要实现具体的缓存后端）
  defp get_from_cache(_key), do: :miss
  defp cache_put(_key, _value, _ttl), do: :ok
  defp clear_cache_pattern(_pattern), do: :ok
end
