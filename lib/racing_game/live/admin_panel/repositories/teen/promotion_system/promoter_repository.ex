defmodule RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromoterRepository do
  @moduledoc """
  👥 推广员数据访问仓储

  负责推广员相关的数据访问操作，包括：
  - 推广员的CRUD操作
  - 推广员等级管理
  - 推广员状态管理
  - 推广员业绩统计
  - 推广员关系链查询
  """

  require Logger
  alias Teen.PromotionSystem.Promoter
  alias RacingGame.Live.AdminPanel.Utils.CacheUtils

  @cache_ttl 600  # 10分钟缓存

  # ==================== 基础CRUD操作 ====================

  @doc """
  创建推广员

  ## 参数
  - `promoter_data` - 推广员数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, promoter}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_promoter(promoter_data, options \\ []) do
    Logger.info("👥 [推广员仓储] 创建推广员: #{inspect(promoter_data[:user_id])}")

    case Promoter.create(promoter_data) do
      {:ok, promoter} ->
        Logger.info("✅ [推广员仓储] 推广员创建成功: #{promoter.id}")
        clear_promoter_cache(promoter.user_id)
        {:ok, promoter}

      {:error, reason} ->
        Logger.error("❌ [推广员仓储] 推广员创建失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  获取推广员信息

  ## 参数
  - `promoter_id` - 推广员ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, promoter}` - 成功
  - `{:error, :not_found}` - 未找到
  """
  def get_promoter(promoter_id, options \\ []) do
    cache_key = "promoter:#{promoter_id}"

    case CacheUtils.get_or_set(cache_key, @cache_ttl, fn ->
      case Promoter.read(promoter_id) do
        {:ok, promoter} -> {:ok, promoter}
        {:error, _} -> {:error, :not_found}
      end
    end) do
      {:ok, promoter} ->
        Logger.debug("👥 [推广员仓储] 获取推广员成功: #{promoter_id}")
        {:ok, promoter}

      {:error, reason} ->
        Logger.warning("⚠️ [推广员仓储] 推广员不存在: #{promoter_id}")
        {:error, reason}
    end
  end

  @doc """
  根据用户ID获取推广员信息

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, promoter}` - 成功
  - `{:error, :not_found}` - 未找到
  """
  def get_promoter_by_user_id(user_id, options \\ []) do
    cache_key = "promoter:user:#{user_id}"

    case CacheUtils.get_or_set(cache_key, @cache_ttl, fn ->
      case Promoter.read_all(filter: [user_id: user_id], limit: 1) do
        {:ok, [promoter]} -> {:ok, promoter}
        {:ok, []} -> {:error, :not_found}
        {:error, reason} -> {:error, reason}
      end
    end) do
      {:ok, promoter} ->
        Logger.debug("👥 [推广员仓储] 根据用户ID获取推广员成功: #{user_id}")
        {:ok, promoter}

      {:error, reason} ->
        Logger.warning("⚠️ [推广员仓储] 用户推广员不存在: #{user_id}")
        {:error, reason}
    end
  end

  @doc """
  根据推广员代码获取推广员信息

  ## 参数
  - `promoter_code` - 推广员代码
  - `options` - 选项参数

  ## 返回
  - `{:ok, promoter}` - 成功
  - `{:error, :not_found}` - 未找到
  """
  def get_promoter_by_code(promoter_code, options \\ []) do
    cache_key = "promoter:code:#{promoter_code}"

    case CacheUtils.get_or_set(cache_key, @cache_ttl, fn ->
      case Promoter.read_all(filter: [promoter_code: promoter_code], limit: 1) do
        {:ok, [promoter]} -> {:ok, promoter}
        {:ok, []} -> {:error, :not_found}
        {:error, reason} -> {:error, reason}
      end
    end) do
      {:ok, promoter} ->
        Logger.debug("👥 [推广员仓储] 根据代码获取推广员成功: #{promoter_code}")
        {:ok, promoter}

      {:error, reason} ->
        Logger.warning("⚠️ [推广员仓储] 推广员代码不存在: #{promoter_code}")
        {:error, reason}
    end
  end

  @doc """
  更新推广员信息

  ## 参数
  - `promoter_id` - 推广员ID
  - `update_data` - 更新数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, promoter}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_promoter(promoter_id, update_data, options \\ []) do
    Logger.info("👥 [推广员仓储] 更新推广员: #{promoter_id}")

    with {:ok, promoter} <- get_promoter(promoter_id),
         {:ok, updated_promoter} <- Promoter.update(promoter, update_data) do
      Logger.info("✅ [推广员仓储] 推广员更新成功: #{promoter_id}")
      clear_promoter_cache(updated_promoter.user_id)
      {:ok, updated_promoter}
    else
      {:error, reason} ->
        Logger.error("❌ [推广员仓储] 推广员更新失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  删除推广员

  ## 参数
  - `promoter_id` - 推广员ID
  - `options` - 选项参数

  ## 返回
  - `:ok` - 成功
  - `{:error, reason}` - 失败
  """
  def delete_promoter(promoter_id, options \\ []) do
    Logger.info("👥 [推广员仓储] 删除推广员: #{promoter_id}")

    with {:ok, promoter} <- get_promoter(promoter_id),
         :ok <- Promoter.destroy(promoter) do
      Logger.info("✅ [推广员仓储] 推广员删除成功: #{promoter_id}")
      clear_promoter_cache(promoter.user_id)
      :ok
    else
      {:error, reason} ->
        Logger.error("❌ [推广员仓储] 推广员删除失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  # ==================== 推广员状态管理 ====================

  @doc """
  审批推广员申请

  ## 参数
  - `promoter_id` - 推广员ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, promoter}` - 成功
  - `{:error, reason}` - 失败
  """
  def approve_promoter(promoter_id, options \\ []) do
    Logger.info("👥 [推广员仓储] 审批推广员: #{promoter_id}")

    with {:ok, promoter} <- get_promoter(promoter_id),
         {:ok, approved_promoter} <- Promoter.approve(promoter) do
      Logger.info("✅ [推广员仓储] 推广员审批成功: #{promoter_id}")
      clear_promoter_cache(approved_promoter.user_id)
      {:ok, approved_promoter}
    else
      {:error, reason} ->
        Logger.error("❌ [推广员仓储] 推广员审批失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  拒绝推广员申请

  ## 参数
  - `promoter_id` - 推广员ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, promoter}` - 成功
  - `{:error, reason}` - 失败
  """
  def reject_promoter(promoter_id, options \\ []) do
    Logger.info("👥 [推广员仓储] 拒绝推广员: #{promoter_id}")

    with {:ok, promoter} <- get_promoter(promoter_id),
         {:ok, rejected_promoter} <- Promoter.reject(promoter) do
      Logger.info("✅ [推广员仓储] 推广员拒绝成功: #{promoter_id}")
      clear_promoter_cache(rejected_promoter.user_id)
      {:ok, rejected_promoter}
    else
      {:error, reason} ->
        Logger.error("❌ [推广员仓储] 推广员拒绝失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  # ==================== 推广员查询操作 ====================

  @doc """
  获取推广员列表

  ## 参数
  - `filters` - 过滤条件
  - `options` - 选项参数

  ## 返回
  - `{:ok, promoters}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_promoters(filters \\ [], options \\ []) do
    Logger.debug("👥 [推广员仓储] 获取推广员列表")

    page = Keyword.get(options, :page, 1)
    page_size = Keyword.get(options, :page_size, 20)
    sort = Keyword.get(options, :sort, inserted_at: :desc)

    case Promoter.read_all(
      filter: filters,
      page: [limit: page_size, offset: (page - 1) * page_size],
      sort: sort
    ) do
      {:ok, promoters} ->
        Logger.debug("✅ [推广员仓储] 获取推广员列表成功: #{length(promoters)} 条")
        {:ok, promoters}

      {:error, reason} ->
        Logger.error("❌ [推广员仓储] 获取推广员列表失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  获取活跃推广员列表

  ## 参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, promoters}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_active_promoters(options \\ []) do
    Logger.debug("👥 [推广员仓储] 获取活跃推广员列表")
    list_promoters([status: 1], options)
  end

  @doc """
  获取待审核推广员列表

  ## 参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, promoters}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_pending_promoters(options \\ []) do
    Logger.debug("👥 [推广员仓储] 获取待审核推广员列表")
    list_promoters([status: 2], options)
  end

  # ==================== 缓存管理 ====================

  defp clear_promoter_cache(user_id) do
    CacheUtils.delete("promoter:user:#{user_id}")
    CacheUtils.delete_pattern("promoter:*")
  end
end
