defmodule Teen.CustomerService.UserTag do
  @moduledoc """
  用户标签资源
  
  管理用户标签，用于用户分类和管理
  """
  
  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.CustomerService,
    extensions: [AshAdmin.Resource]

  postgres do
    table "user_tags"
    repo Cypridina.Repo
  end

  admin do
    table_columns [:id, :name, :color, :status, :inserted_at]
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_tags
    define :search_by_name
  end

  actions do
    defaults [:create, :read, :update, :destroy]

    read :list_active_tags do
      filter expr(status == 1)
    end

    read :search_by_name do
      argument :name, :string, allow_nil?: false
      filter expr(contains(name, ^arg(:name)))
    end

    update :activate do
      change set_attribute(:status, 1)
    end

    update :deactivate do
      change set_attribute(:status, 0)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :name, :string do
      allow_nil? false
      public? true
      description "标签名称"
      constraints max_length: 50
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "标签描述"
      constraints max_length: 500
    end

    attribute :color, :string do
      allow_nil? false
      public? true
      description "标签颜色（十六进制）"
      default "#007bff"
      constraints max_length: 7
    end

    attribute :status, :integer do
      allow_nil? false
      public? true
      description "状态：0-停用，1-启用"
      default 1
      constraints min: 0, max: 1
    end

    attribute :sort_order, :integer do
      allow_nil? false
      public? true
      description "排序"
      default 0
    end

    attribute :category, :string do
      allow_nil? true
      public? true
      description "标签分类"
      constraints max_length: 50
    end

    timestamps()
  end

  identities do
    identity :unique_tag_name, [:name]
  end
end
