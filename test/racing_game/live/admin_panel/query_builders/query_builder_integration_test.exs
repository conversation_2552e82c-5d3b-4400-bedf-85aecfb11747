defmodule RacingGame.Live.AdminPanel.QueryBuilders.QueryBuilderIntegrationTest do
  @moduledoc """
  🧪 QueryBuilder层集成测试

  测试所有QueryBuilder类的复杂查询、并行处理、统计分析等功能
  """

  use ExUnit.Case, async: true
  
  alias RacingGame.Live.AdminPanel.TestHelper
  
  # QueryBuilder modules
  alias RacingGame.Live.AdminPanel.QueryBuilders.Teen.{
    CustomerServiceQueryBuilder,
    GameManagementQueryBuilder,
    SystemSettingsQueryBuilder,
    PaymentSystemQueryBuilder,
    ActivitySystemQueryBuilder,
    DataStatisticsQueryBuilder,
    PromotionSystemQueryBuilder
  }

  setup do
    TestHelper.setup_test_environment()
    # 创建测试数据集
    test_data = TestHelper.create_test_dataset()
    {:ok, test_data: test_data}
  end

  describe "CustomerServiceQueryBuilder" do
    test "构建客服工单统计查询", %{test_data: test_data} do
      # 测试工单统计查询构建
      {:ok, stats} = CustomerServiceQueryBuilder.build_ticket_stats_query()
      
      # 验证统计结果结构
      assert TestHelper.validate_structure(stats, [
        :total_tickets, :open_tickets, :closed_tickets, :avg_response_time
      ]) == :ok
      
      assert is_integer(stats.total_tickets)
      assert is_integer(stats.open_tickets)
      assert is_integer(stats.closed_tickets)
    end

    test "构建客服绩效分析查询" do
      date_range = {Date.utc_today() |> Date.add(-30), Date.utc_today()}
      
      {:ok, performance} = CustomerServiceQueryBuilder.build_agent_performance_query(date_range)
      
      assert TestHelper.validate_structure(performance, [
        :total_handled, :avg_rating, :response_time_stats
      ]) == :ok
    end

    test "并行查询性能测试" do
      # 测试并行执行多个查询
      {results, time} = TestHelper.measure_time(fn ->
        tasks = [
          Task.async(fn -> CustomerServiceQueryBuilder.build_ticket_stats_query() end),
          Task.async(fn -> CustomerServiceQueryBuilder.build_feedback_analysis_query() end),
          Task.async(fn -> CustomerServiceQueryBuilder.build_announcement_stats_query() end)
        ]
        
        Task.await_many(tasks, 10_000)
      end)
      
      # 所有查询都应该成功
      assert Enum.all?(results, fn {:ok, _} -> true; _ -> false end)
      # 并行查询应该在合理时间内完成
      assert time < 5000
    end
  end

  describe "GameManagementQueryBuilder" do
    test "构建游戏平台综合统计" do
      {:ok, stats} = GameManagementQueryBuilder.build_comprehensive_stats_query()
      
      assert TestHelper.validate_structure(stats, [
        :platform_stats, :vip_stats, :robot_stats
      ]) == :ok
      
      # 验证平台统计
      assert is_map(stats.platform_stats)
      assert Map.has_key?(stats.platform_stats, :total_platforms)
      assert Map.has_key?(stats.platform_stats, :active_platforms)
    end

    test "构建用户VIP信息查询" do
      user_id = :rand.uniform(10000)
      recharge_amount = Decimal.new("5000")
      
      {:ok, vip_info} = GameManagementQueryBuilder.build_user_vip_info_query(user_id, recharge_amount)
      
      assert TestHelper.validate_structure(vip_info, [
        :current_level, :next_level, :upgrade_progress, :privileges
      ]) == :ok
      
      assert is_integer(vip_info.current_level.level)
      assert TestHelper.validate_decimal(vip_info.upgrade_progress) == {:ok, vip_info.upgrade_progress}
    end

    test "构建机器人性能分析" do
      filters = [game_type: "racing", difficulty_level: 2]
      
      {:ok, analysis} = GameManagementQueryBuilder.build_robot_performance_query(filters)
      
      assert TestHelper.validate_structure(analysis, [
        :performance_distribution, :win_rate_analysis, :optimization_suggestions
      ]) == :ok
    end
  end

  describe "PaymentSystemQueryBuilder" do
    test "构建支付系统综合报表" do
      date_range = {Date.utc_today() |> Date.add(-7), Date.utc_today()}
      
      {:ok, report} = PaymentSystemQueryBuilder.build_comprehensive_payment_report(date_range)
      
      assert TestHelper.validate_structure(report, [
        :payment_overview, :channel_analysis, :trend_analysis, :risk_analysis
      ]) == :ok
      
      # 验证支付概览
      overview = report.payment_overview
      assert TestHelper.validate_decimal(overview.total_amount) == {:ok, overview.total_amount}
      assert is_integer(overview.total_orders)
    end

    test "构建支付渠道效果分析" do
      {:ok, analysis} = PaymentSystemQueryBuilder.build_channel_performance_analysis()
      
      assert is_list(analysis.channel_stats)
      
      # 验证每个渠道统计的结构
      if length(analysis.channel_stats) > 0 do
        channel_stat = List.first(analysis.channel_stats)
        assert TestHelper.validate_structure(channel_stat, [
          :channel_name, :success_rate, :avg_amount, :total_volume
        ]) == :ok
      end
    end

    test "构建风险监控查询" do
      {:ok, risk_report} = PaymentSystemQueryBuilder.build_risk_monitoring_query()
      
      assert TestHelper.validate_structure(risk_report, [
        :high_risk_transactions, :suspicious_patterns, :risk_score_distribution
      ]) == :ok
      
      assert is_list(risk_report.high_risk_transactions)
      assert is_list(risk_report.suspicious_patterns)
    end
  end

  describe "ActivitySystemQueryBuilder" do
    test "构建活动系统综合分析" do
      {:ok, analysis} = ActivitySystemQueryBuilder.build_comprehensive_activity_analysis()
      
      assert TestHelper.validate_structure(analysis, [
        :activity_overview, :participation_analysis, :reward_distribution, :effectiveness_metrics
      ]) == :ok
      
      # 验证活动概览
      overview = analysis.activity_overview
      assert is_integer(overview.total_activities)
      assert is_integer(overview.active_activities)
    end

    test "构建活动效果评估" do
      activity_id = :rand.uniform(1000)
      
      {:ok, evaluation} = ActivitySystemQueryBuilder.build_activity_effectiveness_query(activity_id)
      
      assert TestHelper.validate_structure(evaluation, [
        :participation_rate, :completion_rate, :user_engagement, :roi_analysis
      ]) == :ok
      
      assert TestHelper.validate_decimal(evaluation.participation_rate) == {:ok, evaluation.participation_rate}
    end
  end

  describe "DataStatisticsQueryBuilder" do
    test "构建数据统计综合报表" do
      date_range = {Date.utc_today() |> Date.add(-30), Date.utc_today()}
      
      {:ok, report} = DataStatisticsQueryBuilder.build_comprehensive_statistics_report(date_range)
      
      assert TestHelper.validate_structure(report, [
        :online_stats, :user_stats, :coin_stats, :retention_stats
      ]) == :ok
      
      # 验证在线统计
      online_stats = report.online_stats
      assert is_integer(online_stats.peak_online_users)
      assert is_integer(online_stats.avg_online_users)
    end

    test "构建用户行为分析" do
      {:ok, behavior_analysis} = DataStatisticsQueryBuilder.build_user_behavior_analysis()
      
      assert TestHelper.validate_structure(behavior_analysis, [
        :activity_patterns, :engagement_metrics, :retention_analysis
      ]) == :ok
    end

    test "构建数据质量报告" do
      {:ok, quality_report} = DataStatisticsQueryBuilder.build_data_quality_report()
      
      assert TestHelper.validate_structure(quality_report, [
        :completeness_score, :consistency_score, :accuracy_score, :timeliness_score
      ]) == :ok
      
      # 所有质量分数应该在0-100之间
      assert TestHelper.assert_in_range(quality_report.completeness_score, 0, 100)
      assert TestHelper.assert_in_range(quality_report.consistency_score, 0, 100)
    end
  end

  describe "PromotionSystemQueryBuilder" do
    test "构建推广员综合统计" do
      {:ok, stats} = PromotionSystemQueryBuilder.build_promoter_comprehensive_stats()
      
      assert TestHelper.validate_structure(stats, [
        :basic_stats, :level_distribution, :performance_ranking
      ]) == :ok
      
      # 验证基础统计
      basic_stats = stats.basic_stats
      assert is_integer(basic_stats.total_promoters)
      assert is_integer(basic_stats.active_promoters)
    end

    test "构建推广关系链查询" do
      promoter_id = :rand.uniform(1000)
      max_depth = 3
      
      {:ok, relationship_tree} = PromotionSystemQueryBuilder.build_promoter_relationship_tree(promoter_id, max_depth)
      
      assert TestHelper.validate_structure(relationship_tree, [
        :root_promoter, :direct_invites, :total_network_size
      ]) == :ok
      
      assert is_integer(relationship_tree.total_network_size)
    end

    test "构建推广系统健康指标" do
      {:ok, health_metrics} = PromotionSystemQueryBuilder.build_system_health_indicators()
      
      assert TestHelper.validate_structure(health_metrics, [
        :overall_health_score, :promoter_activity_rate, :channel_conversion_rate, :settlement_efficiency
      ]) == :ok
      
      # 健康分数应该在0-100之间
      assert TestHelper.assert_in_range(health_metrics.overall_health_score, 0, 100)
    end
  end

  describe "Cross-System Query Integration" do
    test "跨系统用户数据聚合查询" do
      user_id = :rand.uniform(10000)
      
      # 模拟跨系统查询
      tasks = [
        Task.async(fn -> CustomerServiceQueryBuilder.build_user_service_history_query(user_id) end),
        Task.async(fn -> PaymentSystemQueryBuilder.build_user_payment_summary_query(user_id) end),
        Task.async(fn -> ActivitySystemQueryBuilder.build_user_activity_participation_query(user_id) end),
        Task.async(fn -> PromotionSystemQueryBuilder.build_user_promotion_summary_query(user_id) end)
      ]
      
      results = Task.await_many(tasks, 15_000)
      
      # 所有跨系统查询都应该成功
      assert Enum.all?(results, fn {:ok, _} -> true; _ -> false end)
      
      [service_history, payment_summary, activity_participation, promotion_summary] = results
      
      # 验证每个系统的查询结果
      {:ok, service_data} = service_history
      {:ok, payment_data} = payment_summary
      {:ok, activity_data} = activity_participation
      {:ok, promotion_data} = promotion_summary
      
      assert is_map(service_data)
      assert is_map(payment_data)
      assert is_map(activity_data)
      assert is_map(promotion_data)
    end

    test "系统间数据一致性验证查询" do
      {:ok, consistency_report} = Task.async(fn ->
        # 并行执行一致性检查
        tasks = [
          Task.async(fn -> GameManagementQueryBuilder.build_config_consistency_query() end),
          Task.async(fn -> PaymentSystemQueryBuilder.build_financial_consistency_query() end),
          Task.async(fn -> DataStatisticsQueryBuilder.build_data_integrity_query() end)
        ]
        
        results = Task.await_many(tasks, 20_000)
        
        %{
          game_consistency: elem(Enum.at(results, 0), 1),
          payment_consistency: elem(Enum.at(results, 1), 1),
          data_integrity: elem(Enum.at(results, 2), 1)
        }
      end) |> Task.await(25_000)
      
      assert is_map(consistency_report.game_consistency)
      assert is_map(consistency_report.payment_consistency)
      assert is_map(consistency_report.data_integrity)
    end
  end

  describe "Performance and Scalability" do
    test "大数据量查询性能" do
      # 测试处理大量数据的查询性能
      {result, time} = TestHelper.measure_time(fn ->
        DataStatisticsQueryBuilder.build_comprehensive_statistics_report()
      end)
      
      assert {:ok, _report} = result
      # 大数据量查询应该在合理时间内完成（比如10秒）
      assert time < 10_000
    end

    test "并发复杂查询性能" do
      # 测试多个复杂查询的并发执行
      complex_queries = [
        fn -> GameManagementQueryBuilder.build_comprehensive_stats_query() end,
        fn -> PaymentSystemQueryBuilder.build_comprehensive_payment_report() end,
        fn -> ActivitySystemQueryBuilder.build_comprehensive_activity_analysis() end,
        fn -> PromotionSystemQueryBuilder.build_comprehensive_promotion_report() end
      ]
      
      {results, time} = TestHelper.measure_time(fn ->
        TestHelper.concurrent_test(fn ->
          query_fn = Enum.random(complex_queries)
          query_fn.()
        end, 8)
      end)
      
      # 所有并发查询都应该成功
      assert Enum.all?(results, fn {:ok, _} -> true; _ -> false end)
      # 并发复杂查询应该在合理时间内完成
      assert time < 15_000
    end

    test "内存使用优化验证" do
      # 测试查询是否会导致内存泄漏
      initial_memory = :erlang.memory(:total)
      
      # 执行多次查询
      Enum.each(1..50, fn _ ->
        {:ok, _} = DataStatisticsQueryBuilder.build_comprehensive_statistics_report()
        {:ok, _} = PromotionSystemQueryBuilder.build_comprehensive_promotion_report()
      end)
      
      # 强制垃圾回收
      :erlang.garbage_collect()
      
      final_memory = :erlang.memory(:total)
      memory_increase = final_memory - initial_memory
      
      # 内存增长应该在合理范围内（比如不超过50MB）
      assert memory_increase < 50 * 1024 * 1024
    end
  end
end
