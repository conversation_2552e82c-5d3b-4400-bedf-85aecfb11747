defmodule RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.PromotionSettlementRepository do
  @moduledoc """
  💰 推广结算数据访问仓储

  负责推广结算相关的数据访问操作，包括：
  - 推广结算的CRUD操作
  - 佣金计算和记录
  - 结算状态管理
  - 结算统计分析
  - 结算审核流程
  """

  require Logger
  alias Teen.PromotionSystem.PromotionSettlement
  alias RacingGame.Live.AdminPanel.Utils.CacheUtils

  @cache_ttl 600  # 10分钟缓存

  # ==================== 基础CRUD操作 ====================

  @doc """
  创建推广结算记录

  ## 参数
  - `settlement_data` - 结算数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, settlement}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_settlement(settlement_data, options \\ []) do
    Logger.info("💰 [推广结算仓储] 创建推广结算: #{inspect(settlement_data[:promoter_id])}")

    case PromotionSettlement.create(settlement_data) do
      {:ok, settlement} ->
        Logger.info("✅ [推广结算仓储] 推广结算创建成功: #{settlement.id}")
        clear_settlement_cache(settlement.promoter_id)
        {:ok, settlement}

      {:error, reason} ->
        Logger.error("❌ [推广结算仓储] 推广结算创建失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  获取推广结算信息

  ## 参数
  - `settlement_id` - 结算ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, settlement}` - 成功
  - `{:error, :not_found}` - 未找到
  """
  def get_settlement(settlement_id, options \\ []) do
    cache_key = "promotion_settlement:#{settlement_id}"

    case CacheUtils.get_or_set(cache_key, @cache_ttl, fn ->
      case PromotionSettlement.read(settlement_id) do
        {:ok, settlement} -> {:ok, settlement}
        {:error, _} -> {:error, :not_found}
      end
    end) do
      {:ok, settlement} ->
        Logger.debug("💰 [推广结算仓储] 获取推广结算成功: #{settlement_id}")
        {:ok, settlement}

      {:error, reason} ->
        Logger.warning("⚠️ [推广结算仓储] 推广结算不存在: #{settlement_id}")
        {:error, reason}
    end
  end

  @doc """
  更新推广结算信息

  ## 参数
  - `settlement_id` - 结算ID
  - `update_data` - 更新数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, settlement}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_settlement(settlement_id, update_data, options \\ []) do
    Logger.info("💰 [推广结算仓储] 更新推广结算: #{settlement_id}")

    with {:ok, settlement} <- get_settlement(settlement_id),
         {:ok, updated_settlement} <- PromotionSettlement.update(settlement, update_data) do
      Logger.info("✅ [推广结算仓储] 推广结算更新成功: #{settlement_id}")
      clear_settlement_cache(updated_settlement.promoter_id)
      {:ok, updated_settlement}
    else
      {:error, reason} ->
        Logger.error("❌ [推广结算仓储] 推广结算更新失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  删除推广结算

  ## 参数
  - `settlement_id` - 结算ID
  - `options` - 选项参数

  ## 返回
  - `:ok` - 成功
  - `{:error, reason}` - 失败
  """
  def delete_settlement(settlement_id, options \\ []) do
    Logger.info("💰 [推广结算仓储] 删除推广结算: #{settlement_id}")

    with {:ok, settlement} <- get_settlement(settlement_id),
         :ok <- PromotionSettlement.destroy(settlement) do
      Logger.info("✅ [推广结算仓储] 推广结算删除成功: #{settlement_id}")
      clear_settlement_cache(settlement.promoter_id)
      :ok
    else
      {:error, reason} ->
        Logger.error("❌ [推广结算仓储] 推广结算删除失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  # ==================== 推广结算查询操作 ====================

  @doc """
  获取推广员的结算列表

  ## 参数
  - `promoter_id` - 推广员ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, settlements}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_settlements_by_promoter(promoter_id, options \\ []) do
    Logger.debug("💰 [推广结算仓储] 获取推广员结算列表: #{promoter_id}")

    page = Keyword.get(options, :page, 1)
    page_size = Keyword.get(options, :page_size, 20)
    sort = Keyword.get(options, :sort, settlement_date: :desc)

    case PromotionSettlement.read_all(
      filter: [promoter_id: promoter_id],
      page: [limit: page_size, offset: (page - 1) * page_size],
      sort: sort
    ) do
      {:ok, settlements} ->
        Logger.debug("✅ [推广结算仓储] 获取推广员结算列表成功: #{length(settlements)} 条")
        {:ok, settlements}

      {:error, reason} ->
        Logger.error("❌ [推广结算仓储] 获取推广员结算列表失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  根据结算状态获取结算列表

  ## 参数
  - `status` - 结算状态
  - `options` - 选项参数

  ## 返回
  - `{:ok, settlements}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_settlements_by_status(status, options \\ []) do
    Logger.debug("💰 [推广结算仓储] 根据状态获取结算列表: #{status}")

    page = Keyword.get(options, :page, 1)
    page_size = Keyword.get(options, :page_size, 20)
    sort = Keyword.get(options, :sort, settlement_date: :desc)

    case PromotionSettlement.read_all(
      filter: [status: status],
      page: [limit: page_size, offset: (page - 1) * page_size],
      sort: sort
    ) do
      {:ok, settlements} ->
        Logger.debug("✅ [推广结算仓储] 根据状态获取结算列表成功: #{length(settlements)} 条")
        {:ok, settlements}

      {:error, reason} ->
        Logger.error("❌ [推广结算仓储] 根据状态获取结算列表失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  获取待审核结算列表

  ## 参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, settlements}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_pending_settlements(options \\ []) do
    Logger.debug("💰 [推广结算仓储] 获取待审核结算列表")
    list_settlements_by_status(0, options)
  end

  @doc """
  获取已完成结算列表

  ## 参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, settlements}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_completed_settlements(options \\ []) do
    Logger.debug("💰 [推广结算仓储] 获取已完成结算列表")
    list_settlements_by_status(1, options)
  end

  # ==================== 结算统计操作 ====================

  @doc """
  获取推广员结算统计

  ## 参数
  - `promoter_id` - 推广员ID
  - `date_range` - 日期范围
  - `options` - 选项参数

  ## 返回
  - `{:ok, stats}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_promoter_settlement_stats(promoter_id, date_range \\ nil, options \\ []) do
    Logger.debug("💰 [推广结算仓储] 获取推广员结算统计: #{promoter_id}")

    filters = [promoter_id: promoter_id]
    filters = if date_range, do: add_date_range_filter(filters, date_range), else: filters

    case PromotionSettlement.read_all(filter: filters) do
      {:ok, settlements} ->
        stats = calculate_settlement_stats(settlements)
        Logger.debug("✅ [推广结算仓储] 获取推广员结算统计成功: #{promoter_id}")
        {:ok, stats}

      {:error, reason} ->
        Logger.error("❌ [推广结算仓储] 获取推广员结算统计失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  获取系统结算统计

  ## 参数
  - `date_range` - 日期范围
  - `options` - 选项参数

  ## 返回
  - `{:ok, stats}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_system_settlement_stats(date_range \\ nil, options \\ []) do
    Logger.debug("💰 [推广结算仓储] 获取系统结算统计")

    filters = []
    filters = if date_range, do: add_date_range_filter(filters, date_range), else: filters

    case PromotionSettlement.read_all(filter: filters) do
      {:ok, settlements} ->
        stats = calculate_settlement_stats(settlements)
        Logger.debug("✅ [推广结算仓储] 获取系统结算统计成功")
        {:ok, stats}

      {:error, reason} ->
        Logger.error("❌ [推广结算仓储] 获取系统结算统计失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  # ==================== 私有辅助函数 ====================

  defp add_date_range_filter(filters, {start_date, end_date}) do
    filters ++ [settlement_date: [gte: start_date, lte: end_date]]
  end

  defp calculate_settlement_stats(settlements) do
    total_count = length(settlements)
    total_amount = settlements |> Enum.map(& &1.commission_amount) |> Enum.reduce(Decimal.new("0"), &Decimal.add/2)
    pending_count = settlements |> Enum.count(& &1.status == 0)
    completed_count = settlements |> Enum.count(& &1.status == 1)
    failed_count = settlements |> Enum.count(& &1.status == 2)

    %{
      total_count: total_count,
      total_amount: total_amount,
      pending_count: pending_count,
      completed_count: completed_count,
      failed_count: failed_count,
      completion_rate: if(total_count > 0, do: Decimal.div(Decimal.new(completed_count), Decimal.new(total_count)) |> Decimal.mult(Decimal.new(100)) |> Decimal.round(2), else: Decimal.new("0.00"))
    }
  end

  defp clear_settlement_cache(promoter_id) do
    CacheUtils.delete_pattern("promotion_settlement:*")
    CacheUtils.delete_pattern("promoter:#{promoter_id}:settlements:*")
  end
end
