defmodule RacingGame.Live.AdminPanel.Repositories.QueryBuilders.UserQueryBuilder do
  @moduledoc """
  用户查询构建器

  提供复杂用户查询的构建功能：
  - 动态查询条件构建
  - 搜索查询优化
  - 过滤条件组合
  - 排序和分页支持
  """

  require Logger
  alias Cypridina.Accounts.User
  require Ash.Query

  # 常量定义
  @searchable_fields [:username, :email, :phone]
  @filterable_fields [:permission_level, :active, :inserted_at, :updated_at]
  @sortable_fields [:username, :email, :permission_level, :inserted_at, :updated_at]

  # ============================================================================
  # 公共API
  # ============================================================================

  @doc """
  构建列表查询

  ## 参数
  - `params` - 查询参数
    - `:search` - 搜索关键词
    - `:filters` - 过滤条件
    - `:sort` - 排序条件

  ## 返回
  - `{:ok, query}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_list_query(params) do
    Logger.debug("🔨 [查询构建器] 构建用户列表查询")

    try do
      query = User
      |> apply_search_conditions(params[:search])
      |> apply_filter_conditions(params[:filters])
      |> apply_sort_conditions(params[:sort])

      {:ok, query}
    rescue
      error ->
        Logger.error("❌ [查询构建器] 构建查询失败: #{inspect(error)}")
        {:error, "查询构建失败"}
    end
  end

  @doc """
  构建搜索查询

  ## 参数
  - `search_term` - 搜索词
  - `fields` - 搜索字段列表

  ## 返回
  - 查询对象
  """
  def build_search_query(search_term, fields \\ @searchable_fields) do
    Logger.debug("🔍 [查询构建器] 构建搜索查询: #{search_term}")

    User
    |> apply_search_across_fields(search_term, fields)
    |> Ash.Query.sort(username: :asc)
  end

  @doc """
  构建统计查询

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, query}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_statistics_query(params \\ %{}) do
    Logger.debug("📊 [查询构建器] 构建统计查询")

    try do
      query = User
      |> apply_date_range_filter(params[:date_from], params[:date_to])
      |> apply_filter_conditions(params[:filters])

      {:ok, query}
    rescue
      error ->
        Logger.error("❌ [查询构建器] 构建统计查询失败: #{inspect(error)}")
        {:error, "统计查询构建失败"}
    end
  end

  @doc """
  构建权限过滤查询

  ## 参数
  - `current_user` - 当前用户
  - `base_query` - 基础查询（可选）

  ## 返回
  - 查询对象
  """
  def build_permission_filtered_query(current_user, base_query \\ User) do
    Logger.debug("🔐 [查询构建器] 构建权限过滤查询")

    base_query
    |> apply_permission_filter(current_user)
  end

  @doc """
  构建代理关系查询

  ## 参数
  - `agent_id` - 代理ID
  - `options` - 选项

  ## 返回
  - 查询对象
  """
  def build_agent_subordinates_query(agent_id, options \\ []) do
    Logger.debug("👥 [查询构建器] 构建代理下级查询: #{agent_id}")

    include_inactive = Keyword.get(options, :include_inactive, false)

    query = User
    |> Ash.Query.filter(exists(subordinate_relationships, agent_id == ^agent_id))

    if include_inactive do
      query
    else
      Ash.Query.filter(query, active == true)
    end
  end

  # ============================================================================
  # 搜索条件应用
  # ============================================================================

  # 应用搜索条件
  defp apply_search_conditions(query, nil), do: query
  defp apply_search_conditions(query, ""), do: query
  defp apply_search_conditions(query, search_term) do
    trimmed_search = String.trim(search_term)

    if String.length(trimmed_search) >= 2 do
      apply_search_across_fields(query, trimmed_search, @searchable_fields)
    else
      query
    end
  end

  # 跨字段搜索
  defp apply_search_across_fields(query, search_term, fields) do
    search_conditions = build_search_conditions(search_term, fields)

    case search_conditions do
      [] -> query
      [condition] -> Ash.Query.filter(query, ^condition)
      conditions -> Ash.Query.filter(query, ^{:or, conditions})
    end
  end

  # 构建搜索条件
  defp build_search_conditions(_search_term, _fields) do
    # TODO: 实现搜索条件构建逻辑
    # 这里需要根据Ash框架的查询语法来构建搜索条件
    []
  end

  # ============================================================================
  # 过滤条件应用
  # ============================================================================

  # 应用过滤条件
  defp apply_filter_conditions(query, nil), do: query
  defp apply_filter_conditions(query, filters) when map_size(filters) == 0, do: query
  defp apply_filter_conditions(query, filters) do
    Enum.reduce(filters, query, fn {field, value}, acc_query ->
      apply_single_filter(acc_query, field, value)
    end)
  end

  # 应用单个过滤条件
  defp apply_single_filter(query, field, value) when field in @filterable_fields do
    case {field, value} do
      {:permission_level, level} when is_integer(level) ->
        Ash.Query.filter(query, permission_level == ^level)

      {:active, status} when is_boolean(status) ->
        Ash.Query.filter(query, active == ^status)

      {:inserted_at, %{from: from_date, to: to_date}} ->
        apply_date_range_filter(query, from_date, to_date)

      {:updated_at, %{from: from_date, to: to_date}} ->
        query
        |> Ash.Query.filter(updated_at >= ^from_date)
        |> Ash.Query.filter(updated_at <= ^to_date)

      _ ->
        Logger.warning("⚠️ [查询构建器] 忽略无效过滤条件: #{field} = #{inspect(value)}")
        query
    end
  end
  defp apply_single_filter(query, field, _value) do
    Logger.warning("⚠️ [查询构建器] 忽略不支持的过滤字段: #{field}")
    query
  end

  # 应用日期范围过滤
  defp apply_date_range_filter(query, nil, nil), do: query
  defp apply_date_range_filter(query, from_date, nil) do
    Ash.Query.filter(query, inserted_at >= ^from_date)
  end
  defp apply_date_range_filter(query, nil, to_date) do
    Ash.Query.filter(query, inserted_at <= ^to_date)
  end
  defp apply_date_range_filter(query, from_date, to_date) do
    query
    |> Ash.Query.filter(inserted_at >= ^from_date)
    |> Ash.Query.filter(inserted_at <= ^to_date)
  end

  # ============================================================================
  # 排序条件应用
  # ============================================================================

  # 应用排序条件
  defp apply_sort_conditions(query, nil), do: Ash.Query.sort(query, inserted_at: :desc)
  defp apply_sort_conditions(query, []), do: Ash.Query.sort(query, inserted_at: :desc)
  defp apply_sort_conditions(query, sort_conditions) when is_list(sort_conditions) do
    validated_sorts = validate_sort_conditions(sort_conditions)

    case validated_sorts do
      [] -> Ash.Query.sort(query, inserted_at: :desc)
      sorts -> Ash.Query.sort(query, sorts)
    end
  end
  defp apply_sort_conditions(query, sort_condition) do
    apply_sort_conditions(query, [sort_condition])
  end

  # 验证排序条件
  defp validate_sort_conditions(sort_conditions) do
    Enum.reduce(sort_conditions, [], fn sort_condition, acc ->
      case validate_single_sort_condition(sort_condition) do
        {:ok, validated_sort} -> [validated_sort | acc]
        :error -> acc
      end
    end)
    |> Enum.reverse()
  end

  # 验证单个排序条件
  defp validate_single_sort_condition({field, direction})
       when field in @sortable_fields and direction in [:asc, :desc] do
    {:ok, {field, direction}}
  end
  defp validate_single_sort_condition(field) when field in @sortable_fields do
    {:ok, {field, :asc}}
  end
  defp validate_single_sort_condition(sort_condition) do
    Logger.warning("⚠️ [查询构建器] 忽略无效排序条件: #{inspect(sort_condition)}")
    :error
  end

  # ============================================================================
  # 权限过滤
  # ============================================================================

  # 应用权限过滤
  defp apply_permission_filter(query, current_user) do
    case current_user.permission_level do
      level when level >= 2 ->
        # 超级管理员可以看到所有用户
        query

      1 ->
        # 管理员可以看到普通用户和自己
        Ash.Query.filter(query, permission_level <= 1)

      _ ->
        # 普通用户只能看到自己
        Ash.Query.filter(query, id == ^current_user.id)
    end
  end

  # ============================================================================
  # 查询优化
  # ============================================================================

  @doc """
  优化查询性能

  ## 参数
  - `query` - 查询对象
  - `options` - 优化选项

  ## 返回
  - 优化后的查询对象
  """
  def optimize_query(query, options \\ []) do
    Logger.debug("⚡ [查询构建器] 优化查询性能")

    query
    |> maybe_add_index_hints(options)
    |> maybe_limit_results(options)
    |> maybe_select_fields(options)
  end

  # 可能添加索引提示
  defp maybe_add_index_hints(query, options) do
    # TODO: 根据查询条件添加索引提示
    query
  end

  # 可能限制结果数量
  defp maybe_limit_results(query, options) do
    case Keyword.get(options, :max_results) do
      nil -> query
      max_results when is_integer(max_results) and max_results > 0 ->
        Ash.Query.limit(query, max_results)
      _ -> query
    end
  end

  # 可能选择特定字段
  defp maybe_select_fields(query, options) do
    case Keyword.get(options, :select_fields) do
      nil -> query
      fields when is_list(fields) ->
        Ash.Query.select(query, fields)
      _ -> query
    end
  end

  # ============================================================================
  # 查询分析
  # ============================================================================

  @doc """
  分析查询复杂度

  ## 参数
  - `query` - 查询对象

  ## 返回
  - 复杂度分析结果
  """
  def analyze_query_complexity(query) do
    Logger.debug("🔍 [查询构建器] 分析查询复杂度")

    # TODO: 实现查询复杂度分析
    %{
      complexity_score: :medium,
      estimated_cost: :low,
      suggestions: []
    }
  end

  @doc """
  获取查询执行计划

  ## 参数
  - `query` - 查询对象

  ## 返回
  - 执行计划信息
  """
  def get_execution_plan(query) do
    Logger.debug("📋 [查询构建器] 获取查询执行计划")

    # TODO: 实现查询执行计划获取
    %{
      plan_type: :index_scan,
      estimated_rows: :unknown,
      estimated_time: :unknown
    }
  end
end
