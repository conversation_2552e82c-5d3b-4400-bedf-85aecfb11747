defmodule <PERSON>prid<PERSON>.Teen.GameSystem.Games.Baccarat.BaccaratGame do
  @moduledoc """
  百家乐游戏定义模块

  实现游戏工厂行为，定义百家乐游戏的基本信息和配置
  这是一个示例，展示如何添加新游戏而不修改房间管理器
  """

  @behaviour Cypridina.RoomSystem.GameFactory

  @impl true
  def game_type, do: :baccarat

  @impl true
  def game_name, do: "百家乐"

  @impl true
  def room_module, do: <PERSON>pridina.Teen.GameSystem.Games.Baccarat.BaccaratRoom

  @impl true
  def default_config do
    %{
      max_players: 100,
      # 百人场不需要最小玩家数
      min_players: 0,
      # 2秒后自动开始
      auto_start_delay: 2000,
      enable_robots: true,
      robot_count: 10,
      # 下注时间(秒)
      bet_time: 20,
      # 发牌时间(秒)
      deal_time: 5,
      # 亮牌时间(秒)
      reveal_time: 8,
      # 结算时间(秒)
      settle_time: 5,
      # 最小下注
      min_bet: 20,
      # 最大下注 (单次)
      max_bet: 20000,
      # 个人总下注限制
      max_total_bet: 100000,
      # 区域下注限制
      area_bet_limits: %{
        banker: 200000,    # 庄家
        player: 200000,    # 闲家
        tie: 100000,       # 和
        banker_pair: 50000, # 庄对
        player_pair: 50000  # 闲对
      }
    }
  end

  @impl true
  def is_lobby_game?, do: true

  @impl true
  def supported_game_ids do
    [
      # 百家乐游戏ID
      202
    ]
  end
end
