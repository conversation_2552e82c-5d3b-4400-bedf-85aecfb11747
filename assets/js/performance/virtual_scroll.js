/**
 * Racing Game Admin Panel - 虚拟滚动系统
 * 
 * 实现高性能的虚拟滚动，支持大数据量列表渲染
 */

/**
 * 虚拟滚动管理器
 */
class VirtualScrollManager {
  constructor(container, options = {}) {
    this.container = container;
    this.options = {
      itemHeight: options.itemHeight || 60,
      bufferSize: options.bufferSize || 5,
      threshold: options.threshold || 0.1,
      estimatedItemHeight: options.estimatedItemHeight || 60,
      overscan: options.overscan || 3,
      ...options
    };

    this.items = [];
    this.visibleItems = [];
    this.startIndex = 0;
    this.endIndex = 0;
    this.scrollTop = 0;
    this.containerHeight = 0;
    this.totalHeight = 0;
    this.itemHeights = new Map();
    
    this.isScrolling = false;
    this.scrollTimeout = null;
    this.resizeObserver = null;
    
    this.init();
  }

  init() {
    this.setupContainer();
    this.setupScrollListener();
    this.setupResizeObserver();
    this.calculateVisibleRange();
    this.render();
  }

  /**
   * 设置容器
   */
  setupContainer() {
    this.container.style.position = 'relative';
    this.container.style.overflow = 'auto';
    
    // 创建虚拟滚动内容容器
    this.scrollContent = document.createElement('div');
    this.scrollContent.className = 'virtual-scroll-content';
    this.scrollContent.style.position = 'relative';
    
    // 创建可见项目容器
    this.viewport = document.createElement('div');
    this.viewport.className = 'virtual-scroll-viewport';
    this.viewport.style.position = 'absolute';
    this.viewport.style.top = '0';
    this.viewport.style.left = '0';
    this.viewport.style.right = '0';
    
    this.scrollContent.appendChild(this.viewport);
    this.container.appendChild(this.scrollContent);
    
    this.containerHeight = this.container.clientHeight;
  }

  /**
   * 设置滚动监听器
   */
  setupScrollListener() {
    let ticking = false;
    
    this.container.addEventListener('scroll', () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          this.handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    });
  }

  /**
   * 设置尺寸变化观察器
   */
  setupResizeObserver() {
    if (window.ResizeObserver) {
      this.resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          if (entry.target === this.container) {
            this.handleResize();
          }
        }
      });
      
      this.resizeObserver.observe(this.container);
    }
  }

  /**
   * 处理滚动事件
   */
  handleScroll() {
    const newScrollTop = this.container.scrollTop;
    
    if (Math.abs(newScrollTop - this.scrollTop) < 1) {
      return;
    }
    
    this.scrollTop = newScrollTop;
    this.isScrolling = true;
    
    // 清除之前的滚动结束定时器
    if (this.scrollTimeout) {
      clearTimeout(this.scrollTimeout);
    }
    
    // 设置滚动结束定时器
    this.scrollTimeout = setTimeout(() => {
      this.isScrolling = false;
      this.onScrollEnd();
    }, 150);
    
    this.calculateVisibleRange();
    this.render();
  }

  /**
   * 处理容器尺寸变化
   */
  handleResize() {
    const newHeight = this.container.clientHeight;
    if (newHeight !== this.containerHeight) {
      this.containerHeight = newHeight;
      this.calculateVisibleRange();
      this.render();
    }
  }

  /**
   * 滚动结束回调
   */
  onScrollEnd() {
    // 可以在这里执行一些滚动结束后的优化操作
    this.optimizeRendering();
  }

  /**
   * 计算可见范围
   */
  calculateVisibleRange() {
    if (this.items.length === 0) {
      this.startIndex = 0;
      this.endIndex = 0;
      return;
    }

    const { itemHeight, overscan } = this.options;
    const visibleCount = Math.ceil(this.containerHeight / itemHeight);
    
    this.startIndex = Math.floor(this.scrollTop / itemHeight);
    this.endIndex = Math.min(
      this.startIndex + visibleCount + overscan,
      this.items.length
    );
    
    // 添加缓冲区
    this.startIndex = Math.max(0, this.startIndex - overscan);
  }

  /**
   * 渲染可见项目
   */
  render() {
    if (this.items.length === 0) {
      this.viewport.innerHTML = '';
      return;
    }

    // 更新总高度
    this.totalHeight = this.items.length * this.options.itemHeight;
    this.scrollContent.style.height = `${this.totalHeight}px`;

    // 清空当前视口
    this.viewport.innerHTML = '';
    
    // 设置视口位置
    const offsetY = this.startIndex * this.options.itemHeight;
    this.viewport.style.transform = `translateY(${offsetY}px)`;

    // 渲染可见项目
    this.visibleItems = [];
    for (let i = this.startIndex; i < this.endIndex; i++) {
      const item = this.items[i];
      if (item) {
        const element = this.renderItem(item, i);
        this.viewport.appendChild(element);
        this.visibleItems.push({ item, element, index: i });
      }
    }

    // 触发渲染完成事件
    this.onRenderComplete();
  }

  /**
   * 渲染单个项目
   */
  renderItem(item, index) {
    const element = document.createElement('div');
    element.className = 'virtual-scroll-item';
    element.style.height = `${this.options.itemHeight}px`;
    element.style.position = 'relative';
    element.dataset.index = index;

    // 调用自定义渲染函数
    if (this.options.renderItem) {
      const content = this.options.renderItem(item, index);
      if (typeof content === 'string') {
        element.innerHTML = content;
      } else if (content instanceof HTMLElement) {
        element.appendChild(content);
      }
    } else {
      element.textContent = JSON.stringify(item);
    }

    return element;
  }

  /**
   * 渲染完成回调
   */
  onRenderComplete() {
    // 触发自定义事件
    const event = new CustomEvent('virtualScrollRender', {
      detail: {
        startIndex: this.startIndex,
        endIndex: this.endIndex,
        visibleItems: this.visibleItems,
        totalItems: this.items.length
      }
    });
    this.container.dispatchEvent(event);
  }

  /**
   * 优化渲染性能
   */
  optimizeRendering() {
    // 在滚动停止后进行一些优化操作
    // 例如：预加载即将可见的项目、清理不需要的DOM元素等
    
    if (this.options.onOptimize) {
      this.options.onOptimize(this.visibleItems);
    }
  }

  /**
   * 设置数据
   */
  setItems(items) {
    this.items = items || [];
    this.itemHeights.clear();
    this.calculateVisibleRange();
    this.render();
  }

  /**
   * 添加项目
   */
  addItem(item, index = -1) {
    if (index >= 0 && index < this.items.length) {
      this.items.splice(index, 0, item);
    } else {
      this.items.push(item);
    }
    this.calculateVisibleRange();
    this.render();
  }

  /**
   * 移除项目
   */
  removeItem(index) {
    if (index >= 0 && index < this.items.length) {
      this.items.splice(index, 1);
      this.itemHeights.delete(index);
      this.calculateVisibleRange();
      this.render();
    }
  }

  /**
   * 更新项目
   */
  updateItem(index, item) {
    if (index >= 0 && index < this.items.length) {
      this.items[index] = item;
      // 如果项目在可见范围内，重新渲染
      if (index >= this.startIndex && index < this.endIndex) {
        this.render();
      }
    }
  }

  /**
   * 滚动到指定项目
   */
  scrollToItem(index, alignment = 'auto') {
    if (index < 0 || index >= this.items.length) {
      return;
    }

    const itemTop = index * this.options.itemHeight;
    let scrollTop;

    switch (alignment) {
      case 'start':
        scrollTop = itemTop;
        break;
      case 'center':
        scrollTop = itemTop - (this.containerHeight - this.options.itemHeight) / 2;
        break;
      case 'end':
        scrollTop = itemTop - this.containerHeight + this.options.itemHeight;
        break;
      default: // 'auto'
        if (itemTop < this.scrollTop) {
          scrollTop = itemTop;
        } else if (itemTop + this.options.itemHeight > this.scrollTop + this.containerHeight) {
          scrollTop = itemTop - this.containerHeight + this.options.itemHeight;
        } else {
          return; // 已经可见，不需要滚动
        }
    }

    scrollTop = Math.max(0, Math.min(scrollTop, this.totalHeight - this.containerHeight));
    this.container.scrollTop = scrollTop;
  }

  /**
   * 获取可见项目
   */
  getVisibleItems() {
    return this.visibleItems;
  }

  /**
   * 获取项目总数
   */
  getItemCount() {
    return this.items.length;
  }

  /**
   * 销毁虚拟滚动
   */
  destroy() {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
    
    if (this.scrollTimeout) {
      clearTimeout(this.scrollTimeout);
    }
    
    this.container.innerHTML = '';
    this.items = [];
    this.visibleItems = [];
    this.itemHeights.clear();
  }
}

/**
 * 虚拟表格实现
 */
class VirtualTable extends VirtualScrollManager {
  constructor(container, options = {}) {
    super(container, {
      itemHeight: 50,
      ...options
    });
    
    this.columns = options.columns || [];
    this.setupTable();
  }

  setupTable() {
    // 创建表格头部
    this.createTableHeader();
    
    // 调整容器样式
    this.container.classList.add('virtual-table');
  }

  createTableHeader() {
    const header = document.createElement('div');
    header.className = 'virtual-table-header';
    header.style.position = 'sticky';
    header.style.top = '0';
    header.style.zIndex = '10';
    header.style.backgroundColor = 'var(--color-base-100)';
    header.style.borderBottom = '1px solid var(--color-base-300)';

    const headerRow = document.createElement('div');
    headerRow.className = 'virtual-table-header-row flex';
    headerRow.style.height = `${this.options.itemHeight}px`;

    this.columns.forEach(column => {
      const cell = document.createElement('div');
      cell.className = 'virtual-table-header-cell flex-1 px-4 py-2 font-semibold';
      cell.textContent = column.title || column.key;
      cell.style.width = column.width || 'auto';
      headerRow.appendChild(cell);
    });

    header.appendChild(headerRow);
    this.container.insertBefore(header, this.scrollContent);
  }

  renderItem(item, index) {
    const element = document.createElement('div');
    element.className = 'virtual-table-row flex border-b border-base-200';
    element.style.height = `${this.options.itemHeight}px`;
    element.dataset.index = index;

    this.columns.forEach(column => {
      const cell = document.createElement('div');
      cell.className = 'virtual-table-cell flex-1 px-4 py-2 flex items-center';
      cell.style.width = column.width || 'auto';
      
      const value = this.getCellValue(item, column);
      if (column.render) {
        const rendered = column.render(value, item, index);
        if (typeof rendered === 'string') {
          cell.innerHTML = rendered;
        } else if (rendered instanceof HTMLElement) {
          cell.appendChild(rendered);
        }
      } else {
        cell.textContent = value;
      }
      
      element.appendChild(cell);
    });

    return element;
  }

  getCellValue(item, column) {
    if (column.dataIndex) {
      return this.getNestedValue(item, column.dataIndex);
    }
    return item[column.key] || '';
  }

  getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }
}

// Phoenix LiveView Hook
const VirtualScrollHook = {
  mounted() {
    const options = {
      itemHeight: parseInt(this.el.dataset.itemHeight) || 60,
      renderItem: (item, index) => {
        // 触发Phoenix事件来渲染项目
        this.pushEvent('render_virtual_item', { item, index });
        return `<div class="loading loading-spinner"></div>`;
      }
    };

    this.virtualScroll = new VirtualScrollManager(this.el, options);
    
    // 监听数据更新
    this.handleEvent('update_virtual_data', ({ items }) => {
      this.virtualScroll.setItems(items);
    });
  },

  destroyed() {
    if (this.virtualScroll) {
      this.virtualScroll.destroy();
    }
  }
};

// 导出
export { VirtualScrollManager, VirtualTable, VirtualScrollHook };
