#!/usr/bin/env elixir

# 🔧 修复Ash查询中的 ^ 操作符问题
# 在Ash框架中，不需要使用 ^ 操作符来引用变量

defmodule AshQueryFixer do
  @moduledoc """
  修复Ash查询中的 ^ 操作符问题
  """

  def run do
    IO.puts("🔧 开始修复Ash查询中的 ^ 操作符问题...")

    # 查找所有Repository文件
    repository_files = find_repository_files()

    IO.puts("📁 找到 #{length(repository_files)} 个Repository文件")

    # 修复每个文件
    results = Enum.map(repository_files, &fix_file/1)

    # 统计结果
    total_files = length(results)
    fixed_files = results |> Enum.count(fn {_, status} -> status == :fixed end)
    skipped_files = results |> Enum.count(fn {_, status} -> status == :skipped end)
    error_files = results |> Enum.count(fn {_, status} -> status == :error end)

    IO.puts("\n" <> "=" |> String.duplicate(50))
    IO.puts("📊 修复结果统计:")
    IO.puts("   总文件数: #{total_files}")
    IO.puts("   已修复: #{fixed_files} ✅")
    IO.puts("   跳过: #{skipped_files} ⏭️")
    IO.puts("   错误: #{error_files} ❌")

    if error_files == 0 do
      IO.puts("\n🎉 所有文件修复完成！")
    else
      IO.puts("\n⚠️ 部分文件修复失败，请检查错误信息")
    end
  end

  defp find_repository_files do
    # 查找所有Repository和QueryBuilder文件
    (Path.wildcard("lib/racing_game/live/admin_panel/repositories/**/*_repository.ex") ++
     Path.wildcard("lib/racing_game/live/admin_panel/repositories/**/*_query_builder.ex") ++
     Path.wildcard("lib/racing_game/live/admin_panel/query_builders/**/*.ex"))
    |> Enum.filter(&File.exists?/1)
    |> Enum.uniq()
  end

  defp fix_file(file_path) do
    IO.write("修复 #{Path.basename(file_path)}... ")

    try do
      content = File.read!(file_path)

      # 检查是否包含需要修复的模式
      if String.contains?(content, "^") and String.contains?(content, "Ash.Query.filter") do
        fixed_content = fix_ash_queries(content)

        if fixed_content != content do
          File.write!(file_path, fixed_content)
          IO.puts("✅ 已修复")
          {file_path, :fixed}
        else
          IO.puts("⏭️ 无需修复")
          {file_path, :skipped}
        end
      else
        IO.puts("⏭️ 跳过")
        {file_path, :skipped}
      end
    rescue
      error ->
        IO.puts("❌ 错误: #{inspect(error)}")
        {file_path, :error}
    end
  end

  defp fix_ash_queries(content) do
    content
    # 修复所有包含 ^ 操作符的行
    |> String.split("\n")
    |> Enum.map(&fix_line/1)
    |> Enum.join("\n")
  end

  defp fix_line(line) do
    cond do
      # 跳过注释行
      String.trim(line) |> String.starts_with?("#") ->
        line

      # 修复 Ash.Query.filter 中的 ^ 操作符
      String.contains?(line, "Ash.Query.filter") and String.contains?(line, "^") ->
        fix_ash_filter_line(line)

      # 修复其他包含 ^ 的查询行
      String.contains?(line, "^") and (String.contains?(line, "==") or String.contains?(line, "<=") or String.contains?(line, ">=")) ->
        fix_query_line(line)

      true ->
        line
    end
  end

  defp fix_ash_filter_line(line) do
    line
    # 基本比较操作符
    |> String.replace(~r/\s*==\s*\^([a-zA-Z_][a-zA-Z0-9_]*)/, " == \\1")
    |> String.replace(~r/\s*<=\s*\^([a-zA-Z_][a-zA-Z0-9_]*)/, " <= \\1")
    |> String.replace(~r/\s*>=\s*\^([a-zA-Z_][a-zA-Z0-9_]*)/, " >= \\1")
    |> String.replace(~r/\s*<\s*\^([a-zA-Z_][a-zA-Z0-9_]*)/, " < \\1")
    |> String.replace(~r/\s*>\s*\^([a-zA-Z_][a-zA-Z0-9_]*)/, " > \\1")
    # 函数调用中的 ^
    |> String.replace(~r/contains\(([^,]+),\s*\^([a-zA-Z_][a-zA-Z0-9_]*)\)/, "contains(\\1, \\2)")
    |> String.replace(~r/ilike\(([^,]+),\s*\^([a-zA-Z_][a-zA-Z0-9_]*)\)/, "ilike(\\1, \\2)")
    # in 操作符
    |> String.replace(~r/\s+in\s+\^([a-zA-Z_][a-zA-Z0-9_]*)/, " in \\1")
    # 复杂条件中的 ^
    |> String.replace(~r/\^([a-zA-Z_][a-zA-Z0-9_]*)\s+and/, "\\1 and")
    |> String.replace(~r/\s+or\s+([^=]+)\s*<=\s*\^([a-zA-Z_][a-zA-Z0-9_]*)/, " or \\1 <= \\2")
  end

  defp fix_query_line(line) do
    line
    |> String.replace(~r/\s*==\s*\^([a-zA-Z_][a-zA-Z0-9_]*)/, " == \\1")
    |> String.replace(~r/\s*<=\s*\^([a-zA-Z_][a-zA-Z0-9_]*)/, " <= \\1")
    |> String.replace(~r/\s*>=\s*\^([a-zA-Z_][a-zA-Z0-9_]*)/, " >= \\1")
    |> String.replace(~r/\s*<\s*\^([a-zA-Z_][a-zA-Z0-9_]*)/, " < \\1")
    |> String.replace(~r/\s*>\s*\^([a-zA-Z_][a-zA-Z0-9_]*)/, " > \\1")
    |> String.replace(~r/\s+in\s+\^([a-zA-Z_][a-zA-Z0-9_]*)/, " in \\1")
  end

  def show_problematic_patterns do
    IO.puts("🔍 查找包含问题模式的文件...")

    repository_files = find_repository_files()

    Enum.each(repository_files, fn file_path ->
      content = File.read!(file_path)

      # 查找包含 ^ 操作符的 Ash.Query.filter 行
      lines = String.split(content, "\n")

      problematic_lines = lines
      |> Enum.with_index(1)
      |> Enum.filter(fn {line, _} ->
        String.contains?(line, "Ash.Query.filter") and String.contains?(line, "^")
      end)

      if length(problematic_lines) > 0 do
        IO.puts("\n📄 #{file_path}:")
        Enum.each(problematic_lines, fn {line, line_num} ->
          IO.puts("   第#{line_num}行: #{String.trim(line)}")
        end)
      end
    end)
  end
end

# 主程序入口
case System.argv() do
  ["--show"] ->
    AshQueryFixer.show_problematic_patterns()

  [] ->
    AshQueryFixer.run()

  _ ->
    IO.puts("用法:")
    IO.puts("  elixir fix_ash_queries.exs        # 修复所有问题")
    IO.puts("  elixir fix_ash_queries.exs --show # 显示问题模式")
end
