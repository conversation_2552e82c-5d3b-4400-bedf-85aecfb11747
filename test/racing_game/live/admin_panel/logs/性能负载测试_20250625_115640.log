Compiling 76 files (.ex)
    warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
    │
 97 │           Logger.warn("⚠️ [CDKEY活动仓储] CDKEY不存在: #{code}")
    │                  ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex:97:18: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.CdkeyActivityRepository.get_cdkey_by_code/2

     warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
     │
 130 │           Logger.warn("⚠️ [CDKEY活动仓储] CDKEY不存在: #{cdkey_id}")
     │                  ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex:130:18: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.CdkeyActivityRepository.get_cdkey/2

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 201 │       {:updated_at, %{from: from_date, to: to_date}} ->
     │                             ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/user_query_builder.ex:201:29: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.UserQueryBuilder.apply_single_filter/3

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 201 │       {:updated_at, %{from: from_date, to: to_date}} ->
     │                                            ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/user_query_builder.ex:201:44: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.UserQueryBuilder.apply_single_filter/3

     warning: variable "max_level" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 146 │   defp apply_user_filter(query, :agent_level_range, {min_level, max_level}) do
     │                                                                 ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:146:65: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_user_filter/3

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 218 │   defp apply_date_range_filter(query, from_date, nil) do
     │                                       ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/user_query_builder.ex:218:39: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.UserQueryBuilder.apply_date_range_filter/3

     warning: variable "min_level" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 146 │   defp apply_user_filter(query, :agent_level_range, {min_level, max_level}) do
     │                                                      ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:146:54: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_user_filter/3

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 221 │   defp apply_date_range_filter(query, nil, to_date) do
     │                                            ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/user_query_builder.ex:221:44: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.UserQueryBuilder.apply_date_range_filter/3

     warning: variable "user_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 264 │   defp apply_user_recipient_filter(query, user_id) do
     │                                           ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/communication_query_builder.ex:264:43: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CommunicationQueryBuilder.apply_user_recipient_filter/2

     warning: variable "status_code" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 220 │       status_code ->
     │       ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/bet_query_builder.ex:220:7: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.BetQueryBuilder.apply_status_filter/2

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 282 │   defp apply_date_range_filter(query, from_date, nil) do
     │                                       ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/stock_query_builder.ex:282:39: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.StockQueryBuilder.apply_date_range_filter/3

     warning: variable "now" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 278 │     now = DateTime.utc_now()
     │     ~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/communication_query_builder.ex:278:5: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CommunicationQueryBuilder.apply_expiry_filter/1

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 224 │   defp apply_date_range_filter(query, from_date, to_date) do
     │                                       ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/user_query_builder.ex:224:39: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.UserQueryBuilder.apply_date_range_filter/3

     warning: variable "date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 166 │   defp apply_user_filter(query, :created_after, date) do
     │                                                 ~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:166:49: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_user_filter/3

     warning: variable "cache_patterns" is unused (there is a variable with the same name in the context, use the pin operator (^) to match on it or prefix this variable with underscore if it is not meant to be used)
     │
 262 │       cache_patterns = ["bet:#{bet_id}:*" | cache_patterns]
     │       ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/bet_repository.ex:262:7: RacingGame.Live.AdminPanel.Repositories.BetRepository.clear_cache/2

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 285 │   defp apply_date_range_filter(query, nil, to_date) do
     │                                            ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/stock_query_builder.ex:285:44: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.StockQueryBuilder.apply_date_range_filter/3

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 224 │   defp apply_date_range_filter(query, from_date, to_date) do
     │                                                  ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/user_query_builder.ex:224:50: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.UserQueryBuilder.apply_date_range_filter/3

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 297 │       {:updated_at, %{from: from_date, to: to_date}} ->
     │                             ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/communication_query_builder.ex:297:29: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CommunicationQueryBuilder.apply_single_filter/3

     warning: variable "date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 170 │   defp apply_user_filter(query, :created_before, date) do
     │                                                  ~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:170:50: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_user_filter/3

     warning: variable "cache_patterns" is unused (there is a variable with the same name in the context, use the pin operator (^) to match on it or prefix this variable with underscore if it is not meant to be used)
     │
 266 │       cache_patterns = ["user_bets:#{user_id}:*" | cache_patterns]
     │       ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/bet_repository.ex:266:7: RacingGame.Live.AdminPanel.Repositories.BetRepository.clear_cache/2

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 288 │   defp apply_date_range_filter(query, from_date, to_date) do
     │                                       ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/stock_query_builder.ex:288:39: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.StockQueryBuilder.apply_date_range_filter/3

     warning: variable "end_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 174 │   defp apply_user_filter(query, :created_date_range, {start_date, end_date}) do
     │                                                                   ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:174:67: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_user_filter/3

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 297 │       {:updated_at, %{from: from_date, to: to_date}} ->
     │                                            ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/communication_query_builder.ex:297:44: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CommunicationQueryBuilder.apply_single_filter/3

     warning: variable "cache_patterns" is unused (there is a variable with the same name in the context, use the pin operator (^) to match on it or prefix this variable with underscore if it is not meant to be used)
     │
 270 │       cache_patterns = ["bet:*", "user_bets:*"]
     │       ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/bet_repository.ex:270:7: RacingGame.Live.AdminPanel.Repositories.BetRepository.clear_cache/2

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 288 │   defp apply_date_range_filter(query, from_date, to_date) do
     │                                                  ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/stock_query_builder.ex:288:50: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.StockQueryBuilder.apply_date_range_filter/3

     warning: variable "start_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 174 │   defp apply_user_filter(query, :created_date_range, {start_date, end_date}) do
     │                                                       ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:174:55: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_user_filter/3

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 302 │       {:expires_at, %{from: from_date, to: to_date}} ->
     │                             ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/communication_query_builder.ex:302:29: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CommunicationQueryBuilder.apply_single_filter/3

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 275 │   defp apply_date_range_filter(query, from_date, nil) do
     │                                       ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/bet_query_builder.ex:275:39: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.BetQueryBuilder.apply_date_range_filter/3

     warning: variable "account_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 254 │         {:ok, account_id} ->
     │               ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/ledger_query_builder.ex:254:15: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.LedgerQueryBuilder.build_user_financial_query/3

     warning: variable "max_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 178 │   defp apply_user_filter(query, :numeric_id_range, {min_id, max_id}) do
     │                                                             ~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:178:61: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_user_filter/3

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 302 │       {:expires_at, %{from: from_date, to: to_date}} ->
     │                                            ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/communication_query_builder.ex:302:44: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CommunicationQueryBuilder.apply_single_filter/3

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 315 │       {:inserted_at, %{from: from_date, to: to_date}} ->
     │                              ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/stock_query_builder.ex:315:30: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.StockQueryBuilder.apply_single_filter/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 143 │   def get_user_agent(user_id, options \\ []) do
     │                               ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/agent_relationship_repository.ex:143:31: RacingGame.Live.AdminPanel.Repositories.Accounts.AgentRelationshipRepository.get_user_agent/2

     warning: variable "min_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 178 │   defp apply_user_filter(query, :numeric_id_range, {min_id, max_id}) do
     │                                                     ~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:178:53: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_user_filter/3

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 278 │   defp apply_date_range_filter(query, nil, to_date) do
     │                                            ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/bet_query_builder.ex:278:44: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.BetQueryBuilder.apply_date_range_filter/3

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 318 │   defp maybe_add_index_hints(query, options) do
     │                                     ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/user_query_builder.ex:318:37: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.UserQueryBuilder.maybe_add_index_hints/2

     warning: variable "token_value" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 175 │   def get_token_by_value(token_value, options \\ []) do
     │                          ~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/user_identity_repository.ex:175:26: RacingGame.Live.AdminPanel.Repositories.Accounts.UserIdentityRepository.get_token_by_value/2

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 315 │       {:inserted_at, %{from: from_date, to: to_date}} ->
     │                                             ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/stock_query_builder.ex:315:45: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.StockQueryBuilder.apply_single_filter/3

     warning: variable "account_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 263 │         {:ok, account_id} ->
     │               ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/ledger_query_builder.ex:263:15: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.LedgerQueryBuilder.build_user_financial_query/3

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 281 │   defp apply_date_range_filter(query, from_date, to_date) do
     │                                       ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/bet_query_builder.ex:281:39: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.BetQueryBuilder.apply_date_range_filter/3

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 281 │   defp apply_date_range_filter(query, from_date, to_date) do
     │                                                  ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/bet_query_builder.ex:281:50: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.BetQueryBuilder.apply_date_range_filter/3

     warning: variable "query" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 356 │   def analyze_query_complexity(query) do
     │                                ~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/user_query_builder.ex:356:32: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.UserQueryBuilder.analyze_query_complexity/1

     warning: variable "date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 228 │   defp apply_identity_filter(query, :created_after, date) do
     │                                                     ~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:228:53: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_identity_filter/3

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 319 │   defp apply_date_range_filter(query, from_date, nil) do
     │                                       ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/communication_query_builder.ex:319:39: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CommunicationQueryBuilder.apply_date_range_filter/3

     warning: variable "date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 232 │   defp apply_identity_filter(query, :created_before, date) do
     │                                                      ~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:232:54: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_identity_filter/3

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 322 │   defp apply_date_range_filter(query, nil, to_date) do
     │                                            ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/communication_query_builder.ex:322:44: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CommunicationQueryBuilder.apply_date_range_filter/3

     warning: variable "query" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 376 │   def get_execution_plan(query) do
     │                          ~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/user_query_builder.ex:376:26: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.UserQueryBuilder.get_execution_plan/1

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 325 │   defp apply_date_range_filter(query, from_date, to_date) do
     │                                       ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/communication_query_builder.ex:325:39: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CommunicationQueryBuilder.apply_date_range_filter/3

     warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
     │
 269 │           Logger.warn("⚠️ [余额仓储] 批量创建部分失败: 成功 #{length(successes)}, 失败 #{length(failures)}")
     │                  ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/ledger/balance_repository.ex:269:18: RacingGame.Live.AdminPanel.Repositories.Ledger.BalanceRepository.create_balances_batch/1

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 273 │   def get_account_balance(account_id, options \\ []) do
     │                                       ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/ledger/account_repository.ex:273:39: RacingGame.Live.AdminPanel.Repositories.Ledger.AccountRepository.get_account_balance/2

     warning: variable "from_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 311 │       {:updated_at, %{from: from_date, to: to_date}} ->
     │                             ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/bet_query_builder.ex:311:29: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.BetQueryBuilder.apply_single_filter/3

     warning: variable "max_level" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 277 │   defp apply_agent_relationship_filter(query, :level_range, {min_level, max_level}) do
     │                                                                         ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:277:73: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_agent_relationship_filter/3

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 325 │   defp apply_date_range_filter(query, from_date, to_date) do
     │                                                  ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/communication_query_builder.ex:325:50: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CommunicationQueryBuilder.apply_date_range_filter/3

     warning: variable "read_ids_query" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 320 │       read_ids_query = SystemCommunicationRead
     │       ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:320:7: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.build_user_unread_communications_query/2

     warning: variable "min_level" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 277 │   defp apply_agent_relationship_filter(query, :level_range, {min_level, max_level}) do
     │                                                              ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:277:62: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_agent_relationship_filter/3

     warning: variable "to_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 311 │       {:updated_at, %{from: from_date, to: to_date}} ->
     │                                            ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/bet_query_builder.ex:311:44: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.BetQueryBuilder.apply_single_filter/3

     warning: variable "search_term" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 378 │   defp get_matching_user_ids(search_term) do
     │                              ~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/stock_query_builder.ex:378:30: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.StockQueryBuilder.get_matching_user_ids/1

     warning: variable "options" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 302 │   def get_user_balance(user_id, currency \\ :XAA, options \\ []) do
     │                                                   ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/ledger/account_repository.ex:302:51: RacingGame.Live.AdminPanel.Repositories.Ledger.AccountRepository.get_user_balance/3

     warning: variable "date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 285 │   defp apply_agent_relationship_filter(query, :created_after, date) do
     │                                                               ~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:285:63: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_agent_relationship_filter/3

     warning: variable "date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 289 │   defp apply_agent_relationship_filter(query, :created_before, date) do
     │                                                                ~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:289:64: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_agent_relationship_filter/3

     warning: variable "end_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 293 │   defp apply_agent_relationship_filter(query, :created_date_range, {start_date, end_date}) do
     │                                                                                 ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:293:81: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_agent_relationship_filter/3

     warning: variable "start_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 293 │   defp apply_agent_relationship_filter(query, :created_date_range, {start_date, end_date}) do
     │                                                                     ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:293:69: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.apply_agent_relationship_filter/3

     warning: variable "pattern" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 405 │   defp apply_single_search_condition(query, {:ilike, field, pattern}) do
     │                                                             ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/ledger_query_builder.ex:405:61: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.LedgerQueryBuilder.apply_single_search_condition/2

     warning: variable "pattern" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 414 │       {:ilike, field, pattern} -> Ash.Query.filter(query, ilike(^field, pattern))
     │                       ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/ledger_query_builder.ex:414:23: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.LedgerQueryBuilder.apply_multiple_search_conditions/2

     warning: variable "type" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 336 │   defp apply_type_filter(query, type) do
     │                                 ~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/communication_repository.ex:336:33: RacingGame.Live.AdminPanel.Repositories.CommunicationRepository.apply_type_filter/2

     warning: variable "pattern" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 510 │     Enum.reduce(conditions, query, fn {op, field, pattern}, acc_query ->
     │                                                   ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:510:51: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_search_across_fields/3

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 429 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/ledger_query_builder.ex:429:5: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.LedgerQueryBuilder.maybe_limit_by_days/2

     warning: variable "race_issue" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 361 │   defp apply_race_issue_filter(query, race_issue) do
     │                                       ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/bet_repository.ex:361:39: RacingGame.Live.AdminPanel.Repositories.BetRepository.apply_race_issue_filter/2

     warning: variable "resource" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 477 │   defp execute_paginated_query(query, params, resource) do
     │                                               ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/user_repository.ex:477:47: RacingGame.Live.AdminPanel.Repositories.Accounts.UserRepository.execute_paginated_query/3

     warning: variable "user_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 532 │   defp apply_single_filter(query, :user_id, user_id) do
     │                                             ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:532:45: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_single_filter/3

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 391 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/communication_repository.ex:391:5: RacingGame.Live.AdminPanel.Repositories.CommunicationRepository.get_recent_count/1

     warning: variable "search_term" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 374 │   defp get_matching_user_ids(search_term) do
     │                              ~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/bet_query_builder.ex:374:30: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.BetQueryBuilder.get_matching_user_ids/1

     warning: variable "identifier" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 435 │   defp get_account_id_by_identifier(identifier) do
     │                                     ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/ledger_query_builder.ex:435:37: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.LedgerQueryBuilder.get_account_id_by_identifier/1

     warning: variable "customer_service_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 535 │   defp apply_single_filter(query, :customer_service_id, customer_service_id) do
     │                                                         ~~~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:535:57: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_single_filter/3

     warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
     │
 456 │     Logger.warn("⚠️ [查询构建器] 未知条件类型: #{inspect(condition)}")
     │            ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/accounts_query_builder.ex:456:12: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder.build_condition_expr/1

     warning: variable "assigned_to" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 538 │   defp apply_single_filter(query, :assigned_to, assigned_to) do
     │                                                 ~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:538:49: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_single_filter/3

     warning: variable "end_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 456 │   defp apply_race_filter(query, :date_range, {start_date, end_date}) do
     │                                                           ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:456:59: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.apply_race_filter/3

     warning: variable "start_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 456 │   defp apply_race_filter(query, :date_range, {start_date, end_date}) do
     │                                               ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:456:47: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.apply_race_filter/3

     warning: variable "auditor_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 541 │   defp apply_single_filter(query, :auditor_id, auditor_id) do
     │                                                ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:541:48: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_single_filter/3

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 567 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/user_repository.ex:567:5: RacingGame.Live.AdminPanel.Repositories.Accounts.UserRepository.get_recent_registrations_count/1

     warning: variable "search_term" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 464 │     search_term = "%#{search}%"
     │     ~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:464:5: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.apply_race_search/2

     warning: variable "created_by" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 544 │   defp apply_single_filter(query, :created_by, created_by) do
     │                                                ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:544:48: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_single_filter/3

     warning: variable "status" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 547 │   defp apply_single_filter(query, :status, status) do
     │                                            ~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:547:44: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_single_filter/3

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 470 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:470:5: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.build_recent_races_query/1

     warning: variable "priority" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 550 │   defp apply_single_filter(query, :priority, priority) do
     │                                              ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:550:46: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_single_filter/3

     warning: variable "progress_status" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 553 │   defp apply_single_filter(query, :progress_status, progress_status) do
     │                                                     ~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:553:53: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_single_filter/3

     warning: variable "word_type" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 556 │   defp apply_single_filter(query, :word_type, word_type) do
     │                                               ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:556:47: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_single_filter/3

     warning: variable "purpose" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 476 │   defp maybe_filter_token_purpose(query, purpose) do
     │                                          ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/user_identity_repository.ex:476:42: RacingGame.Live.AdminPanel.Repositories.Accounts.UserIdentityRepository.maybe_filter_token_purpose/2

     warning: variable "agent_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 427 │   defp check_existing_relationship(user_id, agent_id) do
     │                                             ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/agent_relationship_repository.ex:427:45: RacingGame.Live.AdminPanel.Repositories.Accounts.AgentRelationshipRepository.check_existing_relationship/2

     warning: variable "tag_type" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 559 │   defp apply_single_filter(query, :tag_type, tag_type) do
     │                                              ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:559:46: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_single_filter/3

     warning: variable "user_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 427 │   defp check_existing_relationship(user_id, agent_id) do
     │                                    ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/agent_relationship_repository.ex:427:36: RacingGame.Live.AdminPanel.Repositories.Accounts.AgentRelationshipRepository.check_existing_relationship/2

     warning: variable "now" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 482 │     now = DateTime.utc_now()
     │     ~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/user_identity_repository.ex:482:5: RacingGame.Live.AdminPanel.Repositories.Accounts.UserIdentityRepository.maybe_filter_active_tokens/2

     warning: variable "code_type" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 562 │   defp apply_single_filter(query, :code_type, code_type) do
     │                                               ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:562:47: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_single_filter/3

     warning: variable "search" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 506 │   defp apply_bet_search(query, search) do
     │                                ~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:506:32: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.apply_bet_search/2

     warning: variable "now" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 527 │     now = DateTime.utc_now()
     │     ~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/user_identity_repository.ex:527:5: RacingGame.Live.AdminPanel.Repositories.Accounts.UserIdentityRepository.get_active_tokens_count/0

     warning: variable "enabled" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 565 │   defp apply_single_filter(query, :enabled, enabled) do
     │                                             ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:565:45: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_single_filter/3

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 513 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:513:5: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.build_recent_bets_query/1

     warning: variable "identifier" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 465 │     identifier = "user:#{currency}:#{user_id}"
     │     ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/ledger/balance_repository.ex:465:5: RacingGame.Live.AdminPanel.Repositories.Ledger.BalanceRepository.get_user_account_id/2

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 587 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/ledger/account_repository.ex:587:5: RacingGame.Live.AdminPanel.Repositories.Ledger.AccountRepository.get_recent_accounts_count/1

     warning: variable "is_used" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 568 │   defp apply_single_filter(query, :is_used, is_used) do
     │                                             ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:568:45: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_single_filter/3

     warning: variable "end_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 572 │     {start_date, end_date} = date_range
     │                  ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:572:18: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_single_filter/3

     warning: variable "now" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 538 │     now = DateTime.utc_now()
     │     ~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/user_identity_repository.ex:538:5: RacingGame.Live.AdminPanel.Repositories.Accounts.UserIdentityRepository.get_expired_tokens_count/0

     warning: variable "start_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 572 │     {start_date, end_date} = date_range
     │      ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:572:6: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_single_filter/3

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 549 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/user_identity_repository.ex:549:5: RacingGame.Live.AdminPanel.Repositories.Accounts.UserIdentityRepository.get_recent_logins_count/1

     warning: variable "search_term" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 544 │     search_term = "%#{search}%"
     │     ~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:544:5: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.apply_stock_search/2

     warning: variable "end_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 576 │     {start_date, end_date} = date_range
     │                  ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:576:18: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_single_filter/3

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 550 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:550:5: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.build_recent_stock_transactions_query/1

     warning: variable "start_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 576 │     {start_date, end_date} = date_range
     │      ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:576:6: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_single_filter/3

     warning: variable "end_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 580 │     {start_date, end_date} = date_range
     │                  ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:580:18: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_single_filter/3

     warning: variable "user_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 558 │   defp build_user_stock_transactions_query(user_id, options) do
     │                                            ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:558:44: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.build_user_stock_transactions_query/2

     warning: variable "end_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 562 │   defp maybe_date_range(query, {start_date, end_date}) do
     │                                             ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/ledger/balance_repository.ex:562:45: RacingGame.Live.AdminPanel.Repositories.Ledger.BalanceRepository.maybe_date_range/2

     warning: variable "start_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 580 │     {start_date, end_date} = date_range
     │      ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:580:6: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_single_filter/3

     warning: variable "start_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 562 │   defp maybe_date_range(query, {start_date, end_date}) do
     │                                 ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/ledger/balance_repository.ex:562:33: RacingGame.Live.AdminPanel.Repositories.Ledger.BalanceRepository.maybe_date_range/2

     warning: variable "level" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 581 │   defp maybe_filter_by_level(query, level) do
     │                                     ~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/agent_relationship_repository.ex:581:37: RacingGame.Live.AdminPanel.Repositories.Accounts.AgentRelationshipRepository.maybe_filter_by_level/2

     warning: variable "end_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 601 │   defp maybe_apply_date_range(query, {start_date, end_date}) do
     │                                                   ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:601:51: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.maybe_apply_date_range/2

     warning: variable "status" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 599 │   defp apply_status_filter(query, status) do
     │                                   ~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:599:35: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_status_filter/2

     warning: variable "start_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 601 │   defp maybe_apply_date_range(query, {start_date, end_date}) do
     │                                       ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:601:39: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.maybe_apply_date_range/2

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 602 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/ledger/balance_repository.ex:602:5: RacingGame.Live.AdminPanel.Repositories.Ledger.BalanceRepository.get_recent_balance_changes/1

     warning: variable "status" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 587 │   defp maybe_filter_by_status(query, status) do
     │                                      ~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/agent_relationship_repository.ex:587:38: RacingGame.Live.AdminPanel.Repositories.Accounts.AgentRelationshipRepository.maybe_filter_by_status/2

     warning: variable "balances" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 614 │   defp calculate_balance_trend(balances, interval) do
     │                                ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/ledger/balance_repository.ex:614:32: RacingGame.Live.AdminPanel.Repositories.Ledger.BalanceRepository.calculate_balance_trend/2

     warning: variable "status" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 607 │   defp maybe_apply_status_filter(query, status) do
     │                                         ~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:607:41: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.maybe_apply_status_filter/2

     warning: variable "priority" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 605 │   defp apply_priority_filter(query, priority) do
     │                                     ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:605:37: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_priority_filter/2

     warning: variable "interval" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 614 │   defp calculate_balance_trend(balances, interval) do
     │                                          ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/ledger/balance_repository.ex:614:42: RacingGame.Live.AdminPanel.Repositories.Ledger.BalanceRepository.calculate_balance_trend/2

     warning: variable "agent_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 592 │   defp get_agent_sub_users_count(agent_id) do
     │                                  ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/agent_relationship_repository.ex:592:34: RacingGame.Live.AdminPanel.Repositories.Accounts.AgentRelationshipRepository.get_agent_sub_users_count/1

     warning: variable "date_from" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 618 │   defp maybe_apply_date_from(query, date_from) do
     │                                     ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:618:37: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.maybe_apply_date_from/2

     warning: variable "bet_type" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 613 │   defp maybe_apply_bet_type_filter(query, bet_type) do
     │                                           ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:613:43: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.maybe_apply_bet_type_filter/2

     warning: variable "agent_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 602 │   defp get_active_sub_users_count(agent_id) do
     │                                   ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/agent_relationship_repository.ex:602:35: RacingGame.Live.AdminPanel.Repositories.Accounts.AgentRelationshipRepository.get_active_sub_users_count/1

     warning: variable "date_to" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 623 │   defp maybe_apply_date_to(query, date_to) do
     │                                   ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:623:35: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.maybe_apply_date_to/2

     warning: variable "stock_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 626 │   defp maybe_apply_stock_filter(query, stock_id) do
     │                                        ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:626:40: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.maybe_apply_stock_filter/2

     warning: variable "word_type" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 629 │   defp apply_word_type_filter(query, word_type) do
     │                                      ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:629:38: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_word_type_filter/2

     warning: variable "transaction_type" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 632 │   defp maybe_apply_transaction_type_filter(query, transaction_type) do
     │                                                   ~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:632:51: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.maybe_apply_transaction_type_filter/2

     warning: variable "tag_type" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 635 │   defp apply_tag_type_filter(query, tag_type) do
     │                                     ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:635:37: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_tag_type_filter/2

     warning: variable "agent_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 613 │   defp get_direct_sub_users_count(agent_id) do
     │                                   ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/agent_relationship_repository.ex:613:35: RacingGame.Live.AdminPanel.Repositories.Accounts.AgentRelationshipRepository.get_direct_sub_users_count/1

     warning: variable "code_type" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 641 │   defp apply_code_type_filter(query, code_type) do
     │                                      ~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:641:38: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.apply_code_type_filter/2

     warning: variable "end_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 655 │   defp apply_communication_filter(query, :date_range, {start_date, end_date}) do
     │                                                                    ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:655:68: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.apply_communication_filter/3

     warning: variable "agent_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 623 │   defp get_indirect_sub_users_count(agent_id) do
     │                                     ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/agent_relationship_repository.ex:623:37: RacingGame.Live.AdminPanel.Repositories.Accounts.AgentRelationshipRepository.get_indirect_sub_users_count/1

     warning: variable "start_date" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 655 │   defp apply_communication_filter(query, :date_range, {start_date, end_date}) do
     │                                                        ~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:655:56: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.apply_communication_filter/3

     warning: variable "search_term" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 663 │     search_term = "%#{search}%"
     │     ~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:663:5: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.apply_communication_search/2

     warning: variable "agent_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 649 │   defp get_recent_additions_count(agent_id, days) do
     │                                   ~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/agent_relationship_repository.ex:649:35: RacingGame.Live.AdminPanel.Repositories.Accounts.AgentRelationshipRepository.get_recent_additions_count/2

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 669 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:669:5: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.build_recent_communications_query/1

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 650 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/agent_relationship_repository.ex:650:5: RacingGame.Live.AdminPanel.Repositories.Accounts.AgentRelationshipRepository.get_recent_additions_count/2

     warning: variable "communication_type" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 678 │   defp maybe_apply_communication_type_filter(query, communication_type) do
     │                                                     ~~~~~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/racing_game_query_builder.ex:678:53: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder.maybe_apply_communication_type_filter/2

     warning: variable "date_threshold" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 702 │     date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
     │     ~~~~~~~~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/accounts/agent_relationship_repository.ex:702:5: RacingGame.Live.AdminPanel.Repositories.Accounts.AgentRelationshipRepository.get_recent_relationships_count/1

     warning: variable "one_hour_ago" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 744 │       one_hour_ago = DateTime.add(current_time, -3600, :second)
     │       ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:744:7: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.build_system_health_check_query/1

     warning: variable "params" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 739 │   defp build_system_health_check_query(params) do
     │                                        ~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:739:40: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.build_system_health_check_query/1

     warning: variable "user_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 763 │   defp build_user_questions_query(user_id, options \\ []) do
     │                                   ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:763:35: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.build_user_questions_query/2

     warning: variable "user_id" is unused (if the variable is not meant to be used, prefix it with an underscore)
     │
 771 │   defp build_user_codes_query(user_id, options \\ []) do
     │                               ~~~~~~~
     │
     └─ lib/racing_game/live/admin_panel/repositories/query_builders/customer_service_query_builder.ex:771:31: RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CustomerServiceQueryBuilder.build_user_codes_query/2

     error: undefined variable "expires_at"
     │
 365 │     Ash.Query.filter(query, is_nil(expires_at) or expires_at  >= now)
     │                                                   ^^^^^^^^^^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex:365:51: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.CdkeyActivityRepository.maybe_filter_by_expiry/2

     error: undefined variable "expires_at"
     │
 365 │     Ash.Query.filter(query, is_nil(expires_at) or expires_at  >= now)
     │                                    ^^^^^^^^^^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex:365:36: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.CdkeyActivityRepository.maybe_filter_by_expiry/2

     error: undefined variable "expires_at"
     │
 361 │     Ash.Query.filter(query, not is_nil(expires_at) and expires_at  < now)
     │                                                        ^^^^^^^^^^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex:361:56: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.CdkeyActivityRepository.maybe_filter_by_expiry/2

     error: undefined variable "expires_at"
     │
 361 │     Ash.Query.filter(query, not is_nil(expires_at) and expires_at  < now)
     │                                        ^^^^^^^^^^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex:361:40: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.CdkeyActivityRepository.maybe_filter_by_expiry/2

     error: undefined variable "used_by_user_id"
     │
 354 │     Ash.Query.filter(query, used_by_user_id  == user_id)
     │                             ^^^^^^^^^^^^^^^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex:354:29: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.CdkeyActivityRepository.maybe_filter_by_user/2

     error: undefined variable "id"
     │
 124 │       |> Ash.Query.filter(id == cdkey_id)
     │                           ^^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex:124:27: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.CdkeyActivityRepository.get_cdkey/2

    error: misplaced operator ^code

    The pin operator ^ is supported only inside matches or inside custom macros. Make sure you are inside a match or all necessary macros have been required
    │
 91 │       |> Ash.Query.filter(cdkey == ^code)
    │                                    ^
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex:91:36: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.CdkeyActivityRepository.get_cdkey_by_code/2

    error: undefined variable "cdkey"
    │
 91 │       |> Ash.Query.filter(cdkey == ^code)
    │                           ^^^^^
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex:91:27: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.CdkeyActivityRepository.get_cdkey_by_code/2


== Compilation error in file lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex ==
** (CompileError) lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex: cannot compile module RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.CdkeyActivityRepository (errors have been logged)

