Compiling 76 files (.ex)
    warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
    │
 97 │           Logger.warn("⚠️ [CDKEY活动仓储] CDKEY不存在: #{code}")
    │                  ~
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex:97:18: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.CdkeyActivityRepository.get_cdkey_by_code/2

     warning: Logger.warn/1 is deprecated. Use Logger.warning/2 instead
     │
 130 │           Logger.warn("⚠️ [CDKEY活动仓储] CDKEY不存在: #{cdkey_id}")
     │                  ~
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex:130:18: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.CdkeyActivityRepository.get_cdkey/2

     error: undefined variable "expires_at"
     │
 365 │     Ash.Query.filter(query, is_nil(expires_at) or expires_at  >= now)
     │                                                   ^^^^^^^^^^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex:365:51: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.CdkeyActivityRepository.maybe_filter_by_expiry/2

     error: undefined variable "expires_at"
     │
 365 │     Ash.Query.filter(query, is_nil(expires_at) or expires_at  >= now)
     │                                    ^^^^^^^^^^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex:365:36: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.CdkeyActivityRepository.maybe_filter_by_expiry/2

     error: undefined variable "expires_at"
     │
 361 │     Ash.Query.filter(query, not is_nil(expires_at) and expires_at  < now)
     │                                                        ^^^^^^^^^^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex:361:56: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.CdkeyActivityRepository.maybe_filter_by_expiry/2

     error: undefined variable "expires_at"
     │
 361 │     Ash.Query.filter(query, not is_nil(expires_at) and expires_at  < now)
     │                                        ^^^^^^^^^^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex:361:40: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.CdkeyActivityRepository.maybe_filter_by_expiry/2

     error: undefined variable "used_by_user_id"
     │
 354 │     Ash.Query.filter(query, used_by_user_id  == user_id)
     │                             ^^^^^^^^^^^^^^^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex:354:29: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.CdkeyActivityRepository.maybe_filter_by_user/2

     error: undefined variable "id"
     │
 124 │       |> Ash.Query.filter(id == cdkey_id)
     │                           ^^
     │
     └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex:124:27: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.CdkeyActivityRepository.get_cdkey/2

    error: undefined variable "cdkey"
    │
 91 │       |> Ash.Query.filter(cdkey == code)
    │                           ^^^^^
    │
    └─ lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex:91:27: RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.CdkeyActivityRepository.get_cdkey_by_code/2


== Compilation error in file lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex ==
** (CompileError) lib/racing_game/live/admin_panel/repositories/teen/activity_system/cdkey_activity_repository.ex: cannot compile module RacingGame.Live.AdminPanel.Repositories.Teen.ActivitySystem.CdkeyActivityRepository (errors have been logged)

