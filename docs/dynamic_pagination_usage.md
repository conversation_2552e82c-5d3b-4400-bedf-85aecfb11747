# 动态分页组件使用指南

## 概述

动态分页组件提供了灵活、可复用的分页功能，支持多种分页样式和交互方式。

## 组件文件

- `lib/racing_game/utils/dynamic_pagination.ex` - 分页UI组件
- `lib/racing_game/utils/pagination_handler.ex` - 分页事件处理器

## 基本使用

### 1. 在LiveView组件中引入

```elixir
defmodule YourLiveComponent do
  use Phoenix.LiveComponent
  
  alias RacingGame.Utils.DynamicPagination
  alias RacingGame.Utils.PaginationHandler
  
  # ... 其他代码
end
```

### 2. 初始化分页状态

```elixir
def mount(socket) do
  socket = 
    socket
    |> PaginationHandler.init_pagination(page: 1, per_page: 20, total_count: 0)
    |> load_data()
  
  {:ok, socket}
end
```

### 3. 在模板中使用分页组件

```heex
<!-- 完整版分页 -->
<DynamicPagination.pagination
  current_page={@page}
  total_count={@total_count}
  per_page={@per_page}
  target={@myself}
  page_change_event="page_change"
  per_page_change_event="per_page_change"
  show_per_page_selector={true}
  show_page_info={true}
  show_quick_jump={true}
  per_page_options={[10, 20, 50, 100]}
  max_visible_pages={7}
/>

<!-- 简化版分页 -->
<DynamicPagination.simple_pagination
  current_page={@page}
  total_count={@total_count}
  per_page={@per_page}
  target={@myself}
  page_change_event="page_change"
/>
```

### 4. 处理分页事件

```elixir
# 页码变化
def handle_event("page_change", params, socket) do
  PaginationHandler.handle_page_change(params, socket, &load_data/1)
end

# 每页数量变化
def handle_event("per_page_change", params, socket) do
  PaginationHandler.handle_per_page_change(params, socket, &load_data/1)
end

# 页面跳转
def handle_event("page_jump", params, socket) do
  PaginationHandler.handle_page_jump(params, socket, &load_data/1)
end

# 数据加载函数
defp load_data(socket) do
  # 获取分页参数
  page = socket.assigns.page
  per_page = socket.assigns.per_page
  
  # 加载数据
  {data, total_count} = YourDataModule.get_paginated_data(page, per_page)
  
  socket
  |> assign(:data, data)
  |> PaginationHandler.update_total_count(total_count)
end
```

## 高级用法

### 1. 自定义分页样式

```heex
<DynamicPagination.pagination
  current_page={@page}
  total_count={@total_count}
  per_page={@per_page}
  target={@myself}
  per_page_options={[5, 10, 25, 50]}
  max_visible_pages={5}
  show_per_page_selector={false}
  show_quick_jump={false}
/>
```

### 2. 与数据库查询结合

```elixir
defp load_data_from_db(socket) do
  page = socket.assigns.page
  per_page = socket.assigns.per_page
  
  # 计算SQL分页参数
  {limit, offset} = PaginationHandler.get_sql_pagination(page, per_page)
  
  # 执行数据库查询
  query = from(u in User, limit: ^limit, offset: ^offset)
  data = Repo.all(query)
  total_count = Repo.aggregate(User, :count)
  
  socket
  |> assign(:data, data)
  |> PaginationHandler.update_total_count(total_count)
end
```

### 3. 内存数据分页

```elixir
defp load_data_from_memory(socket) do
  all_data = get_all_data()
  page = socket.assigns.page
  per_page = socket.assigns.per_page
  
  # 对内存数据进行分页
  {paged_data, total_count} = PaginationHandler.paginate_data(all_data, page, per_page)
  
  socket
  |> assign(:data, paged_data)
  |> PaginationHandler.update_total_count(total_count)
end
```

## 组件属性说明

### DynamicPagination.pagination

| 属性 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `current_page` | integer | 是 | - | 当前页码 |
| `total_count` | integer | 是 | - | 总记录数 |
| `per_page` | integer | 是 | - | 每页显示数量 |
| `target` | string | 否 | nil | 事件目标组件 |
| `page_change_event` | string | 否 | "page_change" | 页码变化事件名 |
| `per_page_change_event` | string | 否 | "per_page_change" | 每页数量变化事件名 |
| `show_per_page_selector` | boolean | 否 | true | 是否显示每页数量选择器 |
| `show_page_info` | boolean | 否 | true | 是否显示页面信息 |
| `show_quick_jump` | boolean | 否 | true | 是否显示快速跳转 |
| `per_page_options` | list | 否 | [10, 20, 50, 100] | 每页数量选项 |
| `max_visible_pages` | integer | 否 | 7 | 最大显示页码数 |

### DynamicPagination.simple_pagination

| 属性 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `current_page` | integer | 是 | - | 当前页码 |
| `total_count` | integer | 是 | - | 总记录数 |
| `per_page` | integer | 是 | - | 每页显示数量 |
| `target` | string | 否 | nil | 事件目标组件 |
| `page_change_event` | string | 否 | "page_change" | 页码变化事件名 |

## 事件处理函数

### PaginationHandler 提供的函数

- `handle_page_change/3` - 处理页码变化
- `handle_per_page_change/3` - 处理每页数量变化
- `handle_page_jump/3` - 处理页面跳转
- `init_pagination/2` - 初始化分页状态
- `update_total_count/2` - 更新总记录数
- `get_page_range/2` - 获取页面数据范围
- `paginate_data/3` - 对数据进行分页
- `get_sql_pagination/2` - 生成SQL分页参数

## 样式说明

分页组件使用Tailwind CSS进行样式设计，具有以下特点：

- 响应式设计，在移动设备上自动调整布局
- 统一的颜色方案和交互效果
- 禁用状态的视觉反馈
- 当前页的高亮显示
- 悬停效果和过渡动画

## 最佳实践

1. **性能优化**: 对于大数据集，建议使用数据库分页而不是内存分页
2. **用户体验**: 合理设置每页数量选项，避免单页数据过多
3. **错误处理**: 使用PaginationHandler提供的验证函数确保参数有效性
4. **状态管理**: 在数据变化时及时更新总记录数
5. **响应式设计**: 在移动设备上考虑隐藏部分功能以节省空间

## 集成示例

参考 `system_communications_component.ex` 中的实际使用案例，了解如何在复杂的管理界面中集成分页功能。
