defmodule RacingGame.Live.AdminPanel.TestRunner do
  @moduledoc """
  🧪 Teen系统集成测试运行器

  提供统一的测试运行、报告生成、结果分析功能
  """

  alias RacingGame.Live.AdminPanel.TestHelper

  @test_suites [
    {"Repository层测试", "test/racing_game/live/admin_panel/repositories/repository_integration_test.exs"},
    {"QueryBuilder层测试", "test/racing_game/live/admin_panel/query_builders/query_builder_integration_test.exs"},
    {"Service层测试", "test/racing_game/live/admin_panel/services/service_integration_test.exs"},
    {"跨系统集成测试", "test/racing_game/live/admin_panel/cross_system_integration_test.exs"},
    {"性能负载测试", "test/racing_game/live/admin_panel/performance_load_test.exs"}
  ]

  @doc """
  运行所有测试套件
  """
  def run_all_tests(options \\ []) do
    IO.puts("🚀 开始运行Teen系统完整测试套件...")
    IO.puts("=" |> String.duplicate(80))

    start_time = System.monotonic_time(:millisecond)

    # 设置测试环境
    TestHelper.setup_test_environment()

    # 运行各个测试套件
    results = Enum.map(@test_suites, fn {name, path} ->
      run_test_suite(name, path, options)
    end)

    end_time = System.monotonic_time(:millisecond)
    total_time = end_time - start_time

    # 生成测试报告
    generate_test_report(results, total_time)

    # 清理测试环境
    TestHelper.cleanup_test_data()

    results
  end

  @doc """
  运行单个测试套件
  """
  def run_test_suite(name, path, options \\ []) do
    IO.puts("\n📋 运行测试套件: #{name}")
    IO.puts("-" |> String.duplicate(60))

    start_time = System.monotonic_time(:millisecond)

    # 检查测试文件是否存在
    if File.exists?(path) do
      try do
        # 运行测试
        {output, exit_code} = System.cmd("mix", ["test", path] ++ format_options(options),
          stderr_to_stdout: true,
          cd: File.cwd!()
        )

        end_time = System.monotonic_time(:millisecond)
        execution_time = end_time - start_time

        result = %{
          suite_name: name,
          path: path,
          exit_code: exit_code,
          execution_time: execution_time,
          output: output,
          status: if(exit_code == 0, do: :passed, else: :failed)
        }

        print_suite_result(result)
        result

      rescue
        error ->
          end_time = System.monotonic_time(:millisecond)
          execution_time = end_time - start_time

          result = %{
            suite_name: name,
            path: path,
            exit_code: -1,
            execution_time: execution_time,
            output: "Error: #{inspect(error)}",
            status: :error
          }

          print_suite_result(result)
          result
      end
    else
      IO.puts("❌ 测试文件不存在: #{path}")

      %{
        suite_name: name,
        path: path,
        exit_code: -1,
        execution_time: 0,
        output: "Test file not found",
        status: :not_found
      }
    end
  end

  @doc """
  运行性能测试
  """
  def run_performance_tests(options \\ []) do
    IO.puts("⚡ 运行性能测试套件...")

    performance_options = Keyword.merge(options, [
      timeout: 60_000,  # 性能测试需要更长时间
      max_cases: 10     # 限制并发测试用例数
    ])

    run_test_suite("性能负载测试", "test/racing_game/live/admin_panel/performance_load_test.exs", performance_options)
  end

  @doc """
  运行集成测试（不包括性能测试）
  """
  def run_integration_tests(options \\ []) do
    IO.puts("🔗 运行集成测试套件...")

    integration_suites = @test_suites |> Enum.reject(fn {name, _} ->
      String.contains?(name, "性能")
    end)

    results = Enum.map(integration_suites, fn {name, path} ->
      run_test_suite(name, path, options)
    end)

    generate_integration_report(results)
    results
  end

  @doc """
  运行快速测试（基础功能验证）
  """
  def run_quick_tests(options \\ []) do
    IO.puts("⚡ 运行快速测试...")

    quick_options = Keyword.merge(options, [
      only: ["basic", "crud", "simple"],
      exclude: ["performance", "load", "stress"]
    ])

    # 只运行Repository和Service层的基础测试
    quick_suites = [
      {"Repository层基础测试", "test/racing_game/live/admin_panel/repositories/repository_integration_test.exs"},
      {"Service层基础测试", "test/racing_game/live/admin_panel/services/service_integration_test.exs"}
    ]

    results = Enum.map(quick_suites, fn {name, path} ->
      run_test_suite(name, path, quick_options)
    end)

    generate_quick_report(results)
    results
  end

  # ==================== 私有辅助函数 ====================

  defp format_options(options) do
    options
    |> Enum.flat_map(fn
      {:timeout, timeout} -> ["--timeout", to_string(timeout)]
      {:only, tags} when is_list(tags) -> ["--only"] ++ tags
      {:exclude, tags} when is_list(tags) -> ["--exclude"] ++ tags
      {:max_cases, max} -> ["--max-cases", to_string(max)]
      {:verbose, true} -> ["--verbose"]
      {:trace, true} -> ["--trace"]
      {key, value} -> ["--#{key}", to_string(value)]
    end)
  end

  defp print_suite_result(result) do
    status_icon = case result.status do
      :passed -> "✅"
      :failed -> "❌"
      :error -> "💥"
      :not_found -> "❓"
    end

    IO.puts("#{status_icon} #{result.suite_name}")
    IO.puts("   执行时间: #{result.execution_time}ms")
    IO.puts("   退出码: #{result.exit_code}")

    if result.status != :passed do
      IO.puts("   输出预览:")
      preview = result.output
      |> String.split("\n")
      |> Enum.take(5)
      |> Enum.join("\n")
      IO.puts("   #{preview}")
    end
  end

  defp generate_test_report(results, total_time) do
    IO.puts("\n" <> "=" |> String.duplicate(80))
    IO.puts("📊 Teen系统测试报告")
    IO.puts("=" |> String.duplicate(80))

    # 统计结果
    total_suites = length(results)
    passed_suites = results |> Enum.count(fn r -> r.status == :passed end)
    failed_suites = results |> Enum.count(fn r -> r.status == :failed end)
    error_suites = results |> Enum.count(fn r -> r.status == :error end)
    not_found_suites = results |> Enum.count(fn r -> r.status == :not_found end)

    success_rate = if total_suites > 0, do: passed_suites / total_suites * 100, else: 0

    IO.puts("📈 测试统计:")
    IO.puts("   总测试套件数: #{total_suites}")
    IO.puts("   通过: #{passed_suites} ✅")
    IO.puts("   失败: #{failed_suites} ❌")
    IO.puts("   错误: #{error_suites} 💥")
    IO.puts("   未找到: #{not_found_suites} ❓")
    IO.puts("   成功率: #{Float.round(success_rate, 2)}%")
    IO.puts("   总执行时间: #{total_time}ms (#{Float.round(total_time / 1000, 2)}s)")

    # 详细结果
    IO.puts("\n📋 详细结果:")
    Enum.each(results, fn result ->
      status_text = case result.status do
        :passed -> "通过"
        :failed -> "失败"
        :error -> "错误"
        :not_found -> "未找到"
      end

      IO.puts("   #{result.suite_name}: #{status_text} (#{result.execution_time}ms)")
    end)

    # 建议
    IO.puts("\n💡 建议:")
    cond do
      success_rate == 100 ->
        IO.puts("   🎉 所有测试都通过了！系统状态良好。")
      success_rate >= 80 ->
        IO.puts("   ✨ 大部分测试通过，系统基本稳定，建议修复失败的测试。")
      success_rate >= 60 ->
        IO.puts("   ⚠️  部分测试失败，建议优先修复关键功能测试。")
      true ->
        IO.puts("   🚨 大量测试失败，系统可能存在严重问题，需要立即修复。")
    end

    # 保存报告到文件
    save_report_to_file(results, total_time)
  end

  defp generate_integration_report(results) do
    IO.puts("\n🔗 集成测试报告")
    IO.puts("-" |> String.duplicate(50))

    passed_count = results |> Enum.count(fn r -> r.status == :passed end)
    total_count = length(results)

    IO.puts("集成测试通过率: #{passed_count}/#{total_count} (#{Float.round(passed_count/total_count*100, 2)}%)")

    if passed_count == total_count do
      IO.puts("🎉 所有集成测试通过！系统各模块协调良好。")
    else
      failed_suites = results |> Enum.filter(fn r -> r.status != :passed end)
      IO.puts("❌ 以下集成测试需要关注:")
      Enum.each(failed_suites, fn suite ->
        IO.puts("   - #{suite.suite_name}")
      end)
    end
  end

  defp generate_quick_report(results) do
    IO.puts("\n⚡ 快速测试报告")
    IO.puts("-" |> String.duplicate(40))

    passed_count = results |> Enum.count(fn r -> r.status == :passed end)
    total_count = length(results)
    total_time = results |> Enum.map(fn r -> r.execution_time end) |> Enum.sum()

    IO.puts("快速测试结果: #{passed_count}/#{total_count}")
    IO.puts("执行时间: #{total_time}ms")

    if passed_count == total_count do
      IO.puts("✅ 基础功能验证通过！")
    else
      IO.puts("❌ 基础功能存在问题，建议运行完整测试。")
    end
  end

  defp save_report_to_file(results, total_time) do
    timestamp = DateTime.utc_now() |> DateTime.to_string() |> String.replace(":", "-")
    filename = "test_report_#{timestamp}.json"
    filepath = Path.join(["test", "reports", filename])

    # 确保报告目录存在
    File.mkdir_p!(Path.dirname(filepath))

    report_data = %{
      timestamp: DateTime.utc_now(),
      total_time: total_time,
      summary: %{
        total_suites: length(results),
        passed: results |> Enum.count(fn r -> r.status == :passed end),
        failed: results |> Enum.count(fn r -> r.status == :failed end),
        error: results |> Enum.count(fn r -> r.status == :error end)
      },
      results: results
    }

    case Jason.encode(report_data, pretty: true) do
      {:ok, json} ->
        File.write!(filepath, json)
        IO.puts("\n💾 测试报告已保存: #{filepath}")
      {:error, reason} ->
        IO.puts("\n❌ 保存测试报告失败: #{inspect(reason)}")
    end
  end

  @doc """
  获取所有测试套件列表
  """
  def get_test_suites, do: @test_suites

  @doc """
  根据名称查找测试套件
  """
  def find_test_suite(suite_name) do
    @test_suites |> Enum.find(fn {name, _} ->
      String.downcase(name) |> String.contains?(String.downcase(suite_name))
    end)
  end
end

# 如果直接运行此脚本，执行所有测试
if __ENV__.file == :code.which(RacingGame.Live.AdminPanel.TestRunner) do
  case System.argv() do
    ["all"] -> RacingGame.Live.AdminPanel.TestRunner.run_all_tests()
    ["integration"] -> RacingGame.Live.AdminPanel.TestRunner.run_integration_tests()
    ["performance"] -> RacingGame.Live.AdminPanel.TestRunner.run_performance_tests()
    ["quick"] -> RacingGame.Live.AdminPanel.TestRunner.run_quick_tests()
    [] -> RacingGame.Live.AdminPanel.TestRunner.run_quick_tests()  # 默认运行快速测试
    [suite_name] ->
      case RacingGame.Live.AdminPanel.TestRunner.find_test_suite(suite_name) do
        {name, path} -> RacingGame.Live.AdminPanel.TestRunner.run_test_suite(name, path)
        nil -> IO.puts("❌ 未找到测试套件: #{suite_name}")
      end
  end
end
