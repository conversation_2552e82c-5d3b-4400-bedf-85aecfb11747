defmodule RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.IpWhitelistRepository do
  @moduledoc """
  🛡️ IP白名单仓储层

  负责IP白名单的数据访问操作，包括：
  - IP白名单的CRUD操作
  - IP地址验证和检查
  - IP范围管理
  - 白名单状态管理
  - IP访问统计
  """

  require Logger
  import Ash.Query

  alias Teen.SystemSettings.IpWhitelist

  # 缓存配置
  @cache_ttl 600_000  # 10分钟缓存（IP白名单变化较少）
  @cache_prefix "ip_whitelist_repo"

  # ==================== 基础 CRUD 操作 ====================

  @doc """
  创建IP白名单

  ## 参数
  - `ip_data` - IP白名单数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, ip_whitelist}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_ip_whitelist(ip_data, options \\ []) do
    Logger.info("🛡️ [IP白名单仓储] 创建IP白名单: #{inspect(ip_data[:ip_address])}")

    try do
      result = IpWhitelist
      |> Ash.Changeset.for_create(:create, ip_data)
      |> Ash.create()

      case result do
        {:ok, ip_whitelist} ->
          Logger.info("✅ [IP白名单仓储] IP白名单创建成功: #{ip_whitelist.ip_address || ip_whitelist.ip_range}")
          {:ok, ip_whitelist}
        {:error, error} ->
          Logger.error("❌ [IP白名单仓储] IP白名单创建失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [IP白名单仓储] 创建IP白名单异常: #{inspect(exception)}")
        {:error, :create_ip_whitelist_exception}
    end
  end

  @doc """
  根据ID获取IP白名单

  ## 参数
  - `ip_id` - IP白名单ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, ip_whitelist}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_ip_whitelist(ip_id, options \\ []) do
    Logger.info("🛡️ [IP白名单仓储] 获取IP白名单: #{ip_id}")

    try do
      result = IpWhitelist
      |> Ash.get(ip_id)

      case result do
        {:ok, ip_whitelist} when not is_nil(ip_whitelist) ->
          Logger.info("✅ [IP白名单仓储] IP白名单获取成功: #{ip_whitelist.ip_address || ip_whitelist.ip_range}")
          {:ok, ip_whitelist}
        {:ok, nil} ->
          Logger.warn("⚠️ [IP白名单仓储] IP白名单不存在: #{ip_id}")
          {:error, :ip_whitelist_not_found}
        {:error, error} ->
          Logger.error("❌ [IP白名单仓储] IP白名单获取失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [IP白名单仓储] 获取IP白名单异常: #{inspect(exception)}")
        {:error, :get_ip_whitelist_exception}
    end
  end

  @doc """
  更新IP白名单

  ## 参数
  - `ip_id` - IP白名单ID
  - `update_data` - 更新数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, ip_whitelist}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_ip_whitelist(ip_id, update_data, options \\ []) do
    Logger.info("🛡️ [IP白名单仓储] 更新IP白名单: #{ip_id}")

    try do
      with {:ok, ip_whitelist} <- get_ip_whitelist(ip_id, options) do
        result = ip_whitelist
        |> Ash.Changeset.for_update(:update, update_data)
        |> Ash.update()

        case result do
          {:ok, updated_ip} ->
            Logger.info("✅ [IP白名单仓储] IP白名单更新成功: #{updated_ip.ip_address || updated_ip.ip_range}")
            {:ok, updated_ip}
          {:error, error} ->
            Logger.error("❌ [IP白名单仓储] IP白名单更新失败: #{inspect(error)}")
            {:error, error}
        end
      end
    rescue
      exception ->
        Logger.error("💥 [IP白名单仓储] 更新IP白名单异常: #{inspect(exception)}")
        {:error, :update_ip_whitelist_exception}
    end
  end

  @doc """
  删除IP白名单

  ## 参数
  - `ip_id` - IP白名单ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, ip_whitelist}` - 成功
  - `{:error, reason}` - 失败
  """
  def delete_ip_whitelist(ip_id, options \\ []) do
    Logger.info("🛡️ [IP白名单仓储] 删除IP白名单: #{ip_id}")

    try do
      with {:ok, ip_whitelist} <- get_ip_whitelist(ip_id, options) do
        result = ip_whitelist |> Ash.destroy()

        case result do
          :ok ->
            Logger.info("✅ [IP白名单仓储] IP白名单删除成功: #{ip_whitelist.ip_address || ip_whitelist.ip_range}")
            {:ok, ip_whitelist}
          {:error, error} ->
            Logger.error("❌ [IP白名单仓储] IP白名单删除失败: #{inspect(error)}")
            {:error, error}
        end
      end
    rescue
      exception ->
        Logger.error("💥 [IP白名单仓储] 删除IP白名单异常: #{inspect(exception)}")
        {:error, :delete_ip_whitelist_exception}
    end
  end

  # ==================== 查询操作 ====================

  @doc """
  获取IP白名单列表

  ## 参数
  - `params` - 查询参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, {ip_whitelists, total_count}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_ip_whitelists(params \\ %{}, options \\ []) do
    Logger.info("🛡️ [IP白名单仓储] 获取IP白名单列表: #{inspect(params)}")

    try do
      query = IpWhitelist
      |> build_list_query(params)
      |> maybe_load_associations(options)

      # 获取总数
      count_result = query |> Ash.count()
      
      # 获取数据
      list_result = query |> Ash.read()

      case {count_result, list_result} do
        {{:ok, total_count}, {:ok, ip_whitelists}} ->
          Logger.info("✅ [IP白名单仓储] IP白名单列表获取成功: #{length(ip_whitelists)}条记录")
          {:ok, {ip_whitelists, total_count}}
        {{:error, error}, _} ->
          Logger.error("❌ [IP白名单仓储] 获取IP白名单总数失败: #{inspect(error)}")
          {:error, error}
        {_, {:error, error}} ->
          Logger.error("❌ [IP白名单仓储] 获取IP白名单列表失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [IP白名单仓储] 获取IP白名单列表异常: #{inspect(exception)}")
        {:error, :list_ip_whitelists_exception}
    end
  end

  @doc """
  根据状态获取IP白名单

  ## 参数
  - `status` - 状态
  - `options` - 选项参数

  ## 返回
  - `{:ok, ip_whitelists}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_ip_whitelists_by_status(status, options \\ []) do
    Logger.info("🛡️ [IP白名单仓储] 根据状态获取IP白名单: #{status}")

    try do
      result = IpWhitelist
      |> Ash.Query.for_read(:list_by_status, %{status: status})
      |> maybe_load_associations(options)
      |> Ash.read()

      case result do
        {:ok, ip_whitelists} ->
          Logger.info("✅ [IP白名单仓储] 状态IP白名单获取成功: #{length(ip_whitelists)}条记录")
          {:ok, ip_whitelists}
        {:error, error} ->
          Logger.error("❌ [IP白名单仓储] 状态IP白名单获取失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [IP白名单仓储] 根据状态获取IP白名单异常: #{inspect(exception)}")
        {:error, :list_by_status_exception}
    end
  end

  @doc """
  根据类型获取IP白名单

  ## 参数
  - `type` - 类型
  - `options` - 选项参数

  ## 返回
  - `{:ok, ip_whitelists}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_ip_whitelists_by_type(type, options \\ []) do
    Logger.info("🛡️ [IP白名单仓储] 根据类型获取IP白名单: #{type}")

    try do
      result = IpWhitelist
      |> Ash.Query.for_read(:list_by_type, %{type: type})
      |> maybe_load_associations(options)
      |> Ash.read()

      case result do
        {:ok, ip_whitelists} ->
          Logger.info("✅ [IP白名单仓储] 类型IP白名单获取成功: #{length(ip_whitelists)}条记录")
          {:ok, ip_whitelists}
        {:error, error} ->
          Logger.error("❌ [IP白名单仓储] 类型IP白名单获取失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [IP白名单仓储] 根据类型获取IP白名单异常: #{inspect(exception)}")
        {:error, :list_by_type_exception}
    end
  end

  # ==================== IP验证操作 ====================

  @doc """
  检查IP是否被允许

  ## 参数
  - `ip_address` - IP地址
  - `options` - 选项参数

  ## 返回
  - `{:ok, allowed}` - 成功，返回是否允许
  - `{:error, reason}` - 失败
  """
  def check_ip_allowed(ip_address, options \\ []) do
    Logger.info("🛡️ [IP白名单仓储] 检查IP是否被允许: #{ip_address}")

    try do
      result = IpWhitelist
      |> Ash.Query.for_read(:check_ip_allowed, %{ip_address: ip_address})
      |> Ash.read_one()

      case result do
        {:ok, ip_whitelist} when not is_nil(ip_whitelist) ->
          Logger.info("✅ [IP白名单仓储] IP地址被允许: #{ip_address}")
          {:ok, true}
        {:ok, nil} ->
          Logger.warn("⚠️ [IP白名单仓储] IP地址不在白名单中: #{ip_address}")
          {:ok, false}
        {:error, error} ->
          Logger.error("❌ [IP白名单仓储] IP检查失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [IP白名单仓储] 检查IP异常: #{inspect(exception)}")
        {:error, :check_ip_exception}
    end
  end

  # ==================== 状态管理操作 ====================

  @doc """
  激活IP白名单

  ## 参数
  - `ip_id` - IP白名单ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, ip_whitelist}` - 成功
  - `{:error, reason}` - 失败
  """
  def activate_ip_whitelist(ip_id, options \\ []) do
    Logger.info("🛡️ [IP白名单仓储] 激活IP白名单: #{ip_id}")

    try do
      with {:ok, ip_whitelist} <- get_ip_whitelist(ip_id, options) do
        result = ip_whitelist
        |> Ash.Changeset.for_update(:activate)
        |> Ash.update()

        case result do
          {:ok, updated_ip} ->
            Logger.info("✅ [IP白名单仓储] IP白名单激活成功: #{updated_ip.ip_address || updated_ip.ip_range}")
            {:ok, updated_ip}
          {:error, error} ->
            Logger.error("❌ [IP白名单仓储] IP白名单激活失败: #{inspect(error)}")
            {:error, error}
        end
      end
    rescue
      exception ->
        Logger.error("💥 [IP白名单仓储] 激活IP白名单异常: #{inspect(exception)}")
        {:error, :activate_ip_whitelist_exception}
    end
  end

  @doc """
  禁用IP白名单

  ## 参数
  - `ip_id` - IP白名单ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, ip_whitelist}` - 成功
  - `{:error, reason}` - 失败
  """
  def deactivate_ip_whitelist(ip_id, options \\ []) do
    Logger.info("🛡️ [IP白名单仓储] 禁用IP白名单: #{ip_id}")

    try do
      with {:ok, ip_whitelist} <- get_ip_whitelist(ip_id, options) do
        result = ip_whitelist
        |> Ash.Changeset.for_update(:deactivate)
        |> Ash.update()

        case result do
          {:ok, updated_ip} ->
            Logger.info("✅ [IP白名单仓储] IP白名单禁用成功: #{updated_ip.ip_address || updated_ip.ip_range}")
            {:ok, updated_ip}
          {:error, error} ->
            Logger.error("❌ [IP白名单仓储] IP白名单禁用失败: #{inspect(error)}")
            {:error, error}
        end
      end
    rescue
      exception ->
        Logger.error("💥 [IP白名单仓储] 禁用IP白名单异常: #{inspect(exception)}")
        {:error, :deactivate_ip_whitelist_exception}
    end
  end

  # ==================== 私有辅助函数 ====================

  # 构建列表查询
  defp build_list_query(query, params) do
    query
    |> maybe_filter_by_ip_address(params)
    |> maybe_filter_by_ip_range(params)
    |> maybe_filter_by_type(params)
    |> maybe_filter_by_status(params)
    |> maybe_filter_by_created_by(params)
    |> apply_pagination(params)
    |> apply_sorting(params)
  end

  # 按IP地址过滤
  defp maybe_filter_by_ip_address(query, %{"ip_address" => ip_address}) when is_binary(ip_address) and ip_address != "" do
    query |> Ash.Query.filter(contains(ip_address, ip_address))
  end
  defp maybe_filter_by_ip_address(query, _), do: query

  # 按IP范围过滤
  defp maybe_filter_by_ip_range(query, %{"ip_range" => ip_range}) when is_binary(ip_range) and ip_range != "" do
    query |> Ash.Query.filter(contains(ip_range, ip_range))
  end
  defp maybe_filter_by_ip_range(query, _), do: query

  # 按类型过滤
  defp maybe_filter_by_type(query, %{"type" => type}) when is_binary(type) and type != "" do
    query |> Ash.Query.filter(type == type)
  end
  defp maybe_filter_by_type(query, _), do: query

  # 按状态过滤
  defp maybe_filter_by_status(query, %{"status" => status}) when is_integer(status) do
    query |> Ash.Query.filter(status == status)
  end
  defp maybe_filter_by_status(query, _), do: query

  # 按创建者过滤
  defp maybe_filter_by_created_by(query, %{"created_by" => created_by}) when is_binary(created_by) and created_by != "" do
    query |> Ash.Query.filter(created_by == created_by)
  end
  defp maybe_filter_by_created_by(query, _), do: query

  # 应用分页
  defp apply_pagination(query, params) do
    page = Map.get(params, "page", 1) |> ensure_integer()
    page_size = Map.get(params, "page_size", 20) |> ensure_integer() |> min(100)
    offset = (page - 1) * page_size

    query
    |> Ash.Query.limit(page_size)
    |> Ash.Query.offset(offset)
  end

  # 应用排序
  defp apply_sorting(query, params) do
    sort_by = Map.get(params, "sort_by", "inserted_at")
    sort_order = Map.get(params, "sort_order", "desc")

    case {sort_by, sort_order} do
      {"inserted_at", "desc"} -> query |> Ash.Query.sort(inserted_at: :desc)
      {"inserted_at", "asc"} -> query |> Ash.Query.sort(inserted_at: :asc)
      {"ip_address", "asc"} -> query |> Ash.Query.sort(ip_address: :asc)
      {"ip_address", "desc"} -> query |> Ash.Query.sort(ip_address: :desc)
      {"type", "asc"} -> query |> Ash.Query.sort(type: :asc)
      {"type", "desc"} -> query |> Ash.Query.sort(type: :desc)
      _ -> query |> Ash.Query.sort(inserted_at: :desc)
    end
  end

  # 加载关联数据
  defp maybe_load_associations(query, options) do
    load_associations = Keyword.get(options, :load, [])
    if Enum.empty?(load_associations) do
      query
    else
      query |> Ash.Query.load(load_associations)
    end
  end

  # 确保整数类型
  defp ensure_integer(value) when is_integer(value), do: value
  defp ensure_integer(value) when is_binary(value) do
    case Integer.parse(value) do
      {int, _} -> int
      :error -> 1
    end
  end
  defp ensure_integer(_), do: 1
end
