defmodule RacingGame.Live.AdminPanel.Repositories.Teen.SystemSettings.AdminUserRepository do
  @moduledoc """
  👤 管理员用户仓储层

  负责管理员用户的数据访问操作，包括：
  - 管理员用户的CRUD操作
  - 用户认证和授权
  - 用户状态管理
  - 用户查询和过滤
  - 用户统计信息
  """

  require Logger
  import Ash.Query

  alias Teen.SystemSettings.AdminUser

  # 缓存配置
  @cache_ttl 300_000  # 5分钟缓存
  @cache_prefix "admin_user_repo"

  # ==================== 基础 CRUD 操作 ====================

  @doc """
  创建管理员用户

  ## 参数
  - `user_data` - 用户数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, admin_user}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_admin_user(user_data, options \\ []) do
    Logger.info("👤 [管理员用户仓储] 创建管理员用户: #{inspect(user_data[:username])}")

    try do
      result = AdminUser
      |> Ash.Changeset.for_create(:create, user_data)
      |> Ash.create()

      case result do
        {:ok, admin_user} ->
          Logger.info("✅ [管理员用户仓储] 管理员用户创建成功: #{admin_user.username}")
          {:ok, admin_user}
        {:error, error} ->
          Logger.error("❌ [管理员用户仓储] 管理员用户创建失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [管理员用户仓储] 创建管理员用户异常: #{inspect(exception)}")
        {:error, :create_admin_user_exception}
    end
  end

  @doc """
  根据ID获取管理员用户

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, admin_user}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_admin_user(user_id, options \\ []) do
    Logger.info("👤 [管理员用户仓储] 获取管理员用户: #{user_id}")

    try do
      result = AdminUser
      |> Ash.get(user_id)

      case result do
        {:ok, admin_user} when not is_nil(admin_user) ->
          Logger.info("✅ [管理员用户仓储] 管理员用户获取成功: #{admin_user.username}")
          {:ok, admin_user}
        {:ok, nil} ->
          Logger.warn("⚠️ [管理员用户仓储] 管理员用户不存在: #{user_id}")
          {:error, :admin_user_not_found}
        {:error, error} ->
          Logger.error("❌ [管理员用户仓储] 管理员用户获取失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [管理员用户仓储] 获取管理员用户异常: #{inspect(exception)}")
        {:error, :get_admin_user_exception}
    end
  end

  @doc """
  更新管理员用户

  ## 参数
  - `user_id` - 用户ID
  - `update_data` - 更新数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, admin_user}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_admin_user(user_id, update_data, options \\ []) do
    Logger.info("👤 [管理员用户仓储] 更新管理员用户: #{user_id}")

    try do
      with {:ok, admin_user} <- get_admin_user(user_id, options) do
        result = admin_user
        |> Ash.Changeset.for_update(:update, update_data)
        |> Ash.update()

        case result do
          {:ok, updated_user} ->
            Logger.info("✅ [管理员用户仓储] 管理员用户更新成功: #{updated_user.username}")
            {:ok, updated_user}
          {:error, error} ->
            Logger.error("❌ [管理员用户仓储] 管理员用户更新失败: #{inspect(error)}")
            {:error, error}
        end
      end
    rescue
      exception ->
        Logger.error("💥 [管理员用户仓储] 更新管理员用户异常: #{inspect(exception)}")
        {:error, :update_admin_user_exception}
    end
  end

  @doc """
  删除管理员用户

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, admin_user}` - 成功
  - `{:error, reason}` - 失败
  """
  def delete_admin_user(user_id, options \\ []) do
    Logger.info("👤 [管理员用户仓储] 删除管理员用户: #{user_id}")

    try do
      with {:ok, admin_user} <- get_admin_user(user_id, options) do
        result = admin_user |> Ash.destroy()

        case result do
          :ok ->
            Logger.info("✅ [管理员用户仓储] 管理员用户删除成功: #{admin_user.username}")
            {:ok, admin_user}
          {:error, error} ->
            Logger.error("❌ [管理员用户仓储] 管理员用户删除失败: #{inspect(error)}")
            {:error, error}
        end
      end
    rescue
      exception ->
        Logger.error("💥 [管理员用户仓储] 删除管理员用户异常: #{inspect(exception)}")
        {:error, :delete_admin_user_exception}
    end
  end

  # ==================== 查询操作 ====================

  @doc """
  获取管理员用户列表

  ## 参数
  - `params` - 查询参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, {admin_users, total_count}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_admin_users(params \\ %{}, options \\ []) do
    Logger.info("👤 [管理员用户仓储] 获取管理员用户列表: #{inspect(params)}")

    try do
      query = AdminUser
      |> build_list_query(params)
      |> maybe_load_associations(options)

      # 获取总数
      count_result = query |> Ash.count()
      
      # 获取数据
      list_result = query |> Ash.read()

      case {count_result, list_result} do
        {{:ok, total_count}, {:ok, admin_users}} ->
          Logger.info("✅ [管理员用户仓储] 管理员用户列表获取成功: #{length(admin_users)}条记录")
          {:ok, {admin_users, total_count}}
        {{:error, error}, _} ->
          Logger.error("❌ [管理员用户仓储] 获取用户总数失败: #{inspect(error)}")
          {:error, error}
        {_, {:error, error}} ->
          Logger.error("❌ [管理员用户仓储] 获取用户列表失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [管理员用户仓储] 获取管理员用户列表异常: #{inspect(exception)}")
        {:error, :list_admin_users_exception}
    end
  end

  @doc """
  根据角色获取管理员用户

  ## 参数
  - `role_id` - 角色ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, admin_users}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_admin_users_by_role(role_id, options \\ []) do
    Logger.info("👤 [管理员用户仓储] 根据角色获取管理员用户: #{role_id}")

    try do
      result = AdminUser
      |> Ash.Query.for_read(:list_by_role, %{role_id: role_id})
      |> maybe_load_associations(options)
      |> Ash.read()

      case result do
        {:ok, admin_users} ->
          Logger.info("✅ [管理员用户仓储] 角色用户获取成功: #{length(admin_users)}条记录")
          {:ok, admin_users}
        {:error, error} ->
          Logger.error("❌ [管理员用户仓储] 角色用户获取失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [管理员用户仓储] 根据角色获取用户异常: #{inspect(exception)}")
        {:error, :list_by_role_exception}
    end
  end

  @doc """
  根据状态获取管理员用户

  ## 参数
  - `status` - 状态
  - `options` - 选项参数

  ## 返回
  - `{:ok, admin_users}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_admin_users_by_status(status, options \\ []) do
    Logger.info("👤 [管理员用户仓储] 根据状态获取管理员用户: #{status}")

    try do
      result = AdminUser
      |> Ash.Query.for_read(:list_by_status, %{status: status})
      |> maybe_load_associations(options)
      |> Ash.read()

      case result do
        {:ok, admin_users} ->
          Logger.info("✅ [管理员用户仓储] 状态用户获取成功: #{length(admin_users)}条记录")
          {:ok, admin_users}
        {:error, error} ->
          Logger.error("❌ [管理员用户仓储] 状态用户获取失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [管理员用户仓储] 根据状态获取用户异常: #{inspect(exception)}")
        {:error, :list_by_status_exception}
    end
  end

  # ==================== 认证操作 ====================

  @doc """
  用户认证

  ## 参数
  - `username` - 用户名
  - `password` - 密码
  - `options` - 选项参数

  ## 返回
  - `{:ok, admin_user}` - 成功
  - `{:error, reason}` - 失败
  """
  def authenticate_admin_user(username, password, options \\ []) do
    Logger.info("🔐 [管理员用户仓储] 用户认证: #{username}")

    try do
      result = AdminUser
      |> Ash.Query.for_read(:authenticate, %{username: username, password: password})
      |> maybe_load_associations(options)
      |> Ash.read_one()

      case result do
        {:ok, admin_user} when not is_nil(admin_user) ->
          Logger.info("✅ [管理员用户仓储] 用户认证成功: #{admin_user.username}")
          {:ok, admin_user}
        {:ok, nil} ->
          Logger.warn("⚠️ [管理员用户仓储] 用户认证失败: 用户名或密码错误")
          {:error, :invalid_credentials}
        {:error, error} ->
          Logger.error("❌ [管理员用户仓储] 用户认证失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [管理员用户仓储] 用户认证异常: #{inspect(exception)}")
        {:error, :authenticate_exception}
    end
  end

  # ==================== 私有辅助函数 ====================

  # 构建列表查询
  defp build_list_query(query, params) do
    query
    |> maybe_filter_by_username(params)
    |> maybe_filter_by_email(params)
    |> maybe_filter_by_role(params)
    |> maybe_filter_by_status(params)
    |> maybe_filter_by_permission_level(params)
    |> apply_pagination(params)
    |> apply_sorting(params)
  end

  # 按用户名过滤
  defp maybe_filter_by_username(query, %{"username" => username}) when is_binary(username) and username != "" do
    query |> Ash.Query.filter(contains(username, username))
  end
  defp maybe_filter_by_username(query, _), do: query

  # 按邮箱过滤
  defp maybe_filter_by_email(query, %{"email" => email}) when is_binary(email) and email != "" do
    query |> Ash.Query.filter(contains(email, email))
  end
  defp maybe_filter_by_email(query, _), do: query

  # 按角色过滤
  defp maybe_filter_by_role(query, %{"role_id" => role_id}) when is_binary(role_id) and role_id != "" do
    query |> Ash.Query.filter(role_id == role_id)
  end
  defp maybe_filter_by_role(query, _), do: query

  # 按状态过滤
  defp maybe_filter_by_status(query, %{"status" => status}) when is_integer(status) do
    query |> Ash.Query.filter(status == status)
  end
  defp maybe_filter_by_status(query, _), do: query

  # 按权限级别过滤
  defp maybe_filter_by_permission_level(query, %{"permission_level" => level}) when is_integer(level) do
    query |> Ash.Query.filter(permission_level == level)
  end
  defp maybe_filter_by_permission_level(query, _), do: query

  # 应用分页
  defp apply_pagination(query, params) do
    page = Map.get(params, "page", 1) |> ensure_integer()
    page_size = Map.get(params, "page_size", 20) |> ensure_integer() |> min(100)
    offset = (page - 1) * page_size

    query
    |> Ash.Query.limit(page_size)
    |> Ash.Query.offset(offset)
  end

  # 应用排序
  defp apply_sorting(query, params) do
    sort_by = Map.get(params, "sort_by", "inserted_at")
    sort_order = Map.get(params, "sort_order", "desc")

    case {sort_by, sort_order} do
      {"inserted_at", "desc"} -> query |> Ash.Query.sort(inserted_at: :desc)
      {"inserted_at", "asc"} -> query |> Ash.Query.sort(inserted_at: :asc)
      {"username", "asc"} -> query |> Ash.Query.sort(username: :asc)
      {"username", "desc"} -> query |> Ash.Query.sort(username: :desc)
      {"last_login_at", "desc"} -> query |> Ash.Query.sort(last_login_at: :desc)
      {"last_login_at", "asc"} -> query |> Ash.Query.sort(last_login_at: :asc)
      _ -> query |> Ash.Query.sort(inserted_at: :desc)
    end
  end

  # 加载关联数据
  defp maybe_load_associations(query, options) do
    load_associations = Keyword.get(options, :load, [])
    if Enum.empty?(load_associations) do
      query
    else
      query |> Ash.Query.load(load_associations)
    end
  end

  # 确保整数类型
  defp ensure_integer(value) when is_integer(value), do: value
  defp ensure_integer(value) when is_binary(value) do
    case Integer.parse(value) do
      {int, _} -> int
      :error -> 1
    end
  end
  defp ensure_integer(_), do: 1
end
