#!/usr/bin/env elixir

defmodule ImportFixer do
  @moduledoc """
  修复Repository文件中缺少的 import Ash.Query 导入
  """

  def run do
    IO.puts("🔧 开始修复Repository文件中的导入问题...")

    files = find_repository_files()
    IO.puts("📁 找到 #{length(files)} 个Repository文件")

    results = Enum.map(files, &fix_file/1)

    fixed_count = Enum.count(results, & &1 == :fixed)
    skipped_count = Enum.count(results, & &1 == :skipped)
    error_count = Enum.count(results, & &1 == :error)

    IO.puts("\n" <> String.duplicate("=", 50))
    IO.puts("📊 修复结果统计:")
    IO.puts("   总文件数: #{length(files)}")
    IO.puts("   已修复: #{fixed_count} ✅")
    IO.puts("   跳过: #{skipped_count} ⏭️")
    IO.puts("   错误: #{error_count} ❌")
    IO.puts("\n🎉 所有文件修复完成！")
  end

  defp find_repository_files do
    # 查找所有Repository文件
    Path.wildcard("lib/racing_game/live/admin_panel/repositories/**/*_repository.ex")
    |> Enum.filter(&File.exists?/1)
    |> Enum.uniq()
  end

  defp fix_file(file_path) do
    filename = Path.basename(file_path)
    IO.write("修复 #{filename}... ")

    try do
      content = File.read!(file_path)

      # 检查是否已经有 import Ash.Query
      if String.contains?(content, "import Ash.Query") do
        IO.puts("⏭️ 跳过")
        :skipped
      else
        # 检查是否有 alias Ash.Query
        if String.contains?(content, "alias Ash.Query") do
          # 在 alias Ash.Query 后添加 import Ash.Query
          new_content = String.replace(content,
            ~r/(alias Ash\.Query)(\n)/,
            "\\1\\2  import Ash.Query\\2"
          )

          if new_content != content do
            File.write!(file_path, new_content)
            IO.puts("✅ 已修复")
            :fixed
          else
            IO.puts("⏭️ 跳过")
            :skipped
          end
        else
          # 检查是否使用了 Ash.Query.filter，如果是则需要添加导入
          if String.contains?(content, "Ash.Query.filter") do
            # 找到 require Logger 行，在其后添加导入
            new_content = String.replace(content,
              ~r/(require Logger\n)/,
              "\\1  alias Ash.Query\n  import Ash.Query\n"
            )

            if new_content != content do
              File.write!(file_path, new_content)
              IO.puts("✅ 已修复")
              :fixed
            else
              IO.puts("⏭️ 跳过")
              :skipped
            end
          else
            IO.puts("⏭️ 跳过")
            :skipped
          end
        end
      end
    rescue
      error ->
        IO.puts("❌ 错误: #{inspect(error)}")
        :error
    end
  end
end

ImportFixer.run()
