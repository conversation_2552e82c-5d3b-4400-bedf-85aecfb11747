defmodule RacingGame.Live.AdminPanel.Repositories.BetRepository do
  @moduledoc """
  下注记录数据访问仓储
  
  提供下注记录相关的数据访问抽象：
  - 下注记录的查询和过滤
  - 用户下注历史查询
  - 下注统计和分析
  - 数据缓存管理
  """

  require Logger
  alias RacingGame.Bet
  alias RacingGame.Live.AdminPanel.Repositories.QueryBuilders.BetQueryBuilder
  alias Cypridina.Accounts.User
  require Ash.Query

  # 常量定义
  @cache_ttl 180  # 3分钟缓存
  @default_limit 20
  @max_limit 100
  @bet_statuses %{0 => :pending, 1 => :won, 2 => :lost}

  # ============================================================================
  # 基础查询方法
  # ============================================================================

  @doc """
  根据ID获取下注记录

  ## 参数
  - `bet_id` - 下注ID
  - `options` - 选项

  ## 返回
  - `{:ok, bet}` - 成功
  - `{:error, :not_found}` - 下注记录不存在
  - `{:error, reason}` - 其他错误
  """
  def get_by_id(bet_id, options \\ []) do
    Logger.debug("🔍 [下注仓储] 根据ID获取下注记录: #{bet_id}")
    
    preload = Keyword.get(options, :preload, [:user])
    use_cache = Keyword.get(options, :use_cache, true)
    
    cache_key = build_cache_key("bet", bet_id, preload)
    
    if use_cache do
      case get_from_cache(cache_key) do
        {:ok, bet} -> {:ok, bet}
        :miss -> fetch_and_cache_bet_by_id(bet_id, preload, cache_key)
      end
    else
      fetch_bet_by_id(bet_id, preload)
    end
  end

  @doc """
  分页查询下注记录

  ## 参数
  - `params` - 查询参数
    - `:page` - 页码
    - `:limit` - 每页数量
    - `:user_id` - 用户ID过滤
    - `:race_issue` - 比赛期号过滤
    - `:status` - 状态过滤
    - `:date_from` - 开始日期
    - `:date_to` - 结束日期
    - `:sort` - 排序条件

  ## 返回
  - `{:ok, %{bets: bets, page_info: page_info}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_paginated(params \\ %{}) do
    Logger.debug("📋 [下注仓储] 分页查询下注记录")
    
    try do
      normalized_params = normalize_list_params(params)
      
      with {:ok, query} <- BetQueryBuilder.build_list_query(normalized_params),
           {:ok, results} <- execute_paginated_query(query, normalized_params) do
        {:ok, results}
      else
        {:error, reason} -> {:error, reason}
      end
    rescue
      error ->
        Logger.error("❌ [下注仓储] 分页查询异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  获取用户下注记录

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项
    - `:limit` - 数量限制
    - `:status` - 状态过滤
    - `:race_issue` - 比赛期号过滤

  ## 返回
  - `{:ok, bets}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_bets(user_id, options \\ []) do
    Logger.debug("👤 [下注仓储] 获取用户下注记录: #{user_id}")
    
    try do
      query = Bet
      |> Ash.Query.filter(user_id == ^user_id)
      |> apply_status_filter(options[:status])
      |> apply_race_issue_filter(options[:race_issue])
      |> Ash.Query.sort(inserted_at: :desc)
      |> maybe_limit(options[:limit])
      
      case Ash.read(query) do
        {:ok, bets} -> {:ok, bets}
        {:error, error} ->
          Logger.error("❌ [下注仓储] 获取用户记录失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [下注仓储] 获取用户记录异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  获取比赛期号的下注记录

  ## 参数
  - `race_issue` - 比赛期号
  - `options` - 选项

  ## 返回
  - `{:ok, bets}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_race_bets(race_issue, options \\ []) do
    Logger.debug("🏁 [下注仓储] 获取比赛下注记录: #{race_issue}")
    
    try do
      query = Bet
      |> Ash.Query.filter(race_issue == ^race_issue)
      |> Ash.Query.load([:user])
      |> apply_status_filter(options[:status])
      |> Ash.Query.sort(inserted_at: :desc)
      |> maybe_limit(options[:limit])
      
      case Ash.read(query) do
        {:ok, bets} -> {:ok, bets}
        {:error, error} ->
          Logger.error("❌ [下注仓储] 获取比赛记录失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [下注仓储] 获取比赛记录异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # ============================================================================
  # 统计查询
  # ============================================================================

  @doc """
  获取下注统计信息

  ## 参数
  - `params` - 查询参数
    - `:user_id` - 用户ID（可选）
    - `:date_from` - 开始日期
    - `:date_to` - 结束日期

  ## 返回
  - `{:ok, statistics}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_statistics(params \\ %{}) do
    Logger.debug("📊 [下注仓储] 获取下注统计")
    
    try do
      base_query = build_statistics_base_query(params)
      
      stats = %{
        total_bets: get_total_bets(base_query),
        total_amount: get_total_amount(base_query),
        total_payout: get_total_payout(base_query),
        win_rate: get_win_rate(base_query),
        status_distribution: get_status_distribution(base_query),
        popular_selections: get_popular_selections(base_query),
        daily_stats: get_daily_stats(base_query, params)
      }
      
      {:ok, stats}
    rescue
      error ->
        Logger.error("❌ [下注仓储] 获取统计异常: #{inspect(error)}")
        {:error, :statistics_error}
    end
  end

  @doc """
  获取用户下注汇总

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - `{:ok, summary}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_summary(user_id, options \\ []) do
    Logger.debug("📊 [下注仓储] 获取用户下注汇总: #{user_id}")
    
    try do
      query = Bet |> Ash.Query.filter(user_id == ^user_id)
      
      summary = %{
        total_bets: get_total_bets(query),
        total_amount: get_total_amount(query),
        total_payout: get_total_payout(query),
        win_count: get_win_count(query),
        loss_count: get_loss_count(query),
        pending_count: get_pending_count(query),
        win_rate: get_win_rate(query),
        profit_loss: get_profit_loss(query),
        favorite_selections: get_user_favorite_selections(user_id),
        recent_activity: get_user_recent_activity(user_id, options[:days] || 7)
      }
      
      {:ok, summary}
    rescue
      error ->
        Logger.error("❌ [下注仓储] 获取用户汇总异常: #{inspect(error)}")
        {:error, :summary_error}
    end
  end

  # ============================================================================
  # 缓存管理
  # ============================================================================

  @doc """
  清除下注相关缓存

  ## 参数
  - `bet_id` - 下注ID（可选）
  - `user_id` - 用户ID（可选）
  """
  def clear_cache(bet_id \\ nil, user_id \\ nil) do
    cache_patterns = []
    
    if bet_id do
      cache_patterns = ["bet:#{bet_id}:*" | cache_patterns]
    end
    
    if user_id do
      cache_patterns = ["user_bets:#{user_id}:*" | cache_patterns]
    end
    
    if Enum.empty?(cache_patterns) do
      cache_patterns = ["bet:*", "user_bets:*"]
    end
    
    Logger.debug("🧹 [下注仓储] 清除下注缓存: #{inspect(cache_patterns)}")
    Enum.each(cache_patterns, &clear_cache_pattern/1)
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 标准化列表参数
  defp normalize_list_params(params) do
    %{
      page: max(1, params[:page] || 1),
      limit: min(params[:limit] || @default_limit, @max_limit),
      user_id: params[:user_id],
      race_issue: params[:race_issue],
      status: params[:status],
      date_from: params[:date_from],
      date_to: params[:date_to],
      sort: params[:sort] || [inserted_at: :desc],
      preload: params[:preload] || [:user]
    }
  end

  # 获取并缓存下注记录
  defp fetch_and_cache_bet_by_id(bet_id, preload, cache_key) do
    case fetch_bet_by_id(bet_id, preload) do
      {:ok, bet} ->
        cache_put(cache_key, bet, @cache_ttl)
        {:ok, bet}
      error -> error
    end
  end

  # 获取下注记录
  defp fetch_bet_by_id(bet_id, preload) do
    try do
      case Bet
           |> maybe_preload(preload)
           |> Ash.read(bet_id) do
        {:ok, bet} -> {:ok, bet}
        {:error, %Ash.Error.Query.NotFound{}} -> {:error, :not_found}
        {:error, error} ->
          Logger.error("❌ [下注仓储] 获取下注失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [下注仓储] 获取下注异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # 执行分页查询
  defp execute_paginated_query(query, params) do
    offset = (params.page - 1) * params.limit
    
    case query
         |> maybe_preload(params.preload)
         |> Ash.Query.page(count: true, limit: params.limit, offset: offset)
         |> Ash.read() do
      {:ok, %Ash.Page.Offset{results: bets, count: total_count}} ->
        page_info = %{
          total_count: total_count,
          page: params.page,
          limit: params.limit,
          total_pages: ceil(total_count / params.limit)
        }
        {:ok, %{bets: bets, page_info: page_info}}
      {:error, error} ->
        Logger.error("❌ [下注仓储] 分页查询失败: #{inspect(error)}")
        {:error, :query_failed}
    end
  end

  # 应用状态过滤
  defp apply_status_filter(query, nil), do: query
  defp apply_status_filter(query, status) when is_integer(status) do
    Ash.Query.filter(query, status == ^status)
  end
  defp apply_status_filter(query, status) when is_atom(status) do
    status_code = Enum.find_value(@bet_statuses, fn {code, stat} -> 
      if stat == status, do: code 
    end)
    if status_code, do: Ash.Query.filter(query, status == ^status_code), else: query
  end

  # 应用比赛期号过滤
  defp apply_race_issue_filter(query, nil), do: query
  defp apply_race_issue_filter(query, race_issue) do
    Ash.Query.filter(query, race_issue == ^race_issue)
  end

  # 可能限制数量
  defp maybe_limit(query, nil), do: query
  defp maybe_limit(query, limit) when is_integer(limit) and limit > 0 do
    Ash.Query.limit(query, limit)
  end
  defp maybe_limit(query, _), do: query

  # 可能预加载关联数据
  defp maybe_preload(query, []), do: query
  defp maybe_preload(query, preload) when is_list(preload) do
    Ash.Query.load(query, preload)
  end

  # 构建统计基础查询
  defp build_statistics_base_query(params) do
    query = Bet
    
    query = if params[:user_id] do
      Ash.Query.filter(query, user_id == ^params[:user_id])
    else
      query
    end
    
    query = if params[:date_from] do
      Ash.Query.filter(query, inserted_at >= ^params[:date_from])
    else
      query
    end
    
    if params[:date_to] do
      Ash.Query.filter(query, inserted_at <= ^params[:date_to])
    else
      query
    end
  end

  # 统计函数（简化实现，实际需要使用聚合查询）
  defp get_total_bets(query) do
    case query |> Ash.Query.aggregate(:count, :id) |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_total_amount(query) do
    case query |> Ash.Query.aggregate(:sum, :amount) |> Ash.read() do
      {:ok, [%{sum: sum}]} when is_number(sum) -> sum
      _ -> 0
    end
  end

  defp get_total_payout(query) do
    case query |> Ash.Query.aggregate(:sum, :payout) |> Ash.read() do
      {:ok, [%{sum: sum}]} when is_number(sum) -> sum
      _ -> 0
    end
  end

  defp get_win_rate(query) do
    total = get_total_bets(query)
    if total > 0 do
      win_count = get_win_count(query)
      Float.round(win_count / total * 100, 2)
    else
      0.0
    end
  end

  defp get_win_count(query) do
    case query |> Ash.Query.filter(status == 1) |> Ash.Query.aggregate(:count, :id) |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_loss_count(query) do
    case query |> Ash.Query.filter(status == 2) |> Ash.Query.aggregate(:count, :id) |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_pending_count(query) do
    case query |> Ash.Query.filter(status == 0) |> Ash.Query.aggregate(:count, :id) |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_profit_loss(query) do
    total_payout = get_total_payout(query)
    total_amount = get_total_amount(query)
    total_payout - total_amount
  end

  # 其他统计函数的简化实现
  defp get_status_distribution(_query), do: %{}
  defp get_popular_selections(_query), do: []
  defp get_daily_stats(_query, _params), do: []
  defp get_user_favorite_selections(_user_id), do: []
  defp get_user_recent_activity(_user_id, _days), do: []

  # 构建缓存键
  defp build_cache_key(prefix, id, extra \\ nil) do
    case extra do
      nil -> "#{prefix}:#{id}"
      extra -> "#{prefix}:#{id}:#{:erlang.phash2(extra)}"
    end
  end

  # 缓存操作（需要实现具体的缓存后端）
  defp get_from_cache(_key), do: :miss
  defp cache_put(_key, _value, _ttl), do: :ok
  defp clear_cache_pattern(_pattern), do: :ok
end
