/**
 * Racing Game Admin Panel - 反馈系统样式
 * 
 * 通知、加载状态、错误提示等反馈组件的样式
 */

/* ===== 通知容器 ===== */
.notification-container {
  position: fixed;
  z-index: 10000;
  pointer-events: none;
  max-width: 400px;
  width: 100%;
}

.notification-container.position-top-right {
  top: 1rem;
  right: 1rem;
}

.notification-container.position-top-left {
  top: 1rem;
  left: 1rem;
}

.notification-container.position-bottom-right {
  bottom: 1rem;
  right: 1rem;
}

.notification-container.position-bottom-left {
  bottom: 1rem;
  left: 1rem;
}

.notification-container.position-top-center {
  top: 1rem;
  left: 50%;
  transform: translateX(-50%);
}

.notification-container.position-bottom-center {
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
}

/* ===== 通知样式 ===== */
.notification {
  position: relative;
  margin-bottom: 0.75rem;
  padding: 1rem;
  border-radius: 0.75rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  pointer-events: auto;
  transform: translateX(100%);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.notification.notification-show {
  transform: translateX(0);
  opacity: 1;
}

.notification.notification-hide {
  transform: translateX(100%);
  opacity: 0;
  margin-bottom: 0;
  padding-top: 0;
  padding-bottom: 0;
  max-height: 0;
}

/* ===== 通知类型样式 ===== */
.notification-success {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.9), rgba(22, 163, 74, 0.9));
  color: white;
}

.notification-error {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.9), rgba(220, 38, 38, 0.9));
  color: white;
}

.notification-warning {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.9), rgba(217, 119, 6, 0.9));
  color: white;
}

.notification-info {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.9), rgba(37, 99, 235, 0.9));
  color: white;
}

/* ===== 通知内容 ===== */
.notification-content {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.notification-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.notification-body {
  flex: 1;
  min-width: 0;
}

.notification-message {
  font-weight: 500;
  line-height: 1.5;
  margin-bottom: 0.5rem;
}

.notification-close {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  width: 1.5rem;
  height: 1.5rem;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  color: currentColor;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  line-height: 1;
  transition: background-color 0.2s ease;
}

.notification-close:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* ===== 通知操作按钮 ===== */
.notification-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.75rem;
}

.notification-action {
  padding: 0.375rem 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: currentColor;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.notification-action:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

/* ===== 通知进度条 ===== */
.notification-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.notification-progress .progress-bar {
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  width: 100%;
  transform: translateX(-100%);
  animation: notification-progress linear forwards;
}

@keyframes notification-progress {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

/* ===== 加载覆盖层 ===== */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.loading-overlay.overlay-mode {
  position: absolute;
}

.loading-overlay.inline-mode {
  position: relative;
  min-height: 100px;
}

.loading-overlay.loader-show {
  opacity: 1;
  visibility: visible;
}

.loading-overlay.loader-hide {
  opacity: 0;
  visibility: hidden;
}

.loading-content {
  text-align: center;
  color: #374151;
}

.loading-text {
  margin-top: 0.75rem;
  font-weight: 500;
  color: #6b7280;
}

/* ===== 全局加载指示器 ===== */
.global-loader {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.global-loader.show {
  opacity: 1;
  visibility: visible;
}

.global-loader.hidden {
  display: none;
}

.global-loader .loader-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.global-loader .loader-content {
  position: relative;
  z-index: 1;
  text-align: center;
  background: white;
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);
  min-width: 200px;
}

.global-loader .loader-text {
  margin-top: 1rem;
  font-weight: 600;
  color: #374151;
}

.global-loader .loader-progress {
  margin-top: 1rem;
  height: 4px;
  background: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
  display: none;
}

.global-loader .progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* ===== 加载动画组件 ===== */
.loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid rgba(59, 130, 246, 0.2);
  border-top-color: #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-dots {
  display: inline-flex;
  gap: 0.25rem;
}

.loading-dots span {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background: #3b82f6;
  animation: loading-dots 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }
.loading-dots span:nth-child(3) { animation-delay: 0s; }

.loading-wave {
  display: inline-flex;
  gap: 0.125rem;
}

.loading-wave span {
  width: 0.25rem;
  height: 1.5rem;
  background: #3b82f6;
  border-radius: 0.125rem;
  animation: loading-wave 1.2s ease-in-out infinite;
}

.loading-wave span:nth-child(1) { animation-delay: -1.1s; }
.loading-wave span:nth-child(2) { animation-delay: -1.0s; }
.loading-wave span:nth-child(3) { animation-delay: -0.9s; }
.loading-wave span:nth-child(4) { animation-delay: -0.8s; }
.loading-wave span:nth-child(5) { animation-delay: -0.7s; }

.loading-pulse {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background: #3b82f6;
  animation: loading-pulse 1.5s ease-in-out infinite;
}

.loading-bars {
  display: inline-flex;
  gap: 0.125rem;
  align-items: end;
}

.loading-bars span {
  width: 0.25rem;
  height: 1rem;
  background: #3b82f6;
  border-radius: 0.125rem;
  animation: loading-bars 1s ease-in-out infinite;
}

.loading-bars span:nth-child(1) { animation-delay: -0.4s; }
.loading-bars span:nth-child(2) { animation-delay: -0.3s; }
.loading-bars span:nth-child(3) { animation-delay: -0.2s; }
.loading-bars span:nth-child(4) { animation-delay: -0.1s; }

/* ===== 加载动画关键帧 ===== */
@keyframes loading-dots {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes loading-wave {
  0%, 60%, 100% {
    transform: initial;
  }
  30% {
    transform: translateY(-0.5rem);
  }
}

@keyframes loading-pulse {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

@keyframes loading-bars {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}

/* ===== 响应式设计 ===== */
@media (max-width: 640px) {
  .notification-container {
    max-width: calc(100vw - 2rem);
    left: 1rem !important;
    right: 1rem !important;
    transform: none !important;
  }

  .notification {
    margin-bottom: 0.5rem;
    padding: 0.75rem;
  }

  .notification-content {
    gap: 0.5rem;
  }

  .notification-actions {
    flex-direction: column;
    gap: 0.375rem;
  }

  .global-loader .loader-content {
    margin: 1rem;
    padding: 1.5rem;
    min-width: auto;
    width: calc(100vw - 2rem);
    max-width: 300px;
  }
}

/* ===== 暗色主题适配 ===== */
@media (prefers-color-scheme: dark) {
  .loading-overlay {
    background: rgba(0, 0, 0, 0.8);
  }

  .loading-content,
  .loading-text {
    color: #e5e7eb;
  }

  .global-loader .loader-content {
    background: #1f2937;
    color: #e5e7eb;
  }

  .global-loader .loader-progress {
    background: #374151;
  }
}
