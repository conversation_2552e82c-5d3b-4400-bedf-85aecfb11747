defmodule <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.Slot777.Slot777Room do
  @moduledoc """
  Slot777游戏房间实现
  """

  use <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.RoomBase, game_type: :slot777
  require Logger

  alias <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.RoomManager
  alias <PERSON><PERSON><PERSON>ina.Teen.GameSystem.Games.Slot777.Slot777GameLogic
  alias <PERSON><PERSON><PERSON>ina.Teen.GameSystem.Games.Slot777.Slot777Jackpot
  alias Cypridina.Teen.GameSystem.Games.Slot777.Slot777FreeGameTest



  # 本地 Jackpot 管理函数
  defp get_jackpot_amount(state) do
    state.game_data.jackpot_state.current_amount
  end

  # 处理本地 Jackpot 逻辑
  defp handle_local_jackpot(state, bet_amount, total_win, game_result) do
    # 计算贡献到奖池的金额（通常是下注金额的一定比例）
    contribution_rate = 0.01  # 1% 贡献率
    contribution = trunc(bet_amount * contribution_rate)

    # 更新奖池状态
    updated_jackpot_state = Slot777Jackpot.contribute_to_jackpot(state.game_data.jackpot_state, contribution)

    # 检查是否中奖 Jackpot
    seven_count = Map.get(game_result, "sevennum", 0)
    {jackpot_amount, final_jackpot_state} = if seven_count >= 3 do
      # 中奖 Jackpot，计算奖金并更新状态
      jackpot_win = Slot777Jackpot.calculate_jackpot_amount(updated_jackpot_state.current_amount, seven_count)
      new_jackpot_state = Slot777Jackpot.win_jackpot(updated_jackpot_state, jackpot_win, "玩家", seven_count)
      {jackpot_win, new_jackpot_state}
    else
      {0, updated_jackpot_state}
    end

    # 更新游戏状态
    updated_game_data = %{state.game_data | jackpot_state: final_jackpot_state}
    updated_state = %{state | game_data: updated_game_data}

    {jackpot_amount, updated_state}
  end









  # 初始化游戏逻辑
  def init_game_logic(state) do
    Logger.info("🎰 [SLOT777] 初始化游戏房间: #{state.id}")

    # 通过 GameFactory 确保全局管理器已启动，然后注册房间
    # case GameFactory.get_global_manager(:slot777) do
    #   {:ok, _pid} ->
    #     # 全局管理器已存在，直接注册
    #     register_to_global_manager(state)

    #   {:error, :not_found} ->
    #     # 全局管理器不存在，尝试启动
    #     case GameFactory.start_global_manager(:slot777) do
    #       {:ok, _pid} ->
    #         Logger.info("🎰 [SLOT777] 全局管理器启动成功，注册房间")
    #         register_to_global_manager(state)

    #       {:error, reason} ->
    #         Logger.error("❌ [SLOT777] 全局管理器启动失败: #{inspect(reason)}")
    #         init_with_default_config(state)
    #     end
    # end
    # 使用本地配置初始化
    init_with_local_config(state)
  end

  # 使用本地配置初始化
  defp init_with_local_config(state) do
    Logger.info("✅ [SLOT777] 使用本地配置初始化房间")

    game_config = %{
      # 最小下注
      min_bet: Map.get(state.config, :min_bet, 10),
      # 最大下注
      max_bet: Map.get(state.config, :max_bet, 10000),
      # 赔率配置 - 使用从1开始的下标
      odds_config: Map.get(state.config, :odds_config, %{
        "1" => 1,
        "2" => 2,
        "3" => 5,
        "4" => 10,
        "5" => 20,
        "6" => 50,
        "7" => 100
      }),
      # 底分
      difen: 100,
      # 固定倍率 (对应前端的 BET_RATE_NUM)
      bet_rate_num: 9,
      # 金币比例 (对应前端的 SCORE_RATE)
      score_rate: 1
    }

    # 初始化本地 Jackpot 状态
    jackpot_state = Slot777Jackpot.init_state([])

    game_data = %{
      # 游戏配置
      config: game_config,
      # 当前状态
      status: :waiting,
      # 玩家下注记录
      bets: %{},
      # 当前玩家下注倍率
      current_odds: %{},
      # 游戏结果
      results: [],
      # 当前回合
      current_round: 0,
      # 免费游戏状态
      free_games: %{},
      # 待发送的免费游戏
      pending_free_games: %{},
      # 本地 Jackpot 状态
      jackpot_state: jackpot_state,
      # 🎰 免费游戏测试状态 (临时设置为1次触发，方便测试)
      free_game_test_state: Slot777FreeGameTest.init_state([trigger_count: 1])
    }

    %{state | game_data: game_data}
  end



  # 玩家加入房间后的处理
  def on_player_joined(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    Logger.info("🎰 [SLOT777] 玩家加入: #{user_id}, numeric_id: #{numeric_id}")
    Logger.info("🎰 [SLOT777] 玩家信息详情: #{inspect(player, pretty: true, limit: :infinity)}")

    # 获取玩家真实积分
    initial_points = get_player_points(state, numeric_id)
    Logger.info("🎰 [SLOT777] 玩家初始积分: #{initial_points} (机器人: #{player.is_robot})")

    # 更新用户信息
    updated_user_info = player.user
    |> Map.put(:money, initial_points)

    # 更新state中的玩家信息
    updated_player = %{player | user: updated_user_info}
    new_state = %{state | players: Map.put(state.players, numeric_id, updated_player)}

    send_player_info(new_state, player)
    # 发送游戏配置给新玩家
    send_game_config(new_state, player)

    # 发送当前游戏状态给新玩家
    send_game_state_to_user(new_state, player)

    # 发送玩家列表给新加入的玩家
    send_player_list_to_user(new_state, player)

    # 广播玩家数量变化通知
    broadcast_player_count_change(new_state)

    new_state
  end

  # 玩家重连加入房间
  def on_player_rejoined(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    Logger.info("🎰 [SLOT777] 玩家重连加入: #{user_id}, numeric_id: #{numeric_id}")

    # 获取玩家真实积分
    current_points = get_player_points(state, numeric_id)
    Logger.info("🎰 [SLOT777] 重连玩家积分: #{current_points}")

    send_player_info(state, player)
    # 发送游戏配置给重连玩家
    send_game_config(state, player)

    # 发送当前游戏状态给重连玩家
    send_game_state_to_user(state, player)

    # 发送玩家列表给重连玩家
    send_player_list_to_user(state, player)

    state
  end

  # 玩家离开房间
  def on_player_left(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    Logger.info("🎰 [SLOT777] 玩家离开: #{user_id}, numeric_id: #{numeric_id}")

    # 积分已由新系统自动同步，无需手动处理

    # 广播玩家数量变化通知
    broadcast_player_count_change(state)

    state
  end



  # 游戏开始
  def on_game_start(state) do
    Logger.info("🎰 [SLOT777] 游戏开始: #{state.id}")
    state
  end

  # 处理游戏消息
  def handle_game_message(state, player, message) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    Logger.info("🎰 [SLOT777] 收到游戏消息 - 房间: #{state.id}, 用户: #{user_id}, numeric_id: #{numeric_id}, 消息: #{inspect(message)}")

    case message do
      # 处理MainID=4的退出房间协议
      %{"mainId" => 4, "subId" => 40} ->
        data = message["data"] || %{}
        handle_exit_room_protocol(state, player, data)

      # 处理MainID=5的Slot777协议消息
      %{"mainId" => 5, "subId" => sub_id} ->
        data = message["data"] || %{}
        handle_slot777_protocol(state, player, sub_id, data)

      # 兼容性处理
      %{"cmd" => "get_room_info"} ->
        Logger.info("🏠 [ROOM_INFO_REQUEST] 房间信息请求 - 用户: #{user_id}")
        # 发送房间信息给用户
        send_game_config(state, player)
        send_game_state_to_user(state, player)
        state

      %{"cmd" => "request_jackpot"} ->
        Logger.info("🎰 [JACKPOT] Jackpot信息已自动广播，无需手动请求 - 用户: #{user_id}")
        state

      _ ->
        Logger.info("ℹ️ [GAME_MESSAGE] 消息已通过客户端协议处理或为未知消息 - 用户: #{user_id}, 消息: #{inspect(message)}")
        state
    end
  end

  # 处理Slot777协议消息
  defp handle_slot777_protocol(state, player, sub_id, data) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    Logger.info("🎰 [SLOT777_PROTOCOL] 处理协议 - SubID: #{sub_id}, 用户: #{user_id}, numeric_id: #{numeric_id}, 数据: #{inspect(data)}")

    case sub_id do
      # 离开房间 (CS_SLOT777_LEAVE_ROOM_P)
      2 ->
        handle_leave_room_request(state, player, data)

      # 游戏开始 (CS_SLOT777_GAMESTART_P)
      1000 ->
        handle_game_start_request(state, player, data)

      # Jackpot记录 (CS_SLOT777_JPLIST_P) - 同时发送1004和1005
      1003 ->
        handle_jackpot_list_request(state, player, data)

      # 切换下注倍率 (CS_SLOTS_SWITCH_BET_P)
      1009 ->
        handle_switch_bet_request(state, player, data)

      # 获取房间信息
      1006 ->
        handle_room_info_request(state, player, data)

      # 其他未实现的协议
      _ ->
        Logger.warning("🎰 [SLOT777] 未实现的子协议: #{sub_id}")
        send_error_response(state, player, sub_id, "未实现的协议")
        state
    end
  end

  # 发送游戏配置给玩家
  defp send_game_config(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    Logger.info("⚙️ [SEND_CONFIG] 发送游戏配置 - 用户: #{user_id}, numeric_id: #{numeric_id}, 房间: #{state.id}")

    # 🔧 使用新的标准积分获取方式
    player_money = get_player_points(state, numeric_id)

    # 获取玩家上次使用的下注倍率
    last_odds = Map.get(state.game_data.current_odds, user_id, 1)  # 默认为1倍

    player_name = "玩家#{numeric_id}"

    # 构建当前玩家信息
    player_info = %{
      "playerid" => numeric_id,  # 使用numeric_id作为playerid
      "money" => player_money,
      "name" => player_name,
      "seat" => 1
    }

    # 构建玩家列表（slot777是单机游戏，只包含当前玩家）
    playerlist = %{
      "1" => player_info  # 使用字符串键，与客户端期望的格式一致
    }

    message = %{
      "mainId" => 5,     # MainProto.Slot777
      "subId" => 0,       # 游戏配置协议
      "data" => %{
        # 底分配置
        "difen" => state.game_data.config.difen,
        # 下注倍率配置
        "odds" => state.game_data.config.odds_config,
        # 玩家上次使用的下注倍率
        "lastodds" => last_odds,
        # 下注限制
        "BetMax" => state.game_data.config.max_bet,
        "BetNeed" => state.game_data.config.min_bet,
        # 当前Jackpot金额
        "jackpot" => get_jackpot_amount(state),
        # 玩家金币
        "money" => player_money,
        # 当前回合ID
        "room_id" => state.id,
        "roundid" => state.game_data.current_round,
        # 玩家列表（替换playerInfo）
        "playerlist" => playerlist
      }
    }

    Logger.info("📤 [SEND_CONFIG] 配置消息内容 - 协议: 5/0, 底分: #{state.game_data.config.difen}, 上次倍率: #{last_odds}, 玩家: #{player_name}")
    send_to_player(state, player, message)
  end



  # 发送游戏状态给指定用户
  defp send_game_state_to_user(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    # 🔧 使用新的标准积分获取方式
    player_money = get_player_points(state, numeric_id)

    message = %{
      "mainId" => 4,     # MainProto.Slot777
      "subId" => 3,       # 游戏状态协议
      "data" => %{
        "status" => state.game_data.status,
        "round" => state.game_data.current_round,
        "money" => player_money,
        "jackpot" => get_jackpot_amount(state)
      }
    }

    Logger.info("📤 [SEND_GAME_STATE] 发送游戏状态给用户 - 用户: #{user_id}, numeric_id: #{numeric_id}, 状态: #{state.game_data.status}")
    send_to_player(state, player, message)
  end

  # 发送玩家列表给指定用户（slot777单机游戏简化版）
  defp send_player_list_to_user(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    # slot777是单机游戏，只发送当前玩家信息
    player_money = get_player_points(state, numeric_id)

    player_info = %{
      "playerid" => numeric_id,  # 使用numeric_id作为playerid
      "money" => player_money,
      "name" => "玩家#{numeric_id}",
      "seat" => 1
    }

    message = %{
      "mainId" => 5,     # MainProto.Slot777
      "subId" => 1007,    # SC_SLOT777_PLAYERLIST_P
      "data" => %{
        "playerlist" => [player_info],
        "totalplayernum" => 1
      }
    }

    Logger.info("📤 [SEND_PLAYER_LIST] 发送玩家信息给用户 - 用户: #{user_id}, numeric_id: #{numeric_id}")
    send_to_player(state, player, message)
  end
# 发送玩家信息给指定用户（slot777单机游戏简化版）
  defp send_player_info(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    # slot777是单机游戏，只发送当前玩家信息
    player_money = get_player_points(state, numeric_id)

    player_info = %{
      "playerid" => numeric_id,  # 使用numeric_id作为playerid
      "money" => player_money,
      "name" => "玩家#{numeric_id}",
      "seat" => 1
    }

    message = %{
      "mainId" => 4,     # MainProto.Slot777
      "subId" => 2,    # SC_SLOT777_PLAYERLIST_P
      "data" => %{
        "playerlist" => [player_info],
        "totalplayernum" => 1
      }
    }

    Logger.info("📤 [SEND_PLAYER_INFO] 发送玩家信息给用户 - 用户: #{user_id}, numeric_id: #{numeric_id}")
    send_to_player(state, player, message)
  end
  # 广播玩家数量变化通知
  defp broadcast_player_count_change(state) do
    message = %{
      "mainId" => 5,     # MainProto.Slot777
      "subId" => 1007,    # SC_SLOT777_PLAYERLIST_P
      "data" => %{
        "totalplayernum" => map_size(state.players)
      }
    }

    Logger.info("📤 [BROADCAST_PLAYER_COUNT] 广播玩家数量变化 - 房间总人数: #{map_size(state.players)}")
    broadcast_to_room(state, message)
  end





  @doc """
  开始游戏
  """
  def start_game(room_id, user_id, odds) do
    case GenServer.call({:via, Registry, {:game_room_registry, room_id}}, {:start_game, user_id, odds}) do
      {:ok, result} -> {:ok, result}
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  切换下注倍率
  """
  def switch_bet(room_id, user_id, odds) do
    case GenServer.call({:via, Registry, {:game_room_registry, room_id}}, {:switch_bet, user_id, odds}) do
      {:ok, result} -> {:ok, result}
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  获取Jackpot记录（从本地状态获取）
  """
  def get_jackpot_records(state) do
    # 从本地 Jackpot 状态获取最近的中奖记录
    case Slot777Jackpot.get_recent_winners(state.game_data.jackpot_state, 20) do
      winners when is_list(winners) ->
        # 转换为前端期望的格式
        formatted_records = Enum.map(winners, fn winner ->
          %{
            "player_name" => winner.player_name,
            "amount" => winner.amount,
            "seven_count" => winner.seven_count,
            "timestamp" => DateTime.to_unix(winner.timestamp),
            "room_id" => state.id
          }
        end)
        {:ok, formatted_records}
      error ->
        Logger.error("❌ [JACKPOT_RECORDS] 获取本地记录失败: #{inspect(error)}")
        {:error, "获取记录失败"}
    end
  end

  # 处理游戏开始请求
  @impl true
  def handle_call({:start_game, user_id, odds}, _from, state) do
    Logger.info("🎰 [SLOT777] 玩家 #{user_id} 开始游戏，倍率: #{odds}")

    # 通过 user_id 找到对应的玩家
    player_entry = Enum.find(state.players, fn {_numeric_id, player} ->
      player.user_id == user_id
    end)

    case player_entry do
      nil ->
        {:reply, {:error, "玩家不在房间中"}, state}
      {numeric_id, _player} ->
        # 验证倍率是否有效 - 检查倍率是否在配置的值中
        valid_odds = Map.values(state.game_data.config.odds_config)
        if odds not in valid_odds do
          {:reply, {:error, "无效的下注倍率"}, state}
        else
          # 🔧 使用新的标准积分获取方式
          current_points = get_player_points(state, numeric_id)

        # 计算下注金额 - 修正计算逻辑以匹配前端
        # 前端计算: odds × BET_RATE_NUM × difen × SCORE_RATE
        # 前端: odds × 9 × 100 × 100 = odds × 90000
        bet_amount = state.game_data.config.difen * odds * state.game_data.config.bet_rate_num * state.game_data.config.score_rate
        Logger.info("🎰 [SLOT777] 玩家积分 #{current_points} 下注金额: #{bet_amount} (倍率: #{odds}, 底分: #{state.game_data.config.difen}, 固定倍率: #{state.game_data.config.bet_rate_num}, 金币比例: #{state.game_data.config.score_rate})")
        # 🎰 免费游戏测试功能 - 记录玩家旋转次数并检查是否应该触发免费游戏
        Logger.info("🎰 [FREE_GAME_TEST] 开始检查免费游戏测试 - 用户: #{user_id}")
        Logger.info("🎰 [FREE_GAME_TEST] 当前免费游戏测试状态: #{inspect(state.game_data.free_game_test_state)}")

        updated_free_game_test_state = Slot777FreeGameTest.record_player_spin(state.game_data.free_game_test_state, user_id)
        current_count = Map.get(updated_free_game_test_state.player_spin_counts, user_id, 0)
        should_trigger_test_free_game = Slot777FreeGameTest.should_trigger_free_game(updated_free_game_test_state, user_id)

        Logger.info("🎰 [FREE_GAME_TEST] 玩家 #{user_id} 旋转计数: #{current_count}/#{updated_free_game_test_state.trigger_count}")
        Logger.info("🎰 [FREE_GAME_TEST] 应该触发免费游戏: #{should_trigger_test_free_game}")

        # 更新状态中的 free_game_test_state
        temp_game_data = %{state.game_data |
          free_game_test_state: updated_free_game_test_state
        }
        temp_state = %{state | game_data: temp_game_data}

        # 检查玩家余额
        if current_points < bet_amount do
          # 余额不足时，仍然生成游戏结果，但不扣除金币
          Logger.warning("❌ [INSUFFICIENT_FUNDS] 玩家余额不足，生成游戏结果但不扣除金币 - 用户: #{user_id}, 当前积分: #{current_points}, 需要积分: #{bet_amount}")

          # 🌐 生成游戏结果（由全局管理器决定是否中奖）
          game_result = cond do
            should_trigger_test_free_game ->
              Logger.info("🎰 [FREE_GAME_TEST] 玩家 #{user_id} 触发测试免费游戏，强制生成星星结果")
              # 强制生成免费游戏结果
              generate_test_free_game_result(odds, current_points, temp_state.game_data.config.difen, temp_state.game_data.config, temp_state)
            true ->
              # 使用本地游戏逻辑生成游戏结果
              Slot777GameLogic.generate_game_result(odds, current_points, temp_state.game_data.config.difen, temp_state.game_data.config)
          end

          # 添加余额不足标识和错误信息
          insufficient_funds_result = game_result
          |> Map.put("code", 1)
          |> Map.put("msg", "余额不足")
          |> Map.put("error_type", "insufficient_funds")
          |> Map.put("current_money", current_points)
          |> Map.put("required_money", bet_amount)
          |> Map.put("playerid", numeric_id)
          |> Map.put("changemoney", 0)  # 余额不足时金币变化为0

          {:reply, {:ok, insufficient_funds_result}, state}
        else
          # 🌐 生成游戏结果（由全局管理器决定是否中奖）
          game_result = cond do
            should_trigger_test_free_game ->
              Logger.info("🎰 [FREE_GAME_TEST] 玩家 #{user_id} 触发测试免费游戏，强制生成星星结果")
              # 强制生成免费游戏结果
              generate_test_free_game_result(odds, current_points, temp_state.game_data.config.difen, temp_state.game_data.config, temp_state)
            true ->
              # 使用本地游戏逻辑生成游戏结果
              Slot777GameLogic.generate_game_result(odds, current_points, temp_state.game_data.config.difen, temp_state.game_data.config)
          end

          # 🔧 使用新的标准积分操作方式
          win_money = game_result["winmoney"]
          jackpot_cash = game_result["jackpotcash"]
          total_win = win_money + jackpot_cash

          # 先扣除下注金额
          temp_updated_state = subtract_player_points(temp_state, numeric_id, bet_amount)

          # 再增加奖金
          final_updated_state = if total_win > 0 do
            add_player_points(temp_updated_state, numeric_id, total_win)
          else
            temp_updated_state
          end

          # 计算最终积分和变化金额
          final_points = get_player_points(final_updated_state, numeric_id)
          change_amount = final_points - current_points

          Logger.info("🎰 [SLOT777] 积分变化 - 用户: #{user_id}, 原积分: #{current_points}, 下注: #{bet_amount}, 奖金: #{win_money}, jackpot: #{jackpot_cash}, 新积分: #{final_points}, 变化: #{change_amount}")

          # 更新房间状态
          new_game_data = %{final_updated_state.game_data |
            current_round: final_updated_state.game_data.current_round + 1,
            current_odds: Map.put(final_updated_state.game_data.current_odds, user_id, odds)
          }

          new_state = %{final_updated_state | game_data: new_game_data}

          # 处理本地 Jackpot：贡献到奖池并检查是否中奖
          {jackpot_amount, state_after_jackpot} = handle_local_jackpot(new_state, bet_amount, total_win, game_result)
          final_state = state_after_jackpot

          # 如果有Jackpot奖金，需要加到玩家积分中
          final_state_with_jackpot = if jackpot_amount > 0 do
            Logger.info("🎉 [JACKPOT_WIN] 玩家中奖，添加Jackpot奖金到积分 - 用户: #{user_id}, 奖金: #{jackpot_amount}")
            add_player_points(final_state, numeric_id, jackpot_amount)
          else
            final_state
          end

          # 🌐 计算最终积分（包含Jackpot奖金）
          final_points_with_jackpot = get_player_points(final_state_with_jackpot, numeric_id)
          total_change_amount = final_points_with_jackpot - current_points

          Logger.info("🎰 [SLOT777] 最终积分变化 - 用户: #{user_id}, 原积分: #{current_points}, 下注: #{bet_amount}, 游戏奖金: #{total_win}, Jackpot奖金: #{jackpot_amount}, 最终积分: #{final_points_with_jackpot}, 总变化: #{total_change_amount}")

          # 发送游戏结果（包含最新积分信息和Jackpot奖金）
          enhanced_result = game_result
          |> Map.put("current_money", final_points_with_jackpot)  # 使用包含Jackpot的最新积分
          |> Map.put("playerid", numeric_id)  # 使用numeric_id作为playerid
          |> Map.put("changemoney", total_change_amount)  # 使用包含Jackpot的总变化金额
          |> Map.put("jackpotcash", jackpot_amount)  # 确保Jackpot奖金正确显示

          # 存储免费游戏信息到状态中，稍后在handle_game_start_request中发送
          # 同时处理免费游戏测试模式的计数器重置
          updated_state = if game_result["freetimes"] > 0 do
            free_game_info = %{
              user_id: user_id,
              free_times: game_result["freetimes"],
              odds: odds
            }

            # 如果是测试模式触发的免费游戏，重置计数器
            updated_free_game_test_state = if should_trigger_test_free_game do
              Logger.info("🎰 [FREE_GAME_TEST] 玩家 #{user_id} 触发免费游戏后重置计数器")
              Slot777FreeGameTest.reset_player_count(final_state_with_jackpot.game_data.free_game_test_state, user_id)
            else
              final_state_with_jackpot.game_data.free_game_test_state
            end

            # 更新游戏数据
            updated_game_data = %{final_state_with_jackpot.game_data | free_game_test_state: updated_free_game_test_state}
            updated_final_state = %{final_state_with_jackpot | game_data: updated_game_data}

            put_in(updated_final_state, [:game_data, :pending_free_games, user_id], free_game_info)
          else
            final_state_with_jackpot
          end

          {:reply, {:ok, enhanced_result}, updated_state}
        end
      end
    end
  end

  # 处理广播消息
  @impl true
  def handle_cast({:broadcast_message, message}, state) do
    Logger.info("📢 [BROADCAST_MESSAGE] 收到广播消息 - 房间: #{state.id}, 消息: #{inspect(message)}")

    # 广播消息给房间内所有玩家
    broadcast_to_room(state, message)

    {:noreply, state}
  end

  # 处理房间终止
  @impl true
  def terminate(reason, state) do
    Logger.info("🏠 [ROOM_TERMINATE] 房间终止 - 房间: #{state.id}, 原因: #{inspect(reason)}")
    # 本地管理，无需注销全局管理器
    :ok
  end

  # 处理切换下注倍率请求
  @impl true
  def handle_call({:switch_bet, user_id, odds}, _from, state) do
    Logger.info("🎰 [SLOT777] 玩家 #{user_id} 切换下注倍率: #{odds}")

    # 通过 user_id 找到对应的玩家
    player_entry = Enum.find(state.players, fn {_numeric_id, player} ->
      player.user_id == user_id
    end)

    case player_entry do
      nil ->
        {:reply, {:error, "玩家不在房间中"}, state}
      {_numeric_id, _player} ->
        # 验证倍率是否有效 - 检查倍率是否在配置的值中
        valid_odds = Map.values(state.game_data.config.odds_config)
        if odds not in valid_odds do
          {:reply, {:error, "无效的下注倍率"}, state}
        else
          # 更新玩家当前倍率
          new_current_odds = Map.put(state.game_data.current_odds, user_id, odds)
          new_game_data = %{state.game_data | current_odds: new_current_odds}
          new_state = %{state | game_data: new_game_data}

          result = %{
            "code" => 0,
            "msg" => "切换下注倍率成功",
            "odds" => odds,
            "bet_amount" => state.game_data.config.difen * odds * state.game_data.config.bet_rate_num * state.game_data.config.score_rate
          }

          {:reply, {:ok, result}, new_state}
        end
    end
  end

  # 处理MainID=4, SubID=40的退出房间协议
  defp handle_exit_room_protocol(state, player, data) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    Logger.info("🚪 [EXIT_ROOM] 处理退出房间协议 - 用户: #{user_id}, numeric_id: #{numeric_id}, 数据: #{inspect(data)}")

    # 清理玩家房间信息
    cleaned_state = cleanup_player_room_data(state, player)

    # 发送退出房间成功响应
    response = %{
      "mainId" => 4,
      "subId" => 14,  # 退出房间响应协议
      "data" => %{
        "code" => 0,
        "msg" => "退出房间成功",
        "playerid" => numeric_id,
        "room_id" => state.id
      }
    }

    Logger.info("📤 [EXIT_ROOM_RESPONSE] 发送退出房间响应 - 用户: #{user_id}, numeric_id: #{numeric_id}")
    send_to_player(cleaned_state, player, response)

    # 广播玩家离开通知给其他玩家（如果有的话）
    # broadcast_player_left_notification(cleaned_state, player)

    cleaned_state
  end

  # 处理离开房间请求
  defp handle_leave_room_request(state, player, data) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    Logger.info("🎰 [LEAVE_ROOM] 处理离开房间请求 - 用户: #{user_id}, numeric_id: #{numeric_id}, 数据: #{inspect(data)}")

    # 发送离开房间成功响应
    response = %{
      "mainId" => 5,
      "subId" => 12,  # SC_SLOT777_LEAVE_ROOM_P
      "data" => %{
        "code" => 0,
        "msg" => "离开房间成功"
      }
    }

    send_to_player(state, player, response)
    state
  end

  # 清理玩家房间数据
  defp cleanup_player_room_data(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    Logger.info("🧹 [CLEANUP] 清理玩家房间数据 - 用户: #{user_id}, numeric_id: #{numeric_id}")

    # 清理游戏数据中的玩家相关信息（不清理共享的 Jackpot 数据）
    cleaned_game_data = %{state.game_data |
      # 清理当前倍率记录
      current_odds: Map.delete(state.game_data.current_odds, user_id),
      # 清理待处理的免费游戏
      pending_free_games: Map.delete(state.game_data.pending_free_games, user_id)
    }

    # ⚠️ 注意：不清理 Jackpot 数据，因为 Jackpot 是全局共享的
    # Jackpot 数据由全局管理器管理，不需要在房间级别清理

    # 清理免费游戏测试状态中的玩家数据
    cleaned_free_game_test_state = Slot777FreeGameTest.cleanup_player_data(cleaned_game_data.free_game_test_state, user_id)

    # 更新游戏数据（保持 Jackpot 数据不变）
    final_game_data = %{cleaned_game_data |
      free_game_test_state: cleaned_free_game_test_state
    }

    # 从玩家列表中移除玩家
    cleaned_players = Map.delete(state.players, numeric_id)

    # 返回清理后的状态
    cleaned_state = %{state |
      players: cleaned_players,
      game_data: final_game_data
    }

    Logger.info("✅ [CLEANUP_COMPLETE] 玩家房间数据清理完成 - 用户: #{user_id}, 剩余玩家数: #{map_size(cleaned_players)} (Jackpot数据保持共享)")
    cleaned_state
  end

  # 广播玩家离开通知
  defp broadcast_player_left_notification(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    # 如果房间还有其他玩家，广播玩家离开通知
    if map_size(state.players) > 0 do
      Logger.info("📢 [PLAYER_LEFT] 广播玩家离开通知 - 用户: #{user_id}, numeric_id: #{numeric_id}")

      notification = %{
        "mainId" => 4,
        "subId" => 41,  # 玩家离开通知协议
        "data" => %{
          "playerid" => numeric_id,
          "player_name" => "玩家#{numeric_id}",
          "room_id" => state.id,
          "remaining_players" => map_size(state.players)
        }
      }

      broadcast_to_room(state, notification)
    else
      Logger.info("🏠 [EMPTY_ROOM] 房间已无其他玩家，无需广播离开通知 - 房间: #{state.id}")
    end
  end

  # 处理游戏开始请求
  defp handle_game_start_request(state, player, data) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    Logger.info("🎰 [GAME_START] 处理游戏开始请求 - 用户: #{user_id}, numeric_id: #{numeric_id}, 数据: #{inspect(data)}")

    odds = Map.get(data, "odds", 1)

    # 调用内部游戏开始处理
    case handle_call({:start_game, user_id, odds}, nil, state) do
      {:reply, {:ok, result}, new_state} ->
        # 检查是否是余额不足的情况
        is_insufficient_funds = Map.get(result, "error_type") == "insufficient_funds"

        # 1. 先发送1001协议 - 游戏结果（包含完整的游戏数据）
        response_1001 = %{
          "mainId" => 5,
          "subId" => 1001,  # SC_SLOT777_GAMESTART_P
          "data" => result
        }
        send_to_player(new_state, player, response_1001)

        # 2. 只有在非余额不足的情况下才发送金币更新协议
        if not is_insufficient_funds do
          current_points = get_player_points(new_state, numeric_id)
          money_update_data = %{
            "playerid" => numeric_id,
            "coin" => current_points
          }

          response_money_update = %{
            "mainId" => 4,  # MainProto.Game
            "subId" => 8,   # Game.SC_ROOM_RESET_COIN_P
            "data" => money_update_data
          }

          Logger.info("💰 [MONEY_UPDATE] 发送金币更新协议 - 用户: #{user_id}, numeric_id: #{numeric_id}, 积分: #{current_points}")
          send_to_player(new_state, player, response_money_update)
        else
          Logger.info("❌ [INSUFFICIENT_FUNDS] 余额不足，跳过金币更新协议 - 用户: #{user_id}")
        end

        # 2. 检查是否有待发送的免费游戏，如果有则发送1002协议
        final_state = case get_in(new_state, [:game_data, :pending_free_games, user_id]) do
          %{free_times: free_times, odds: free_odds} ->
            Logger.info("🎰 [FREE_GAME] 发送免费游戏 - 用户: #{user_id}, 免费次数: #{free_times}")
            # 生成并发送免费游戏结果，获取更新后的状态
            state_after_free_games = generate_and_send_free_games(new_state, user_id, free_times, free_odds)
            # 清除待发送的免费游戏信息
            put_in(state_after_free_games, [:game_data, :pending_free_games], Map.delete(state_after_free_games.game_data.pending_free_games || %{}, user_id))
          _ ->
            new_state
        end

        # 广播游戏结果给其他玩家
        broadcast_game_result(final_state, user_id, result)

        final_state

      {:reply, {:error, reason}, state} ->
        # 其他错误使用原有的错误响应
        send_error_response(state, player, 1001, reason)
        state
    end
  end

  # 处理Jackpot记录请求 - 同时发送1004和1005协议
  defp handle_jackpot_list_request(state, player, data) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    Logger.info("🎰 [JACKPOT_LIST] 处理Jackpot记录请求 - 用户: #{user_id}, numeric_id: #{numeric_id}, 数据: #{inspect(data)}")

    case get_jackpot_records(state) do
      {:ok, records} ->
        # 前端期望的是直接的记录数组，不是嵌套在 "records" 字段中
        # 将数组转换为以索引为键的对象格式
        records_map = records
        |> Enum.with_index()
        |> Enum.into(%{}, fn {record, index} -> {to_string(index), record} end)

        # 1. 发送1004协议 - Jackpot记录
        response_1004 = %{
          "mainId" => 5,
          "subId" => 1004,  # SC_SLOT777_JPLIST_P
          "data" => records_map
        }

        Logger.info("🎰 [JACKPOT_LIST] 发送1004协议 - Jackpot记录数据")
        send_to_player(state, player, response_1004)

        # 2. 发送1005协议 - 当前Jackpot金额
        current_jackpot = get_jackpot_amount(state)
        response_1005 = %{
          "mainId" => 5,
          "subId" => 1005,  # SC_SLOT777_JACKPOT_P
          "data" => %{
            "jackpot" => current_jackpot
          }
        }

        Logger.info("🎰 [JACKPOT_AMOUNT] 发送1005协议 - 当前Jackpot金额: #{current_jackpot}")
        send_to_player(state, player, response_1005)

      {:error, reason} ->
        Logger.error("🎰 [JACKPOT_LIST] 获取记录失败: #{reason}")
        send_error_response(state, player, 1004, "获取Jackpot记录失败: #{reason}")
    end

    state
  end



  # 处理切换下注倍率请求
  defp handle_switch_bet_request(state, player, data) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    Logger.info("🎰 [SWITCH_BET] 处理切换下注倍率请求 - 用户: #{user_id}, numeric_id: #{numeric_id}, 数据: #{inspect(data)}")

    odds = Map.get(data, "odds", 1)

    # 调用内部切换倍率处理
    case handle_call({:switch_bet, user_id, odds}, nil, state) do
      {:reply, {:ok, result}, new_state} ->
        # 发送切换结果给玩家
        response = %{
          "mainId" => 5,
          "subId" => 1009,  # CS_SLOTS_SWITCH_BET_P
          "data" => result
        }
        send_to_player(new_state, player, response)
        new_state

      {:reply, {:error, reason}, state} ->
        send_error_response(state, player, 1009, reason)
        state
    end
  end

  # 处理房间信息请求
  defp handle_room_info_request(state, player, data) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    Logger.info("🎰 [ROOM_INFO] 处理房间信息请求 - 用户: #{user_id}, numeric_id: #{numeric_id}, 数据: #{inspect(data)}")
    send_player_list_to_user(state, player)
    # 发送游戏配置和状态
    send_game_config(state, player)
    send_game_state_to_user(state, player)

    state
  end

  # 发送错误响应
  defp send_error_response(state, player, sub_id, reason) do
    response = %{
      "mainId" => 5,
      "subId" => sub_id,
      "data" => %{
        "code" => 1,
        "msg" => reason
      }
    }
    send_to_player(state, player, response)
  end

  # 生成并发送免费游戏结果
  defp generate_and_send_free_games(state, user_id, free_times, odds) do
    Logger.info("🎰 [FREE_GAME] 开始生成免费游戏 - 用户: #{user_id}, 免费次数: #{free_times}")

    # 生成免费游戏结果数组
    free_game_results = generate_free_game_results(free_times, odds, state.game_data.config.difen)

    # 计算免费游戏总奖金
    total_free_game_winnings = calculate_total_free_game_winnings(free_game_results)
    Logger.info("🎰 [FREE_GAME] 免费游戏总奖金: #{total_free_game_winnings}")

    # 🔧 使用新的标准积分操作方式（加上免费游戏奖金）
    updated_state = if total_free_game_winnings > 0 do
      player = find_player_by_user_id(state, user_id)
      if player do
        numeric_id = player.numeric_id
        current_points = get_player_points(state, numeric_id)
        new_state = add_player_points(state, numeric_id, total_free_game_winnings)
        new_points = get_player_points(new_state, numeric_id)
        Logger.info("🎰 [FREE_GAME] 更新玩家积分 - 用户: #{user_id}, 原积分: #{current_points}, 免费游戏奖金: #{total_free_game_winnings}, 新积分: #{new_points}")
        new_state
      else
        state
      end
    else
      state
    end

    # 发送免费游戏结果给玩家
    response = %{
      "mainId" => 5,
      "subId" => 1002,  # SC_SLOT777_FREEGAME_P
      "data" => free_game_results
    }

    Logger.info("📤 [FREE_GAME] 发送免费游戏结果 - 用户: #{user_id}, 结果数量: #{map_size(free_game_results)}")
    player = find_player_by_user_id(updated_state, user_id)
    if player do
      send_to_player(updated_state, player, response)

      # 发送金币更新协议 (免费游戏后，使用更新后的积分)
      numeric_id = player.numeric_id
      current_points = get_player_points(updated_state, numeric_id)
      money_update_data = %{
        "playerid" => numeric_id,
        "coin" => current_points
      }

      response_money_update = %{
        "mainId" => 4,  # MainProto.Game
        "subId" => 8,   # Game.SC_ROOM_RESET_COIN_P
        "data" => money_update_data
      }

      Logger.info("💰 [FREE_GAME_MONEY_UPDATE] 发送免费游戏后积分更新协议 - 用户: #{user_id}, numeric_id: #{numeric_id}, 积分: #{current_points}")
      send_to_player(updated_state, player, response_money_update)
    else
      Logger.warning("⚠️ [FREE_GAME] 找不到玩家 - user_id: #{user_id}")
    end

    updated_state
  end

  # 计算免费游戏总奖金
  defp calculate_total_free_game_winnings(free_game_results) do
    free_game_results
    |> Enum.reduce(0, fn {_round, result}, acc ->
      win_money = Map.get(result, "winmoney", 0)
      jackpot_cash = Map.get(result, "jackpotcash", 0)
      acc + win_money + jackpot_cash
    end)
  end

  # 生成免费游戏结果数组（下标从1开始）
  defp generate_free_game_results(free_times, odds, difen) do
    Logger.info("🎰 [FREE_GAME] 生成 #{free_times} 次免费游戏结果")

    # 生成指定次数的免费游戏结果，转换为以1为起始下标的Map
    1..free_times
    |> Enum.map(fn round ->
      # 每次免费游戏都生成新的结果
      game_result = Slot777GameLogic.generate_game_result(odds, 0, difen, nil)  # 免费游戏不扣金币，传入0，使用默认配置

      # 免费游戏结果包含所有必要字段
      result = %{
        "round" => round,
        "freetimes" => 0,  # 免费游戏中不再触发免费游戏
        "sevennum" => game_result["sevennum"],
        "iconresult" => game_result["iconresult"],
        "linecount" => game_result["linecount"],
        "lineresult" => game_result["lineresult"],
        "totalmult" => game_result["totalmult"],
        "winmoney" => game_result["winmoney"],
        "changemoney" => game_result["changemoney"],
        "jackpotcash" => game_result["jackpotcash"],
        "luckyjackpot" => Map.get(game_result, "luckyjackpot", 0)
      }

      {round, result}  # 返回{下标, 结果}的元组
    end)
    |> Enum.into(%{})  # 转换为Map，下标从1开始
  end

  # 发送Jackpot中奖通知（1006协议）
  defp send_jackpot_award_notification(state, user_id, player_name, jackpot_amount, game_result) do
    # 通过user_id找到numeric_id
    player = find_player_by_user_id(state, user_id)
    numeric_id = if player, do: player.numeric_id, else: 0

    # 构建1006协议数据，根据前端期望的格式
    jackpot_award_data = %{
      "playerid" => numeric_id,
      "name" => player_name,
      "headid" => 1,  # 默认头像ID
      "wxheadurl" => "",  # 自定义头像URL（暂时为空）
      "winscore" => jackpot_amount  # Jackpot中奖金额
    }

    message = %{
      "mainId" => 5,
      "subId" => 1006,  # SC_SLOT777_JPAWARD_P
      "data" => jackpot_award_data
    }

    Logger.info("🎰 [JACKPOT_AWARD] 发送Jackpot中奖通知 - 玩家: #{user_id}, 奖金: #{jackpot_amount}")

    # 发送给当前玩家
    player = find_player_by_user_id(state, user_id)
    if player do
      send_to_player(state, player, message)
    else
      Logger.warning("⚠️ [JACKPOT_AWARD] 找不到玩家 - user_id: #{user_id}")
    end

    # 同时广播给其他玩家（让其他玩家看到有人中奖）
    # 找到要排除的玩家对象
    exclude_player = find_player_by_user_id(state, user_id)
    if exclude_player do
      broadcast_to_room(state, message, [exclude_player])
    else
      broadcast_to_room(state, message)
    end
  end

  # 广播游戏结果给其他玩家 (1008协议)
  defp broadcast_game_result(state, user_id, result) do
    # 通过 user_id 找到对应的玩家获取 numeric_id
    player_entry = Enum.find(state.players, fn {_numeric_id, player} ->
      player.user_id == user_id
    end)

    case player_entry do
      {numeric_id, player} ->
        # 获取玩家当前积分
        current_points = get_player_points(state, numeric_id)

        # 计算赢的分数（不包括下注金额）
        win_score = Map.get(result, "winmoney", 0)

        # 计算倍数（总倍数除以10，与前端逻辑一致）
        mult = Map.get(result, "totalmult", 0) / 10

        # 构建1008协议数据，符合前端要求的格式
        broadcast_data = %{
          "playerid" => numeric_id,     # 玩家ID
          "winscore" => win_score,      # 赢的分数
          "mult" => mult,               # 赢的倍数
          "pmoney" => current_points    # 玩家当前积分
        }

        message = %{
          "mainId" => 5,
          "subId" => 1008,  # SC_SLOT777_GAMERESULT_P
          "data" => broadcast_data
        }

        Logger.info("📤 [BROADCAST_GAME_RESULT] 广播游戏结果 - 玩家: #{user_id}, numeric_id: #{numeric_id}, 赢分: #{win_score}, 倍数: #{mult}, 余额: #{current_points}")

        # 找到要排除的玩家对象
        exclude_player = find_player_by_user_id(state, user_id)
        if exclude_player do
          broadcast_to_room(state, message, [exclude_player])  # 排除玩家自己
        else
          broadcast_to_room(state, message)
        end
      nil ->
        Logger.warning("⚠️ [BROADCAST_GAME_RESULT] 找不到玩家 - user_id: #{user_id}")
    end
  end



  # 🧪 临时测试功能 - 可随时删除
  # 生成测试用的免费游戏结果（星星）
  defp generate_test_free_game_result(odds, current_money, difen, game_config \\ nil, state \\ nil) do
    # 使用配置中的值或默认值
    bet_rate_num = if game_config, do: game_config.bet_rate_num, else: 9
    score_rate = if game_config, do: game_config.score_rate, else: 100
    bet_amount = difen * odds * bet_rate_num * score_rate

    # 获取免费游戏测试状态
    free_game_test_state = if state, do: state.game_data.free_game_test_state, else: Slot777FreeGameTest.init_state()

    # 生成测试免费游戏矩阵
    test_icon_result = Slot777FreeGameTest.generate_test_free_game_matrix(free_game_test_state)
    |> matrix_to_icon_result_map()

    # 获取免费游戏次数
    free_times = Slot777FreeGameTest.get_free_times(free_game_test_state)

    # 计算星星数量
    star_count = count_stars_in_matrix(test_icon_result)

    # 🎰 添加免费游戏测试的 jackpotcash 奖金
    # 根据免费游戏类型给予不同的 jackpot 奖金
    jackpot_cash = case free_game_test_state.free_game_type do
      :normal -> bet_amount * 10   # Normal: 10倍下注金额的jackpot
      :mega -> bet_amount * 20     # Mega: 20倍下注金额的jackpot
      :super -> bet_amount * 50    # Super: 50倍下注金额的jackpot
      _ -> bet_amount * 10
    end

    # 计算总变化金额（包含jackpot奖金）
    total_change = jackpot_cash - bet_amount

    Logger.info("🎰 [FREE_GAME_TEST] 生成测试免费游戏结果 - 星星数量: #{star_count}, 免费次数: #{free_times}, Jackpot奖金: #{jackpot_cash}")

    %{
      "freetimes" => free_times,
      "sevennum" => 0,  # 免费游戏触发时没有7
      "iconresult" => test_icon_result,
      "linecount" => 0,  # 免费游戏触发时没有中奖线
      "lineresult" => [],
      "totalmult" => 0,
      "winmoney" => 0,  # 免费游戏触发时没有直接奖金
      "changemoney" => total_change,  # 包含jackpot奖金的总变化
      "jackpotcash" => jackpot_cash,  # 🎰 添加jackpot奖金
      "luckyjackpot" => 1  # 表示中了jackpot
    }
  end

  # 🧪 临时测试功能 - 可随时删除
  # 生成测试用的jackpot结果（5个7）
  defp generate_test_jackpot_result(odds, _current_money, difen, game_config \\ nil, state \\ nil) do
    # 使用配置中的值或默认值
    bet_rate_num = if game_config, do: game_config.bet_rate_num, else: 9
    score_rate = if game_config, do: game_config.score_rate, else: 100
    bet_amount = difen * odds * bet_rate_num * score_rate

    # 强制生成5个7的图标结果
    test_icon_result = %{
      "1" => 7, "2" => 7, "3" => 7, "4" => 7, "5" => 7,
      "6" => 7, "7" => 7, "8" => 7, "9" => 7, "10" => 7,
      "11" => 7, "12" => 7, "13" => 7, "14" => 7, "15" => 7
    }

    # 计算中奖线（所有线都是5个7）
    test_line_result = [
      %{"line" => 1, "num" => 5},
      %{"line" => 2, "num" => 5},
      %{"line" => 3, "num" => 5},
      %{"line" => 4, "num" => 5},
      %{"line" => 5, "num" => 5},
      %{"line" => 6, "num" => 5},
      %{"line" => 7, "num" => 5},
      %{"line" => 8, "num" => 5},
      %{"line" => 9, "num" => 5}
    ]

    # 计算巨额奖金（基础奖金 * 倍率 * 线数）
    base_win = bet_amount * 100  # 5个7的基础倍率
    total_win = base_win * length(test_line_result)

    # 获取当前jackpot金额
    jackpot_amount = if state, do: get_jackpot_amount(state), else: 100_000
    jackpot_win = trunc(jackpot_amount * 0.8)  # 80%的jackpot

    total_change = total_win + jackpot_win - bet_amount

    Logger.info("🧪 [TEST_JACKPOT] 生成测试jackpot结果 - 基础奖金: #{total_win}, Jackpot奖金: #{jackpot_win}, 总变化: #{total_change}")

    %{
      "freetimes" => 0,
      "sevennum" => 15,  # 15个7
      "iconresult" => test_icon_result,
      "linecount" => length(test_line_result),
      "lineresult" => test_line_result,
      "totalmult" => 100,
      "winmoney" => total_win,
      "changemoney" => total_change,
      "jackpotcash" => jackpot_win,
      "luckyjackpot" => 1  # 表示中了jackpot
    }
  end

  # 🧪 临时测试功能控制函数 - 可随时删除
  @doc """
  启用测试模式的便捷函数
  注意：现在需要在每个房间内部单独管理测试模式
  """
  def enable_test_mode() do
    Logger.info("🧪 [TEST_CONTROL] 注意：测试模式现在由每个房间内部管理，请在房间内调用相关函数")
  end

  @doc """
  禁用测试模式的便捷函数
  注意：现在需要在每个房间内部单独管理测试模式
  """
  def disable_test_mode() do
    Logger.info("🧪 [TEST_CONTROL] 注意：测试模式现在由每个房间内部管理，请在房间内调用相关函数")
  end

  @doc """
  重置所有玩家的旋转计数器
  注意：现在需要在每个房间内部单独管理测试模式
  """
  def reset_test_counters() do
    Logger.info("🧪 [TEST_CONTROL] 注意：测试模式现在由每个房间内部管理，请在房间内调用相关函数")
  end

  # 辅助函数：将3x5矩阵转换为icon_result格式的Map
  defp matrix_to_icon_result_map(matrix) do
    matrix
    |> Enum.with_index()
    |> Enum.flat_map(fn {row, row_index} ->
      row
      |> Enum.with_index()
      |> Enum.map(fn {value, col_index} ->
        # 计算位置索引：row * 5 + col + 1 (1-based indexing)
        position = row_index * 5 + col_index + 1
        {to_string(position), value}
      end)
    end)
    |> Enum.into(%{})
  end

  # 辅助函数：计算矩阵中星星的数量
  defp count_stars_in_matrix(icon_result) do
    icon_result
    |> Enum.count(fn {_pos, value} -> value == 8 end)  # 8 = 星星
  end

  # 辅助函数：通过user_id查找玩家对象
  defp find_player_by_user_id(state, user_id) do
    state.players
    |> Enum.find_value(fn {_id, player} ->
      if player.user_id == user_id, do: player, else: nil
    end)
  end



end
