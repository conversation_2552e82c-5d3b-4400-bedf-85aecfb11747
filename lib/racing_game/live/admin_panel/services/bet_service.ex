defmodule RacingGame.Live.AdminPanel.Services.BetService do
  @moduledoc """
  下注管理服务
  
  提供下注相关的业务逻辑：
  - 下注记录查询和管理
  - 下注统计和分析
  - 下注状态管理
  - 业务规则验证
  """

  require Logger
  alias RacingGame.Live.AdminPanel.Repositories.{RepositoryManager, BetRepository, UserRepository}
  alias RacingGame.Bet
  alias RacingGame.Live.AdminPanel.Utils.{ValidationHelper, CalculationHelper}

  # 常量定义
  @bet_statuses %{0 => :pending, 1 => :won, 2 => :lost}
  @status_codes %{pending: 0, won: 1, lost: 2}
  @min_bet_amount 1
  @max_bet_amount 1_000_000

  # ============================================================================
  # 下注查询和管理
  # ============================================================================

  @doc """
  获取下注详情

  ## 参数
  - `bet_id` - 下注ID
  - `options` - 选项

  ## 返回
  - `{:ok, bet}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_bet(bet_id, options \\ []) do
    Logger.debug("🔍 [下注服务] 获取下注详情: #{bet_id}")
    
    case BetRepository.get_by_id(bet_id, options) do
      {:ok, bet} ->
        enriched_bet = enrich_bet_data(bet)
        {:ok, enriched_bet}
      error -> error
    end
  end

  @doc """
  分页查询下注列表

  ## 参数
  - `params` - 查询参数
  - `options` - 选项

  ## 返回
  - `{:ok, %{bets: bets, page_info: page_info}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_bets(params \\ %{}, options \\ []) do
    Logger.debug("📋 [下注服务] 分页查询下注列表")
    
    # 应用业务过滤规则
    filtered_params = apply_business_filters(params, options)
    
    case BetRepository.list_paginated(filtered_params) do
      {:ok, %{bets: bets, page_info: page_info}} ->
        enriched_bets = Enum.map(bets, &enrich_bet_data/1)
        {:ok, %{bets: enriched_bets, page_info: page_info}}
      error -> error
    end
  end

  @doc """
  获取用户下注记录

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - `{:ok, bets}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_bets(user_id, options \\ []) do
    Logger.debug("👤 [下注服务] 获取用户下注记录: #{user_id}")
    
    case BetRepository.get_user_bets(user_id, options) do
      {:ok, bets} ->
        enriched_bets = Enum.map(bets, &enrich_bet_data/1)
        {:ok, enriched_bets}
      error -> error
    end
  end

  @doc """
  获取比赛下注记录

  ## 参数
  - `race_issue` - 比赛期号
  - `options` - 选项

  ## 返回
  - `{:ok, bets}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_race_bets(race_issue, options \\ []) do
    Logger.debug("🏁 [下注服务] 获取比赛下注记录: #{race_issue}")
    
    case BetRepository.get_race_bets(race_issue, options) do
      {:ok, bets} ->
        enriched_bets = Enum.map(bets, &enrich_bet_data/1)
        {:ok, enriched_bets}
      error -> error
    end
  end

  # ============================================================================
  # 下注状态管理
  # ============================================================================

  @doc """
  更新下注状态

  ## 参数
  - `bet_id` - 下注ID
  - `status` - 新状态
  - `payout` - 赔付金额（可选）
  - `operator_id` - 操作者ID

  ## 返回
  - `{:ok, bet}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_bet_status(bet_id, status, payout \\ nil, operator_id) do
    Logger.info("🔄 [下注服务] 更新下注状态: #{bet_id} -> #{status}")
    
    with {:ok, bet} <- get_bet_for_update(bet_id),
         :ok <- validate_status_update(bet, status),
         {:ok, update_params} <- prepare_status_update_params(status, payout),
         {:ok, updated_bet} <- update_bet_record(bet, update_params),
         :ok <- handle_status_change_effects(updated_bet, bet.status) do
      
      Logger.info("✅ [下注服务] 下注状态更新成功: #{bet_id}")
      clear_bet_cache(bet_id)
      {:ok, updated_bet}
    else
      {:error, reason} ->
        Logger.error("❌ [下注服务] 下注状态更新失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  批量更新下注状态

  ## 参数
  - `bet_ids` - 下注ID列表
  - `status` - 新状态
  - `operator_id` - 操作者ID

  ## 返回
  - `{:ok, results}` - 成功
  - `{:error, reason}` - 失败
  """
  def batch_update_status(bet_ids, status, operator_id) do
    Logger.info("📦 [下注服务] 批量更新下注状态: #{length(bet_ids)}个下注")
    
    operations = Enum.map(bet_ids, fn id ->
      {:update_bet_status, [id, status, nil, operator_id]}
    end)
    
    case RepositoryManager.batch_execute(:bet, operations) do
      {:ok, results} ->
        # 清除相关缓存
        Enum.each(bet_ids, &clear_bet_cache/1)
        {:ok, results}
      error -> error
    end
  end

  @doc """
  结算比赛下注

  ## 参数
  - `race_issue` - 比赛期号
  - `winning_selection` - 获胜选择
  - `payout_rate` - 赔付率
  - `operator_id` - 操作者ID

  ## 返回
  - `{:ok, settlement_result}` - 成功
  - `{:error, reason}` - 失败
  """
  def settle_race_bets(race_issue, winning_selection, payout_rate, operator_id) do
    Logger.info("🏆 [下注服务] 结算比赛下注: #{race_issue} - #{winning_selection}")
    
    with {:ok, race_bets} <- get_race_bets(race_issue),
         {:ok, settlement_plan} <- calculate_settlement_plan(race_bets, winning_selection, payout_rate),
         {:ok, settlement_result} <- execute_settlement(settlement_plan, operator_id) do
      
      Logger.info("✅ [下注服务] 比赛结算完成: #{race_issue}")
      {:ok, settlement_result}
    else
      {:error, reason} ->
        Logger.error("❌ [下注服务] 比赛结算失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  # ============================================================================
  # 统计和分析
  # ============================================================================

  @doc """
  获取下注统计信息

  ## 参数
  - `params` - 查询参数
  - `options` - 选项

  ## 返回
  - `{:ok, statistics}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_statistics(params \\ %{}, options \\ []) do
    Logger.debug("📊 [下注服务] 获取下注统计")
    
    with {:ok, basic_stats} <- BetRepository.get_statistics(params),
         {:ok, business_stats} <- calculate_business_statistics(params, options) do
      
      combined_stats = Map.merge(basic_stats, business_stats)
      {:ok, combined_stats}
    else
      error -> error
    end
  end

  @doc """
  获取用户下注汇总

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - `{:ok, summary}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_summary(user_id, options \\ []) do
    Logger.debug("📊 [下注服务] 获取用户下注汇总: #{user_id}")
    
    with {:ok, basic_summary} <- BetRepository.get_user_summary(user_id, options),
         {:ok, business_summary} <- calculate_user_business_summary(user_id, options) do
      
      combined_summary = Map.merge(basic_summary, business_summary)
      {:ok, combined_summary}
    else
      error -> error
    end
  end

  @doc """
  获取热门选择统计

  ## 参数
  - `race_issue` - 比赛期号（可选）
  - `options` - 选项

  ## 返回
  - `{:ok, popular_selections}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_popular_selections(race_issue \\ nil, options \\ []) do
    Logger.debug("🔥 [下注服务] 获取热门选择统计")
    
    # TODO: 实现热门选择统计逻辑
    {:ok, []}
  end

  # ============================================================================
  # 数据验证和业务规则
  # ============================================================================

  @doc """
  验证下注数据

  ## 参数
  - `bet_data` - 下注数据

  ## 返回
  - `:ok` - 验证通过
  - `{:error, reason}` - 验证失败
  """
  def validate_bet_data(bet_data) do
    with :ok <- validate_bet_amount(bet_data[:amount]),
         :ok <- validate_selection(bet_data[:selection]),
         :ok <- validate_race_issue(bet_data[:race_issue]) do
      :ok
    else
      error -> error
    end
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 丰富下注数据
  defp enrich_bet_data(bet) do
    bet
    |> Map.put(:status_name, Map.get(@bet_statuses, bet.status, :unknown))
    |> Map.put(:profit_loss, calculate_profit_loss(bet))
    |> Map.put(:is_settled, bet.status != 0)
  end

  # 计算盈亏
  defp calculate_profit_loss(bet) do
    case bet.status do
      1 -> bet.payout - bet.amount  # 赢了
      2 -> -bet.amount              # 输了
      _ -> 0                        # 待定
    end
  end

  # 应用业务过滤规则
  defp apply_business_filters(params, _options) do
    # TODO: 根据用户权限和业务规则过滤参数
    params
  end

  # 获取下注用于更新
  defp get_bet_for_update(bet_id) do
    BetRepository.get_by_id(bet_id, preload: [:user])
  end

  # 验证状态更新
  defp validate_status_update(bet, new_status) do
    case {bet.status, new_status} do
      {0, status} when status in [1, 2] -> :ok  # 从待定到已结算
      {old_status, new_status} when old_status == new_status -> {:error, "状态未改变"}
      _ -> {:error, "无效的状态转换"}
    end
  end

  # 准备状态更新参数
  defp prepare_status_update_params(status, payout) do
    status_code = Map.get(@status_codes, status, status)
    params = %{status: status_code}
    
    params = if payout do
      Map.put(params, :payout, payout)
    else
      params
    end
    
    {:ok, params}
  end

  # 更新下注记录
  defp update_bet_record(bet, params) do
    case Bet.update(bet, params) do
      {:ok, updated_bet} -> {:ok, updated_bet}
      {:error, error} -> {:error, "更新下注失败: #{inspect(error)}"}
    end
  end

  # 处理状态变更效果
  defp handle_status_change_effects(updated_bet, old_status) do
    # TODO: 处理状态变更的副作用，如积分变更、通知等
    :ok
  end

  # 计算结算计划
  defp calculate_settlement_plan(bets, winning_selection, payout_rate) do
    settlement_plan = Enum.map(bets, fn bet ->
      if bet.selection == winning_selection do
        payout = CalculationHelper.calculate_payout(bet.amount, payout_rate)
        %{bet_id: bet.id, status: 1, payout: payout}
      else
        %{bet_id: bet.id, status: 2, payout: 0}
      end
    end)
    
    {:ok, settlement_plan}
  end

  # 执行结算
  defp execute_settlement(settlement_plan, operator_id) do
    operations = Enum.map(settlement_plan, fn plan ->
      {:update_bet_status, [plan.bet_id, plan.status, plan.payout, operator_id]}
    end)
    
    case RepositoryManager.transaction(operations) do
      {:ok, results} ->
        settlement_result = %{
          total_bets: length(settlement_plan),
          winning_bets: Enum.count(settlement_plan, &(&1.status == 1)),
          losing_bets: Enum.count(settlement_plan, &(&1.status == 2)),
          total_payout: Enum.sum(Enum.map(settlement_plan, & &1.payout))
        }
        {:ok, settlement_result}
      error -> error
    end
  end

  # 计算业务统计
  defp calculate_business_statistics(_params, _options) do
    # TODO: 实现业务统计计算
    {:ok, %{}}
  end

  # 计算用户业务汇总
  defp calculate_user_business_summary(_user_id, _options) do
    # TODO: 实现用户业务汇总计算
    {:ok, %{}}
  end

  # 验证下注金额
  defp validate_bet_amount(amount) when is_integer(amount) do
    if amount >= @min_bet_amount and amount <= @max_bet_amount do
      :ok
    else
      {:error, "下注金额必须在#{@min_bet_amount}-#{@max_bet_amount}之间"}
    end
  end
  defp validate_bet_amount(_), do: {:error, "下注金额必须是整数"}

  # 验证选择
  defp validate_selection(selection) when is_binary(selection) and selection != "", do: :ok
  defp validate_selection(_), do: {:error, "选择不能为空"}

  # 验证比赛期号
  defp validate_race_issue(race_issue) when is_binary(race_issue) and race_issue != "", do: :ok
  defp validate_race_issue(_), do: {:error, "比赛期号不能为空"}

  # 清除下注缓存
  defp clear_bet_cache(bet_id), do: BetRepository.clear_cache(bet_id)
end
