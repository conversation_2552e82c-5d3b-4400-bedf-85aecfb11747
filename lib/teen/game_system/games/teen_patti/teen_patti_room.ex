defmodule <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.TeenPatti.TeenPattiRoom do
  @moduledoc """
  Teen Patti游戏房间实现

  Teen Patti是一种流行的印度纸牌游戏，类似于三张牌扑克。
  游戏规则：
  - 每个玩家发3张牌
  - 玩家可以选择看牌或盲注
  - 支持加注、跟注、弃牌等操作
  - 最后比较牌型大小决定胜负
  """

  use <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.RoomBase, game_type: :teen_patti

  alias <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.TeenPatti.{TeenPattiLogic, TeenPattiProtocol}
  alias Cypridina.Teen.GameSystem.RobotService

  # 游戏状态
  @game_phases %{
    # 等待开始
    waiting: :waiting,
    # 发牌阶段
    dealing: :dealing,
    # 下注阶段
    betting: :betting,
    # 摊牌阶段
    showdown: :showdown,
    # 结算阶段
    settling: :settling
  }

  # 玩家动作
  @player_actions %{
    # 弃牌
    fold: :fold,
    # 跟注
    call: :call,
    # 加注
    raise: :raise,
    # 过牌
    check: :check,
    # 摊牌
    show: :show,
    # 盲注
    blind: :blind,
    # 看牌
    seen: :seen
  }

  # 牌型等级 (从小到大)
  @hand_ranks %{
    # 高牌
    high_card: 1,
    # 对子
    pair: 2,
    # 同花
    flush: 3,
    # 顺子
    straight: 4,
    # 同花顺
    straight_flush: 5,
    # 三条
    three_of_kind: 6
  }

  @impl true
  def init_game_logic(state) do
    Logger.info("🃏 [TEEN_PATTI] 初始化游戏逻辑: #{state.id}")

    game_config =
      Map.merge(
        %{
          # 最小下注
          min_bet: 10,
          # 最大下注
          max_bet: 1000,
          # 底注
          ante: 5,
          # 最大轮数
          max_rounds: 50,
          # 摊牌费用倍数
          show_cost_multiplier: 2,
          # 启用机器人
          enable_robots: true,
          # 机器人数量
          robot_count: 3
        },
        state.config
      )

    game_data = %{
      phase: @game_phases.waiting,
      round: 0,
      current_bet: game_config.ante,
      # 奖池
      pot: 0,
      # 牌堆
      deck: [],
      # 玩家数据 %{user_id => player_data}
      players: %{},
      # 活跃玩家列表
      active_players: [],
      # 当前行动玩家
      current_player: nil,
      # 庄家
      dealer: nil,
      # 最后动作
      last_action: nil,
      # 下注历史
      betting_history: [],
      # 阶段计时器
      phase_timer: nil,
      config: game_config
    }

    %{state | game_data: game_data}
  end

  @impl true
  def on_player_joined(state, player) do
    user_id = player.user_id
    Logger.info("🃏 [TEEN_PATTI] 玩家加入: #{user_id}")

    # 获取玩家真实积分作为筹码
    real_points = 10000

    # 初始化玩家数据
    player_data = %{
      user_id: user_id,
      user_info: player.user_info || %{},
      # 使用真实积分作为筹码
      chips: real_points,
      # 当前下注
      bet: 0,
      # 手牌
      cards: [],
      # 玩家状态
      status: :waiting,
      # 是否看牌
      is_seen: false,
      # 是否活跃
      is_active: true,
      seat_id: get_next_seat_id(state),
      joined_at: System.system_time(:millisecond)
    }

    # 更新游戏数据
    new_players = Map.put(state.game_data.players, user_id, player_data)
    game_data = %{state.game_data | players: new_players}
    new_state = %{state | game_data: game_data}

    # 发送房间信息给新玩家
    send_room_info(new_state, player)

    # 检查是否可以开始游戏
    check_game_start(new_state)
  end

  @impl true
  def on_player_left(state, player) do
    user_id = player.user_id
    Logger.info("🃏 [TEEN_PATTI] 玩家离开: #{user_id}")

    # 移除玩家数据
    new_players = Map.delete(state.game_data.players, user_id)
    new_active_players = List.delete(state.game_data.active_players, user_id)

    game_data = %{state.game_data | players: new_players, active_players: new_active_players}

    new_state = %{state | game_data: game_data}

    # 如果是当前行动玩家离开，切换到下一个玩家
    if state.game_data.current_player == user_id do
      next_player(new_state)
    else
      new_state
    end
  end

  @impl true
  def on_game_start(state) do
    Logger.info("🃏 [TEEN_PATTI] 开始新一轮游戏: #{state.id}")

    # 重置游戏数据
    game_data = %{
      state.game_data
      | phase: :dealing,
        round: state.game_data.round + 1,
        pot: 0,
        deck: TeenPattiLogic.create_deck(),
        betting_history: [],
        current_bet: state.game_data.config.ante
    }

    # 重置玩家状态
    new_players = reset_players_for_new_round(game_data.players)
    active_players = get_active_player_list(new_players)

    game_data = %{
      game_data
      | players: new_players,
        active_players: active_players,
        dealer: select_dealer(active_players, state.game_data.dealer)
    }

    new_state = %{state | game_data: game_data}

    # 发牌
    deal_cards(new_state)
  end

  @impl true
  def handle_game_message(state, player, message) do
    user_id = player.user_id
    case message do
      %{"cmd" => "player_action", "action" => action, "amount" => amount} ->
        handle_player_action(state, user_id, action, amount)

      %{"cmd" => "get_room_info"} ->
        send_room_info(state, player)
        state

      %{"cmd" => "ready"} ->
        handle_player_ready(state, user_id)

      _ ->
        Logger.warning("🃏 [TEEN_PATTI] 未知消息: #{inspect(message)}")
        state
    end
  end

  @impl true
  def handle_game_tick(state) do
    # 处理游戏阶段转换和超时
    case state.game_data.phase do
      :betting ->
        check_betting_timeout(state)

      :showdown ->
        check_showdown_timeout(state)

      _ ->
        state
    end
  end

  # 私有函数

  defp get_next_seat_id(state) do
    existing_seats =
      state.game_data.players
      |> Map.values()
      |> Enum.map(& &1.seat_id)

    1..6
    |> Enum.find(fn seat -> seat not in existing_seats end) ||
      1
  end

  defp send_room_info(state, player) do
    user_id = player.user_id
    room_info = %{
      room_id: state.id,
      game_type: :teen_patti,
      phase: state.game_data.phase,
      round: state.game_data.round,
      pot: state.game_data.pot,
      current_bet: state.game_data.current_bet,
      current_player: state.game_data.current_player,
      players: format_players_for_client(state.game_data.players, user_id),
      config: state.game_data.config
    }

    # 通过Phoenix Channel发送消息
    send_to_player(state, player, %{cmd: "room_info", data: room_info})
  end

  defp format_players_for_client(players, requesting_user_id) do
    Enum.map(players, fn {user_id, player_data} ->
      %{
        user_id: user_id,
        seat_id: player_data.seat_id,
        chips: player_data.chips,
        bet: player_data.bet,
        status: player_data.status,
        is_seen: player_data.is_seen,
        is_active: player_data.is_active,
        # 只向玩家自己显示手牌
        cards: if(user_id == requesting_user_id, do: player_data.cards, else: [])
      }
    end)
  end

  defp check_game_start(state) do
    player_count = map_size(state.game_data.players)
    min_players = state.game_data.config[:min_players] || 2

    if player_count >= min_players and state.game_data.phase == :waiting do
      # 延迟开始游戏
      Process.send_after(self(), :start_game, 3000)

      # 广播游戏即将开始
      broadcast_to_room(state, %{
        countdown: 3,
        message: "游戏即将开始..."
      })
    end

    state
  end

  defp reset_players_for_new_round(players) do
    Enum.map(players, fn {user_id, player_data} ->
      {user_id,
       %{player_data | bet: 0, cards: [], status: :waiting, is_seen: false, is_active: true}}
    end)
    |> Enum.into(%{})
  end

  defp get_active_player_list(players) do
    players
    |> Enum.filter(fn {_user_id, player_data} -> player_data.is_active end)
    |> Enum.map(fn {user_id, _player_data} -> user_id end)
  end

  defp select_dealer(active_players, current_dealer) do
    case current_dealer do
      nil ->
        List.first(active_players)

      dealer ->
        case Enum.find_index(active_players, &(&1 == dealer)) do
          nil ->
            List.first(active_players)

          index ->
            next_index = rem(index + 1, length(active_players))
            Enum.at(active_players, next_index)
        end
    end
  end

  defp deal_cards(state) do
    Logger.info("🃏 [TEEN_PATTI] 发牌阶段")

    # 给每个活跃玩家发3张牌
    {new_deck, new_players} =
      TeenPattiLogic.deal_cards_to_players(
        state.game_data.deck,
        state.game_data.players,
        state.game_data.active_players
      )

    game_data = %{
      state.game_data
      | deck: new_deck,
        players: new_players,
        phase: :betting,
        current_player: List.first(state.game_data.active_players)
    }

    new_state = %{state | game_data: game_data}

    # 广播发牌完成 (使用协议格式: MainProto.XC (5) + SC_TEENPATTI_SENDCARD_P (1001))
    broadcast_to_room(new_state, %{
      "mainId" => 5,     # MainProto.XC
      "subId" => 1001,   # SC_TEENPATTI_SENDCARD_P
      "data" => %{
        "phase" => :betting,
        "current_player" => game_data.current_player,
        "pot" => game_data.pot
      }
    })

    # 启动下注计时器
    start_betting_timer(new_state)
  end

  defp handle_player_action(state, user_id, action, amount \\ 0) do
    if state.game_data.current_player == user_id do
      case action do
        "fold" -> handle_fold(state, user_id)
        "call" -> handle_call(state, user_id)
        "raise" -> handle_raise(state, user_id, amount)
        "check" -> handle_check(state, user_id)
        "show" -> handle_show(state, user_id)
        _ -> state
      end
    else
      Logger.warning("🃏 [TEEN_PATTI] 非当前玩家尝试行动: #{user_id}")
      state
    end
  end

  defp handle_fold(state, user_id) do
    Logger.info("🃏 [TEEN_PATTI] 玩家弃牌: #{user_id}")

    # 更新玩家状态
    new_players = put_in(state.game_data.players[user_id].status, :folded)
    new_players = put_in(new_players[user_id].is_active, false)

    # 移除活跃玩家
    new_active_players = List.delete(state.game_data.active_players, user_id)

    game_data = %{state.game_data | players: new_players, active_players: new_active_players}

    new_state = %{state | game_data: game_data}

    # 广播玩家动作
    broadcast_player_action(new_state, user_id, :fold, 0)

    # 检查游戏是否结束
    check_game_end(new_state)
  end

  defp handle_call(state, user_id) do
    current_bet = state.game_data.current_bet
    player = state.game_data.players[user_id]
    call_amount = current_bet - player.bet

    if player.chips >= call_amount do
      # 更新玩家数据
      new_players =
        state.game_data.players
        |> put_in([user_id, :bet], current_bet)
        |> put_in([user_id, :chips], player.chips - call_amount)

      # 更新奖池
      new_pot = state.game_data.pot + call_amount

      game_data = %{state.game_data | players: new_players, pot: new_pot}

      new_state = %{state | game_data: game_data}

      # 广播玩家动作
      broadcast_player_action(new_state, user_id, :call, call_amount)

      # 切换到下一个玩家
      next_player(new_state)
    else
      # 筹码不足，自动弃牌
      handle_fold(state, user_id)
    end
  end

  defp handle_raise(state, user_id, raise_amount) do
    current_bet = state.game_data.current_bet
    new_bet = current_bet + raise_amount
    player = state.game_data.players[user_id]
    total_amount = new_bet - player.bet

    if player.chips >= total_amount and raise_amount >= state.game_data.config.min_bet do
      # 更新玩家数据
      new_players =
        state.game_data.players
        |> put_in([user_id, :bet], new_bet)
        |> put_in([user_id, :chips], player.chips - total_amount)

      # 更新游戏数据
      game_data = %{
        state.game_data
        | players: new_players,
          current_bet: new_bet,
          pot: state.game_data.pot + total_amount
      }

      new_state = %{state | game_data: game_data}

      # 广播玩家动作
      broadcast_player_action(new_state, user_id, :raise, raise_amount)

      # 切换到下一个玩家
      next_player(new_state)
    else
      Logger.warning("🃏 [TEEN_PATTI] 无效加注: #{user_id}, 金额: #{raise_amount}")
      state
    end
  end

  defp handle_check(state, user_id) do
    # 过牌只在没有下注时有效
    if state.game_data.current_bet == 0 do
      broadcast_player_action(state, user_id, :check, 0)
      next_player(state)
    else
      # 有下注时必须跟注或弃牌
      handle_call(state, user_id)
    end
  end

  defp handle_show(state, user_id) do
    # 摊牌逻辑
    Logger.info("🃏 [TEEN_PATTI] 玩家摊牌: #{user_id}")

    # 进入摊牌阶段
    game_data = %{state.game_data | phase: :showdown}
    new_state = %{state | game_data: game_data}

    # 计算游戏结果
    settle_game(new_state)
  end

  defp handle_player_ready(state, user_id) do
    # 更新玩家准备状态
    new_players = put_in(state.game_data.players[user_id].status, :ready)
    game_data = %{state.game_data | players: new_players}
    new_state = %{state | game_data: game_data}

    # 检查是否所有玩家都准备好了
    check_all_ready(new_state)
  end

  defp next_player(state) do
    current_index =
      Enum.find_index(
        state.game_data.active_players,
        &(&1 == state.game_data.current_player)
      )

    if current_index do
      next_index = rem(current_index + 1, length(state.game_data.active_players))
      next_player_id = Enum.at(state.game_data.active_players, next_index)

      game_data = %{state.game_data | current_player: next_player_id}
      new_state = %{state | game_data: game_data}

      # 广播当前玩家变更 (使用协议格式: MainProto.XC (5) + SC_TEENPATTI_WAITOPT_P (1014))
      broadcast_to_room(new_state, %{
        "mainId" => 5,     # MainProto.XC
        "subId" => 1014,   # SC_TEENPATTI_WAITOPT_P
        "data" => %{
          "current_player" => next_player_id
        }
      })

      new_state
    else
      state
    end
  end

  defp check_game_end(state) do
    active_count = length(state.game_data.active_players)

    if active_count <= 1 do
      # 游戏结束，进行结算
      settle_game(state)
    else
      # 继续游戏
      next_player(state)
    end
  end

  defp settle_game(state) do
    Logger.info("🃏 [TEEN_PATTI] 游戏结算")

    # 计算获胜者
    winner = determine_winner(state.game_data.active_players, state.game_data.players)

    # 分配奖池
    if winner do
      new_players =
        put_in(
          state.game_data.players[winner.user_id].chips,
          winner.chips + state.game_data.pot
        )

      game_data = %{state.game_data | players: new_players, phase: :settling, pot: 0}

      new_state = %{state | game_data: game_data}

      # 广播游戏结果 (使用协议格式: MainProto.XC (5) + SC_TEENPATTI_JIESHUAN_P (1015))
      broadcast_to_room(new_state, %{
        "mainId" => 5,     # MainProto.XC
        "subId" => 1015,   # SC_TEENPATTI_JIESHUAN_P
        "data" => %{
          "winner" => winner,
          "pot" => state.game_data.pot,
          "final_hands" => get_final_hands(state.game_data.players)
        }
      })

      # 3秒后开始新一轮
      Process.send_after(self(), :start_new_round, 3000)
      new_state
    else
      state
    end
  end

  defp determine_winner(active_players, players) do
    # 比较所有活跃玩家的牌型
    active_players
    |> Enum.map(fn user_id ->
      player = players[user_id]
      hand_rank = TeenPattiLogic.evaluate_hand(player.cards)
      %{user_id: user_id, hand_rank: hand_rank, chips: player.chips}
    end)
    |> Enum.max_by(& &1.hand_rank)
  end

  defp get_final_hands(players) do
    Enum.map(players, fn {user_id, player_data} ->
      %{
        user_id: user_id,
        cards: player_data.cards,
        hand_rank: TeenPattiLogic.evaluate_hand(player_data.cards)
      }
    end)
  end

  defp check_all_ready(state) do
    all_ready =
      state.game_data.players
      |> Map.values()
      |> Enum.all?(&(&1.status == :ready))

    if all_ready and map_size(state.game_data.players) >= 2 do
      Process.send_after(self(), :start_game, 1000)
    end

    state
  end

  defp start_betting_timer(state) do
    # 30秒下注时间
    Process.send_after(self(), :betting_timeout, 30_000)
    state
  end

  defp check_betting_timeout(state) do
    # 如果当前玩家超时，自动弃牌
    if state.game_data.current_player do
      handle_fold(state, state.game_data.current_player)
    else
      state
    end
  end

  defp check_showdown_timeout(state) do
    # 摊牌阶段超时，自动结算
    settle_game(state)
  end

  defp broadcast_player_action(state, user_id, action, amount) do
    # 根据动作类型选择对应的协议ID
    sub_id = case action do
      :fold -> 1011  # SC_TEENPATTI_FOLD_P
      :call -> 1003  # SC_TEENPATTI_BET_P
      :raise -> 1003 # SC_TEENPATTI_BET_P
      :check -> 1003 # SC_TEENPATTI_BET_P
      :show -> 1007  # SC_TEENPATTI_COMPETITION_P
      _ -> 1003      # 默认使用下注协议
    end

    # 使用协议格式: MainProto.XC (5) + 对应的子协议
    broadcast_to_room(state, %{
      "mainId" => 5,     # MainProto.XC
      "subId" => sub_id,
      "data" => %{
        "user_id" => user_id,
        "action" => action,
        "amount" => amount,
        "pot" => state.game_data.pot,
        "current_bet" => state.game_data.current_bet
      }
    })
  end

  # 处理下注超时消息
  def handle_info(:betting_timeout, state) do
    Logger.info("🃏 [TEEN_PATTI] 下注超时: #{state.game_data.current_player}")

    new_state = check_betting_timeout(state)
    {:noreply, new_state}
  end

  # 处理摊牌超时消息
  def handle_info(:showdown_timeout, state) do
    Logger.info("🃏 [TEEN_PATTI] 摊牌超时")

    new_state = check_showdown_timeout(state)
    {:noreply, new_state}
  end

  # 处理开始游戏消息
  def handle_info(:start_game, state) do
    Logger.info("🃏 [TEEN_PATTI] 开始游戏")

    new_state = on_game_start(state)
    {:noreply, new_state}
  end

  # 处理开始新一轮消息
  def handle_info(:start_new_round, state) do
    Logger.info("🃏 [TEEN_PATTI] 开始新一轮")

    new_state = on_game_start(state)
    {:noreply, new_state}
  end

  # 私有辅助函数


end
