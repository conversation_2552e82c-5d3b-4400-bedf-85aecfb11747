defmodule RacingGame.Live.AdminPanel.Services.StockService do
  @moduledoc """
  股票持仓管理服务
  
  提供股票持仓相关的业务逻辑：
  - 股票持仓查询和管理
  - 持仓统计和分析
  - 股票价格管理
  - 业务规则验证
  """

  require Logger
  alias RacingGame.Live.AdminPanel.Repositories.{RepositoryManager, StockRepository, UserRepository}
  alias RacingGame.Stock
  alias RacingGame.Live.AdminPanel.Utils.{ValidationHelper, CalculationHelper}

  # 常量定义
  @min_quantity 1
  @max_quantity 1_000_000
  @min_price Decimal.new("0.01")
  @max_price Decimal.new("1000.00")

  # ============================================================================
  # 股票持仓查询和管理
  # ============================================================================

  @doc """
  获取股票持仓详情

  ## 参数
  - `stock_id` - 股票持仓ID
  - `options` - 选项

  ## 返回
  - `{:ok, stock}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_stock(stock_id, options \\ []) do
    Logger.debug("🔍 [股票服务] 获取股票持仓详情: #{stock_id}")
    
    case StockRepository.get_by_id(stock_id, options) do
      {:ok, stock} ->
        enriched_stock = enrich_stock_data(stock)
        {:ok, enriched_stock}
      error -> error
    end
  end

  @doc """
  分页查询股票持仓列表

  ## 参数
  - `params` - 查询参数
  - `options` - 选项

  ## 返回
  - `{:ok, %{stocks: stocks, page_info: page_info}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_stocks(params \\ %{}, options \\ []) do
    Logger.debug("📋 [股票服务] 分页查询股票持仓列表")
    
    # 应用业务过滤规则
    filtered_params = apply_business_filters(params, options)
    
    case StockRepository.list_paginated(filtered_params) do
      {:ok, %{stocks: stocks, page_info: page_info}} ->
        enriched_stocks = Enum.map(stocks, &enrich_stock_data/1)
        {:ok, %{stocks: enriched_stocks, page_info: page_info}}
      error -> error
    end
  end

  @doc """
  获取用户股票持仓

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - `{:ok, stocks}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_stocks(user_id, options \\ []) do
    Logger.debug("👤 [股票服务] 获取用户股票持仓: #{user_id}")
    
    case StockRepository.get_user_stocks(user_id, options) do
      {:ok, stocks} ->
        enriched_stocks = Enum.map(stocks, &enrich_stock_data/1)
        {:ok, enriched_stocks}
      error -> error
    end
  end

  @doc """
  获取赛车手持仓分布

  ## 参数
  - `racer_id` - 赛车手ID
  - `options` - 选项

  ## 返回
  - `{:ok, holdings}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_racer_holdings(racer_id, options \\ []) do
    Logger.debug("🏎️ [股票服务] 获取赛车手持仓分布: #{racer_id}")
    
    case StockRepository.get_racer_holdings(racer_id, options) do
      {:ok, holdings} ->
        enriched_holdings = Enum.map(holdings, &enrich_stock_data/1)
        {:ok, enriched_holdings}
      error -> error
    end
  end

  @doc """
  获取用户特定赛车手持仓

  ## 参数
  - `user_id` - 用户ID
  - `racer_id` - 赛车手ID

  ## 返回
  - `{:ok, stock}` - 成功
  - `{:error, :not_found}` - 持仓不存在
  - `{:error, reason}` - 其他错误
  """
  def get_user_racer_stock(user_id, racer_id) do
    Logger.debug("🎯 [股票服务] 获取用户特定持仓: #{user_id} - #{racer_id}")
    
    case StockRepository.get_user_racer_stock(user_id, racer_id) do
      {:ok, stock} ->
        enriched_stock = enrich_stock_data(stock)
        {:ok, enriched_stock}
      error -> error
    end
  end

  # ============================================================================
  # 股票交易管理
  # ============================================================================

  @doc """
  更新股票持仓

  ## 参数
  - `stock_id` - 股票持仓ID
  - `params` - 更新参数
  - `operator_id` - 操作者ID

  ## 返回
  - `{:ok, stock}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_stock(stock_id, params, operator_id) do
    Logger.info("✏️ [股票服务] 更新股票持仓: #{stock_id}")
    
    with {:ok, stock} <- get_stock_for_update(stock_id),
         {:ok, validated_params} <- validate_update_params(params),
         {:ok, updated_stock} <- update_stock_record(stock, validated_params),
         :ok <- handle_update_effects(updated_stock, stock) do
      
      Logger.info("✅ [股票服务] 股票持仓更新成功: #{stock_id}")
      clear_stock_cache(stock_id)
      {:ok, updated_stock}
    else
      {:error, reason} ->
        Logger.error("❌ [股票服务] 股票持仓更新失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  批量调整持仓数量

  ## 参数
  - `adjustments` - 调整列表，每个元素为 %{stock_id: id, quantity_change: change}
  - `operator_id` - 操作者ID

  ## 返回
  - `{:ok, results}` - 成功
  - `{:error, reason}` - 失败
  """
  def batch_adjust_quantities(adjustments, operator_id) do
    Logger.info("📦 [股票服务] 批量调整持仓数量: #{length(adjustments)}个持仓")
    
    operations = Enum.map(adjustments, fn adjustment ->
      {:adjust_stock_quantity, [adjustment.stock_id, adjustment.quantity_change, operator_id]}
    end)
    
    case RepositoryManager.batch_execute(:stock, operations) do
      {:ok, results} ->
        # 清除相关缓存
        stock_ids = Enum.map(adjustments, & &1.stock_id)
        Enum.each(stock_ids, &clear_stock_cache/1)
        {:ok, results}
      error -> error
    end
  end

  @doc """
  重新计算持仓成本

  ## 参数
  - `stock_id` - 股票持仓ID
  - `new_price` - 新价格
  - `operator_id` - 操作者ID

  ## 返回
  - `{:ok, stock}` - 成功
  - `{:error, reason}` - 失败
  """
  def recalculate_cost(stock_id, new_price, operator_id) do
    Logger.info("💰 [股票服务] 重新计算持仓成本: #{stock_id}")
    
    with {:ok, stock} <- get_stock_for_update(stock_id),
         :ok <- validate_price(new_price),
         {:ok, new_total_cost} <- calculate_new_total_cost(stock.quantity, new_price),
         {:ok, updated_stock} <- update_stock_record(stock, %{total_cost: new_total_cost}) do
      
      Logger.info("✅ [股票服务] 持仓成本重新计算成功: #{stock_id}")
      clear_stock_cache(stock_id)
      {:ok, updated_stock}
    else
      {:error, reason} ->
        Logger.error("❌ [股票服务] 持仓成本重新计算失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  # ============================================================================
  # 统计和分析
  # ============================================================================

  @doc """
  获取股票持仓统计信息

  ## 参数
  - `params` - 查询参数
  - `options` - 选项

  ## 返回
  - `{:ok, statistics}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_statistics(params \\ %{}, options \\ []) do
    Logger.debug("📊 [股票服务] 获取股票持仓统计")
    
    with {:ok, basic_stats} <- StockRepository.get_statistics(params),
         {:ok, business_stats} <- calculate_business_statistics(params, options) do
      
      combined_stats = Map.merge(basic_stats, business_stats)
      {:ok, combined_stats}
    else
      error -> error
    end
  end

  @doc """
  获取用户持仓汇总

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - `{:ok, summary}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_summary(user_id, options \\ []) do
    Logger.debug("📊 [股票服务] 获取用户持仓汇总: #{user_id}")
    
    with {:ok, basic_summary} <- StockRepository.get_user_summary(user_id, options),
         {:ok, business_summary} <- calculate_user_business_summary(user_id, options) do
      
      combined_summary = Map.merge(basic_summary, business_summary)
      {:ok, combined_summary}
    else
      error -> error
    end
  end

  @doc """
  获取热门赛车手统计

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, popular_racers}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_popular_racers(options \\ []) do
    Logger.debug("🔥 [股票服务] 获取热门赛车手统计")
    
    # TODO: 实现热门赛车手统计逻辑
    {:ok, []}
  end

  @doc """
  获取大户持仓排行

  ## 参数
  - `min_quantity` - 最小持仓数量
  - `options` - 选项

  ## 返回
  - `{:ok, large_holders}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_large_holders(min_quantity \\ 1000, options \\ []) do
    Logger.debug("🐋 [股票服务] 获取大户持仓排行")
    
    # TODO: 实现大户持仓排行逻辑
    {:ok, []}
  end

  # ============================================================================
  # 数据验证和业务规则
  # ============================================================================

  @doc """
  验证股票持仓数据

  ## 参数
  - `stock_data` - 股票持仓数据

  ## 返回
  - `:ok` - 验证通过
  - `{:error, reason}` - 验证失败
  """
  def validate_stock_data(stock_data) do
    with :ok <- validate_quantity(stock_data[:quantity]),
         :ok <- validate_racer_id(stock_data[:racer_id]),
         :ok <- validate_total_cost(stock_data[:total_cost]) do
      :ok
    else
      error -> error
    end
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 丰富股票持仓数据
  defp enrich_stock_data(stock) do
    average_cost = if stock.quantity > 0 do
      Decimal.div(stock.total_cost, stock.quantity)
    else
      Decimal.new(0)
    end
    
    stock
    |> Map.put(:average_cost, average_cost)
    |> Map.put(:has_holdings, stock.quantity > 0)
  end

  # 应用业务过滤规则
  defp apply_business_filters(params, _options) do
    # TODO: 根据用户权限和业务规则过滤参数
    params
  end

  # 获取股票持仓用于更新
  defp get_stock_for_update(stock_id) do
    StockRepository.get_by_id(stock_id, preload: [:user])
  end

  # 验证更新参数
  defp validate_update_params(params) do
    # TODO: 实现参数验证逻辑
    {:ok, params}
  end

  # 更新股票持仓记录
  defp update_stock_record(stock, params) do
    case Stock.update(stock, params) do
      {:ok, updated_stock} -> {:ok, updated_stock}
      {:error, error} -> {:error, "更新股票持仓失败: #{inspect(error)}"}
    end
  end

  # 处理更新效果
  defp handle_update_effects(_updated_stock, _original_stock) do
    # TODO: 处理更新的副作用
    :ok
  end

  # 验证价格
  defp validate_price(price) when is_number(price) do
    decimal_price = Decimal.new(price)
    if Decimal.compare(decimal_price, @min_price) != :lt and 
       Decimal.compare(decimal_price, @max_price) != :gt do
      :ok
    else
      {:error, "价格必须在#{@min_price}-#{@max_price}之间"}
    end
  end
  defp validate_price(%Decimal{} = price) do
    if Decimal.compare(price, @min_price) != :lt and 
       Decimal.compare(price, @max_price) != :gt do
      :ok
    else
      {:error, "价格必须在#{@min_price}-#{@max_price}之间"}
    end
  end
  defp validate_price(_), do: {:error, "价格必须是数字"}

  # 计算新的总成本
  defp calculate_new_total_cost(quantity, price) do
    decimal_price = if is_number(price), do: Decimal.new(price), else: price
    total_cost = Decimal.mult(Decimal.new(quantity), decimal_price)
    {:ok, total_cost}
  end

  # 计算业务统计
  defp calculate_business_statistics(_params, _options) do
    # TODO: 实现业务统计计算
    {:ok, %{}}
  end

  # 计算用户业务汇总
  defp calculate_user_business_summary(_user_id, _options) do
    # TODO: 实现用户业务汇总计算
    {:ok, %{}}
  end

  # 验证数量
  defp validate_quantity(quantity) when is_integer(quantity) do
    if quantity >= @min_quantity and quantity <= @max_quantity do
      :ok
    else
      {:error, "持仓数量必须在#{@min_quantity}-#{@max_quantity}之间"}
    end
  end
  defp validate_quantity(_), do: {:error, "持仓数量必须是整数"}

  # 验证赛车手ID
  defp validate_racer_id(racer_id) when is_integer(racer_id) and racer_id > 0, do: :ok
  defp validate_racer_id(_), do: {:error, "赛车手ID必须是正整数"}

  # 验证总成本
  defp validate_total_cost(total_cost) when is_number(total_cost) and total_cost >= 0, do: :ok
  defp validate_total_cost(%Decimal{} = total_cost) do
    if Decimal.compare(total_cost, Decimal.new(0)) != :lt, do: :ok, else: {:error, "总成本不能为负数"}
  end
  defp validate_total_cost(_), do: {:error, "总成本必须是非负数"}

  # 清除股票持仓缓存
  defp clear_stock_cache(stock_id), do: StockRepository.clear_cache(stock_id)
end
