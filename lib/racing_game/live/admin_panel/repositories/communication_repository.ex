defmodule RacingGame.Live.AdminPanel.Repositories.CommunicationRepository do
  @moduledoc """
  系统通信数据访问仓储
  
  提供系统通信相关的数据访问抽象，隔离数据访问细节：
  - 通信记录的查询和过滤
  - 复杂查询构建
  - 数据缓存管理
  - 查询优化
  """

  require Logger
  alias RacingGame.SystemCommunication
  alias RacingGame.Live.AdminPanel.Repositories.QueryBuilders.CommunicationQueryBuilder
  require Ash.Query

  # 常量定义
  @cache_ttl 300  # 5分钟缓存
  @default_limit 20
  @max_limit 100

  # ============================================================================
  # 基础查询方法
  # ============================================================================

  @doc """
  根据ID获取通信记录

  ## 参数
  - `communication_id` - 通信ID
  - `options` - 选项
    - `:preload` - 预加载关联数据
    - `:use_cache` - 是否使用缓存

  ## 返回
  - `{:ok, communication}` - 成功
  - `{:error, :not_found}` - 通信不存在
  - `{:error, reason}` - 其他错误
  """
  def get_by_id(communication_id, options \\ []) do
    Logger.debug("🔍 [通信仓储] 根据ID获取通信: #{communication_id}")
    
    preload = Keyword.get(options, :preload, [])
    use_cache = Keyword.get(options, :use_cache, true)
    
    cache_key = build_cache_key("communication", communication_id, preload)
    
    if use_cache do
      case get_from_cache(cache_key) do
        {:ok, communication} -> {:ok, communication}
        :miss -> fetch_and_cache_communication_by_id(communication_id, preload, cache_key)
      end
    else
      fetch_communication_by_id(communication_id, preload)
    end
  end

  @doc """
  分页查询通信记录

  ## 参数
  - `params` - 查询参数
    - `:page` - 页码
    - `:limit` - 每页数量
    - `:search` - 搜索关键词
    - `:type` - 通信类型过滤
    - `:status` - 状态过滤
    - `:priority` - 优先级过滤
    - `:sort` - 排序条件
    - `:preload` - 预加载关联

  ## 返回
  - `{:ok, %{communications: communications, page_info: page_info}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_paginated(params \\ %{}) do
    Logger.debug("📋 [通信仓储] 分页查询通信记录")
    
    try do
      normalized_params = normalize_list_params(params)
      
      with {:ok, query} <- CommunicationQueryBuilder.build_list_query(normalized_params),
           {:ok, results} <- execute_paginated_query(query, normalized_params) do
        {:ok, results}
      else
        {:error, reason} -> {:error, reason}
      end
    rescue
      error ->
        Logger.error("❌ [通信仓储] 分页查询异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  搜索通信记录

  ## 参数
  - `search_term` - 搜索词
  - `options` - 选项
    - `:limit` - 结果数量限制
    - `:fields` - 搜索字段

  ## 返回
  - `{:ok, communications}` - 成功
  - `{:error, reason}` - 失败
  """
  def search(search_term, options \\ []) do
    Logger.debug("🔍 [通信仓储] 搜索通信记录: #{search_term}")
    
    limit = Keyword.get(options, :limit, @default_limit)
    fields = Keyword.get(options, :fields, [:title, :content])
    
    try do
      query = CommunicationQueryBuilder.build_search_query(search_term, fields)
      
      case query
           |> Ash.Query.limit(limit)
           |> Ash.read() do
        {:ok, communications} -> {:ok, communications}
        {:error, error} ->
          Logger.error("❌ [通信仓储] 搜索失败: #{inspect(error)}")
          {:error, :search_failed}
      end
    rescue
      error ->
        Logger.error("❌ [通信仓储] 搜索异常: #{inspect(error)}")
        {:error, :search_error}
    end
  end

  # ============================================================================
  # 业务查询方法
  # ============================================================================

  @doc """
  获取活跃的通信记录

  ## 参数
  - `options` - 选项
    - `:type` - 通信类型过滤
    - `:limit` - 数量限制

  ## 返回
  - `{:ok, communications}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_active(options \\ []) do
    Logger.debug("📋 [通信仓储] 获取活跃通信记录")
    
    try do
      query = SystemCommunication
      |> Ash.Query.filter(active == true)
      |> apply_type_filter(options[:type])
      |> apply_expiry_filter()
      |> Ash.Query.sort(inserted_at: :desc)
      |> maybe_limit(options[:limit])
      
      case Ash.read(query) do
        {:ok, communications} -> {:ok, communications}
        {:error, error} ->
          Logger.error("❌ [通信仓储] 获取活跃记录失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [通信仓储] 获取活跃记录异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  获取用户相关的通信记录

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - `{:ok, communications}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_communications(user_id, options \\ []) do
    Logger.debug("👤 [通信仓储] 获取用户通信记录: #{user_id}")
    
    try do
      query = SystemCommunication
      |> Ash.Query.filter(
        (recipient_type == :all) or 
        (recipient_type == :user and recipient_id == ^user_id)
      )
      |> Ash.Query.filter(active == true)
      |> apply_expiry_filter()
      |> Ash.Query.sort(inserted_at: :desc)
      |> maybe_limit(options[:limit])
      
      case Ash.read(query) do
        {:ok, communications} -> {:ok, communications}
        {:error, error} ->
          Logger.error("❌ [通信仓储] 获取用户记录失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [通信仓储] 获取用户记录异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # ============================================================================
  # 统计查询
  # ============================================================================

  @doc """
  获取通信统计信息

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, statistics}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_statistics(options \\ []) do
    Logger.debug("📊 [通信仓储] 获取通信统计")
    
    try do
      stats = %{
        total_count: get_total_count(),
        active_count: get_active_count(),
        type_distribution: get_type_distribution(),
        priority_distribution: get_priority_distribution(),
        recent_count: get_recent_count(options[:days] || 7)
      }
      
      {:ok, stats}
    rescue
      error ->
        Logger.error("❌ [通信仓储] 获取统计异常: #{inspect(error)}")
        {:error, :statistics_error}
    end
  end

  # ============================================================================
  # 缓存管理
  # ============================================================================

  @doc """
  清除通信相关缓存

  ## 参数
  - `communication_id` - 通信ID（可选）
  """
  def clear_cache(communication_id \\ nil) do
    if communication_id do
      Logger.debug("🧹 [通信仓储] 清除通信缓存: #{communication_id}")
      cache_patterns = ["communication:#{communication_id}:*"]
      Enum.each(cache_patterns, &clear_cache_pattern/1)
    else
      Logger.debug("🧹 [通信仓储] 清除所有通信缓存")
      clear_cache_pattern("communication:*")
    end
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 标准化列表参数
  defp normalize_list_params(params) do
    %{
      page: max(1, params[:page] || 1),
      limit: min(params[:limit] || @default_limit, @max_limit),
      search: String.trim(params[:search] || ""),
      type: params[:type],
      status: params[:status],
      priority: params[:priority],
      sort: params[:sort] || [inserted_at: :desc],
      preload: params[:preload] || []
    }
  end

  # 获取并缓存通信记录
  defp fetch_and_cache_communication_by_id(communication_id, preload, cache_key) do
    case fetch_communication_by_id(communication_id, preload) do
      {:ok, communication} ->
        cache_put(cache_key, communication, @cache_ttl)
        {:ok, communication}
      error -> error
    end
  end

  # 获取通信记录
  defp fetch_communication_by_id(communication_id, preload) do
    try do
      case SystemCommunication
           |> maybe_preload(preload)
           |> Ash.read(communication_id) do
        {:ok, communication} -> {:ok, communication}
        {:error, %Ash.Error.Query.NotFound{}} -> {:error, :not_found}
        {:error, error} ->
          Logger.error("❌ [通信仓储] 获取通信失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [通信仓储] 获取通信异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # 执行分页查询
  defp execute_paginated_query(query, params) do
    offset = (params.page - 1) * params.limit
    
    case query
         |> maybe_preload(params.preload)
         |> Ash.Query.page(count: true, limit: params.limit, offset: offset)
         |> Ash.read() do
      {:ok, %Ash.Page.Offset{results: communications, count: total_count}} ->
        page_info = %{
          total_count: total_count,
          page: params.page,
          limit: params.limit,
          total_pages: ceil(total_count / params.limit)
        }
        {:ok, %{communications: communications, page_info: page_info}}
      {:error, error} ->
        Logger.error("❌ [通信仓储] 分页查询失败: #{inspect(error)}")
        {:error, :query_failed}
    end
  end

  # 应用类型过滤
  defp apply_type_filter(query, nil), do: query
  defp apply_type_filter(query, type) do
    Ash.Query.filter(query, type == ^type)
  end

  # 应用过期时间过滤
  defp apply_expiry_filter(query) do
    Ash.Query.filter(query, is_nil(expires_at) or expires_at > ^DateTime.utc_now())
  end

  # 可能限制数量
  defp maybe_limit(query, nil), do: query
  defp maybe_limit(query, limit) when is_integer(limit) and limit > 0 do
    Ash.Query.limit(query, limit)
  end
  defp maybe_limit(query, _), do: query

  # 可能预加载关联数据
  defp maybe_preload(query, []), do: query
  defp maybe_preload(query, preload) when is_list(preload) do
    Ash.Query.load(query, preload)
  end

  # 获取总数
  defp get_total_count do
    case SystemCommunication |> Ash.Query.aggregate(:count, :id) |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  # 获取活跃数量
  defp get_active_count do
    case SystemCommunication 
         |> Ash.Query.filter(active == true)
         |> Ash.Query.aggregate(:count, :id) 
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  # 获取类型分布
  defp get_type_distribution do
    # TODO: 实现类型分布统计
    %{}
  end

  # 获取优先级分布
  defp get_priority_distribution do
    # TODO: 实现优先级分布统计
    %{}
  end

  # 获取最近数量
  defp get_recent_count(days) do
    date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
    
    case SystemCommunication 
         |> Ash.Query.filter(inserted_at >= ^date_threshold)
         |> Ash.Query.aggregate(:count, :id) 
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  # 构建缓存键
  defp build_cache_key(prefix, id, extra \\ nil) do
    case extra do
      nil -> "#{prefix}:#{id}"
      extra -> "#{prefix}:#{id}:#{:erlang.phash2(extra)}"
    end
  end

  # 缓存操作（需要实现具体的缓存后端）
  defp get_from_cache(_key), do: :miss
  defp cache_put(_key, _value, _ttl), do: :ok
  defp clear_cache_pattern(_pattern), do: :ok
end
