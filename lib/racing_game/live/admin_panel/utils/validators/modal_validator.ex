defmodule RacingGame.Live.AdminPanel.Validators.ModalValidator do
  @moduledoc """
  模态对话框表单验证器
  
  专门处理模态对话框中的表单验证逻辑
  """

  require Logger

  @doc """
  验证系统通信模态表单数据
  
  ## 参数
  - `form_data` - 表单数据
  
  ## 返回
  - `{:ok, validated_data}` - 验证成功
  - `{:error, errors}` - 验证失败
  """
  def validate_system_communication_form(form_data) do
    Logger.debug("🔍 [模态验证器] 验证系统通信表单")
    
    errors = []
    
    # 验证标题
    errors = validate_title(form_data["title"], errors)
    
    # 验证内容
    errors = validate_content(form_data["content"], errors)
    
    # 验证类型
    errors = validate_type(form_data["type"], errors)
    
    # 验证优先级
    errors = validate_priority(form_data["priority"], errors)
    
    # 验证接收者类型
    errors = validate_recipient_type(form_data["recipient_type"], errors)
    
    if Enum.empty?(errors) do
      {:ok, form_data}
    else
      {:error, errors}
    end
  end

  @doc """
  验证必填字段
  
  ## 参数
  - `form_data` - 表单数据
  - `required_fields` - 必填字段列表
  
  ## 返回
  - `{:ok, validated_data}` - 验证成功
  - `{:error, errors}` - 验证失败
  """
  def validate_required_fields(form_data, required_fields \\ ["title", "content"]) do
    Logger.debug("🔍 [模态验证器] 验证必填字段: #{inspect(required_fields)}")
    
    errors = Enum.reduce(required_fields, [], fn field, acc ->
      case form_data[field] do
        nil -> ["#{get_field_display_name(field)}不能为空" | acc]
        "" -> ["#{get_field_display_name(field)}不能为空" | acc]
        value when is_binary(value) ->
          if String.trim(value) == "" do
            ["#{get_field_display_name(field)}不能为空" | acc]
          else
            acc
          end
        _ -> acc
      end
    end)
    
    if Enum.empty?(errors) do
      {:ok, form_data}
    else
      {:error, Enum.reverse(errors)}
    end
  end

  @doc """
  验证字段长度
  
  ## 参数
  - `value` - 字段值
  - `field_name` - 字段名称
  - `max_length` - 最大长度
  
  ## 返回
  - `{:ok, value}` - 验证成功
  - `{:error, message}` - 验证失败
  """
  def validate_field_length(value, field_name, max_length) when is_binary(value) do
    if String.length(value) <= max_length do
      {:ok, value}
    else
      {:error, "#{get_field_display_name(field_name)}不能超过#{max_length}个字符"}
    end
  end
  
  def validate_field_length(nil, _field_name, _max_length), do: {:ok, nil}
  def validate_field_length("", _field_name, _max_length), do: {:ok, ""}

  # 私有函数 - 验证标题
  defp validate_title(title, errors) do
    cond do
      is_blank?(title) ->
        ["标题不能为空" | errors]
      String.length(title) > 200 ->
        ["标题不能超过200个字符" | errors]
      true ->
        errors
    end
  end

  # 私有函数 - 验证内容
  defp validate_content(content, errors) do
    cond do
      is_blank?(content) ->
        ["内容不能为空" | errors]
      String.length(content) > 5000 ->
        ["内容不能超过5000个字符" | errors]
      true ->
        errors
    end
  end

  # 私有函数 - 验证类型
  defp validate_type(type, errors) do
    valid_types = ["message", "announcement", "notification"]
    
    if type in valid_types do
      errors
    else
      ["无效的通信类型" | errors]
    end
  end

  # 私有函数 - 验证优先级
  defp validate_priority(priority, errors) do
    valid_priorities = ["low", "medium", "high", "urgent"]
    
    if priority in valid_priorities do
      errors
    else
      ["无效的优先级" | errors]
    end
  end

  # 私有函数 - 验证接收者类型
  defp validate_recipient_type(recipient_type, errors) do
    valid_recipient_types = ["all", "user", "admin"]
    
    if recipient_type in valid_recipient_types do
      errors
    else
      ["无效的接收者类型" | errors]
    end
  end

  # 私有函数 - 检查是否为空
  defp is_blank?(nil), do: true
  defp is_blank?(""), do: true
  defp is_blank?(str) when is_binary(str), do: String.trim(str) == ""
  defp is_blank?(_), do: false

  # 私有函数 - 获取字段显示名称
  defp get_field_display_name("title"), do: "标题"
  defp get_field_display_name("content"), do: "内容"
  defp get_field_display_name("type"), do: "类型"
  defp get_field_display_name("priority"), do: "优先级"
  defp get_field_display_name("recipient_type"), do: "接收者类型"
  defp get_field_display_name("recipient_id"), do: "接收者ID"
  defp get_field_display_name("expires_at"), do: "过期时间"
  defp get_field_display_name(field), do: field
end
