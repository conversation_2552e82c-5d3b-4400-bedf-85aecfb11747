defmodule Cypridina.Protocol.QuickLoginHandler do
  @moduledoc """
  快速登录处理器

  处理通过客户端唯一标识(client_uniq_id)的快速登录功能
  如果用户不存在，自动创建游客账户并赠送50000积分
  """

  require Logger
  alias Cypridina.Accounts.User


  @doc """
  处理快速登录

  ## 参数
  - client_uniq_id: 客户端唯一标识

  ## 返回值
  - {:ok, user}: 登录成功，返回用户信息
  - {:error, reason}: 登录失败，返回错误原因
  """
  def handle_quick_login(client_uniq_id) when is_binary(client_uniq_id) and client_uniq_id != "" do
    Logger.info("🚀 [QUICK_LOGIN] 开始处理快速登录 - client_uniq_id: #{client_uniq_id}")

    case User.get_by_client_uniq_id(client_uniq_id) do
      {:ok, user} ->
        Logger.info("🚀 [QUICK_LOGIN] 找到现有用户 - 用户ID: #{inspect(user)}")
        {:ok, user}

      {:error, _reason} ->
        Logger.info("🚀 [QUICK_LOGIN] 用户不存在，创建新游客账户")
        create_guest_user(client_uniq_id)

      # {:error, reason} ->
      #   Logger.error("🚀 [QUICK_LOGIN] 查询用户失败 - 原因: #{inspect(reason)}")
      #   {:error, "查询用户失败"}
    end
  end

  def handle_quick_login(_), do: {:error, "无效的客户端唯一标识"}

  defp create_guest_user(client_uniq_id) do
    Logger.info("🚀 [QUICK_LOGIN] 创建游客用户 - client_uniq_id: #{client_uniq_id}")

    case User.create_guest_user(%{
      client_uniq_id: client_uniq_id
    }) do
      {:ok, user} ->
        Logger.info("🚀 [QUICK_LOGIN] 游客用户创建成功 - 用户ID: #{user.id}, 用户名: #{user.username}")
        {:ok, user}

      {:error, %Ash.Error.Invalid{errors: errors}} ->
        error_msg = format_ash_errors(errors)
        Logger.error("🚀 [QUICK_LOGIN] 创建游客用户失败 - 错误: #{error_msg}")
        {:error, "创建游客用户失败: #{error_msg}"}

      {:error, reason} ->
        Logger.error("🚀 [QUICK_LOGIN] 创建游客用户失败 - 原因: #{inspect(reason)}")
        {:error, "创建游客用户失败"}
    end
  end

  defp format_ash_errors(errors) when is_list(errors) do
    errors
    |> Enum.map(&format_single_error/1)
    |> Enum.join(", ")
  end

  defp format_single_error(%{message: message}), do: message
  defp format_single_error(%{field: field, message: message}), do: "#{field}: #{message}"
  defp format_single_error(error), do: inspect(error)
end
