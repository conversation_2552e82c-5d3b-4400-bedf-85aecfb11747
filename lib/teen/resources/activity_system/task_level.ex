defmodule Teen.ActivitySystem.TaskLevel do
  @moduledoc """
  任务等级资源

  管理任务等级配置和要求
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  postgres do
    table "task_levels"
    repo Cypridina.Repo
  end

  admin do
    table_columns [:id, :level, :level_name, :required_exp, :status]
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_levels
    define :list_by_level_range
  end

  actions do
    defaults [:create, :read, :update, :destroy]

    read :list_active_levels do
      filter expr(status == 1)
    end

    read :list_by_level_range do
      argument :min_level, :integer, allow_nil?: false
      argument :max_level, :integer, allow_nil?: false
      filter expr(level >= ^arg(:min_level) and level <= ^arg(:max_level) and status == 1)
    end

    update :enable do
      change set_attribute(:status, 1)
    end

    update :disable do
      change set_attribute(:status, 0)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :level, :integer do
      allow_nil? false
      public? true
      description "等级"
      constraints min: 1
    end

    attribute :level_name, :string do
      allow_nil? false
      public? true
      description "等级名称"
      constraints max_length: 100
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "等级描述"
      constraints max_length: 1000
    end

    attribute :required_exp, :decimal do
      allow_nil? false
      public? true
      description "所需经验值"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :task_requirements, {:array, :map} do
      allow_nil? true
      public? true
      description "任务要求配置"
      default []
    end

    attribute :unlock_features, {:array, :string} do
      allow_nil? true
      public? true
      description "解锁功能列表"
      default []
    end

    attribute :privileges, {:array, :string} do
      allow_nil? true
      public? true
      description "等级特权列表"
      default []
    end

    attribute :bonus_multiplier, :decimal do
      allow_nil? false
      public? true
      description "奖励倍数"
      default Decimal.new("1.0")
      constraints min: Decimal.new("0.1")
    end

    attribute :daily_task_limit, :integer do
      allow_nil? false
      public? true
      description "每日任务限制"
      default 10
      constraints min: 1
    end

    attribute :weekly_task_limit, :integer do
      allow_nil? false
      public? true
      description "每周任务限制"
      default 50
      constraints min: 1
    end

    attribute :icon_url, :string do
      allow_nil? true
      public? true
      description "等级图标URL"
      constraints max_length: 500
    end

    attribute :badge_url, :string do
      allow_nil? true
      public? true
      description "等级徽章URL"
      constraints max_length: 500
    end

    attribute :color_theme, :string do
      allow_nil? true
      public? true
      description "等级主题色"
      constraints max_length: 20
    end

    attribute :status, :integer do
      allow_nil? false
      public? true
      description "状态：0-禁用，1-启用"
      default 1
      constraints min: 0, max: 1
    end

    attribute :sort_order, :integer do
      allow_nil? false
      public? true
      description "排序顺序"
      default 0
      constraints min: 0
    end

    timestamps()
  end

  identities do
    identity :unique_level, [:level]
    identity :unique_level_name, [:level_name]
  end
end
