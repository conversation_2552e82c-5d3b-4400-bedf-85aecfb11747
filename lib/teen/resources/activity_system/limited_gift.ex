defmodule Teen.ActivitySystem.LimitedGift do
  @moduledoc """
  限时礼包资源

  管理限时礼包活动配置和奖励
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  postgres do
    table "limited_gifts"
    repo Cypridina.Repo
  end

  admin do
    table_columns [:id, :gift_name, :gift_type, :start_time, :end_time, :status]
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_gifts
    define :list_current_gifts
    define :list_by_type
  end

  actions do
    defaults [:create, :read, :update, :destroy]

    read :list_active_gifts do
      filter expr(status == 1)
    end

    read :list_current_gifts do
      filter expr(status == 1 and start_time <= ^DateTime.utc_now() and end_time >= ^DateTime.utc_now())
    end

    read :list_by_type do
      argument :gift_type, :integer, allow_nil?: false
      filter expr(gift_type == ^arg(:gift_type) and status == 1)
    end

    update :enable do
      change set_attribute(:status, 1)
    end

    update :disable do
      change set_attribute(:status, 0)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :gift_name, :string do
      allow_nil? false
      public? true
      description "礼包名称"
      constraints max_length: 100
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "礼包描述"
      constraints max_length: 1000
    end

    attribute :gift_type, :integer do
      allow_nil? false
      public? true
      description "礼包类型：1-首次礼包，2-定时礼包，3-等级礼包"
      default 1
      constraints min: 1, max: 3
    end

    attribute :start_time, :utc_datetime do
      allow_nil? true
      public? true
      description "开始时间（定时礼包使用）"
    end

    attribute :end_time, :utc_datetime do
      allow_nil? true
      public? true
      description "结束时间（定时礼包使用）"
    end

    attribute :valid_days, :integer do
      allow_nil? true
      public? true
      description "有效天数（首次礼包使用）"
      constraints min: 1
    end

    attribute :required_level, :integer do
      allow_nil? true
      public? true
      description "所需等级（等级礼包使用）"
      constraints min: 1
    end

    attribute :coins_reward, :decimal do
      allow_nil? false
      public? true
      description "金币奖励"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :points_reward, :decimal do
      allow_nil? false
      public? true
      description "积分奖励"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :items_reward, {:array, :map} do
      allow_nil? true
      public? true
      description "道具奖励配置"
      default []
    end

    attribute :usage_limit, :integer do
      allow_nil? false
      public? true
      description "使用次数限制（0为无限制）"
      default 1
      constraints min: 0
    end

    attribute :per_user_limit, :integer do
      allow_nil? false
      public? true
      description "每用户限制次数（0为无限制）"
      default 1
      constraints min: 0
    end

    attribute :used_count, :integer do
      allow_nil? false
      public? true
      description "已使用次数"
      default 0
      constraints min: 0
    end

    attribute :status, :integer do
      allow_nil? false
      public? true
      description "状态：0-禁用，1-启用"
      default 1
      constraints min: 0, max: 1
    end

    attribute :priority, :integer do
      allow_nil? false
      public? true
      description "优先级（数字越大优先级越高）"
      default 0
      constraints min: 0
    end

    attribute :conditions, :map do
      allow_nil? true
      public? true
      description "领取条件配置"
    end

    timestamps()
  end

  identities do
    identity :unique_gift_name, [:gift_name]
  end
end
