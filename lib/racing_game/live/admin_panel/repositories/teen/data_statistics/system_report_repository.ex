defmodule RacingGame.Live.AdminPanel.Repositories.Teen.DataStatistics.SystemReportRepository do
  @moduledoc """
  📊 系统报表仓储层

  负责系统报表的数据访问操作，包括：
  - 系统报表的CRUD操作
  - 报表数据统计和分析
  - 报表查询和过滤
  - 报表缓存管理
  - 报表性能优化
  """

  require Logger
  import Ash.Query

  alias Teen.Statistics.SystemReport

  # 缓存配置
  @cache_ttl 600_000  # 10分钟缓存
  @cache_prefix "system_report_repo"

  # ==================== 基础 CRUD 操作 ====================

  @doc """
  创建系统报表

  ## 参数
  - `report_data` - 报表数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, system_report}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_system_report(report_data, options \\ []) do
    Logger.info("📊 [系统报表仓储] 创建系统报表: #{inspect(report_data[:report_type])}")

    try do
      result = SystemReport
      |> Ash.Changeset.for_create(:create, report_data)
      |> Ash.create()

      case result do
        {:ok, system_report} ->
          Logger.info("✅ [系统报表仓储] 系统报表创建成功: #{system_report.report_type}")
          clear_cache()
          {:ok, system_report}
        {:error, error} ->
          Logger.error("❌ [系统报表仓储] 系统报表创建失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [系统报表仓储] 创建系统报表异常: #{inspect(exception)}")
        {:error, :create_failed}
    end
  end

  @doc """
  获取系统报表

  ## 参数
  - `report_id` - 报表ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, system_report}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_system_report(report_id, options \\ []) do
    Logger.info("📊 [系统报表仓储] 获取系统报表: #{report_id}")

    cache_key = "#{@cache_prefix}:get:#{report_id}"
    
    case get_from_cache(cache_key) do
      {:ok, cached_result} ->
        Logger.info("🎯 [系统报表仓储] 缓存命中: #{report_id}")
        {:ok, cached_result}
      
      {:error, :not_found} ->
        try do
          result = SystemReport
          |> Ash.get(report_id)

          case result do
            {:ok, system_report} ->
              Logger.info("✅ [系统报表仓储] 系统报表获取成功: #{system_report.id}")
              put_to_cache(cache_key, system_report)
              {:ok, system_report}
            {:error, error} ->
              Logger.error("❌ [系统报表仓储] 系统报表获取失败: #{inspect(error)}")
              {:error, error}
          end
        rescue
          exception ->
            Logger.error("💥 [系统报表仓储] 获取系统报表异常: #{inspect(exception)}")
            {:error, :get_failed}
        end
    end
  end

  @doc """
  更新系统报表

  ## 参数
  - `system_report` - 系统报表实例
  - `update_data` - 更新数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, updated_system_report}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_system_report(system_report, update_data, options \\ []) do
    Logger.info("📊 [系统报表仓储] 更新系统报表: #{system_report.id}")

    try do
      result = system_report
      |> Ash.Changeset.for_update(:update, update_data)
      |> Ash.update()

      case result do
        {:ok, updated_system_report} ->
          Logger.info("✅ [系统报表仓储] 系统报表更新成功: #{updated_system_report.id}")
          clear_cache()
          {:ok, updated_system_report}
        {:error, error} ->
          Logger.error("❌ [系统报表仓储] 系统报表更新失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [系统报表仓储] 更新系统报表异常: #{inspect(exception)}")
        {:error, :update_failed}
    end
  end

  @doc """
  删除系统报表

  ## 参数
  - `system_report` - 系统报表实例
  - `options` - 选项参数

  ## 返回
  - `{:ok, deleted_system_report}` - 成功
  - `{:error, reason}` - 失败
  """
  def delete_system_report(system_report, options \\ []) do
    Logger.info("📊 [系统报表仓储] 删除系统报表: #{system_report.id}")

    try do
      result = system_report |> Ash.destroy()

      case result do
        :ok ->
          Logger.info("✅ [系统报表仓储] 系统报表删除成功: #{system_report.id}")
          clear_cache()
          {:ok, system_report}
        {:error, error} ->
          Logger.error("❌ [系统报表仓储] 系统报表删除失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      exception ->
        Logger.error("💥 [系统报表仓储] 删除系统报表异常: #{inspect(exception)}")
        {:error, :delete_failed}
    end
  end

  # ==================== 查询操作 ====================

  @doc """
  列出系统报表

  ## 参数
  - `params` - 查询参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, {reports, total_count}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_system_reports(params \\ %{}, options \\ []) do
    Logger.info("📊 [系统报表仓储] 列出系统报表: #{inspect(params)}")

    try do
      query = SystemReport
      |> build_list_query(params)
      |> maybe_load_associations(options)

      # 获取总数
      total_count = query |> Ash.count!()

      # 应用分页
      query = query
      |> limit(Map.get(params, :page_size, 20))
      |> offset(Map.get(params, :offset, 0))

      reports = query |> Ash.read!()

      Logger.info("✅ [系统报表仓储] 系统报表列表获取成功，共 #{length(reports)} 条")
      {:ok, {reports, total_count}}
    rescue
      exception ->
        Logger.error("💥 [系统报表仓储] 列出系统报表异常: #{inspect(exception)}")
        {:error, :list_failed}
    end
  end

  @doc """
  按报表类型获取报表

  ## 参数
  - `report_type` - 报表类型
  - `options` - 选项参数

  ## 返回
  - `{:ok, reports}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_reports_by_type(report_type, options \\ []) do
    Logger.info("📊 [系统报表仓储] 按类型获取报表: #{report_type}")

    cache_key = "#{@cache_prefix}:by_type:#{report_type}"
    
    case get_from_cache(cache_key) do
      {:ok, cached_result} ->
        Logger.info("🎯 [系统报表仓储] 缓存命中: #{report_type}")
        {:ok, cached_result}
      
      {:error, :not_found} ->
        try do
          reports = SystemReport
          |> Ash.Query.filter(report_type == ^report_type)
          |> Ash.Query.sort(report_date: :desc)
          |> maybe_load_associations(options)
          |> Ash.read!()

          Logger.info("✅ [系统报表仓储] 按类型获取报表成功: #{length(reports)} 条")
          put_to_cache(cache_key, reports)
          {:ok, reports}
        rescue
          exception ->
            Logger.error("💥 [系统报表仓储] 按类型获取报表异常: #{inspect(exception)}")
            {:error, :get_by_type_failed}
        end
    end
  end

  @doc """
  按日期范围获取报表

  ## 参数
  - `start_date` - 开始日期
  - `end_date` - 结束日期
  - `options` - 选项参数

  ## 返回
  - `{:ok, reports}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_reports_by_date_range(start_date, end_date, options \\ []) do
    Logger.info("📊 [系统报表仓储] 按日期范围获取报表: #{start_date} - #{end_date}")

    try do
      reports = SystemReport
      |> Ash.Query.filter(report_date >= ^start_date and report_date <= ^end_date)
      |> Ash.Query.sort(report_date: :desc)
      |> maybe_load_associations(options)
      |> Ash.read!()

      Logger.info("✅ [系统报表仓储] 按日期范围获取报表成功: #{length(reports)} 条")
      {:ok, reports}
    rescue
      exception ->
        Logger.error("💥 [系统报表仓储] 按日期范围获取报表异常: #{inspect(exception)}")
        {:error, :get_by_date_range_failed}
    end
  end

  @doc """
  获取最新报表

  ## 参数
  - `report_type` - 报表类型
  - `options` - 选项参数

  ## 返回
  - `{:ok, report}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_latest_report(report_type, options \\ []) do
    Logger.info("📊 [系统报表仓储] 获取最新报表: #{report_type}")

    try do
      report = SystemReport
      |> Ash.Query.filter(report_type == ^report_type)
      |> Ash.Query.sort(report_date: :desc)
      |> Ash.Query.limit(1)
      |> maybe_load_associations(options)
      |> Ash.read!()
      |> List.first()

      case report do
        nil ->
          Logger.info("ℹ️ [系统报表仓储] 未找到最新报表: #{report_type}")
          {:error, :not_found}
        report ->
          Logger.info("✅ [系统报表仓储] 获取最新报表成功: #{report.id}")
          {:ok, report}
      end
    rescue
      exception ->
        Logger.error("💥 [系统报表仓储] 获取最新报表异常: #{inspect(exception)}")
        {:error, :get_latest_failed}
    end
  end

  # ==================== 私有辅助函数 ====================

  # 构建列表查询
  defp build_list_query(query, params) do
    query
    |> maybe_filter_by_report_type(params)
    |> maybe_filter_by_date_range(params)
    |> maybe_apply_search(params)
    |> maybe_apply_sort(params)
  end

  # 按报表类型过滤
  defp maybe_filter_by_report_type(query, %{report_type: report_type}) when report_type != "" do
    query |> Ash.Query.filter(report_type == ^report_type)
  end
  defp maybe_filter_by_report_type(query, _), do: query

  # 按日期范围过滤
  defp maybe_filter_by_date_range(query, %{start_date: start_date, end_date: end_date}) 
    when not is_nil(start_date) and not is_nil(end_date) do
    query |> Ash.Query.filter(report_date >= ^start_date and report_date <= ^end_date)
  end
  defp maybe_filter_by_date_range(query, _), do: query

  # 应用搜索
  defp maybe_apply_search(query, %{search: search}) when search != "" do
    query |> Ash.Query.filter(contains(report_type, ^search))
  end
  defp maybe_apply_search(query, _), do: query

  # 应用排序
  defp maybe_apply_sort(query, %{sort_by: sort_by, sort_order: sort_order}) do
    sort_field = String.to_atom(sort_by)
    sort_direction = String.to_atom(sort_order)
    query |> Ash.Query.sort([{sort_direction, sort_field}])
  end
  defp maybe_apply_sort(query, _) do
    query |> Ash.Query.sort(report_date: :desc)
  end

  # 加载关联数据
  defp maybe_load_associations(query, options) do
    case Keyword.get(options, :load_associations, false) do
      true -> query |> Ash.Query.load([])
      _ -> query
    end
  end

  # 缓存操作
  defp get_from_cache(_key), do: {:error, :not_found}
  defp put_to_cache(_key, _value), do: :ok
  defp clear_cache(), do: :ok
end
