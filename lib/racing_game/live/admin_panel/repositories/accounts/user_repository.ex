defmodule RacingGame.Live.AdminPanel.Repositories.Accounts.UserRepository do
  @moduledoc """
  用户账户数据访问仓储
  
  提供用户账户相关的数据访问抽象：
  - 用户基础信息管理
  - 用户身份认证
  - 代理关系管理
  - 复杂查询构建
  - 数据缓存管理
  """

  require Logger
  alias Cypridina.Accounts.{User, UserIdentity, AgentRelationship}
  alias RacingGame.Live.AdminPanel.Repositories.QueryBuilders.AccountsQueryBuilder
  require Ash.Query

  # 常量定义
  @cache_ttl 600  # 10分钟缓存
  @default_limit 20
  @max_limit 100

  # ============================================================================
  # 用户基础查询
  # ============================================================================

  @doc """
  根据ID获取用户

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - `{:ok, user}` - 成功
  - `{:error, :not_found}` - 用户不存在
  - `{:error, reason}` - 其他错误
  """
  def get_user_by_id(user_id, options \\ []) do
    Logger.debug("🔍 [用户仓储] 根据ID获取用户: #{user_id}")
    
    preload = Keyword.get(options, :preload, [])
    use_cache = Keyword.get(options, :use_cache, true)
    
    cache_key = build_cache_key("user", user_id, preload)
    
    if use_cache do
      case get_from_cache(cache_key) do
        {:ok, user} -> {:ok, user}
        :miss -> fetch_and_cache_user_by_id(user_id, preload, cache_key)
      end
    else
      fetch_user_by_id(user_id, preload)
    end
  end

  @doc """
  根据用户名获取用户

  ## 参数
  - `username` - 用户名
  - `options` - 选项

  ## 返回
  - `{:ok, user}` - 成功
  - `{:error, :not_found}` - 用户不存在
  - `{:error, reason}` - 其他错误
  """
  def get_user_by_username(username, options \\ []) do
    Logger.debug("🔍 [用户仓储] 根据用户名获取用户: #{username}")
    
    try do
      case User.get_by_username(username) do
        {:ok, user} -> 
          maybe_preload_user(user, options[:preload])
        {:error, %Ash.Error.Query.NotFound{}} -> 
          {:error, :not_found}
        {:error, error} ->
          Logger.error("❌ [用户仓储] 获取用户失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [用户仓储] 获取用户异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  根据数字ID获取用户

  ## 参数
  - `numeric_id` - 数字ID
  - `options` - 选项

  ## 返回
  - `{:ok, user}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_by_numeric_id(numeric_id, options \\ []) do
    Logger.debug("🔍 [用户仓储] 根据数字ID获取用户: #{numeric_id}")
    
    try do
      case User.get_by_numeric_id(numeric_id) do
        {:ok, user} -> 
          maybe_preload_user(user, options[:preload])
        {:error, %Ash.Error.Query.NotFound{}} -> 
          {:error, :not_found}
        {:error, error} ->
          Logger.error("❌ [用户仓储] 获取用户失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [用户仓储] 获取用户异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  分页查询用户列表

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, %{users: users, page_info: page_info}}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_users_paginated(params \\ %{}) do
    Logger.debug("📋 [用户仓储] 分页查询用户列表")
    
    try do
      normalized_params = normalize_list_params(params)
      
      with {:ok, query} <- AccountsQueryBuilder.build_user_list_query(normalized_params),
           {:ok, results} <- execute_paginated_query(query, normalized_params, User) do
        {:ok, %{users: results.items, page_info: results.page_info}}
      else
        {:error, reason} -> {:error, reason}
      end
    rescue
      error ->
        Logger.error("❌ [用户仓储] 分页查询异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  搜索用户

  ## 参数
  - `search_term` - 搜索词
  - `options` - 选项

  ## 返回
  - `{:ok, users}` - 成功
  - `{:error, reason}` - 失败
  """
  def search_users(search_term, options \\ []) do
    Logger.debug("🔍 [用户仓储] 搜索用户: #{search_term}")
    
    limit = Keyword.get(options, :limit, @default_limit)
    fields = Keyword.get(options, :fields, [:username, :email])
    
    try do
      query = AccountsQueryBuilder.build_user_search_query(search_term, fields)
      
      case query
           |> Ash.Query.limit(limit)
           |> Ash.read() do
        {:ok, users} -> {:ok, users}
        {:error, error} ->
          Logger.error("❌ [用户仓储] 搜索失败: #{inspect(error)}")
          {:error, :search_failed}
      end
    rescue
      error ->
        Logger.error("❌ [用户仓储] 搜索异常: #{inspect(error)}")
        {:error, :search_error}
    end
  end

  # ============================================================================
  # 用户创建和更新
  # ============================================================================

  @doc """
  创建用户

  ## 参数
  - `user_data` - 用户数据

  ## 返回
  - `{:ok, user}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_user(user_data) do
    Logger.info("➕ [用户仓储] 创建用户: #{user_data[:username]}")
    
    try do
      case User.register_with_username(user_data) do
        {:ok, user} ->
          clear_cache(:user)
          Logger.info("✅ [用户仓储] 用户创建成功: #{user.id}")
          {:ok, user}
        {:error, error} ->
          Logger.error("❌ [用户仓储] 创建用户失败: #{inspect(error)}")
          {:error, :create_failed}
      end
    rescue
      error ->
        Logger.error("❌ [用户仓储] 创建用户异常: #{inspect(error)}")
        {:error, :create_error}
    end
  end

  @doc """
  创建游客用户

  ## 参数
  - `guest_data` - 游客数据

  ## 返回
  - `{:ok, user}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_guest_user(guest_data \\ %{}) do
    Logger.info("👤 [用户仓储] 创建游客用户")
    
    try do
      case User.create_guest_user(guest_data) do
        {:ok, user} ->
          clear_cache(:user)
          Logger.info("✅ [用户仓储] 游客用户创建成功: #{user.id}")
          {:ok, user}
        {:error, error} ->
          Logger.error("❌ [用户仓储] 创建游客用户失败: #{inspect(error)}")
          {:error, :create_failed}
      end
    rescue
      error ->
        Logger.error("❌ [用户仓储] 创建游客用户异常: #{inspect(error)}")
        {:error, :create_error}
    end
  end

  @doc """
  更新用户

  ## 参数
  - `user_id` - 用户ID
  - `update_data` - 更新数据

  ## 返回
  - `{:ok, user}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_user(user_id, update_data) do
    Logger.info("✏️ [用户仓储] 更新用户: #{user_id}")
    
    with {:ok, user} <- get_user_by_id(user_id, use_cache: false),
         {:ok, updated_user} <- do_update_user(user, update_data) do
      
      clear_cache(:user, user_id)
      Logger.info("✅ [用户仓储] 用户更新成功: #{user_id}")
      {:ok, updated_user}
    else
      error -> error
    end
  end

  # ============================================================================
  # 代理关系管理
  # ============================================================================

  @doc """
  获取用户的代理关系

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - `{:ok, agent_relationships}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_agent_relationships(user_id, options \\ []) do
    Logger.debug("🔗 [用户仓储] 获取用户代理关系: #{user_id}")
    
    try do
      query = AgentRelationship
      |> Ash.Query.filter(user_id == ^user_id or agent_id == ^user_id)
      |> maybe_preload(options[:preload])
      |> apply_sort([inserted_at: :desc])
      
      case Ash.read(query) do
        {:ok, relationships} -> {:ok, relationships}
        {:error, error} ->
          Logger.error("❌ [用户仓储] 获取代理关系失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [用户仓储] 获取代理关系异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  @doc """
  获取用户的下级代理

  ## 参数
  - `agent_id` - 代理ID
  - `options` - 选项

  ## 返回
  - `{:ok, sub_agents}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_sub_agents(agent_id, options \\ []) do
    Logger.debug("👥 [用户仓储] 获取下级代理: #{agent_id}")
    
    try do
      query = AgentRelationship
      |> Ash.Query.filter(agent_id == ^agent_id)
      |> Ash.Query.load(:user)
      |> maybe_limit(options[:limit])
      |> apply_sort([inserted_at: :desc])
      
      case Ash.read(query) do
        {:ok, relationships} -> 
          users = Enum.map(relationships, & &1.user)
          {:ok, users}
        {:error, error} ->
          Logger.error("❌ [用户仓储] 获取下级代理失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [用户仓储] 获取下级代理异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # ============================================================================
  # 统计查询
  # ============================================================================

  @doc """
  获取用户统计信息

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, statistics}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_statistics(options \\ []) do
    Logger.debug("📊 [用户仓储] 获取用户统计")
    
    try do
      stats = %{
        total_users: get_total_users(),
        active_users: get_active_users_count(),
        guest_users: get_guest_users_count(),
        registered_users: get_registered_users_count(),
        agent_users: get_agent_users_count(),
        recent_registrations: get_recent_registrations_count(options[:days] || 7)
      }
      
      {:ok, stats}
    rescue
      error ->
        Logger.error("❌ [用户仓储] 获取统计异常: #{inspect(error)}")
        {:error, :statistics_error}
    end
  end

  # ============================================================================
  # 缓存管理
  # ============================================================================

  @doc """
  清除用户相关缓存

  ## 参数
  - `cache_type` - 缓存类型
  - `id` - ID（可选）
  """
  def clear_cache(cache_type, id \\ nil) do
    cache_patterns = case {cache_type, id} do
      {:user, nil} -> ["user:*"]
      {:user, user_id} -> ["user:#{user_id}:*"]
      {:all, _} -> ["user:*", "agent:*"]
    end
    
    Logger.debug("🧹 [用户仓储] 清除缓存: #{inspect(cache_patterns)}")
    Enum.each(cache_patterns, &clear_cache_pattern/1)
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 标准化列表参数
  defp normalize_list_params(params) do
    %{
      page: max(1, params[:page] || 1),
      limit: min(params[:limit] || @default_limit, @max_limit),
      search: String.trim(params[:search] || ""),
      filters: params[:filters] || %{},
      sort: params[:sort] || [inserted_at: :desc],
      preload: params[:preload] || []
    }
  end

  # 获取并缓存用户
  defp fetch_and_cache_user_by_id(user_id, preload, cache_key) do
    case fetch_user_by_id(user_id, preload) do
      {:ok, user} ->
        cache_put(cache_key, user, @cache_ttl)
        {:ok, user}
      error -> error
    end
  end

  # 获取用户
  defp fetch_user_by_id(user_id, preload) do
    try do
      case User
           |> maybe_preload(preload)
           |> Ash.get(user_id) do
        {:ok, user} -> {:ok, user}
        {:error, %Ash.Error.Query.NotFound{}} -> {:error, :not_found}
        {:error, error} ->
          Logger.error("❌ [用户仓储] 获取用户失败: #{inspect(error)}")
          {:error, :query_failed}
      end
    rescue
      error ->
        Logger.error("❌ [用户仓储] 获取用户异常: #{inspect(error)}")
        {:error, :query_error}
    end
  end

  # 可能预加载用户数据
  defp maybe_preload_user(user, nil), do: {:ok, user}
  defp maybe_preload_user(user, []), do: {:ok, user}
  defp maybe_preload_user(user, preload) when is_list(preload) do
    case Ash.load(user, preload) do
      {:ok, loaded_user} -> {:ok, loaded_user}
      error -> error
    end
  end

  # 执行更新用户
  defp do_update_user(user, update_data) do
    try do
      case user
           |> Ash.Changeset.for_update(:update, update_data)
           |> Ash.update() do
        {:ok, updated_user} -> {:ok, updated_user}
        {:error, error} ->
          Logger.error("❌ [用户仓储] 更新用户失败: #{inspect(error)}")
          {:error, :update_failed}
      end
    rescue
      error ->
        Logger.error("❌ [用户仓储] 更新用户异常: #{inspect(error)}")
        {:error, :update_error}
    end
  end

  # 执行分页查询
  defp execute_paginated_query(query, params, resource) do
    offset = (params.page - 1) * params.limit
    
    case query
         |> maybe_preload(params.preload)
         |> Ash.Query.page(count: true, limit: params.limit, offset: offset)
         |> Ash.read() do
      {:ok, %Ash.Page.Offset{results: items, count: total_count}} ->
        page_info = %{
          total_count: total_count,
          page: params.page,
          limit: params.limit,
          total_pages: ceil(total_count / params.limit)
        }
        {:ok, %{items: items, page_info: page_info}}
      {:error, error} ->
        Logger.error("❌ [用户仓储] 分页查询失败: #{inspect(error)}")
        {:error, :query_failed}
    end
  end

  # 可能预加载关联数据
  defp maybe_preload(query, []), do: query
  defp maybe_preload(query, preload) when is_list(preload) do
    Ash.Query.load(query, preload)
  end

  # 可能限制数量
  defp maybe_limit(query, nil), do: query
  defp maybe_limit(query, limit) when is_integer(limit) and limit > 0 do
    Ash.Query.limit(query, limit)
  end
  defp maybe_limit(query, _), do: query

  # 应用排序
  defp apply_sort(query, sort) when is_list(sort) do
    Ash.Query.sort(query, sort)
  end
  defp apply_sort(query, _), do: query

  # 统计函数（简化实现）
  defp get_total_users do
    case User |> Ash.Query.aggregate(:count, :id) |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_active_users_count do
    # 假设有 is_active 字段
    case User 
         |> Ash.Query.filter(confirmed_at != nil)
         |> Ash.Query.aggregate(:count, :id) 
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_guest_users_count do
    case User 
         |> Ash.Query.filter(username == nil)
         |> Ash.Query.aggregate(:count, :id) 
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_registered_users_count do
    case User 
         |> Ash.Query.filter(username != nil)
         |> Ash.Query.aggregate(:count, :id) 
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_agent_users_count do
    case User 
         |> Ash.Query.filter(agent_level >= 0)
         |> Ash.Query.aggregate(:count, :id) 
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  defp get_recent_registrations_count(days) do
    date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)
    
    case User 
         |> Ash.Query.filter(inserted_at >= ^date_threshold)
         |> Ash.Query.aggregate(:count, :id) 
         |> Ash.read() do
      {:ok, [%{count: count}]} -> count
      _ -> 0
    end
  end

  # 构建缓存键
  defp build_cache_key(prefix, id, extra \\ nil) do
    case extra do
      nil -> "#{prefix}:#{id}"
      extra -> "#{prefix}:#{id}:#{:erlang.phash2(extra)}"
    end
  end

  # 缓存操作（需要实现具体的缓存后端）
  defp get_from_cache(_key), do: :miss
  defp cache_put(_key, _value, _ttl), do: :ok
  defp clear_cache_pattern(_pattern), do: :ok
end
