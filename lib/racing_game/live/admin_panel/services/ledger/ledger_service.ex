defmodule RacingGame.Live.AdminPanel.Services.Ledger.LedgerService do
  @moduledoc """
  账本业务逻辑服务
  
  提供账本系统的业务逻辑协调：
  - 账户管理业务逻辑
  - 转账业务流程
  - 余额管理和验证
  - 财务报表和统计
  """

  require Logger
  alias RacingGame.Live.AdminPanel.Repositories.Ledger.{
    AccountRepository,
    TransferRepository,
    BalanceRepository
  }
  alias RacingGame.Live.AdminPanel.Repositories.QueryBuilders.LedgerQueryBuilder
  alias Phoenix.PubSub

  # 常量定义
  @pubsub_topic "ledger_updates"
  @default_currency :XAA

  # ============================================================================
  # 账户管理业务逻辑
  # ============================================================================

  @doc """
  创建用户账户

  ## 参数
  - `user_id` - 用户ID
  - `currency` - 货币类型
  - `options` - 选项

  ## 返回
  - `{:ok, account}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_user_account(user_id, currency \\ @default_currency, options \\ []) do
    Logger.info("👤 [账本服务] 创建用户账户: #{user_id} - #{currency}")
    
    account_data = %{
      identifier: "user:#{currency}:#{user_id}",
      account_type: :user,
      currency: currency,
      description: options[:description] || "用户账户 - #{user_id}",
      is_active: true
    }
    
    with {:ok, account} <- AccountRepository.create_account(account_data),
         {:ok, _balance} <- create_initial_balance(account.id, currency),
         :ok <- broadcast_account_created(account) do
      
      Logger.info("✅ [账本服务] 用户账户创建成功: #{account.id}")
      {:ok, account}
    else
      {:error, reason} = error ->
        Logger.error("❌ [账本服务] 创建用户账户失败: #{inspect(reason)}")
        error
    end
  end

  @doc """
  获取用户账户信息

  ## 参数
  - `user_id` - 用户ID
  - `currency` - 货币类型
  - `options` - 选项

  ## 返回
  - `{:ok, account_info}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_account_info(user_id, currency \\ @default_currency, options \\ []) do
    Logger.debug("👤 [账本服务] 获取用户账户信息: #{user_id} - #{currency}")
    
    with {:ok, account} <- AccountRepository.get_user_account(user_id, currency, preload: [:balance_as_of]),
         {:ok, balance} <- AccountRepository.get_user_balance(user_id, currency),
         {:ok, statistics} <- get_account_statistics(account.id, options) do
      
      account_info = %{
        account: account,
        current_balance: balance,
        statistics: statistics
      }
      
      {:ok, account_info}
    else
      error -> error
    end
  end

  @doc """
  激活/停用账户

  ## 参数
  - `account_id` - 账户ID
  - `is_active` - 是否激活
  - `reason` - 原因

  ## 返回
  - `{:ok, account}` - 成功
  - `{:error, reason}` - 失败
  """
  def toggle_account_status(account_id, is_active, reason \\ nil) do
    Logger.info("🔄 [账本服务] 切换账户状态: #{account_id} -> #{is_active}")
    
    with {:ok, account} <- AccountRepository.get_account_by_id(account_id),
         :ok <- validate_account_status_change(account, is_active),
         {:ok, updated_account} <- AccountRepository.update_account(account_id, %{is_active: is_active, status_reason: reason}),
         :ok <- broadcast_account_status_changed(updated_account, is_active, reason) do
      
      Logger.info("✅ [账本服务] 账户状态切换成功: #{account_id}")
      {:ok, updated_account}
    else
      error -> error
    end
  end

  # ============================================================================
  # 转账业务逻辑
  # ============================================================================

  @doc """
  执行转账

  ## 参数
  - `from_user_id` - 转出用户ID
  - `to_user_id` - 转入用户ID
  - `amount` - 转账金额
  - `currency` - 货币类型
  - `options` - 选项

  ## 返回
  - `{:ok, transfer}` - 成功
  - `{:error, reason}` - 失败
  """
  def transfer_between_users(from_user_id, to_user_id, amount, currency \\ @default_currency, options \\ []) do
    Logger.info("💸 [账本服务] 用户间转账: #{from_user_id} -> #{to_user_id}, #{amount} #{currency}")
    
    with {:ok, from_account} <- AccountRepository.get_user_account(from_user_id, currency),
         {:ok, to_account} <- AccountRepository.get_user_account(to_user_id, currency),
         :ok <- validate_transfer(from_account, to_account, amount, currency),
         {:ok, transfer} <- execute_transfer(from_account.id, to_account.id, amount, currency, options),
         :ok <- broadcast_transfer_completed(transfer) do
      
      Logger.info("✅ [账本服务] 转账成功: #{transfer.id}")
      {:ok, transfer}
    else
      {:error, reason} = error ->
        Logger.error("❌ [账本服务] 转账失败: #{inspect(reason)}")
        error
    end
  end

  @doc """
  批量转账

  ## 参数
  - `transfers_data` - 转账数据列表

  ## 返回
  - `{:ok, results}` - 成功
  - `{:error, reason}` - 失败
  """
  def batch_transfer(transfers_data) do
    Logger.info("📦 [账本服务] 批量转账: #{length(transfers_data)} 笔")
    
    results = Enum.map(transfers_data, fn transfer_data ->
      transfer_between_users(
        transfer_data.from_user_id,
        transfer_data.to_user_id,
        transfer_data.amount,
        transfer_data[:currency] || @default_currency,
        transfer_data[:options] || []
      )
    end)
    
    case Enum.split_with(results, &match?({:ok, _}, &1)) do
      {successes, []} ->
        transfers = Enum.map(successes, fn {:ok, transfer} -> transfer end)
        Logger.info("✅ [账本服务] 批量转账全部成功: #{length(transfers)} 笔")
        {:ok, %{successes: transfers, failures: []}}
      {successes, failures} ->
        Logger.warn("⚠️ [账本服务] 批量转账部分失败: 成功 #{length(successes)}, 失败 #{length(failures)}")
        {:ok, %{successes: successes, failures: failures}}
    end
  end

  @doc """
  取消转账

  ## 参数
  - `transfer_id` - 转账ID
  - `reason` - 取消原因
  - `operator_id` - 操作员ID

  ## 返回
  - `{:ok, transfer}` - 成功
  - `{:error, reason}` - 失败
  """
  def cancel_transfer(transfer_id, reason, operator_id \\ nil) do
    Logger.info("❌ [账本服务] 取消转账: #{transfer_id} - #{reason}")
    
    with {:ok, transfer} <- TransferRepository.get_transfer_by_id(transfer_id),
         :ok <- validate_transfer_cancellation(transfer),
         {:ok, cancelled_transfer} <- TransferRepository.cancel_transfer(transfer_id, reason),
         :ok <- reverse_transfer_effects(cancelled_transfer),
         :ok <- broadcast_transfer_cancelled(cancelled_transfer, operator_id) do
      
      Logger.info("✅ [账本服务] 转账取消成功: #{transfer_id}")
      {:ok, cancelled_transfer}
    else
      error -> error
    end
  end

  # ============================================================================
  # 余额管理业务逻辑
  # ============================================================================

  @doc """
  调整用户余额

  ## 参数
  - `user_id` - 用户ID
  - `amount` - 调整金额（正数为增加，负数为减少）
  - `currency` - 货币类型
  - `reason` - 调整原因
  - `options` - 选项

  ## 返回
  - `{:ok, balance}` - 成功
  - `{:error, reason}` - 失败
  """
  def adjust_user_balance(user_id, amount, currency \\ @default_currency, reason, options \\ []) do
    Logger.info("⚖️ [账本服务] 调整用户余额: #{user_id}, #{amount} #{currency} - #{reason}")
    
    with {:ok, account} <- AccountRepository.get_user_account(user_id, currency),
         {:ok, current_balance} <- AccountRepository.get_user_balance(user_id, currency),
         :ok <- validate_balance_adjustment(current_balance, amount),
         {:ok, new_balance} <- calculate_new_balance(current_balance, amount),
         {:ok, balance_record} <- create_balance_record(account.id, new_balance, reason, options),
         :ok <- broadcast_balance_adjusted(account, amount, reason) do
      
      Logger.info("✅ [账本服务] 余额调整成功: #{user_id}")
      {:ok, balance_record}
    else
      error -> error
    end
  end

  @doc """
  冻结/解冻用户余额

  ## 参数
  - `user_id` - 用户ID
  - `amount` - 冻结金额
  - `currency` - 货币类型
  - `action` - 操作类型 (:freeze | :unfreeze)
  - `reason` - 原因

  ## 返回
  - `{:ok, result}` - 成功
  - `{:error, reason}` - 失败
  """
  def freeze_user_balance(user_id, amount, currency \\ @default_currency, action, reason) do
    Logger.info("🧊 [账本服务] #{action}用户余额: #{user_id}, #{amount} #{currency} - #{reason}")
    
    with {:ok, account} <- AccountRepository.get_user_account(user_id, currency),
         {:ok, current_balance} <- AccountRepository.get_user_balance(user_id, currency),
         :ok <- validate_freeze_operation(current_balance, amount, action),
         {:ok, result} <- execute_freeze_operation(account, amount, action, reason),
         :ok <- broadcast_balance_frozen(account, amount, action, reason) do
      
      Logger.info("✅ [账本服务] 余额#{action}成功: #{user_id}")
      {:ok, result}
    else
      error -> error
    end
  end

  # ============================================================================
  # 统计和报表业务逻辑
  # ============================================================================

  @doc """
  获取用户财务报表

  ## 参数
  - `user_id` - 用户ID
  - `currency` - 货币类型
  - `options` - 选项

  ## 返回
  - `{:ok, report}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_user_financial_report(user_id, currency \\ @default_currency, options \\ []) do
    Logger.debug("📊 [账本服务] 获取用户财务报表: #{user_id} - #{currency}")
    
    with {:ok, queries} <- LedgerQueryBuilder.build_user_financial_query(user_id, currency, options),
         {:ok, account_info} <- get_user_account_info(user_id, currency, options),
         {:ok, transfer_stats} <- get_user_transfer_statistics(user_id, currency, options),
         {:ok, balance_trend} <- get_user_balance_trend(user_id, currency, options) do
      
      report = %{
        user_id: user_id,
        currency: currency,
        account_info: account_info,
        transfer_statistics: transfer_stats,
        balance_trend: balance_trend,
        generated_at: DateTime.utc_now()
      }
      
      {:ok, report}
    else
      error -> error
    end
  end

  @doc """
  获取系统财务统计

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, statistics}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_system_financial_statistics(options \\ []) do
    Logger.debug("📊 [账本服务] 获取系统财务统计")
    
    with {:ok, account_stats} <- AccountRepository.get_account_statistics(options),
         {:ok, transfer_stats} <- TransferRepository.get_transfer_statistics(options),
         {:ok, balance_stats} <- BalanceRepository.get_balance_statistics(options) do
      
      statistics = %{
        accounts: account_stats,
        transfers: transfer_stats,
        balances: balance_stats,
        summary: calculate_system_summary(account_stats, transfer_stats, balance_stats),
        generated_at: DateTime.utc_now()
      }
      
      {:ok, statistics}
    else
      error -> error
    end
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 创建初始余额
  defp create_initial_balance(account_id, currency) do
    balance_data = %{
      account_id: account_id,
      balance: Money.new(0, currency),
      description: "初始余额"
    }
    
    BalanceRepository.create_balance(balance_data)
  end

  # 验证账户状态变更
  defp validate_account_status_change(account, is_active) do
    cond do
      account.is_active == is_active ->
        {:error, :status_unchanged}
      not is_active and has_pending_transfers?(account.id) ->
        {:error, :has_pending_transfers}
      true ->
        :ok
    end
  end

  # 验证转账
  defp validate_transfer(from_account, to_account, amount, currency) do
    cond do
      not from_account.is_active ->
        {:error, :from_account_inactive}
      not to_account.is_active ->
        {:error, :to_account_inactive}
      from_account.currency != currency ->
        {:error, :currency_mismatch}
      to_account.currency != currency ->
        {:error, :currency_mismatch}
      Money.negative?(amount) ->
        {:error, :invalid_amount}
      true ->
        validate_sufficient_balance(from_account.id, amount, currency)
    end
  end

  # 验证余额充足
  defp validate_sufficient_balance(account_id, amount, currency) do
    case AccountRepository.get_account_balance(account_id) do
      {:ok, balance} ->
        if Money.compare(balance, amount) >= 0 do
          :ok
        else
          {:error, :insufficient_balance}
        end
      error -> error
    end
  end

  # 执行转账
  defp execute_transfer(from_account_id, to_account_id, amount, currency, options) do
    transfer_data = %{
      from_account_id: from_account_id,
      to_account_id: to_account_id,
      amount: amount,
      currency: currency,
      description: options[:description] || "用户转账",
      reference: options[:reference] || generate_transfer_reference()
    }
    
    TransferRepository.create_transfer(transfer_data)
  end

  # 验证转账取消
  defp validate_transfer_cancellation(transfer) do
    case transfer.status do
      :pending -> :ok
      :processing -> :ok
      :completed -> {:error, :transfer_completed}
      :cancelled -> {:error, :already_cancelled}
      :failed -> {:error, :transfer_failed}
      _ -> {:error, :invalid_status}
    end
  end

  # 反转转账效果
  defp reverse_transfer_effects(transfer) do
    # 这里需要实现转账反转逻辑
    # 简化实现
    Logger.info("🔄 [账本服务] 反转转账效果: #{transfer.id}")
    :ok
  end

  # 验证余额调整
  defp validate_balance_adjustment(current_balance, adjustment_amount) do
    if Money.negative?(adjustment_amount) do
      new_balance = Money.add(current_balance, adjustment_amount)
      if Money.negative?(new_balance) do
        {:error, :insufficient_balance}
      else
        :ok
      end
    else
      :ok
    end
  end

  # 计算新余额
  defp calculate_new_balance(current_balance, adjustment_amount) do
    new_balance = Money.add(current_balance, adjustment_amount)
    {:ok, new_balance}
  end

  # 创建余额记录
  defp create_balance_record(account_id, balance, reason, options) do
    balance_data = %{
      account_id: account_id,
      balance: balance,
      description: reason,
      metadata: options[:metadata] || %{}
    }
    
    BalanceRepository.create_balance(balance_data)
  end

  # 验证冻结操作
  defp validate_freeze_operation(current_balance, amount, action) do
    case action do
      :freeze ->
        if Money.compare(current_balance, amount) >= 0 do
          :ok
        else
          {:error, :insufficient_balance}
        end
      :unfreeze ->
        # 这里需要检查冻结余额
        :ok
      _ ->
        {:error, :invalid_action}
    end
  end

  # 执行冻结操作
  defp execute_freeze_operation(account, amount, action, reason) do
    # 这里需要实现具体的冻结逻辑
    # 简化实现
    Logger.info("🧊 [账本服务] 执行冻结操作: #{account.id}, #{action}")
    {:ok, %{account_id: account.id, amount: amount, action: action, reason: reason}}
  end

  # 获取账户统计
  defp get_account_statistics(account_id, options) do
    TransferRepository.get_account_transfer_statistics(account_id, options)
  end

  # 获取用户转账统计
  defp get_user_transfer_statistics(user_id, currency, options) do
    case AccountRepository.get_user_account(user_id, currency) do
      {:ok, account} -> TransferRepository.get_account_transfer_statistics(account.id, options)
      error -> error
    end
  end

  # 获取用户余额趋势
  defp get_user_balance_trend(user_id, currency, options) do
    case AccountRepository.get_user_account(user_id, currency) do
      {:ok, account} -> BalanceRepository.get_balance_trend(account.id, options)
      error -> error
    end
  end

  # 计算系统摘要
  defp calculate_system_summary(account_stats, transfer_stats, balance_stats) do
    %{
      total_accounts: account_stats.total_accounts,
      total_transfers: transfer_stats.total_transfers,
      total_volume: transfer_stats.total_volume,
      total_value: balance_stats.total_value
    }
  end

  # 辅助函数
  defp has_pending_transfers?(account_id) do
    # 简化实现
    false
  end

  defp generate_transfer_reference do
    "TXN_#{System.unique_integer([:positive])}_#{DateTime.utc_now() |> DateTime.to_unix()}"
  end

  # ============================================================================
  # 事件广播
  # ============================================================================

  defp broadcast_account_created(account) do
    PubSub.broadcast(RacingGame.PubSub, @pubsub_topic, {:account_created, account})
  end

  defp broadcast_account_status_changed(account, is_active, reason) do
    PubSub.broadcast(RacingGame.PubSub, @pubsub_topic, {:account_status_changed, account, is_active, reason})
  end

  defp broadcast_transfer_completed(transfer) do
    PubSub.broadcast(RacingGame.PubSub, @pubsub_topic, {:transfer_completed, transfer})
  end

  defp broadcast_transfer_cancelled(transfer, operator_id) do
    PubSub.broadcast(RacingGame.PubSub, @pubsub_topic, {:transfer_cancelled, transfer, operator_id})
  end

  defp broadcast_balance_adjusted(account, amount, reason) do
    PubSub.broadcast(RacingGame.PubSub, @pubsub_topic, {:balance_adjusted, account, amount, reason})
  end

  defp broadcast_balance_frozen(account, amount, action, reason) do
    PubSub.broadcast(RacingGame.PubSub, @pubsub_topic, {:balance_frozen, account, amount, action, reason})
  end
end
