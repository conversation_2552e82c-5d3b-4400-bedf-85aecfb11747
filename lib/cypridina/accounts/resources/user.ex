defmodule <PERSON><PERSON><PERSON><PERSON>.Accounts.User do
  use Ash.Resource,
    otp_app: :cyprid<PERSON>,
    domain: Cyp<PERSON>ina.Accounts,
    authorizers: [Ash.Policy.Authorizer],
    extensions: [AshAuthentication, AshPaperTrail.Resource],
    data_layer: AshPostgres.DataLayer,
    notifiers: [Ash.Notifier.PubSub]

  require Logger

  authentication do
    add_ons do
      log_out_everywhere do
        apply_on_password_change? true
      end

      confirmation :confirm_new_user do
        monitor_fields [:email]
        confirm_on_create? true
        confirm_on_update? false
        require_interaction? true
        confirmed_at_field :confirmed_at

        auto_confirm_actions [
          :sign_in_with_magic_link,
          :reset_password_with_token,
          :register_with_username
        ]

        sender Cy<PERSON><PERSON><PERSON>.Accounts.User.Senders.SendNewUserConfirmationEmail
      end
    end

    tokens do
      enabled? true
      token_resource Cypridina.Accounts.Token
      signing_secret Cy<PERSON>ridina.Secrets
      store_all_tokens? true
      require_token_presence_for_authentication? true
    end

    strategies do
      password :username do
        identity_field :username
        hashed_password_field :hashed_password
        sign_in_tokens_enabled? true
        sign_in_action_name :sign_in_with_username

        resettable do
          sender <PERSON><PERSON><PERSON><PERSON>.Accounts.User.Senders.SendPasswordResetEmail
          # these configurations will be the default in a future release
          password_reset_action_name :reset_password_with_token
          # request_password_reset_action_name :request_password_reset_token
        end
      end

      api_key :api_key do
        api_key_relationship :valid_api_keys
        api_key_hash_attribute :api_key_hash
      end

      # password :email do
      #   identity_field :email
      #   hashed_password_field :hashed_password
      #   sign_in_tokens_enabled? true
      #   sign_in_action_name :sign_in_with_email

      #   resettable do
      #     sender Cypridina.Accounts.User.Senders.SendPasswordResetEmail
      #     #   # these configurations will be the default in a future release
      #     password_reset_action_name :reset_password_with_token
      #     request_password_reset_action_name :request_password_reset_token
      #   end
      # end
    end
  end

  postgres do
    table "users"
    repo Cypridina.Repo
  end

  paper_trail do
    # default is :uuid
    primary_key_type(:uuid_v7)

    # default is :snapshot
    change_tracking_mode(:changes_only)

    # default is false
    store_action_name?(true)

    # the primary keys are always ignored
    ignore_attributes([:inserted_at, :updated_at])

    # default is []
    ignore_actions([:destroy])
  end

  # 定义默认加载的字段
  resource do
  end

  code_interface do
    define :get_by_username, action: :read, get_by_identity: :unique_username
    define :get_by_numeric_id, action: :read, get_by_identity: :unique_numeric_id
    define :get_by_client_uniq_id, action: :read, get_by_identity: :unique_client_uniq_id
    define :sign_in_with_token
    define :create_guest_user, action: :create_guest_user
    define :register_with_username, action: :register_with_username
    define :read
  end

  actions do
    defaults [:read]

    read :get_by_subject do
      description "通过JWT中的subject声明获取用户"
      argument :subject, :string, allow_nil?: false
      get? true
      prepare AshAuthentication.Preparations.FilterBySubject
    end

    update :change_password do
      # Use this action to allow users to change their password by providing
      # their current password and a new password.

      require_atomic? false
      accept []
      argument :current_password, :string, sensitive?: true, allow_nil?: false

      argument :password, :string,
        sensitive?: true,
        allow_nil?: false,
        constraints: [min_length: 6]

      argument :password_confirmation, :string, sensitive?: true, allow_nil?: false

      validate confirm(:password, :password_confirmation)

      validate {AshAuthentication.Strategy.Password.PasswordValidation,
                strategy_name: :username, password_argument: :current_password}

      change {AshAuthentication.Strategy.Password.HashPasswordChange, strategy_name: :username}
    end

    read :sign_in_with_email do
      description "尝试使用邮箱和密码登录"
      get? true

      argument :email, :ci_string do
        description "用于检索用户的邮箱"
        allow_nil? false
      end

      argument :password, :string do
        description "用于验证匹配用户的密码"
        allow_nil? false
        sensitive? true
      end

      # validates the provided email and password and generates a token
      prepare AshAuthentication.Strategy.Password.SignInPreparation

      metadata :token, :string do
        description "可用于认证用户的JWT令牌"
        allow_nil? false
      end
    end

    # read :sign_in_with_username do
    #   description "使用用户名和密码登录"
    #   get? true

    #   argument :username, :ci_string do
    #     description "用于检索用户的用户名"
    #     allow_nil? false
    #   end

    #   argument :password, :string do
    #     description "用于验证的密码"
    #     allow_nil? false
    #     sensitive? true
    #   end

    #   # 明确指定策略名称为 :username
    #   prepare {AshAuthentication.Strategy.Password.SignInPreparation, strategy_name: :username}
    #   # prepare build(load: [:is_admin, :is_agent, :role_name, :points])

    #   metadata :token, :string do
    #     description "用于认证的JWT令牌"
    #     allow_nil? false
    #   end
    # end

    read :sign_in_with_token do
      # In the generated sign in components, we validate the
      # email and password directly in the LiveView
      # and generate a short-lived token that can be used to sign in over
      # a standard controller action, exchanging it for a standard token.
      # This action performs that exchange. If you do not use the generated
      # liveviews, you may remove this action, and set
      # `sign_in_tokens_enabled? false` in the password strategy.

      description "尝试使用短期登录令牌登录"
      get? true

      argument :token, :string do
        description "短期登录令牌"
        allow_nil? false
        sensitive? true
      end

      # validates the provided sign in token and generates a token
      prepare AshAuthentication.Strategy.Password.SignInWithTokenPreparation

      metadata :token, :string do
        description "可用于认证用户的JWT令牌"
        allow_nil? false
      end
    end

    create :register_with_username do
      description "使用用户名和密码注册新用户"

      accept [
        :email,
        :confirmed_at,
        :permission_level,
        :agent_level
      ]

      # 添加用户名参数
      argument :username, :ci_string do
        allow_nil? false

        constraints min_length: 3,
                    max_length: 20,
                    # 只允许字母、数字和下划线
                    match: "^[a-zA-Z0-9_]+$"
      end

      argument :password, :string do
        description "用户密码"
        allow_nil? false
        constraints min_length: 6
        sensitive? true
      end

      argument :password_confirmation, :string do
        description "确认密码"
        allow_nil? false
        sensitive? true
      end

      argument :asset, :map do
        description "用户资产初始化数据"
        allow_nil? true
        default %{points: 0}
      end



      # 设置属性
      change set_attribute(:username, arg(:username))

      # 生成数字ID - 使用 force_change_attribute 来避免验证后修改的错误
      change before_action(fn changeset, _context ->
               uuid = Ash.Changeset.get_attribute(changeset, :id)
               numeric_id = generate_short_numeric_id_from_uuid(uuid)

               changeset
               |> Ash.Changeset.force_change_attribute(:numeric_id, numeric_id)
             end)

      # 哈希密码
      change AshAuthentication.Strategy.Password.HashPasswordChange

      # 生成认证令牌
      change AshAuthentication.GenerateTokenChange

      # 验证密码确认
      validate AshAuthentication.Strategy.Password.PasswordConfirmationValidation

      # 创建用户积分账户
      change after_action(fn changeset, user, _context ->
               case Cypridina.Ledger.Account.open(%{
                 identifier: "user:XAA:#{user.id}",
                 currency: :XAA,
                 account_type: :user,
                 user_id: user.id,
                 description: "用户积分账户"
               }) do
                 {:ok, account} ->
                   {:ok, %{user | point_account: account}}
                 {:error, error} ->
                   {:error, error}
               end
             end)

      metadata :token, :string do
        description "用于认证的JWT令牌"
        allow_nil? false
      end
    end

    create :create_guest_user do
      description "创建游客用户（快速登录）"
      accept [:client_uniq_id]

      # 生成游客用户名和邮箱
      change before_action(fn changeset, _context ->
               # 生成数字ID
               uuid = Ash.Changeset.get_attribute(changeset, :id)
               numeric_id = generate_short_numeric_id_from_uuid(uuid)
               guest_username = "Guest#{numeric_id}"
               guest_email = "guest#{numeric_id}@guest.local"

               changeset
               |> Ash.Changeset.force_change_attribute(:numeric_id, numeric_id)
               |> Ash.Changeset.force_change_attribute(:username, guest_username)
               |> Ash.Changeset.force_change_attribute(:email, guest_email)
               |> Ash.Changeset.force_change_attribute(
                 :hashed_password,
                 Bcrypt.hash_pwd_salt("guest_password_#{numeric_id}")
               )
             end)

      # 创建游客积分账户并添加初始积分
      change after_action(fn changeset, user, _context ->
               case Cypridina.Ledger.Account.open(%{
                 identifier: "user:XAA:#{user.id}",
                 currency: :XAA,
                 account_type: :user,
                 user_id: user.id,
                 description: "游客积分账户"
               }) do
                 {:ok, account} ->
                   # 创建用户对象，包含积分账户
                   user_with_account = %{user | point_account: account}

                   # 给新用户添加 50000 初始积分
                   Cypridina.Ledger.add_user_points(user_with_account, 50000, [
                     transaction_type: :manual_add,
                     description: "新用户注册奖励",
                     metadata: %{
                       "operation" => "new_user_bonus",
                       "bonus_type" => "registration",
                       "amount" => 50000
                     }
                   ])

                   {:ok, user_with_account}

                 {:error, error} ->
                   {:error, error}
               end
             end)

      metadata :token, :string do
        description "用于认证的JWT令牌"
        allow_nil? false
      end
    end

    action :request_password_reset_token do
      description "如果用户存在，向其发送密码重置说明"

      argument :email, :ci_string do
        allow_nil? false
      end

      # creates a reset token and invokes the relevant senders
      run {AshAuthentication.Strategy.Password.RequestPasswordReset, action: :get_by_username}
    end

    read :get_by_email do
      description "通过邮箱查找用户"
      get? true

      argument :email, :ci_string do
        allow_nil? false
      end

      filter expr(email == ^arg(:email))
    end

    # 添加通过用户名查询用户的操作
    read :get_by_username do
      description "查找指定用户名的用户"
      argument :username, :ci_string, allow_nil?: false
      get? true
      filter expr(username == ^arg(:username))
    end

    # 添加通过客户端唯一标识查询用户的操作
    read :get_by_client_uniq_id do
      description "通过客户端唯一标识查找用户"
      argument :client_uniq_id, :string, allow_nil?: false
      get? true
      filter expr(client_uniq_id == ^arg(:client_uniq_id))

      # 使用自定义preparation生成token
      prepare Cypridina.Accounts.Preparations.GenerateTokenPreparation

      metadata :token, :string do
        description "可用于认证用户的JWT令牌"
        allow_nil? false
      end
    end

    update :reset_password_with_token do
      argument :reset_token, :string do
        allow_nil? false
        sensitive? true
      end

      argument :password, :string do
        description "用户的明文密码建议"
        allow_nil? false
        constraints min_length: 8
        sensitive? true
      end

      argument :password_confirmation, :string do
        description "用户的明文密码建议（再次输入）"
        allow_nil? false
        sensitive? true
      end

      # validates the provided reset token
      validate AshAuthentication.Strategy.Password.ResetTokenValidation

      # validates that the password matches the confirmation
      validate AshAuthentication.Strategy.Password.PasswordConfirmationValidation

      # Hashes the provided password
      change AshAuthentication.Strategy.Password.HashPasswordChange

      # Generates an authentication token for the user
      change AshAuthentication.GenerateTokenChange
    end

    update :update_permission_level do
      description "更新用户权限级别"
      accept [:permission_level]

      argument :permission_level, :integer do
        allow_nil? false
        constraints min: 0, max: 2
      end

      change set_attribute(:permission_level, arg(:permission_level))
    end

    update :update_agent_level do
      description "更新用户代理等级"
      accept [:agent_level]

      argument :agent_level, :integer do
        allow_nil? false
        constraints min: -1
      end

      change set_attribute(:agent_level, arg(:agent_level))
    end

    update :admin_reset_password do
      description "管理员重置用户密码（无需当前密码验证）"
      require_atomic? false
      accept []

      argument :password, :string,
        sensitive?: true,
        allow_nil?: false,
        constraints: [min_length: 6]

      argument :password_confirmation, :string, sensitive?: true, allow_nil?: false

      validate confirm(:password, :password_confirmation)

      change fn changeset, _context ->
        case Ash.Changeset.get_argument(changeset, :password) do
          nil ->
            changeset

          password ->
            hashed_password = Bcrypt.hash_pwd_salt(password)
            Ash.Changeset.change_attribute(changeset, :hashed_password, hashed_password)
        end
      end
    end

    read :get_admins do
      description "获取所有管理员用户"
      filter expr(permission_level >= 1)
    end

    read :get_super_admins do
      description "获取所有超级管理员用户"
      filter expr(permission_level == 2)
    end

    read :sign_in_with_api_key do
      argument :api_key, :string, allow_nil?: false
      prepare AshAuthentication.Strategy.ApiKey.SignInPreparation
    end
  end

  policies do
    bypass AshAuthentication.Checks.AshAuthenticationInteraction do
      authorize_if always()
    end

    # # 允许任何人创建用户（包括代理创建下线）
    # policy action_type(:create) do
    #   authorize_if always()
    # end

    # # 允许用户读取自己的信息
    # policy action_type(:read) do
    #   authorize_if expr(id == ^actor(:id))
    # end

    # # 允许用户更新自己的信息
    # policy action_type(:update) do
    #   authorize_if expr(id == ^actor(:id))
    # end

    # 默认拒绝其他操作
    policy always() do
      authorize_if always()
    end
  end

  pub_sub do
    module CypridinaWeb.Endpoint
    prefix "user"

    # 发布用户变化事件，使用 publish_all 来发布到多个主题
    # 这将发布到 "user" 和 "user:id" 主题
    publish_all :create, [[:id, nil]]
    publish_all :update, [[:id, nil]]
    publish_all :destroy, [[:id, nil]]
  end

  preparations do
    prepare build(load: [:is_admin, :is_agent, :role_name, :point_account])
  end

  attributes do
    uuid_primary_key :id

    # 添加数字ID属性
    attribute :numeric_id, :integer do
      public? true
      allow_nil? false
      description "用户的可读数字ID"
    end

    # 添加用户名属性
    attribute :username, :ci_string do
      public? true
      allow_nil? true
      description "用户名，用于登录和显示"
    end

    # 添加客户端唯一标识字段
    attribute :client_uniq_id, :string do
      public? true
      allow_nil? true
      description "客户端唯一标识，用于快速登录"
    end

    attribute :email, :ci_string do
      allow_nil? true
      public? true
    end

    attribute :hashed_password, :string do
      allow_nil? false
      sensitive? true
    end

    attribute :confirmed_at, :utc_datetime_usec

    # 代理系统相关字段
    attribute :agent_level, :integer do
      allow_nil? false
      default -1
      public? true
      description "代理等级：-1=不是代理，0=根代理，>0=下级代理"
      constraints min: -1
    end



    # 用户权限级别字段
    attribute :permission_level, :integer do
      allow_nil? false
      default 0
      public? true
      description "权限级别: 0-普通用户, 1-管理员, 2-超级管理员"
      constraints min: 0, max: 2
    end

    timestamps()
  end

  relationships do
    # 添加与账本账户的关系（复式记账系统）
    has_one :point_account, Cypridina.Ledger.Account do
      public? true
      source_attribute :id
      destination_attribute :user_id
      filter expr(account_type == :user and currency == :XAA)
    end

    # 代理关系
    has_many :agent_relationships, Cypridina.Accounts.AgentRelationship do
      public? true
      source_attribute :id
      destination_attribute :subordinate_id
    end

    has_many :subordinate_relationships, Cypridina.Accounts.AgentRelationship do
      public? true
      source_attribute :id
      destination_attribute :agent_id
    end



    has_many :valid_api_keys, Cypridina.Accounts.ApiKey do
      filter expr(valid)
    end
  end

  calculations do
    # 添加显示ID的计算字段
    calculate :display_id, :string, expr("用户#{numeric_id}")

    # 用户角色显示
    calculate :role_name,
              :string,
              expr(
                cond do
                  permission_level == 0 -> "普通玩家"
                  permission_level == 1 -> "管理员"
                  permission_level == 2 -> "超级管理员"
                  # 默认值
                  true -> "未知角色"
                end
              )

    # 是否为管理员（兼容性）
    calculate :is_admin, :boolean, expr(permission_level >= 1)

    # 是否为超级管理员（兼容性）
    calculate :is_super_admin, :boolean, expr(permission_level >= 1)

    # 代理相关计算字段
    calculate :is_agent, :boolean, expr(agent_level >= 0)

    calculate :is_root_agent, :boolean, expr(agent_level == 0)

    # 是否可以访问后台管理
    calculate :can_access_admin, :boolean, expr(permission_level >= 1 or agent_level >= 0)
  end

  identities do
    # 邮箱唯一性约束
    identity :unique_email, [:email]

    # 添加用户名唯一性约束
    identity :unique_username, [:username]

    # 添加数字ID唯一性约束
    identity :unique_numeric_id, [:numeric_id]

    # 添加客户端唯一标识唯一性约束
    identity :unique_client_uniq_id, [:client_uniq_id]
  end

  # 私有函数：根据UUID生成纯数字ID
  defp generate_numeric_id_from_uuid(uuid) when is_binary(uuid) do
    # 方案1：使用CRC32哈希（推荐）
    # 移除UUID中的连字符，然后计算CRC32
    uuid_clean = String.replace(uuid, "-", "")

    # 将UUID转换为二进制，然后计算CRC32
    uuid_binary = Base.decode16!(uuid_clean, case: :mixed)
    crc32 = :erlang.crc32(uuid_binary)

    # 确保结果是正数，并限制在合理范围内（8位数字）
    (abs(crc32) |> rem(100_000_000)) + 10_000_000
  end

  # 备用方案：如果需要更短的数字ID
  defp generate_short_numeric_id_from_uuid(uuid) when is_binary(uuid) do
    # 取UUID的前8个字符，转换为整数
    uuid
    |> String.replace("-", "")
    |> String.slice(0, 8)
    |> String.to_integer(16)
    # 限制为8位数字
    |> rem(100_000_000)
    # 确保是8位数字
    |> Kernel.+(10_000_000)
  end

  # 备用方案：基于时间戳的数字ID
  defp generate_timestamp_based_numeric_id(uuid) when is_binary(uuid) do
    # 获取当前时间戳（微秒）
    timestamp = System.system_time(:microsecond)

    # 取UUID的一部分作为随机种子
    uuid_seed =
      uuid
      |> String.replace("-", "")
      |> String.slice(0, 4)
      |> String.to_integer(16)

    # 组合时间戳和UUID种子
    ((timestamp + uuid_seed) |> rem(100_000_000)) + 10_000_000
  end
end
