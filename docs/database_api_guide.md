# 数据库API使用指南

## 概述

本文档提供了赛车游戏管理系统数据库的API使用指南，包括常用查询、存储过程、函数调用等。

---

## 1. 用户管理API

### 1.1 用户注册

```sql
-- 创建新用户
INSERT INTO users (
    numeric_id, 
    username, 
    email, 
    password_hash, 
    role, 
    status
) VALUES (
    generate_user_numeric_id(),
    $1,  -- username
    $2,  -- email
    crypt($3, gen_salt('bf')),  -- password
    'user',
    'active'
) RETURNING id, numeric_id, username, email;

-- 创建用户账户
INSERT INTO user_accounts (user_id) VALUES ($1);

-- 创建用户资料
INSERT INTO user_profiles (user_id) VALUES ($1);
```

### 1.2 用户登录验证

```sql
-- 验证用户登录
SELECT 
    u.id,
    u.numeric_id,
    u.username,
    u.email,
    u.role,
    u.status,
    ua.balance,
    ua.vip_level
FROM users u
LEFT JOIN user_accounts ua ON u.id = ua.user_id
WHERE u.username = $1 
AND u.password_hash = crypt($2, u.password_hash)
AND u.status = 'active';

-- 更新最后登录时间
UPDATE users 
SET last_login_at = NOW(), 
    last_login_ip = $2,
    login_attempts = 0
WHERE id = $1;
```

### 1.3 用户余额查询

```sql
-- 查询用户余额详情
SELECT 
    ua.balance,
    ua.frozen_balance,
    ua.total_deposit,
    ua.total_withdrawal,
    ua.total_bet,
    ua.total_win,
    ua.points,
    ua.vip_level,
    (ua.total_win - ua.total_bet) as net_profit
FROM user_accounts ua
WHERE ua.user_id = $1;
```

---

## 2. 系统通信API

### 2.1 发送系统消息

```sql
-- 发送给特定用户
INSERT INTO system_messages (
    type,
    title,
    content,
    recipient_id,
    priority,
    created_by,
    expires_at
) VALUES (
    $1,  -- type: 'message', 'announcement', 'notification'
    $2,  -- title
    $3,  -- content
    $4,  -- recipient_id
    $5,  -- priority: 'low', 'medium', 'high'
    $6,  -- created_by
    $7   -- expires_at
) RETURNING id;

-- 发送给所有用户 (recipient_id = NULL)
INSERT INTO system_messages (
    type,
    title,
    content,
    recipient_id,
    priority,
    created_by
) VALUES (
    $1, $2, $3, NULL, $4, $5
) RETURNING id;
```

### 2.2 获取用户消息

```sql
-- 获取用户未读消息
SELECT 
    sm.id,
    sm.type,
    sm.title,
    sm.content,
    sm.priority,
    sm.inserted_at,
    sm.expires_at,
    CASE WHEN mrl.id IS NOT NULL THEN true ELSE false END as is_read
FROM system_messages sm
LEFT JOIN message_read_logs mrl ON sm.id = mrl.message_id AND mrl.user_id = $1
WHERE (sm.recipient_id = $1 OR sm.recipient_id IS NULL)
AND sm.active = true
AND (sm.expires_at IS NULL OR sm.expires_at > NOW())
AND mrl.id IS NULL
ORDER BY sm.priority DESC, sm.inserted_at DESC;

-- 标记消息为已读
INSERT INTO message_read_logs (message_id, user_id, ip_address, user_agent)
VALUES ($1, $2, $3, $4)
ON CONFLICT (message_id, user_id) DO NOTHING;
```

---

## 3. 比赛管理API

### 3.1 创建比赛

```sql
-- 创建新比赛
INSERT INTO races (
    race_code,
    track_id,
    name,
    race_type,
    scheduled_start,
    total_laps,
    weather_condition,
    total_prize_pool,
    max_participants,
    betting_deadline,
    created_by
) VALUES (
    $1,  -- race_code
    $2,  -- track_id
    $3,  -- name
    $4,  -- race_type
    $5,  -- scheduled_start
    $6,  -- total_laps
    $7,  -- weather_condition
    $8,  -- total_prize_pool
    $9,  -- max_participants
    $10, -- betting_deadline
    $11  -- created_by
) RETURNING id, race_code;
```

### 3.2 添加比赛参与者

```sql
-- 添加赛车手到比赛
INSERT INTO race_participants (
    race_id,
    racer_id,
    starting_position
) VALUES ($1, $2, $3)
ON CONFLICT (race_id, racer_id) DO NOTHING
RETURNING id;

-- 更新比赛参与者数量
UPDATE races 
SET current_participants = (
    SELECT COUNT(*) 
    FROM race_participants 
    WHERE race_id = $1
)
WHERE id = $1;
```

### 3.3 查询比赛信息

```sql
-- 获取比赛详情
SELECT 
    r.id,
    r.race_code,
    r.name,
    r.race_type,
    r.scheduled_start,
    r.actual_start,
    r.status,
    r.total_laps,
    r.weather_condition,
    r.total_prize_pool,
    r.current_participants,
    r.max_participants,
    t.name as track_name,
    t.location as track_location,
    t.length_km as track_length
FROM races r
JOIN tracks t ON r.track_id = t.id
WHERE r.id = $1;

-- 获取比赛参与者
SELECT 
    rp.starting_position,
    rp.final_position,
    rp.status,
    ra.name as racer_name,
    ra.team,
    ra.skill_level,
    ra.win_rate
FROM race_participants rp
JOIN racers ra ON rp.racer_id = ra.id
WHERE rp.race_id = $1
ORDER BY rp.starting_position;
```

---

## 4. 投注系统API

### 4.1 下注

```sql
-- 创建投注记录
INSERT INTO bets (
    user_id,
    race_id,
    bet_type,
    selections,
    stake_amount,
    potential_payout,
    odds,
    ip_address,
    user_agent
) VALUES (
    $1,  -- user_id
    $2,  -- race_id
    $3,  -- bet_type
    $4,  -- selections (JSON)
    $5,  -- stake_amount
    $6,  -- potential_payout
    $7,  -- odds
    $8,  -- ip_address
    $9   -- user_agent
) RETURNING id;

-- 扣除用户余额
UPDATE user_accounts 
SET balance = balance - $2,
    total_bet = total_bet + $2
WHERE user_id = $1 AND balance >= $2;

-- 记录余额变动
INSERT INTO balance_transactions (
    user_id,
    account_id,
    transaction_type,
    amount,
    balance_before,
    balance_after,
    reference_id,
    reference_type,
    description
) VALUES (
    $1, $2, 'bet_placed', -$3, $4, $5, $6, 'bet', '投注扣款'
);
```

### 4.2 结算投注

```sql
-- 更新投注结果
UPDATE bets 
SET status = $2,
    actual_payout = $3,
    settled_at = NOW(),
    settlement_details = $4
WHERE id = $1;

-- 如果中奖，增加用户余额
UPDATE user_accounts 
SET balance = balance + $2,
    total_win = total_win + $2
WHERE user_id = $1 AND $2 > 0;

-- 记录中奖余额变动
INSERT INTO balance_transactions (
    user_id,
    account_id,
    transaction_type,
    amount,
    balance_before,
    balance_after,
    reference_id,
    reference_type,
    description
) VALUES (
    $1, $2, 'bet_win', $3, $4, $5, $6, 'bet', '投注中奖'
) WHERE $3 > 0;
```

### 4.3 查询投注记录

```sql
-- 查询用户投注历史
SELECT 
    b.id,
    b.bet_type,
    b.selections,
    b.stake_amount,
    b.potential_payout,
    b.actual_payout,
    b.odds,
    b.status,
    b.placed_at,
    b.settled_at,
    r.name as race_name,
    r.race_code,
    r.scheduled_start
FROM bets b
JOIN races r ON b.race_id = r.id
WHERE b.user_id = $1
ORDER BY b.placed_at DESC
LIMIT $2 OFFSET $3;
```

---

## 5. 财务管理API

### 5.1 充值处理

```sql
-- 创建充值记录
INSERT INTO deposits (
    user_id,
    transaction_id,
    payment_method,
    amount,
    currency,
    fee,
    net_amount,
    status
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, 'pending'
) RETURNING id;

-- 充值成功后更新
UPDATE deposits 
SET status = 'completed',
    processed_at = NOW(),
    confirmed_at = NOW()
WHERE transaction_id = $1;

-- 增加用户余额
UPDATE user_accounts 
SET balance = balance + $2,
    total_deposit = total_deposit + $2
WHERE user_id = $1;
```

### 5.2 提现处理

```sql
-- 创建提现申请
INSERT INTO withdrawals (
    user_id,
    transaction_id,
    withdrawal_method,
    amount,
    fee,
    net_amount,
    bank_account,
    status
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, 'pending'
) RETURNING id;

-- 冻结用户余额
UPDATE user_accounts 
SET balance = balance - $2,
    frozen_balance = frozen_balance + $2
WHERE user_id = $1 AND balance >= $2;

-- 审核通过
UPDATE withdrawals 
SET status = 'approved',
    reviewed_by = $2,
    reviewed_at = NOW(),
    review_notes = $3
WHERE id = $1;

-- 提现完成
UPDATE withdrawals 
SET status = 'completed',
    completed_at = NOW()
WHERE id = $1;

-- 减少冻结余额
UPDATE user_accounts 
SET frozen_balance = frozen_balance - $2,
    total_withdrawal = total_withdrawal + $2
WHERE user_id = $1;
```

---

## 6. 统计查询API

### 6.1 用户统计

```sql
-- 用户投注统计
SELECT 
    COUNT(DISTINCT b.id) as total_bets,
    COALESCE(SUM(b.stake_amount), 0) as total_stake,
    COALESCE(SUM(b.actual_payout), 0) as total_payout,
    COALESCE(SUM(CASE WHEN b.status = 'won' THEN 1 ELSE 0 END), 0) as winning_bets,
    COALESCE(
        SUM(CASE WHEN b.status = 'won' THEN 1 ELSE 0 END)::DECIMAL / 
        NULLIF(COUNT(DISTINCT b.id), 0) * 100, 
        0
    ) as win_rate
FROM bets b
WHERE b.user_id = $1
AND b.placed_at >= $2  -- 时间范围
AND b.placed_at <= $3;
```

### 6.2 比赛统计

```sql
-- 比赛投注统计
SELECT 
    r.name,
    r.race_code,
    COUNT(DISTINCT b.user_id) as unique_bettors,
    COUNT(b.id) as total_bets,
    COALESCE(SUM(b.stake_amount), 0) as total_volume,
    COALESCE(SUM(b.actual_payout), 0) as total_payout,
    r.status
FROM races r
LEFT JOIN bets b ON r.id = b.race_id
WHERE r.scheduled_start >= $1
AND r.scheduled_start <= $2
GROUP BY r.id, r.name, r.race_code, r.status
ORDER BY total_volume DESC;
```

---

## 7. 存储过程示例

### 7.1 用户余额变动存储过程

```sql
CREATE OR REPLACE FUNCTION update_user_balance(
    p_user_id BIGINT,
    p_amount DECIMAL,
    p_transaction_type VARCHAR,
    p_reference_id BIGINT DEFAULT NULL,
    p_reference_type VARCHAR DEFAULT NULL,
    p_description TEXT DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
    v_account_id BIGINT;
    v_balance_before DECIMAL;
    v_balance_after DECIMAL;
BEGIN
    -- 获取账户信息
    SELECT id, balance INTO v_account_id, v_balance_before
    FROM user_accounts 
    WHERE user_id = p_user_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'User account not found for user_id: %', p_user_id;
    END IF;
    
    -- 检查余额是否足够（如果是扣款）
    IF p_amount < 0 AND v_balance_before < ABS(p_amount) THEN
        RAISE EXCEPTION 'Insufficient balance';
    END IF;
    
    -- 更新余额
    UPDATE user_accounts 
    SET balance = balance + p_amount,
        updated_at = NOW()
    WHERE user_id = p_user_id;
    
    v_balance_after := v_balance_before + p_amount;
    
    -- 记录交易
    INSERT INTO balance_transactions (
        user_id,
        account_id,
        transaction_type,
        amount,
        balance_before,
        balance_after,
        reference_id,
        reference_type,
        description
    ) VALUES (
        p_user_id,
        v_account_id,
        p_transaction_type,
        p_amount,
        v_balance_before,
        v_balance_after,
        p_reference_id,
        p_reference_type,
        p_description
    );
    
    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RAISE;
END;
$$ LANGUAGE plpgsql;
```

---

*文档版本: v1.0*  
*最后更新: 2024-06-21*  
*维护者: 后端开发团队*
