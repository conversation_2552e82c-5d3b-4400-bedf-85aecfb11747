# 数据库文件总结

## 📁 数据库文件位置

### 1. 核心数据库文件

#### **数据库配置**
- `lib/cypridina/repo.ex` - 主要的Ecto仓库配置
- `config/dev.exs` - 开发环境数据库配置
- `config/runtime.exs` - 运行时数据库配置
- `config/config.exs` - 基础配置

#### **数据库迁移文件**
- `priv/repo/migrations/20250617073904_init_db_extensions_1.exs` - 数据库扩展初始化
- `priv/repo/migrations/20250617073906_init_db.exs` - 主要数据库表结构
- `priv/repo/seeds.exs` - 数据库种子数据

### 2. 数据模型定义 (Ash Resources)

#### **用户管理模块** (`lib/cypridina/accounts/`)
- `resources/user.ex` - 用户资源定义
- `resources/user_identity.ex` - 用户身份认证
- `resources/token.ex` - 认证令牌
- `resources/agent_relationship.ex` - 代理关系

#### **账本系统** (`lib/cypridina/ledger/`)
- `resources/account.ex` - 账户资源
- `resources/transfer.ex` - 转账记录
- `resources/balance.ex` - 余额记录

#### **Teen后台系统** (`lib/teen/`)
- 多个业务域的资源定义
- 客服管理、支付系统、游戏管理等

---

## 🗄️ 数据库表结构概览

### 核心业务表

#### **用户相关表**
```sql
users                    -- 用户基础信息
user_identities         -- 用户身份认证
tokens                   -- 认证令牌
agent_relationships      -- 代理关系
user_bans               -- 用户封禁记录
user_assets             -- 用户资产
user_tags               -- 用户标签
```

#### **账本系统表**
```sql
ledger_accounts         -- 账户表
ledger_transfers        -- 转账记录
ledger_balances         -- 余额记录
```

#### **赛车游戏表**
```sql
races                   -- 比赛记录
racers                  -- 赛车手信息
racing_game_bets        -- 投注记录
racing_game_stocks      -- 股票持仓
racing_game_points_transactions  -- 积分交易
leaderboards           -- 排行榜
```

#### **系统管理表**
```sql
admin_users            -- 管理员用户
roles                  -- 角色管理
permissions            -- 权限管理
role_permissions       -- 角色权限关联
operation_logs         -- 操作日志
```

#### **支付系统表**
```sql
payment_gateways       -- 支付网关
payment_configs        -- 支付配置
exchange_orders        -- 兑换订单
exchange_configs       -- 兑换配置
```

#### **活动系统表**
```sql
sign_in_activities     -- 签到活动
cdkey_activities       -- 兑换码活动
vip_levels            -- VIP等级
```

#### **客服系统表**
```sql
customer_chats         -- 客服聊天
user_questions         -- 用户问题
verification_codes     -- 验证码
sensitive_words        -- 敏感词
```

---

## 🔧 数据库技术栈

### **数据库引擎**
- **PostgreSQL 16+** - 主数据库
- **Ecto** - Elixir ORM
- **Ash Framework** - 资源管理框架

### **扩展功能**
- **AshPostgres** - PostgreSQL数据层
- **AshAuthentication** - 用户认证
- **AshDoubleEntry** - 复式记账
- **AshMoney** - 货币处理
- **AshPaperTrail** - 审计日志

### **特殊类型**
- `money_with_currency` - 货币类型
- `citext` - 大小写不敏感文本
- `uuid` - UUID主键
- `jsonb` - JSON数据

---

## 📊 数据库设计特点

### **1. 现代化设计**
- ✅ UUID主键
- ✅ 时间戳字段 (inserted_at, updated_at)
- ✅ 软删除支持
- ✅ 审计日志
- ✅ 版本控制

### **2. 业务特性**
- ✅ 多货币支持
- ✅ 复式记账系统
- ✅ 用户权限分级
- ✅ 代理关系管理
- ✅ 游戏数据统计

### **3. 安全性**
- ✅ 密码哈希存储
- ✅ 敏感数据加密
- ✅ 操作日志记录
- ✅ 权限控制

### **4. 性能优化**
- ✅ 合理的索引设计
- ✅ 数据分区支持
- ✅ 查询优化
- ✅ 连接池配置

---

## 🚀 快速操作指南

### **数据库初始化**
```bash
# 创建数据库
mix ecto.create

# 运行迁移
mix ecto.migrate

# 初始化种子数据
mix run priv/repo/seeds.exs
```

### **生成新迁移**
```bash
# Ash资源迁移
mix ash_postgres.generate_migrations

# 手动迁移
mix ecto.gen.migration migration_name
```

### **数据库重置**
```bash
# 完全重置
mix ecto.drop --force-drop
mix ecto.create
mix ecto.migrate
```

### **查看数据库状态**
```bash
# 迁移状态
mix ecto.migrations

# 数据库信息
mix ecto.dump
```

---

## 📈 数据库监控

### **性能监控**
- 慢查询日志
- 连接池状态
- 索引使用情况
- 表大小统计

### **业务监控**
- 用户注册趋势
- 交易量统计
- 游戏活跃度
- 系统错误率

---

## 🔗 相关文档

- [数据库设计文档](./database_schema.md) - 详细的表结构设计
- [数据库ER图](./database_er_diagram.md) - 实体关系图
- [API使用指南](./database_api_guide.md) - 数据库API文档

---

## 📝 注意事项

### **开发环境**
- 需要设置 `DATABASE_URL` 环境变量
- PostgreSQL版本需要16+
- 确保安装了必要的扩展

### **生产环境**
- 配置数据库连接池
- 设置备份策略
- 监控数据库性能
- 定期维护索引

### **数据安全**
- 定期备份数据
- 加密敏感信息
- 审计重要操作
- 控制访问权限

---

*最后更新: 2024-06-21*  
*维护者: 开发团队*
