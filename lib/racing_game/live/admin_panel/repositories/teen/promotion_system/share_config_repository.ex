defmodule RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.ShareConfigRepository do
  @moduledoc """
  ⚙️ 分享配置数据访问仓储

  负责分享配置相关的数据访问操作，包括：
  - 分享配置的CRUD操作
  - 配置类型管理
  - 配置模板管理
  - 配置版本控制
  - 配置生效管理
  """

  require Logger
  alias Teen.PromotionSystem.ShareConfig
  alias RacingGame.Live.AdminPanel.Utils.CacheUtils

  @cache_ttl 1800  # 30分钟缓存，配置变更较少

  # ==================== 基础CRUD操作 ====================

  @doc """
  创建分享配置

  ## 参数
  - `config_data` - 配置数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, config}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_config(config_data, options \\ []) do
    Logger.info("⚙️ [分享配置仓储] 创建分享配置: #{inspect(config_data[:config_key])}")

    case ShareConfig.create(config_data) do
      {:ok, config} ->
        Logger.info("✅ [分享配置仓储] 分享配置创建成功: #{config.id}")
        clear_config_cache()
        {:ok, config}

      {:error, reason} ->
        Logger.error("❌ [分享配置仓储] 分享配置创建失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  获取分享配置信息

  ## 参数
  - `config_id` - 配置ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, config}` - 成功
  - `{:error, :not_found}` - 未找到
  """
  def get_config(config_id, options \\ []) do
    cache_key = "share_config:#{config_id}"

    case CacheUtils.get_or_set(cache_key, @cache_ttl, fn ->
      case ShareConfig.read(config_id) do
        {:ok, config} -> {:ok, config}
        {:error, _} -> {:error, :not_found}
      end
    end) do
      {:ok, config} ->
        Logger.debug("⚙️ [分享配置仓储] 获取分享配置成功: #{config_id}")
        {:ok, config}

      {:error, reason} ->
        Logger.warning("⚠️ [分享配置仓储] 分享配置不存在: #{config_id}")
        {:error, reason}
    end
  end

  @doc """
  根据配置键获取分享配置

  ## 参数
  - `config_key` - 配置键
  - `options` - 选项参数

  ## 返回
  - `{:ok, config}` - 成功
  - `{:error, :not_found}` - 未找到
  """
  def get_config_by_key(config_key, options \\ []) do
    cache_key = "share_config:key:#{config_key}"

    case CacheUtils.get_or_set(cache_key, @cache_ttl, fn ->
      case ShareConfig.read_all(filter: [config_key: config_key], limit: 1) do
        {:ok, [config]} -> {:ok, config}
        {:ok, []} -> {:error, :not_found}
        {:error, reason} -> {:error, reason}
      end
    end) do
      {:ok, config} ->
        Logger.debug("⚙️ [分享配置仓储] 根据键获取分享配置成功: #{config_key}")
        {:ok, config}

      {:error, reason} ->
        Logger.warning("⚠️ [分享配置仓储] 分享配置键不存在: #{config_key}")
        {:error, reason}
    end
  end

  @doc """
  更新分享配置信息

  ## 参数
  - `config_id` - 配置ID
  - `update_data` - 更新数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, config}` - 成功
  - `{:error, reason}` - 失败
  """
  def update_config(config_id, update_data, options \\ []) do
    Logger.info("⚙️ [分享配置仓储] 更新分享配置: #{config_id}")

    with {:ok, config} <- get_config(config_id),
         {:ok, updated_config} <- ShareConfig.update(config, update_data) do
      Logger.info("✅ [分享配置仓储] 分享配置更新成功: #{config_id}")
      clear_config_cache()
      {:ok, updated_config}
    else
      {:error, reason} ->
        Logger.error("❌ [分享配置仓储] 分享配置更新失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  删除分享配置

  ## 参数
  - `config_id` - 配置ID
  - `options` - 选项参数

  ## 返回
  - `:ok` - 成功
  - `{:error, reason}` - 失败
  """
  def delete_config(config_id, options \\ []) do
    Logger.info("⚙️ [分享配置仓储] 删除分享配置: #{config_id}")

    with {:ok, config} <- get_config(config_id),
         :ok <- ShareConfig.destroy(config) do
      Logger.info("✅ [分享配置仓储] 分享配置删除成功: #{config_id}")
      clear_config_cache()
      :ok
    else
      {:error, reason} ->
        Logger.error("❌ [分享配置仓储] 分享配置删除失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  # ==================== 分享配置查询操作 ====================

  @doc """
  获取所有分享配置列表

  ## 参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, configs}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_configs(options \\ []) do
    Logger.debug("⚙️ [分享配置仓储] 获取分享配置列表")

    page = Keyword.get(options, :page, 1)
    page_size = Keyword.get(options, :page_size, 50)
    sort = Keyword.get(options, :sort, updated_at: :desc)

    case ShareConfig.read_all(
      page: [limit: page_size, offset: (page - 1) * page_size],
      sort: sort
    ) do
      {:ok, configs} ->
        Logger.debug("✅ [分享配置仓储] 获取分享配置列表成功: #{length(configs)} 条")
        {:ok, configs}

      {:error, reason} ->
        Logger.error("❌ [分享配置仓储] 获取分享配置列表失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  根据配置类型获取配置列表

  ## 参数
  - `config_type` - 配置类型
  - `options` - 选项参数

  ## 返回
  - `{:ok, configs}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_configs_by_type(config_type, options \\ []) do
    Logger.debug("⚙️ [分享配置仓储] 根据类型获取配置列表: #{config_type}")

    page = Keyword.get(options, :page, 1)
    page_size = Keyword.get(options, :page_size, 50)
    sort = Keyword.get(options, :sort, updated_at: :desc)

    case ShareConfig.read_all(
      filter: [config_type: config_type],
      page: [limit: page_size, offset: (page - 1) * page_size],
      sort: sort
    ) do
      {:ok, configs} ->
        Logger.debug("✅ [分享配置仓储] 根据类型获取配置列表成功: #{length(configs)} 条")
        {:ok, configs}

      {:error, reason} ->
        Logger.error("❌ [分享配置仓储] 根据类型获取配置列表失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  获取活跃分享配置列表

  ## 参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, configs}` - 成功
  - `{:error, reason}` - 失败
  """
  def list_active_configs(options \\ []) do
    Logger.debug("⚙️ [分享配置仓储] 获取活跃分享配置列表")

    cache_key = "share_configs:active"

    case CacheUtils.get_or_set(cache_key, @cache_ttl, fn ->
      case ShareConfig.read_all(filter: [status: 1], sort: [updated_at: :desc]) do
        {:ok, configs} -> {:ok, configs}
        {:error, reason} -> {:error, reason}
      end
    end) do
      {:ok, configs} ->
        Logger.debug("✅ [分享配置仓储] 获取活跃分享配置列表成功: #{length(configs)} 条")
        {:ok, configs}

      {:error, reason} ->
        Logger.error("❌ [分享配置仓储] 获取活跃分享配置列表失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  # ==================== 配置管理操作 ====================

  @doc """
  启用分享配置

  ## 参数
  - `config_id` - 配置ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, config}` - 成功
  - `{:error, reason}` - 失败
  """
  def enable_config(config_id, options \\ []) do
    Logger.info("⚙️ [分享配置仓储] 启用分享配置: #{config_id}")
    update_config(config_id, %{status: 1}, options)
  end

  @doc """
  禁用分享配置

  ## 参数
  - `config_id` - 配置ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, config}` - 成功
  - `{:error, reason}` - 失败
  """
  def disable_config(config_id, options \\ []) do
    Logger.info("⚙️ [分享配置仓储] 禁用分享配置: #{config_id}")
    update_config(config_id, %{status: 0}, options)
  end

  @doc """
  批量更新配置状态

  ## 参数
  - `config_ids` - 配置ID列表
  - `status` - 新状态
  - `options` - 选项参数

  ## 返回
  - `{:ok, updated_count}` - 成功
  - `{:error, reason}` - 失败
  """
  def batch_update_status(config_ids, status, options \\ []) do
    Logger.info("⚙️ [分享配置仓储] 批量更新配置状态: #{length(config_ids)} 条, 状态: #{status}")

    results = Enum.map(config_ids, fn config_id ->
      update_config(config_id, %{status: status}, options)
    end)

    success_count = results |> Enum.count(fn {result, _} -> result == :ok end)
    
    if success_count == length(config_ids) do
      Logger.info("✅ [分享配置仓储] 批量更新配置状态成功: #{success_count} 条")
      {:ok, success_count}
    else
      Logger.error("❌ [分享配置仓储] 批量更新配置状态部分失败: 成功 #{success_count}/#{length(config_ids)} 条")
      {:error, :partial_failure}
    end
  end

  # ==================== 私有辅助函数 ====================

  defp clear_config_cache do
    CacheUtils.delete_pattern("share_config:*")
    CacheUtils.delete_pattern("share_configs:*")
  end
end
