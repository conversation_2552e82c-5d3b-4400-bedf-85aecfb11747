defmodule RacingGame.Live.AdminPanel.Validators.DeleteConstraintValidator do
  @moduledoc """
  删除约束验证器

  处理系统通信管理组件中的删除约束检查逻辑。

  ## 功能特性
  - 阅读记录约束检查
  - 系统保护约束检查
  - 业务依赖约束检查
  - 模板通信保护
  - 关键通信识别
  - 详细的约束违规提示

  ## 约束类型
  - **阅读记录约束**: 防止删除有大量阅读记录的通信
  - **系统保护约束**: 保护系统关键通信和重要公告
  - **业务约束**: 检查业务依赖和外部引用
  - **模板保护**: 保护模板通信不被误删
  """

  require Logger

  # 常量定义
  @max_read_records_warning 100
  @max_read_records_error 1000
  @recent_days_threshold 7
  @critical_keywords ["系统维护", "紧急通知", "安全更新", "重要升级", "系统升级", "维护公告"]
  @template_indicators ["模板", "template", "示例", "example", "样例", "demo"]
  @high_priority_types [:announcement, "announcement"]
  @high_priority_levels [:high, :urgent, "high", "urgent"]

  @doc """
  检查删除约束

  ## 参数
  - `communication` - 要删除的通信记录

  ## 返回
  - `:ok` - 可以删除
  - `{:error, error_message}` - 不能删除，返回错误消息
  """
  def check_delete_constraints(communication) do
    Logger.info("🔍 [约束验证] 开始检查删除约束，ID: #{communication.id}")
    Logger.debug("🔍 [约束验证] 通信信息: #{inspect(%{title: communication.title, type: communication.type, active: communication.active})}")

    try do
      case perform_constraint_checks(communication) do
        :ok ->
          Logger.info("✅ [约束验证] 删除约束检查通过")
          :ok
        {:error, message} ->
          Logger.warning("❌ [约束验证] 删除约束检查失败: #{message}")
          {:error, message}
      end
    rescue
      error ->
        Logger.error("❌ [约束验证] 约束检查异常: #{inspect(error)}")
        {:error, "约束检查过程中发生异常，为安全起见，禁止删除"}
    end
  end

  # 执行所有约束检查
  defp perform_constraint_checks(communication) do
    with :ok <- check_read_records_constraint(communication),
         :ok <- check_system_protection_constraint(communication),
         :ok <- check_business_constraint(communication),
         :ok <- check_additional_constraints(communication) do
      :ok
    else
      {:error, message} -> {:error, message}
    end
  end

  # 检查额外约束
  defp check_additional_constraints(communication) do
    with :ok <- check_expiration_constraint(communication),
         :ok <- check_recipient_constraint(communication) do
      :ok
    else
      {:error, message} -> {:error, message}
    end
  end

  # 私有函数 - 检查阅读记录约束
  defp check_read_records_constraint(communication) do
    Logger.debug("📊 [约束验证] 检查阅读记录约束")

    case get_read_records_count(communication.id) do
      count when count > @max_read_records_error ->
        error_msg = "该通信有大量阅读记录（#{count}条），删除可能影响数据完整性。建议先备份相关数据或联系系统管理员。"
        Logger.warning("⚠️ [约束验证] #{error_msg}")
        {:error, error_msg}
      count when count > @max_read_records_warning ->
        Logger.warning("⚠️ [约束验证] 通信有较多阅读记录: #{count}条，但允许删除")
        :ok
      count ->
        Logger.debug("📊 [约束验证] 阅读记录数量正常: #{count}条")
        :ok
    end
  end

  # 私有函数 - 检查系统保护约束
  defp check_system_protection_constraint(communication) do
    Logger.debug("🛡️ [约束验证] 检查系统保护约束")

    with :ok <- check_critical_communication(communication),
         :ok <- check_recent_important_announcement(communication),
         :ok <- check_system_default_communication(communication) do
      Logger.debug("🛡️ [约束验证] 系统保护约束检查通过")
      :ok
    else
      {:error, message} -> {:error, message}
    end
  end

  # 检查关键通信
  defp check_critical_communication(communication) do
    if is_system_critical_communication?(communication) do
      error_msg = "这是系统关键通信，不允许删除。如需删除，请联系系统管理员。"
      Logger.warning("🛡️ [约束验证] #{error_msg}")
      {:error, error_msg}
    else
      :ok
    end
  end

  # 检查最近重要公告
  defp check_recent_important_announcement(communication) do
    if is_recent_important_announcement?(communication) do
      error_msg = "这是最近发布的重要公告，删除可能影响用户体验。建议禁用而非删除。"
      Logger.warning("🛡️ [约束验证] #{error_msg}")
      {:error, error_msg}
    else
      :ok
    end
  end

  # 检查系统默认通信
  defp check_system_default_communication(communication) do
    if is_system_default_communication?(communication) do
      error_msg = "这是系统默认通信，删除可能影响系统功能。请联系技术支持。"
      Logger.warning("🛡️ [约束验证] #{error_msg}")
      {:error, error_msg}
    else
      :ok
    end
  end

  # 私有函数 - 检查业务约束
  defp check_business_constraint(communication) do
    # 检查是否有业务依赖
    case has_business_dependencies?(communication) do
      true ->
        {:error, "该通信存在业务依赖关系，删除可能影响相关功能。请先处理依赖关系。"}
      false ->
        # 检查是否是活跃通信
        case is_active_communication?(communication) do
          true ->
            Logger.warn("⚠️ [约束验证] 正在删除活跃状态的通信")
            :ok
          false ->
            :ok
        end
    end
  end

  # 检查过期约束
  defp check_expiration_constraint(communication) do
    Logger.debug("⏰ [约束验证] 检查过期约束")

    case communication.expires_at do
      nil ->
        :ok
      expires_at ->
        if DateTime.compare(expires_at, DateTime.utc_now()) == :gt do
          Logger.debug("⏰ [约束验证] 通信尚未过期，允许删除")
          :ok
        else
          Logger.debug("⏰ [约束验证] 通信已过期，允许删除")
          :ok
        end
    end
  end

  # 检查接收者约束
  defp check_recipient_constraint(communication) do
    Logger.debug("👥 [约束验证] 检查接收者约束")

    case communication.recipient_type do
      "all" ->
        if communication.active do
          Logger.warning("👥 [约束验证] 删除面向所有用户的活跃通信")
        end
        :ok
      _ ->
        :ok
    end
  end

  # 检查是否是系统默认通信
  defp is_system_default_communication?(communication) do
    # 检查是否是系统默认生成的通信
    default_indicators = ["系统默认", "default", "初始化", "init"]
    text_to_check = "#{communication.title || ""} #{communication.content || ""}"
    text_lower = String.downcase(text_to_check)

    Enum.any?(default_indicators, fn indicator ->
      String.contains?(text_lower, String.downcase(indicator))
    end)
  end

  # 私有函数 - 获取阅读记录数量
  defp get_read_records_count(_communication_id) do
    # TODO: 实现查询阅读记录数量的逻辑
    # 这里应该查询相关的阅读记录表
    # 暂时返回模拟数据
    :rand.uniform(150)
  end

  # 私有函数 - 检查是否是系统关键通信
  defp is_system_critical_communication?(communication) do
    title_lower = String.downcase(communication.title || "")
    content_lower = String.downcase(communication.content || "")
    text_to_check = "#{title_lower} #{content_lower}"

    Enum.any?(@critical_keywords, fn keyword ->
      String.contains?(text_to_check, String.downcase(keyword))
    end)
  end

  # 私有函数 - 检查是否是最近的重要公告
  defp is_recent_important_announcement?(communication) do
    with true <- is_high_priority_announcement?(communication),
         true <- is_recent_communication?(communication) do
      true
    else
      _ -> false
    end
  end

  # 检查是否是高优先级公告
  defp is_high_priority_announcement?(communication) do
    communication.type in @high_priority_types and
    communication.priority in @high_priority_levels
  end

  # 检查是否是最近的通信
  defp is_recent_communication?(communication) do
    case communication.inserted_at do
      nil -> false
      inserted_at ->
        threshold_date = DateTime.add(DateTime.utc_now(), -@recent_days_threshold, :day)
        DateTime.compare(inserted_at, threshold_date) == :gt
    end
  end

  # 私有函数 - 检查是否有业务依赖
  defp has_business_dependencies?(communication) do
    # TODO: 实现检查业务依赖的逻辑
    # 这里可以检查是否有其他模块引用了这个通信
    # 例如：工作流、自动化规则、报表等

    # 检查是否是模板通信
    case is_template_communication?(communication) do
      true -> true
      false ->
        # 检查是否被其他功能引用
        has_external_references?(communication)
    end
  end

  # 私有函数 - 检查是否是模板通信
  defp is_template_communication?(communication) do
    text_to_check = "#{communication.title || ""} #{communication.content || ""}"
    text_lower = String.downcase(text_to_check)

    Enum.any?(@template_indicators, fn indicator ->
      String.contains?(text_lower, String.downcase(indicator))
    end)
  end

  # 私有函数 - 检查是否有外部引用
  defp has_external_references?(_communication) do
    # TODO: 实现检查外部引用的逻辑
    # 这里可以查询其他表是否引用了这个通信ID
    false
  end

  # 私有函数 - 检查是否是活跃通信
  defp is_active_communication?(communication) do
    communication.active == true
  end
end
