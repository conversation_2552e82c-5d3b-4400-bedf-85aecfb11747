defmodule RacingGame.Live.AdminPanel.Handlers.DialogEventHandler do
  @moduledoc """
  对话框事件处理器

  处理系统通信管理组件中的对话框显示和隐藏操作。
  """

  require Logger
  import RacingGame.Utils.DialogHelper

  @doc """
  处理隐藏指定对话框事件

  ## 参数
  - `dialog_name` - 对话框名称
  - `socket` - Phoenix LiveView socket

  ## 返回
  - `{:noreply, socket}` - 更新后的socket
  """
  def handle_hide_dialog(dialog_name, socket) when is_binary(dialog_name) do
    Logger.info("🔒 [对话框] 隐藏指定对话框: #{dialog_name}")

    dialog_atom = String.to_existing_atom(dialog_name)
    socket = hide_dialog(socket, dialog_atom)
    {:noreply, socket}
  end

  @doc """
  处理隐藏所有对话框事件

  ## 参数
  - `socket` - Phoenix LiveView socket

  ## 返回
  - `{:noreply, socket}` - 更新后的socket
  """
  def handle_hide_all_dialogs(socket) do
    Logger.info("🔒 [对话框] 隐藏所有对话框")

    socket =
      socket
      |> hide_dialog(:delete_confirm)
      |> hide_dialog(:save_success)
      |> hide_dialog(:operation_error)
      |> hide_dialog(:status_confirm)

    {:noreply, socket}
  end
end
