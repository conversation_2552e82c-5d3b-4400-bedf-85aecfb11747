defmodule RacingGame.Live.AdminPanel.Mappings.SystemCommunicationsMappings do
  @moduledoc """
  系统通信映射模块

  定义系统通信相关的类型映射、配置和常量。
  """

  @doc """
  通信类型配置映射
  """
  def communication_types do
    %{
      "message" => %{
        title: "系统消息",
        description: "向用户发送重要的系统消息和提醒",
        icon: "fas fa-envelope",
        color: "blue",
        priority_default: "medium"
      },
      "announcement" => %{
        title: "系统公告",
        description: "发布重要的系统公告和通知",
        icon: "fas fa-bullhorn",
        color: "green",
        priority_default: "high"
      },
      "notification" => %{
        title: "系统通知",
        description: "发送系统状态和操作通知",
        icon: "fas fa-bell",
        color: "yellow",
        priority_default: "medium"
      }
    }
  end

  @doc """
  优先级选项映射
  """
  def priority_options do
    %{
      "low" => %{
        label: "低",
        color: "gray",
        icon: "fas fa-flag",
        order: 1
      },
      "medium" => %{
        label: "中",
        color: "blue",
        icon: "fas fa-flag",
        order: 2
      },
      "high" => %{
        label: "高",
        color: "orange",
        icon: "fas fa-flag",
        order: 3
      },
      "urgent" => %{
        label: "紧急",
        color: "red",
        icon: "fas fa-exclamation-triangle",
        order: 4
      }
    }
  end

  @doc """
  优先级选择选项（用于下拉框）
  """
  def priority_select_options do
    [
      {nil, "选择优先级"},
      {"low", "低"},
      {"medium", "中"},
      {"high", "高"},
      {"urgent", "紧急"}
    ]
  end

  @doc """
  接收者类型选项
  """
  def recipient_type_options do
    [
      {"all", "所有用户"},
      {"user", "特定用户"},
      {"admin", "管理员"}
    ]
  end

  @doc """
  状态选项
  """
  def status_options do
    %{
      true => %{
        label: "启用",
        color: "green",
        icon: "fas fa-check-circle"
      },
      false => %{
        label: "禁用",
        color: "red",
        icon: "fas fa-times-circle"
      }
    }
  end

  @doc """
  获取通信类型配置

  ## 参数
  - `type` - 通信类型

  ## 返回
  - 类型配置映射
  """
  def get_type_config(type) when is_binary(type) do
    Map.get(communication_types(), type, get_default_type_config())
  end

  def get_type_config(type) when is_atom(type) do
    get_type_config(Atom.to_string(type))
  end

  def get_type_config(_), do: get_default_type_config()

  @doc """
  获取优先级配置

  ## 参数
  - `priority` - 优先级

  ## 返回
  - 优先级配置映射
  """
  def get_priority_config(priority) when is_binary(priority) do
    Map.get(priority_options(), priority, get_default_priority_config())
  end

  def get_priority_config(priority) when is_atom(priority) do
    get_priority_config(Atom.to_string(priority))
  end

  def get_priority_config(_), do: get_default_priority_config()

  @doc """
  获取状态配置

  ## 参数
  - `status` - 状态

  ## 返回
  - 状态配置映射
  """
  def get_status_config(status) when is_boolean(status) do
    Map.get(status_options(), status, get_default_status_config())
  end

  def get_status_config(_), do: get_default_status_config()

  @doc """
  验证通信类型是否有效

  ## 参数
  - `type` - 通信类型

  ## 返回
  - `true` | `false`
  """
  def valid_type?(type) when is_binary(type) do
    Map.has_key?(communication_types(), type)
  end

  def valid_type?(type) when is_atom(type) do
    valid_type?(Atom.to_string(type))
  end

  def valid_type?(_), do: false

  @doc """
  验证优先级是否有效

  ## 参数
  - `priority` - 优先级

  ## 返回
  - `true` | `false`
  """
  def valid_priority?(priority) when is_binary(priority) do
    Map.has_key?(priority_options(), priority)
  end

  def valid_priority?(priority) when is_atom(priority) do
    valid_priority?(Atom.to_string(priority))
  end

  def valid_priority?(_), do: false

  @doc """
  验证接收者类型是否有效

  ## 参数
  - `recipient_type` - 接收者类型

  ## 返回
  - `true` | `false`
  """
  def valid_recipient_type?(recipient_type) when is_binary(recipient_type) do
    recipient_type in ["all", "user", "admin"]
  end

  def valid_recipient_type?(_), do: false

  @doc """
  获取所有有效的通信类型

  ## 返回
  - 类型列表
  """
  def get_valid_types do
    Map.keys(communication_types())
  end

  @doc """
  获取所有有效的优先级

  ## 返回
  - 优先级列表
  """
  def get_valid_priorities do
    Map.keys(priority_options())
  end

  @doc """
  获取所有有效的接收者类型

  ## 返回
  - 接收者类型列表
  """
  def get_valid_recipient_types do
    ["all", "user", "admin"]
  end

  @doc """
  将通信记录转换为表单数据

  ## 参数
  - `communication` - 通信记录

  ## 返回
  - 表单数据映射
  """
  def communication_to_form_data(nil) do
    %{
      "title" => "",
      "content" => "",
      "type" => "message",
      "priority" => "medium",
      "recipient_type" => "all",
      "recipient_id" => "",
      "active" => true,
      "expires_at" => ""
    }
  end

  def communication_to_form_data(communication) when is_map(communication) do
    %{
      "id" => communication.id,
      "title" => communication.title || "",
      "content" => communication.content || "",
      "type" => to_string(communication.type || "message"),
      "priority" => to_string(communication.priority || "medium"),
      "recipient_type" => communication.recipient_type || "all",
      "recipient_id" => communication.recipient_id || "",
      "active" => communication.active || true,
      "expires_at" => format_datetime_for_input(communication.expires_at)
    }
  end

  # 私有函数 - 获取默认类型配置
  defp get_default_type_config do
    %{
      title: "系统通信",
      description: "系统通信记录",
      icon: "fas fa-comments",
      color: "gray",
      priority_default: "medium"
    }
  end

  # 私有函数 - 获取默认优先级配置
  defp get_default_priority_config do
    %{
      label: "中",
      color: "blue",
      icon: "fas fa-flag",
      order: 2
    }
  end

  # 私有函数 - 获取默认状态配置
  defp get_default_status_config do
    %{
      label: "未知",
      color: "gray",
      icon: "fas fa-question-circle"
    }
  end

  # 私有函数 - 格式化日期时间为输入框格式
  defp format_datetime_for_input(nil), do: ""
  defp format_datetime_for_input(%DateTime{} = datetime) do
    datetime
    |> DateTime.to_naive()
    |> NaiveDateTime.to_string()
    |> String.replace(" ", "T")
    |> String.slice(0, 16)
  end
  defp format_datetime_for_input(datetime) when is_binary(datetime), do: datetime
  defp format_datetime_for_input(_), do: ""

  @doc """
  准备保存数据
  """
  def prepare_save_data(data, type) do
    %{
      title: data["title"],
      content: data["content"],
      type: String.to_atom(data["type"] || type),
      priority: String.to_atom(data["priority"] || "medium"),
      recipient_type: String.to_atom(data["recipient_type"] || "all"),
      recipient_id: data["recipient_id"],
      active: data["active"] == "true" || data["active"] == true,
      expires_at: parse_datetime(data["expires_at"])
    }
  end

  @doc """
  准备创建数据（包含创建者信息）
  """
  def prepare_create_data(data, type, creator_id) do
    data
    |> prepare_save_data(type)
    |> Map.put(:created_by, creator_id)
  end

  @doc """
  构建确认消息
  """
  def build_confirm_message(data, mode) do
    action = if mode == :create, do: "创建", else: "更新"
    type_name = get_type_display_name(data["type"] || "message")

    """
    您确定要#{action}这条#{type_name}吗？

    标题：#{data["title"]}
    内容：#{String.slice(data["content"] || "", 0, 50)}#{if String.length(data["content"] || "") > 50, do: "...", else: ""}
    优先级：#{get_priority_display_name(data["priority"] || "medium")}
    接收者：#{get_recipient_display_name(data["recipient_type"] || "all")}
    """
  end

  @doc """
  获取类型显示名称
  """
  def get_type_display_name("message"), do: "消息"
  def get_type_display_name("announcement"), do: "公告"
  def get_type_display_name("notification"), do: "通知"
  def get_type_display_name(_), do: "通信"

  # 私有函数 - 解析日期时间
  defp parse_datetime(nil), do: nil
  defp parse_datetime(""), do: nil
  defp parse_datetime(datetime_str) when is_binary(datetime_str) do
    case DateTime.from_iso8601(datetime_str <> ":00Z") do
      {:ok, datetime, _} -> datetime
      _ -> nil
    end
  end
  defp parse_datetime(_), do: nil

  # 私有函数 - 获取优先级显示名称
  defp get_priority_display_name("low"), do: "低"
  defp get_priority_display_name("medium"), do: "中"
  defp get_priority_display_name("high"), do: "高"
  defp get_priority_display_name("urgent"), do: "紧急"

  # 私有函数 - 获取接收者显示名称
  defp get_recipient_display_name("all"), do: "所有用户"
  defp get_recipient_display_name("user"), do: "指定用户"
  defp get_recipient_display_name("admin"), do: "管理员"
  defp get_recipient_display_name(_), do: "所有用户"
end
