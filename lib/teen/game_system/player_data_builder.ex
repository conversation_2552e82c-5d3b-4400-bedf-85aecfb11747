defmodule Cypridina.Teen.GameSystem.PlayerData do
  @moduledoc """
  玩家数据结构体

  定义统一的玩家数据结构，提供积分管理接口。
  """

  @enforce_keys [:user_id, :numeric_id, :user, :joined_at, :is_ready, :is_robot]
  defstruct [
    :user_id,
    :numeric_id,
    :user,
    :joined_at,
    :is_ready,
    :is_robot
  ]

  @type t :: %__MODULE__{
          user_id: String.t() | integer(),
          numeric_id: integer(),
          user: map(),
          joined_at: DateTime.t(),
          is_ready: boolean(),
          is_robot: boolean()
        }

  # 重载数据
  def reload(state, %__MODULE__{} = player) do
    state.players[player.numeric_id]
  end

  @doc """
  获取玩家积分
  支持向后兼容，对于真实玩家从Ledger系统获取实时余额
  """
  def get_points(%__MODULE__{} = player) do
    if player.is_robot do
      # 机器人使用本地存储的积分
      Map.get(player.user, :points, 0)
    else
      # 真实玩家从账户系统获取实时余额
      Cypridina.Accounts.get_user_points(player.user_id)
    end
  end

  # 向后兼容：处理旧的 Map 格式
  def get_points(%{user: user} = _player) when is_map(user) do
    Map.get(user, :points, 0)
  end

  # 处理其他格式
  def get_points(_player), do: 0

  @doc """
  增加玩家积分
  对于真实玩家使用Ledger系统，机器人使用本地计算
  """
  def add_points(%__MODULE__{} = player, amount) when is_number(amount) and amount >= 0 do
    if player.is_robot do
      # 机器人使用本地计算
      current_points = Map.get(player.user, :points, 0)
      new_points = current_points + amount
      updated_user = Map.put(player.user, :points, new_points)
      %{player | user: updated_user}
    else
      # 真实玩家使用Ledger系统
      case Cypridina.Accounts.add_points(player.user_id, amount, [
        transaction_type: :game_win,
        description: "游戏奖励",
        metadata: %{game_type: "teen_patti", player_id: player.user_id}
      ]) do
        %{points: _new_balance} ->
          # 更新本地缓存
          new_points = Cypridina.Accounts.get_user_points(player.user_id)
          updated_user = Map.put(player.user, :points, new_points)
          %{player | user: updated_user}
        {:error, _} ->
          # 如果失败，保持原状
          player
      end
    end
  end

  @doc """
  扣除玩家积分
  对于真实玩家使用Ledger系统，机器人使用本地计算
  """
  def subtract_points(%__MODULE__{} = player, amount) when is_number(amount) and amount >= 0 do
    if player.is_robot do
      # 机器人使用本地计算
      current_points = Map.get(player.user, :points, 0)
      new_points = max(0, current_points - amount)
      updated_user = Map.put(player.user, :points, new_points)
      %{player | user: updated_user}
    else
      # 真实玩家使用Ledger系统
      case Cypridina.Accounts.subtract_points(player.user_id, amount, [
        transaction_type: :game_bet,
        description: "游戏消费",
        metadata: %{game_type: "teen_patti", player_id: player.user_id}
      ]) do
        %{points: _new_balance} ->
          # 更新本地缓存
          new_points = Cypridina.Accounts.get_user_points(player.user_id)
          updated_user = Map.put(player.user, :points, new_points)
          %{player | user: updated_user}
        {:error, _} ->
          # 如果失败，保持原状
          player
      end
    end
  end



  @doc """
  设置玩家积分
  仅用于机器人或初始化，真实玩家应使用add_points/subtract_points
  """
  def set_points(%__MODULE__{} = player, amount) when is_number(amount) and amount >= 0 do
    updated_user = Map.put(player.user, :points, amount)
    %{player | user: updated_user}
  end

  @doc """
  游戏投注 - 扣除积分
  """
  def game_bet(%__MODULE__{} = player, amount, opts \\ []) when is_number(amount) and amount >= 0 do
    if player.is_robot do
      # 机器人使用本地计算
      subtract_points(player, amount)
    else
      # 真实玩家使用Ledger系统的游戏投注
      case Cypridina.Accounts.game_bet(player.user_id, amount, opts) do
        {:ok, _result} ->
          # 更新本地缓存
          new_points = get_points(player)
          updated_user = Map.put(player.user, :points, new_points)
          %{player | user: updated_user}
        {:error, _} ->
          # 如果失败，保持原状
          player
      end
    end
  end

  @doc """
  游戏获奖 - 增加积分
  """
  def game_win(%__MODULE__{} = player, amount, opts \\ []) when is_number(amount) and amount >= 0 do
    if player.is_robot do
      # 机器人使用本地计算
      add_points(player, amount)
    else
      # 真实玩家使用Ledger系统的游戏获奖
      case Cypridina.Accounts.game_win(player.user_id, amount, opts) do
        {:ok, _result} ->
          # 更新本地缓存
          new_points = get_points(player)
          updated_user = Map.put(player.user, :points, new_points)
          %{player | user: updated_user}
        {:error, _} ->
          # 如果失败，保持原状
          player
      end
    end
  end

  @doc """
  检查玩家是否为机器人
  """
  def is_robot?(%__MODULE__{} = player) do
    player.is_robot or (is_integer(player.numeric_id) and player.numeric_id < 0)
  end

  @doc """
  获取玩家显示名称
  """
  def get_display_name(%__MODULE__{} = player) do
    Map.get(player.user, :nickname, "玩家#{player.numeric_id}")
  end

  @doc """
  格式化玩家数据为客户端期望的格式
  """
  def format_for_client(%__MODULE__{} = player) do
    points = get_points(player)

    %{
      "playerid" => player.numeric_id,
      "nickname" => get_display_name(player),
      "name" => get_display_name(player),
      "coin" => points,
      "playercoin" => points,
      "points" => points,
      "headid" => Map.get(player.user, :avatar_id, 1),
      "wxheadurl" => Map.get(player.user, :avatar_url, ""),
      "level" => Map.get(player.user, :level, 1),
      "is_robot" => is_robot?(player)
    }
  end

  @doc """
  获取机器人AI数据
  支持向后兼容
  """
  def get_robot_ai_data(%__MODULE__{} = player) do
    if is_robot?(player) do
      Map.get(player.user, :robot_ai, %{})
    else
      nil
    end
  end

  # 向后兼容：处理旧的 Map 格式
  def get_robot_ai_data(%{user: user, is_robot: is_robot} = _player)
      when is_map(user) and is_boolean(is_robot) do
    if is_robot do
      Map.get(user, :robot_ai, %{})
    else
      nil
    end
  end

  # 处理其他格式
  def get_robot_ai_data(_player), do: nil

  @doc """
  获取机器人特定AI属性
  """
  def get_robot_ai_attribute(%__MODULE__{} = player, attribute) do
    case get_robot_ai_data(player) do
      nil -> nil
      ai_data -> Map.get(ai_data, attribute)
    end
  end
end

defmodule Cypridina.Teen.GameSystem.PlayerDataBuilder do
  @moduledoc """
  统一的玩家数据构造器

  提供统一的方法来构造真实玩家和机器人的数据结构，
  确保所有游戏房间使用相同的数据格式。
  """

  alias Cypridina.Teen.GameSystem.PlayerData

  @doc """
  统一的玩家数据构造方法 - 用于真实玩家和机器人

  ## 参数
  - `user_data`: 用户基础数据 (来自数据库或机器人生成)
  - `opts`: 可选参数
    - `:is_robot` - 是否为机器人 (默认: false)
    - `:numeric_id` - 数字ID (默认: 从user_data获取)
    - `:user_id` - 用户ID (默认: 从user_data获取或使用numeric_id)
    - `:points` - 积分数量 (默认: 从user_data.points获取)
    - `:joined_at` - 加入时间 (默认: 当前时间)
    - `:is_ready` - 是否准备 (默认: false)

  ## 返回
  PlayerData 结构体
  """
  def create_player_data(user_data, opts \\ []) do
    is_robot = Keyword.get(opts, :is_robot, false)
    numeric_id = Keyword.get(opts, :numeric_id, Map.get(user_data, :numeric_id))
    user_id = Keyword.get(opts, :user_id, Map.get(user_data, :id, numeric_id))
    joined_at = Keyword.get(opts, :joined_at, DateTime.utc_now())
    is_ready = Keyword.get(opts, :is_ready, false)

    # 统一的用户信息结构 - 只使用 points 作为玩家货币
    user_info = %{
      id: user_id,
      numeric_id: numeric_id,
      nickname: Map.get(user_data, :nickname, generate_default_nickname(numeric_id, is_robot)),
      avatar: Map.get(user_data, :avatar, generate_default_avatar(is_robot)),
      avatar_id: Map.get(user_data, :avatar_id, 1),
      avatar_url: Map.get(user_data, :avatar_url, ""),
      level: Map.get(user_data, :level, if(is_robot, do: :rand.uniform(10), else: 1)),
      points: determine_player_points(user_data, opts, is_robot),
      is_robot: is_robot,
      created_at: Map.get(user_data, :created_at, DateTime.utc_now())
    }

    # 如果是机器人，添加机器人特有属性到子结构中
    user_info_with_robot_data =
      if is_robot do
        robot_ai = build_robot_ai_data(user_data)
        Map.put(user_info, :robot_ai, robot_ai)
      else
        user_info
      end

    # 返回 PlayerData 结构体
    %PlayerData{
      user_id: user_id,
      numeric_id: numeric_id,
      user: user_info_with_robot_data,
      joined_at: joined_at,
      is_ready: is_ready,
      is_robot: is_robot
    }
  end

  @doc """
  为真实玩家创建数据结构
  """
  def create_real_player_data(user, opts \\ []) do
    create_player_data(
      user,
      Keyword.merge(
        [
          is_robot: false,
          numeric_id: user.numeric_id,
          user_id: user.id
        ],
        opts
      )
    )
  end

  @doc """
  为机器人创建数据结构
  """
  def create_robot_player_data(robot_data, opts \\ []) do
    create_player_data(
      robot_data,
      Keyword.merge(
        [
          is_robot: true,
          numeric_id: Map.get(robot_data, :numeric_id, Map.get(robot_data, :id)),
          user_id: Map.get(robot_data, :numeric_id, Map.get(robot_data, :id))
        ],
        opts
      )
    )
  end

  # 构建机器人AI数据子结构
  defp build_robot_ai_data(user_data) do
    %{
      # 行为特征
      aggression: Map.get(user_data, :aggression, :rand.uniform()),
      bet_frequency: Map.get(user_data, :bet_frequency, 0.8 + :rand.uniform() * 0.2),
      favorite_area: Map.get(user_data, :favorite_area),
      bet_style: Map.get(user_data, :bet_style),

      # 决策参数
      risk_tolerance: Map.get(user_data, :risk_tolerance, :rand.uniform()),
      follow_trend: Map.get(user_data, :follow_trend, :rand.uniform() > 0.5),

      # 状态信息
      total_bets: 0,
      total_wins: 0,
      total_losses: 0,
      current_streak: 0,
      last_bet_time: nil,

      # 学习参数
      learning_rate: Map.get(user_data, :learning_rate, 0.1),
      memory_depth: Map.get(user_data, :memory_depth, 10),

      # 元数据
      ai_version: "1.0",
      created_at: DateTime.utc_now(),
      last_updated: DateTime.utc_now()
    }
  end

  # 辅助函数：确定玩家积分数量
  defp determine_player_points(user_data, opts, is_robot) do
    cond do
      # 如果明确指定了points，使用指定值
      Keyword.has_key?(opts, :points) ->
        Keyword.get(opts, :points)

      # 机器人使用随机积分或指定积分
      is_robot ->
        Map.get(user_data, :points, Map.get(user_data, :balance, 10000 + :rand.uniform(50000)))

      # 真实玩家使用积分
      true ->
        Map.get(user_data, :points, 0)
    end
  end

  # 辅助函数：生成默认昵称
  defp generate_default_nickname(numeric_id, is_robot) do
    if is_robot do
      robot_names = [
        "智能玩家",
        "机器战士",
        "AI高手",
        "电脑专家",
        "智慧之星",
        "数字英雄",
        "算法大师",
        "逻辑王者",
        "智能助手",
        "机械战神",
        "电子精灵",
        "数码勇士",
        "智慧守护",
        "机器先锋",
        "算法骑士"
      ]

      Enum.random(robot_names)
    else
      "玩家#{numeric_id}"
    end
  end

  # 辅助函数：生成默认头像
  defp generate_default_avatar(is_robot) do
    if is_robot do
      "robot_#{rem(:rand.uniform(5), 5) + 1}"
    else
      "default_avatar"
    end
  end

  @doc """
  更新玩家积分 (委托给 PlayerData.set_points/2)
  """
  def update_player_points(%PlayerData{} = player, new_points) do
    PlayerData.set_points(player, new_points)
  end

  @doc """
  检查玩家是否为机器人 (委托给 PlayerData.is_robot?/1)
  支持向后兼容
  """
  def is_robot?(%PlayerData{} = player) do
    PlayerData.is_robot?(player)
  end

  # 向后兼容：处理旧的 Map 格式
  def is_robot?(%{is_robot: is_robot} = _player) when is_boolean(is_robot) do
    is_robot
  end

  def is_robot?(%{numeric_id: numeric_id} = _player) when is_integer(numeric_id) do
    numeric_id < 0
  end

  # 处理其他格式
  def is_robot?(_player), do: false

  @doc """
  获取玩家显示名称 (委托给 PlayerData.get_display_name/1)
  """
  def get_display_name(%PlayerData{} = player) do
    PlayerData.get_display_name(player)
  end

  @doc """
  获取玩家当前积分 (委托给 PlayerData.get_points/1)
  支持向后兼容
  """
  def get_player_points(%PlayerData{} = player) do
    PlayerData.get_points(player)
  end

  # 向后兼容：处理旧的 Map 格式
  def get_player_points(%{user: user} = _player) when is_map(user) do
    Map.get(user, :points, 0)
  end

  # 处理其他格式
  def get_player_points(_player), do: 0

  @doc """
  获取玩家当前金币 (已废弃，请使用 get_player_points/1)
  """
  def get_player_money(%PlayerData{} = player) do
    PlayerData.get_points(player)
  end

  @doc """
  格式化玩家数据为客户端期望的格式 (委托给 PlayerData.format_for_client/1)
  """
  def format_for_client(%PlayerData{} = player) do
    PlayerData.format_for_client(player)
  end

  @doc """
  获取机器人AI数据 (委托给 PlayerData.get_robot_ai_data/1)
  支持向后兼容
  """
  def get_robot_ai_data(%PlayerData{} = player) do
    PlayerData.get_robot_ai_data(player)
  end

  # 向后兼容：处理旧的 Map 格式
  def get_robot_ai_data(%{user: user} = _player) when is_map(user) do
    if Map.get(user, :is_robot, false) do
      Map.get(user, :robot_ai, %{})
    else
      nil
    end
  end

  # 处理其他格式
  def get_robot_ai_data(_player), do: nil

  @doc """
  更新机器人AI数据
  """
  def update_robot_ai_data(%PlayerData{} = player, ai_data_updates) do
    if PlayerData.is_robot?(player) do
      current_ai_data = PlayerData.get_robot_ai_data(player) || %{}

      updated_ai_data =
        Map.merge(current_ai_data, ai_data_updates)
        |> Map.put(:last_updated, DateTime.utc_now())

      updated_user = Map.put(player.user, :robot_ai, updated_ai_data)
      %{player | user: updated_user}
    else
      player
    end
  end

  @doc """
  获取机器人特定AI属性 (委托给 PlayerData.get_robot_ai_attribute/2)
  """
  def get_robot_ai_attribute(%PlayerData{} = player, attribute) do
    PlayerData.get_robot_ai_attribute(player, attribute)
  end

  @doc """
  更新机器人特定AI属性
  """
  def update_robot_ai_attribute(%PlayerData{} = player, attribute, value) do
    update_robot_ai_data(player, %{attribute => value})
  end

  @doc """
  更新机器人统计数据
  """
  def update_robot_stats(%PlayerData{} = player, bet_amount, win_amount) do
    if PlayerData.is_robot?(player) do
      ai_data = PlayerData.get_robot_ai_data(player)

      updated_stats = %{
        total_bets: Map.get(ai_data, :total_bets, 0) + 1,
        last_bet_time: DateTime.utc_now()
      }

      updated_stats =
        if win_amount > 0 do
          Map.merge(updated_stats, %{
            total_wins: Map.get(ai_data, :total_wins, 0) + 1,
            current_streak: max(0, Map.get(ai_data, :current_streak, 0)) + 1
          })
        else
          Map.merge(updated_stats, %{
            total_losses: Map.get(ai_data, :total_losses, 0) + 1,
            current_streak: min(0, Map.get(ai_data, :current_streak, 0)) - 1
          })
        end

      update_robot_ai_data(player, updated_stats)
    else
      player
    end
  end
end
