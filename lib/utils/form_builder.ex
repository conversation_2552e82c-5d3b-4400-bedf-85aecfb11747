defmodule Cypridina.Utils.FormBuilder do
  @moduledoc """
  表单构建器工具

  提供便捷的方法来构建表单配置，用于 FormModalComponent
  """

  @doc """
  创建新的表单配置
  """
  def new_form(config \\ %{}) do
    %{
      config: config,
      form_fields: []
    }
  end

  @doc """
  添加字段组
  """
  def add_field_group(form, title, opts \\ []) do
    field_group = %{
      title: title,
      icon: opts[:icon],
      grid: opts[:grid],
      fields: []
    }
    
    %{form | form_fields: form.form_fields ++ [field_group]}
  end

  @doc """
  添加字段到最后一个字段组
  """
  def add_field(form, field_config) do
    if length(form.form_fields) == 0 do
      form = add_field_group(form, nil)
    end
    
    [last_group | other_groups] = Enum.reverse(form.form_fields)
    updated_group = %{last_group | fields: last_group.fields ++ [field_config]}
    updated_groups = Enum.reverse([updated_group | other_groups])
    
    %{form | form_fields: updated_groups}
  end

  @doc """
  添加文本输入字段
  """
  def add_text_field(form, name, label, opts \\ []) do
    field = %{
      type: :text,
      name: name,
      label: label,
      icon: opts[:icon],
      placeholder: opts[:placeholder],
      value: opts[:value],
      required: opts[:required] || false,
      maxlength: opts[:maxlength],
      help_text: opts[:help_text],
      wrapper_class: opts[:wrapper_class],
      class: opts[:class],
      input_type: opts[:input_type] || "text"
    }
    
    add_field(form, field)
  end

  @doc """
  添加文本域字段
  """
  def add_textarea_field(form, name, label, opts \\ []) do
    field = %{
      type: :textarea,
      name: name,
      label: label,
      icon: opts[:icon],
      placeholder: opts[:placeholder],
      value: opts[:value],
      required: opts[:required] || false,
      maxlength: opts[:maxlength],
      rows: opts[:rows] || 4,
      help_text: opts[:help_text],
      wrapper_class: opts[:wrapper_class],
      class: opts[:class]
    }
    
    add_field(form, field)
  end

  @doc """
  添加下拉选择字段
  """
  def add_select_field(form, name, label, options, opts \\ []) do
    field = %{
      type: :select,
      name: name,
      label: label,
      icon: opts[:icon],
      options: options,
      value: opts[:value],
      required: opts[:required] || false,
      help_text: opts[:help_text],
      wrapper_class: opts[:wrapper_class],
      class: opts[:class]
    }
    
    add_field(form, field)
  end

  @doc """
  添加复选框字段
  """
  def add_checkbox_field(form, name, label, opts \\ []) do
    field = %{
      type: :checkbox,
      name: name,
      label: label,
      value: opts[:value],
      checked: opts[:checked] || false,
      checkbox_value: opts[:checkbox_value] || "true",
      help_text: opts[:help_text],
      wrapper_class: opts[:wrapper_class]
    }
    
    add_field(form, field)
  end

  @doc """
  添加隐藏字段
  """
  def add_hidden_field(form, name, value) do
    field = %{
      type: :hidden,
      name: name,
      value: value
    }
    
    add_field(form, field)
  end

  @doc """
  构建系统通信表单配置
  """
  def build_communication_form(type, form_data \\ %{}, is_edit \\ false) do
    config = get_communication_config(type, is_edit)
    
    new_form(config)
    |> add_hidden_field("communication[type]", type)
    |> add_field_group("基本信息", icon: "fas fa-info-circle")
    |> add_text_field("communication[title]", "标题", 
         icon: "fas fa-heading",
         placeholder: "请输入标题...",
         value: form_data[:title],
         required: true,
         maxlength: 200,
         help_text: "最多200个字符")
    |> add_textarea_field("communication[content]", "内容",
         icon: "fas fa-align-left", 
         placeholder: "请输入详细内容...",
         value: form_data[:content],
         required: true,
         maxlength: 2000,
         rows: 6,
         help_text: "最多2000个字符")
    |> maybe_add_recipient_fields(type, form_data)
    |> add_field_group("高级设置", icon: "fas fa-cogs", grid: "grid-cols-1 md:grid-cols-2")
    |> add_select_field("communication[priority]", "优先级",
         [
           %{value: "low", label: "🟢 低优先级"},
           %{value: "medium", label: "🟡 中优先级"}, 
           %{value: "high", label: "🔴 高优先级"}
         ],
         icon: "fas fa-flag",
         value: form_data[:priority] || "medium")
    |> add_checkbox_field("communication[active]", "立即启用",
         checked: form_data[:active] != false,
         help_text: "勾选后立即生效",
         wrapper_class: "flex items-center h-full")
  end

  # 根据类型添加接收者字段
  defp maybe_add_recipient_fields(form, type, form_data) when type in ["message", "notification"] do
    form
    |> add_field_group("接收者设置", icon: "fas fa-users")
    |> add_text_field("communication[recipient]", "接收者",
         icon: "fas fa-user",
         placeholder: get_recipient_placeholder(type),
         value: form_data[:recipient],
         required: type == "message",
         maxlength: 100,
         help_text: get_recipient_help_text(type))
  end
  
  defp maybe_add_recipient_fields(form, _type, _form_data), do: form

  # 获取接收者占位符文本
  defp get_recipient_placeholder("message"), do: "请输入用户ID或用户名"
  defp get_recipient_placeholder("notification"), do: "用户ID或用户名，留空表示发送给所有用户"
  defp get_recipient_placeholder(_), do: "接收者"

  # 获取接收者帮助文本
  defp get_recipient_help_text("message"), do: "消息类型必须指定接收者"
  defp get_recipient_help_text("notification"), do: "留空表示发送给所有用户"
  defp get_recipient_help_text(_), do: nil

  # 获取通信类型配置
  defp get_communication_config("message", is_edit) do
    %{
      title: if(is_edit, do: "编辑系统消息", else: "创建系统消息"),
      subtitle: "发送个人消息给特定用户",
      icon: "fas fa-envelope",
      theme: "blue",
      submit_text: if(is_edit, do: "保存修改", else: "创建并发布")
    }
  end

  defp get_communication_config("announcement", is_edit) do
    %{
      title: if(is_edit, do: "编辑系统公告", else: "创建系统公告"),
      subtitle: "发布系统公告给所有用户", 
      icon: "fas fa-bullhorn",
      theme: "green",
      submit_text: if(is_edit, do: "保存修改", else: "创建并发布")
    }
  end

  defp get_communication_config("notification", is_edit) do
    %{
      title: if(is_edit, do: "编辑系统通知", else: "创建系统通知"),
      subtitle: "推送系统通知",
      icon: "fas fa-bell", 
      theme: "purple",
      submit_text: if(is_edit, do: "保存修改", else: "创建并发布")
    }
  end

  defp get_communication_config(_, is_edit) do
    %{
      title: if(is_edit, do: "编辑", else: "创建"),
      subtitle: "填写详细信息",
      icon: "fas fa-plus",
      theme: "gray",
      submit_text: if(is_edit, do: "保存修改", else: "保存")
    }
  end
end
