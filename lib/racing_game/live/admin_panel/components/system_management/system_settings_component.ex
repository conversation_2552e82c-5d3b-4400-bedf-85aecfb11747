defmodule RacingGame.Live.AdminPanel.SystemSettingsComponent do
  @moduledoc """
  系统设置管理组件 - 仅限管理员使用
  """
  use CypridinaWeb, :live_component

  alias CypridinaWeb.AuthHelper
  alias Cypridina.Accounts.User
  alias CypridinaWeb.Components.AdminButtonGroup
  require Ash.Query
  require Logger

  def update(assigns, socket) do
    socket = assign(socket, assigns)

    if AuthHelper.has_permission?(socket.assigns.current_user, :admin) do
      socket =
        socket
        |> assign_defaults()
        |> load_system_settings()

      {:ok, socket}
    else
      # 非管理员用户显示无权限信息
      socket =
        socket
        |> assign_defaults()
        |> assign(:unauthorized, true)
      {:ok, socket}
    end
  end

  defp assign_defaults(socket) do
    socket
    |> assign(:settings, %{})
    |> assign(:show_edit_modal, false)
    |> assign(:current_setting, nil)
    |> assign(:form, %{key: "", value: "", description: ""})
    |> assign(:loading, false)
    |> assign(:unauthorized, false)
  end

  defp load_system_settings(socket) do
    # 加载系统设置
    settings = %{
      "site_name" => "赛马游戏平台",
      "site_description" => "专业的在线赛马游戏平台",
      "maintenance_mode" => false,
      "registration_enabled" => true,
      "max_bet_amount" => 10000,
      "min_bet_amount" => 10,
      "commission_rate" => 0.05,
      "max_users_online" => 1000,
      "session_timeout" => 3600,
      "backup_enabled" => true,
      "backup_interval" => 24,
      "log_retention_days" => 30
    }

    assign(socket, :settings, settings)
  end

  def handle_event("edit_setting", %{"key" => key}, socket) do
    setting_value = Map.get(socket.assigns.settings, key, "")
    form = %{
      key: key,
      value: to_string(setting_value),
      description: get_setting_description(key)
    }

    socket =
      socket
      |> assign(:show_edit_modal, true)
      |> assign(:current_setting, key)
      |> assign(:form, form)

    {:noreply, socket}
  end

  def handle_event("save_setting", %{"form" => form_params}, socket) do
    key = form_params["key"]
    value = form_params["value"]

    # 这里应该保存到数据库或配置文件
    # 暂时只更新内存中的设置
    updated_settings = Map.put(socket.assigns.settings, key, parse_setting_value(key, value))

    socket =
      socket
      |> assign(:settings, updated_settings)
      |> assign(:show_edit_modal, false)
      |> put_flash(:info, "设置已更新")

    {:noreply, socket}
  end

  def handle_event("cancel_edit", _params, socket) do
    socket = assign(socket, :show_edit_modal, false)
    {:noreply, socket}
  end

  def handle_event("refresh", _params, socket) do
    socket = load_system_settings(socket)
    {:noreply, socket}
  end

  defp get_setting_description(key) do
    case key do
      "site_name" -> "网站名称"
      "site_description" -> "网站描述"
      "maintenance_mode" -> "维护模式（true/false）"
      "registration_enabled" -> "是否允许注册（true/false）"
      "max_bet_amount" -> "最大下注金额"
      "min_bet_amount" -> "最小下注金额"
      "commission_rate" -> "抽水比例（0-1之间的小数）"
      "max_users_online" -> "最大在线用户数"
      "session_timeout" -> "会话超时时间（秒）"
      "backup_enabled" -> "是否启用备份（true/false）"
      "backup_interval" -> "备份间隔（小时）"
      "log_retention_days" -> "日志保留天数"
      _ -> "系统设置项"
    end
  end

  defp parse_setting_value(key, value) do
    case key do
      k when k in ["maintenance_mode", "registration_enabled", "backup_enabled"] ->
        value in ["true", "1", "yes"]
      k when k in ["max_bet_amount", "min_bet_amount", "max_users_online", "session_timeout", "backup_interval", "log_retention_days"] ->
        case Integer.parse(value) do
          {int, _} -> int
          :error -> 0
        end
      "commission_rate" ->
        case Float.parse(value) do
          {float, _} -> float
          :error -> 0.0
        end
      _ ->
        value
    end
  end

  def render(assigns) do
    ~H"""
    <div class="p-6 bg-white rounded-lg shadow-md">
      <%= if @unauthorized do %>
        <h3 class="text-xl font-bold text-red-600 mb-4">权限不足</h3>
        <p class="text-gray-700">您没有访问系统设置的权限。只有管理员可以查看此页面。</p>
      <% else %>
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-xl font-bold text-gray-800">系统设置管理</h3>
          <AdminButtonGroup.refresh_button
            action="refresh"
            target={@myself}
          />
        </div>

        <!-- 设置列表 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <%= for {key, value} <- @settings do %>
            <div class="p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors">
              <div class="flex justify-between items-start">
                <div class="flex-1">
                  <h4 class="text-lg font-medium text-gray-800"><%= get_setting_description(key) %></h4>
                  <p class="text-sm text-gray-500 mt-1">键名: <%= key %></p>
                  <div class="mt-2">
                    <span class="text-sm text-gray-600">当前值: </span>
                    <span class="font-mono text-sm bg-gray-100 px-2 py-1 rounded">
                      <%= inspect(value) %>
                    </span>
                  </div>
                </div>
                <AdminButtonGroup.edit_button
                  action="edit_setting"
                  target={@myself}
                  id={key}
                  text="编辑"
                  class="ml-4 px-3 py-1 text-sm"
                />
              </div>
            </div>
          <% end %>
        </div>

        <!-- 编辑设置模态框 -->
        <%= if @show_edit_modal do %>
          <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg p-6 w-full max-w-md">
              <h3 class="text-lg font-bold text-gray-800 mb-4">编辑设置</h3>

              <form phx-submit="save_setting" phx-target={@myself}>
                <input type="hidden" name="form[key]" value={@form.key} />

                <div class="mb-4">
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    设置名称
                  </label>
                  <p class="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                    <%= @form.description %>
                  </p>
                </div>

                <div class="mb-4">
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    设置值
                  </label>
                  <input
                    type="text"
                    name="form[value]"
                    value={@form.value}
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>

                <div class="flex justify-end space-x-4 pt-4 border-t border-gray-200">
                  <AdminButtonGroup.cancel_button
                    text="取消"
                    action="cancel_edit"
                    target={@myself}
                  />
                  <AdminButtonGroup.save_button
                    text="保存"
                    type="submit"
                  />
                </div>
              </form>
            </div>
          </div>
        <% end %>
      <% end %>
    </div>
    """
  end
end
