defmodule RacingGame.Live.DialogDemoLive do
  @moduledoc """
  动态对话框演示页面

  展示如何使用动态对话框组件的完整示例。
  """

  use Phoenix.LiveView
  import RacingGame.Utils.DynamicDialog
  import RacingGame.Utils.DialogHelper

  def mount(_params, _session, socket) do
    # 初始化对话框状态
    socket = init_dialogs(socket, [
      :delete_confirm,
      :save_confirm,
      :info_dialog,
      :warning_dialog,
      :error_dialog,
      :success_dialog,
      :custom_dialog
    ])

    socket = assign(socket,
      page_title: "美化对话框演示",
      demo_data: %{
        selected_id: nil,
        operation_result: nil,
        last_action: nil
      }
    )

    {:ok, socket}
  end

  def handle_event("show_delete_dialog", %{"id" => id}, socket) do
    socket = show_confirm_dialog(socket, :delete_confirm,
      title: "确认删除记录",
      message: "您确定要删除ID为 #{id} 的记录吗？此操作不可撤销，请谨慎操作。",
      confirm_action: "confirm_delete",
      confirm_data: %{"id" => id},
      danger: true
    )
    {:noreply, socket}
  end

  def handle_event("show_save_dialog", _params, socket) do
    socket = show_preset_dialog(socket, :save_confirm, :save_confirm,
      message: "您确定要保存当前的所有更改吗？保存后将无法撤销。",
      confirm_action: "confirm_save"
    )
    {:noreply, socket}
  end

  def handle_event("show_info_dialog", _params, socket) do
    socket = show_info_dialog(socket, :info_dialog,
      title: "系统信息",
      message: "这是一个信息提示对话框。用于向用户展示重要信息或操作结果。",
      confirm_action: "hide_dialog",
      confirm_data: %{"dialog" => "info_dialog"}
    )
    {:noreply, socket}
  end

  def handle_event("show_warning_dialog", _params, socket) do
    socket = show_warning_dialog(socket, :warning_dialog,
      title: "警告提示",
      message: "检测到系统配置可能存在问题，建议您检查相关设置。继续操作可能会影响系统稳定性。",
      confirm_text: "继续",
      confirm_action: "continue_operation",
      cancel_text: "取消"
    )
    {:noreply, socket}
  end

  def handle_event("show_error_dialog", _params, socket) do
    socket = show_error_dialog(socket, :error_dialog,
      title: "操作失败",
      message: "抱歉，操作执行失败。错误代码：E001。请检查网络连接或联系系统管理员。",
      confirm_action: "hide_dialog",
      confirm_data: %{"dialog" => "error_dialog"}
    )
    {:noreply, socket}
  end

  def handle_event("show_success_dialog", _params, socket) do
    socket = show_success_dialog(socket, :success_dialog,
      title: "操作成功",
      message: "恭喜！您的操作已成功完成。所有更改已保存并生效。",
      confirm_action: "hide_dialog",
      confirm_data: %{"dialog" => "success_dialog"}
    )
    {:noreply, socket}
  end

  def handle_event("show_custom_dialog", _params, socket) do
    socket = show_custom_dialog(socket, :custom_dialog,
      title: "自定义对话框",
      message: "这是一个自定义样式的对话框，您可以根据需要调整大小、颜色和图标。",
      size: "lg",
      icon: "fas fa-cog",
      confirm_text: "应用设置",
      confirm_action: "apply_settings"
    )
    {:noreply, socket}
  end

  # 确认操作处理
  def handle_event("confirm_delete", %{"id" => id}, socket) do
    # 模拟删除操作
    socket =
      socket
      |> hide_dialog(:delete_confirm)
      |> assign(:demo_data, %{socket.assigns.demo_data |
          operation_result: "已删除记录 ID: #{id}",
          last_action: "delete"
        })
      |> show_success_dialog(:success_dialog,
          title: "删除成功",
          message: "记录 ID: #{id} 已成功删除！",
          confirm_action: "hide_dialog",
          confirm_data: %{"dialog" => "success_dialog"}
        )
    {:noreply, socket}
  end

  def handle_event("confirm_save", _params, socket) do
    # 模拟保存操作
    socket =
      socket
      |> hide_dialog(:save_confirm)
      |> assign(:demo_data, %{socket.assigns.demo_data |
          operation_result: "数据已保存",
          last_action: "save"
        })
      |> show_success_dialog(:success_dialog,
          title: "保存成功",
          message: "所有更改已成功保存！",
          confirm_action: "hide_dialog",
          confirm_data: %{"dialog" => "success_dialog"}
        )
    {:noreply, socket}
  end

  def handle_event("continue_operation", _params, socket) do
    socket =
      socket
      |> hide_dialog(:warning_dialog)
      |> assign(:demo_data, %{socket.assigns.demo_data |
          operation_result: "继续执行操作",
          last_action: "continue"
        })
    {:noreply, socket}
  end

  def handle_event("apply_settings", _params, socket) do
    socket =
      socket
      |> hide_dialog(:custom_dialog)
      |> assign(:demo_data, %{socket.assigns.demo_data |
          operation_result: "设置已应用",
          last_action: "apply_settings"
        })
    {:noreply, socket}
  end

  # 通用隐藏对话框处理
  def handle_event("hide_dialog", %{"dialog" => dialog_name}, socket) do
    dialog_atom = String.to_existing_atom(dialog_name)
    socket = hide_dialog(socket, dialog_atom)
    {:noreply, socket}
  end

  def handle_event("hide_dialog", _params, socket) do
    # 隐藏所有对话框
    socket =
      socket
      |> hide_dialog(:delete_confirm)
      |> hide_dialog(:save_confirm)
      |> hide_dialog(:info_dialog)
      |> hide_dialog(:warning_dialog)
      |> hide_dialog(:error_dialog)
      |> hide_dialog(:success_dialog)
      |> hide_dialog(:custom_dialog)
    {:noreply, socket}
  end

  def render(assigns) do
    ~H"""
    <div class="min-h-screen bg-gradient-to-br from-gray-100 via-blue-50 to-indigo-100">
      <!-- 页面头部 -->
      <div class="bg-white shadow-lg border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between items-center py-6">
            <div class="flex items-center">
              <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mr-4">
                <i class="fas fa-comments text-white text-xl"></i>
              </div>
              <div>
                <h1 class="text-3xl font-bold text-gray-900">美化对话框演示</h1>
                <p class="text-gray-600 mt-1">体验全新设计的对话框组件</p>
              </div>
            </div>
            <div class="flex items-center space-x-4">
              <div class="text-right">
                <p class="text-sm text-gray-500">版本</p>
                <p class="text-lg font-semibold text-gray-900">v2.0</p>
              </div>
              <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                <i class="fas fa-check text-green-600"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        <!-- 操作结果显示 -->
        <%= if @demo_data.operation_result do %>
          <div class="mb-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div class="flex items-center">
              <i class="fas fa-info-circle text-blue-600 mr-3"></i>
              <div>
                <p class="text-blue-800 font-medium">最后操作结果</p>
                <p class="text-blue-700"><%= @demo_data.operation_result %></p>
                <p class="text-blue-600 text-sm">操作类型：<%= @demo_data.last_action %></p>
              </div>
            </div>
          </div>
        <% end %>

        <!-- 演示按钮区域 -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h2 class="text-xl font-semibold text-gray-900 mb-6">对话框类型演示</h2>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- 删除确认对话框 -->
            <button
              phx-click="show_delete_dialog"
              phx-value-id="12345"
              class="flex flex-col items-center p-4 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 transition-colors"
            >
              <i class="fas fa-trash text-2xl text-red-600 mb-2"></i>
              <span class="text-red-800 font-medium">删除确认</span>
              <span class="text-red-600 text-sm text-center">危险操作确认对话框</span>
            </button>

            <!-- 保存确认对话框 -->
            <button
              phx-click="show_save_dialog"
              class="flex flex-col items-center p-4 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors"
            >
              <i class="fas fa-save text-2xl text-blue-600 mb-2"></i>
              <span class="text-blue-800 font-medium">保存确认</span>
              <span class="text-blue-600 text-sm text-center">保存操作确认对话框</span>
            </button>

            <!-- 信息提示对话框 -->
            <button
              phx-click="show_info_dialog"
              class="flex flex-col items-center p-4 bg-gray-50 border border-gray-200 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <i class="fas fa-info-circle text-2xl text-gray-600 mb-2"></i>
              <span class="text-gray-800 font-medium">信息提示</span>
              <span class="text-gray-600 text-sm text-center">一般信息展示对话框</span>
            </button>

            <!-- 警告提示对话框 -->
            <button
              phx-click="show_warning_dialog"
              class="flex flex-col items-center p-4 bg-yellow-50 border border-yellow-200 rounded-lg hover:bg-yellow-100 transition-colors"
            >
              <i class="fas fa-exclamation-triangle text-2xl text-yellow-600 mb-2"></i>
              <span class="text-yellow-800 font-medium">警告提示</span>
              <span class="text-yellow-600 text-sm text-center">警告信息提示对话框</span>
            </button>

            <!-- 错误提示对话框 -->
            <button
              phx-click="show_error_dialog"
              class="flex flex-col items-center p-4 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 transition-colors"
            >
              <i class="fas fa-times-circle text-2xl text-red-600 mb-2"></i>
              <span class="text-red-800 font-medium">错误提示</span>
              <span class="text-red-600 text-sm text-center">错误信息展示对话框</span>
            </button>

            <!-- 成功提示对话框 -->
            <button
              phx-click="show_success_dialog"
              class="flex flex-col items-center p-4 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors"
            >
              <i class="fas fa-check-circle text-2xl text-green-600 mb-2"></i>
              <span class="text-green-800 font-medium">成功提示</span>
              <span class="text-green-600 text-sm text-center">成功信息展示对话框</span>
            </button>

            <!-- 自定义对话框 -->
            <button
              phx-click="show_custom_dialog"
              class="flex flex-col items-center p-4 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors"
            >
              <i class="fas fa-cog text-2xl text-purple-600 mb-2"></i>
              <span class="text-purple-800 font-medium">自定义对话框</span>
              <span class="text-purple-600 text-sm text-center">自定义样式对话框</span>
            </button>

            <!-- 关闭所有对话框 -->
            <button
              phx-click="hide_dialog"
              class="flex flex-col items-center p-4 bg-gray-50 border border-gray-300 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <i class="fas fa-times text-2xl text-gray-500 mb-2"></i>
              <span class="text-gray-700 font-medium">关闭所有</span>
              <span class="text-gray-500 text-sm text-center">关闭所有打开的对话框</span>
            </button>
          </div>
        </div>

        <!-- 使用说明 -->
        <div class="bg-white rounded-lg shadow-lg p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">使用说明</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 class="text-lg font-medium text-gray-800 mb-3">功能特点</h3>
              <ul class="space-y-2 text-gray-600">
                <li class="flex items-start">
                  <i class="fas fa-check text-green-500 mr-2 mt-1"></i>
                  支持多种对话框类型（确认、信息、警告、错误、成功）
                </li>
                <li class="flex items-start">
                  <i class="fas fa-check text-green-500 mr-2 mt-1"></i>
                  可自定义标题、内容、按钮文字和图标
                </li>
                <li class="flex items-start">
                  <i class="fas fa-check text-green-500 mr-2 mt-1"></i>
                  支持键盘 ESC 键和点击遮罩关闭
                </li>
                <li class="flex items-start">
                  <i class="fas fa-check text-green-500 mr-2 mt-1"></i>
                  响应式设计，适配各种屏幕尺寸
                </li>
                <li class="flex items-start">
                  <i class="fas fa-check text-green-500 mr-2 mt-1"></i>
                  提供预设配置和完全自定义两种使用方式
                </li>
              </ul>
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-800 mb-3">操作指南</h3>
              <ul class="space-y-2 text-gray-600">
                <li class="flex items-start">
                  <i class="fas fa-mouse-pointer text-blue-500 mr-2 mt-1"></i>
                  点击上方按钮可以测试不同类型的对话框
                </li>
                <li class="flex items-start">
                  <i class="fas fa-keyboard text-blue-500 mr-2 mt-1"></i>
                  按 ESC 键可以关闭当前打开的对话框
                </li>
                <li class="flex items-start">
                  <i class="fas fa-hand-pointer text-blue-500 mr-2 mt-1"></i>
                  点击遮罩区域也可以关闭对话框
                </li>
                <li class="flex items-start">
                  <i class="fas fa-eye text-blue-500 mr-2 mt-1"></i>
                  观察不同类型对话框的颜色和图标差异
                </li>
                <li class="flex items-start">
                  <i class="fas fa-code text-blue-500 mr-2 mt-1"></i>
                  查看源代码了解具体的实现方法
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- 所有对话框组件 -->
        <.dynamic_dialog
          id="delete-confirm-dialog"
          type={get_dialog_state(@socket, :delete_confirm).type}
          title={get_dialog_state(@socket, :delete_confirm).title}
          message={get_dialog_state(@socket, :delete_confirm).message}
          show={get_dialog_state(@socket, :delete_confirm).show}
          confirm_text={get_dialog_state(@socket, :delete_confirm).confirm_text}
          cancel_text={get_dialog_state(@socket, :delete_confirm).cancel_text}
          confirm_action={get_dialog_state(@socket, :delete_confirm).confirm_action}
          cancel_action="hide_dialog"
          target={@myself}
          danger={get_dialog_state(@socket, :delete_confirm).danger}
          size={get_dialog_state(@socket, :delete_confirm).size}
          closable={get_dialog_state(@socket, :delete_confirm).closable}
        />

        <.dynamic_dialog
          id="save-confirm-dialog"
          type={get_dialog_state(@socket, :save_confirm).type}
          title={get_dialog_state(@socket, :save_confirm).title}
          message={get_dialog_state(@socket, :save_confirm).message}
          show={get_dialog_state(@socket, :save_confirm).show}
          confirm_text={get_dialog_state(@socket, :save_confirm).confirm_text}
          cancel_text={get_dialog_state(@socket, :save_confirm).cancel_text}
          confirm_action={get_dialog_state(@socket, :save_confirm).confirm_action}
          cancel_action="hide_dialog"
          target={@myself}
          danger={get_dialog_state(@socket, :save_confirm).danger}
          size={get_dialog_state(@socket, :save_confirm).size}
          closable={get_dialog_state(@socket, :save_confirm).closable}
        />

        <.dynamic_dialog
          id="info-dialog"
          type={get_dialog_state(@socket, :info_dialog).type}
          title={get_dialog_state(@socket, :info_dialog).title}
          message={get_dialog_state(@socket, :info_dialog).message}
          show={get_dialog_state(@socket, :info_dialog).show}
          confirm_text={get_dialog_state(@socket, :info_dialog).confirm_text}
          cancel_text={get_dialog_state(@socket, :info_dialog).cancel_text}
          confirm_action={get_dialog_state(@socket, :info_dialog).confirm_action}
          cancel_action={get_dialog_state(@socket, :info_dialog).cancel_action}
          target={@myself}
          danger={get_dialog_state(@socket, :info_dialog).danger}
          size={get_dialog_state(@socket, :info_dialog).size}
          closable={get_dialog_state(@socket, :info_dialog).closable}
        />

        <.dynamic_dialog
          id="warning-dialog"
          type={get_dialog_state(@socket, :warning_dialog).type}
          title={get_dialog_state(@socket, :warning_dialog).title}
          message={get_dialog_state(@socket, :warning_dialog).message}
          show={get_dialog_state(@socket, :warning_dialog).show}
          confirm_text={get_dialog_state(@socket, :warning_dialog).confirm_text}
          cancel_text={get_dialog_state(@socket, :warning_dialog).cancel_text}
          confirm_action={get_dialog_state(@socket, :warning_dialog).confirm_action}
          cancel_action="hide_dialog"
          target={@myself}
          danger={get_dialog_state(@socket, :warning_dialog).danger}
          size={get_dialog_state(@socket, :warning_dialog).size}
          closable={get_dialog_state(@socket, :warning_dialog).closable}
        />

        <.dynamic_dialog
          id="error-dialog"
          type={get_dialog_state(@socket, :error_dialog).type}
          title={get_dialog_state(@socket, :error_dialog).title}
          message={get_dialog_state(@socket, :error_dialog).message}
          show={get_dialog_state(@socket, :error_dialog).show}
          confirm_text={get_dialog_state(@socket, :error_dialog).confirm_text}
          cancel_text={get_dialog_state(@socket, :error_dialog).cancel_text}
          confirm_action={get_dialog_state(@socket, :error_dialog).confirm_action}
          cancel_action={get_dialog_state(@socket, :error_dialog).cancel_action}
          target={@myself}
          danger={get_dialog_state(@socket, :error_dialog).danger}
          size={get_dialog_state(@socket, :error_dialog).size}
          closable={get_dialog_state(@socket, :error_dialog).closable}
        />

        <.dynamic_dialog
          id="success-dialog"
          type={get_dialog_state(@socket, :success_dialog).type}
          title={get_dialog_state(@socket, :success_dialog).title}
          message={get_dialog_state(@socket, :success_dialog).message}
          show={get_dialog_state(@socket, :success_dialog).show}
          confirm_text={get_dialog_state(@socket, :success_dialog).confirm_text}
          cancel_text={get_dialog_state(@socket, :success_dialog).cancel_text}
          confirm_action={get_dialog_state(@socket, :success_dialog).confirm_action}
          cancel_action={get_dialog_state(@socket, :success_dialog).cancel_action}
          target={@myself}
          danger={get_dialog_state(@socket, :success_dialog).danger}
          size={get_dialog_state(@socket, :success_dialog).size}
          closable={get_dialog_state(@socket, :success_dialog).closable}
        />

        <.dynamic_dialog
          id="custom-dialog"
          type={get_dialog_state(@socket, :custom_dialog).type}
          title={get_dialog_state(@socket, :custom_dialog).title}
          message={get_dialog_state(@socket, :custom_dialog).message}
          show={get_dialog_state(@socket, :custom_dialog).show}
          confirm_text={get_dialog_state(@socket, :custom_dialog).confirm_text}
          cancel_text={get_dialog_state(@socket, :custom_dialog).cancel_text}
          confirm_action={get_dialog_state(@socket, :custom_dialog).confirm_action}
          cancel_action="hide_dialog"
          target={@myself}
          danger={get_dialog_state(@socket, :custom_dialog).danger}
          size={get_dialog_state(@socket, :custom_dialog).size}
          closable={get_dialog_state(@socket, :custom_dialog).closable}
          icon="fas fa-cog"
        />
      </div>
    </div>
    """
  end
end
