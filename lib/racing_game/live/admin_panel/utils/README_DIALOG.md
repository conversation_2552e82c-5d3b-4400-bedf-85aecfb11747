# 动态对话提示框组件使用指南

## 概述

动态对话提示框组件提供了灵活、美观、易用的对话框解决方案，支持多种类型的提示和交互。

## 组件结构

### 1. DynamicDialog - 核心对话框组件
- 提供完整的对话框功能
- 支持多种类型和自定义样式
- 响应式设计，支持键盘和鼠标交互

### 2. DialogHelper - 辅助管理模块
- 简化对话框状态管理
- 提供预设配置
- 便捷的显示/隐藏方法

### 3. DialogDemoComponent - 演示组件
- 展示各种使用场景
- 可作为参考实现

## 快速开始

### 基本使用

```elixir
# 在 LiveView 组件中
defmodule MyComponent do
  use Phoenix.LiveComponent
  import RacingGame.Utils.DynamicDialog

  def render(assigns) do
    ~H"""
    <button phx-click="show_dialog" phx-target={@myself}>
      显示对话框
    </button>

    <.dynamic_dialog
      id="my-dialog"
      type="confirm"
      title="确认操作"
      message="您确定要执行此操作吗？"
      show={@show_dialog}
      confirm_action="confirm"
      cancel_action="cancel"
      target={@myself}
    />
    """
  end

  def handle_event("show_dialog", _, socket) do
    {:noreply, assign(socket, :show_dialog, true)}
  end

  def handle_event("confirm", _, socket) do
    # 执行确认操作
    {:noreply, assign(socket, :show_dialog, false)}
  end

  def handle_event("cancel", _, socket) do
    {:noreply, assign(socket, :show_dialog, false)}
  end
end
```

### 使用 DialogHelper 简化管理

```elixir
defmodule MyComponent do
  use Phoenix.LiveComponent
  import RacingGame.Utils.DynamicDialog
  import RacingGame.Utils.DialogHelper

  def mount(socket) do
    socket = init_dialogs(socket, [:confirm_delete, :show_info])
    {:ok, socket}
  end

  def handle_event("delete_item", %{"id" => id}, socket) do
    socket = show_confirm_dialog(socket, :confirm_delete,
      title: "确认删除",
      message: "您确定要删除此项目吗？",
      confirm_action: "do_delete",
      confirm_data: %{"id" => id},
      danger: true
    )
    {:noreply, socket}
  end

  def handle_event("do_delete", %{"id" => id}, socket) do
    # 执行删除
    socket = 
      socket
      |> hide_dialog(:confirm_delete)
      |> show_success_dialog(:show_info,
          title: "删除成功",
          message: "项目已成功删除！"
        )
    {:noreply, socket}
  end
end
```

## 对话框类型

### 1. 确认对话框 (confirm)
- 用于需要用户确认的操作
- 包含确认和取消按钮
- 适用于删除、保存等重要操作

### 2. 信息对话框 (info)
- 用于显示信息提示
- 通常只有确认按钮
- 适用于操作结果通知

### 3. 警告对话框 (warning)
- 用于显示警告信息
- 提醒用户注意潜在风险
- 黄色主题色

### 4. 错误对话框 (error)
- 用于显示错误信息
- 红色主题色
- 通常只有确认按钮

### 5. 成功对话框 (success)
- 用于显示成功信息
- 绿色主题色
- 通常只有确认按钮

## 属性说明

### 必需属性
- `id` - 对话框唯一标识符
- `show` - 是否显示对话框

### 常用属性
- `type` - 对话框类型 ("confirm", "info", "warning", "error", "success")
- `title` - 对话框标题
- `message` - 对话框消息内容
- `confirm_action` - 确认按钮事件名称
- `cancel_action` - 取消按钮事件名称
- `target` - 事件目标组件

### 样式属性
- `size` - 对话框大小 ("sm", "md", "lg", "xl")
- `danger` - 是否为危险操作（影响按钮颜色）
- `icon` - 自定义图标类名
- `closable` - 是否可通过ESC键或点击遮罩关闭

### 文本属性
- `confirm_text` - 确认按钮文字（默认"确定"）
- `cancel_text` - 取消按钮文字（默认"取消"）

## 预设配置

DialogHelper 提供了常用的预设配置：

```elixir
# 删除确认对话框
show_preset_dialog(socket, :delete_dialog, :delete_confirm,
  message: "您确定要删除这条记录吗？",
  confirm_action: "confirm_delete"
)

# 保存确认对话框
show_preset_dialog(socket, :save_dialog, :save_confirm,
  confirm_action: "confirm_save"
)

# 操作成功提示
show_preset_dialog(socket, :success_dialog, :operation_success,
  message: "操作已成功完成！"
)
```

## 简化版组件

对于简单场景，可以使用简化版组件：

```elixir
# 确认对话框
<.confirm_dialog
  id="simple-confirm"
  show={@show_dialog}
  title="确认删除"
  message="此操作不可撤销"
  on_confirm="delete"
  on_cancel="cancel"
  target={@myself}
  danger={true}
/>

# 信息对话框
<.info_dialog
  id="simple-info"
  show={@show_info}
  title="操作成功"
  message="数据已保存"
  on_confirm="close"
  target={@myself}
/>
```

## 最佳实践

### 1. 状态管理
- 使用 DialogHelper 管理多个对话框状态
- 在组件 mount 时初始化对话框
- 及时隐藏不需要的对话框

### 2. 用户体验
- 为危险操作使用 `danger={true}`
- 提供清晰的标题和消息
- 合理使用不同类型的对话框

### 3. 事件处理
- 确认操作后及时隐藏对话框
- 传递必要的数据给事件处理器
- 处理取消操作

### 4. 样式定制
- 根据内容选择合适的尺寸
- 使用自定义图标增强视觉效果
- 保持界面风格一致

## 示例场景

### 删除确认
```elixir
socket = show_confirm_dialog(socket, :delete_confirm,
  title: "确认删除",
  message: "您确定要删除「#{item.name}」吗？此操作不可撤销。",
  confirm_action: "confirm_delete",
  confirm_data: %{"id" => item.id},
  danger: true
)
```

### 操作结果通知
```elixir
socket = show_success_dialog(socket, :result_info,
  title: "保存成功",
  message: "您的更改已成功保存！",
  confirm_action: "close_dialog"
)
```

### 警告提示
```elixir
socket = show_warning_dialog(socket, :warning_dialog,
  title: "注意",
  message: "检测到未保存的更改，继续操作将丢失这些更改。",
  confirm_text: "继续",
  confirm_action: "continue_operation"
)
```

## 注意事项

1. **唯一ID**: 确保每个对话框有唯一的ID
2. **事件目标**: 正确设置 `target` 属性
3. **状态同步**: 及时更新对话框显示状态
4. **内存管理**: 避免创建过多未使用的对话框状态
5. **用户体验**: 不要同时显示多个对话框

## 扩展功能

### 自定义内容
```elixir
<.dynamic_dialog
  id="custom-dialog"
  show={@show_custom}
  title="自定义内容"
  target={@myself}
>
  <:custom_content>
    <div class="space-y-4">
      <p>这里可以放置任何自定义内容</p>
      <form>
        <input type="text" placeholder="输入内容" />
      </form>
    </div>
  </:custom_content>
</.dynamic_dialog>
```

### 动态配置
```elixir
def get_dialog_config(type) do
  case type do
    :critical -> [danger: true, size: "lg", icon: "fas fa-exclamation-triangle"]
    :info -> [size: "md", icon: "fas fa-info-circle"]
    _ -> []
  end
end
```

这个动态对话框组件提供了完整的解决方案，可以满足各种对话框需求，同时保持代码的简洁和可维护性。
