defmodule RacingGame.Live.AdminPanel.Repositories.QueryBuilders.RacingGameQueryBuilder do
  @moduledoc """
  赛车游戏系统查询构建器

  提供赛车游戏系统的动态查询构建：
  - 比赛查询构建
  - 投注查询构建
  - 股票查询构建
  - 复合查询构建
  """

  require Logger
  alias RacingGame.{Race, Bet, Stock, StockHolding, StockTransaction, RaceResult, SystemCommunication, SystemCommunicationRead}
  require Ash.Query

  # ============================================================================
  # 比赛查询构建
  # ============================================================================

  @doc """
  构建比赛列表查询

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, query}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_race_list_query(params) do
    Logger.debug("🏗️ [赛车查询构建器] 构建比赛列表查询")

    try do
      query = Race
      |> apply_race_filters(params.filters)
      |> apply_race_search(params.search)
      |> apply_sort(params.sort)

      {:ok, query}
    rescue
      error ->
        Logger.error("❌ [赛车查询构建器] 构建比赛查询异常: #{inspect(error)}")
        {:error, :query_build_error}
    end
  end

  @doc """
  构建比赛搜索查询

  ## 参数
  - `search_term` - 搜索词
  - `fields` - 搜索字段

  ## 返回
  - `query` - 查询对象
  """
  def build_race_search_query(search_term, fields \\ [:name, :description]) do
    Logger.debug("🔍 [赛车查询构建器] 构建比赛搜索查询: #{search_term}")

    search_conditions = build_search_conditions(search_term, fields)

    Race
    |> Ash.Query.filter(^search_conditions)
  end

  @doc """
  构建比赛统计查询

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, queries}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_race_statistics_query(options \\ []) do
    Logger.debug("📊 [赛车查询构建器] 构建比赛统计查询")

    try do
      queries = %{
        total_races: Race |> Ash.Query.aggregate(:count, :id),
        races_by_status: Race |> Ash.Query.aggregate(:count, :id, group: [:status]),
        races_by_type: Race |> Ash.Query.aggregate(:count, :id, group: [:race_type]),
        recent_races: build_recent_races_query(options[:days] || 7)
      }

      {:ok, queries}
    rescue
      error ->
        Logger.error("❌ [赛车查询构建器] 构建比赛统计查询异常: #{inspect(error)}")
        {:error, :query_build_error}
    end
  end

  # ============================================================================
  # 投注查询构建
  # ============================================================================

  @doc """
  构建投注列表查询

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, query}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_bet_list_query(params) do
    Logger.debug("🏗️ [赛车查询构建器] 构建投注列表查询")

    try do
      query = Bet
      |> apply_bet_filters(params.filters)
      |> apply_bet_search(params.search)
      |> apply_sort(params.sort)

      {:ok, query}
    rescue
      error ->
        Logger.error("❌ [赛车查询构建器] 构建投注查询异常: #{inspect(error)}")
        {:error, :query_build_error}
    end
  end

  @doc """
  构建用户投注查询

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - `{:ok, query}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_user_bet_query(user_id, options \\ []) do
    Logger.debug("👤 [赛车查询构建器] 构建用户投注查询: #{user_id}")

    try do
      query = Bet
      |> Ash.Query.filter(user_id == ^user_id)
      |> maybe_apply_date_range(options[:date_range])
      |> maybe_apply_status_filter(options[:status])
      |> maybe_apply_bet_type_filter(options[:bet_type])

      {:ok, query}
    rescue
      error ->
        Logger.error("❌ [赛车查询构建器] 构建用户投注查询异常: #{inspect(error)}")
        {:error, :query_build_error}
    end
  end

  @doc """
  构建投注统计查询

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, queries}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_bet_statistics_query(options \\ []) do
    Logger.debug("📊 [赛车查询构建器] 构建投注统计查询")

    try do
      queries = %{
        total_bets: Bet |> Ash.Query.aggregate(:count, :id),
        bets_by_status: Bet |> Ash.Query.aggregate(:count, :id, group: [:status]),
        bets_by_type: Bet |> Ash.Query.aggregate(:count, :id, group: [:bet_type]),
        recent_bets: build_recent_bets_query(options[:days] || 7)
      }

      {:ok, queries}
    rescue
      error ->
        Logger.error("❌ [赛车查询构建器] 构建投注统计查询异常: #{inspect(error)}")
        {:error, :query_build_error}
    end
  end

  # ============================================================================
  # 股票查询构建
  # ============================================================================

  @doc """
  构建股票列表查询

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, query}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_stock_list_query(params) do
    Logger.debug("🏗️ [赛车查询构建器] 构建股票列表查询")

    try do
      query = Stock
      |> apply_stock_filters(params.filters)
      |> apply_stock_search(params.search)
      |> apply_sort(params.sort)

      {:ok, query}
    rescue
      error ->
        Logger.error("❌ [赛车查询构建器] 构建股票查询异常: #{inspect(error)}")
        {:error, :query_build_error}
    end
  end

  @doc """
  构建用户持仓查询

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - `{:ok, query}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_user_holdings_query(user_id, options \\ []) do
    Logger.debug("👤 [赛车查询构建器] 构建用户持仓查询: #{user_id}")

    try do
      query = StockHolding
      |> Ash.Query.filter(user_id == ^user_id)
      |> maybe_apply_active_only_filter(options[:active_only])
      |> maybe_apply_stock_filter(options[:stock_id])

      {:ok, query}
    rescue
      error ->
        Logger.error("❌ [赛车查询构建器] 构建用户持仓查询异常: #{inspect(error)}")
        {:error, :query_build_error}
    end
  end

  @doc """
  构建股票统计查询

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, queries}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_stock_statistics_query(options \\ []) do
    Logger.debug("📊 [赛车查询构建器] 构建股票统计查询")

    try do
      queries = %{
        total_stocks: Stock |> Ash.Query.aggregate(:count, :id),
        active_stocks: Stock |> Ash.Query.filter(is_active == true) |> Ash.Query.aggregate(:count, :id),
        total_holdings: StockHolding |> Ash.Query.aggregate(:count, :id),
        total_transactions: StockTransaction |> Ash.Query.aggregate(:count, :id),
        recent_transactions: build_recent_stock_transactions_query(options[:days] || 7)
      }

      {:ok, queries}
    rescue
      error ->
        Logger.error("❌ [赛车查询构建器] 构建股票统计查询异常: #{inspect(error)}")
        {:error, :query_build_error}
    end
  end

  # ============================================================================
  # 系统通信查询构建
  # ============================================================================

  @doc """
  构建系统通信列表查询

  ## 参数
  - `params` - 查询参数

  ## 返回
  - `{:ok, query}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_communication_list_query(params) do
    Logger.debug("🏗️ [赛车查询构建器] 构建系统通信列表查询")

    try do
      query = SystemCommunication
      |> apply_communication_filters(params.filters)
      |> apply_communication_search(params.search)
      |> apply_sort(params.sort)

      {:ok, query}
    rescue
      error ->
        Logger.error("❌ [赛车查询构建器] 构建系统通信查询异常: #{inspect(error)}")
        {:error, :query_build_error}
    end
  end

  @doc """
  构建用户未读消息查询

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - `{:ok, query}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_user_unread_communications_query(user_id, options \\ []) do
    Logger.debug("👤 [赛车查询构建器] 构建用户未读消息查询: #{user_id}")

    try do
      # 子查询获取用户已读的消息ID
      read_ids_query = SystemCommunicationRead
      |> Ash.Query.filter(user_id == ^user_id)
      |> Ash.Query.select([:system_communication_id])

      query = SystemCommunication
      |> Ash.Query.filter(is_active == true)
      |> Ash.Query.filter(not exists(read_ids_query, system_communication_id == id))
      |> maybe_apply_communication_type_filter(options[:communication_type])
      |> apply_sort(options[:sort] || [inserted_at: :desc])

      {:ok, query}
    rescue
      error ->
        Logger.error("❌ [赛车查询构建器] 构建用户未读消息查询异常: #{inspect(error)}")
        {:error, :query_build_error}
    end
  end

  @doc """
  构建系统通信统计查询

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, queries}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_communication_statistics_query(options \\ []) do
    Logger.debug("📊 [赛车查询构建器] 构建系统通信统计查询")

    try do
      queries = %{
        total_communications: SystemCommunication |> Ash.Query.aggregate(:count, :id),
        active_communications: SystemCommunication |> Ash.Query.filter(is_active == true) |> Ash.Query.aggregate(:count, :id),
        communications_by_type: SystemCommunication |> Ash.Query.aggregate(:count, :id, group: [:communication_type]),
        recent_communications: build_recent_communications_query(options[:days] || 7),
        total_reads: SystemCommunicationRead |> Ash.Query.aggregate(:count, :id)
      }

      {:ok, queries}
    rescue
      error ->
        Logger.error("❌ [赛车查询构建器] 构建系统通信统计查询异常: #{inspect(error)}")
        {:error, :query_build_error}
    end
  end

  # ============================================================================
  # 复合查询构建
  # ============================================================================

  @doc """
  构建用户综合查询

  ## 参数
  - `user_id` - 用户ID
  - `options` - 选项

  ## 返回
  - `{:ok, queries}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_user_comprehensive_query(user_id, options \\ []) do
    Logger.debug("🔄 [赛车查询构建器] 构建用户综合查询: #{user_id}")

    try do
      queries = %{
        user_bets: build_user_bet_query(user_id, options),
        user_holdings: build_user_holdings_query(user_id, options),
        user_transactions: build_user_stock_transactions_query(user_id, options),
        user_unread_communications: build_user_unread_communications_query(user_id, options)
      }

      {:ok, queries}
    rescue
      error ->
        Logger.error("❌ [赛车查询构建器] 构建用户综合查询异常: #{inspect(error)}")
        {:error, :query_build_error}
    end
  end

  @doc """
  构建系统综合统计查询

  ## 参数
  - `options` - 选项

  ## 返回
  - `{:ok, queries}` - 成功
  - `{:error, reason}` - 失败
  """
  def build_system_comprehensive_statistics_query(options \\ []) do
    Logger.debug("📊 [赛车查询构建器] 构建系统综合统计查询")

    try do
      with {:ok, race_queries} <- build_race_statistics_query(options),
           {:ok, bet_queries} <- build_bet_statistics_query(options),
           {:ok, stock_queries} <- build_stock_statistics_query(options) do

        queries = %{
          races: race_queries,
          bets: bet_queries,
          stocks: stock_queries
        }

        {:ok, queries}
      else
        error -> error
      end
    rescue
      error ->
        Logger.error("❌ [赛车查询构建器] 构建系统综合统计查询异常: #{inspect(error)}")
        {:error, :query_build_error}
    end
  end

  # ============================================================================
  # 私有函数 - 比赛查询
  # ============================================================================

  # 应用比赛过滤器
  defp apply_race_filters(query, filters) when map_size(filters) == 0, do: query
  defp apply_race_filters(query, filters) do
    Enum.reduce(filters, query, fn {key, value}, acc_query ->
      apply_race_filter(acc_query, key, value)
    end)
  end

  # 应用单个比赛过滤器
  defp apply_race_filter(query, :status, status) when not is_nil(status) do
    Ash.Query.filter(query, status == ^status)
  end
  defp apply_race_filter(query, :race_type, race_type) when not is_nil(race_type) do
    Ash.Query.filter(query, race_type == ^race_type)
  end
  defp apply_race_filter(query, :date_range, {start_date, end_date}) do
    Ash.Query.filter(query, start_time >= ^start_date and start_time <= ^end_date)
  end
  defp apply_race_filter(query, _key, _value), do: query

  # 应用比赛搜索
  defp apply_race_search(query, search) when search == "" or is_nil(search), do: query
  defp apply_race_search(query, search) do
    search_term = "%#{search}%"
    Ash.Query.filter(query, ilike(name, ^search_term) or ilike(description, ^search_term))
  end

  # 构建最近比赛查询
  defp build_recent_races_query(days) do
    date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)

    Race
    |> Ash.Query.filter(inserted_at >= ^date_threshold)
    |> Ash.Query.aggregate(:count, :id)
  end

  # ============================================================================
  # 私有函数 - 投注查询
  # ============================================================================

  # 应用投注过滤器
  defp apply_bet_filters(query, filters) when map_size(filters) == 0, do: query
  defp apply_bet_filters(query, filters) do
    Enum.reduce(filters, query, fn {key, value}, acc_query ->
      apply_bet_filter(acc_query, key, value)
    end)
  end

  # 应用单个投注过滤器
  defp apply_bet_filter(query, :status, status) when not is_nil(status) do
    Ash.Query.filter(query, status == ^status)
  end
  defp apply_bet_filter(query, :bet_type, bet_type) when not is_nil(bet_type) do
    Ash.Query.filter(query, bet_type == ^bet_type)
  end
  defp apply_bet_filter(query, :user_id, user_id) when not is_nil(user_id) do
    Ash.Query.filter(query, user_id == ^user_id)
  end
  defp apply_bet_filter(query, :race_id, race_id) when not is_nil(race_id) do
    Ash.Query.filter(query, race_id == ^race_id)
  end
  defp apply_bet_filter(query, _key, _value), do: query

  # 应用投注搜索
  defp apply_bet_search(query, search) when search == "" or is_nil(search), do: query
  defp apply_bet_search(query, search) do
    # 这里可以根据需要搜索投注相关字段
    query
  end

  # 构建最近投注查询
  defp build_recent_bets_query(days) do
    date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)

    Bet
    |> Ash.Query.filter(inserted_at >= ^date_threshold)
    |> Ash.Query.aggregate(:count, :id)
  end

  # ============================================================================
  # 私有函数 - 股票查询
  # ============================================================================

  # 应用股票过滤器
  defp apply_stock_filters(query, filters) when map_size(filters) == 0, do: query
  defp apply_stock_filters(query, filters) do
    Enum.reduce(filters, query, fn {key, value}, acc_query ->
      apply_stock_filter(acc_query, key, value)
    end)
  end

  # 应用单个股票过滤器
  defp apply_stock_filter(query, :is_active, is_active) when not is_nil(is_active) do
    Ash.Query.filter(query, is_active == ^is_active)
  end
  defp apply_stock_filter(query, :stock_type, stock_type) when not is_nil(stock_type) do
    Ash.Query.filter(query, stock_type == ^stock_type)
  end
  defp apply_stock_filter(query, _key, _value), do: query

  # 应用股票搜索
  defp apply_stock_search(query, search) when search == "" or is_nil(search), do: query
  defp apply_stock_search(query, search) do
    search_term = "%#{search}%"
    Ash.Query.filter(query, ilike(stock_code, ^search_term) or ilike(stock_name, ^search_term))
  end

  # 构建最近股票交易查询
  defp build_recent_stock_transactions_query(days) do
    date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)

    StockTransaction
    |> Ash.Query.filter(inserted_at >= ^date_threshold)
    |> Ash.Query.aggregate(:count, :id)
  end

  # 构建用户股票交易查询
  defp build_user_stock_transactions_query(user_id, options) do
    query = StockTransaction
    |> Ash.Query.filter(user_id == ^user_id)
    |> maybe_apply_date_range(options[:date_range])
    |> maybe_apply_transaction_type_filter(options[:transaction_type])

    {:ok, query}
  end

  # ============================================================================
  # 私有函数 - 通用辅助
  # ============================================================================

  # 构建搜索条件
  defp build_search_conditions(search_term, fields) do
    search_pattern = "%#{search_term}%"

    conditions = Enum.map(fields, fn field ->
      case field do
        :name -> {:ilike, :name, search_pattern}
        :description -> {:ilike, :description, search_pattern}
        :stock_code -> {:ilike, :stock_code, search_pattern}
        :stock_name -> {:ilike, :stock_name, search_pattern}
        _ -> nil
      end
    end)
    |> Enum.filter(&(!is_nil(&1)))

    case conditions do
      [] -> true
      [condition] -> condition
      conditions -> {:or, conditions}
    end
  end

  # 应用排序
  defp apply_sort(query, sort) when is_list(sort) do
    Ash.Query.sort(query, sort)
  end
  defp apply_sort(query, _), do: query

  # 可能应用日期范围
  defp maybe_apply_date_range(query, nil), do: query
  defp maybe_apply_date_range(query, {start_date, end_date}) do
    Ash.Query.filter(query, inserted_at >= ^start_date and inserted_at <= ^end_date)
  end

  # 可能应用状态过滤
  defp maybe_apply_status_filter(query, nil), do: query
  defp maybe_apply_status_filter(query, status) do
    Ash.Query.filter(query, status == ^status)
  end

  # 可能应用投注类型过滤
  defp maybe_apply_bet_type_filter(query, nil), do: query
  defp maybe_apply_bet_type_filter(query, bet_type) do
    Ash.Query.filter(query, bet_type == ^bet_type)
  end

  # 可能应用活跃持仓过滤
  defp maybe_apply_active_only_filter(query, nil), do: query
  defp maybe_apply_active_only_filter(query, true) do
    Ash.Query.filter(query, quantity > 0)
  end
  defp maybe_apply_active_only_filter(query, _), do: query

  # 可能应用股票过滤
  defp maybe_apply_stock_filter(query, nil), do: query
  defp maybe_apply_stock_filter(query, stock_id) do
    Ash.Query.filter(query, stock_id == ^stock_id)
  end

  # 可能应用交易类型过滤
  defp maybe_apply_transaction_type_filter(query, nil), do: query
  defp maybe_apply_transaction_type_filter(query, transaction_type) do
    Ash.Query.filter(query, transaction_type == ^transaction_type)
  end

  # ============================================================================
  # 私有函数 - 系统通信查询
  # ============================================================================

  # 应用系统通信过滤器
  defp apply_communication_filters(query, filters) when map_size(filters) == 0, do: query
  defp apply_communication_filters(query, filters) do
    Enum.reduce(filters, query, fn {key, value}, acc_query ->
      apply_communication_filter(acc_query, key, value)
    end)
  end

  # 应用单个系统通信过滤器
  defp apply_communication_filter(query, :is_active, is_active) when not is_nil(is_active) do
    Ash.Query.filter(query, is_active == ^is_active)
  end
  defp apply_communication_filter(query, :communication_type, communication_type) when not is_nil(communication_type) do
    Ash.Query.filter(query, communication_type == ^communication_type)
  end
  defp apply_communication_filter(query, :date_range, {start_date, end_date}) do
    Ash.Query.filter(query, inserted_at >= ^start_date and inserted_at <= ^end_date)
  end
  defp apply_communication_filter(query, _key, _value), do: query

  # 应用系统通信搜索
  defp apply_communication_search(query, search) when search == "" or is_nil(search), do: query
  defp apply_communication_search(query, search) do
    search_term = "%#{search}%"
    Ash.Query.filter(query, ilike(title, ^search_term) or ilike(content, ^search_term))
  end

  # 构建最近系统通信查询
  defp build_recent_communications_query(days) do
    date_threshold = DateTime.utc_now() |> DateTime.add(-days * 24 * 3600, :second)

    SystemCommunication
    |> Ash.Query.filter(inserted_at >= ^date_threshold)
    |> Ash.Query.aggregate(:count, :id)
  end

  # 可能应用通信类型过滤
  defp maybe_apply_communication_type_filter(query, nil), do: query
  defp maybe_apply_communication_type_filter(query, communication_type) do
    Ash.Query.filter(query, communication_type == ^communication_type)
  end
end
