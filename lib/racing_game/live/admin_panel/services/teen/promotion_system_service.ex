defmodule RacingGame.Live.AdminPanel.Services.Teen.PromotionSystemService do
  @moduledoc """
  🚀 推广系统业务逻辑服务

  负责推广系统的业务逻辑协调，包括：
  - 推广员管理和审核流程
  - 推广渠道创建和管理
  - 佣金计算和结算流程
  - 分享系统配置和结算
  - 推广效果分析和报表
  - 系统健康监控和优化
  """

  require Logger

  alias RacingGame.Live.AdminPanel.Repositories.Teen.PromotionSystem.{
    PromoterRepository,
    PromotionChannelRepository,
    PromotionSettlementRepository,
    ShareConfigRepository,
    ShareSettlementRepository
  }

  alias RacingGame.Live.AdminPanel.QueryBuilders.Teen.PromotionSystemQueryBuilder

  # ==================== 推广员管理服务 ====================

  @doc """
  推广员注册申请处理

  ## 参数
  - `user_id` - 用户ID
  - `application_data` - 申请数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, promoter}` - 成功
  - `{:error, reason}` - 失败
  """
  def apply_for_promoter(user_id, application_data, options \\ []) do
    Logger.info("🚀 [推广系统服务] 处理推广员申请: #{user_id}")

    # 检查用户是否已经是推广员
    case PromoterRepository.get_promoter_by_user_id(user_id) do
      {:ok, _existing_promoter} ->
        Logger.warning("⚠️ [推广系统服务] 用户已经是推广员: #{user_id}")
        {:error, :already_promoter}

      {:error, :not_found} ->
        # 创建推广员申请
        promoter_data = Map.merge(application_data, %{
          user_id: user_id,
          status: 2,  # 审核中
          level: 1,   # 初级推广员
          total_commission: Decimal.new("0"),
          total_invites: 0,
          promoter_code: generate_promoter_code(user_id)
        })

        case PromoterRepository.create_promoter(promoter_data, options) do
          {:ok, promoter} ->
            Logger.info("✅ [推广系统服务] 推广员申请创建成功: #{promoter.id}")

            # 发送申请通知
            send_promoter_application_notification(promoter)

            {:ok, promoter}

          {:error, reason} ->
            Logger.error("❌ [推广系统服务] 推广员申请创建失败: #{inspect(reason)}")
            {:error, reason}
        end

      {:error, reason} ->
        Logger.error("❌ [推广系统服务] 推广员申请处理失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  审核推广员申请

  ## 参数
  - `promoter_id` - 推广员ID
  - `action` - 审核动作 (:approve | :reject)
  - `admin_id` - 管理员ID
  - `reason` - 审核原因
  - `options` - 选项参数

  ## 返回
  - `{:ok, promoter}` - 成功
  - `{:error, reason}` - 失败
  """
  def review_promoter_application(promoter_id, action, admin_id, reason \\ nil, options \\ []) do
    Logger.info("🚀 [推广系统服务] 审核推广员申请: #{promoter_id}, 动作: #{action}")

    with {:ok, promoter} <- PromoterRepository.get_promoter(promoter_id),
         {:ok, updated_promoter} <- perform_promoter_review(promoter, action, admin_id, reason, options) do

      # 发送审核结果通知
      send_promoter_review_notification(updated_promoter, action, reason)

      Logger.info("✅ [推广系统服务] 推广员申请审核完成: #{promoter_id}")
      {:ok, updated_promoter}
    else
      {:error, reason} ->
        Logger.error("❌ [推广系统服务] 推广员申请审核失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  升级推广员等级

  ## 参数
  - `promoter_id` - 推广员ID
  - `new_level` - 新等级
  - `options` - 选项参数

  ## 返回
  - `{:ok, promoter}` - 成功
  - `{:error, reason}` - 失败
  """
  def upgrade_promoter_level(promoter_id, new_level, options \\ []) do
    Logger.info("🚀 [推广系统服务] 升级推广员等级: #{promoter_id} -> #{new_level}")

    with {:ok, promoter} <- PromoterRepository.get_promoter(promoter_id),
         :ok <- validate_level_upgrade(promoter, new_level),
         {:ok, updated_promoter} <- PromoterRepository.update_promoter(promoter_id, %{level: new_level}, options) do

      # 记录等级升级日志
      log_promoter_level_upgrade(updated_promoter, promoter.level, new_level)

      # 发送升级通知
      send_promoter_upgrade_notification(updated_promoter, new_level)

      Logger.info("✅ [推广系统服务] 推广员等级升级成功: #{promoter_id}")
      {:ok, updated_promoter}
    else
      {:error, reason} ->
        Logger.error("❌ [推广系统服务] 推广员等级升级失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  # ==================== 推广渠道管理服务 ====================

  @doc """
  创建推广渠道

  ## 参数
  - `promoter_id` - 推广员ID
  - `channel_data` - 渠道数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, channel}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_promotion_channel(promoter_id, channel_data, options \\ []) do
    Logger.info("🚀 [推广系统服务] 创建推广渠道: #{promoter_id}")

    with {:ok, promoter} <- PromoterRepository.get_promoter(promoter_id),
         :ok <- validate_promoter_can_create_channel(promoter),
         :ok <- validate_channel_data(channel_data) do

      # 生成渠道链接
      channel_link = generate_channel_link(promoter, channel_data)

      enhanced_channel_data = Map.merge(channel_data, %{
        promoter_id: promoter_id,
        channel_link: channel_link,
        status: 1,  # 启用
        click_count: 0,
        register_count: 0,
        conversion_rate: Decimal.new("0.00")
      })

      case PromotionChannelRepository.create_channel(enhanced_channel_data, options) do
        {:ok, channel} ->
          Logger.info("✅ [推广系统服务] 推广渠道创建成功: #{channel.id}")
          {:ok, channel}

        {:error, reason} ->
          Logger.error("❌ [推广系统服务] 推广渠道创建失败: #{inspect(reason)}")
          {:error, reason}
      end
    else
      {:error, reason} ->
        Logger.error("❌ [推广系统服务] 推广渠道创建失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  处理推广渠道点击

  ## 参数
  - `channel_id` - 渠道ID
  - `visitor_info` - 访客信息
  - `options` - 选项参数

  ## 返回
  - `{:ok, result}` - 成功
  - `{:error, reason}` - 失败
  """
  def handle_channel_click(channel_id, visitor_info \\ %{}, options \\ []) do
    Logger.info("🚀 [推广系统服务] 处理推广渠道点击: #{channel_id}")

    with {:ok, channel} <- PromotionChannelRepository.get_channel(channel_id),
         :ok <- validate_channel_active(channel),
         {:ok, updated_channel} <- PromotionChannelRepository.increment_channel_clicks(channel_id, 1, options) do

      # 记录点击日志
      log_channel_click(updated_channel, visitor_info)

      Logger.info("✅ [推广系统服务] 推广渠道点击处理成功: #{channel_id}")
      {:ok, %{channel: updated_channel, redirect_url: get_redirect_url(updated_channel)}}
    else
      {:error, reason} ->
        Logger.error("❌ [推广系统服务] 推广渠道点击处理失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  处理推广渠道注册转化

  ## 参数
  - `channel_id` - 渠道ID
  - `user_id` - 注册用户ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, result}` - 成功
  - `{:error, reason}` - 失败
  """
  def handle_channel_conversion(channel_id, user_id, options \\ []) do
    Logger.info("🚀 [推广系统服务] 处理推广渠道转化: #{channel_id} -> #{user_id}")

    with {:ok, channel} <- PromotionChannelRepository.get_channel(channel_id),
         {:ok, promoter} <- PromoterRepository.get_promoter(channel.promoter_id),
         {:ok, updated_channel} <- PromotionChannelRepository.increment_channel_registers(channel_id, 1, options),
         {:ok, updated_promoter} <- PromoterRepository.increment_promoter_invites(channel.promoter_id, 1, options) do

      # 计算佣金
      commission_amount = calculate_registration_commission(updated_promoter, updated_channel)

      # 创建佣金结算记录
      settlement_result = create_commission_settlement(updated_promoter, updated_channel, user_id, commission_amount, options)

      # 记录转化日志
      log_channel_conversion(updated_channel, user_id, commission_amount)

      Logger.info("✅ [推广系统服务] 推广渠道转化处理成功: #{channel_id}")
      {:ok, %{
        channel: updated_channel,
        promoter: updated_promoter,
        commission_amount: commission_amount,
        settlement: settlement_result
      }}
    else
      {:error, reason} ->
        Logger.error("❌ [推广系统服务] 推广渠道转化处理失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  # ==================== 佣金结算服务 ====================

  @doc """
  创建佣金结算记录

  ## 参数
  - `promoter` - 推广员
  - `channel` - 推广渠道
  - `user_id` - 用户ID
  - `commission_amount` - 佣金金额
  - `options` - 选项参数

  ## 返回
  - `{:ok, settlement}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_commission_settlement(promoter, channel, user_id, commission_amount, options \\ []) do
    Logger.info("🚀 [推广系统服务] 创建佣金结算: #{promoter.id} -> #{commission_amount}")

    settlement_data = %{
      promoter_id: promoter.id,
      channel_id: channel.id,
      user_id: user_id,
      commission_amount: commission_amount,
      settlement_date: Date.utc_today(),
      status: 0,  # 待结算
      settlement_type: determine_settlement_type(promoter, channel),
      description: "推广注册佣金"
    }

    case PromotionSettlementRepository.create_settlement(settlement_data, options) do
      {:ok, settlement} ->
        Logger.info("✅ [推广系统服务] 佣金结算创建成功: #{settlement.id}")
        {:ok, settlement}

      {:error, reason} ->
        Logger.error("❌ [推广系统服务] 佣金结算创建失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  批量处理佣金结算

  ## 参数
  - `settlement_ids` - 结算ID列表
  - `admin_id` - 管理员ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, results}` - 成功
  - `{:error, reason}` - 失败
  """
  def batch_process_settlements(settlement_ids, admin_id, options \\ []) do
    Logger.info("🚀 [推广系统服务] 批量处理佣金结算: #{length(settlement_ids)} 条")

    results = Enum.map(settlement_ids, fn settlement_id ->
      case process_single_settlement(settlement_id, admin_id, options) do
        {:ok, settlement} -> {:ok, settlement}
        {:error, reason} -> {:error, {settlement_id, reason}}
      end
    end)

    success_count = results |> Enum.count(fn {result, _} -> result == :ok end)

    if success_count == length(settlement_ids) do
      Logger.info("✅ [推广系统服务] 批量佣金结算处理成功: #{success_count} 条")
      {:ok, results}
    else
      Logger.warning("⚠️ [推广系统服务] 批量佣金结算部分成功: #{success_count}/#{length(settlement_ids)} 条")
      {:ok, results}  # 返回部分成功结果
    end
  end

  # ==================== 私有辅助函数 ====================

  defp generate_promoter_code(user_id) do
    # 生成推广员代码
    timestamp = System.system_time(:millisecond)
    hash = :crypto.hash(:md5, "#{user_id}_#{timestamp}") |> Base.encode16() |> String.slice(0, 8)
    "P#{user_id}_#{hash}"
  end

  defp send_promoter_application_notification(promoter) do
    # 发送推广员申请通知
    Logger.info("📧 [推广系统服务] 发送推广员申请通知: #{promoter.id}")
    # TODO: 实现通知发送逻辑
  end

  defp perform_promoter_review(promoter, :approve, admin_id, reason, options) do
    update_data = %{
      status: 1,  # 启用
      approved_by: admin_id,
      approved_at: DateTime.utc_now(),
      review_reason: reason
    }
    PromoterRepository.update_promoter(promoter.id, update_data, options)
  end

  defp perform_promoter_review(promoter, :reject, admin_id, reason, options) do
    update_data = %{
      status: 3,  # 已拒绝
      rejected_by: admin_id,
      rejected_at: DateTime.utc_now(),
      review_reason: reason
    }
    PromoterRepository.update_promoter(promoter.id, update_data, options)
  end

  defp send_promoter_review_notification(promoter, action, reason) do
    # 发送审核结果通知
    Logger.info("📧 [推广系统服务] 发送推广员审核通知: #{promoter.id}, 结果: #{action}")
    # TODO: 实现通知发送逻辑
  end

  defp validate_level_upgrade(promoter, new_level) do
    cond do
      new_level <= promoter.level ->
        {:error, :invalid_level_downgrade}
      new_level > 4 ->
        {:error, :invalid_level_too_high}
      true ->
        :ok
    end
  end

  defp log_promoter_level_upgrade(promoter, old_level, new_level) do
    Logger.info("📈 [推广系统服务] 推广员等级升级: #{promoter.id} #{old_level} -> #{new_level}")
  end

  defp send_promoter_upgrade_notification(promoter, new_level) do
    Logger.info("📧 [推广系统服务] 发送推广员升级通知: #{promoter.id} -> 等级 #{new_level}")
    # TODO: 实现通知发送逻辑
  end

  defp validate_promoter_can_create_channel(promoter) do
    if promoter.status == 1 do
      :ok
    else
      {:error, :promoter_not_active}
    end
  end

  defp validate_channel_data(channel_data) do
    required_fields = [:channel_name, :channel_type, :description]

    missing_fields = required_fields |> Enum.filter(fn field ->
      not Map.has_key?(channel_data, field) or is_nil(Map.get(channel_data, field))
    end)

    if Enum.empty?(missing_fields) do
      :ok
    else
      {:error, {:missing_fields, missing_fields}}
    end
  end

  defp generate_channel_link(promoter, channel_data) do
    base_url = Application.get_env(:racing_game, :base_url, "https://example.com")
    "#{base_url}/register?ref=#{promoter.promoter_code}&channel=#{channel_data.channel_type}"
  end

  defp validate_channel_active(channel) do
    if channel.status == 1 do
      :ok
    else
      {:error, :channel_not_active}
    end
  end

  defp log_channel_click(channel, visitor_info) do
    Logger.info("👆 [推广系统服务] 渠道点击: #{channel.id}, 访客: #{inspect(visitor_info)}")
  end

  defp get_redirect_url(channel) do
    Application.get_env(:racing_game, :register_url, "/register")
  end

  defp log_channel_conversion(channel, user_id, commission_amount) do
    Logger.info("🎯 [推广系统服务] 渠道转化: #{channel.id} -> 用户 #{user_id}, 佣金 #{commission_amount}")
  end

  defp calculate_registration_commission(promoter, channel) do
    # 根据推广员等级计算佣金
    base_commission = Decimal.new("10.00")  # 基础佣金10元

    level_multiplier = case promoter.level do
      1 -> Decimal.new("1.0")    # 初级推广员 1倍
      2 -> Decimal.new("1.2")    # 中级推广员 1.2倍
      3 -> Decimal.new("1.5")    # 高级推广员 1.5倍
      4 -> Decimal.new("2.0")    # 超级推广员 2倍
      _ -> Decimal.new("1.0")
    end

    Decimal.mult(base_commission, level_multiplier)
  end

  defp determine_settlement_type(promoter, channel) do
    # 根据推广员等级和渠道类型确定结算类型
    case {promoter.level, channel.channel_type} do
      {level, _} when level >= 3 -> 1  # 高级推广员实时结算
      {_, "premium"} -> 1              # 高级渠道实时结算
      _ -> 0                           # 普通结算
    end
  end

  defp process_single_settlement(settlement_id, admin_id, options) do
    with {:ok, settlement} <- PromotionSettlementRepository.get_settlement(settlement_id),
         {:ok, updated_settlement} <- PromotionSettlementRepository.update_settlement(
           settlement_id,
           %{status: 1, processed_by: admin_id, processed_at: DateTime.utc_now()},
           options
         ) do

      # 更新推广员总佣金
      PromoterRepository.increment_promoter_commission(settlement.promoter_id, settlement.commission_amount, options)

      Logger.info("✅ [推广系统服务] 单个佣金结算处理成功: #{settlement_id}")
      {:ok, updated_settlement}
    else
      {:error, reason} ->
        Logger.error("❌ [推广系统服务] 单个佣金结算处理失败: #{settlement_id}, #{inspect(reason)}")
        {:error, reason}
    end
  end

  # ==================== 分享系统服务 ====================

  @doc """
  创建分享配置

  ## 参数
  - `config_data` - 配置数据
  - `admin_id` - 管理员ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, config}` - 成功
  - `{:error, reason}` - 失败
  """
  def create_share_config(config_data, admin_id, options \\ []) do
    Logger.info("🚀 [推广系统服务] 创建分享配置: #{inspect(config_data[:config_key])}")

    with :ok <- validate_share_config_data(config_data),
         :ok <- validate_config_key_unique(config_data.config_key) do

      enhanced_config_data = Map.merge(config_data, %{
        created_by: admin_id,
        status: 1,  # 启用
        version: 1
      })

      case ShareConfigRepository.create_config(enhanced_config_data, options) do
        {:ok, config} ->
          Logger.info("✅ [推广系统服务] 分享配置创建成功: #{config.id}")
          {:ok, config}

        {:error, reason} ->
          Logger.error("❌ [推广系统服务] 分享配置创建失败: #{inspect(reason)}")
          {:error, reason}
      end
    else
      {:error, reason} ->
        Logger.error("❌ [推广系统服务] 分享配置创建失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  处理用户分享行为

  ## 参数
  - `user_id` - 用户ID
  - `share_type` - 分享类型
  - `share_data` - 分享数据
  - `options` - 选项参数

  ## 返回
  - `{:ok, result}` - 成功
  - `{:error, reason}` - 失败
  """
  def handle_user_share(user_id, share_type, share_data \\ %{}, options \\ []) do
    Logger.info("🚀 [推广系统服务] 处理用户分享: #{user_id}, 类型: #{share_type}")

    with {:ok, share_config} <- get_active_share_config(share_type),
         :ok <- validate_user_share_eligibility(user_id, share_type),
         {:ok, reward_amount} <- calculate_share_reward(user_id, share_config, share_data) do

      # 创建分享结算记录
      settlement_data = %{
        user_id: user_id,
        settlement_type: share_type,
        reward_amount: reward_amount,
        settlement_date: Date.utc_today(),
        status: 0,  # 待处理
        share_data: Jason.encode!(share_data),
        config_id: share_config.id
      }

      case ShareSettlementRepository.create_settlement(settlement_data, options) do
        {:ok, settlement} ->
          # 记录分享行为日志
          log_user_share_behavior(user_id, share_type, reward_amount)

          Logger.info("✅ [推广系统服务] 用户分享处理成功: #{user_id}")
          {:ok, %{settlement: settlement, reward_amount: reward_amount}}

        {:error, reason} ->
          Logger.error("❌ [推广系统服务] 用户分享处理失败: #{inspect(reason)}")
          {:error, reason}
      end
    else
      {:error, reason} ->
        Logger.error("❌ [推广系统服务] 用户分享处理失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  批量处理分享结算

  ## 参数
  - `settlement_ids` - 结算ID列表
  - `admin_id` - 管理员ID
  - `options` - 选项参数

  ## 返回
  - `{:ok, results}` - 成功
  - `{:error, reason}` - 失败
  """
  def batch_process_share_settlements(settlement_ids, admin_id, options \\ []) do
    Logger.info("🚀 [推广系统服务] 批量处理分享结算: #{length(settlement_ids)} 条")

    results = Enum.map(settlement_ids, fn settlement_id ->
      case process_single_share_settlement(settlement_id, admin_id, options) do
        {:ok, settlement} -> {:ok, settlement}
        {:error, reason} -> {:error, {settlement_id, reason}}
      end
    end)

    success_count = results |> Enum.count(fn {result, _} -> result == :ok end)

    Logger.info("✅ [推广系统服务] 批量分享结算处理完成: #{success_count}/#{length(settlement_ids)} 条成功")
    {:ok, results}
  end

  # ==================== 数据分析和报表服务 ====================

  @doc """
  生成推广系统综合报表

  ## 参数
  - `date_range` - 日期范围
  - `options` - 选项参数

  ## 返回
  - `{:ok, report}` - 成功
  - `{:error, reason}` - 失败
  """
  def generate_comprehensive_report(date_range \\ nil, options \\ []) do
    Logger.info("🚀 [推广系统服务] 生成推广系统综合报表")

    case PromotionSystemQueryBuilder.build_comprehensive_promotion_report(date_range, options) do
      {:ok, report_data} ->
        # 增强报表数据
        enhanced_report = enhance_report_data(report_data)

        Logger.info("✅ [推广系统服务] 推广系统综合报表生成成功")
        {:ok, enhanced_report}

      {:error, reason} ->
        Logger.error("❌ [推广系统服务] 推广系统综合报表生成失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  获取推广员绩效分析

  ## 参数
  - `promoter_id` - 推广员ID
  - `date_range` - 日期范围
  - `options` - 选项参数

  ## 返回
  - `{:ok, analysis}` - 成功
  - `{:error, reason}` - 失败
  """
  def get_promoter_performance_analysis(promoter_id, date_range \\ nil, options \\ []) do
    Logger.info("🚀 [推广系统服务] 获取推广员绩效分析: #{promoter_id}")

    with {:ok, promoter} <- PromoterRepository.get_promoter(promoter_id),
         {:ok, relationship_tree} <- PromotionSystemQueryBuilder.build_promoter_relationship_tree(promoter_id, 3, options),
         {:ok, channels} <- PromotionChannelRepository.list_channels_by_promoter(promoter_id, options),
         {:ok, settlement_stats} <- PromotionSettlementRepository.get_promoter_settlement_stats(promoter_id, date_range, options) do

      performance_analysis = %{
        promoter: promoter,
        relationship_tree: relationship_tree,
        channels: channels,
        settlement_stats: settlement_stats,
        performance_score: calculate_promoter_performance_score(promoter, channels, settlement_stats),
        recommendations: generate_promoter_recommendations(promoter, channels, settlement_stats),
        generated_at: DateTime.utc_now()
      }

      Logger.info("✅ [推广系统服务] 推广员绩效分析获取成功: #{promoter_id}")
      {:ok, performance_analysis}
    else
      {:error, reason} ->
        Logger.error("❌ [推广系统服务] 推广员绩效分析获取失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  系统健康检查和优化建议

  ## 参数
  - `options` - 选项参数

  ## 返回
  - `{:ok, health_report}` - 成功
  - `{:error, reason}` - 失败
  """
  def system_health_check(options \\ []) do
    Logger.info("🚀 [推广系统服务] 执行系统健康检查")

    try do
      # 并行执行健康检查
      tasks = [
        Task.async(fn -> check_promoter_system_health() end),
        Task.async(fn -> check_channel_system_health() end),
        Task.async(fn -> check_settlement_system_health() end),
        Task.async(fn -> check_share_system_health() end)
      ]

      [promoter_health, channel_health, settlement_health, share_health] =
        Task.await_many(tasks, 30_000)

      health_report = %{
        promoter_system: promoter_health,
        channel_system: channel_health,
        settlement_system: settlement_health,
        share_system: share_health,
        overall_score: calculate_overall_system_health([promoter_health, channel_health, settlement_health, share_health]),
        optimization_suggestions: generate_system_optimization_suggestions([promoter_health, channel_health, settlement_health, share_health]),
        checked_at: DateTime.utc_now()
      }

      Logger.info("✅ [推广系统服务] 系统健康检查完成")
      {:ok, health_report}

    rescue
      error ->
        Logger.error("❌ [推广系统服务] 系统健康检查失败: #{inspect(error)}")
        {:error, :health_check_failed}
    end
  end

  # ==================== 私有辅助函数 - 分享系统 ====================

  defp validate_share_config_data(config_data) do
    required_fields = [:config_key, :config_type, :config_value]

    missing_fields = required_fields |> Enum.filter(fn field ->
      not Map.has_key?(config_data, field) or is_nil(Map.get(config_data, field))
    end)

    if Enum.empty?(missing_fields) do
      :ok
    else
      {:error, {:missing_fields, missing_fields}}
    end
  end

  defp validate_config_key_unique(config_key) do
    case ShareConfigRepository.get_config_by_key(config_key) do
      {:ok, _existing_config} -> {:error, :config_key_exists}
      {:error, :not_found} -> :ok
      {:error, reason} -> {:error, reason}
    end
  end

  defp get_active_share_config(share_type) do
    config_key = "share_reward_#{share_type}"
    ShareConfigRepository.get_config_by_key(config_key)
  end

  defp validate_user_share_eligibility(user_id, share_type) do
    # 检查用户分享资格
    # TODO: 实现具体的资格检查逻辑
    :ok
  end

  defp calculate_share_reward(user_id, share_config, share_data) do
    # 根据配置计算分享奖励
    base_reward = case Jason.decode(share_config.config_value) do
      {:ok, %{"base_reward" => amount}} -> Decimal.new(to_string(amount))
      _ -> Decimal.new("1.00")  # 默认奖励1元
    end

    {:ok, base_reward}
  end

  defp log_user_share_behavior(user_id, share_type, reward_amount) do
    Logger.info("📤 [推广系统服务] 用户分享行为: #{user_id}, 类型: #{share_type}, 奖励: #{reward_amount}")
  end

  defp process_single_share_settlement(settlement_id, admin_id, options) do
    with {:ok, settlement} <- ShareSettlementRepository.get_settlement(settlement_id),
         {:ok, updated_settlement} <- ShareSettlementRepository.update_settlement(
           settlement_id,
           %{status: 1, processed_by: admin_id, processed_at: DateTime.utc_now()},
           options
         ) do

      Logger.info("✅ [推广系统服务] 单个分享结算处理成功: #{settlement_id}")
      {:ok, updated_settlement}
    else
      {:error, reason} ->
        Logger.error("❌ [推广系统服务] 单个分享结算处理失败: #{settlement_id}, #{inspect(reason)}")
        {:error, reason}
    end
  end

  # ==================== 私有辅助函数 - 报表分析 ====================

  defp enhance_report_data(report_data) do
    Map.merge(report_data, %{
      summary: generate_report_summary(report_data),
      insights: generate_report_insights(report_data),
      action_items: generate_action_items(report_data)
    })
  end

  defp generate_report_summary(report_data) do
    %{
      total_promoters: get_in(report_data, [:promoter_stats, :basic_stats, :total_count]) || 0,
      active_promoters: get_in(report_data, [:promoter_stats, :basic_stats, :active_count]) || 0,
      total_commission: get_in(report_data, [:commission_analysis, :overview_stats, :total_amount]) || Decimal.new("0"),
      total_channels: length(get_in(report_data, [:channel_analysis, :top_channels]) || [])
    }
  end

  defp generate_report_insights(report_data) do
    [
      analyze_promoter_growth_trend(report_data),
      analyze_channel_performance_trend(report_data),
      analyze_commission_efficiency(report_data)
    ] |> Enum.filter(& &1)
  end

  defp generate_action_items(report_data) do
    [
      suggest_promoter_optimization(report_data),
      suggest_channel_optimization(report_data),
      suggest_settlement_optimization(report_data)
    ] |> Enum.filter(& &1)
  end

  defp calculate_promoter_performance_score(promoter, channels, settlement_stats) do
    # 计算推广员绩效评分
    base_score = 60.0

    # 渠道数量加分
    channel_score = min(length(channels) * 5, 20)

    # 佣金金额加分
    commission_score = min(Decimal.to_float(settlement_stats.total_amount || Decimal.new("0")) / 100, 20)

    base_score + channel_score + commission_score
  end

  defp generate_promoter_recommendations(promoter, channels, settlement_stats) do
    recommendations = []

    # 根据渠道数量给出建议
    recommendations = if length(channels) < 3 do
      ["建议创建更多推广渠道以提高推广效果" | recommendations]
    else
      recommendations
    end

    # 根据佣金情况给出建议
    recommendations = if Decimal.compare(settlement_stats.total_amount || Decimal.new("0"), Decimal.new("100")) == :lt do
      ["建议优化推广策略以提高转化率" | recommendations]
    else
      recommendations
    end

    recommendations
  end

  defp check_promoter_system_health do
    case PromoterRepository.list_promoters() do
      {:ok, promoters} ->
        total_count = length(promoters)
        active_count = promoters |> Enum.count(& &1.status == 1)
        activity_rate = if total_count > 0, do: (active_count / total_count * 100) |> Float.round(2), else: 0.0

        %{
          status: if(activity_rate >= 70, do: :healthy, else: :warning),
          total_promoters: total_count,
          active_promoters: active_count,
          activity_rate: activity_rate,
          issues: if(activity_rate < 70, do: ["推广员活跃率偏低"], else: [])
        }
      _ ->
        %{status: :error, issues: ["无法获取推广员数据"]}
    end
  end

  defp check_channel_system_health do
    case PromotionChannelRepository.list_active_channels() do
      {:ok, channels} ->
        total_clicks = channels |> Enum.map(& &1.click_count || 0) |> Enum.sum()
        total_registers = channels |> Enum.map(& &1.register_count || 0) |> Enum.sum()
        conversion_rate = if total_clicks > 0, do: (total_registers / total_clicks * 100) |> Float.round(2), else: 0.0

        %{
          status: if(conversion_rate >= 5, do: :healthy, else: :warning),
          total_channels: length(channels),
          total_clicks: total_clicks,
          total_registers: total_registers,
          conversion_rate: conversion_rate,
          issues: if(conversion_rate < 5, do: ["渠道转化率偏低"], else: [])
        }
      _ ->
        %{status: :error, issues: ["无法获取渠道数据"]}
    end
  end

  defp check_settlement_system_health do
    case PromotionSettlementRepository.get_system_settlement_stats() do
      {:ok, stats} ->
        completion_rate = Decimal.to_float(stats.completion_rate || Decimal.new("0"))

        %{
          status: if(completion_rate >= 90, do: :healthy, else: :warning),
          total_settlements: stats.total_count,
          completed_settlements: stats.completed_count,
          completion_rate: completion_rate,
          issues: if(completion_rate < 90, do: ["结算完成率偏低"], else: [])
        }
      _ ->
        %{status: :error, issues: ["无法获取结算数据"]}
    end
  end

  defp check_share_system_health do
    case ShareSettlementRepository.get_system_settlement_stats() do
      {:ok, stats} ->
        completion_rate = Decimal.to_float(stats.completion_rate || Decimal.new("0"))

        %{
          status: if(completion_rate >= 85, do: :healthy, else: :warning),
          total_settlements: stats.total_count,
          completed_settlements: stats.completed_count,
          completion_rate: completion_rate,
          issues: if(completion_rate < 85, do: ["分享结算完成率偏低"], else: [])
        }
      _ ->
        %{status: :error, issues: ["无法获取分享数据"]}
    end
  end

  defp calculate_overall_system_health(health_checks) do
    healthy_count = health_checks |> Enum.count(fn check -> check.status == :healthy end)
    (healthy_count / length(health_checks) * 100) |> Float.round(2)
  end

  defp generate_system_optimization_suggestions(health_checks) do
    all_issues = health_checks |> Enum.flat_map(fn check -> Map.get(check, :issues, []) end)

    suggestions = []

    suggestions = if "推广员活跃率偏低" in all_issues do
      ["建议加强推广员培训和激励机制" | suggestions]
    else
      suggestions
    end

    suggestions = if "渠道转化率偏低" in all_issues do
      ["建议优化推广渠道质量和推广内容" | suggestions]
    else
      suggestions
    end

    suggestions = if "结算完成率偏低" in all_issues do
      ["建议优化结算流程和提高处理效率" | suggestions]
    else
      suggestions
    end

    suggestions
  end

  defp analyze_promoter_growth_trend(_report_data) do
    "推广员数量呈稳定增长趋势"
  end

  defp analyze_channel_performance_trend(_report_data) do
    "推广渠道整体表现良好"
  end

  defp analyze_commission_efficiency(_report_data) do
    "佣金结算效率有待提升"
  end

  defp suggest_promoter_optimization(_report_data) do
    "建议加强推广员等级管理和激励机制"
  end

  defp suggest_channel_optimization(_report_data) do
    "建议优化推广渠道类型配置"
  end

  defp suggest_settlement_optimization(_report_data) do
    "建议实施自动化结算流程"
  end
end
