defmodule Teen.AlertSystem.AlertRecord do
  @moduledoc """
  报警记录资源
  
  记录系统各种报警信息，包括总金币报警、用户金币报警、注册报警等
  """
  
  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.AlertSystem,
    extensions: [AshAdmin.Resource]

  postgres do
    table "alert_records"
    repo Cypridina.Repo
  end

  admin do
    table_columns [:id, :alert_type_name, :alert_content, :alert_time, :status]
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_by_alert_type
    define :list_unread_alerts
    define :list_by_time_range
  end

  actions do
    defaults [:create, :read, :update, :destroy]

    read :list_by_alert_type do
      argument :alert_type, :integer, allow_nil?: false
      filter expr(alert_type == ^arg(:alert_type))
    end

    read :list_unread_alerts do
      filter expr(status == 0)
    end

    read :list_by_time_range do
      argument :start_time, :utc_datetime, allow_nil?: false
      argument :end_time, :utc_datetime, allow_nil?: false
      filter expr(alert_time >= ^arg(:start_time) and alert_time <= ^arg(:end_time))
    end

    update :mark_as_read do
      change set_attribute(:status, 1)
      change set_attribute(:read_at, &DateTime.utc_now/0)
    end

    create :create_alert do
      argument :alert_type, :integer, allow_nil?: false
      argument :alert_content, :string, allow_nil?: false
      argument :alert_data, :map, allow_nil?: true

      change set_attribute(:alert_type, arg(:alert_type))
      change set_attribute(:alert_content, arg(:alert_content))
      change set_attribute(:alert_data, arg(:alert_data))
      change set_attribute(:alert_time, &DateTime.utc_now/0)
      change set_attribute(:status, 0)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :alert_type, :integer do
      allow_nil? false
      public? true
      description "报警类型：1-总金币报警，2-金币流通渠道报警，3-用户金币报警，4-注册报警，5-单个用户输赢报警"
      constraints min: 1, max: 5
    end

    attribute :alert_type_name, :string do
      allow_nil? false
      public? true
      description "报警类型名称"
      constraints max_length: 100
    end

    attribute :alert_content, :string do
      allow_nil? false
      public? true
      description "报警内容"
      constraints max_length: 2000
    end

    attribute :alert_data, :map do
      allow_nil? true
      public? true
      description "报警相关数据（JSON）"
    end

    attribute :alert_time, :utc_datetime do
      allow_nil? false
      public? true
      description "报警时间"
    end

    attribute :status, :integer do
      allow_nil? false
      public? true
      description "状态：0-未读，1-已读"
      default 0
      constraints min: 0, max: 1
    end

    attribute :read_at, :utc_datetime do
      allow_nil? true
      public? true
      description "读取时间"
    end

    attribute :severity, :integer do
      allow_nil? false
      public? true
      description "严重程度：1-低，2-中，3-高，4-紧急"
      default 2
      constraints min: 1, max: 4
    end

    attribute :source, :string do
      allow_nil? true
      public? true
      description "报警来源"
      constraints max_length: 100
    end

    timestamps()
  end

  identities do
    identity :unique_alert_time_type, [:alert_time, :alert_type]
  end
end
